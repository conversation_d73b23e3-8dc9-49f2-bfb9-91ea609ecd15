using FileInfo = Levelbuild.Core.FileInterface.FileInfo;

namespace Levelbuild.Domain.FileSystemFile.Dto;

/// <inheritdoc />
public class FileSystemFileInfo : FileInfo
{
	internal FileSystemFileInfo(string baseDir, string path) : base(baseDir, path)
	{
	}

	/// <inheritdoc />
	public override void WriteFile(Stream fileData)
	{
		CreateDirectoryIfNotExists();
		using Stream outFileStream = File.Create(AbsolutePath);
		fileData.CopyTo(outFileStream);
	}
	
	/// <inheritdoc />
	public override async Task WriteFileAsync(Stream fileData)
	{
		CreateDirectoryIfNotExists();
		await using Stream outFileStream = File.Create(AbsolutePath);
		await fileData.CopyToAsync(outFileStream);
	}

	/// <inheritdoc />
	public override void WriteFile(byte[] fileData)
	{
		CreateDirectoryIfNotExists();
		using Stream outFileStream = File.Create(AbsolutePath);
		var ms = new MemoryStream(fileData);
		ms.CopyTo(outFileStream);
	}
	
	/// <inheritdoc />
	public override async Task WriteFileAsync(byte[] fileData)
	{
		CreateDirectoryIfNotExists();
		await using Stream outFileStream = File.Create(AbsolutePath);
		var ms = new MemoryStream(fileData);
		await ms.CopyToAsync(outFileStream);
	}

	/// <inheritdoc />
	public override void WriteText(string textData)
	{
		CreateDirectoryIfNotExists();
		using var stream = new StreamWriter(AbsolutePath);
		stream.Write(textData);
	}

	/// <inheritdoc />
	public override Stream WriteFile()
	{
		CreateDirectoryIfNotExists();
		return File.Create(AbsolutePath);
	}

	/// <inheritdoc />
	public override Stream ReadFile(long? from = null, long? length = null)
	{
		if (!Exists())
			throw new FileNotFoundException($"File '{AbsolutePath}' does not exist.");
		Stream stream = File.OpenRead(AbsolutePath);

		if (from.HasValue && length.HasValue)
		{
			stream.Seek(from.Value, SeekOrigin.Begin);
			stream = new LimitedStream(stream, length.Value);
		}

		return stream;
	}

	/// <inheritdoc />
	public override byte[] ReadAllBytes()
	{
		if (!Exists())
			throw new FileNotFoundException($"File '{AbsolutePath}' does not exist.");
		return File.ReadAllBytes(AbsolutePath);
	}

	/// <inheritdoc />
	public override IEnumerable<string> ReadAllLines()
	{
		if (!Exists())
			throw new FileNotFoundException($"File '{AbsolutePath}' does not exist.");
		return File.ReadLines(AbsolutePath);
	}

	/// <inheritdoc />
	public override long GetFileSize()
	{
		if (!Exists())
			throw new FileNotFoundException($"File '{AbsolutePath}' does not exist.");
		return new System.IO.FileInfo(AbsolutePath).Length;
	}

	/// <inheritdoc />
	public override bool Exists()
	{
		return File.Exists(AbsolutePath);
	}

	/// <inheritdoc />
	public override void MoveTo(FileInfo destFileInfo)
	{
		if (destFileInfo is FileSystemFileInfo destFileSystemFileInfo)
		{
			destFileSystemFileInfo.CreateDirectoryIfNotExists();
			File.Move(AbsolutePath, destFileSystemFileInfo.AbsolutePath);
			// DeleteDirectoryIfEmpty(); // not necessary to delete
		}
		else
		{
			base.MoveTo(destFileInfo);
		}
	}

	/// <inheritdoc />
	public override void CopyTo(FileInfo destFileInfo)
	{
		if (destFileInfo is FileSystemFileInfo destFileSystemFileInfo)
		{
			destFileSystemFileInfo.CreateDirectoryIfNotExists();
			File.Copy(AbsolutePath, destFileSystemFileInfo.AbsolutePath);
			// DeleteDirectoryIfEmpty(); // not necessary to delete
		}
		else
		{
			base.CopyTo(destFileInfo);
		}
	}

	/// <inheritdoc />
	public override void DeleteFile()
	{
		if (!Exists())
		{
			throw new FileNotFoundException($"File '{AbsolutePath}' does not exist.");
		}

		File.Delete(AbsolutePath);
		// DeleteDirectoryIfEmpty(); // not necessary to delete
	}

	private void CreateDirectoryIfNotExists()
	{
		Directory.CreateDirectory(System.IO.Path.GetDirectoryName(AbsolutePath)!);
	}

	private void DeleteDirectoryIfEmpty()
	{
		try
		{
			Directory.Delete(AbsolutePath, false);
		}
		catch (Exception) // thrown if directory is not empty
		{
			// ignored
		}
	}
}