using Levelbuild.Core.FileInterface;
using DirectoryInfo = Levelbuild.Core.FileInterface.DirectoryInfo;
using FileInfo = Levelbuild.Core.FileInterface.FileInfo;

namespace Levelbuild.Domain.FileSystemFile.Dto;

/// <inheritdoc/>
public class FileSystemStore : FileStore
{
	
	/// <inheritdoc/>
	public FileSystemStore(FileSystemFileStoreConfig fileStoreConfig) : base(fileStoreConfig)
	{
		
	}
	
	/// <inheritdoc />
	public override DirectoryInfo GetDirectory(string path)
	{
		return new FileSystemDirectoryInfo(BaseDir, path);
	}
	
	/// <inheritdoc />
	public override FileInfo GetFile(string path)
	{
		return new FileSystemFileInfo(BaseDir, path);
	}
}