<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        
        <RootNamespace>Levelbuild.Domain.FileSystemFile</RootNamespace>
        <Company>levelbuild AG</Company>
        <PackageId>levelbuild.FileSystemFile</PackageId>
        
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
    </PropertyGroup>


    <!-- for development purpose -->
    <ItemGroup>
        <Reference Include="FileInterface">
            <HintPath>..\..\WebAppCore\FileInterface\bin\Debug\net8.0\FileInterface.dll</HintPath>
        </Reference>
    </ItemGroup>


    <!-- for release purpose -->
    <ItemGroup Condition="'$(Configuration)'=='Release'">
        <PackageReference Include="levelbuild.FileInterface" Version="$(LVL_RELEASE_VERSION)"/>
    </ItemGroup>


    <ItemGroup>
        <PackageReference Include="Serilog" Version="4.1.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    </ItemGroup>

</Project>
