<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <RootNamespace>Levelbuild.Domain.GoogleGeoData</RootNamespace>
        <Company>levelbuild AG</Company>
        <PackageId>levelbuild.GoogleGeoData</PackageId>

        <GenerateDocumentationFile>true</GenerateDocumentationFile>
</PropertyGroup>


    <!-- for development purpose -->
    <ItemGroup>
        <Reference Include="GeoDataInterface">
            <HintPath>..\..\WebAppCore\GeoDataInterface\bin\Debug\net8.0\GeoDataInterface.dll</HintPath>
        </Reference>
    </ItemGroup>


    <!-- for release purpose -->
    <ItemGroup Condition="'$(Configuration)'=='Release'">
        <PackageReference Include="levelbuild.GeoDataInterface" Version="$(LVL_RELEASE_VERSION)"/>
    </ItemGroup>


    <ItemGroup>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
    </ItemGroup>

</Project>
