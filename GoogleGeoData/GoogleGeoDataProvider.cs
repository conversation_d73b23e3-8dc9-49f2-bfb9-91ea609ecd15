using Levelbuild.Core.GeoDataInterface;
using Levelbuild.Core.GeoDataInterface.Dto;
using Levelbuild.Core.GeoDataInterface.Exceptions;
using Levelbuild.Domain.GoogleGeoData.Dto;
using Newtonsoft.Json;

namespace Levelbuild.Domain.GoogleGeoData;

/// <summary>
/// Google implementation of a GeoDtaProvider
/// </summary>
public class GoogleGeoDataProvider : IGeoDataProvider
{
	private readonly string _apiKey;
	
	/// <inheritdoc />
	public static IGeoDataProvider GetInstance(GeoDataConfig? config = null)
	{
		return new GoogleGeoDataProvider(config?.ApiKey ?? "");
	}

	private GoogleGeoDataProvider(string apiKey)
	{
		_apiKey = apiKey;
	}

	/// <inheritdoc />
	public async Task<GeoLocation?> GetLocation(string address)
	{
		var requestUri = $"https://maps.google.com/maps/api/geocode/json?address={Uri.EscapeDataString(address)}&key={_apiKey}";
		HttpResponseMessage request;
		using (var client = new HttpClient())
		{
			request = await client.GetAsync(requestUri);
		}

		var jsonResponse = await request.Content.ReadAsStringAsync();
		var response = JsonConvert.DeserializeObject<GoogleGeoResponse>(jsonResponse);

		// should never happen
		if (response == null)
			throw new Exception("received empty response");
		
		// expected responses
		switch (response.Status)
		{
			case "REQUEST_DENIED":
				throw new UnauthorizedAccessException();
			case "OVER_QUERY_LIMIT":
				throw new QuotaExceededException();
			case "ZERO_RESULTS":
				return null;
		}
		
		// unexpected response
		if (response.Status != "OK")
			throw new Exception("unexpected response status: " + response.Status + " for request " + requestUri);
		
		var firstResult = response.Results.First();
		var location = firstResult.Geometry!.Location;
		var viewport = firstResult.Geometry!.Viewport;
		
		if (viewport == null)
			return new GeoLocation(location.Latitude, location.Longitude, firstResult.FormattedAddress);
		
		return new GeoLocation(location.Latitude, location.Longitude, firstResult.FormattedAddress)
		{
			Area = new GeoArea(viewport.SouthWest.Latitude, viewport.NorthEast.Latitude, viewport.SouthWest.Longitude, viewport.NorthEast.Longitude)
		};
	}
}