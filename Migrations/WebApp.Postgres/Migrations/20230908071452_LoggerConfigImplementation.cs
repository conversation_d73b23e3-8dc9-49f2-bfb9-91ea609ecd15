using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.WebApp.Postgres
{
    /// <inheritdoc />
    public partial class LoggerConfigImplementation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:PostgresExtension:uuid-ossp", ",,");

            migrationBuilder.CreateTable(
                name: "LoggerConfigs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    LoggerSource = table.Column<string>(type: "text", nullable: false),
                    SourceIsGroup = table.Column<bool>(type: "boolean", nullable: false),
                    Level = table.Column<int>(type: "integer", nullable: false),
                    LogToFile = table.Column<bool>(type: "boolean", nullable: false),
                    LogFilePath = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoggerConfigs", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_LoggerConfigs_LoggerSource",
                table: "LoggerConfigs",
                column: "LoggerSource",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LoggerConfigs");
        }
    }
}
