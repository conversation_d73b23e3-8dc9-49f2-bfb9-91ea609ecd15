// <auto-generated />
using System;
using System.Collections.Generic;
using Levelbuild.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    [DbContext(typeof(CoreDatabaseContext))]
    [Migration("20231214085910_Localization")]
    partial class Localization
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "uuid-ossp");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CustomerUser", b =>
                {
                    b.Property<Guid>("CustomersId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UsersId")
                        .HasColumnType("uuid");

                    b.HasKey("CustomersId", "UsersId");

                    b.HasIndex("UsersId");

                    b.ToTable("CustomerUser");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Customer.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Dictionary<string, object>>("Options")
                        .HasColumnType("jsonb");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("DataStoreConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Cultures");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CultureId")
                        .HasColumnType("uuid");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("SystemTranslation")
                        .HasColumnType("boolean");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CultureId");

                    b.ToTable("Translations");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.LoggerConfig.LoggerConfig", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<string>("LogFilePath")
                        .HasColumnType("text");

                    b.Property<bool>("LogToFile")
                        .HasColumnType("boolean");

                    b.Property<string>("LoggerSource")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("SourceIsGroup")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("LoggerSource")
                        .IsUnique();

                    b.ToTable("LoggerConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("MainCustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MainCustomerId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CustomerUser", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.Customer", null)
                        .WithMany()
                        .HasForeignKey("CustomersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.User.User", null)
                        .WithMany()
                        .HasForeignKey("UsersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "Culture")
                        .WithMany("Translations")
                        .HasForeignKey("CultureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Culture");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.User", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.Customer", "MainCustomer")
                        .WithMany()
                        .HasForeignKey("MainCustomerId");

                    b.Navigation("MainCustomer");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Navigation("Translations");
                });
#pragma warning restore 612, 618
        }
    }
}
