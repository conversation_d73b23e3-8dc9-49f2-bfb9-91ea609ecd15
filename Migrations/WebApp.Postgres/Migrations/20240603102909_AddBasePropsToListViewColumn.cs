using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddBasePropsToListViewColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "AllowEdit",
                table: "ListViewColumns",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "CheckRights",
                table: "ListViewColumns",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "Display",
                table: "ListViewColumns",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Label",
                table: "ListViewColumns",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaxWidth",
                table: "ListViewColumns",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MaxWidthUnit",
                table: "ListViewColumns",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MinWidth",
                table: "ListViewColumns",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MinWidthUnit",
                table: "ListViewColumns",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AllowEdit",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "CheckRights",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "Display",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "Label",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "MaxWidth",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "MaxWidthUnit",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "MinWidth",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "MinWidthUnit",
                table: "ListViewColumns");
        }
    }
}
