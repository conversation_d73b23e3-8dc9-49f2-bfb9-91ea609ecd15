using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class DataFieldLookupSourceSetNull : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DataFields_DataSources_LookupSourceId",
                table: "DataFields");

            migrationBuilder.AddForeignKey(
                name: "FK_DataFields_DataSources_LookupSourceId",
                table: "DataFields",
                column: "LookupSourceId",
                principalTable: "DataSources",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DataFields_DataSources_LookupSourceId",
                table: "DataFields");

            migrationBuilder.AddForeignKey(
                name: "FK_DataFields_DataSources_LookupSourceId",
                table: "DataFields",
                column: "LookupSourceId",
                principalTable: "DataSources",
                principalColumn: "Id");
        }
    }
}
