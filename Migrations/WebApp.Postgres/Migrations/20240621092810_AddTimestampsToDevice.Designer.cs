// <auto-generated />
using System;
using System.Collections.Generic;
using Levelbuild.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    [DbContext(typeof(CoreDatabaseContext))]
    [Migration("20240621092810_AddTimestampsToDevice")]
    partial class AddTimestampsToDevice
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "uuid-ossp");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CustomerEntityUserEntity", b =>
                {
                    b.Property<Guid>("CustomersId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UsersId")
                        .HasColumnType("uuid");

                    b.HasKey("CustomersId", "UsersId");

                    b.HasIndex("UsersId");

                    b.ToTable("CustomerEntityUserEntity");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Customer.CustomerEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DataSourceId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<int>("DecimalPlaces")
                        .HasColumnType("integer");

                    b.Property<int>("FieldType")
                        .HasColumnType("integer");

                    b.Property<int>("Length")
                        .HasColumnType("integer");

                    b.Property<Guid?>("LookupDisplayFieldId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LookupSourceId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Mandatory")
                        .HasColumnType("boolean");

                    b.Property<bool>("Multi")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("Nullable")
                        .HasColumnType("boolean");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("SystemField")
                        .HasColumnType("boolean");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<bool>("Unique")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("VirtualDataFieldId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("VirtualLookupFieldId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DataSourceId");

                    b.HasIndex("LookupDisplayFieldId");

                    b.HasIndex("LookupSourceId");

                    b.HasIndex("VirtualDataFieldId");

                    b.HasIndex("VirtualLookupFieldId");

                    b.ToTable("DataFields");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("DataStoreId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Encryption")
                        .HasColumnType("boolean");

                    b.Property<bool>("FulltextSearch")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Responsible")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("StoreFieldContent")
                        .HasColumnType("boolean");

                    b.Property<bool>("StoreFileContent")
                        .HasColumnType("boolean");

                    b.Property<bool>("StoreRevision")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("DataStoreId");

                    b.ToTable("DataSources");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Dictionary<string, object>>("Options")
                        .HasColumnType("jsonb");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("DataStoreConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreContext.DataStoreContextEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomerId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DataStoreId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Dictionary<string, object>>("Options")
                        .HasColumnType("jsonb");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("DataStoreId");

                    b.ToTable("DataStoreContexts");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Device.DeviceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApiKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<int>("Format")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Devices");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Cultures");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("CultureId")
                        .HasColumnType("uuid");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Responsible")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("SystemTranslation")
                        .HasColumnType("boolean");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("CultureId", "Key")
                        .IsUnique();

                    b.ToTable("Translations");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.LoggerConfig.LoggerConfigEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<string>("LogFilePath")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("LogToFile")
                        .HasColumnType("boolean");

                    b.Property<string>("LoggerSource")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("SourceIsGroup")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("LoggerSource")
                        .IsUnique();

                    b.ToTable("LoggerConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.PageEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AppPage")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("DataSourceId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("DataStoreId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DefaultViewId")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("MemorizeDefaultView")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DataSourceId");

                    b.HasIndex("DefaultViewId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Pages");

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewFieldEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ColEnd")
                        .HasColumnType("integer");

                    b.Property<int>("ColStart")
                        .HasColumnType("integer");

                    b.Property<Guid?>("DataFieldId")
                        .HasColumnType("uuid");

                    b.Property<int>("DataType")
                        .HasColumnType("integer");

                    b.Property<string>("DefaultValue")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FontColor")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("HelpText")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Placeholder")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("Readonly")
                        .HasColumnType("boolean");

                    b.Property<bool>("Required")
                        .HasColumnType("boolean");

                    b.Property<int>("RowEnd")
                        .HasColumnType("integer");

                    b.Property<int>("RowStart")
                        .HasColumnType("integer");

                    b.Property<Guid>("SectionId")
                        .HasColumnType("uuid");

                    b.Property<int>("TextAlign")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DataFieldId");

                    b.HasIndex("SectionId");

                    b.ToTable("GridViewFields", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewPageEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ColEnd")
                        .HasColumnType("integer");

                    b.Property<int>("ColStart")
                        .HasColumnType("integer");

                    b.Property<int>("GridViewColumn")
                        .HasColumnType("integer");

                    b.Property<int>("GridViewColumnPosition")
                        .HasColumnType("integer");

                    b.Property<Guid>("GridViewId")
                        .HasColumnType("uuid");

                    b.Property<int>("RowEnd")
                        .HasColumnType("integer");

                    b.Property<int>("RowStart")
                        .HasColumnType("integer");

                    b.Property<Guid>("SectionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("GridViewId");

                    b.HasIndex("SectionId");

                    b.ToTable("GridViewPages");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowMinimize")
                        .HasColumnType("boolean");

                    b.Property<int>("GridViewColumn")
                        .HasColumnType("integer");

                    b.Property<Guid>("GridViewId")
                        .HasColumnType("uuid");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.Property<bool>("ShowTitle")
                        .HasColumnType("boolean");

                    b.Property<bool>("StartMinimized")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("GridViewId");

                    b.ToTable("GridViewSections");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewTextEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ColEnd")
                        .HasColumnType("integer");

                    b.Property<int>("ColStart")
                        .HasColumnType("integer");

                    b.Property<string>("Color")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("RowEnd")
                        .HasColumnType("integer");

                    b.Property<int>("RowStart")
                        .HasColumnType("integer");

                    b.Property<Guid>("SectionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TextAlign")
                        .HasColumnType("integer");

                    b.Property<int>("TextType")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SectionId");

                    b.ToTable("GridViewTexts", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewColumnEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowEdit")
                        .HasColumnType("boolean");

                    b.Property<bool>("CheckRights")
                        .HasColumnType("boolean");

                    b.Property<bool>("Display")
                        .HasColumnType("boolean");

                    b.Property<Guid>("FieldId")
                        .HasColumnType("uuid");

                    b.Property<string>("Label")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("ListViewId")
                        .HasColumnType("uuid");

                    b.Property<int>("MaxWidth")
                        .HasColumnType("integer");

                    b.Property<int>("MaxWidthUnit")
                        .HasColumnType("integer");

                    b.Property<int>("MinWidth")
                        .HasColumnType("integer");

                    b.Property<int>("MinWidthUnit")
                        .HasColumnType("integer");

                    b.Property<int?>("Position")
                        .HasColumnType("integer");

                    b.Property<int>("TextAlign")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("FieldId");

                    b.HasIndex("ListViewId");

                    b.ToTable("ListViewColumns");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.PageViewEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowDisplayColumns")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Display")
                        .HasColumnType("boolean");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Icon")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.Property<bool>("ShowPreview")
                        .HasColumnType("boolean");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("SystemView")
                        .HasColumnType("boolean");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PageId", "Name")
                        .IsUnique();

                    b.ToTable("PageViews", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CultureId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CurrentCustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("IsMachineUser")
                        .HasColumnType("boolean");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("MainCustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("PersonalAccessToken")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("PersonalAccessTokenExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Salt")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("CultureId");

                    b.HasIndex("CurrentCustomerId");

                    b.HasIndex("MainCustomerId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.MultiData.MultiDataPageEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.Page.PageEntity");

                    b.Property<int?>("SingleRecordBehaviour")
                        .HasColumnType("integer");

                    b.ToTable("MultiDataPage", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.Page.PageEntity");

                    b.Property<string>("Test")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.ToTable("SingleDataPage", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GalleryView.GalleryViewEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.PageView.PageViewEntity");

                    b.Property<bool?>("TestBool")
                        .HasColumnType("boolean");

                    b.ToTable("GalleryViews", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.PageView.PageViewEntity");

                    b.Property<int>("ColumnCount")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnOneMinWidth")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnOneRatio")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnThreeMinWidth")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnThreeRatio")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnTwoMinWidth")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnTwoRatio")
                        .HasColumnType("integer");

                    b.Property<bool>("Readonly")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowViewer")
                        .HasColumnType("boolean");

                    b.ToTable("GridViews", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.PageView.PageViewEntity");

                    b.Property<string>("Test")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.ToTable("ListViews", (string)null);
                });

            modelBuilder.Entity("CustomerEntityUserEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", null)
                        .WithMany()
                        .HasForeignKey("CustomersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", null)
                        .WithMany()
                        .HasForeignKey("UsersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "DataSource")
                        .WithMany("Fields")
                        .HasForeignKey("DataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "LookupDisplayField")
                        .WithMany()
                        .HasForeignKey("LookupDisplayFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "LookupSource")
                        .WithMany()
                        .HasForeignKey("LookupSourceId");

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "VirtualDataField")
                        .WithMany()
                        .HasForeignKey("VirtualDataFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "VirtualLookupField")
                        .WithMany()
                        .HasForeignKey("VirtualLookupFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("DataSource");

                    b.Navigation("LookupDisplayField");

                    b.Navigation("LookupSource");

                    b.Navigation("VirtualDataField");

                    b.Navigation("VirtualLookupField");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", "DataStore")
                        .WithMany()
                        .HasForeignKey("DataStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataStore");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreContext.DataStoreContextEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", "DataStore")
                        .WithMany()
                        .HasForeignKey("DataStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("DataStore");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Device.DeviceEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", "User")
                        .WithMany("Devices")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "Culture")
                        .WithMany("Translations")
                        .HasForeignKey("CultureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Culture");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.PageEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "DataSource")
                        .WithMany()
                        .HasForeignKey("DataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", "DefaultView")
                        .WithMany()
                        .HasForeignKey("DefaultViewId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("DataSource");

                    b.Navigation("DefaultView");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewFieldEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "DataField")
                        .WithMany()
                        .HasForeignKey("DataFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", "Section")
                        .WithMany("Fields")
                        .HasForeignKey("SectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataField");

                    b.Navigation("Section");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewPageEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", "GridView")
                        .WithMany("Pages")
                        .HasForeignKey("GridViewId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", "Section")
                        .WithMany()
                        .HasForeignKey("SectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GridView");

                    b.Navigation("Section");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", "GridView")
                        .WithMany("Sections")
                        .HasForeignKey("GridViewId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GridView");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewTextEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", "Section")
                        .WithMany("Texts")
                        .HasForeignKey("SectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Section");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewColumnEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "Field")
                        .WithMany()
                        .HasForeignKey("FieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", "ListView")
                        .WithMany("Columns")
                        .HasForeignKey("ListViewId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Field");

                    b.Navigation("ListView");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.PageViewEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.PageEntity", "Page")
                        .WithMany("Views")
                        .HasForeignKey("PageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Page");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "Culture")
                        .WithMany("Users")
                        .HasForeignKey("CultureId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "CurrentCustomer")
                        .WithMany()
                        .HasForeignKey("CurrentCustomerId");

                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "MainCustomer")
                        .WithMany()
                        .HasForeignKey("MainCustomerId");

                    b.Navigation("Culture");

                    b.Navigation("CurrentCustomer");

                    b.Navigation("MainCustomer");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.MultiData.MultiDataPageEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.PageEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.Page.MultiData.MultiDataPageEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.PageEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GalleryView.GalleryViewEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.PageView.GalleryView.GalleryViewEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.Navigation("Fields");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Navigation("Translations");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.PageEntity", b =>
                {
                    b.Navigation("Views");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", b =>
                {
                    b.Navigation("Fields");

                    b.Navigation("Texts");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.Navigation("Devices");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", b =>
                {
                    b.Navigation("Pages");

                    b.Navigation("Sections");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", b =>
                {
                    b.Navigation("Columns");
                });
#pragma warning restore 612, 618
        }
    }
}
