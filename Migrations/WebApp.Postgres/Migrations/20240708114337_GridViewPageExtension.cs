using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class GridViewPageExtension : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "GridViewColumnPosition",
                table: "GridViewPages",
                newName: "Position");

            migrationBuilder.AlterColumn<Guid>(
                name: "SectionId",
                table: "GridViewPages",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "GridViewId",
                table: "GridViewPages",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<bool>(
                name: "AllowCreate",
                table: "GridViewPages",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "AllowMaximize",
                table: "GridViewPages",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "AllowMinimize",
                table: "GridViewPages",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "EmbeddedPageId",
                table: "GridViewPages",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "EmbeddedSectionId",
                table: "GridViewPages",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "EmbeddedViewId",
                table: "GridViewPages",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "KeyFieldId",
                table: "GridViewPages",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaxHeight",
                table: "GridViewPages",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "ShowTitle",
                table: "GridViewPages",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "StartMinimized",
                table: "GridViewPages",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "GridViewPages",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_GridViewPages_EmbeddedPageId",
                table: "GridViewPages",
                column: "EmbeddedPageId");

            migrationBuilder.CreateIndex(
                name: "IX_GridViewPages_EmbeddedSectionId",
                table: "GridViewPages",
                column: "EmbeddedSectionId");

            migrationBuilder.CreateIndex(
                name: "IX_GridViewPages_EmbeddedViewId",
                table: "GridViewPages",
                column: "EmbeddedViewId");

            migrationBuilder.CreateIndex(
                name: "IX_GridViewPages_KeyFieldId",
                table: "GridViewPages",
                column: "KeyFieldId");

            migrationBuilder.AddForeignKey(
                name: "FK_GridViewPages_DataFields_KeyFieldId",
                table: "GridViewPages",
                column: "KeyFieldId",
                principalTable: "DataFields",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_GridViewPages_GridViewSections_EmbeddedSectionId",
                table: "GridViewPages",
                column: "EmbeddedSectionId",
                principalTable: "GridViewSections",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_GridViewPages_PageViews_EmbeddedViewId",
                table: "GridViewPages",
                column: "EmbeddedViewId",
                principalTable: "PageViews",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_GridViewPages_Pages_EmbeddedPageId",
                table: "GridViewPages",
                column: "EmbeddedPageId",
                principalTable: "Pages",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_GridViewPages_DataFields_KeyFieldId",
                table: "GridViewPages");

            migrationBuilder.DropForeignKey(
                name: "FK_GridViewPages_GridViewSections_EmbeddedSectionId",
                table: "GridViewPages");

            migrationBuilder.DropForeignKey(
                name: "FK_GridViewPages_PageViews_EmbeddedViewId",
                table: "GridViewPages");

            migrationBuilder.DropForeignKey(
                name: "FK_GridViewPages_Pages_EmbeddedPageId",
                table: "GridViewPages");

            migrationBuilder.DropIndex(
                name: "IX_GridViewPages_EmbeddedPageId",
                table: "GridViewPages");

            migrationBuilder.DropIndex(
                name: "IX_GridViewPages_EmbeddedSectionId",
                table: "GridViewPages");

            migrationBuilder.DropIndex(
                name: "IX_GridViewPages_EmbeddedViewId",
                table: "GridViewPages");

            migrationBuilder.DropIndex(
                name: "IX_GridViewPages_KeyFieldId",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "AllowCreate",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "AllowMaximize",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "AllowMinimize",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "EmbeddedPageId",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "EmbeddedSectionId",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "EmbeddedViewId",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "KeyFieldId",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "MaxHeight",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "ShowTitle",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "StartMinimized",
                table: "GridViewPages");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "GridViewPages");

            migrationBuilder.RenameColumn(
                name: "Position",
                table: "GridViewPages",
                newName: "GridViewColumnPosition");

            migrationBuilder.AlterColumn<Guid>(
                name: "SectionId",
                table: "GridViewPages",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "GridViewId",
                table: "GridViewPages",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);
        }
    }
}
