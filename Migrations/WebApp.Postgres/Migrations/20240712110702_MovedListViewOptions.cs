using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class MovedListViewOptions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AllowDisplayColumns",
                table: "PageViews");

            migrationBuilder.DropColumn(
                name: "ShowPreview",
                table: "PageViews");

            migrationBuilder.AddColumn<bool>(
                name: "AllowDisplayColumns",
                table: "ListViews",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ShowPreview",
                table: "ListViews",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AllowDisplayColumns",
                table: "ListViews");

            migrationBuilder.DropColumn(
                name: "ShowPreview",
                table: "ListViews");

            migrationBuilder.AddColumn<bool>(
                name: "AllowDisplayColumns",
                table: "PageViews",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ShowPreview",
                table: "PageViews",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }
    }
}
