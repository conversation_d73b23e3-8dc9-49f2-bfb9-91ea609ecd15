using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class CreateUserlaneSteps : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.CreateTable(
				name: "UserlaneSteps",
				columns: table => new
				{
					Id = table.Column<Guid>(type: "uuid", nullable: false),
					UserlaneId = table.Column<Guid>(type: "uuid", nullable: false),
					TargetElement = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
					Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
					Description = table.Column<string>(type: "character varying(255)", nullable: false),
					Order = table.Column<int>(type: "integer", nullable: false),
					Delay = table.Column<int>(type: "integer", nullable: false),
				},
				constraints: table =>
				{
					table.PrimaryKey("PK_UserlaneSteps", x => x.Id);
					table.ForeignKey("FK_Userlanes", x => x.UserlaneId, "Userlanes", "Id");
				});

        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.DropTable(
				name: "UserlaneSteps");
        }
    }
}
