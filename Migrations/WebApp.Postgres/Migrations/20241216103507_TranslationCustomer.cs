using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class TranslationCustomer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Translations_CultureId_Key",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_Translations_Key",
                table: "Translations");

            migrationBuilder.AddColumn<Guid>(
                name: "CustomerId",
                table: "Translations",
                type: "uuid",
                nullable: true);
			
            migrationBuilder.CreateIndex(
                name: "IX_Translations_CultureId_Key_CustomerId",
                table: "Translations",
                columns: new[] { "CultureId", "Key", "CustomerId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Translations_CustomerId",
                table: "Translations",
                column: "CustomerId");

            migrationBuilder.AddForeignKey(
                name: "FK_Translations_Customers_CustomerId",
                table: "Translations",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Translations_Customers_CustomerId",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_Translations_CultureId_Key_CustomerId",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_Translations_CustomerId",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "CustomerId",
                table: "Translations");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_CultureId_Key",
                table: "Translations",
                columns: new[] { "CultureId", "Key" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Translations_Key",
                table: "Translations",
                column: "Key");
        }
    }
}
