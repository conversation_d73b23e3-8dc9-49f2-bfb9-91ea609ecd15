using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class CreateUserlaneResultBatchTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
			
            migrationBuilder.CreateTable(
                name: "UserlaneResultBatch",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
					BatchNo = table.Column<int>(type: "int", nullable: false).Annotation("Npgsql:ValueGenerationStrategy",Npgsql.EntityFrameworkCore.PostgreSQL.Metadata.NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserlaneId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedDateTime = table.Column<DateTime>(type: "timestamp", nullable: false, defaultValue: DateTime.Now)
                },
				constraints: table =>
				{
					table.PrimaryKey("PK_UserlaneResultBatch", x => x.Id);
					table.ForeignKey(
						name: "FK_UserlaneResultBatch_Userlanes_UserlaneId",
						column: x => x.UserlaneId,
						principalTable: "Userlanes",
						principalColumn: "Id",
						onDelete: ReferentialAction.Cascade);
					table.ForeignKey(
						name: "FK_UserlaneResultBatch_Users_UserId",
						column: x => x.UserId,
						principalTable: "Users",
						principalColumn: "Id",
						onDelete: ReferentialAction.Cascade);
				});
			
			migrationBuilder.AddColumn<Guid>(
				name: "BatchId",
				table: "UserlaneResult",
				nullable: true);
			
			migrationBuilder.AddForeignKey(
				name: "FK_UserlaneResult_UserlaneResultBatch_batchId", // Name of the FK constraint
				table: "UserlaneResult",                               // Table with the FK (child table)
				column: "BatchId",                                     // Column in 'UserlaneResult' that references the other table
				principalTable: "UserlaneResultBatch",                 // The referenced (parent) table
				principalColumn: "Id",                                 // Referenced column in the parent table (typically 'Id')
				onDelete: ReferentialAction.Cascade                    // Specify behavior on delete (Cascade, Restrict, etc.)
			);
			
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.DropForeignKey(
				name: "FK_UserlaneResult_UserlaneResultBatch_batchId",
				table: "UserlaneResult");
			
			migrationBuilder.DropColumn(
				name: "BatchId",
				table: "UserlaneResult");

			migrationBuilder.DropTable(name: "UserlaneResultBatch");
        }
    }
}
