using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class Add_StartTime_EndTimeStatusAndRunTime_To_UserlaneResultBatch : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "EndTime",
                table: "UserlaneResultBatch",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "Runtime",
                table: "UserlaneResultBatch",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "StartTime",
                table: "UserlaneResultBatch",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "UserlaneResultBatch",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserlaneSteps_UserlaneId",
                table: "UserlaneSteps",
                column: "UserlaneId");

            migrationBuilder.CreateIndex(
                name: "IX_UserlaneStepActions_UserlaneStepsId",
                table: "UserlaneStepActions",
                column: "UserlaneStepsId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserlaneStepActions_UserlaneSteps_UserlaneStepsId",
                table: "UserlaneStepActions",
                column: "UserlaneStepsId",
                principalTable: "UserlaneSteps",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserlaneSteps_Userlanes_UserlaneId",
                table: "UserlaneSteps",
                column: "UserlaneId",
                principalTable: "Userlanes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
			
			migrationBuilder.DropForeignKey(
				name: "FK_UserlaneSteps",
				table: "UserlaneStepActions");
			
			migrationBuilder.DropForeignKey(
				name: "FK_Userlanes",
				table: "UserlaneSteps");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserlaneStepActions_UserlaneSteps_UserlaneStepsId",
                table: "UserlaneStepActions");

            migrationBuilder.DropForeignKey(
                name: "FK_UserlaneSteps_Userlanes_UserlaneId",
                table: "UserlaneSteps");

            migrationBuilder.DropIndex(
                name: "IX_UserlaneSteps_UserlaneId",
                table: "UserlaneSteps");

            migrationBuilder.DropIndex(
                name: "IX_UserlaneStepActions_UserlaneStepsId",
                table: "UserlaneStepActions");

            migrationBuilder.DropColumn(
                name: "EndTime",
                table: "UserlaneResultBatch");

            migrationBuilder.DropColumn(
                name: "Runtime",
                table: "UserlaneResultBatch");

            migrationBuilder.DropColumn(
                name: "StartTime",
                table: "UserlaneResultBatch");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "UserlaneResultBatch");

            migrationBuilder.DropColumn(
                name: "UserName",
                table: "UserlaneResultBatch");
        }
    }
}
