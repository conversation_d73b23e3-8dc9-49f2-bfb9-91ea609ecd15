using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class CachedFileEntitiesChangePKtoID : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.DropPrimaryKey(
				name: "PK_DeepZoomImages",
				table: "DeepZoomImages");
			
			migrationBuilder.DropPrimaryKey(
				name: "PK_Thumbnails",
				table: "Thumbnails");

            migrationBuilder.AlterColumn<string>(
                name: "FileId",
                table: "Thumbnails",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "FileId",
                table: "DeepZoomImages",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AddColumn<int>(
                name: "D<PERSON>",
                table: "DeepZoomImages",
				
                type: "integer",
                nullable: true);
			
			migrationBuilder.Sql("UPDATE \"Thumbnails\" SET \"Id\" = uuid_generate_v4()");
			
            migrationBuilder.AddPrimaryKey(
                name: "PK_Thumbnails",
                table: "Thumbnails",
                column: "Id");

			migrationBuilder.Sql("UPDATE \"DeepZoomImages\" SET \"Id\" = uuid_generate_v4()");
			
            migrationBuilder.AddPrimaryKey(
                name: "PK_DeepZoomImages",
                table: "DeepZoomImages",
                column: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_Thumbnails",
                table: "Thumbnails");

            migrationBuilder.DropPrimaryKey(
                name: "PK_DeepZoomImages",
                table: "DeepZoomImages");

            migrationBuilder.DropColumn(
                name: "Dpi",
                table: "DeepZoomImages");

            migrationBuilder.AlterColumn<string>(
                name: "FileId",
                table: "Thumbnails",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "FileId",
                table: "DeepZoomImages",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AddPrimaryKey(
                name: "PK_Thumbnails",
                table: "Thumbnails",
                column: "ThumbnailId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_DeepZoomImages",
                table: "DeepZoomImages",
                column: "DeepZoomImageId");
        }
    }
}
