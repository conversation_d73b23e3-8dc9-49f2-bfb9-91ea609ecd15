using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class Removes_Column_Props : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CheckRights",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "MaxWidth",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "MaxWidthUnit",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "MinWidth",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "MinWidthUnit",
                table: "ListViewColumns");

            migrationBuilder.DropColumn(
                name: "TextAlign",
                table: "ListViewColumns");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "CheckRights",
                table: "ListViewColumns",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "MaxWidth",
                table: "ListViewColumns",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MaxWidthUnit",
                table: "ListViewColumns",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MinWidth",
                table: "ListViewColumns",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MinWidthUnit",
                table: "ListViewColumns",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TextAlign",
                table: "ListViewColumns",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }
    }
}
