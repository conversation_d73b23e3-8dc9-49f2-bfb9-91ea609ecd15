using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class ChangeUserlaneResultTableToUserlaneResultsAndUserlaneResultBatchTableToUserlaneResultBatches : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserlaneStepActions_UserlaneSteps_UserlaneStepsId",
                table: "UserlaneStepActions");

            migrationBuilder.DropTable(
                name: "UserlaneResult");

            migrationBuilder.DropTable(
                name: "UserlaneResultBatch");

            migrationBuilder.RenameColumn(
                name: "UserlaneStepsId",
                table: "UserlaneStepActions",
                newName: "UserlaneStepId");

            migrationBuilder.RenameIndex(
                name: "IX_UserlaneStepActions_UserlaneStepsId",
                table: "UserlaneStepActions",
                newName: "IX_UserlaneStepActions_UserlaneStepId");

            migrationBuilder.AlterColumn<string>(
                name: "Title",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

			migrationBuilder.Sql("ALTER TABLE \"UserlaneStepActions\" ALTER COLUMN \"ActionType\" TYPE integer USING \"ActionType\"::integer;");
                
                migrationBuilder.AlterColumn<int>(
                    name: "ActionType",
                    table: "UserlaneStepActions",
                    type: "integer",
                    nullable: false,
                    defaultValue: 0,
                    oldClrType: typeof(string),
                    oldType: "character varying(255)",
                    oldMaxLength: 255,
                    oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "StartPoint",
                table: "Userlanes",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PageId",
                table: "Userlanes",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.CreateTable(
                name: "UserlaneResultBatches",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserlaneId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Status = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Runtime = table.Column<int>(type: "integer", nullable: false),
                    StartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedDateTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserlaneResultBatches", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UserlaneResults",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserlaneId = table.Column<Guid>(type: "uuid", nullable: false),
                    BatchId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserlaneStepId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserlaneStepActionId = table.Column<Guid>(type: "uuid", nullable: false),
                    Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    StartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Duration = table.Column<int>(type: "integer", nullable: false),
                    Complete = table.Column<bool>(type: "boolean", nullable: false),
                    Found = table.Column<bool>(type: "boolean", nullable: false),
                    Result = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserlaneResults", x => x.Id);
                });

            migrationBuilder.AddForeignKey(
                name: "FK_UserlaneStepActions_UserlaneSteps_UserlaneStepId",
                table: "UserlaneStepActions",
                column: "UserlaneStepId",
                principalTable: "UserlaneSteps",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserlaneStepActions_UserlaneSteps_UserlaneStepId",
                table: "UserlaneStepActions");

            migrationBuilder.DropTable(
                name: "UserlaneResultBatches");

            migrationBuilder.DropTable(
                name: "UserlaneResults");

            migrationBuilder.RenameColumn(
                name: "UserlaneStepId",
                table: "UserlaneStepActions",
                newName: "UserlaneStepsId");

            migrationBuilder.RenameIndex(
                name: "IX_UserlaneStepActions_UserlaneStepId",
                table: "UserlaneStepActions",
                newName: "IX_UserlaneStepActions_UserlaneStepsId");

            migrationBuilder.AlterColumn<string>(
                name: "Title",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ActionType",
                table: "UserlaneStepActions",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<string>(
                name: "StartPoint",
                table: "Userlanes",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "PageId",
                table: "Userlanes",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.CreateTable(
                name: "UserlaneResult",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    BatchId = table.Column<Guid>(type: "uuid", nullable: false),
                    Complete = table.Column<bool>(type: "boolean", nullable: false),
                    Duration = table.Column<int>(type: "integer", nullable: false),
                    EndTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Found = table.Column<bool>(type: "boolean", nullable: false),
                    Result = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    StartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    UserlaneId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserlaneStepActionId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserlaneStepId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserlaneResult", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UserlaneResultBatch",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    BatchNo = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CreatedDateTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Runtime = table.Column<int>(type: "integer", nullable: false),
                    StartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    UserlaneId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserlaneResultBatch", x => x.Id);
                });

            migrationBuilder.AddForeignKey(
                name: "FK_UserlaneStepActions_UserlaneSteps_UserlaneStepsId",
                table: "UserlaneStepActions",
                column: "UserlaneStepsId",
                principalTable: "UserlaneSteps",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
