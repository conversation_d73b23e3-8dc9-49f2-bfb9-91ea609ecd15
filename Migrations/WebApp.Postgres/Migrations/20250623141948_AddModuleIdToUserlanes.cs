using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddModuleIdToUserlanes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ModuleId",
                table: "Userlanes",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Userlanes_ModuleId",
                table: "Userlanes",
                column: "ModuleId");

            migrationBuilder.AddForeignKey(
                name: "FK_Userlanes_Modules_ModuleId",
                table: "Userlanes",
                column: "ModuleId",
                principalTable: "Modules",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Userlanes_Modules_ModuleId",
                table: "Userlanes");

            migrationBuilder.DropIndex(
                name: "IX_Userlanes_ModuleId",
                table: "Userlanes");

            migrationBuilder.DropColumn(
                name: "ModuleId",
                table: "Userlanes");
        }
    }
}
