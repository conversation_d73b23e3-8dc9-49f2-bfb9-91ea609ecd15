// <auto-generated />
using System;
using System.Collections.Generic;
using Levelbuild.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    [DbContext(typeof(CoreDatabaseContext))]
    [Migration("20250630080407_AddUserlaneFeedMigration")]
    partial class AddUserlaneFeedMigration
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "uuid-ossp");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CustomerEntityUserEntity", b =>
                {
                    b.Property<Guid>("CustomersId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UsersId")
                        .HasColumnType("uuid");

                    b.HasKey("CustomersId", "UsersId");

                    b.HasIndex("UsersId");

                    b.ToTable("CustomerEntityUserEntity");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Auth.OneTimeAuthenticationCodeEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AuthCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PasskeyId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UsedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AuthCode")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("OneTimeAuthenticationCodes");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Customer.CustomerEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("DisplayName")
                        .IsUnique();

                    b.HasIndex("RemoteId")
                        .IsUnique();

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldColumnEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("DataFieldId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DisplayFieldId")
                        .HasColumnType("uuid");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DataFieldId");

                    b.HasIndex("DisplayFieldId");

                    b.ToTable("DataFieldColumns");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AutoGenerated")
                        .HasColumnType("boolean");

                    b.Property<bool>("ColumnView")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("DataSourceId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<int>("DecimalPlaces")
                        .HasColumnType("integer");

                    b.Property<string>("DefaultValue")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("FieldType")
                        .HasColumnType("integer");

                    b.Property<bool>("FilterSelf")
                        .HasColumnType("boolean");

                    b.Property<int?>("FormatType")
                        .HasColumnType("integer");

                    b.Property<bool>("HasVirtualData")
                        .HasColumnType("boolean");

                    b.Property<int?>("InputDataType")
                        .HasColumnType("integer");

                    b.Property<string>("LabelFalse")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("LabelTrue")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Length")
                        .HasColumnType("integer");

                    b.Property<Guid?>("LookupDisplayFieldId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LookupSourceId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Mandatory")
                        .HasColumnType("boolean");

                    b.Property<bool>("Multi")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<List<Guid>>("ReferencingVirtualFieldIds")
                        .HasColumnType("uuid[]");

                    b.Property<bool>("RichText")
                        .HasColumnType("boolean");

                    b.Property<string>("Sign")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("SystemField")
                        .HasColumnType("boolean");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<bool>("Unique")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("VirtualDataFieldId")
                        .HasColumnType("uuid");

                    b.Property<string>("VirtualDataStoreQueryName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("VirtualLookupFieldId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DataSourceId");

                    b.HasIndex("LookupDisplayFieldId");

                    b.HasIndex("LookupSourceId");

                    b.HasIndex("VirtualDataFieldId");

                    b.HasIndex("VirtualLookupFieldId");

                    b.ToTable("DataFields");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldFilterEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CompareValue")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("DataFieldId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FilterFieldId")
                        .HasColumnType("uuid");

                    b.Property<int>("Operator")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DataFieldId");

                    b.HasIndex("FilterFieldId");

                    b.ToTable("DataFieldFilters");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldSortingEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Direction")
                        .HasColumnType("integer");

                    b.Property<Guid>("FieldId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("FieldId");

                    b.HasIndex("PageId");

                    b.ToTable("DataFieldSortings");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowFile")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("AnnotationCreatePageId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AnnotationDetailPageId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AnnotationGroupByFieldId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AnnotationKeyFieldId")
                        .HasColumnType("uuid");

                    b.Property<string>("AnnotationLabel")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("AnnotationSourceId")
                        .HasColumnType("uuid");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("DataStoreId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DefaultDetailPageId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Encryption")
                        .HasColumnType("boolean");

                    b.Property<bool>("Favor")
                        .HasColumnType("boolean");

                    b.Property<bool>("FulltextSearch")
                        .HasColumnType("boolean");

                    b.Property<string>("Icon")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("Inactive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("ModuleId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Responsible")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("Revision")
                        .HasColumnType("uuid");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("StoreFieldContent")
                        .HasColumnType("boolean");

                    b.Property<bool>("StoreFileContent")
                        .HasColumnType("boolean");

                    b.Property<bool>("StoreRevision")
                        .HasColumnType("boolean");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AnnotationCreatePageId");

                    b.HasIndex("AnnotationDetailPageId");

                    b.HasIndex("AnnotationGroupByFieldId");

                    b.HasIndex("AnnotationKeyFieldId");

                    b.HasIndex("AnnotationSourceId");

                    b.HasIndex("DataStoreId");

                    b.HasIndex("DefaultDetailPageId");

                    b.HasIndex("ModuleId");

                    b.ToTable("DataSources");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Dictionary<string, object>>("Options")
                        .HasColumnType("jsonb");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("DataStoreConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreContext.DataStoreContextEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomerId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DataStoreId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Dictionary<string, object>>("Options")
                        .HasColumnType("jsonb");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("DataStoreId");

                    b.ToTable("DataStoreContexts");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DeepZoom.CachedDeepZoomEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CachedFileId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("Dpi")
                        .HasColumnType("integer");

                    b.Property<int>("ErrorCount")
                        .HasColumnType("integer");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FileId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePositions")
                        .HasColumnType("text");

                    b.Property<int?>("Height")
                        .HasColumnType("integer");

                    b.Property<DateTime>("LastTouched")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("Overlap")
                        .HasColumnType("integer");

                    b.Property<int>("State")
                        .HasColumnType("integer");

                    b.Property<int?>("TileSize")
                        .HasColumnType("integer");

                    b.Property<int?>("Width")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("DeepZoomImages");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Device.DeviceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApiKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<int>("Format")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PasskeyId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Devices");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.FileUploads.FileUploadsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("DataSourceId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("FileId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("FileId"));

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LockExpires")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LockId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("LockValue")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("Revision")
                        .HasColumnType("uuid");

                    b.Property<string>("UserInfo")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("DataSourceId");

                    b.ToTable("FileUploads");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Cultures");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("CultureId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Responsible")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("SystemTranslation")
                        .HasColumnType("boolean");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CultureId", "Key", "CustomerId")
                        .IsUnique();

                    b.ToTable("Translations");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.LoggerConfig.LoggerConfigEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<string>("LogFilePath")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("LogToFile")
                        .HasColumnType("boolean");

                    b.Property<string>("LoggerSource")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("SourceIsGroup")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("LoggerSource")
                        .IsUnique();

                    b.ToTable("LoggerConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Module.ModuleEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("DataStoreId")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Icon")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("ResponsibleId")
                        .HasColumnType("uuid");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("DataStoreId");

                    b.HasIndex("ResponsibleId");

                    b.ToTable("Modules");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.MultiData.MultiPageFilterFieldEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("DisplayInPanel")
                        .HasColumnType("boolean");

                    b.Property<Guid>("FieldId")
                        .HasColumnType("uuid");

                    b.Property<bool>("MultiValue")
                        .HasColumnType("boolean");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.Property<bool>("ValuePreview")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("FieldId");

                    b.HasIndex("PageId");

                    b.ToTable("MultiPageFilterFields");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.PageEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AppPage")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("DataSourceId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("DataStoreId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DefaultViewId")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("MemorizeDefaultView")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("Revision")
                        .HasColumnType("uuid");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DataSourceId");

                    b.HasIndex("DefaultViewId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Pages");

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.SingleData.PageHeaderElementEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.Property<Guid>("Revision")
                        .HasColumnType("uuid");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("PageId");

                    b.ToTable("PageHeaderElements", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewFieldEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ColEnd")
                        .HasColumnType("integer");

                    b.Property<int>("ColStart")
                        .HasColumnType("integer");

                    b.Property<Guid?>("DataFieldId")
                        .HasColumnType("uuid");

                    b.Property<int>("DataType")
                        .HasColumnType("integer");

                    b.Property<string>("DefaultValue")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FontColor")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("HelpText")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("HideIfEmpty")
                        .HasColumnType("boolean");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Placeholder")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("Readonly")
                        .HasColumnType("boolean");

                    b.Property<bool>("Required")
                        .HasColumnType("boolean");

                    b.Property<int>("RowEnd")
                        .HasColumnType("integer");

                    b.Property<int>("RowStart")
                        .HasColumnType("integer");

                    b.Property<Guid>("SectionId")
                        .HasColumnType("uuid");

                    b.Property<int>("TextAlign")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DataFieldId");

                    b.HasIndex("SectionId");

                    b.ToTable("GridViewFields", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewPageEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowCreate")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowMaximize")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowMinimize")
                        .HasColumnType("boolean");

                    b.Property<bool?>("AllowOpenNewTab")
                        .HasColumnType("boolean");

                    b.Property<int>("ColEnd")
                        .HasColumnType("integer");

                    b.Property<int>("ColStart")
                        .HasColumnType("integer");

                    b.Property<int?>("Divider")
                        .HasColumnType("integer");

                    b.Property<Guid?>("EmbeddedPageId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("EmbeddedSectionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("EmbeddedViewId")
                        .HasColumnType("uuid");

                    b.Property<int>("GridViewColumn")
                        .HasColumnType("integer");

                    b.Property<Guid?>("GridViewId")
                        .HasColumnType("uuid");

                    b.Property<int>("GridViewPageType")
                        .HasColumnType("integer");

                    b.Property<string>("Icon")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("KeyFieldId")
                        .HasColumnType("uuid");

                    b.Property<int?>("MaxHeight")
                        .HasColumnType("integer");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ReferenceFieldId")
                        .HasColumnType("uuid");

                    b.Property<int>("ReferenceType")
                        .HasColumnType("integer");

                    b.Property<Guid>("Revision")
                        .HasColumnType("uuid");

                    b.Property<int>("RowEnd")
                        .HasColumnType("integer");

                    b.Property<int>("RowStart")
                        .HasColumnType("integer");

                    b.Property<Guid?>("SectionId")
                        .HasColumnType("uuid");

                    b.Property<bool>("ShowTitle")
                        .HasColumnType("boolean");

                    b.Property<bool>("StartMinimized")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool?>("WithThousandSeparators")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("EmbeddedPageId");

                    b.HasIndex("EmbeddedSectionId");

                    b.HasIndex("EmbeddedViewId");

                    b.HasIndex("GridViewId");

                    b.HasIndex("KeyFieldId");

                    b.HasIndex("ReferenceFieldId");

                    b.HasIndex("SectionId");

                    b.ToTable("GridViewPages");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewPageFilterEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CompareValue")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("FilterFieldId")
                        .HasColumnType("uuid");

                    b.Property<int>("Operator")
                        .HasColumnType("integer");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("FilterFieldId");

                    b.HasIndex("PageId");

                    b.ToTable("GridViewPageFilters");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowMinimize")
                        .HasColumnType("boolean");

                    b.Property<int>("GridViewColumn")
                        .HasColumnType("integer");

                    b.Property<Guid>("GridViewId")
                        .HasColumnType("uuid");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.Property<Guid>("Revision")
                        .HasColumnType("uuid");

                    b.Property<int>("RowCount")
                        .HasColumnType("integer");

                    b.Property<bool>("ShowTitle")
                        .HasColumnType("boolean");

                    b.Property<bool>("StartMinimized")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("GridViewId");

                    b.ToTable("GridViewSections");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewTextEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ColEnd")
                        .HasColumnType("integer");

                    b.Property<int>("ColStart")
                        .HasColumnType("integer");

                    b.Property<string>("Color")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("RowEnd")
                        .HasColumnType("integer");

                    b.Property<int>("RowStart")
                        .HasColumnType("integer");

                    b.Property<Guid>("SectionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TextAlign")
                        .HasColumnType("integer");

                    b.Property<int>("TextType")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SectionId");

                    b.ToTable("GridViewTexts", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewColumnEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowEdit")
                        .HasColumnType("boolean");

                    b.Property<bool>("Display")
                        .HasColumnType("boolean");

                    b.Property<Guid>("FieldId")
                        .HasColumnType("uuid");

                    b.Property<string>("Label")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("ListViewId")
                        .HasColumnType("uuid");

                    b.Property<int?>("Position")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("FieldId");

                    b.HasIndex("ListViewId");

                    b.ToTable("ListViewColumns");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.PageViewEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Display")
                        .HasColumnType("boolean");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("ExpertMode")
                        .HasColumnType("boolean");

                    b.Property<string>("Icon")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.Property<Guid>("Revision")
                        .HasColumnType("uuid");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("SystemView")
                        .HasColumnType("boolean");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PageId", "Name")
                        .IsUnique();

                    b.ToTable("PageViews", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.StartupMigration.StartupMigrationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Checksum")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("Success")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("StartupMigrations");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Thumbnail.CachedThumbnailEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CachedFileId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FileId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("LastTouched")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Thumbnails");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("CultureId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CurrentCustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("FirstName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("IsMachineUser")
                        .HasColumnType("boolean");

                    b.Property<string>("LastName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("MainCustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("PersonalAccessToken")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("PersonalAccessTokenExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Salt")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("CultureId");

                    b.HasIndex("CurrentCustomerId");

                    b.HasIndex("DisplayName")
                        .IsUnique();

                    b.HasIndex("MainCustomerId");

                    b.HasIndex("RemoteId")
                        .IsUnique();

                    b.ToTable("Users", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserPreferencesEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<int>("CultureOptionType")
                        .HasColumnType("integer");

                    b.Property<Guid?>("CurrentCultureId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CurrentCultureId");

                    b.ToTable("Users", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.UserCustomerMapping.UserCustomerMappingEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("UserId");

                    b.ToTable("UserCustomerMappings");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Userlane.UserlaneEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ClientContext")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("ModuleId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PageId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("Speed")
                        .IsRequired()
                        .HasColumnType("integer");

                    b.Property<string>("StartPoint")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("TesterRole")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ModuleId");

                    b.ToTable("Userlanes");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Userlane.UserlaneFeedEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ActivityTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ActivityType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Metadata")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserlaneId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("UserlaneId");

                    b.ToTable("UserlaneFeedItems");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Userlane.UserlaneResultBatchEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Runtime")
                        .HasColumnType("integer");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("UserId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<string>("UserName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("UserlaneId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("UserlaneResultBatches");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Userlane.UserlaneResultEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Complete")
                        .HasColumnType("boolean");

                    b.Property<int>("Duration")
                        .HasColumnType("integer");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Found")
                        .HasColumnType("boolean");

                    b.Property<string>("Result")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("UserlaneId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserlaneStepActionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserlaneStepId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("UserlaneResults");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.UserlaneStep.UserlaneStepEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Delay")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("TargetElement")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Title")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("UserlaneId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserlaneId");

                    b.ToTable("UserlaneSteps");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.UserlaneStepAction.UserlaneStepActionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ActionType")
                        .HasColumnType("integer");

                    b.Property<int>("Delay")
                        .HasColumnType("integer");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("Target")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("TargetValue")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Trigger")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("UserlaneStepId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserlaneStepId");

                    b.ToTable("UserlaneStepActions");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.UserlaneStepTestCondition.UserlaneStepTestConditionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ConditionType")
                        .HasColumnType("integer");

                    b.Property<string>("Field")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("LogicalOperator")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("Operator")
                        .HasColumnType("integer");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserlaneStepId")
                        .HasColumnType("uuid");

                    b.Property<string>("Value")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("UserlaneStepId");

                    b.ToTable("UserlaneStepTestConditions");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Workflow.WorkflowEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("DataSourceId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("RecipientsFieldId")
                        .HasColumnType("uuid");

                    b.Property<int>("Slot")
                        .HasColumnType("integer");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("StatusFieldId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DataSourceId");

                    b.HasIndex("RecipientsFieldId");

                    b.HasIndex("StatusFieldId");

                    b.ToTable("Workflows");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Workflow.WorkflowNodeEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<short>("Sorting")
                        .HasColumnType("smallint");

                    b.Property<int>("State")
                        .HasColumnType("integer");

                    b.Property<Guid>("WorkflowId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("WorkflowId");

                    b.ToTable("WorkflowNodes");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.Create.CreatePageEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.Page.PageEntity");

                    b.Property<string>("SaveButtonLabel")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.ToTable("CreatePage", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.MultiData.MultiDataPageEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.Page.PageEntity");

                    b.Property<Guid?>("CreatePageId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DetailPageId")
                        .HasColumnType("uuid");

                    b.Property<int?>("SingleRecordBehaviour")
                        .HasColumnType("integer");

                    b.HasIndex("CreatePageId");

                    b.HasIndex("DetailPageId");

                    b.ToTable("MultiDataPage", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.Page.PageEntity");

                    b.Property<string>("BreadcrumbLabel")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.ToTable("SingleDataPage", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GalleryView.GalleryViewEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.PageView.PageViewEntity");

                    b.Property<Guid?>("SubtitleFieldId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TitleFieldId")
                        .HasColumnType("uuid");

                    b.HasIndex("SubtitleFieldId");

                    b.HasIndex("TitleFieldId");

                    b.ToTable("GalleryViews", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.PageView.PageViewEntity");

                    b.Property<Guid?>("AnnotationViewId")
                        .HasColumnType("uuid");

                    b.Property<int>("ColumnCount")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnOneMinWidth")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnOneRatio")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnThreeMinWidth")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnThreeRatio")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnTwoMinWidth")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnTwoRatio")
                        .HasColumnType("integer");

                    b.Property<bool>("Readonly")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowViewer")
                        .HasColumnType("boolean");

                    b.Property<int>("ViewerRatio")
                        .HasColumnType("integer");

                    b.HasIndex("AnnotationViewId");

                    b.ToTable("GridViews", (string)null);
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", b =>
                {
                    b.HasBaseType("Levelbuild.Entities.Features.PageView.PageViewEntity");

                    b.Property<bool>("AllowDisplayColumns")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowPreview")
                        .HasColumnType("boolean");

                    b.Property<int>("StickyColumnCount")
                        .HasColumnType("integer");

                    b.ToTable("ListViews", (string)null);
                });

            modelBuilder.Entity("CustomerEntityUserEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", null)
                        .WithMany()
                        .HasForeignKey("CustomersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", null)
                        .WithMany()
                        .HasForeignKey("UsersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Auth.OneTimeAuthenticationCodeEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldColumnEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "DataField")
                        .WithMany("Columns")
                        .HasForeignKey("DataFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "DisplayField")
                        .WithMany()
                        .HasForeignKey("DisplayFieldId");

                    b.Navigation("DataField");

                    b.Navigation("DisplayField");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "DataSource")
                        .WithMany("Fields")
                        .HasForeignKey("DataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "LookupDisplayField")
                        .WithMany()
                        .HasForeignKey("LookupDisplayFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "LookupSource")
                        .WithMany()
                        .HasForeignKey("LookupSourceId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "VirtualDataField")
                        .WithMany()
                        .HasForeignKey("VirtualDataFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "VirtualLookupField")
                        .WithMany()
                        .HasForeignKey("VirtualLookupFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("DataSource");

                    b.Navigation("LookupDisplayField");

                    b.Navigation("LookupSource");

                    b.Navigation("VirtualDataField");

                    b.Navigation("VirtualLookupField");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldFilterEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "DataField")
                        .WithMany("Filters")
                        .HasForeignKey("DataFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "FilterField")
                        .WithMany()
                        .HasForeignKey("FilterFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataField");

                    b.Navigation("FilterField");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldSortingEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "Field")
                        .WithMany()
                        .HasForeignKey("FieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.Page.MultiData.MultiDataPageEntity", "Page")
                        .WithMany("DefaultSorting")
                        .HasForeignKey("PageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Field");

                    b.Navigation("Page");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.Create.CreatePageEntity", "AnnotationCreatePage")
                        .WithMany()
                        .HasForeignKey("AnnotationCreatePageId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", "AnnotationDetailPage")
                        .WithMany()
                        .HasForeignKey("AnnotationDetailPageId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "AnnotationGroupByField")
                        .WithMany()
                        .HasForeignKey("AnnotationGroupByFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "AnnotationKeyField")
                        .WithMany()
                        .HasForeignKey("AnnotationKeyFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "AnnotationSource")
                        .WithMany()
                        .HasForeignKey("AnnotationSourceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", "DataStore")
                        .WithMany()
                        .HasForeignKey("DataStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", "DefaultDetailPage")
                        .WithMany()
                        .HasForeignKey("DefaultDetailPageId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.Module.ModuleEntity", "Module")
                        .WithMany("DataSources")
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AnnotationCreatePage");

                    b.Navigation("AnnotationDetailPage");

                    b.Navigation("AnnotationGroupByField");

                    b.Navigation("AnnotationKeyField");

                    b.Navigation("AnnotationSource");

                    b.Navigation("DataStore");

                    b.Navigation("DefaultDetailPage");

                    b.Navigation("Module");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreContext.DataStoreContextEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", "DataStore")
                        .WithMany()
                        .HasForeignKey("DataStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("DataStore");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Device.DeviceEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", "User")
                        .WithMany("Devices")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.FileUploads.FileUploadsEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "DataSource")
                        .WithMany()
                        .HasForeignKey("DataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataSource");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "Culture")
                        .WithMany("Translations")
                        .HasForeignKey("CultureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "Customer")
                        .WithMany("Translations")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Culture");

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Module.ModuleEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", "DataStore")
                        .WithMany()
                        .HasForeignKey("DataStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", "Responsible")
                        .WithMany("Modules")
                        .HasForeignKey("ResponsibleId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("DataStore");

                    b.Navigation("Responsible");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.MultiData.MultiPageFilterFieldEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "Field")
                        .WithMany()
                        .HasForeignKey("FieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.Page.MultiData.MultiDataPageEntity", "Page")
                        .WithMany("FilterFields")
                        .HasForeignKey("PageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Field");

                    b.Navigation("Page");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.PageEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "DataSource")
                        .WithMany()
                        .HasForeignKey("DataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", "DefaultView")
                        .WithMany()
                        .HasForeignKey("DefaultViewId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("DataSource");

                    b.Navigation("DefaultView");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.SingleData.PageHeaderElementEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", "Page")
                        .WithMany("HeaderElements")
                        .HasForeignKey("PageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Page");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewFieldEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "DataField")
                        .WithMany()
                        .HasForeignKey("DataFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", "Section")
                        .WithMany("Fields")
                        .HasForeignKey("SectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataField");

                    b.Navigation("Section");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewPageEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.PageEntity", "EmbeddedPage")
                        .WithMany()
                        .HasForeignKey("EmbeddedPageId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", "EmbeddedSection")
                        .WithMany()
                        .HasForeignKey("EmbeddedSectionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", "EmbeddedView")
                        .WithMany()
                        .HasForeignKey("EmbeddedViewId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", "GridView")
                        .WithMany("Pages")
                        .HasForeignKey("GridViewId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "KeyField")
                        .WithMany()
                        .HasForeignKey("KeyFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "ReferenceField")
                        .WithMany()
                        .HasForeignKey("ReferenceFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", "Section")
                        .WithMany("Pages")
                        .HasForeignKey("SectionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("EmbeddedPage");

                    b.Navigation("EmbeddedSection");

                    b.Navigation("EmbeddedView");

                    b.Navigation("GridView");

                    b.Navigation("KeyField");

                    b.Navigation("ReferenceField");

                    b.Navigation("Section");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewPageFilterEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "FilterField")
                        .WithMany()
                        .HasForeignKey("FilterFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewPageEntity", "Page")
                        .WithMany("Filters")
                        .HasForeignKey("PageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FilterField");

                    b.Navigation("Page");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", "GridView")
                        .WithMany("Sections")
                        .HasForeignKey("GridViewId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GridView");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewTextEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", "Section")
                        .WithMany("Texts")
                        .HasForeignKey("SectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Section");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewColumnEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "Field")
                        .WithMany()
                        .HasForeignKey("FieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", "ListView")
                        .WithMany("Columns")
                        .HasForeignKey("ListViewId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Field");

                    b.Navigation("ListView");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.PageViewEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.PageEntity", "Page")
                        .WithMany("Views")
                        .HasForeignKey("PageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Page");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "Culture")
                        .WithMany("Users")
                        .HasForeignKey("CultureId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "CurrentCustomer")
                        .WithMany()
                        .HasForeignKey("CurrentCustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "MainCustomer")
                        .WithMany()
                        .HasForeignKey("MainCustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Culture");

                    b.Navigation("CurrentCustomer");

                    b.Navigation("MainCustomer");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserPreferencesEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "CurrentCulture")
                        .WithMany()
                        .HasForeignKey("CurrentCultureId");

                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", null)
                        .WithOne("Preferences")
                        .HasForeignKey("Levelbuild.Entities.Features.User.UserPreferencesEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CurrentCulture");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.UserCustomerMapping.UserCustomerMappingEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "Customer")
                        .WithMany("UserMapping")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", "User")
                        .WithMany("CustomerMapping")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Customer");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Userlane.UserlaneEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Module.ModuleEntity", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Module");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Userlane.UserlaneFeedEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.Userlane.UserlaneEntity", "Userlane")
                        .WithMany()
                        .HasForeignKey("UserlaneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");

                    b.Navigation("Userlane");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.UserlaneStep.UserlaneStepEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Userlane.UserlaneEntity", "Userlane")
                        .WithMany("Steps")
                        .HasForeignKey("UserlaneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Userlane");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.UserlaneStepAction.UserlaneStepActionEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.UserlaneStep.UserlaneStepEntity", "UserlaneStep")
                        .WithMany("Actions")
                        .HasForeignKey("UserlaneStepId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UserlaneStep");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.UserlaneStepTestCondition.UserlaneStepTestConditionEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.UserlaneStep.UserlaneStepEntity", "UserlaneStep")
                        .WithMany("TestConditions")
                        .HasForeignKey("UserlaneStepId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UserlaneStep");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Workflow.WorkflowEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "DataSource")
                        .WithMany("Workflows")
                        .HasForeignKey("DataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "RecipientsField")
                        .WithMany()
                        .HasForeignKey("RecipientsFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "StatusField")
                        .WithMany()
                        .HasForeignKey("StatusFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataSource");

                    b.Navigation("RecipientsField");

                    b.Navigation("StatusField");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Workflow.WorkflowNodeEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId");

                    b.HasOne("Levelbuild.Entities.Features.Workflow.WorkflowEntity", "Workflow")
                        .WithMany("Nodes")
                        .HasForeignKey("WorkflowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Workflow");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.Create.CreatePageEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.PageEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.Page.Create.CreatePageEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.MultiData.MultiDataPageEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.Create.CreatePageEntity", "CreatePage")
                        .WithMany()
                        .HasForeignKey("CreatePageId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", "DetailPage")
                        .WithMany()
                        .HasForeignKey("DetailPageId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.Page.PageEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.Page.MultiData.MultiDataPageEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatePage");

                    b.Navigation("DetailPage");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Page.PageEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GalleryView.GalleryViewEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.PageView.GalleryView.GalleryViewEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "SubtitleField")
                        .WithMany()
                        .HasForeignKey("SubtitleFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.DataField.DataFieldEntity", "TitleField")
                        .WithMany()
                        .HasForeignKey("TitleFieldId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("SubtitleField");

                    b.Navigation("TitleField");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", "AnnotationView")
                        .WithMany()
                        .HasForeignKey("AnnotationViewId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AnnotationView");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.PageView.PageViewEntity", null)
                        .WithOne()
                        .HasForeignKey("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Customer.CustomerEntity", b =>
                {
                    b.Navigation("Translations");

                    b.Navigation("UserMapping");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldEntity", b =>
                {
                    b.Navigation("Columns");

                    b.Navigation("Filters");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.Navigation("Fields");

                    b.Navigation("Workflows");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Navigation("Translations");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Module.ModuleEntity", b =>
                {
                    b.Navigation("DataSources");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.PageEntity", b =>
                {
                    b.Navigation("Views");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewPageEntity", b =>
                {
                    b.Navigation("Filters");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewSectionEntity", b =>
                {
                    b.Navigation("Fields");

                    b.Navigation("Pages");

                    b.Navigation("Texts");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.Navigation("CustomerMapping");

                    b.Navigation("Devices");

                    b.Navigation("Modules");

                    b.Navigation("Preferences")
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Userlane.UserlaneEntity", b =>
                {
                    b.Navigation("Steps");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.UserlaneStep.UserlaneStepEntity", b =>
                {
                    b.Navigation("Actions");

                    b.Navigation("TestConditions");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Workflow.WorkflowEntity", b =>
                {
                    b.Navigation("Nodes");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.MultiData.MultiDataPageEntity", b =>
                {
                    b.Navigation("DefaultSorting");

                    b.Navigation("FilterFields");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Page.SingleData.SingleDataPageEntity", b =>
                {
                    b.Navigation("HeaderElements");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.GridView.GridViewEntity", b =>
                {
                    b.Navigation("Pages");

                    b.Navigation("Sections");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.PageView.ListView.ListViewEntity", b =>
                {
                    b.Navigation("Columns");
                });
#pragma warning restore 612, 618
        }
    }
}
