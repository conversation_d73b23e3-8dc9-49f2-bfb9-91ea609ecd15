using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.SqlServer.Migrations
{
    /// <inheritdoc />
    public partial class UserCulture : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Translations_CultureId",
                table: "Translations");

            migrationBuilder.AddColumn<Guid>(
                name: "CultureId",
                table: "Users",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Key",
                table: "Translations",
                type: "nvarchar(450)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<DateTime>(
                name: "Created",
                table: "Translations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CreatedById",
                table: "Translations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModified",
                table: "Translations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "LastModifiedById",
                table: "Translations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Responsible",
                table: "Translations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Slug",
                table: "Cultures",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_Users_CultureId",
                table: "Users",
                column: "CultureId");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_CreatedById",
                table: "Translations",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_CultureId_Key",
                table: "Translations",
                columns: new[] { "CultureId", "Key" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Translations_LastModifiedById",
                table: "Translations",
                column: "LastModifiedById");

            migrationBuilder.AddForeignKey(
                name: "FK_Translations_Users_CreatedById",
                table: "Translations",
                column: "CreatedById",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_Users_Cultures_CultureId",
                table: "Users",
                column: "CultureId",
                principalTable: "Cultures",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Translations_Users_CreatedById",
                table: "Translations");

            migrationBuilder.DropForeignKey(
                name: "FK_Users_Cultures_CultureId",
                table: "Users");

            migrationBuilder.DropIndex(
                name: "IX_Users_CultureId",
                table: "Users");

            migrationBuilder.DropIndex(
                name: "IX_Translations_CreatedById",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_Translations_CultureId_Key",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_Translations_LastModifiedById",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "CultureId",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Created",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "CreatedById",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "LastModified",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "LastModifiedById",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "Responsible",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "Slug",
                table: "Cultures");

            migrationBuilder.AlterColumn<string>(
                name: "Key",
                table: "Translations",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_CultureId",
                table: "Translations",
                column: "CultureId");
        }
    }
}
