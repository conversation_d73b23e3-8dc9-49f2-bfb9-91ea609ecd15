using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.SqlServer.Migrations
{
    /// <inheritdoc />
    public partial class AddDataStoreContext : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Users_Customers_MainCustomerId",
                table: "Users");

            migrationBuilder.CreateTable(
                name: "DataStoreContexts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Slug = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    DataStoreId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Enabled = table.Column<bool>(type: "bit", nullable: false),
                    Options = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataStoreContexts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataStoreContexts_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DataStoreContexts_DataStoreConfigs_DataStoreId",
                        column: x => x.DataStoreId,
                        principalTable: "DataStoreConfigs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DataStoreContexts_CustomerId",
                table: "DataStoreContexts",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_DataStoreContexts_DataStoreId",
                table: "DataStoreContexts",
                column: "DataStoreId");

            migrationBuilder.AddForeignKey(
                name: "FK_Users_Customers_MainCustomerId",
                table: "Users",
                column: "MainCustomerId",
                principalTable: "Customers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Users_Customers_MainCustomerId",
                table: "Users");

            migrationBuilder.DropTable(
                name: "DataStoreContexts");

            migrationBuilder.AddForeignKey(
                name: "FK_Users_Customers_MainCustomerId",
                table: "Users",
                column: "MainCustomerId",
                principalTable: "Customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
