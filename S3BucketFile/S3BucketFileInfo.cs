using System.Text;
using Amazon.S3;
using Amazon.S3.Model;
using FileInfo = Levelbuild.Core.FileInterface.FileInfo;

namespace Levelbuild.Domain.S3BucketFile;

/// <inheritdoc />
public class S3BucketFileInfo : FileInfo
{
	private readonly IAmazonS3 _client;
	private readonly string _bucket;
	
	internal S3BucketFileInfo(IAmazonS3 client, string bucket, string baseDir, string path) : base(baseDir, path)
	{
		_client = client;
		_bucket = bucket;
	}
	
	/// <inheritdoc />
	public override void WriteFile(Stream fileData)
	{
		var task = _client.UploadObjectFromStreamAsync(_bucket, GetLibPath(), fileData, new Dictionary<string, object>());
		task.ConfigureAwait(false);
		task.Wait(TimeSpan.FromMinutes(10));
	}
	
	/// <inheritdoc />
	public override async Task WriteFileAsync(Stream fileData)
	{
		await _client.UploadObjectFromStreamAsync(_bucket, GetLibPath(), fileData, new Dictionary<string, object>());
	}
	
	/// <inheritdoc />
	public override Stream WriteFile()
	{
		return new S3UploadStream(_client, _bucket, GetLibPath());
	}
	
	/// <inheritdoc />
	public override void WriteFile(byte[] fileData)
	{
		using var ms = new MemoryStream(fileData);
		WriteFile(ms);
	}
	
	/// <inheritdoc />
	public override async Task WriteFileAsync(byte[] fileData)
	{
		using var ms = new MemoryStream(fileData);
		await WriteFileAsync(ms);
	}
	
	/// <inheritdoc />
	public override void WriteText(string textData)
	{
		//throw new NotSupportedException("Move operations are not supported for S3");
		using var stream = new MemoryStream();
		using var writer = new StreamWriter(stream);
		writer.Write(textData);
		writer.Flush();
		WriteFile(stream);
	}
	
	/// <inheritdoc />
	public override Stream ReadFile(long? from = null, long? length = null)
	{
		try
		{
			var request = new GetObjectRequest
			{
				BucketName = _bucket,
				Key = GetLibPath()
			};

			if (from.HasValue && length.HasValue)
			{
				request.ByteRange = new ByteRange(from.Value, from.Value + length.Value - 1);
			}

			return _client.GetObjectAsync(request).GetAwaiter().GetResult().ResponseStream;
		}
		catch (Exception e)
		{
			if (e.Message.Contains("The specified key does not exist"))
				throw new FileNotFoundException($"File {_bucket}::{AbsolutePath} does not exist.");
			throw;
		}
	}
	
	/// <inheritdoc />
	public override byte[] ReadAllBytes()
	{
		try
		{
			var input = ReadFile();
			byte[] buffer = new byte[16 * 1024];
			using (MemoryStream ms = new MemoryStream())
			{
				int read;
				while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
				{
					ms.Write(buffer, 0, read);
				}
				
				return ms.ToArray();
			}
		}
		catch (Exception e)
		{
			if (e.Message.Contains("Http Status Code NotFound"))
				throw new FileNotFoundException($"File {_bucket}::{AbsolutePath} does not exist.");
			throw;
		}
	}
	
	/// <inheritdoc />
	public override IEnumerable<string> ReadAllLines()
	{
		// This is necessary. Although ReadFile() in GetIterator already throws FileNotFoundException, everything in GetIterator is executed lazy because of yield.
		// The exception would only surface, once someone tries to iterate over the IEnumerable.
		if (!Exists())
			throw new FileNotFoundException($"File {_bucket}::{AbsolutePath} does not exist.");
		return GetIterator();
	}
	
	private IEnumerable<string> GetIterator()
	{
		using var stream = ReadFile();
		using var reader = new StreamReader(stream, Encoding.UTF8);
		
		string? line;
		while ((line = reader.ReadLine()) != null)
		{
			yield return line;
		}
	}
	
	/// <inheritdoc />
	public override long GetFileSize()
	{
		try
		{
			var metadata = _client.GetObjectMetadataAsync(_bucket, GetLibPath()).Result;
			return metadata.ContentLength;
		}
		catch (Exception e)
		{
			if (e.Message.Contains("Http Status Code NotFound"))
				throw new FileNotFoundException($"File {_bucket}::{AbsolutePath} does not exist.");
			throw;
		}
	}
	
	/// <inheritdoc />
	public override bool Exists()
	{
		return 0 < _client.ListObjectsAsync(new ListObjectsRequest()
		{
			BucketName = _bucket,
			Prefix = GetLibPath()
		}).Result.S3Objects.Count;
	}
	
	/// <inheritdoc />
	public override void DeleteFile()
	{
		if (!Exists())
		{
			throw new FileNotFoundException($"File '{AbsolutePath}' does not exist.");
		}
		
		var res = _client.DeleteObjectAsync(_bucket, GetLibPath()).Result;
	}
	
	private string GetLibPath()
	{
		return AbsolutePath.Substring(1);
	}
}