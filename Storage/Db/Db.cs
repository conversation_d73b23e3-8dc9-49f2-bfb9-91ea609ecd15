using System.Collections.Concurrent;
using Elastic.Clients.Elasticsearch;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Domain.Storage.Db.Postgres;
using Levelbuild.Domain.StorageEntities;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore.Infrastructure;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Core.Events;
using MongoDB.Driver.Core.Extensions.DiagnosticSources;
using Serilog;

namespace Levelbuild.Domain.Storage.Db;

public class Db
{
	public readonly SqlType Type;
	public readonly StorageContextOrm? CustomerContext;
	internal readonly IDataStoreAuthentication? Authentication;

	public readonly SqlConnectionHelper ConnectionHelper;
	public readonly SqlDataHelper DataHelper;
	public readonly SqlMigrator Migrator;
	public readonly ILogger logger;
	public bool InlineMultiValueFields => DataHelper.InlineMultiValueFields;

	public string ConnectionString { get; }

	public ElasticsearchClient? ElasticClient;
	public MongoClient? MongoDbClient;
	private readonly PooledDbContextFactory<PostgresStorageDatabaseContext> _dbContextPool;
	internal readonly FeatureFlags FeatureFlags;

	private static readonly ConcurrentDictionary<string, MongoClient> _mongoClients = new();

	public Db(SqlType type, FeatureFlags featureFlags, string connectionString, StorageContextOrm? customerContext, IDataStoreAuthentication? authentication,
			  SqlConnectionHelper connectionHelper, PooledDbContextFactory<PostgresStorageDatabaseContext> dbContextPool, HashSet<string> failedMigrationTables,
			  ILogger logger)
	{
		Type = type;
		FeatureFlags = featureFlags;
		CustomerContext = customerContext;
		Authentication = authentication;
		ConnectionString = connectionString;
		ConnectionHelper = connectionHelper;
		DataHelper = new PostgresDataHelper(this, failedMigrationTables, logger);
		Migrator = new PostgresMigrator(this, logger);
		_dbContextPool = dbContextPool;
		this.logger = logger;
		InitElastic();
		InitMongoDb();
	}

	public StorageDatabaseContext GetEfCoreDbContext()
	{
		return _dbContextPool.CreateDbContext();
	}

	private void InitElastic()
	{
		if (!FeatureFlags.UseElasticsearch || CustomerContext == null)
			return;

		var settings = new ElasticsearchClientSettings(new Uri(CustomerContext.ElasticConnectionString));
		// only needed for logging the requests and responses
		settings.DisableDirectStreaming();
		ElasticClient = new ElasticsearchClient(settings);
	}

	private void InitMongoDb()
	{
		if (CustomerContext == null || string.IsNullOrEmpty(CustomerContext.MongoDbConnectionString))
			return;

		MongoDbClient = _mongoClients.GetOrAdd(CustomerContext.MongoDbConnectionString, connectionString =>
		{
			var mongoClientSettings = MongoClientSettings.FromUrl(new MongoUrl(connectionString));
			mongoClientSettings.ClusterConfigurator = cb =>
			{
				cb.Subscribe<CommandStartedEvent>(e => { logger.Debug($"{e.CommandName} - {e.Command.ToJson()}"); });
				var options = new InstrumentationOptions { CaptureCommandText = true };
				cb.Subscribe(new DiagnosticsActivityEventSubscriber(options));
			};
			return new MongoClient(mongoClientSettings);
		});
	}
}