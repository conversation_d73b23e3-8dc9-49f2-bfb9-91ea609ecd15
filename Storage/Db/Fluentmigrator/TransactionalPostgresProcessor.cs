using System.Data;
using FluentMigrator;
using FluentMigrator.Runner;
using FluentMigrator.Runner.Generators.Postgres;
using FluentMigrator.Runner.Initialization;
using FluentMigrator.Runner.Processors;
using FluentMigrator.Runner.Processors.Postgres;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Levelbuild.Domain.Storage.Db.Fluentmigrator;

/// <inheritdoc/>
public class TransactionalPostgresProcessor : PostgresProcessor
{
	private readonly bool _isOwnTransaction;
	
	/// <inheritdoc/>
	[Obsolete]
	public TransactionalPostgresProcessor(IDbConnection connection, IMigrationGenerator generator, IAnnouncer announcer, IMigrationProcessorOptions options, IDbFactory factory, PostgresOptions pgOptions) : base(connection, generator, announcer, options, factory, pgOptions)
	{
	}

	/// <inheritdoc/>
	public TransactionalPostgresProcessor(PostgresDbFactory factory, PostgresGenerator generator, ILogger<PostgresProcessor> logger, IOptionsSnapshot<ProcessorOptions> options, IConnectionStringAccessor connectionStringAccessor, PostgresOptions pgOptions, Func<IDbConnection>? connection, Func<IDbTransaction>? transaction) : base(factory, generator, logger, options, connectionStringAccessor, pgOptions)
	{
		if (connection != null)
			Connection = connection();
		if (transaction != null)
		{
			Transaction = transaction();
			_isOwnTransaction = true;
		}  
	}

	//
	public override void BeginTransaction()
	{
		if (Transaction != null) return;

		EnsureConnectionIsOpen();

		Logger.LogSay("Not beginning Transaction");

		if (!_isOwnTransaction)
			Transaction = Connection?.BeginTransaction();
	}

	public override void RollbackTransaction()
	{
		if (_isOwnTransaction)
			Connection = null;
		
		if (Transaction == null)
			return;

		Logger.LogSay("Not rolling back transaction");
		if (!_isOwnTransaction)
		{
			Transaction.Rollback();
			Transaction.Dispose();
		}
		WasCommitted = true;
		Transaction = null;
		
	}

	public override void CommitTransaction()
	{	
		if (Transaction == null) return;

		Logger.LogSay("Not committing Transaction");
		if (!_isOwnTransaction)
		{
			Transaction.Commit();
			Transaction.Dispose();
			WasCommitted = true;
			Transaction = null;
		}
	}
}