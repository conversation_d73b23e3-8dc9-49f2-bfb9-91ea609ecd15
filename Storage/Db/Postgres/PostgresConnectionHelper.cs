using System.Data;
using System.Data.Common;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Npgsql;
using Serilog;
using SqlKata.Compilers;
using SqlKata.Execution;

namespace Levelbuild.Domain.Storage.Db.Postgres;

public class PostgresConnectionHelper : SqlConnectionHelper
{
	public PostgresConnectionHelper(string connectionString, ILogger logger) : base(connectionString, logger)
	{
	}

	protected override DbConnection GetConnection(string connectionString)
	{
		return new NpgsqlConnection(connectionString);
	}

	public override DbDataReader GetReader(string sql, DbConnection connection,
										   CommandBehavior? commandBehavior = null)
	{
		var cmd = new NpgsqlCommand(sql, (NpgsqlConnection)connection);
		if (commandBehavior.HasValue)
			return cmd.ExecuteReader(commandBehavior.Value);
		return cmd.ExecuteReader();
	}

	protected internal override Compiler GetCompiler()
	{
		return new PostgresCompiler();
	}

	protected internal override void CreateDatabase(string databaseName, DbConnection connection)
	{
		if (connection.State != ConnectionState.Open)
			connection.Open();
		
		var selectSql = "SELECT count(*) FROM pg_database WHERE lower(datname) = lower('" + databaseName + "')";
		using var selectCmd = new NpgsqlCommand(selectSql, (NpgsqlConnection)connection);
		long count = (long)(selectCmd.ExecuteScalar() ?? 0);

		if (count != 0)
		{
			throw new DataStoreOperationException($"Database {databaseName} already exists");
		}
		
		var createSql = "CREATE DATABASE \"" + databaseName + "\"";
		using var createCmd = new NpgsqlCommand(createSql, (NpgsqlConnection)connection);
		createCmd.ExecuteNonQuery();
	}

	protected override string[] GetSupportedLanguages()
	{
		var langs = new PostgresLanguages();
		return WithQueryFactory(factory => factory.Query("pg_ts_config").Select("cfgname").OrderBy("cfgname").Get()).Select(lang =>
				((IDictionary<string, object>)lang)).Select(it => langs.Reverse((string)it["cfgname"])).Where(it => it != null).ToArray()!;
	}
}