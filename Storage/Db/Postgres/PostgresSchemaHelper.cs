using System.Data;
using System.Data.Common;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Microsoft.Data.SqlClient;

namespace Levelbuild.Domain.Storage.Db.Postgres;

public class PostgresSchemaHelper : SqlSchemaHelper
{
	protected override void AddColsDefault(DbConnection connection, string storageIndexDefinition,
										   List<StorageFieldDefinitionOrm> fields)
	{
		var restrictions = new string[4];
		restrictions[2] = storageIndexDefinition;
		var tableSchema = connection.GetSchema("Columns", restrictions);
		var attrList = tableSchema.Columns;
		var nameIndex = attrList.IndexOf("COLUMN_NAME");
		var defaultIndex = attrList.IndexOf("COLUMN_DEFAULT");
		var columns = tableSchema.Rows;
		//tableSchema.Constraints

		foreach (DataRow column in columns)
		{
			var col = fields.Single(it => it.Name == (string)column[nameIndex]);
			var def = column[defaultIndex];
			/*if (def is not DBNull)
			{
				switch (def as string)
				{
					case "(getdate())":
						col.DefaultValue = SystemMethods.CurrentDateTime.ToString();
						break;
					default:
						var s = def as string;
						var match = Regex.Match(s, "\\(N?'(.*)'\\)");
						if (match.Success)
						{
							s = match.Groups[1].Value;
						}
						col.DefaultValue = s;
						break;
				}
			}*/
		}
	}

	protected override HashSet<string> GetUniqueCols(DbConnection connection, string storageIndexDefinition)
	{
		HashSet<string> uniqueCols = new();
		// Abfrage, um Informationen zu Unique Constraints abzurufen
		var query = @"
            SELECT COL_NAME(ic.object_id,ic.column_id) AS column_name
                FROM sys.indexes AS i
                INNER JOIN sys.index_columns AS ic
                    ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                WHERE 
                (i.is_unique = 1 or i.is_unique_constraint = 1)
                and i.object_id = OBJECT_ID(@DefinitionName)";

		using (var command = new SqlCommand(query, (SqlConnection)connection))
		{
			command.Parameters.AddWithValue("@DefinitionName", storageIndexDefinition);
			using (var reader = command.ExecuteReader())
			{
				while (reader.Read())
				{
					var columnName = reader.GetString(0);
					uniqueCols.Add(columnName);
				}
			}
		}

		return uniqueCols;
	}
}