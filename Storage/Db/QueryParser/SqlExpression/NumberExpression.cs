using System.Globalization;
using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Domain.Storage.Db.QueryParser.SqlExpression;

/// <inheritdoc/>
public class NumberExpression : SqlExpression
{
	/// <summary>
	/// The representing value
	/// </summary>
	private double Value { get; }

	/// <inheritdoc/>
	public NumberExpression(Db db, double value): base(db)
	{
		Value = value;
	}

	/// <inheritdoc/>
	protected internal override List<ColumnExpression> GetColumnExpressions()
	{
		return [];
	}

	/// <inheritdoc/>
	public override string AsString() => Value.ToString(CultureInfo.InvariantCulture);

	/// <inheritdoc/>
	protected internal override void DetermineType()
	{
		MultiValue = false;
		Type = DataStoreFieldType.Double;
		Nullable = false;
	}
}