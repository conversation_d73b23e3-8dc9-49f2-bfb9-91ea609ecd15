using System.Globalization;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Domain.Storage.Db.QueryParser.SqlExpression;

namespace Levelbuild.Domain.Storage.Db.QueryParser;

/// <summary>
/// Parse a generic sql expression string
/// </summary>
public class SqlExpressionParser
{
	private readonly Db _db;
	private readonly string _input;
	private int _position;
	private readonly bool _allowAggregateFunctions;

	/// <summary>
	/// 
	/// </summary>
	/// <param name="db"></param>
	/// <param name="input"></param>
	/// <param name="allowAggregateFunctions"></param>
	public SqlExpressionParser(Db db, string input, bool allowAggregateFunctions)
	{
		_db = db;
		_input = input;
		_position = 0;
		_allowAggregateFunctions = allowAggregateFunctions;
	}

	/// <summary>
	/// Parses the expression
	/// </summary>
	/// <returns></returns>
	public SqlExpression.SqlExpression Parse()
	{
		return ParseExpression();
	}

	private SqlExpression.SqlExpression ParseExpression()
	{
		var left = ParseTerm();
		while (Match("+", "-"))
		{
			string op = Previous();
			var right = ParseTerm();
			left = new BinaryExpression(_db, left, op, right);
		}

		return left;
	}

	private SqlExpression.SqlExpression ParseTerm()
	{
		var left = ParseFactor();
		while (Match("*", "/"))
		{
			string op = Previous();
			var right = ParseFactor();
			left = new BinaryExpression(_db, left, op, right);
		}

		return left;
	}

	private SqlExpression.SqlExpression ParseFactor()
	{
		if (MatchNumber(out double number))
		{
			return new NumberExpression(_db, number);
		}

		if (MatchIdentifier(out string identifier))
		{
			if (Match("("))
			{
				if (Enum.TryParse<DataStoreColumnAggregationFunction>(identifier, true, out var aggregationFunction))
				{
					if (Match("*"))
					{
						if (aggregationFunction != DataStoreColumnAggregationFunction.Count)
							throw new DataStoreQueryException($"Can only do count(*) aggregations. Found {identifier} in input '{_input}' at position {_position}");
						
						Consume(")", $"Expected closing parenthesis for {identifier} in input {_input} at position {_position}");

						var column = new RawExpression(_db, "*", false, DataStoreFieldType.String, null);
						return new AggregateExpression(_db, aggregationFunction, column);
					}
					var expression = ParseExpression();
					Consume(")", $"Expected closing parenthesis for {identifier} in input {_input} at position {_position}");
					
					if (!_allowAggregateFunctions)
						throw new DataStoreQueryException($"Aggregation functions are not allowed in this context. Found {identifier} in input '{_input}' at position {_position}");
					if (aggregationFunction == DataStoreColumnAggregationFunction.Avg)
						expression = new CastExpression(_db, expression, DataStoreFieldType.Double);
					return new AggregateExpression(_db, aggregationFunction, expression);
				}

				throw new DataStoreQueryException($"Unknown function: {identifier} in input '{_input}' at position {_position}");
			}

			return new ColumnExpression(_db, identifier);
		}

		if (Match("("))
		{
			var expression = ParseExpression();
			Consume(")", $"Expected closing parenthesis. Found opening parenthesis in input '{_input}' at position {_position} instead.");
			return expression;
		}

		throw new DataStoreQueryException($"Unexpected token in input '{_input}' at position {_position}");
	}

	private bool Match(params string[] tokens)
	{
		foreach (var token in tokens)
		{
			if (LookAhead(token))
			{
				_position += token.Length;
				return true;
			}
		}

		return false;
	}

	private string Previous()
	{
		return _input.Substring(_position - 1, 1);
	}

	private void Consume(string expected, string errorMessage)
	{
		if (!Match(expected))
		{
			throw new DataStoreQueryException(errorMessage);
		}
	}

	private bool MatchNumber(out double number)
	{
		SkipWhitespace();
		int start = _position;
		while (_position < _input.Length && (char.IsDigit(_input[_position]) || _input[_position] == '.'))
		{
			_position++;
		}

		if (start == _position)
		{
			number = 0;
			return false;
		}

		string numStr = _input[start.._position];
		number = double.Parse(numStr, CultureInfo.InvariantCulture);
		SkipWhitespace();
		return true;
	}

	private bool MatchIdentifier(out string identifier)
	{
		SkipWhitespace();
		int start = _position;
		while (_position < _input.Length && (char.IsLetterOrDigit(_input[_position]) || _input[_position] == '_' || _input[_position] == '.'))
		{
			_position++;
		}

		if (start == _position)
		{
			identifier = "";
			return false;
		}

		identifier = _input[start.._position];
		SkipWhitespace();
		return true;
	}

	private bool LookAhead(string expected)
	{
		SkipWhitespace();
		return _input[_position..].StartsWith(expected);
	}

	private void SkipWhitespace()
	{
		while (_position < _input.Length && char.IsWhiteSpace(_input[_position]))
		{
			_position++;
		}
	}
}