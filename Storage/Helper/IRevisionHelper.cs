using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using SqlKata.Execution;

namespace Levelbuild.Domain.Storage.Helper;

public interface IRevisionHelper
{
	public long GetCurrentRevisionNumberWithFile(StorageIndexDefinition definition, string elementId);

	public void CreateRevision(QueryFactory? queryFactory, StorageIndexDefinition storageIndexDefinition, string elementId,
							   IDictionary<string, object?> values, DataStoreOperationOrigin origin, bool hasFile,
							   DataStoreOperationType operationType, long newRevNumber, IDictionary<string, object?>? oldValues = null);

	public void CreateMultipleRevisionsAfterInsert(QueryFactory queryFactory, StorageIndexDefinition storageIndexDefinition,
												   DataStoreResultSet<DataStoreElement> elementDatas,
												   DataStoreOperationOrigin origin, bool hasFile, DataStoreOperationType operationType);

	public DataStoreRevisionData? GetRevision(StorageIndexDefinition storageIndexDefinition, string elementId, string revisionId);

	public DataStoreRevisionData GetRevision(StorageIndexDefinition storageIndexDefinition, string elementId, long revisionNumber);

	public void AddRevision(QueryFactory queryFactory, StorageIndexDefinition storageIndexDefinition, string elementId,
							IDictionary<string, object?> values, IDictionary<string, object?>? oldValues, DataStoreOperationOrigin origin,
							bool hasFile, long newRevNumber);

	public DataStoreResultSet<DataStoreRevisionInfo> GetRevisionsByField(StorageIndexDefinition storageIndexDefinition, string elementId,
																		 string fieldName);

	public void DeleteRevisionsForElement(StorageIndexDefinition storageIndexDefinition, string elementId);

	public void DeleteRevision(StorageIndexDefinition storageIndexDefinition, string elementId, string revisionId);

	public DataStoreResultSet<DataStoreRevisionInfo> GetRevisions(DataStoreRevisionQuery dataStoreRevisionQuery);
}