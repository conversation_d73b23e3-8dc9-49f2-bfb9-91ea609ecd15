using System.Collections;
using System.Runtime.ExceptionServices;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Npgsql;
using Serilog;

namespace Levelbuild.Domain.Storage.Helper;

/// <summary>
/// Helper for various database schema migrations tasks
/// </summary>
public class MigrationHelper
{
	private FeatureFlags _featureFlags;
	private readonly Db.Db _db;
	private readonly ILogger _logger;
	private readonly DbFactory _dbFactory;

	/// <summary>
	/// 
	/// </summary>
	/// <param name="featureFlags"></param>
	/// <param name="db"></param>
	/// <param name="dbFactory"></param>
	/// <param name="logger"></param>
	public MigrationHelper(FeatureFlags featureFlags, Db.Db db, DbFactory dbFactory, ILogger logger)
	{
		_featureFlags = featureFlags;
		_db = db;
		_dbFactory = dbFactory;
		_logger = logger;
	}

	public void CreateDatabaseAndMigrateStorageTables(StorageContextOrm storageContext, out string? contextDatabaseName)
	{
		var db = _dbFactory.GetDb(storageContext.DatabaseConnectionString, storageContext);
		contextDatabaseName = null;
		try
		{
			using (NpgsqlConnection connection = new NpgsqlConnection(storageContext.DatabaseConnectionString))
			{
				contextDatabaseName = connection.Database;
				_db.ConnectionHelper.CreateDatabase(contextDatabaseName);
			}

			Exception? exception = null;
			for (int i = 0; i < 500; i++)
			{
				try
				{
					using (var database = db.GetEfCoreDbContext())
					{
						exception = null;
						database.Database.Migrate();
						break;
					}
				}
				catch (PostgresException e)
				{
					if (!e.Message.Contains("terminating connection due to administrator command"))
						throw;
					exception = e;
				}
			}

			if (exception != null)
				ExceptionDispatchInfo.Capture(exception).Throw();
		}
		catch (DataStoreOperationException)
		{
			throw;
		}
		catch (Exception e)
		{
			throw new DataStoreOperationException("Exception creating or migrating database '"
												  + contextDatabaseName + "' for " + storageContext.Identifier, e);
		}
	}

	/// <summary>
	/// Initial table migration on creating a new context
	/// </summary>
	/// <param name="mainConnection"></param>
	/// <param name="customerConnection"></param>
	public void MigrateInitialTables(StorageConnection mainConnection, StorageConnection customerConnection)
	{
		// create temp customer connection for migration
		var dataSources = mainConnection.GetDataSources();

		// First create data sources without fields due to maybe missing lookup sources
		foreach (var dataSource in dataSources)
		{
			var storageIndexDefinition = mainConnection.GetIndexDefinition(dataSource);
			customerConnection.CreateDataSource(storageIndexDefinition!.ToDto(), true);
		}

		// afterwards create fields for data sources, if not existing in customer data sources
		foreach (var dataSource in dataSources)
		{
			var storageIndexDefinition = mainConnection.GetIndexDefinition(dataSource);
			var storageIndexDefinitionCustomer = customerConnection.GetIndexDefinition(dataSource);
			foreach (var storageFieldDefinitionOrm in storageIndexDefinition!.Fields)
			{
				var test = storageIndexDefinitionCustomer!.Fields.Where(it => it.Name == storageFieldDefinitionOrm.Name);
				if (test.Count() == 0)
					customerConnection.CreateField(storageIndexDefinition.Name, storageFieldDefinitionOrm.ToDto(), true);
			}
		}

		var storageSchemaChanges = mainConnection.WithDbContext(db => { return db.StorageSchemaChange.OrderBy(it => it.Id).ToList(); });

		// ...and add StorageSchemaChanges
		foreach (StorageSchemaChange storageSchemaChange in storageSchemaChanges)
		{
			customerConnection.WithDbContext(db =>
			{
				db.StorageSchemaChange.Add(new StorageSchemaChange(storageSchemaChange));
				return db.SaveChanges();
			});
		}
	}

	private static void CreateDataSource(StorageConnection customerConnection, StorageIndexDefinition? storageIndexDefinition)
	{
		customerConnection.CreateDataSource(storageIndexDefinition!.ToDto(), true);
		var createdDefinition = customerConnection.GetIndexDefinition(storageIndexDefinition.Name);
		foreach (var storageFieldDefinitionOrm in storageIndexDefinition.Fields)
		{
			var test = createdDefinition!.Fields.Where(it => it.Name == storageFieldDefinitionOrm.Name);
			if (test.Count() == 0)
				customerConnection.CreateField(storageIndexDefinition.Name, storageFieldDefinitionOrm.ToDto(), true);
		}
	}

	public async Task RunMigrationsInCustomerDatabases(StorageConnection mainConnection, StorageConnectionFactory storageConnectionFactory,
													   StorageSchemaChange storageSchemaChange)
	{
		var contexts = mainConnection.GetContexts();
		List<Task> tasks = new List<Task>();
		foreach (StorageContext storageContext in contexts)
		{
			tasks.Add(Task.Run(() =>
			{
				var storageContextOrm = new StorageContextOrm(storageContext);
				try
				{
					using StorageConnection customerConnection = storageConnectionFactory.GetConnection(storageContextOrm);
					// create temp customer connection and run all migrations up to this point
					GetCustomerConnection(storageContextOrm, mainConnection, customerConnection, true);
				}
				catch (Exception e)
				{
					SaveStorageSchemaChangeError(mainConnection, storageContext.Identifier, storageSchemaChange);
					_logger.Error(e, "Database migration failed for customer {ContextId} - {@StorageSchemaChange}", storageContext.Identifier,
								  storageSchemaChange);
				}
			}));
		}

		await Task.WhenAll(tasks).ConfigureAwait(false);
	}

	private static void SaveStorageSchemaChangeError(StorageConnection mainConnection, string storageContextIdentifier, StorageSchemaChange storageSchemaChange)
	{
		mainConnection.WithDbContext(db =>
		{
			StorageSchemaChangeError storageSchemaChangeError = new StorageSchemaChangeError(storageSchemaChange.Hash, storageContextIdentifier);
			db.StorageSchemaChangeError.Add(storageSchemaChangeError);
			return db.SaveChanges();
		});
	}

	public void DeleteCustomerDatabase(StorageContextOrm storageContextOrm)
	{
		var db = _dbFactory.GetDb(storageContextOrm.DatabaseConnectionString, storageContextOrm);
		string? dbName = null;
		try
		{
			using var storageContext = db.GetEfCoreDbContext();
			dbName = storageContext.Database.GetDbConnection().Database;
			storageContext.Database.EnsureDeleted();
		}
		catch (Exception e)
		{
			Log.Error(e, "Database deletion failed for customer {ContextId} - DB: {Database}", storageContextOrm.Identifier, dbName);
		}
	}

	private StorageConnection GetCustomerConnection(StorageContextOrm storageContext, StorageConnection? mainConnection, StorageConnection customerConnection,
													bool force = false)
	{
		if (mainConnection != null)
		{
			Storage.CheckMigrations(storageContext.DatabaseConnectionString, customerConnection, storageContext.Identifier, mainConnection,
									_logger, force);
		}

		return customerConnection;
	}

	public static void CheckSchemaChanges(StorageConnection mainConnection, StorageConnection customerStorageConnection)
	{
		string? lastCustomerStorageSchemaChangeHash = customerStorageConnection.WithDbContext(db =>
		{
			return db.StorageSchemaChange.OrderByDescending(it => it.Id)
				.Select(it => it.Hash).FirstOrDefault();
		});

		var storageMainSchemaChangeIdByCustomer = mainConnection.WithDbContext(db =>
		{
			return db.StorageSchemaChange.Where(it => it.Hash == lastCustomerStorageSchemaChangeHash)
				.Select(it => it.Id).FirstOrDefault();
		});

		var schemaChanges = mainConnection.WithDbContext(db =>
		{
			return db.StorageSchemaChange.OrderBy(it => it.Id)
				.Where(it => it.Id > storageMainSchemaChangeIdByCustomer).ToList();
		});

		Exception? exception = null;
		var failedMigrationTables =
			StorageConnectionFactory.FailedMigrationTables.GetOrAdd(customerStorageConnection.Db.ConnectionString, new HashSet<string>());
		failedMigrationTables.Clear();
		foreach (var storageSchemaChange in schemaChanges)
		{
			if (exception != null)
			{
				failedMigrationTables.Add(storageSchemaChange.DataSourceName);
				continue;
			}

			try
			{
				AddMissingSchemaChange(mainConnection, customerStorageConnection, new StorageSchemaChange(storageSchemaChange));
			}
			catch (Exception e)
			{
				failedMigrationTables.Add(storageSchemaChange.DataSourceName);
				exception = e;
			}
		}

		if (exception != null)
			throw new DataStoreOperationException($"Migrations failed for tables: {string.Join(", ", failedMigrationTables)}.", exception);
	}

	private static void AddMissingSchemaChange(StorageConnection mainConnection, StorageConnection customerConnection, StorageSchemaChange storageSchemaChange)
	{
		switch (storageSchemaChange.Operation)
		{
			case SchemaChangeOperation.CreateField:
				StorageFieldConfig storageFieldCreate = JsonConvert.DeserializeObject<StorageFieldConfig>(storageSchemaChange.SourceObject!)!;
				customerConnection.CreateField(storageSchemaChange.DataSourceName, storageFieldCreate, true);
				break;
			case SchemaChangeOperation.UpdateField:
				StorageFieldConfig storageFieldUpdate = JsonConvert.DeserializeObject<StorageFieldConfig>(storageSchemaChange.SourceObject!)!;
				customerConnection.UpdateField(storageSchemaChange.DataSourceName, storageFieldUpdate, true);
				break;
			case SchemaChangeOperation.RenameField:
				customerConnection.RenameField(storageSchemaChange.DataSourceName, storageSchemaChange.SourceObject!, storageSchemaChange.DestinationObject!,
											   true);
				break;
			case SchemaChangeOperation.RemoveField:
				customerConnection.RemoveField(storageSchemaChange.DataSourceName, storageSchemaChange.SourceObject!, true);
				break;
			case SchemaChangeOperation.CreateDataSource:
				StorageIndexDefinition storageIndexDefinitionCreate =
					JsonConvert.DeserializeObject<StorageIndexDefinition>(storageSchemaChange.SourceObject!)!;
				var customerDefinitionCreate = customerConnection.GetDataSource(storageIndexDefinitionCreate.Name);
				if (customerDefinitionCreate == null)
				{
					CreateDataSource(customerConnection, storageIndexDefinitionCreate);
					MongoDbMigrationHelper.MigrateMongoDb(customerConnection, storageIndexDefinitionCreate.Name);
				}

				break;
			case SchemaChangeOperation.UpdateDataSource:
				StorageIndexDefinition storageIndexDefinitionUpdate =
					JsonConvert.DeserializeObject<StorageIndexDefinition>(storageSchemaChange.SourceObject!)!;
				var customerDefinitionUpdate = customerConnection.GetDataSource(storageIndexDefinitionUpdate.Name);
				if (customerDefinitionUpdate?.CustomerSpecific != true)
					customerConnection.UpdateDataSource(storageIndexDefinitionUpdate.ToDto(), true);
				break;
			case SchemaChangeOperation.RemoveDataSource:
				var customerDefinitionRemove = customerConnection.GetDataSource(storageSchemaChange.DataSourceName);
				if (customerDefinitionRemove?.CustomerSpecific != true)
					customerConnection.RemoveDataSource(storageSchemaChange.DataSourceName, true);
				break;
		}

		var changes = customerConnection.WithDbContext(db =>
		{
			return db.StorageSchemaChange.Where(it => it.Id > 0)
				.Select(it => new DictionaryEntry(it.Id, it.Hash)).ToList();
		});

		// ...and add StorageSchemaChange
		customerConnection.WithDbContext(db =>
		{
			db.StorageSchemaChange.Add(storageSchemaChange);
			return db.SaveChanges();
		});

		// delete error, if exists
		mainConnection.WithDbContext(db =>
		{
			return db.StorageSchemaChangeError
				.Where(it => it.StorageSchemaChangeHash == storageSchemaChange.Hash
							 && it.StorageContextIdentifier == customerConnection.Db.CustomerContext!.Identifier)
				.ExecuteDelete();
		});
	}
}