using Levelbuild.Domain.Storage.Db;
using Serilog;

namespace Levelbuild.Domain.Storage.Helper;

/// <summary>
/// Factory for <see cref="MigrationHelper"/>
/// </summary>
public class MigrationHelperFactory
{
	private readonly FeatureFlags _featureFlags;
	private readonly ILogger _logger;
	private readonly DbFactory _dbFactory;

	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="featureFlags"></param>
	/// <param name="dbFactory"></param>
	/// <param name="logger"></param>
	public MigrationHelperFactory(FeatureFlags featureFlags, DbFactory dbFactory, ILogger logger)
	{
		_featureFlags = featureFlags;
		_dbFactory = dbFactory;
		_logger = logger;
	}

	/// <summary>
	/// Returns a <see cref="MigrationHelper"/>
	/// </summary>
	/// <param name="db"></param>
	/// <returns></returns>
	public MigrationHelper CreateMigrationHelper(Db.Db db)
	{
		return new MigrationHelper(_featureFlags, db, _dbFactory, _logger);
	}
}