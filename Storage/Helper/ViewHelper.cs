using System.ComponentModel.DataAnnotations;
using Elastic.Clients.Elasticsearch;
using FluentMigrator.Model;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace Levelbuild.Domain.Storage.Helper;

public class ViewHelper
{
	private static ILogger _logger = Log.ForContext<ViewHelper>();

	public static void MigrateViews(StorageConnection storageMainConnection, ILogger logger)
	{
		_logger = logger;
		var viewList = storageMainConnection.WithDbContext(db =>
															   db.Database.SqlQuery<ViewColumn>($"""
																								 select "views".table_name as ViewName, "cols".column_name as ColumnName, "cols".data_type as DataType, "cols".udt_name as UdtName, "cols".character_maximum_length as MaxLength from INFORMATION_SCHEMA.views as "views" 
																								 join information_schema.columns as "cols" on "views".table_name = "cols".table_name
																								 WHERE "views".table_schema = ANY (current_schemas(false))
																								 ORDER BY "views".table_name, "cols".ordinal_position;
																								 """).ToList());

		if (viewList.Count == 0)
			return;

		Dictionary<string, List<ViewColumn>> views = new Dictionary<string, List<ViewColumn>>();
		foreach (ViewColumn viewColumn in viewList)
		{
			List<ViewColumn> view = null;
			if (views.ContainsKey(viewColumn.ViewName))
				view = views[viewColumn.ViewName];
			else
				view = new List<ViewColumn>();

			view.Add(viewColumn);
			views[viewColumn.ViewName] = view;
		}

		List<StorageIndexDefinition> defs = storageMainConnection.WithDbContext(db => { return db.StorageIndexDefinition.Include(it => it.Fields).ToList(); });


		var viewDefinitions = storageMainConnection.WithDbContext(db =>
																	  db.Database.SqlQuery<ViewDefinition>($"""
																		   select viewname as "Viewname", definition as "Statement" from pg_views where schemaname = 'public';
																		   """).ToList());
		ProcessViews(storageMainConnection, defs, viewDefinitions, views);
	}

	private static void ProcessViews(StorageConnection storageMainConnection, List<StorageIndexDefinition> defs, List<ViewDefinition> viewDefinitions,
									 Dictionary<string, List<ViewColumn>> views)
	{
		foreach (var (viewName, viewColumns) in views)
		{
			List<string> systemFieldNames = new List<string>()
			{
				StorageSystemField.Id.ToString(), StorageSystemField.SysGroups.ToString(),
				StorageSystemField.SysInactiveDate.ToString(), StorageSystemField.SysFavourites.ToString(), StorageSystemField.SysIsDeleted.ToString()
			};

			if (!systemFieldNames.All(x => (viewColumns.Where(column => column.ColumnName == x).Count() > 0)))
			{
				_logger.Information(
					$"View {viewName} does not contain all system fields ({string.Join(", ", systemFieldNames)}) and will not be created.");
				continue;
			}

			var viewDefinition = viewDefinitions.Where(it => it.Viewname == viewName).FirstOrDefault();
			if (viewDefinition == null)
			{
				_logger.Warning($"No viewDefinition found for {viewName}!");
				continue;
			}

			StorageIndexDefinition? definition = defs.Where(it => it.Name == viewName).FirstOrDefault();
			if (definition != null && !string.IsNullOrWhiteSpace(definition.ViewDefinition)
								   && (definition.ViewDefinition.Trim()) == viewDefinition.Statement.Trim())
			{
				continue;
			}

			CreateOrUpdateViewDefinition(storageMainConnection, viewName, viewColumns, viewDefinition, systemFieldNames, (definition == null));
		}
	}

	private static void CreateOrUpdateViewDefinition(StorageConnection storageMainConnection, string viewName,
													 List<ViewColumn> viewColumns, ViewDefinition viewDefinition, List<string> systemFieldNames,
													 bool createDefinition)
	{
		List<StorageFieldDefinitionOrm> fields = GetFieldDefinitions(viewColumns, systemFieldNames);
		StorageIndexDefinition indexDefinition = new StorageIndexDefinition(viewName, fields);
		indexDefinition.IsView = true;
		indexDefinition.ViewDefinition = viewDefinition.Statement;
		indexDefinition.StoreRevisions = false;

		if (createDefinition)
			storageMainConnection.CreateDataSource(indexDefinition.ToDto());
		else
			storageMainConnection.UpdateDataSource(indexDefinition.ToDto());

		// get created or updated definition clean from database
		StorageIndexDefinition? savedDefinition = storageMainConnection.WithDbContext(db =>
		{
			return db.StorageIndexDefinition.Include(it => it.Fields)
				.FirstOrDefault(it => it.Name == viewName);
		});

		if (savedDefinition is null)
			throw new DataStoreOperationException("Error while creating StorageIndexDefinition for view.");

		foreach (StorageFieldDefinitionOrm newField in fields)
		{
			StorageFieldDefinitionOrm? existingField = savedDefinition.Fields.Where(field => field.Name == newField.Name).FirstOrDefault();

			if (existingField is null)
				storageMainConnection.CreateField(savedDefinition.Name, newField.ToDto());
			else
				storageMainConnection.UpdateField(savedDefinition.Name, newField.ToDto());
		}

		foreach (StorageFieldDefinitionOrm existingField in savedDefinition.Fields)
		{
			StorageFieldDefinitionOrm? newField = fields.Where(field => field.Name == existingField.Name).FirstOrDefault();
			if (newField is null && !existingField.SystemField && !existingField.PrimaryKey)
				storageMainConnection.RemoveField(savedDefinition.Name, existingField.Name);
		}
	}

	private static List<StorageFieldDefinitionOrm> GetFieldDefinitions(List<ViewColumn> viewColumns, List<string> systemFieldNames)
	{
		List<StorageFieldDefinitionOrm> fields = new List<StorageFieldDefinitionOrm>();
		foreach (ViewColumn viewColumn in viewColumns)
		{
			if (!systemFieldNames.Contains(viewColumn.ColumnName))
			{
				var fieldType = DataStoreFieldType.String;
				switch (viewColumn.UdtName)
				{
					case "date":
					case "_date":
						fieldType = DataStoreFieldType.Date;
						break;
					case "float8":
					case "_float8":
						fieldType = DataStoreFieldType.Double;
						break;
					case "bool":
					case "_bool":
						fieldType = DataStoreFieldType.Boolean;
						break;
					case "timestamp":
					case "_timestamp":
						fieldType = DataStoreFieldType.DateTime;
						break;
					case "time":
					case "_time":
						fieldType = DataStoreFieldType.Time;
						break;
					case "int4":
					case "_int4":
						fieldType = DataStoreFieldType.Integer;
						break;
					case "int8":
					case "_int8":
						fieldType = DataStoreFieldType.Long;
						break;
					case "uuid":
					case "_uuid":
						fieldType = DataStoreFieldType.Guid;
						break;
					case "text":
					case "_text":
						fieldType = DataStoreFieldType.Text;
						break;
					case "varchar":
					case "_varchar":
						fieldType = DataStoreFieldType.String;
						break;
					default:
						throw new DataStoreConfigurationException($"Unknown field type {viewColumn.UdtName}");
						break;
				}

				var field = new StorageFieldDefinitionOrm(viewColumn.ColumnName)
				{
					Type = fieldType,
					Nullable = true,
					Readonly = true
				};

				if (viewColumn.MaxLength != null)
					field.Length = (int)viewColumn.MaxLength;

				field.MultiValue = (viewColumn.DataType == "ARRAY");
				fields.Add(field);
			}
		}

		return fields;
	}
}

class ViewDefinition
{
	public string Viewname { get; set; } = null!;
	public string Statement { get; set; } = null!;
}

class ViewColumn
{
	public string ViewName { get; set; } = null!;
	public string ColumnName { get; set; } = null!;
	public string DataType { get; set; } = null!;
	public string UdtName { get; set; } = null!;
	public int? MaxLength { get; set; }
}