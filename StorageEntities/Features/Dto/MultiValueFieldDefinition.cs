using System.Data;
using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Domain.StorageEntities.Features.Dto;

public class MultiValueFieldDefinition : StorageIndexDefinition
{
	public new const string Id = "Id";
	public const string ElementId = "ElementId";
	public const string FieldId = "FieldId";
	public const string FieldValue = "FieldValue";

	/// <summary>
	/// constructor for common mvf table, containing table name and field list
	/// </summary>
	/// <param name="tableName"></param>
	/// <param name="fieldList"></param>
	public MultiValueFieldDefinition(string tableName, List<StorageFieldDefinitionOrm> fieldList) : base(tableName,
																										 fieldList)
	{
		fieldList = new List<StorageFieldDefinitionOrm>()
		{
			new StorageFieldDefinitionOrm(MultiValueFieldDefinition.Id)
			{
				Type = DataStoreFieldType.Integer,
				PrimaryKey = true,
				Unique = true
			},
			new StorageFieldDefinitionOrm(MultiValueFieldDefinition.ElementId)
			{
				Type = DataStoreFieldType.Guid
			},
			new StorageFieldDefinitionOrm(MultiValueFieldDefinition.FieldId, DataStoreFieldType.Long),
			new StorageFieldDefinitionOrm(MultiValueFieldDefinition.FieldValue)
		};
		Fields = fieldList;
		StoreRevisions = false;
	}

	/// <summary>
	/// Defines the foreign key constraint to the main table conteining rule for deletion
	/// </summary>
	/// <param name="primaryTableName">Name of main table</param>
	/// <param name="primaryTableColumnName">name of primary key in main table</param>
	/// <param name="deleteRule">rule for deletion of data set main table (cascade, none, set default, set null)</param>
	public void DefineForeignKey(string primaryTableName, string primaryTableColumnName, Rule deleteRule)
	{
		StorageFieldDefinitionOrm foreignKeyField =
			Fields.Where(it => it.Name == MultiValueFieldDefinition.ElementId).First();
		StorageForeignKey foreignKey = new StorageForeignKey(primaryTableName, primaryTableColumnName, deleteRule);
		foreignKeyField.ForeignKey = foreignKey;
	}
}