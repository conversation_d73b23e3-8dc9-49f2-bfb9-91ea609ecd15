using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Domain.StorageEntities.Features.Dto;

/// <summary>
/// should be the counterpart to DataStoreRevisionValue in storage context
/// </summary>
public class RevisionFieldValue
{
	public const string REVISION_FIELD_ID = "Id";
	public const string REVISION_FIELD_NAME = "Name";
	public const string REVISION_FIELD_TYPE = "Type";
	public const string REVISION_FIELD_VALUE = "Value";
	public const string REVISION_FIELD_CHANGED = "Changed";
	public const string REVISION_FIELD_PREVIOUS_VALUE = "PreviousValue";
	/// <summary>
	/// constructor with necessary initial data
	/// </summary>
	/// <param name="name"></param>
	/// <param name="type"></param>
	/// <param name="value"></param>
	public RevisionFieldValue(string name, DataStoreFieldType type, object? value)
	{
		Name = name;
		Type = type;
		Value = value;
	}

	/// <summary>
	/// need additional Id to existing DataStoreRevisionValue object, cause name can change
	/// </summary>
	public long Id { get; set; }

	/// <summary>
	/// Name of the table column (field) the revision is saved for
	/// </summary>
	public string Name { get; init; }

	/// <summary>
	/// value type of the given data
	/// </summary>
	public DataStoreFieldType Type { get; init; }

	public object? Value { get; init; }

	/// <summary>
	/// Is the value changed compared to the previous value
	/// </summary>
	public bool Changed { get; init; }

	/// <summary>
	/// value before change
	/// </summary>
	public object? PreviousValue { get; init; }
}