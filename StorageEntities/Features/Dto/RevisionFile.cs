using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using FileInfo = Levelbuild.Core.FileInterface.FileInfo;

namespace Levelbuild.Domain.StorageEntities.Features.Dto;

public class RevisionFile
{
	private long _id;
	private string _elementId;
	private DateTime _revisionDateTime;
	private string _username;
	private string _revisionComment;
	private bool _hasFile;
	private DataStoreOperationType _operationType;
	private FileInfo _fileInfo;

	public FileInfo CurrentFileInfo
	{
		get => _fileInfo;
		set => _fileInfo = value;
	}

	public DataStoreOperationType OperationType
	{
		get => _operationType;
		set => _operationType = value;
	}

	public long Id
	{
		get => _id;
		set => _id = value;
	}

	public string ElementId
	{
		get => _elementId;
		set => _elementId = value ?? throw new ArgumentNullException(nameof(value));
	}

	public DateTime RevisionDateTime
	{
		get => _revisionDateTime;
		set => _revisionDateTime = value;
	}

	public string Username
	{
		get => _username;
		set => _username = value;
	}

	public string RevisionComment
	{
		get => _revisionComment;
		set => _revisionComment = value;
	}

	public bool HasFile
	{
		get => _hasFile;
		set => _hasFile = value;
	}

	[ExcludeFromCodeCoverage]
	public DataStoreOperationOrigin Origin
	{
		get => _origin;
		set => _origin = value;
	}

	private DataStoreOperationOrigin _origin;

	/// <summary>
	/// 
	/// </summary>
	/// <param name="origin"></param>
	#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	public RevisionFile(DataStoreOperationOrigin origin)
		#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	{
		_origin = origin;
	}

	/// <summary>
	/// FieldId => {FieldId, FieldChanged, FieldValue}
	/// </summary>
	public IDictionary<String, RevisionFieldValue> Fields { get; set; }

	/// <summary>
	/// transform to data store object for interface
	/// </summary>
	/// <param name="dataStoreRevisionInfo"></param>
	/// <returns></returns>
	public DataStoreRevisionData ToDto(DataStoreRevisionInfo dataStoreRevisionInfo)
	{
		List<DataStoreRevisionValue> values = new List<DataStoreRevisionValue>();
		foreach (var revFieldValue in Fields)
		{
			DataStoreRevisionValue value =
				new DataStoreRevisionValue(revFieldValue.Key, revFieldValue.Value.Type, revFieldValue.Value.Value)
				{
					Changed = revFieldValue.Value.Changed,
					PreviousValue = revFieldValue.Value.PreviousValue
				};
			values.Add(value);
		}

		DataStoreRevisionData data = new DataStoreRevisionData(dataStoreRevisionInfo.Id.ToString(),
															   RevisionDateTime,
															   Username, OperationType, values)
		{
			Username = dataStoreRevisionInfo.Username.ToString(),
			InitiatorId = Int64.Parse(dataStoreRevisionInfo.InitiatorId.ToString()),
			InitiatorName = dataStoreRevisionInfo.InitiatorName?.ToString(),
			ActionName = dataStoreRevisionInfo.ActionName?.ToString(),
			ActionId = Int64.Parse(dataStoreRevisionInfo.ActionId.ToString()),
			Comment = dataStoreRevisionInfo.Comment?.ToString()
		};

		return data;
	}
}