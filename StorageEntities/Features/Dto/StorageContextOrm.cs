using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FileInterface.Enum;
using Levelbuild.Core.StorageInterface.Constants;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;

namespace Levelbuild.Domain.StorageEntities.Features.Dto;

public class StorageContextOrm
{
	#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	protected StorageContextOrm()
		#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	{
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageContext"></param>
	#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	public StorageContextOrm(StorageContext storageContext)
		#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	{
		Identifier = storageContext.Identifier;
		FromConfig(storageContext.Config!);
	}

	/// <summary>
	/// Returns a new StorageContext object with the same property values as the current object 
	/// </summary>
	/// <returns></returns>
	public StorageContext ToDto()
	{
		// need to go one level up as this path always ends with /data
		var storagePath = StoragePath;
		if (storagePath.EndsWith("/"))
			storagePath = storagePath.Substring(0, storagePath.Length - 1);
		var lastSlash = storagePath.LastIndexOf("/");
		if (lastSlash > 0)
			storagePath = storagePath.Substring(0, lastSlash);

		var dict = new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, storagePath },
			{ StorageConfigurationConstants.DatabaseType, DatabaseType.ToString() },
			{ StorageConfigurationConstants.DatabaseConnectionString, DatabaseConnectionString },
			{ StorageConfigurationConstants.FilestoreType, FilestoreType.ToString() },
			{ StorageConfigurationConstants.Language, Language },
			{ StorageConfigurationConstants.ElasticConnectionString, ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, MongoDbConnectionString! }
		};

		if (Description != null)
			dict.Add(StorageConfigurationConstants.Description, Description);
		if (FilestoreContainer != null)
			dict[StorageConfigurationConstants.FilestoreContainer] = FilestoreContainer;
		if (FilestoreConnectionString != null)
			dict[StorageConfigurationConstants.FilestoreUrl] = FilestoreConnectionString;
		if (FilestoreUsername != null)
			dict[StorageConfigurationConstants.FilestoreUsername] = FilestoreUsername;
		if (FilestorePassword != null)
			dict[StorageConfigurationConstants.FilestorePassword] = FilestorePassword;

		if (FilestoreType == FileStoreType.S3Bucket)
			dict[StorageConfigurationConstants.FilestoreS3PathStyleUrl] = S3PathStyleUrl.ToString().ToLower();

		return new StorageContext(Identifier, dict);
	}


	[ExcludeFromCodeCoverage]
	[Key]
	[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
	public long Id { get; set; }

	public string StoragePath { get; set; }
	public string TemporaryPath { get; set; }
	public string Identifier { get; set; }
	public string? Description { get; set; }

	public FileStoreType FilestoreType { get; set; }
	public string? FilestoreContainer { get; set; }

	[AllowNull]
	public string? FilestoreConnectionString { get; set; }

	public bool S3PathStyleUrl { get; set; } = true;

	public string? FilestoreUsername { get; set; }
	public string? FilestorePassword { get; set; }

	public string DatabaseConnectionString { get; set; }

	public string Language { get; set; }

	public string ElasticConnectionString { get; set; }
	public string? MongoDbConnectionString { get; set; }

	public SqlType DatabaseType { get; set; }

	public void FromConfig(IDictionary<string, object?> config)
	{
		if (config.TryGetValue(StorageConfigurationConstants.Description, out var option))
			Description = option?.ToString();

		if (config.TryGetValue(StorageConfigurationConstants.DatabaseConnectionString, out option))
			DatabaseConnectionString = option!.ToString()!;

		if (config.TryGetValue(StorageConfigurationConstants.DatabaseType, out option))
		{
			if (System.Enum.TryParse<SqlType>(option!.ToString(), ignoreCase: true, out var type))
				DatabaseType = type;
		}

		if (config.TryGetValue(StorageConfigurationConstants.StoragePath, out option))
		{
			string path = (option!.ToString()!.EndsWith("/") ? option : option + "/").ToString()!;
			StoragePath = path + "data";
			TemporaryPath = path + "temp";
		}

		if (config.TryGetValue(StorageConfigurationConstants.FilestoreUrl, value: out option))
			FilestoreConnectionString = option?.ToString();

		if (config.TryGetValue(StorageConfigurationConstants.FilestoreContainer, out option))
			FilestoreContainer = option?.ToString();

		if (config.TryGetValue(StorageConfigurationConstants.FilestoreUsername, out option))
			FilestoreUsername = option?.ToString();

		if (config.TryGetValue(StorageConfigurationConstants.FilestorePassword, out option))
			FilestorePassword = option?.ToString();

		if (config.TryGetValue(StorageConfigurationConstants.FilestoreType, out option))
		{
			if (System.Enum.TryParse<FileStoreType>(option!.ToString(), ignoreCase: true, out var type))
				FilestoreType = type;
		}

		if (config.TryGetValue(StorageConfigurationConstants.FilestoreS3PathStyleUrl, out option))
		{
			if (option != null && bool.TryParse(option.ToString(), out var pathStyleUrl))
				S3PathStyleUrl = pathStyleUrl;
		}

		if (config.TryGetValue(StorageConfigurationConstants.Language, out option))
			Language = option!.ToString()!;

		if (config.TryGetValue(StorageConfigurationConstants.ElasticConnectionString, out option))
			ElasticConnectionString = option!.ToString()!;

		if (config.TryGetValue(StorageConfigurationConstants.MongoDbConnectionString, out option))
			MongoDbConnectionString = (option != null) ? option.ToString()! : "";
	}
}