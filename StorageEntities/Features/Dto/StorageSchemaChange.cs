using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using System.Security.Cryptography;
using System.Text;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Newtonsoft.Json;
using SqlKata;

namespace Levelbuild.Domain.StorageEntities.Features.Dto;

public class StorageSchemaChange
{
	[Key]
	[ExcludeFromCodeCoverage]
	[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
	public long Id { get; set; }

	public string Hash { get; set; }

	public SchemaChangeOperation Operation { get; set; }

	public String DataSourceName { get; set; }

	public string? SourceObject { get; set; }

	public string? DestinationObject { get; set; }

	public DateTime DateTime { get; set; }

	public string User { get; set; }

	#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	protected StorageSchemaChange()
		#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	{
	}

	public StorageSchemaChange(SchemaChangeOperation schemaChangeOperation, string dataSourceName, object sourceObject, object? destinationObject,
							   string user)
	{
		Operation = schemaChangeOperation;
		DataSourceName = dataSourceName;

		if ((schemaChangeOperation == SchemaChangeOperation.RenameField) || (schemaChangeOperation == SchemaChangeOperation.RemoveField))
		{
			SourceObject = (string)sourceObject;
			DestinationObject = (string?)destinationObject;
		}
		else
		{
			SourceObject = JsonConvert.SerializeObject(sourceObject);
			DestinationObject = JsonConvert.SerializeObject(destinationObject);
		}

		DateTime = DateTime.UtcNow;
		User = user;
		Hash = Md5(Operation.ToString() + DataSourceName + SourceObject + DestinationObject + User + DateTime);
	}

	/// <summary>
	/// Clone <paramref name="other"/>
	/// </summary>
	/// <param name="other"></param>
	public StorageSchemaChange(StorageSchemaChange other)
	{
		Hash = other.Hash;
		Operation = other.Operation;
		DataSourceName = other.DataSourceName;
		SourceObject = other.SourceObject;
		DestinationObject = other.DestinationObject;
		User = other.User;
		DateTime = DateTime.UtcNow;
	}

	public static string Md5(string input, bool isLowercase = false)
	{
		using (var md5 = MD5.Create())
		{
			var byteHash = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
			var hash = BitConverter.ToString(byteHash).Replace("-", "");
			return (isLowercase) ? hash.ToLower() : hash;
		}
	}
}