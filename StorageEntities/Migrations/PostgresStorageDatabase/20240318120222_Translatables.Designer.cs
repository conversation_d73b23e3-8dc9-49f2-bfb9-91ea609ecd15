// <auto-generated />
using System;
using Levelbuild.Domain.StorageEntities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
    [DbContext(typeof(PostgresStorageDatabaseContext))]
    [Migration("20240318120222_Translatables")]
    partial class Translatables
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "uuid-ossp");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("StoreFileContent")
                        .HasColumnType("boolean");

                    b.Property<bool>("StoreRevisions")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("StorageIndexDefinition");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageLink", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text");

                    b.Property<string>("ElementId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LinkedElementId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("LinkedIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.Property<long?>("LinkedRevisionNumber")
                        .HasColumnType("bigint");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("StorageLink");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StoragePath", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("StorageIndexDefinitionId", "Path");

                    b.ToTable("StoragePath");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageTempFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ElementId")
                        .HasColumnType("text");

                    b.Property<DateTime>("FileDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileId")
                        .HasColumnType("bigint");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("RevisionNumber")
                        .HasColumnType("bigint");

                    b.Property<long?>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("StorageIndexDefinitionId");

                    b.ToTable("StorageTempFile");
                });

            modelBuilder.Entity("StorageEntities.Features.Dto.StorageFieldDefinitionOrm", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("DecimalPlaces")
                        .HasColumnType("integer");

                    b.Property<string>("DefaultValueJson")
                        .HasColumnType("text");

                    b.Property<bool>("Encrypted")
                        .HasColumnType("boolean");

                    b.Property<int>("Length")
                        .HasColumnType("integer");

                    b.Property<bool>("LookupCheck")
                        .HasColumnType("boolean");

                    b.Property<string>("LookupSource")
                        .HasColumnType("text");

                    b.Property<string>("LookupSourceField")
                        .HasColumnType("text");

                    b.Property<bool>("MultiValue")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Nullable")
                        .HasColumnType("boolean");

                    b.Property<bool>("PrimaryKey")
                        .HasColumnType("boolean");

                    b.Property<bool>("Readonly")
                        .HasColumnType("boolean");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.Property<bool>("SystemField")
                        .HasColumnType("boolean");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<bool>("Unique")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("StorageIndexDefinitionId", "Name");

                    b.ToTable("StorageFieldDefinition");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StoragePath", b =>
                {
                    b.HasOne("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", "StorageIndexDefinition")
                        .WithMany()
                        .HasForeignKey("StorageIndexDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StorageIndexDefinition");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageTempFile", b =>
                {
                    b.HasOne("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", "StorageIndexDefinition")
                        .WithMany()
                        .HasForeignKey("StorageIndexDefinitionId");

                    b.Navigation("StorageIndexDefinition");
                });

            modelBuilder.Entity("StorageEntities.Features.Dto.StorageFieldDefinitionOrm", b =>
                {
                    b.HasOne("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", null)
                        .WithMany("Fields")
                        .HasForeignKey("StorageIndexDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", b =>
                {
                    b.Navigation("Fields");
                });
#pragma warning restore 612, 618
        }
    }
}
