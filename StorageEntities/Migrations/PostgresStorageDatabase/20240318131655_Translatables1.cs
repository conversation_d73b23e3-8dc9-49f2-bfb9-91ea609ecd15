using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class Translatables1 : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				name: "DefaultLanguage",
				table: "StorageFieldDefinition",
				type: "text",
				nullable: false,
				defaultValue: "");

			migrationBuilder.AddColumn<string[]>(
				name: "Languages",
				table: "StorageFieldDefinition",
				type: "text[]",
				nullable: false,
				defaultValue: new string[0]);

			migrationBuilder.AddColumn<bool>(
				name: "Translatable",
				table: "StorageFieldDefinition",
				type: "boolean",
				nullable: false,
				defaultValue: false);
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				name: "DefaultLanguage",
				table: "StorageFieldDefinition");

			migrationBuilder.DropColumn(
				name: "Languages",
				table: "StorageFieldDefinition");

			migrationBuilder.DropColumn(
				name: "Translatable",
				table: "StorageFieldDefinition");
		}
	}
}