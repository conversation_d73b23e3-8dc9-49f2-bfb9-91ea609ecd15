using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class Translatables_Languages_nullable : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string[]>(
				name: "Languages",
				table: "StorageFieldDefinition",
				type: "text[]",
				nullable: true,
				oldClrType: typeof(string[]),
				oldType: "text[]");
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string[]>(
				name: "Languages",
				table: "StorageFieldDefinition",
				type: "text[]",
				nullable: false,
				defaultValue: new string[0],
				oldClrType: typeof(string[]),
				oldType: "text[]",
				oldNullable: true);
		}
	}
}