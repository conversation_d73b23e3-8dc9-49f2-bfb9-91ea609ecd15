// <auto-generated />
using System;
using Levelbuild.Domain.StorageEntities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
    [DbContext(typeof(PostgresStorageDatabaseContext))]
    [Migration("20240326084108_ChangeStorageSchemaChangeError")]
    partial class ChangeStorageSchemaChangeError
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "uuid-ossp");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<bool>("CustomerSpecific")
                        .HasColumnType("boolean");

                    b.Property<string>("ExternalName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("StoreFileContent")
                        .HasColumnType("boolean");

                    b.Property<bool>("StoreRevisions")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("StorageIndexDefinition");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageLink", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text");

                    b.Property<string>("ElementId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LinkedElementId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("LinkedIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.Property<long?>("LinkedRevisionNumber")
                        .HasColumnType("bigint");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("StorageLink");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StoragePath", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("StorageIndexDefinitionId", "Path");

                    b.ToTable("StoragePath");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageTempFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ElementId")
                        .HasColumnType("text");

                    b.Property<DateTime>("FileDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileId")
                        .HasColumnType("bigint");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("RevisionNumber")
                        .HasColumnType("bigint");

                    b.Property<long?>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("StorageIndexDefinitionId");

                    b.ToTable("StorageTempFile");
                });

            modelBuilder.Entity("StorageEntities.Features.Dto.StorageContextOrm", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobConnectionString")
                        .HasColumnType("text");

                    b.Property<string>("BlobContainer")
                        .HasColumnType("text");

                    b.Property<string>("DatabaseConnectionString")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Identifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TemporaryPath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("StorageContext");
                });

            modelBuilder.Entity("StorageEntities.Features.Dto.StorageFieldDefinitionOrm", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<bool>("CustomerSpecific")
                        .HasColumnType("boolean");

                    b.Property<int>("DecimalPlaces")
                        .HasColumnType("integer");

                    b.Property<string>("DefaultValueJson")
                        .HasColumnType("text");

                    b.Property<bool>("Encrypted")
                        .HasColumnType("boolean");

                    b.Property<string>("ExternalName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Length")
                        .HasColumnType("integer");

                    b.Property<bool>("LookupCheck")
                        .HasColumnType("boolean");

                    b.Property<string>("LookupSource")
                        .HasColumnType("text");

                    b.Property<string>("LookupSourceField")
                        .HasColumnType("text");

                    b.Property<bool>("MultiValue")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Nullable")
                        .HasColumnType("boolean");

                    b.Property<bool>("PrimaryKey")
                        .HasColumnType("boolean");

                    b.Property<bool>("Readonly")
                        .HasColumnType("boolean");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.Property<bool>("SystemField")
                        .HasColumnType("boolean");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<bool>("Unique")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("StorageIndexDefinitionId", "Name");

                    b.ToTable("StorageFieldDefinition");
                });

            modelBuilder.Entity("StorageEntities.Features.Dto.StorageSchemaChange", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("DataSourceName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DestinationObject")
                        .HasColumnType("text");

                    b.Property<string>("Hash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Operation")
                        .HasColumnType("integer");

                    b.Property<string>("SourceObject")
                        .HasColumnType("text");

                    b.Property<string>("User")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("StorageSchemaChange");
                });

            modelBuilder.Entity("StorageEntities.Features.Dto.StorageSchemaChangeError", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("StorageContextId")
                        .HasColumnType("bigint");

                    b.Property<string>("StorageSchemaChangeHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("StorageSchemaChangeError");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StoragePath", b =>
                {
                    b.HasOne("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", "StorageIndexDefinition")
                        .WithMany()
                        .HasForeignKey("StorageIndexDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StorageIndexDefinition");
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageTempFile", b =>
                {
                    b.HasOne("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", "StorageIndexDefinition")
                        .WithMany()
                        .HasForeignKey("StorageIndexDefinitionId");

                    b.Navigation("StorageIndexDefinition");
                });

            modelBuilder.Entity("StorageEntities.Features.Dto.StorageFieldDefinitionOrm", b =>
                {
                    b.HasOne("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", null)
                        .WithMany("Fields")
                        .HasForeignKey("StorageIndexDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Domain.Storage.Dto.StorageIndexDefinition", b =>
                {
                    b.Navigation("Fields");
                });
#pragma warning restore 612, 618
        }
    }
}
