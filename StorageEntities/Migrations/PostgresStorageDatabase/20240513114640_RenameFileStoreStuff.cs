using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class RenameFileStoreStuff : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.RenameColumn(
				name: "B<PERSON>bContainer",
				table: "StorageContext",
				newName: "FilestoreUsername");
			
			migrationBuilder.RenameColumn(
				name: "BlobConnectionString",
				table: "StorageContext",
				newName: "FilestorePassword");
			
			migrationBuilder.AddColumn<string>(
				name: "FilestoreConnectionString",
				table: "StorageContext",
				type: "text",
				nullable: true);
			
			migrationBuilder.AddColumn<string>(
				name: "FilestoreContainer",
				table: "StorageContext",
				type: "text",
				nullable: true);
		}
		
		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				name: "FilestoreConnectionString",
				table: "StorageContext");
			
			migrationBuilder.DropColumn(
				name: "FilestoreContainer",
				table: "StorageContext");
			
			migrationBuilder.RenameColumn(
				name: "FilestoreUsername",
				table: "StorageContext",
				newName: "BlobContainer");
			
			migrationBuilder.RenameColumn(
				name: "FilestorePassword",
				table: "StorageContext",
				newName: "BlobConnectionString");
		}
	}
}