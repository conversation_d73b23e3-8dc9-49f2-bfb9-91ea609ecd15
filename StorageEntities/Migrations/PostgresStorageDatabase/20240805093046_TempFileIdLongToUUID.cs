using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class TempFileIdLongToUUID : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				name: "FileId",
				table: "StorageTempFile",
				type: "character varying(40)",
				maxLength: 40,
				nullable: false,
				oldClrType: typeof(long),
				oldType: "bigint");
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<long>(
				name: "FileId",
				table: "StorageTempFile",
				type: "bigint",
				nullable: false,
				oldClrType: typeof(string),
				oldType: "character varying(40)",
				oldMaxLength: 40);
		}
	}
}