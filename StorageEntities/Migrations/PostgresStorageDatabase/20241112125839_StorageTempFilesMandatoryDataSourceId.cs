using Levelbuild.Domain.StorageEntities.Features.Dto;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
    /// <inheritdoc />
    public partial class StorageTempFilesMandatoryDataSourceId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
			// id is not nullable, but existing entries probably have null in StorageIndexDefinitionId column - lets just remove the existing entries
			migrationBuilder.Sql($"""DELETE FROM "{nameof(StorageTempFile)}";""");
			
            migrationBuilder.DropForeignKey(
                name: "FK_StorageTempFile_StorageIndexDefinition_StorageIndexDefiniti~",
                table: "StorageTempFile");

            migrationBuilder.AlterColumn<long>(
                name: "StorageIndexDefinitionId",
                table: "StorageTempFile",
                type: "bigint",
                nullable: false,
                defaultValue: 0L,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_StorageTempFile_StorageIndexDefinition_StorageIndexDefiniti~",
                table: "StorageTempFile",
                column: "StorageIndexDefinitionId",
                principalTable: "StorageIndexDefinition",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StorageTempFile_StorageIndexDefinition_StorageIndexDefiniti~",
                table: "StorageTempFile");

            migrationBuilder.AlterColumn<long>(
                name: "StorageIndexDefinitionId",
                table: "StorageTempFile",
                type: "bigint",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "bigint");

            migrationBuilder.AddForeignKey(
                name: "FK_StorageTempFile_StorageIndexDefinition_StorageIndexDefiniti~",
                table: "StorageTempFile",
                column: "StorageIndexDefinitionId",
                principalTable: "StorageIndexDefinition",
                principalColumn: "Id");
        }
    }
}
