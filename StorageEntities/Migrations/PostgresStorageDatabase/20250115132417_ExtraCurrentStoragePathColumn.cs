using Levelbuild.Domain.StorageEntities.Features.Dto;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
    /// <inheritdoc />
    public partial class ExtraCurrentStoragePathColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "CurrentStoragePathId",
                table: "StorageIndexDefinition",
                type: "bigint",
                nullable: true);
			
			migrationBuilder.Sql($"""
								  UPDATE "{nameof(StorageIndexDefinition)}" sid SET "CurrentStoragePathId" = (SELECT max(sp."Id") FROM "{nameof(StoragePath)}" sp WHERE sp."StorageIndexDefinitionId" = sid."Id");
								  """);

            migrationBuilder.CreateIndex(
                name: "IX_StorageIndexDefinition_CurrentStoragePathId",
                table: "StorageIndexDefinition",
                column: "CurrentStoragePathId");

            migrationBuilder.AddForeignKey(
                name: "FK_StorageIndexDefinition_StoragePath_CurrentStoragePathId",
                table: "StorageIndexDefinition",
                column: "CurrentStoragePathId",
                principalTable: "StoragePath",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StorageIndexDefinition_StoragePath_CurrentStoragePathId",
                table: "StorageIndexDefinition");

            migrationBuilder.DropIndex(
                name: "IX_StorageIndexDefinition_CurrentStoragePathId",
                table: "StorageIndexDefinition");

            migrationBuilder.DropColumn(
                name: "CurrentStoragePathId",
                table: "StorageIndexDefinition");
        }
    }
}
