using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Domain.StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class AddMongoDbPropertyToStorageContext : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				name: "MongoDbConnectionString",
				table: "StorageContext",
				type: "text",
				nullable: true);
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				name: "MongoDbConnectionString",
				table: "StorageContext");
		}
	}
}