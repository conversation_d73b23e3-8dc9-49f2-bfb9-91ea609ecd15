main {
	display: flex;
}

.page-content {
	flex-grow: 1;
	overflow: clip;
	height: 100%;
	display: flex;
	background-color: var(--clr-background-lvl-1);
}

.fixed-panel {
	width: 484px;
	height: 100%;
	overflow-y: auto;
	background-color: var(--clr-background-lvl-0);
	
	display: grid;
	grid-template-rows: max-content auto;
	
	& .panel__content,
	& .panel__header {
		padding: var(--size-spacing-m);
	}

	& .panel__content {
		overflow: auto;
	}
	
	& .panel__header {
		justify-content: start;
		height: 4.8rem;
		color: var(--clr-text-secondary);
		border-bottom: 1px solid var(--clr-state-active);
		font-size: var(--size-text-l);
		gap: var(--size-spacing-m);
		
		& lvl-button {
			font-size: var(--size-text-l);
		}
	}
}