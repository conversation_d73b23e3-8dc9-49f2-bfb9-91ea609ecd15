lvl-fab {
	position: fixed;
	bottom: 3rem;
	right: 3rem;
	z-index: 1;
}

.list-view {
	position: relative;
	flex-grow: 1;
	display: flex;
	justify-content: center;
	height: 100%;
	background-color: var(--clr-background-lvl-1);
	padding: var(--size-spacing-m) 0 var(--size-spacing-l);

	& lvl-enumeration {
		width: clamp(300px, 80%, 1400px);
		background-color: var(--clr-background-lvl-0);
		border-radius: var(--size-radius-m);
		box-shadow: 0 0 0.2rem 0 var(--clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--clr-shadow-weak);
	}
}

main:has(lvl-side-nav) {
	display: flex;
}