/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[6],{622:function(ya,ua,n){n.r(ua);var na=n(0),ma=n(358);ya=n(614);n(36);n=n(537);var oa=function(ka){function ia(fa,x){var y=ka.call(this,fa,x)||this;y.url=fa;y.range=x;y.status=ma.a.NOT_STARTED;return y}Object(na.c)(ia,ka);ia.prototype.start=function(){var fa=document.createElement("IFRAME");fa.setAttribute("src",this.url);document.documentElement.appendChild(fa);fa.parentNode.removeChild(fa);this.status=ma.a.STARTED;this.fM()};
return ia}(ya.ByteRangeRequest);ya=function(ka){function ia(fa,x,y,r){fa=ka.call(this,fa,x,y,r)||this;fa.hG=oa;return fa}Object(na.c)(ia,ka);ia.prototype.jD=function(fa,x){return"".concat(fa,"#").concat(x.start,"&").concat(x.stop?x.stop:"")};return ia}(ya["default"]);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
