.pika-lendar {
  float: left;
  width: 220px;
  padding: 8px;
  margin: 0;
}

.pika-table abbr {
  text-decoration: none!important;
}

.pika-single.is-bound {
  border-radius: 4px;
  box-shadow: 0px 5px 10px -5px #868e96;
}
.pika-title {
  color: #485056;
  font-family: Lato;
  font-style: normal;
  font-weight: bold;
  font-size: 13px;
  line-height: 16px;
}

.is-rtl .pika-next,
.pika-prev {
  float: left;
  opacity: 1;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23868E96;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Eicon - chevron - left%3C/title%3E%3Cpath class='cls-1' d='M7.5,2l-4,4,4,4,1-1-3-3,3-3Z'/%3E%3C/svg%3E");
}

.is-rtl .pika-prev,
.pika-next {
  float: right;
  opacity: 1;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23868E96;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Eicon - chevron - right%3C/title%3E%3Cpath class='cls-1' d='M4.5,10l4-4-4-4-1,1,3,3-3,3Z'/%3E%3C/svg%3E");
}
.pika-next:hover, .pika-prev:hover {
  opacity: 0.5;
}


.pika-row .pika-button {
  margin: 0 auto;
  background: none;
  padding: 4px;
  text-align: center;
  font-size: 13px;
  line-height: 16px;
  color: #868E96;
  font-family: Lato;
  font-style: normal;
  font-weight: normal;
  width: 28px;
  height: 23px;
}

.is-today .pika-button {
  background: #E7EDF3;
  color: #868E96;
  border-radius: 4px;
  font-weight: 700;
}

.has-event .pika-button,
.is-selected .pika-button {
  color: #fff;
  background: #3183C8;
  border-radius: 4px;
  box-shadow: none;
}

.pika-table th {
  color: #999;
  font-size: 12px;
  line-height: 25px;
  font-weight: 700;
  text-align: center;
}

.pika-label {
  font-family: Lato;
  font-style: normal;
  font-weight: bold;
  font-size: 13px;
  line-height: 16px;
  text-align: center;
  color: #485056;
}

.pika-table td,
.pika-table th {
  width: 10%;
  padding: 0;
}

.pika-set-today {
  padding: 5px 15px;
  width: auto;
}

button.pika-set-today:disabled,
button.pika-set-today[disabled]{
  opacity: 0.5;
}