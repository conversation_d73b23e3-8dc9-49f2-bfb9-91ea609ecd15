!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/core/legacyOffice/",n(n.s=1)}([function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){e.exports=n(7)},function(e,t,n){"use strict";(function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e){e.createPromiseCapability=function(){var e={};return e.promise=new A((function(t,n){e.resolve=t,e.reject=n})),e};var o=e.Promise,i=o&&"resolve"in o&&"reject"in o&&"all"in o&&"race"in o&&function(){var e;return new o((function(t){e=t})),"function"==typeof e}();"undefined"!=typeof exports&&exports?(exports.Promise=i?o:A,exports.Polyfill=A):"function"==typeof define&&n(6)?define((function(){return i?o:A})):i||(e.Promise=A);var a=function(){};function c(e){return"[object Array]"===Object.prototype.toString.call(e)}var s,u=void 0!==t?t:setTimeout,f=[];function l(){for(var e=0;e<f.length;e++)f[e][0](f[e][1]);f=[],s=!1}function d(e,t){f.push([e,t]),s||(s=!0,u(l,0))}function m(e){var t=e.owner,n=t.state_,r=t.data_,o=e[n],i=e.then;if("function"==typeof o){n="fulfilled";try{r=o(r)}catch(e){v(i,e)}}h(i,r)||("fulfilled"===n&&p(i,r),"rejected"===n&&v(i,r))}function h(e,t){var n;try{if(e===t)throw new TypeError("A promises callback cannot return that same promise.");if(t&&("function"==typeof t||"object"===r(t))){var o=t.then;if("function"==typeof o)return o.call(t,(function(r){n||(n=!0,t!==r?p(e,r):y(e,r))}),(function(t){n||(n=!0,v(e,t))})),!0}}catch(t){return n||v(e,t),!0}return!1}function p(e,t){e!==t&&h(e,t)||y(e,t)}function y(e,t){"pending"===e.state_&&(e.state_="sealed",e.data_=t,d(w,e))}function v(e,t){"pending"===e.state_&&(e.state_="sealed",e.data_=t,d(b,e))}function g(e){var t=e.then_;e.then_=void 0;for(var n=0;n<t.length;n++)m(t[n])}function w(e){e.state_="fulfilled",g(e)}function b(e){e.state_="rejected",g(e)}function A(e){if("function"!=typeof e)throw new TypeError("Promise constructor takes a function argument");if(!(this instanceof A))throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this.then_=[],function(e,t){function n(e){v(t,e)}try{e((function(e){p(t,e)}),n)}catch(e){n(e)}}(e,this)}A.prototype={constructor:A,state_:"pending",then_:null,data_:void 0,then:function(e,t){var n={owner:this,then:new this.constructor(a),fulfilled:e,rejected:t};return"fulfilled"===this.state_||"rejected"===this.state_?d(m,n):this.then_.push(n),n.then},catch:function(e){return this.then(null,e)}},A.all=function(e){if(!c(e))throw new TypeError("You must pass an array to Promise.all().");return new this((function(t,n){var r=[],o=0;function i(e){return o++,function(n){r[e]=n,--o||t(r)}}for(var a,c=0;c<e.length;c++)(a=e[c])&&"function"==typeof a.then?a.then(i(c),n):r[c]=a;o||t(r)}))},A.race=function(e){if(!c(e))throw new TypeError("You must pass an array to Promise.race().");return new this((function(t,n){for(var r,o=0;o<e.length;o++)(r=e[o])&&"function"==typeof r.then?r.then(t,n):t(r)}))},A.resolve=function(e){return e&&"object"===r(e)&&e.constructor===this?e:new this((function(t){t(e)}))},A.reject=function(e){return new this((function(t,n){n(e)}))}}("undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:void 0)}).call(this,n(0),n(3).setImmediate)},function(e,t,n){(function(e){var r=void 0!==e&&e||"undefined"!=typeof self&&self||window,o=Function.prototype.apply;function i(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new i(o.call(setTimeout,r,arguments),clearTimeout)},t.setInterval=function(){return new i(o.call(setInterval,r,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},i.prototype.unref=i.prototype.ref=function(){},i.prototype.close=function(){this._clearFn.call(r,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},n(4),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(this,n(0))},function(e,t,n){(function(e,t){!function(e,n){"use strict";if(!e.setImmediate){var r,o,i,a,c,s=1,u={},f=!1,l=e.document,d=Object.getPrototypeOf&&Object.getPrototypeOf(e);d=d&&d.setTimeout?d:e,"[object process]"==={}.toString.call(e.process)?r=function(e){t.nextTick((function(){h(e)}))}:!function(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}()?e.MessageChannel?((i=new MessageChannel).port1.onmessage=function(e){h(e.data)},r=function(e){i.port2.postMessage(e)}):l&&"onreadystatechange"in l.createElement("script")?(o=l.documentElement,r=function(e){var t=l.createElement("script");t.onreadystatechange=function(){h(e),t.onreadystatechange=null,o.removeChild(t),t=null},o.appendChild(t)}):r=function(e){setTimeout(h,0,e)}:(a="setImmediate$"+Math.random()+"$",c=function(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(a)&&h(+t.data.slice(a.length))},e.addEventListener?e.addEventListener("message",c,!1):e.attachEvent("onmessage",c),r=function(t){e.postMessage(a+t,"*")}),d.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var o={callback:e,args:t};return u[s]=o,r(s),s++},d.clearImmediate=m}function m(e){delete u[e]}function h(e){if(f)setTimeout(h,0,e);else{var t=u[e];if(t){f=!0;try{!function(e){var t=e.callback,n=e.args;switch(n.length){case 0:t();break;case 1:t(n[0]);break;case 2:t(n[0],n[1]);break;case 3:t(n[0],n[1],n[2]);break;default:t.apply(void 0,n)}}(t)}finally{m(e),f=!1}}}}}("undefined"==typeof self?void 0===e?this:e:self)}).call(this,n(0),n(5))},function(e,t){var n,r,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(e){if(n===setTimeout)return setTimeout(e,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(e){n=i}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}}();var s,u=[],f=!1,l=-1;function d(){f&&s&&(f=!1,s.length?u=s.concat(u):l=-1,u.length&&m())}function m(){if(!f){var e=c(d);f=!0;for(var t=u.length;t;){for(s=u,u=[];++l<t;)s&&s[l].run();l=-1,t=u.length}s=null,f=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function p(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new h(e,t)),1!==u.length||f||c(m)},h.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=p,o.addListener=p,o.once=p,o.off=p,o.removeListener=p,o.removeAllListeners=p,o.emit=p,o.prependListener=p,o.prependOnceListener=p,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(e,t){(function(t){e.exports=t}).call(this,{})},function(e,t,n){"use strict";n.r(t);n(2),function(e){void 0===e.crypto&&(e.crypto={getRandomValues:function(e){for(var t=0;t<e.length;t++)e[t]=256*Math.random()}})}("undefined"==typeof window?self:window),console.log,console.warn,console.error;function r(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))}function o(e,t){var n,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,r=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=t.call(e,i)}catch(e){c=[6,e],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;var i={flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},a=function(e){return i[e]},c=function(e,t){a("disableLogs")||(t?console.warn("".concat(e,": ").concat(t)):console.warn(e))};function s(e,t,n){return new Promise((function(r){if(!e)return r();var o=n.document.createElement("script");o.type="text/javascript",o.onload=function(){r()},o.onerror=function(){t&&c(t),r()},o.src=e,n.document.getElementsByTagName("head")[0].appendChild(o)}))}var u,f=function(e){if("string"==typeof e){for(var t=new Uint8Array(e.length),n=e.length,r=0;r<n;r++)t[r]=e.charCodeAt(r);return t}return e},l=function(e){if("string"!=typeof e){for(var t="",n=0,r=e.length,o=void 0;n<r;)o=e.subarray(n,n+1024),n+=1024,t+=String.fromCharCode.apply(null,o);return t}return e},d="undefined"==typeof window?self:window,m=d.importScripts,h=!1,p=function(e,t){h||(m("".concat(d.basePath,"decode.min.js")),h=!0);var n=f(e),r=self.BrotliDecode(n);return t?r:l(r)},y=function(e,t){return r(void 0,void 0,Promise,(function(){var n;return o(this,(function(r){switch(r.label){case 0:return h?[3,2]:[4,s("".concat(self.Core.getWorkerPath(),"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:r.sent(),h=!0,r.label=2;case 2:return n=self.BrotliDecode(f(e)),[2,t?n:l(n)]}}))}))},v=(function(){function e(){this.remainingDataArrays=[]}e.prototype.processRaw=function(e){return e},e.prototype.processBrotli=function(e){return this.remainingDataArrays.push(e),null},e.prototype.GetNextChunk=function(e){return this.decodeFunction||(0===e[0]&&97===e[1]&&115===e[2]&&109===e[3]?this.decodeFunction=this.processRaw:this.decodeFunction=this.processBrotli),this.decodeFunction(e)},e.prototype.End=function(){if(this.remainingDataArrays.length){for(var e=this.arrays,t=0,n=0;n<e.length;++n)t+=e[n].length;var r=new Uint8Array(t),o=0;for(n=0;n<e.length;++n){var i=e[n];r.set(i,o),o+=i.length}return p(r,!0)}return null}}(),function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n=!1);var r=new XMLHttpRequest;r.open("GET",e,t);var o=n&&r.overrideMimeType;return r.responseType=o?"text":"arraybuffer",o&&r.overrideMimeType("text/plain; charset=x-user-defined"),r}),g=function(e,t){var n=t.decompressFunction,r=t.shouldOutputArray,o=t.compressedMaximum,i=void 0!==m?Date.now():null;try{var a=r?A(e):P(e);return"Result length is ".concat(a.length),a.length<o?a=w(a,n,r):r||(a=l(a)),m&&b(t.paths,i),a}catch(e){throw new Error("Failed to decompress: ".concat(e))}},w=function(e,t,n){var r=t(e,n);return c("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this."),m&&"Decompressed length is ".concat(r.length),r},b=function(e,t){var n=e.join(", ");"".concat(n," Decompression took ").concat(Date.now()-t," ms")},A=function(e){var t=e.reduce((function(e,t){var n=new Uint8Array(t);return e.concat(Array.from(n))}),[]);return new Uint8Array(t)},P=function(e){return e.join("")},T=function(e){var t,n=!e.shouldOutputArray,r=e.paths,o=e.isAsync;if(o)t=Promise.all(r.map((function(e){return function(e,t,n){return new Promise((function(r,o){var i=v(e,t,n);i.send(),i.onload=function(){200===this.status||0===this.status?r(i.response):o(new Error("Download Failed ".concat(e)))},i.onerror=function(){o(new Error("Network error occurred ".concat(e)))}}))}(e,o,n)}))).then((function(t){return g(t,e)})).catch((function(e){throw new Error("Failed to fetch or decompress files: ".concat(e.message))}));else{var i=r.map((function(e){var t=v(e,o,n);if(t.send(),200===t.status||0===t.status)return t.response;throw new Error("Failed to load ".concat(e))}));t=g(i,e)}return t},j=function(e){var t=e.lastIndexOf("/");-1===t&&(t=0);var n=e.slice(t).replace(".",".br.");return m||(n.endsWith(".js.mem")?n=n.replace(".js.mem",".mem"):n.endsWith(".js")&&(n=n.concat(".mem"))),e.slice(0,t)+n},_=function(e,t){return t.decompressFunction=m?p:y,t.paths=e.map((function(e){return j(e)})),T(t)},x=function(e,t,n,r){return e.catch((function(e){return c(e),r(t,n)}))},M=function(e,t,n,r){return function(e,t,n){if(n.isAsync){for(var r=t[0](e,n),o=1;o<t.length;++o)r=x(r,e,n,t[o]);return r}for(var i=0,a=t;i<a.length;i++){var s=a[i];try{return s(e,n)}catch(e){c(e.message)}}throw new Error("None of the worker files were able to load. ")}(Array.isArray(e)?e:[e],[_],{compressedMaximum:t,isAsync:n,shouldOutputArray:r})};function S(e,t,n,r){return function e(t,n,r,o,i,a,c){if(a=a||Date.now(),i&&!o)return fetch(j(t)).then((function(e){return WebAssembly.instantiateStreaming(e,n)})).catch((function(i){return"instantiateStreaming Failed ".concat(t," message ").concat(i.message),e(t,n,r,o,!1,a,c)}));var s=o?o.map((function(e,t){return"".concat(e,"PDFNetCWasm-chunk-").concat(t,".wasm")})):t;return M(s,r,!0,!0).then((function(e){return c=Date.now(),"Request took ".concat(c-a," ms"),WebAssembly.instantiate(e,n)}))}(e,t,n,r,!!WebAssembly.instantiateStreaming,void 0,void 0).then((function(e){return"WASM compilation took ".concat(Date.now()-void 0," ms"),e}))}var O,E,I,k,L,R="undefined"==typeof window?self:window,F=(O=navigator.userAgent.toLowerCase(),(E=/(msie) ([\w.]+)/.exec(O)||/(trident)(?:.*? rv:([\w.]+)|)/.exec(O))?parseInt(E[2],10):E),W=(I=R.navigator.userAgent.match(/OPR/),k=R.navigator.userAgent.match(/Maxthon/),L=R.navigator.userAgent.match(/Edge/),R.navigator.userAgent.match(/Chrome\/(.*?) /)&&!I&&!k&&!L),U=(function(){if(!W)return null;var e=R.navigator.userAgent.match(/Chrome\/([0-9]+)\./);e&&parseInt(e[1],10)}(),!!navigator.userAgent.match(/Edge/i)||navigator.userAgent.match(/Edg\/(.*?)/)&&R.navigator.userAgent.match(/Chrome\/(.*?) /)),B=(function(){if(!U)return null;var e=R.navigator.userAgent.match(/Edg\/([0-9]+)\./);e&&parseInt(e[1],10)}(),/iPad|iPhone|iPod/.test(R.navigator.platform)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1||/iPad|iPhone|iPod/.test(R.navigator.userAgent)),C=function(){var e=R.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return e?parseFloat(e[1]):e}(),D=/^((?!chrome|android).)*safari/i.test(R.navigator.userAgent)||/^((?!chrome|android).)*$/.test(R.navigator.userAgent)&&B,N=(D&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&parseInt(null===(u=navigator.userAgent.match(/Version\/(\d+)/))||void 0===u?void 0:u[1],10),R.navigator.userAgent.match(/Firefox/)),z=(function(){if(!N)return null;var e=R.navigator.userAgent.match(/Firefox\/([0-9]+)\./);e&&parseInt(e[1],10)}(),F||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent),navigator.userAgent.match(/(iPad|iPhone|iPod)/i),R.navigator.userAgent.indexOf("Android"),/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(R.navigator.userAgent)),G=!!R.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)&&parseInt(R.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],10)>=14,H=!(!self.WebAssembly||!self.WebAssembly.validate),q=R.navigator.userAgent.indexOf("Edge/16")>-1||R.navigator.userAgent.indexOf("MSAppHost")>-1,$=function(){return H&&!q&&!(!G&&(D&&C<14||z))};var V=function(){function e(e){var t=this;this.promise=e.then((function(e){t.response=e,t.status=200}))}return e.prototype.addEventListener=function(e,t){this.promise.then(t)},e}();function X(e){return(X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Y=null;!function(e){var t,n,r,o,i=[];function a(e){i||(i=[]),i.push(e)}var c=function(){e.basePath="../";var a,c,s=e.legacyOfficeWorkerPath||"";function u(e){var t=function(e){var t=[];return{resource_array:t,msg:JSON.stringify(e.data,(function(e,n){if("object"===X(n)){var i=null;if(n instanceof Uint8Array?i=n:n instanceof ArrayBuffer&&(i=new Uint8Array(n)),i){var a=r(i.length),c=o(a);if(c)new Uint8Array(Module.HEAPU8.buffer,c,i.length).set(i);return t.push(a),{__trn_res_id:a}}}return n}))}}(e);n(t.msg)}e.workerBasePath&&(e.basePath=e.workerBasePath),e.externalPath?e.basePath=e.externalPath:e.basePath+="external/",importScripts("".concat(e.basePath,"Promise.js")),e.ContinueFunc=function(e){t("ContinueFunc called"),setTimeout((function(){onmessage({data:{action:"continue"}})}),e)},e.pdfWorkerPath&&(a=e.pdfWorkerPath),e.officeAsmPath&&(c=e.officeAsmPath),e.Module={memoryInitializerPrefixURL:a,asmjsPrefix:c,onRuntimeInitialized:function(){t||(t=function(){});var e=Date.now()-Y;"time duration from start to ready: ".concat(JSON.stringify(e)),n=function(e){if(null!=e&&0!==e){var t=1+(e.length<<2),n=Module._malloc(t);stringToUTF8(e,n,t)>0&&Module._TRN_OnMessage(n)}},r=function(e){return Module._TRN_CreateBufferResource(e)},o=function(e){return Module._TRN_GetResourcePointer(e)},t("OnReady called"),onmessage=u,Module._TRN_InitWorker();for(var a=0;a<i.length;++a)onmessage(i[a]);i=null},fetchSelf:function(){Y=Date.now(),function(e,t,n,r){var o,i;if($()&&!n){if(self.Module.instantiateWasm=function(n,o){return S("".concat(e,"Wasm.wasm"),n,t["Wasm.wasm"],r).then((function(e){o(e.instance)}))},t.disableObjectURLBlobs)return void importScripts("".concat(e,"Wasm.js"));o=M("".concat(e,"Wasm.js.mem"),t["Wasm.js.mem"],!1,!1),i=new Blob([o],{type:"application/javascript"}),importScripts(URL.createObjectURL(i))}else{if(t.disableObjectURLBlobs)return void importScripts("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+e,".js"));o=M("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+e,".js.mem"),t[".js.mem"],!1);var a=M("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:"")+e,".mem"),t[".mem"],!0,!0);self.Module.memoryInitializerRequest=new V(a),i=new Blob([o],{type:"application/javascript"}),importScripts(URL.createObjectURL(i))}}("".concat(s,"WebB2XOfficeWorker"),{"Wasm.wasm":5e6,"Wasm.js.mem":1e5,".js.mem":5e6,".mem":3e6,disableObjectURLBlobs:e.disableObjectURLBlobs},!!navigator.userAgent.match(/Edge/i)||e.wasmDisabled)},noExitRuntime:!0}};e.onmessage=function(t){"init"===t.data.action&&(e.wasmDisabled=!t.data.wasm,e.externalPath=t.data.externalPath,e.officeAsmPath=t.data.officeAsmPath,e.pdfWorkerPath=t.data.pdfWorkerPath,e.disableObjectURLBlobs=t.data.disableObjectURLBlobs,e.onmessage=a,c(),e.Module.fetchSelf())}}("undefined"==typeof window?self:window);ArrayBuffer.prototype.slice||(ArrayBuffer.prototype.slice=function(e,t){if(void 0===e&&(e=0),void 0===t&&(t=this.byteLength),e=Math.floor(e),t=Math.floor(t),e<0&&(e+=this.byteLength),t<0&&(t+=this.byteLength),e=Math.min(Math.max(0,e),this.byteLength),(t=Math.min(Math.max(0,t),this.byteLength))-e<=0)return new ArrayBuffer(0);var n=new ArrayBuffer(t-e),r=new Uint8Array(n),o=new Uint8Array(this,e,t-e);return r.set(o),n})}]);