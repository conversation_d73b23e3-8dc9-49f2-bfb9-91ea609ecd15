{"version": 3, "sources": ["webpack:///./src/ui/node_modules/@pdftron/webviewer-react-toolkit/dist/esm/components/Input/Input.js"], "names": ["Input", "_a", "ref", "_b", "message", "messageText", "<PERSON><PERSON><PERSON><PERSON>", "wrapperClassName", "padMessageText", "className", "onFocus", "onBlur", "rightElement", "leftElement", "_c", "type", "props", "_d", "focused", "handleOnFocus", "handleOnBlur", "rightIcon", "icon", "undefined", "createElement", "wrapperClass", "mainClass", "inputClass", "disabled"], "mappings": "2FAAA,mGAKWA,EAAQ,sBAAW,SAAUC,EAAIC,GACxC,IAAIC,EAAKF,EAAGG,QAASA,OAAiB,IAAPD,EAAgB,UAAYA,EAAIE,EAAcJ,EAAGI,YAAaC,EAAYL,EAAGK,UAAWC,EAAmBN,EAAGM,iBAAkBC,EAAiBP,EAAGO,eAAgBC,EAAYR,EAAGQ,UAAWC,EAAUT,EAAGS,QAASC,EAASV,EAAGU,OAAQC,EAAeX,EAAGW,aAAcC,EAAcZ,EAAGY,YAAaC,EAAKb,EAAGc,KAAMA,OAAc,IAAPD,EAAgB,OAASA,EAAIE,EAAQ,YAAOf,EAAI,CAAC,UAAW,cAAe,YAAa,mBAAoB,iBAAkB,YAAa,UAAW,SAAU,eAAgB,cAAe,SACrhBgB,EAAK,YAASP,EAASC,GAASO,EAAUD,EAAGC,QAASC,EAAgBF,EAAGE,cAAeC,EAAeH,EAAGG,aAC1GC,EAAY,mBAAQ,WACpB,GAAIT,EACA,OAAOA,EACX,IAAIU,OAAOC,EACX,OAAQnB,GACJ,IAAK,UACDkB,EAAO,UACP,MACJ,IAAK,QACDA,EAAO,QAGf,OAAOA,EAAO,IAAME,cAAc,IAAM,CAAEf,UAAW,kBAAmBa,KAAMA,SAAUC,IACzF,CAACnB,EAASQ,IACTa,EAAe,IAAW,8BAA+B,CACzD,2BAA4BnB,EAC5B,0BAA2BE,IAAmBH,GAC/CE,GACCmB,EAAY,IAAW,YAAa,sBAAwBtB,EAAS,CAAE,qBAAsBc,IAC7FS,EAAa,IAAW,mBAAoB,CAAE,6BAA8BX,EAAMY,UAAYnB,GAClG,OAAQ,IAAMe,cAAc,MAAO,CAAEf,UAAWgB,GAC5C,IAAMD,cAAc,MAAO,CAAEf,UAAWiB,GACpCb,EACA,IAAMW,cAAc,QAAS,YAAS,GAAIR,EAAO,CAAED,KAAMA,EAAML,QAASS,EAAeR,OAAQS,EAAcX,UAAWkB,EAAYzB,IAAKA,KACzImB,GACJhB,EAAc,IAAMmB,cAAc,MAAO,CAAEf,UAAW,0BAA4BJ,QAAekB", "file": "chunks/chunk.0.js", "sourcesContent": ["import { __assign, __rest } from \"tslib\";\nimport classnames from 'classnames';\nimport React, { forwardRef, useMemo } from 'react';\nimport { useFocus } from '../../hooks';\nimport { Icon } from '../Icon';\nexport var Input = forwardRef(function (_a, ref) {\n    var _b = _a.message, message = _b === void 0 ? 'default' : _b, messageText = _a.messageText, fillWidth = _a.fillWidth, wrapperClassName = _a.wrapperClassName, padMessageText = _a.padMessageText, className = _a.className, onFocus = _a.onFocus, onBlur = _a.onBlur, rightElement = _a.rightElement, leftElement = _a.leftElement, _c = _a.type, type = _c === void 0 ? 'text' : _c, props = __rest(_a, [\"message\", \"messageText\", \"fillWidth\", \"wrapperClassName\", \"padMessageText\", \"className\", \"onFocus\", \"onBlur\", \"rightElement\", \"leftElement\", \"type\"]);\n    var _d = useFocus(onFocus, onBlur), focused = _d.focused, handleOnFocus = _d.handleOnFocus, handleOnBlur = _d.handleOnBlur;\n    var rightIcon = useMemo(function () {\n        if (rightElement)\n            return rightElement;\n        var icon = undefined;\n        switch (message) {\n            case 'warning':\n                icon = 'Warning';\n                break;\n            case 'error':\n                icon = 'Error';\n                break;\n        }\n        return icon ? React.createElement(Icon, { className: \"ui__input__icon\", icon: icon }) : undefined;\n    }, [message, rightElement]);\n    var wrapperClass = classnames('ui__base ui__input__wrapper', {\n        'ui__input__wrapper--fill': fillWidth,\n        'ui__input__wrapper--pad': padMessageText && !messageText,\n    }, wrapperClassName);\n    var mainClass = classnames('ui__input', \"ui__input--message-\" + message, { 'ui__input--focused': focused });\n    var inputClass = classnames('ui__input__input', { 'ui__input__input--disabled': props.disabled }, className);\n    return (React.createElement(\"div\", { className: wrapperClass },\n        React.createElement(\"div\", { className: mainClass },\n            leftElement,\n            React.createElement(\"input\", __assign({}, props, { type: type, onFocus: handleOnFocus, onBlur: handleOnBlur, className: inputClass, ref: ref })),\n            rightIcon),\n        messageText ? React.createElement(\"div\", { className: \"ui__input__messageText\" }, messageText) : undefined));\n});\n"], "sourceRoot": ""}