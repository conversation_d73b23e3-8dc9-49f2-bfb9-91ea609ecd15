{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ar-iq.js"], "names": ["module", "exports", "e", "t", "default", "_", "d", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiem", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,sDAAsDC,MAAM,KAAKC,OAAO,+FAA+FD,MAAM,KAAKE,UAAU,EAAEC,cAAc,wCAAwCH,MAAM,KAAKI,YAAY,+FAA+FJ,MAAM,KAAKK,YAAY,gBAAgBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,SAAS,SAASrB,GAAG,OAAOA,EAAE,GAAG,IAAI,KAAKsB,aAAa,CAACC,OAAO,QAAQC,KAAK,SAASC,EAAE,OAAOC,EAAE,QAAQC,GAAG,WAAWC,EAAE,OAAOC,GAAG,WAAWzB,EAAE,MAAM0B,GAAG,UAAUC,EAAE,MAAMC,GAAG,UAAUC,EAAE,MAAMC,GAAG,aAAa,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAAplCD,CAAE,EAAQ", "file": "chunks/chunk.105.js", "sourcesContent": ["!function(e,_){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=_(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],_):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_ar_iq=_(e.dayjs)}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),d={name:\"ar-iq\",weekdays:\"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت\".split(\"_\"),months:\"كانون الثاني_شباط_آذار_نيسان_أيار_حزيران_تموز_آب_أيلول_تشرين الأول_ تشرين الثاني_كانون الأول\".split(\"_\"),weekStart:1,weekdaysShort:\"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت\".split(\"_\"),monthsShort:\"كانون الثاني_شباط_آذار_نيسان_أيار_حزيران_تموز_آب_أيلول_تشرين الأول_ تشرين الثاني_كانون الأول\".split(\"_\"),weekdaysMin:\"ح_ن_ث_ر_خ_ج_س\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},meridiem:function(e){return e>12?\"ص\":\"م\"},relativeTime:{future:\"في %s\",past:\"منذ %s\",s:\"ثوان\",m:\"دقيقة\",mm:\"%d دقائق\",h:\"ساعة\",hh:\"%d ساعات\",d:\"يوم\",dd:\"%d أيام\",M:\"شهر\",MM:\"%d أشهر\",y:\"سنة\",yy:\"%d سنوات\"}};return t.default.locale(d,null,!0),d}));"], "sourceRoot": ""}