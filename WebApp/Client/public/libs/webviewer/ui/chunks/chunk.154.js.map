{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/fr-ch.js"], "names": ["module", "exports", "e", "i", "default", "n", "_", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,sDAAsDC,MAAM,KAAKC,OAAO,uFAAuFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,qCAAqCH,MAAM,KAAKI,YAAY,iEAAiEJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,UAAUC,KAAK,YAAYC,EAAE,oBAAoBC,EAAE,aAAaC,GAAG,aAAaC,EAAE,YAAYC,GAAG,YAAYC,EAAE,UAAUC,GAAG,WAAWC,EAAE,UAAUC,GAAG,UAAUC,EAAE,QAAQC,GAAG,WAAW,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAAhjCD,CAAE,EAAQ", "file": "chunks/chunk.154.js", "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_fr_ch=n(e.dayjs)}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var i=n(e),_={name:\"fr-ch\",weekdays:\"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi\".split(\"_\"),months:\"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre\".split(\"_\"),weekStart:1,weekdaysShort:\"dim._lun._mar._mer._jeu._ven._sam.\".split(\"_\"),monthsShort:\"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.\".split(\"_\"),weekdaysMin:\"di_lu_ma_me_je_ve_sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"dans %s\",past:\"il y a %s\",s:\"quelques secondes\",m:\"une minute\",mm:\"%d minutes\",h:\"une heure\",hh:\"%d heures\",d:\"un jour\",dd:\"%d jours\",M:\"un mois\",MM:\"%d mois\",y:\"un an\",yy:\"%d ans\"}};return i.default.locale(_,null,!0),_}));"], "sourceRoot": ""}