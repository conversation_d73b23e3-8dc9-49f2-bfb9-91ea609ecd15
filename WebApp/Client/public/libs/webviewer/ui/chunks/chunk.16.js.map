{"version": 3, "sources": ["webpack:///./src/ui/src/components/PageReplacementModal/PageReplacementModal.scss?635f", "webpack:///./src/ui/src/components/PageReplacementModal/FileInputPanel/FileInputPanel.js", "webpack:///./src/ui/src/components/PageReplacementModal/FileInputPanel/index.js", "webpack:///./src/ui/src/components/PageReplacementModal/FileInputPanel/FileInputPanel.scss?2066", "webpack:///./src/ui/src/components/PageReplacementModal/FileInputPanel/FileInputPanel.scss", "webpack:///./src/ui/src/components/PageReplacementModal/PageReplacementModal.scss"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "FileInputPanel", "defaultValue", "onFileSelect", "acceptFormats", "extension", "setExtension", "t", "useTranslation", "useState", "value", "setValue", "customizableUI", "useSelector", "state", "selectors", "getFeatureFlags", "useEffect", "className", "htmlFor", "type", "id", "style", "width", "height", "paddingLeft", "fontSize", "boxSizing", "onChange", "e", "target", "placeholder", "Dropdown", "disabled", "labelledById", "onClick", "stopPropagation", "items", "aria<PERSON><PERSON><PERSON>", "onClickItem", "currentSelectionKey", "maxHeight"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,osCC3DnC,IAmDeC,EAnDQ,SAAH,GAA+E,IAAzEC,EAAY,EAAZA,aAAcC,EAAY,EAAZA,aAAcC,EAAa,EAAbA,cAAeC,EAAS,EAATA,UAAWC,EAAY,EAAZA,aACvEC,EAAqB,EAAhBC,cAAgB,GAApB,GAC8C,IAA5BC,mBAASP,GAAgB,IAAG,GAA/CQ,EAAK,KAAEC,EAAQ,KAChBC,EAAiBC,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAKC,IAAUC,gBAAgBF,UAAM,aAAhC,EAAkCF,kBAahF,OANAK,qBAAU,WACHf,GAAgBA,IAAiBQ,GACpCC,EAAS,OAKX,yBAAKO,UAAU,kBACb,2BAAOC,QAAQ,WAAWD,UAAU,oBAAoBX,EAAE,qBAC1D,yBAAKW,UAAU,aACb,2BAAOE,KAAK,OACVC,GAAG,WACHH,UAAU,aACVI,MAAO,CAAEC,MAAO,OAAQC,OAAQ,GAAIC,YAAa,EAAGC,SAAU,GAAIC,UAAW,cAC7EjB,MAAOA,EACPkB,SApBS,SAACC,GAChBlB,EAASkB,EAAEC,OAAOpB,OAClBP,EAAa0B,EAAEC,OAAOpB,QAmBhBqB,YAAcnB,EAAkB,GAAKL,EAAE,mBAGxCD,EACD,yBAAKY,UAAU,sBACb,kBAACc,EAAA,EAAQ,CACPC,UAAWvB,EACXW,GAAG,+BACHa,aAAa,qCACbH,YAAaxB,EAAE,uBACf4B,QAAS,SAACN,GAAC,OAAKA,EAAEO,mBAClBC,MAAOjC,EACPkC,UAAW/B,EAAE,sBACbgC,YAAajC,EACbkC,oBAAqBnC,EACrBoC,UAAW,IACXjB,OAAQ,KAEV,2BAAOH,GAAG,sCAAsCd,EAAE,wBAfnC,OClCVN,O,qBCFf,IAAIjC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,6kBAA8kB,M,sBCLvmB0B,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,+9kBAAg+kB,KAGz/kB0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB", "file": "chunks/chunk.16.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./PageReplacementModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "import React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport { useTranslation } from 'react-i18next';\nimport './FileInputPanel.scss';\nimport Dropdown from 'components/Dropdown';\n\nconst FileInputPanel = ({ defaultValue, onFileSelect, acceptFormats, extension, setExtension }) => {\n  const [t] = useTranslation();\n  const [value, setValue] = useState(defaultValue || '');\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n\n  const onChange = (e) => {\n    setValue(e.target.value);\n    onFileSelect(e.target.value);\n  };\n\n  useEffect(() => {\n    if (!defaultValue && defaultValue !== value) {\n      setValue('');\n    }\n  });\n\n  return (\n    <div className=\"FileInputPanel\">\n      <label htmlFor=\"urlInput\" className=\"url-input-header\">{t('link.enterUrlAlt')}</label>\n      <div className=\"url-input\">\n        <input type=\"text\"\n          id=\"urlInput\"\n          className=\"file-input\"\n          style={{ width: '100%', height: 32, paddingLeft: 8, fontSize: 13, boxSizing: 'border-box' }}\n          value={value}\n          onChange={onChange}\n          placeholder={(customizableUI) ? '' : t('link.urlLink')}\n        />\n      </div>\n      {(!setExtension) ? null :\n        <div className=\"extension-dropdown\">\n          <Dropdown\n            disabled={!value}\n            id=\"open-file-extension-dropdown\"\n            labelledById='open-file-extension-dropdown-label'\n            placeholder={t('tool.selectAnOption')}\n            onClick={(e) => e.stopPropagation()}\n            items={acceptFormats}\n            ariaLabel={t('OpenFile.extension')}\n            onClickItem={setExtension}\n            currentSelectionKey={extension}\n            maxHeight={200}\n            height={32}\n          />\n          <label id=\"open-file-extension-dropdown-label\">{t('OpenFile.extension')}</label>\n        </div>\n      }\n    </div>\n  );\n};\n\nexport default FileInputPanel;", "import FileInputPanel from './FileInputPanel';\n\nexport default FileInputPanel;", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./FileInputPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".FileInputPanel{display:flex;justify-content:center;height:100%;flex-direction:column;background-color:var(--document-background-color);border-radius:4px}.FileInputPanel .url-input-header{font-size:13px;line-height:16px;padding-left:16px;padding-bottom:8px;font-weight:700}.FileInputPanel .url-input{padding:0 16px}.FileInputPanel .url-input input:focus{border-color:var(--outline-color)}.FileInputPanel .url-input input:active{border-color:var(--focus-border)}.FileInputPanel ::-moz-placeholder{color:var(--placeholder-text)}.FileInputPanel ::placeholder{color:var(--placeholder-text)}\", \"\"]);\n\n// exports\n", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.OpenFileModal,.open.PageReplacementModal{visibility:visible}.closed.OpenFileModal,.closed.PageReplacementModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.OpenFileModal .container .footer .modal-btn:hover:not(:disabled),.OpenFileModal .footer .modal-button.confirm:hover,.PageReplacementModal .container .footer .modal-btn:hover:not(:disabled),.PageReplacementModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.OpenFileModal .container .footer .modal-btn,.OpenFileModal .footer .modal-button.confirm,.PageReplacementModal .container .footer .modal-btn,.PageReplacementModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.OpenFileModal .container .Button.disabled,.OpenFileModal .container .footer .modal-btn:disabled,.OpenFileModal .footer .modal-button.confirm.disabled,.PageReplacementModal .container .Button.disabled,.PageReplacementModal .container .footer .modal-btn:disabled,.PageReplacementModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.OpenFileModal .container .Button.disabled span,.OpenFileModal .container .footer .modal-btn:disabled span,.OpenFileModal .footer .modal-button.confirm.disabled span,.PageReplacementModal .container .Button.disabled span,.PageReplacementModal .container .footer .modal-btn:disabled span,.PageReplacementModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.OpenFileModal .footer .modal-button.cancel:hover,.OpenFileModal .footer .modal-button.secondary-btn-custom:hover,.PageReplacementModal .footer .modal-button.cancel:hover,.PageReplacementModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.OpenFileModal .footer .modal-button.cancel,.OpenFileModal .footer .modal-button.secondary-btn-custom,.PageReplacementModal .footer .modal-button.cancel,.PageReplacementModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.OpenFileModal .footer .modal-button.cancel.disabled,.OpenFileModal .footer .modal-button.secondary-btn-custom.disabled,.PageReplacementModal .footer .modal-button.cancel.disabled,.PageReplacementModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.OpenFileModal .footer .modal-button.cancel.disabled span,.OpenFileModal .footer .modal-button.secondary-btn-custom.disabled span,.PageReplacementModal .footer .modal-button.cancel.disabled span,.PageReplacementModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.OpenFileModal.modular-ui .thumb-card:hover,.PageReplacementModal.modular-ui .thumb-card:hover{cursor:pointer;border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.OpenFileModal,.PageReplacementModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.OpenFileModal .modal-container .wrapper .modal-content,.PageReplacementModal .modal-container .wrapper .modal-content{padding:10px}.OpenFileModal .footer,.PageReplacementModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.OpenFileModal .footer.modal-footer,.PageReplacementModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.OpenFileModal .footer .modal-button,.PageReplacementModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.OpenFileModal .footer .modal-button.confirm,.PageReplacementModal .footer .modal-button.confirm{margin-left:4px}.OpenFileModal .footer .modal-button.secondary-btn-custom,.PageReplacementModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OpenFileModal .footer .modal-button,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageReplacementModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OpenFileModal .footer .modal-button,.App.is-web-component:not(.is-in-desktop-only-mode) .PageReplacementModal .footer .modal-button{padding:23px 8px}}.OpenFileModal .swipe-indicator,.PageReplacementModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OpenFileModal .swipe-indicator,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageReplacementModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .OpenFileModal .swipe-indicator,.App.is-web-component:not(.is-in-desktop-only-mode) .PageReplacementModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OpenFileModal .swipe-indicator,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageReplacementModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OpenFileModal .swipe-indicator,.App.is-web-component:not(.is-in-desktop-only-mode) .PageReplacementModal .swipe-indicator{width:32px}}.OpenFileModal .container,.PageReplacementModal .container{overflow-y:auto;max-height:100%}@media(max-height:500px){.App:not(.is-web-component) .OpenFileModal .container,.App:not(.is-web-component) .PageReplacementModal .container,.OpenFileModal .App:not(.is-web-component) .container,.PageReplacementModal .App:not(.is-web-component) .container{overflow:auto;max-height:100%}}@container (max-height: 500px){.App.is-web-component .OpenFileModal .container,.App.is-web-component .PageReplacementModal .container,.OpenFileModal .App.is-web-component .container,.PageReplacementModal .App.is-web-component .container{overflow:auto;max-height:100%}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OpenFileModal .container,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageReplacementModal .container,.OpenFileModal .App:not(.is-in-desktop-only-mode):not(.is-web-component) .container,.PageReplacementModal .App:not(.is-in-desktop-only-mode):not(.is-web-component) .container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OpenFileModal .container,.App.is-web-component:not(.is-in-desktop-only-mode) .PageReplacementModal .container,.OpenFileModal .App.is-web-component:not(.is-in-desktop-only-mode) .container,.PageReplacementModal .App.is-web-component:not(.is-in-desktop-only-mode) .container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}.OpenFileModal .container .tab-list,.PageReplacementModal .container .tab-list{width:100%;height:28px;display:flex;border-radius:4px;color:var(--text-color)}.OpenFileModal .container .tab-list .tab-options-button,.PageReplacementModal .container .tab-list .tab-options-button{text-align:center;vertical-align:middle;line-height:24px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;flex:1;border-radius:0;cursor:pointer}.OpenFileModal .container .tab-list .tab-options-button:first-child,.PageReplacementModal .container .tab-list .tab-options-button:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.OpenFileModal .container .tab-list .tab-options-button:last-child,.PageReplacementModal .container .tab-list .tab-options-button:last-child{border-bottom-right-radius:4px;border-top-right-radius:4px}.OpenFileModal .container .tab-list .tab-options-button:hover,.PageReplacementModal .container .tab-list .tab-options-button:hover{background:var(--popup-button-hover)}.OpenFileModal .container .tab-list .tab-options-button.selected,.PageReplacementModal .container .tab-list .tab-options-button.selected{cursor:default}.OpenFileModal .container .tab-list .tab-options-button.focus-visible,.OpenFileModal .container .tab-list .tab-options-button:focus-visible,.PageReplacementModal .container .tab-list .tab-options-button.focus-visible,.PageReplacementModal .container .tab-list .tab-options-button:focus-visible{outline:var(--focus-visible-outline)}.OpenFileModal .container .tab-panel,.PageReplacementModal .container .tab-panel{width:100%;display:flex;flex-direction:column;align-items:center}.OpenFileModal .container .tab-panel.focus-visible,.OpenFileModal .container .tab-panel:focus-visible,.PageReplacementModal .container .tab-panel.focus-visible,.PageReplacementModal .container .tab-panel:focus-visible{outline:var(--focus-visible-outline)!important}.OpenFileModal .container,.PageReplacementModal .container{box-sizing:border-box;display:flex;flex-direction:column;border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);padding:0;background:var(--component-background);overflow-y:visible;width:480px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OpenFileModal .container,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageReplacementModal .container{border-radius:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OpenFileModal .container,.App.is-web-component:not(.is-in-desktop-only-mode) .PageReplacementModal .container{border-radius:0}}@media(max-height:320px){.App:not(.is-web-component) .OpenFileModal .container,.App:not(.is-web-component) .PageReplacementModal .container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}@container (max-height: 320px){.App.is-web-component .OpenFileModal .container,.App.is-web-component .PageReplacementModal .container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}.OpenFileModal .container .Button,.PageReplacementModal .container .Button{height:100%;justify-content:right}.OpenFileModal .container .Button.close-button,.PageReplacementModal .container .Button.close-button{justify-content:center}.OpenFileModal .container .page-replacement-divider,.PageReplacementModal .container .page-replacement-divider{border-top:1px solid var(--divider);margin:0}.OpenFileModal .container .modal-container,.PageReplacementModal .container .modal-container{overflow-y:unset}.OpenFileModal .container .tabs-header-container,.PageReplacementModal .container .tabs-header-container{padding:16px}.OpenFileModal .container .header,.PageReplacementModal .container .header{margin:0;display:flex;justify-content:space-between;width:100%;font-size:16px;line-height:24px;font-weight:700}.OpenFileModal .container .footer,.PageReplacementModal .container .footer{margin-top:0;padding:16px;justify-content:flex-end}.OpenFileModal .container .footer.isFileSelected,.PageReplacementModal .container .footer.isFileSelected{justify-content:space-between}.OpenFileModal .container .footer .deselect-thumbnails,.PageReplacementModal .container .footer .deselect-thumbnails{border:none;background-color:transparent;color:var(--secondary-button-text);padding:0 8px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer}.OpenFileModal .container .footer .deselect-thumbnails,:host(:not([data-tabbing=true])) .PageReplacementModal .container .footer .deselect-thumbnails,html:not([data-tabbing=true]) .PageReplacementModal .container .footer .deselect-thumbnails{outline:none}.OpenFileModal .container .footer .deselect-thumbnails:hover,.PageReplacementModal .container .footer .deselect-thumbnails:hover{color:var(--secondary-button-hover)}.OpenFileModal .container .footer .deselect-thumbnails.disabled,.PageReplacementModal .container .footer .deselect-thumbnails.disabled{visibility:hidden}.OpenFileModal .container .footer .modal-btn,.PageReplacementModal .container .footer .modal-btn{border:none;background-color:transparent;border-radius:4px;padding:0 20px;height:32px;width:67px;display:flex;align-items:center;justify-content:center;position:relative;cursor:pointer;background-color:var(--primary-button)}.OpenFileModal .container .footer .modal-btn,:host(:not([data-tabbing=true])) .PageReplacementModal .container .footer .modal-btn,html:not([data-tabbing=true]) .PageReplacementModal .container .footer .modal-btn{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OpenFileModal .container .footer .modal-btn,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageReplacementModal .container .footer .modal-btn{height:32px;width:100px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OpenFileModal .container .footer .modal-btn,.App.is-web-component:not(.is-in-desktop-only-mode) .PageReplacementModal .container .footer .modal-btn{height:32px;width:100px;font-size:13px}}.OpenFileModal .container .footer .modal-btn.noFile,.PageReplacementModal .container .footer .modal-btn.noFile{opacity:.5;cursor:default}.OpenFileModal .container .modal-body,.PageReplacementModal .container .modal-body{height:unset;display:flex;flex-direction:column;font-size:13px}.OpenFileModal .container .modal-body .modal-body-container,.PageReplacementModal .container .modal-body .modal-body-container{width:100%;height:409px;padding:16px 0 16px 16px;border-radius:4px;overflow:auto;background-color:var(--gray-2);display:flex;flex-wrap:wrap;grid-gap:16px;gap:16px}.OpenFileModal .container .modal-body .modal-body-container.isLoading,.PageReplacementModal .container .modal-body .modal-body-container.isLoading{justify-content:center;align-items:center}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OpenFileModal .container .modal-body .modal-body-container,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageReplacementModal .container .modal-body .modal-body-container{padding:12px 0 12px 12px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OpenFileModal .container .modal-body .modal-body-container,.App.is-web-component:not(.is-in-desktop-only-mode) .PageReplacementModal .container .modal-body .modal-body-container{padding:12px 0 12px 12px}}.OpenFileModal .container .tab-panel .panel-body,.PageReplacementModal .container .tab-panel .panel-body{width:100%;height:240px;position:relative;padding:0 16px 16px;font-size:13px;border-radius:4px}.OpenFileModal .container .tab-panel .panel-body.upload,.PageReplacementModal .container .tab-panel .panel-body.upload{background:transparent}.OpenFileModal .container .tab-list .tab-options-button,.PageReplacementModal .container .tab-list .tab-options-button{padding:0;border:none;background-color:transparent}.OpenFileModal .container .tab-list .tab-options-button,:host(:not([data-tabbing=true])) .PageReplacementModal .container .tab-list .tab-options-button,html:not([data-tabbing=true]) .PageReplacementModal .container .tab-list .tab-options-button{outline:none}.OpenFileModal .container .tab-list .tab-options-divider+.tab-options-button,.PageReplacementModal .container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.OpenFileModal .container .tab-list .tab-options-button,.PageReplacementModal .container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.OpenFileModal .container .tab-list .tab-options-button:first-child,.PageReplacementModal .container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.OpenFileModal .container .tab-list .tab-options-button:last-child,.PageReplacementModal .container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.OpenFileModal .container .tab-list .tab-options-button:hover,.PageReplacementModal .container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.OpenFileModal .container .tab-list .tab-options-button:hover+button,.OpenFileModal .container .tab-list .tab-options-button:hover+div,.PageReplacementModal .container .tab-list .tab-options-button:hover+button,.PageReplacementModal .container .tab-list .tab-options-button:hover+div{border-left:none}.OpenFileModal .container .tab-list .tab-options-button.selected,.PageReplacementModal .container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.OpenFileModal .container .tab-list .tab-options-button.selected+button,.OpenFileModal .container .tab-list .tab-options-button.selected+div,.PageReplacementModal .container .tab-list .tab-options-button.selected+button,.PageReplacementModal .container .tab-list .tab-options-button.selected+div{border-left:none!important}.OpenFileModal .container .tab-list .tab-options-button:not(.selected),.PageReplacementModal .container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}.OpenFileModal.modular-ui .thumb-card,.PageReplacementModal.modular-ui .thumb-card{border:none;box-shadow:inset 0 0 0 1px var(--lighter-border)}.OpenFileModal.modular-ui .thumb-card:hover,.PageReplacementModal.modular-ui .thumb-card:hover{background:var(--gray-1)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};"], "sourceRoot": ""}