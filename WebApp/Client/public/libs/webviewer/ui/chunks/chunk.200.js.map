{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/oc-lnc.js"], "names": ["module", "exports", "e", "n", "default", "d", "s", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "ordinal", "locale"], "mappings": "gFAAoEA,EAAOC,QAAiL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,SAASC,SAAS,2DAA2DC,MAAM,KAAKC,cAAc,uBAAuBD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,sFAAsFH,MAAM,KAAKI,YAAY,qDAAqDJ,MAAM,KAAKK,UAAU,EAAEC,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,mBAAmBC,IAAI,4BAA4BC,KAAK,kCAAkCC,aAAa,CAACC,OAAO,YAAYC,KAAK,QAAQlB,EAAE,gBAAgBmB,EAAE,aAAaC,GAAG,aAAaC,EAAE,UAAUC,GAAG,UAAUvB,EAAE,UAAUwB,GAAG,WAAWC,EAAE,SAASC,GAAG,WAAWC,EAAE,QAAQC,GAAG,UAAUC,QAAQ,SAAShC,GAAG,OAAOA,EAAE,MAAM,OAAOC,EAAEC,QAAQ+B,OAAO7B,EAAE,MAAK,GAAIA,EAAziCD,CAAE,EAAQ", "file": "chunks/chunk.200.js", "sourcesContent": ["!function(e,d){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=d(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],d):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_oc_lnc=d(e.dayjs)}(this,(function(e){\"use strict\";function d(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=d(e),s={name:\"oc-lnc\",weekdays:\"dimenge_diluns_dimars_dimècres_dijòus_divendres_dissabte\".split(\"_\"),weekdaysShort:\"Dg_Dl_Dm_Dc_Dj_Dv_Ds\".split(\"_\"),weekdaysMin:\"dg_dl_dm_dc_dj_dv_ds\".split(\"_\"),months:\"genièr_febrièr_març_abrial_mai_junh_julhet_agost_setembre_octòbre_novembre_decembre\".split(\"_\"),monthsShort:\"gen_feb_març_abr_mai_junh_julh_ago_set_oct_nov_dec\".split(\"_\"),weekStart:1,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM [de] YYYY\",LLL:\"D MMMM [de] YYYY [a] H:mm\",LLLL:\"dddd D MMMM [de] YYYY [a] H:mm\"},relativeTime:{future:\"d'aquí %s\",past:\"fa %s\",s:\"unas segondas\",m:\"una minuta\",mm:\"%d minutas\",h:\"una ora\",hh:\"%d oras\",d:\"un jorn\",dd:\"%d jorns\",M:\"un mes\",MM:\"%d meses\",y:\"un an\",yy:\"%d ans\"},ordinal:function(e){return e+\"º\"}};return n.default.locale(s,null,!0),s}));"], "sourceRoot": ""}