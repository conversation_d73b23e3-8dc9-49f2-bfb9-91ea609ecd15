{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/tet.js"], "names": ["module", "exports", "e", "u", "default", "t", "a", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA8K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,MAAMC,SAAS,kDAAkDC,MAAM,KAAKC,OAAO,0FAA0FD,MAAM,KAAKE,UAAU,EAAEC,cAAc,iCAAiCH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,YAAY,yBAAyBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,2BAA2BC,aAAa,CAACC,OAAO,SAASC,KAAK,WAAWC,EAAE,eAAeC,EAAE,aAAaC,GAAG,YAAYC,EAAE,WAAWC,GAAG,UAAUC,EAAE,YAAYC,GAAG,WAAWC,EAAE,YAAYC,GAAG,WAAWC,EAAE,YAAYC,GAAG,aAAa,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAA3hCD,CAAE,EAAQ", "file": "chunks/chunk.223.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_tet=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var u=t(e),a={name:\"tet\",weekdays:\"Domingu_Segunda_Tersa_Kuarta_Kinta_Sesta_Sabadu\".split(\"_\"),months:\"Janeiru_Fevereiru_Marsu_Abril_Maiu_Juñu_Jullu_Agustu_Setembru_Outubru_Novembru_Dezembru\".split(\"_\"),weekStart:1,weekdaysShort:\"Dom_Seg_Ters_Kua_Kint_Sest_Sab\".split(\"_\"),monthsShort:\"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez\".split(\"_\"),weekdaysMin:\"Do_Seg_Te_Ku_Ki_Ses_Sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"iha %s\",past:\"%s liuba\",s:\"minutu balun\",m:\"minutu ida\",mm:\"minutu %d\",h:\"oras ida\",hh:\"oras %d\",d:\"loron ida\",dd:\"loron %d\",M:\"fulan ida\",MM:\"fulan %d\",y:\"tinan ida\",yy:\"tinan %d\"}};return u.default.locale(a,null,!0),a}));"], "sourceRoot": ""}