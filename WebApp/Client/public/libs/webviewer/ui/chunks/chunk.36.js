(window.webpackJsonp=window.webpackJsonp||[]).push([[36,47],{1576:function(e,t,n){var i=n(32),r=n(1577);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const i=[];return n.querySelectorAll(t).forEach(e=>i.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&i.push(...e(t,n.shadowRoot))}),i}("apryse-webviewer"));const n=[];for(let i=0;i<t.length;i++){const r=t[i];if(0===i)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};i(r,a);e.exports=r.locals||{}},1577:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".SignaturePanel .spinner{margin:10px;border:5px solid #ddd;border-top-color:#aaa;border-radius:50%;width:40px;height:40px;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}",""])},1578:function(e,t,n){var i=n(32),r=n(1579);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const i=[];return n.querySelectorAll(t).forEach(e=>i.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&i.push(...e(t,n.shadowRoot))}),i}("apryse-webviewer"));const n=[];for(let i=0;i<t.length;i++){const r=t[i];if(0===i)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};i(r,a);e.exports=r.locals||{}},1579:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".signature-widget-info{--widget-header-indent:49px;--widget-body-indent:22px;--arrow-width:12px;padding:4px;display:flex;flex-direction:column;font-size:13px;margin-bottom:10px;margin-left:5px;box-sizing:border-box;border:1px solid transparent;cursor:pointer;--border-radius-amount:4px;-moz-border-radius-topleft:var(--border-radius-amount);-moz-border-radius-topright:var(--border-radius-amount);-moz-border-radius-bottomright:var(--border-radius-amount);-moz-border-radius-bottomleft:var(--border-radius-amount);border-radius:var(--border-radius-amount)}.signature-widget-info p+p{margin:1em 0 0}.signature-widget-info p.result-for-header{margin-top:0}.signature-widget-info p.bold{font-weight:700;margin-bottom:4px}.signature-widget-info p.underline{text-decoration:underline}.signature-widget-info .signatureProperties{padding:0;margin:0}.signature-widget-info .signatureProperties:focus,.signature-widget-info .signatureProperties:hover{color:var(--blue-5)}.signature-widget-info .link{cursor:pointer;outline:none;border:0;background-color:transparent;white-space:nowrap}.signature-widget-info .link.focus-visible,.signature-widget-info .link:focus-visible{outline:var(--focus-visible-outline)}.signature-widget-info .link p{margin:0;padding:0}.signature-widget-info .panel-list-text-container{height:100%}.signature-widget-info .panel-list-text-container .panel-list-label-header .Button span{text-align:left;overflow:visible;white-space:normal;text-overflow:inherit}.signature-widget-info .panel-list-icon-container .Icon{width:20px;height:20px}.signature-widget-info .title{padding-left:4px;font-weight:700;display:flex;align-items:center;min-height:32px;margin-top:-5px;margin-bottom:-5px;overflow:hidden;border:0;background-color:transparent}.signature-widget-info .title button+*,.signature-widget-info .title div+*{margin-left:2px}.signature-widget-info .title .arrow{min-width:var(--arrow-width);transition:transform .1s ease;margin-top:0;background-color:transparent;border:none;padding:0;display:flex;justify-content:center;align-items:center}.signature-widget-info .title .arrow.expanded{transform:rotate(90deg)}.signature-widget-info .title .arrow .Icon{width:var(--arrow-width);height:var(--arrow-width)}.signature-widget-info .title .arrow.hidden{visibility:hidden;padding:23px}.signature-widget-info .title .arrow:hover .Icon{color:var(--blue-6)}.signature-widget-info .title .arrow.focus-visible,.signature-widget-info .title .arrow:focus-visible{outline:var(--focus-visible-outline)}.signature-widget-info .title .signature-icon{margin-right:5px}.signature-widget-info .title.focus-visible,.signature-widget-info .title:focus-visible{outline:var(--focus-visible-outline)}.signature-widget-info .header{margin-left:32px}.signature-widget-info .header ul{padding-left:24px}.signature-widget-info .header .body>div:first-child>p:first-child,.signature-widget-info .header .body>p:first-child{margin-top:.5em}.signature-widget-info .header .body>div:last-child{margin-bottom:.5em}.signature-widget-info .header-with-arrow{margin-left:0}.signature-widget-info .header-with-arrow ul{margin-left:var(--arrow-width)}.signature-widget-info .header-with-arrow ul li{margin-left:18px}.signature-widget-info .panel-list-label-header Button{font-weight:700;font-size:13px}.signature-widget-info .signatureDetails,.signature-widget-info .verificationDetails{cursor:default;padding:0;background-color:transparent;border:none;text-align:left}",""])},1580:function(e,t,n){var i=n(32),r=n(1581);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const i=[];return n.querySelectorAll(t).forEach(e=>i.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&i.push(...e(t,n.shadowRoot))}),i}("apryse-webviewer"));const n=[];for(let i=0;i<t.length;i++){const r=t[i];if(0===i)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};i(r,a);e.exports=r.locals||{}},1581:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SignaturePanel{z-index:65;transition:transform .3s ease,visibility 0s ease .3s}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel{top:0;width:100%;height:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel{top:0;width:100%;height:100%}}.open.SignaturePanel{transform:none;visibility:visible;transition:transform .3s ease,visibility 0s ease 0s}.SignaturePanel .empty-panel-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;width:100%;padding:36px;grid-gap:8px;gap:8px}.SignaturePanel .empty-panel-container .empty-icon{width:60px;height:60px;color:var(--gray-6);fill:var(--gray-6)}.SignaturePanel .empty-panel-container .empty-icon svg{width:60px;height:60px}.SignaturePanel .empty-panel-container .empty-message{text-align:center;max-width:131px;font-size:13px}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel .empty-panel-container .empty-message{line-height:15px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel .empty-panel-container .empty-message{line-height:15px}}.SignaturePanel{margin:0 8px 8px;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel{width:auto;margin:16px;flex-grow:1;overflow-y:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel{width:auto;margin:16px;flex-grow:1;overflow-y:auto}}.SignaturePanel .center{display:flex;justify-content:center;align-items:center}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel .signature-widget-info .title .arrow{min-width:auto;padding:3px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel .signature-widget-info .title .arrow{min-width:auto;padding:3px}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1599:function(e,t,n){"use strict";n.r(t),n.d(t,"renderPermissionStatus",(function(){return H})),n.d(t,"SignaturePanel",(function(){return ee})),n.d(t,"Spinner",(function(){return S}));n(23),n(8),n(24),n(10),n(141),n(9),n(11),n(28),n(56),n(1534),n(41),n(30),n(15),n(19),n(12),n(13),n(14),n(16),n(20),n(18),n(22),n(64),n(65),n(66),n(67),n(37),n(39),n(40),n(63);var i=n(0),r=n.n(i),a=n(6),o=n(429),s=n(2),l=n(1),c=n(3);n(60),n(49),n(53),n(348),n(349),n(350),n(351),n(352),n(353),n(354),n(355),n(356),n(357),n(358),n(359),n(360),n(361),n(362),n(363),n(364),n(365),n(366),n(367),n(368),n(369),n(370),n(371),n(372),n(1595),n(1596),n(1597),n(83),n(36),n(96),n(107);function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",s=r.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var a=t&&t.prototype instanceof g?t:g,o=Object.create(a.prototype),s=new A(r||[]);return i(o,"_invoke",{value:E(e,n,s)}),o}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var f={};function g(){}function m(){}function h(){}var y={};l(y,a,(function(){return this}));var b=Object.getPrototypeOf,v=b&&b(b(M([])));v&&v!==t&&n.call(v,a)&&(y=v);var w=h.prototype=g.prototype=Object.create(y);function x(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){var r;i(this,"_invoke",{value:function(i,a){function o(){return new t((function(r,o){!function i(r,a,o,s){var l=d(e[r],e,a);if("throw"!==l.type){var c=l.arg,f=c.value;return f&&"object"==u(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){i("next",e,o,s)}),(function(e){i("throw",e,o,s)})):t.resolve(f).then((function(e){c.value=e,o(c)}),(function(e){return i("throw",e,o,s)}))}s(l.arg)}(i,a,r,o)}))}return r=r?r.then(o,o):o()}})}function E(e,t,n){var i="suspendedStart";return function(r,a){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===r)throw a;return T()}for(n.method=r,n.arg=a;;){var o=n.delegate;if(o){var s=_(o,n);if(s){if(s===f)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===i)throw i="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i="executing";var l=d(e,t,n);if("normal"===l.type){if(i=n.done?"completed":"suspendedYield",l.arg===f)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(i="completed",n.method="throw",n.arg=l.arg)}}}function _(e,t){var n=t.method,i=e.iterator[n];if(void 0===i)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,_(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var r=d(i,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,f;var a=r.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function V(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(V,this),this.reset(!0)}function M(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,r=function t(){for(;++i<e.length;)if(n.call(e,i))return t.value=e[i],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:T}}function T(){return{value:void 0,done:!0}}return m.prototype=h,i(w,"constructor",{value:h,configurable:!0}),i(h,"constructor",{value:m,configurable:!0}),m.displayName=l(h,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,l(e,s,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},x(S.prototype),l(S.prototype,o,(function(){return this})),e.AsyncIterator=S,e.async=function(t,n,i,r,a){void 0===a&&(a=Promise);var o=new S(c(t,n,i,r),a);return e.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},x(w),l(w,s,"Generator"),l(w,a,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var i in t)n.push(i);return n.reverse(),function e(){for(;n.length;){var i=n.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},e.values=M,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function i(n,i){return o.type="throw",o.arg=e,t.next=n,i&&(t.method="next",t.arg=void 0),!!i}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],o=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(s&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,f):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var r=i.arg;k(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:M(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}function g(e,t,n,i,r,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(i,r)}function m(e){return function(){var t=this,n=arguments;return new Promise((function(i,r){var a=e.apply(t,n);function o(e){g(a,i,r,o,s,"next",e)}function s(e){g(a,i,r,o,s,"throw",e)}o(void 0)}))}}var h=function(){var e=m(p().mark((function e(t,n,i,r,a,o,l){var c;return p().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y(t,n,i,r,a,o);case 2:return c=e.sent,l(s.a.setVerificationResult(c)),e.abrupt("return",c);case 5:case"end":return e.stop()}}),e)})));return function(t,n,i,r,a,o,s){return e.apply(this,arguments)}}(),y=function(){var e=m(p().mark((function e(t,n,i,r,a,o){var s,l,c,u,f,g,h;return p().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s=window.Core.PDFNet,l=s.VerificationResult,c=l.TrustStatus,u=l.DigestStatus,f=l.ModificationPermissionsStatus,g=l.DocumentStatus,h={},e.next=6,s.runWithCleanup(m(p().mark((function e(){var l,y,x,S,E,_,V,k,A,M,T,L,O,N,j,C,P,D,I,R,F,H,B,G,U,z,W,q,Y,$,J,K,Z,Q,X,ee,te,ne,ie,re,ae,oe,se,le,ce,ue,de,fe,pe,ge,me,he,ye,be,ve,we,xe;return p().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.getPDFDoc();case 2:return t=e.sent,e.next=5,s.VerificationOptions.create(s.VerificationOptions.SecurityLevel.e_compatibility_and_archiving);case 5:if(l=e.sent,!a){e.next=9;break}return e.next=9,l.enableOnlineCRLRevocationChecking(!0);case 9:if(null!==o){e.next=14;break}return e.next=12,l.setRevocationProxyPrefix("https://proxy.pdftron.com");case 12:e.next=17;break;case 14:if(void 0===o){e.next=17;break}return e.next=17,l.setRevocationProxyPrefix(o);case 17:y=d(n),e.prev=18,S=p().mark((function e(){var t,n,i;return p().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("string"!=typeof(t=x.value)){e.next=13;break}return e.prev=2,e.next=5,l.addTrustedCertificateFromURL(t);case 5:e.next=11;break;case 7:return e.prev=7,e.t0=e.catch(2),console.error("Error encountered when trying to load certificate from URL: ".concat(t,"\n")+"Certificate will not be used as part of verification process."),e.abrupt("return","continue");case 11:e.next=41;break;case 13:if(!(t instanceof File||"[object File]"===Object.prototype.toString.call(t))){e.next=31;break}return n=new FileReader,i=new Promise((function(e,i){n.addEventListener("load",function(){var t=m(p().mark((function t(n){return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e(new Uint8Array(n.target.result));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),n.addEventListener("error",(function(){i("Error reading the local certificate")})),n.readAsArrayBuffer(t)})),e.prev=16,e.t1=l,e.next=20,i;case 20:return e.t2=e.sent,e.next=23,e.t1.addTrustedCertificate.call(e.t1,e.t2);case 23:e.next=29;break;case 25:return e.prev=25,e.t3=e.catch(16),console.error("Error encountered when trying to load certificate: ".concat(e.t3)+"Certificate will not be used as part of the verification process."),e.abrupt("return","continue");case 29:e.next=41;break;case 31:if(!(t instanceof ArrayBuffer||t instanceof Int8Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray)){e.next=41;break}return e.prev=32,e.next=35,l.addTrustedCertificate(t);case 35:e.next=41;break;case 37:return e.prev=37,e.t4=e.catch(32),console.error("Error encountered when trying to load certificate: ".concat(e.t4)+"Certificate will not be used as part of the verification process."),e.abrupt("return","continue");case 41:case"end":return e.stop()}}),e,null,[[2,7],[16,25],[32,37]])})),y.s();case 21:if((x=y.n()).done){e.next=28;break}return e.delegateYield(S(),"t0",23);case 23:if("continue"!==e.t0){e.next=26;break}return e.abrupt("continue",26);case 26:e.next=21;break;case 28:e.next=33;break;case 30:e.prev=30,e.t1=e.catch(18),y.e(e.t1);case 33:return e.prev=33,y.f(),e.finish(33);case 36:E=d(i),e.prev=37,E.s();case 39:if((_=E.n()).done){e.next=70;break}if(V=_.value,k=V.constructor.name,A=["ArrayBuffer","Int8Array","Uint8Array","Uint8ClampedArray"],M=void 0,"Blob"!==k){e.next=50;break}return e.next=47,V.arrayBuffer();case 47:M=e.sent,e.next=56;break;case 50:if(!A.includes(k)){e.next=54;break}M=V,e.next=56;break;case 54:return console.error("The provided TrustList is an unsupported data-structure. Please ensure the TrustList is formatted as one of the following "+"data-structures: ".concat([].concat(A,["Blob"]).join("|"))),e.abrupt("continue",68);case 56:return e.prev=56,e.next=59,s.FDFDoc.createFromMemoryBuffer(M);case 59:return T=e.sent,e.next=62,l.loadTrustList(T);case 62:e.next=68;break;case 64:return e.prev=64,e.t2=e.catch(56),console.error("Error encountered when trying to load certificate: ".concat(e.t2,". ")+"Certificate will not be used as part of the verification process."),e.abrupt("continue",68);case 68:e.next=39;break;case 70:e.next=75;break;case 72:e.prev=72,e.t3=e.catch(37),E.e(e.t3);case 75:return e.prev=75,E.f(),e.finish(75);case 78:return e.next=80,t.getFieldIteratorBegin();case 80:L=e.sent;case 81:return e.next=83,L.hasNext();case 83:if(!e.sent){e.next=275;break}return e.next=86,L.current();case 86:return O=e.sent,e.next=89,O.isValid();case 89:if(e.t4=!e.sent,e.t4){e.next=96;break}return e.next=93,O.getType();case 93:e.t5=e.sent,e.t6=s.Field.Type.e_signature,e.t4=e.t5!==e.t6;case 96:if(!e.t4){e.next=98;break}return e.abrupt("continue",272);case 98:return e.next=100,s.DigitalSignatureField.createFromField(O);case 100:return N=e.sent,e.prev=101,e.next=104,N.verify(l);case 104:return j=e.sent,e.next=107,N.getSDFObj();case 107:return e.next=109,e.sent.getObjNum();case 109:return C=e.sent,P=void 0,D=void 0,I=void 0,R=void 0,F=void 0,H=void 0,B=void 0,G=void 0,U=void 0,z={},W={},e.next=123,N.hasCryptographicSignature();case 123:if(!(q=e.sent)){e.next=177;break}return e.next=127,N.getSubFilter();case 127:if((Y=e.sent)!==s.DigitalSignatureField.SubFilterType.e_adbe_pkcs7_detached){e.next=142;break}return e.next=131,N.getSignerCertFromCMS();case 131:return $=e.sent,e.next=134,$.getSubjectField();case 134:return J=e.sent,e.next=137,w(J);case 137:if(e.t7=e.sent,e.t7){e.next=140;break}e.t7={};case 140:K=e.t7,P=K.e_commonName;case 142:if(Y===s.DigitalSignatureField.SubFilterType.e_ETSI_RFC3161){e.next=171;break}if(P){e.next=152;break}return e.next=146,N.getSignatureName();case 146:if(e.t8=e.sent,e.t8){e.next=151;break}return e.next=150,N.getContactInfo();case 150:e.t8=e.sent;case 151:P=e.t8;case 152:return e.next=154,N.getSigningTime();case 154:return D=e.sent,e.next=157,D.isValid();case 157:if(!e.sent){e.next=161;break}D=b(D,r),e.next=162;break;case 161:D=null;case 162:return e.next=164,N.getContactInfo();case 164:return F=e.sent,e.next=167,N.getLocation();case 167:return H=e.sent,e.next=170,N.getReason();case 170:B=e.sent;case 171:return e.next=173,N.getDocumentPermissions();case 173:return I=e.sent,e.next=176,N.isCertification();case 176:R=e.sent;case 177:return e.next=179,j.getVerificationStatus();case 179:return Z=e.sent,e.next=182,j.getDocumentStatus();case 182:return Q=e.sent,e.next=185,j.getDigestStatus();case 185:return X=e.sent,e.next=188,j.getTrustStatus();case 188:return ee=e.sent,e.next=191,j.getPermissionsStatus();case 191:return te=e.sent,e.next=194,j.getDigestAlgorithm();case 194:return ne=e.sent,e.t9=Promise,e.next=198,j.getDisallowedChanges();case 198:return e.t10=e.sent.map(function(){var e=m(p().mark((function e(t){return p().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.getObjNum();case 2:return e.t0=e.sent,e.next=5,t.getTypeAsString();case 5:return e.t1=e.sent,e.abrupt("return",{objnum:e.t0,type:e.t1});case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.next=201,e.t9.all.call(e.t9,e.t10);case 201:return ie=e.sent,re=ee===c.e_trust_verified,ae=void 0,oe=void 0,se=void 0,le=void 0,e.next=209,j.hasTrustVerificationResult();case 209:if(!e.sent){e.next=260;break}return e.next=213,j.getTrustVerificationResult();case 213:return ce=e.sent,e.next=216,ce.wasSuccessful();case 216:return oe=e.sent,e.next=219,ce.getResultString();case 219:return ae=e.sent,e.next=222,ce.getTimeOfTrustVerificationEnum();case 222:return se=e.sent,e.next=225,ce.getTimeOfTrustVerification();case 225:return(ue=e.sent)&&(le=v(ue,r)),e.next=229,ce.getCertPath();case 229:if(!(de=e.sent).length){e.next=260;break}return fe=de[0],e.next=234,fe.getIssuerField();case 234:return pe=e.sent,e.next=237,w(pe);case 237:return ge=e.sent,Object.assign(z,ge),e.next=241,fe.getSubjectField();case 241:return me=e.sent,e.next=244,w(me);case 244:return he=e.sent,Object.assign(W,he),ye=de[de.length-1],e.prev=247,e.next=250,ye.getNotBeforeEpochTime();case 250:return be=e.sent,e.next=253,ye.getNotAfterEpochTime();case 253:ve=e.sent,G=ve>=ue&&ue>=be,e.next=260;break;case 257:e.prev=257,e.t11=e.catch(247),e.t11.includes("calendar_point::to_std_timepoint() does not support years after")&&(console.warn("The following error is a known issue with Botan, and aims to be addressed in a future release of PDFNet. This currently does not impact PDFTron's Digital Signature Verification capabilities."),console.warn(e.t11));case 260:return we=void 0,we=Z?"digital_signature_valid":Q!==g.e_no_error||X!==u.e_digest_verified&&X!==u.e_digest_verification_disabled||ee===c.e_no_trust_status||te!==f.e_unmodified&&te!==f.e_has_allowed_changes&&te!==f.e_permissions_verification_disabled?"digital_signature_error":"digital_signature_warning",P?U=P:!P&&W.e_commonName&&(U=W.e_commonName),e.next=265,O.getName();case 265:xe=e.sent,h[xe]={signed:q,signer:P,signerName:U,signTime:D,verificationStatus:Z,documentStatus:Q,digestStatus:X,trustStatus:ee,permissionStatus:te,disallowedChanges:ie,trustVerificationResultBoolean:oe,trustVerificationResultString:ae,timeOfTrustVerificationEnum:se,trustVerificationTime:le,id:C,badgeIcon:we,validSignerIdentity:re,digestAlgorithm:ne,documentPermission:I,isCertification:R,contactInfo:F,location:H,reason:B,issuerField:z,subjectField:W,validAtTimeOfSigning:G},e.next=272;break;case 269:e.prev=269,e.t12=e.catch(101),console.error(e.t12);case 272:L.next(),e.next=81;break;case 275:case"end":return e.stop()}}),e,null,[[18,30,33,36],[37,72,75,78],[56,64],[101,269],[247,257]])}))));case 6:return e.abrupt("return",h);case 7:case"end":return e.stop()}}),e)})));return function(t,n,i,r,a,o){return e.apply(this,arguments)}}(),b=function(e,t){var n=e.year,i=e.month,r=e.day,a=e.hour,o=e.minute,s=e.second;return new Date(Date.UTC(n,i-1,r,a,o,s)).toLocaleDateString(t.replace("_","-"),{year:"numeric",month:"long",weekday:"long",day:"numeric",hour:"numeric",minute:"numeric",timeZoneName:"short"})},v=function(e,t){var n=new Date(0);return n.setUTCSeconds(e),n.toLocaleDateString(t.replace("_","-"),{year:"numeric",month:"long",weekday:"long",day:"numeric",hour:"numeric",minute:"numeric",timeZoneName:"short"})},w=function(){var e=m(p().mark((function e(t){var n,i,r,a,o,s,l,c;return p().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n={},e.next=3,t.getAllAttributesAndValues();case 3:i=e.sent,r=d(i),e.prev=5,r.s();case 7:if((a=r.n()).done){e.next=21;break}return o=a.value,e.next=11,o.getAttributeTypeOID();case 11:return s=e.sent,e.next=14,s.getRawValue();case 14:return l=e.sent,e.next=17,o.getStringValue();case 17:c=e.sent,n[x(l)]=c;case 19:e.next=7;break;case 21:e.next=26;break;case 23:e.prev=23,e.t0=e.catch(5),r.e(e.t0);case 26:return e.prev=26,r.f(),e.finish(26);case 29:return e.abrupt("return",n);case 30:case"end":return e.stop()}}),e,null,[[5,23,26,29]])})));return function(t){return e.apply(this,arguments)}}(),x=function(e){return{"[2,5,4,3]":"e_commonName","[2,5,4,4]":"e_surname","[2,5,4,6]":"e_countryName","[2,5,4,7]":"e_localityName","[2,5,4,8]":"e_stateOrProvinceName","[2,5,4,9]":"e_streetAddress","[2,5,4,10]":"e_organizationName","[2,5,4,11]":"e_organizationalUnitName","[1,2,840,113549,1,9,1]":"e_emailAddress"}["string"==typeof e?e:JSON.stringify(e)]},S=(n(1576),function(){return r.a.createElement("div",{className:"spinner"})}),E=(n(26),n(27),n(25),n(4)),_=n.n(E),V=n(17),k=n.n(V),A=(n(88),n(116)),M=n.n(A),T=n(21);function L(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,a,o,s=[],l=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(i=a.call(n)).done)&&(s.push(i.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return O(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return O(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var N=function(e){var t=e.rect,n=L(Object(i.useState)(!1),2),a=n[0],o=n[1];return Object(i.useEffect)((function(){var e=l.a.getScrollViewElement(),t=function(){o(!1)};return e.addEventListener("scroll",t),function(){return e.removeEventListener("scroll",t)}})),Object(i.useEffect)((function(){t&&(setTimeout((function(){o(!0)}),50),setTimeout((function(){o(!1)}),700))}),[t]),a&&M.a.createPortal(r.a.createElement("div",{style:{position:"absolute",top:t.y1,left:t.x1,width:t.x2-t.x1,height:t.y2-t.y1,border:"1px solid #00a5e4",zIndex:99}}),Object(T.a)().querySelector("#app"))},j=n(99),C=n(1496),P=n(48);n(1578);function D(e){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function I(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,a,o,s=[],l=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(i=a.call(n)).done)&&(s.push(i.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function F(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==D(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==D(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===D(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var H=function(e){var t,n=e.isCertification,i=e.ModificationPermissionsStatus,a=e.permissionStatus,o=e.translate;if(e.digestStatus===e.DigestStatusErrorCodes.e_digest_invalid)return r.a.createElement("p",null,o("digitalSignatureVerification.digestStatus.documentHasBeenAltered"));var s=(F(t={},i.e_invalidated_by_disallowed_changes,"digitalSignatureVerification.permissionStatus.invalidatedByDisallowedChanges"),F(t,i.e_has_allowed_changes,"digitalSignatureVerification.permissionStatus.hasAllowedChanges"),F(t,i.e_unmodified,n?"".concat(o("digitalSignatureVerification.permissionStatus.unmodified")," ").concat(o("digitalSignatureVerification.certified"),"."):"".concat(o("digitalSignatureVerification.permissionStatus.unmodified")," ").concat(o("digitalSignatureVerification.signed"),".")),F(t,i.e_permissions_verification_disabled,"digitalSignatureVerification.permissionStatus.permissionsVerificationDisabled"),F(t,i.e_no_permissions_status,"digitalSignatureVerification.permissionStatus.noPermissionsStatus"),F(t,i.e_unsupported_permissions_features,"digitalSignatureVerification.permissionStatus.unsupportedPermissionsFeatures"),t);return r.a.createElement("p",null,o(s[a]||""))},B={name:_.a.string.isRequired,onClick:_.a.func,field:_.a.instanceOf(window.Core.Annotations.Forms.Field)},G=function(e){var t,n,u=e.name,d=e.field,f=Object(a.e)((function(e){return c.a.getVerificationResult(e,u)})),p=I(Object(i.useState)(null),2),g=p[0],m=p[1],h=I(Object(i.useState)(!1),2),y=h[0],b=h[1],v=window.Core.PDFNet,w=v.VerificationResult,x=v.VerificationOptions.TimeMode,S=w.ModificationPermissionsStatus,E=w.DigestStatus,_=I(Object(o.a)(),1)[0],V=f.signed,A=f.signTime,M=f.verificationStatus,T=f.permissionStatus,L=f.disallowedChanges,O=f.trustVerificationResultBoolean,D=f.timeOfTrustVerificationEnum,R=f.trustVerificationTime,F=f.badgeIcon,B=f.isCertification,G=f.contactInfo,U=f.location,z=f.reason,W=f.signerName,q=f.digestStatus,Y=Object(a.d)(),$=function(){!function(e){if(e.widgets.length){var t=e.widgets[0];l.a.jumpToAnnotation(t);var n=l.a.getScrollViewElement(),i=n.scrollLeft,r=n.scrollTop,a=t.getRect(),o=l.a.getDisplayModeObject().pageToWindow({x:a.x1,y:a.y1},t.PageNumber),s=l.a.getDisplayModeObject().pageToWindow({x:a.x2,y:a.y2},t.PageNumber);m({x1:o.x-i,y1:o.y-r,x2:s.x-i,y2:s.y-r})}}(d)},J=Object(j.a)((function(){Y(s.a.setSignatureValidationModalWidgetName(u)),Y(s.a.openElement("signatureValidationModal"))}));return r.a.createElement("div",{className:"signature-widget-info"},V?r.a.createElement(r.a.Fragment,null,r.a.createElement(C.a,{labelHeader:(n=_(B?"digitalSignatureVerification.Certified":"digitalSignatureVerification.Signed"),n+=" ".concat(_("digitalSignatureVerification.by")," ").concat(W||_("digitalSignatureModal.unknown")),A&&(n+=" ".concat(_("digitalSignatureVerification.on")," ").concat(A)),n),iconGlyph:F,useI18String:!1,onClick:$,onKeyDown:$},r.a.createElement("div",{className:"verificationDetails",tabIndex:-1},r.a.createElement("div",{className:"header"},(t=_(B?"digitalSignatureVerification.Certification":"digitalSignatureVerification.Signature"),r.a.createElement("div",{className:"title"},r.a.createElement("p",null,_(M?"digitalSignatureVerification.verificationStatus.valid":"digitalSignatureVerification.verificationStatus.failed",{verificationType:t})))),r.a.createElement("ul",{className:"body"},r.a.createElement("li",null,H({isCertification:B,ModificationPermissionsStatus:S,permissionStatus:T,translate:_,digestStatus:q,DigestStatusErrorCodes:E})),L.map((function(e){var t=e.objnum,n=e.type;return r.a.createElement("li",{key:t},r.a.createElement("p",null,_("digitalSignatureVerification.disallowedChange",{type:n,objnum:t})))})),function(){var e;switch(D){case x.e_current:e=_("digitalSignatureVerification.trustVerification.current");break;case x.e_signing:e=_("digitalSignatureVerification.trustVerification.signing");break;case x.e_timestamp:e=_("digitalSignatureVerification.trustVerification.timestamp");break;default:console.warn("Unexpected pdftron::PDF::VerificationOptions::TimeMode: ".concat(D))}return r.a.createElement(r.a.Fragment,null,r.a.createElement("li",null,r.a.createElement("p",null,_(O?"digitalSignatureVerification.trustVerification.verifiedTrust":"digitalSignatureVerification.trustVerification.noTrustVerification"))),r.a.createElement("li",null,r.a.createElement("p",null,R)),r.a.createElement("li",null,r.a.createElement("p",null,e)))}(),r.a.createElement("li",null,r.a.createElement("button",{"data-element":"signatureProperties-".concat(u),onClick:J,tabIndex:0,className:"signatureProperties link","aria-label":"Open signature properties modal"},r.a.createElement("p",{className:"bold underline"},_("digitalSignatureVerification.signatureProperties"))))))),r.a.createElement("div",{className:"header header-with-arrow"},G||U||z?r.a.createElement("div",{className:"signatureDetails",tabIndex:-1},r.a.createElement("div",{className:"title collapsible"},r.a.createElement(P.a,{img:"icon-chevron-right",className:k()({arrow:!0,expanded:y}),ariaExpanded:y,isActive:y,ariaLabel:_("digitalSignatureVerification.signatureDetails.signatureDetails"),onClick:function(){return b(!y)}}),r.a.createElement("p",null,_("digitalSignatureVerification.signatureDetails.signatureDetails"))),y&&r.a.createElement("ul",{className:"body"},r.a.createElement("li",null,r.a.createElement("p",{className:"bold"},"".concat(_("digitalSignatureVerification.signatureDetails.contactInformation"),":")),r.a.createElement("p",{className:"result-for-header"},G||_("digitalSignatureVerification.signatureDetails.noContactInformation"))),r.a.createElement("li",null,r.a.createElement("p",{className:"bold"},"".concat(_("digitalSignatureVerification.signatureDetails.location"),":")),r.a.createElement("p",{className:"result-for-header"},U||_("digitalSignatureVerification.signatureDetails.noLocation"))),r.a.createElement("li",null,r.a.createElement("p",{className:"bold"},"".concat(_("digitalSignatureVerification.signatureDetails.reason"),":")),r.a.createElement("p",{className:"result-for-header"},z||_("digitalSignatureVerification.signatureDetails.noReason"))),A&&r.a.createElement("li",null,r.a.createElement("p",{className:"bold"},"".concat(_("digitalSignatureVerification.signatureDetails.signingTime"),":")),r.a.createElement("p",{className:"result-for-header"},A||_("digitalSignatureVerification.signatureDetails.noSigningTime"))))):null))):r.a.createElement(C.a,{labelHeader:_("digitalSignatureVerification.unsignedSignatureField",{fieldName:d.name}),iconGlyph:"digital_signature_empty",useI18String:!1,onClick:$,onKeyDown:$}),r.a.createElement(N,{rect:g}))};G.propTypes=B;var U=G,z=(n(1580),n(43)),W=n(31);function q(e){return(q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Y(e){return function(e){if(Array.isArray(e))return X(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Q(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */$=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",s=r.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var a=t&&t.prototype instanceof f?t:f,o=Object.create(a.prototype),s=new V(r||[]);return i(o,"_invoke",{value:x(e,n,s)}),o}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var d={};function f(){}function p(){}function g(){}var m={};l(m,a,(function(){return this}));var h=Object.getPrototypeOf,y=h&&h(h(k([])));y&&y!==t&&n.call(y,a)&&(m=y);var b=g.prototype=f.prototype=Object.create(m);function v(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var r;i(this,"_invoke",{value:function(i,a){function o(){return new t((function(r,o){!function i(r,a,o,s){var l=u(e[r],e,a);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==q(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){i("next",e,o,s)}),(function(e){i("throw",e,o,s)})):t.resolve(d).then((function(e){c.value=e,o(c)}),(function(e){return i("throw",e,o,s)}))}s(l.arg)}(i,a,r,o)}))}return r=r?r.then(o,o):o()}})}function x(e,t,n){var i="suspendedStart";return function(r,a){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===r)throw a;return A()}for(n.method=r,n.arg=a;;){var o=n.delegate;if(o){var s=S(o,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===i)throw i="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i="executing";var l=u(e,t,n);if("normal"===l.type){if(i=n.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(i="completed",n.method="throw",n.arg=l.arg)}}}function S(e,t){var n=t.method,i=e.iterator[n];if(void 0===i)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=u(i,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var a=r.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function V(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,r=function t(){for(;++i<e.length;)if(n.call(e,i))return t.value=e[i],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:A}}function A(){return{value:void 0,done:!0}}return p.prototype=g,i(b,"constructor",{value:g,configurable:!0}),i(g,"constructor",{value:p,configurable:!0}),p.displayName=l(g,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,l(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},v(w.prototype),l(w.prototype,o,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,i,r,a){void 0===a&&(a=Promise);var o=new w(c(t,n,i,r),a);return e.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},v(b),l(b,s,"Generator"),l(b,a,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var i in t)n.push(i);return n.reverse(),function e(){for(;n.length;){var i=n.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},e.values=k,V.prototype={constructor:V,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function i(n,i){return o.type="throw",o.arg=e,t.next=n,i&&(t.method="next",t.arg=void 0),!!i}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],o=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(s&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),_(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var r=i.arg;_(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function J(e,t,n,i,r,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(i,r)}function K(e){return function(){var t=this,n=arguments;return new Promise((function(i,r){var a=e.apply(t,n);function o(e){J(a,i,r,o,s,"next",e)}function s(e){J(a,i,r,o,s,"throw",e)}o(void 0)}))}}function Z(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,a,o,s=[],l=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(i=a.call(n)).done)&&(s.push(i.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||Q(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(e,t){if(e){if("string"==typeof e)return X(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?X(e,t):void 0}}function X(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var ee=function(){var e=Object(a.d)(),t=Z(Object(i.useState)([]),2),n=t[0],u=t[1],d=Z(Object(i.useState)(!1),2),f=d[0],p=d[1],g=Z(Object(i.useState)(""),2),m=g[0],y=g[1],b=Z(Object(i.useState)(l.a.getDocument()),2),v=b[0],w=b[1],x=Z(Object(a.e)((function(e){return[c.a.isElementDisabled(e,"signaturePanel"),c.a.getCertificates(e),c.a.getTrustLists(e),c.a.getCurrentLanguage(e),c.a.getIsRevocationCheckingEnabled(e),c.a.getRevocationProxyPrefix(e)]})),6),E=x[0],_=x[1],V=x[2],k=x[3],A=x[4],M=x[5],T=Z(Object(o.a)(),1)[0],L=function(){var e=K($().mark((function e(){return $().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:w(l.a.getDocument());case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),O=Object(i.useCallback)((function(){p(!0),e(s.a.setVerificationResult({}))}),[p,e]),N=function(e,t){var n=l.a.getAnnotationManager().getFormFieldCreationManager().isInFormFieldCreationMode();"add"===t?C(l.a.getAnnotationManager().getAnnotationsList()):"delete"===t&&n&&P(e)},j=function(){var e=l.a.getAnnotationManager().getAnnotationsList();C(e)},C=function(e){var t=[];e.forEach((function(e){e instanceof window.Core.Annotations.SignatureWidgetAnnotation&&t.push(e.getField())}));var n=new Set(t);u(Y(n))},P=function(e){e.forEach((function(e){D(e)})),j()},D=function(e){if(e instanceof window.Core.Annotations.WidgetAnnotation){var t=l.a.getAnnotationManager(),n=t.getAnnotationsList().filter((function(t){return t.getCustomData("trn-editing-rectangle-id")===e.Id}));t.deleteAnnotations(n)}},I=function(){u([]),j()};if(Object(i.useEffect)((function(){return l.a.addEventListener("documentLoaded",L),l.a.addEventListener("documentUnloaded",O),l.a.addEventListener("annotationChanged",N),l.a.addEventListener("formFieldCreationModeStarted",I),l.a.addEventListener("formFieldCreationModeEnded",I),function(){l.a.removeEventListener("documentLoaded",L),l.a.removeEventListener("documentUnloaded",O),l.a.removeEventListener("annotationChanged",N),l.a.removeEventListener("formFieldCreationModeStarted",I),l.a.removeEventListener("formFieldCreationModeEnded",I)}}),[O]),Object(i.useEffect)((function(){v?l.a.getAnnotationsLoadedPromise().then((function(){p(!0),h(v,_,V,k,A,M,e).then(function(){var e=K($().mark((function e(t){var n;return $().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=l.a.getAnnotationManager().getFieldManager(),u(Object.keys(t).map((function(e){return n.getField(e)}))),y("");case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){e&&e.message?y(e.message):console.error(e)})).then((function(){j()})).finally((function(){p(!1)}))})):p(!0)}),[_,v,e,k]),E)return null;return r.a.createElement("div",{className:"Panel SignaturePanel","data-element":"signaturePanel"},function(){var e;if(f)e=r.a.createElement(S,null);else if("Error reading the local certificate"===m)e=T("digitalSignatureVerification.panelMessages.localCertificateError");else if("Download Failed"===m)e=T("digitalSignatureVerification.panelMessages.certificateDownloadError");else{if(n.length)return null;e=T("digitalSignatureVerification.panelMessages.noSignatureFields")}return r.a.createElement("div",{className:"empty-panel-container"},r.a.createElement(z.a,{className:"empty-icon",glyph:W.c[W.e.SIGNATURE].icon}),r.a.createElement("div",{className:"empty-message"},e))}(),!f&&n.length>0&&n.map((function(e,t){return r.a.createElement(U,{key:t,name:e.name,collapsible:!0,field:e})})))};t.default=ee},1925:function(e,t,n){var i=n(32),r=n(1926);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const i=[];return n.querySelectorAll(t).forEach(e=>i.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&i.push(...e(t,n.shadowRoot))}),i}("apryse-webviewer"));const n=[];for(let i=0;i<t.length;i++){const r=t[i];if(0===i)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};i(r,a);e.exports=r.locals||{}},1926:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".signature-icon .badge{color:#fff}.signature-icon .medium{width:18px;height:18px}.signature-icon .small{width:16px;height:16px}",""])},1927:function(e,t,n){var i=n(32),r=n(1928);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const i=[];return n.querySelectorAll(t).forEach(e=>i.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&i.push(...e(t,n.shadowRoot))}),i}("apryse-webviewer"));const n=[];for(let i=0;i<t.length;i++){const r=t[i];if(0===i)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};i(r,a);e.exports=r.locals||{}},1928:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,".open.SignatureValidationModal{visibility:visible}.closed.SignatureValidationModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SignatureValidationModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.SignatureValidationModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.SignatureValidationModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.SignatureValidationModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.SignatureValidationModal .footer .modal-button.cancel:hover,.SignatureValidationModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.SignatureValidationModal .footer .modal-button.cancel,.SignatureValidationModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.SignatureValidationModal .footer .modal-button.cancel.disabled,.SignatureValidationModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.SignatureValidationModal .footer .modal-button.cancel.disabled span,.SignatureValidationModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.SignatureValidationModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.SignatureValidationModal .modal-container .wrapper .modal-content{padding:10px}.SignatureValidationModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.SignatureValidationModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.SignatureValidationModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.SignatureValidationModal .footer .modal-button.confirm{margin-left:4px}.SignatureValidationModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureValidationModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureValidationModal .footer .modal-button{padding:23px 8px}}.SignatureValidationModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureValidationModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureValidationModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureValidationModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureValidationModal .swipe-indicator{width:32px}}.SignatureValidationModal .container{display:flex;flex-direction:column;justify-content:space-around;border-radius:4px;min-width:350px;padding:20px;max-width:630px}.SignatureValidationModal .validation-header{position:relative;display:flex;justify-content:left;align-items:center;padding-bottom:20px;font-size:16px}.SignatureValidationModal .validation-header [data-element=signatureValidationModalCloseButton]{position:absolute;right:0}.SignatureValidationModal .validation-header [data-element=signatureValidationModalCloseButton]:hover{background:none}.SignatureValidationModal .summary-box{position:relative;display:flex;border:1px solid;border-color:var(--gray-4);border-radius:5px;padding:16px;font-weight:700}.SignatureValidationModal .summary-box>:not(:first-child){margin-left:8px}.SignatureValidationModal .validation-header-valid{background-color:#8dd88d}.SignatureValidationModal .validation-header-warning{background-color:#e2b719}.SignatureValidationModal .validation-header-error{background-color:#ff7979}.SignatureValidationModal .validation-header-unknown{background-color:#ddd}.SignatureValidationModal .body{margin-top:16px}.SignatureValidationModal div.body>div.section:first-child{margin-top:0}.SignatureValidationModal div.body>div.section{margin:16px;padding-bottom:16px;border-bottom:1px solid var(--gray-5)}.SignatureValidationModal div.body>div.section:last-child{margin-bottom:0}.SignatureValidationModal div.body>div.section>p{font-size:13px;margin:8px 0}.SignatureValidationModal div.body>div.section>p:last-child{margin:0}.SignatureValidationModal .header{font-weight:700}.SignatureValidationModal .modal-footer{display:flex;justify-content:flex-end;margin-top:32px}.SignatureValidationModal .modal-footer .close-modal-button{background:var(--primary-button);border:1px;border-color:var(--primary-button);border-radius:4px;padding:0 16px;height:32px;width:-moz-fit-content;width:fit-content;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureValidationModal .modal-footer .close-modal-button{height:40px;width:128px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureValidationModal .modal-footer .close-modal-button{height:40px;width:128px}}.SignatureValidationModal .modal-footer .close-modal-button:hover{background:var(--primary-button-hover)}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1994:function(e,t,n){"use strict";n.r(t);n(19),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(16),n(15),n(20),n(18);var i=n(0),r=n.n(i),a=n(6),o=n(429),s=n(17),l=n.n(s),c=n(3),u=n(5),d=n(48),f=n(341),p=n(1599),g=n(4),m=n.n(g),h=n(43),y=(n(1925),{badge:m.a.string,size:m.a.string}),b=function(e){var t=e.badge,n=e.size,i=void 0===n?"medium":n;return r.a.createElement("div",{className:"signature-icon"},t&&r.a.createElement(h.a,{glyph:t,className:"badge ".concat(i)}))};b.propTypes=y;var v=b,w=n(2),x=(n(1927),n(172));function S(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,a,o,s=[],l=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(i=a.call(n)).done)&&(s.push(i.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return E(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return E(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var _=function(){var e=S(Object(o.a)(),1)[0],t=S(Object(a.e)((function(e){var t=e.digitalSignatureValidation.validationModalWidgetName;return[c.a.isElementOpen(e,u.a.SIGNATURE_VALIDATION_MODAL),c.a.getVerificationResult(e,t)]}),a.c),2),n=t[0],s=t[1],g=Object(a.d)(),m=Object(x.a)((function(){g(w.a.closeElements([u.a.SIGNATURE_VALIDATION_MODAL]))}));Object(i.useEffect)((function(){n&&g(w.a.closeElements([u.a.SIGNATURE_MODAL,u.a.LOADING_MODAL,u.a.PRINT_MODAL,u.a.ERROR_MODAL,u.a.PASSWORD_MODAL]))}),[g,n]);var h,y=s.badgeIcon,b=s.verificationStatus,E=s.permissionStatus,_=s.isCertification,V=s.documentPermission,k=s.trustVerificationResultString,A=s.timeOfTrustVerificationEnum,M=s.trustVerificationTime,T=s.digestAlgorithm,L=s.digestStatus,O=s.documentStatus,N=s.trustStatus,j=s.signerName,C=window.Core.PDFNet,P=C.DigestAlgorithm,D=C.DigitalSignatureField,I=C.VerificationOptions,R=C.VerificationResult,F=R.ModificationPermissionsStatus,H=R.TrustStatus,B=R.DigestStatus,G=R.DocumentStatus,U=I.TimeMode;return r.a.createElement("div",{className:l()({Modal:!0,SignatureValidationModal:!0,open:n,closed:!n}),"data-element":u.a.SIGNATURE_VALIDATION_MODAL},r.a.createElement(f.a,{title:(h=e(_?"digitalSignatureModal.Certification":"digitalSignatureModal.Signature"),e("digitalSignatureModal.title",{type:h})),closeHandler:m,onCloseClick:m,isOpen:n,swipeToClose:!0},r.a.createElement("div",{className:"container"},function(){var t;switch(y){case"digital_signature_valid":t=e("digitalSignatureModal.valid");break;case"digital_signature_warning":t=e("digitalSignatureModal.unknown");break;case"digital_signature_error":t=e("digitalSignatureModal.invalid");break;default:t=e("digitalSignatureModal.unknown")}var n=e(_?"digitalSignatureModal.certification":"digitalSignatureModal.signature");return r.a.createElement("div",null,r.a.createElement("div",{className:"summary-box"},r.a.createElement(v,{badge:y,size:"medium"}),r.a.createElement("div",null,e("digitalSignatureModal.summaryBox.summary",{type:n,status:t}),"digital_signature_valid"===y?e("digitalSignatureModal.summaryBox.signedBy",{name:j||e("digitalSignatureModal.unknown"),interpolation:{escapeValue:!1}}):"")))}(),void 0===b?r.a.createElement("div",{className:"center"},r.a.createElement(p.Spinner,null)):r.a.createElement(r.a.Fragment,null,r.a.createElement("div",{className:"body"},r.a.createElement("div",{className:"section"},r.a.createElement("p",{className:"header"},e("digitalSignatureModal.header.documentIntegrity")),Object(p.renderPermissionStatus)({isCertification:_,ModificationPermissionsStatus:F,permissionStatus:E,translate:e,digestStatus:L,DigestStatusErrorCodes:B}),function(){if(V){var t="",n=_?"certifier":"signer";switch(V){case D.DocumentPermissions.e_no_changes_allowed:t+=e("digitalSignatureModal.documentPermission.noChangesAllowed",{editor:n});break;case D.DocumentPermissions.e_formfilling_signing_allowed:t+=e("digitalSignatureModal.documentPermission.formfillingSigningAllowed",{editor:n});break;case D.DocumentPermissions.e_annotating_formfilling_signing_allowed:t+=e("digitalSignatureModal.documentPermission.annotatingFormfillingSigningAllowed",{editor:n});break;case D.DocumentPermissions.e_unrestricted:t+=e("digitalSignatureModal.documentPermission.unrestricted",{editor:n})}return r.a.createElement("p",null,t)}}()),r.a.createElement("div",{className:"section"},r.a.createElement("p",{className:"header"},e("digitalSignatureModal.header.identitiesTrust")),function(){var t,n=e(_?"digitalSignatureVerification.certifier":"digitalSignatureVerification.signer");switch(N){case H.e_trust_verified:t=e("digitalSignatureVerification.trustStatus.trustVerified",{verificationType:n});break;case H.e_untrusted:t=e("digitalSignatureVerification.trustStatus.untrusted");break;case H.e_trust_verification_disabled:t=e("digitalSignatureVerification.trustStatus.trustVerificationDisabled");break;case H.e_no_trust_status:t=e("digitalSignatureVerification.trustStatus.noTrustStatus")}return r.a.createElement("p",null,t)}(),function(){if(!k)return r.a.createElement("p",null,e("digitalSignatureModal.trustVerification.none"));var t="";switch(A){case U.e_current:t+=e("digitalSignatureModal.trustVerification.current",{trustVerificationTime:M});break;case U.e_signing:t+=e("digitalSignatureModal.trustVerification.signing",{trustVerificationTime:M});break;case U.e_timestamp:t+=e("digitalSignatureModal.trustVerification.timestamp",{trustVerificationTime:M})}return r.a.createElement("p",null,t)}()),r.a.createElement("div",{className:"section"},r.a.createElement("p",{className:"header"},e("digitalSignatureModal.header.generalErrors")),function(){var t;switch(O){case G.e_no_error:t=e("digitalSignatureVerification.documentStatus.noError");break;case G.e_corrupt_file:t=e("digitalSignatureVerification.documentStatus.corruptFile");break;case G.e_unsigned:t=e("digitalSignatureVerification.documentStatus.unsigned");break;case G.e_bad_byteranges:t=e("digitalSignatureVerification.documentStatus.badByteRanges");break;case G.e_corrupt_cryptographic_contents:t=e("digitalSignatureVerification.documentStatus.corruptCryptographicContents")}return r.a.createElement("p",null,t)}()),r.a.createElement("div",{className:"section"},r.a.createElement("p",{className:"header"},e("digitalSignatureModal.header.digestStatus")),function(){var t;switch(L){case B.e_digest_invalid:t=e("digitalSignatureVerification.digestStatus.digestInvalid");break;case B.e_digest_verified:t=e("digitalSignatureVerification.digestStatus.digestVerified");break;case B.e_digest_verification_disabled:t=e("digitalSignatureVerification.digestStatus.digestVerificationDisabled");break;case B.e_weak_digest_algorithm_but_digest_verifiable:t=e("digitalSignatureVerification.digestStatus.weakDigestAlgorithmButDigestVerifiable");break;case B.e_no_digest_status:t=e("digitalSignatureVerification.digestStatus.noDigestStatus");break;case B.e_unsupported_encoding:t=e("digitalSignatureVerification.digestStatus.unsupportedEncoding")}return r.a.createElement("p",null,t)}(),function(){var t=e("digitalSignatureModal.digestAlgorithm.preamble");switch(T){case P.Type.e_SHA1:t+="SHA1.";break;case P.Type.e_SHA256:t+="SHA256.";break;case P.Type.e_SHA384:t+="SHA384.";break;case P.Type.e_SHA512:t+="SHA512.";break;case P.Type.e_RIPEMD160:t+="RIPEMD160.";break;case P.Type.e_unknown_digest_algorithm:t=e("digitalSignatureModal.digestAlgorithm.unknown")}return r.a.createElement("p",null,t)}())),r.a.createElement("div",{className:"modal-footer"},r.a.createElement(d.a,{className:"close-modal-button",onClick:m,label:e("action.close")}))))))};t.default=_}}]);
//# sourceMappingURL=chunk.36.js.map