(window.webpackJsonp=window.webpackJsonp||[]).push([[4],{1487:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r}));n(16);var o=function(e,t,n){return{icon:t,label:n,title:n,option:e,dataElement:"".concat(e[0].toUpperCase()+e.slice(1),"Button")}},i={OPENFILE:"openFile",RENAME:"rename",SETDEST:"setDestination",DOWNLOAD:"download",DELETE:"delete",OPENFORMFIELDPANEL:"openFormFieldPanel",MOVE_UP:"moveUp",MOVE_DOWN:"moveDown",MOVE_LEFT:"moveLeft",MOVE_RIGHT:"moveRight"},r=[o(i.OPENFORMFIELDPANEL,"icon-edit-form-field","action.edit"),o(i.OPENFILE,"icon-portfolio-file","portfolio.openFile"),o(i.RENAME,"ic_edit_page_24px","action.rename"),o(i.SETDEST,"icon-thumbtack","action.setDestination"),o(i.DOWNLOAD,"icon-download","action.download"),o(i.DELETE,"icon-delete-line","action.delete"),o(i.MOVE_UP,"icon-page-move-up","action.moveUp"),o(i.MOVE_DOWN,"icon-page-move-down","action.moveDown"),o(i.MOVE_LEFT,"icon-page-move-left","action.moveLeft"),o(i.MOVE_RIGHT,"icon-page-move-right","action.moveRight")]},1542:function(e,t,n){"use strict";n(41),n(12),n(13),n(36),n(19),n(8),n(14),n(10),n(9),n(11),n(16),n(15),n(20),n(18);var o=n(0),i=n.n(o),r=n(4),l=n.n(r),a=n(17),c=n.n(a),s=n(429),u=n(98),p=n(48),d=n(1313),f=n(43);n(1612);function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,l,a=[],c=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=r.call(n)).done)&&(a.push(o.value),a.length!==t);c=!0);}catch(e){s=!0,i=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(s)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return h(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var b=function(e,t){var n=m(Object(o.useState)(1),2),i=n[0],r=n[1];return Object(o.useEffect)((function(){for(var n=-1,o=null==e?void 0:e.current;o;)n++,o=o.parentElement.closest(t);r(n)}),[e]),i};n(49),n(26),n(27),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46);function g(e){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){w(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function w(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==g(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==g(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===g(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var E=function(e,t,n,o,i){var r,l=(w(r={},i.DELETE,(function(){return n})),w(r,i.DOWNLOAD,(function(){return"portfolio"!==t})),w(r,i.OPENFILE,(function(){return"portfolio"!==t})),w(r,i.SETDEST,(function(){return"outline"!==t})),w(r,i.OPENFORMFIELDPANEL,(function(){return["portfolio","bookmark"].includes(t)})),w(r,i.MOVE_LEFT,(function(){return"outline"!==t})),w(r,i.MOVE_RIGHT,(function(){return"outline"!==t})),w(r,i.MOVE_UP,(function(){return"outline"!==t&&"portfolio"!==t})),w(r,i.MOVE_DOWN,(function(){return"outline"!==t&&"portfolio"!==t})),r);return e.map((function(e){var n=e.option,i=!!l[n]&&l[n]();return y(y({},e),{},{hidden:i,dataElement:"".concat(t).concat(e.dataElement),onClick:function(){return o(e.option)}})}))},x=n(1487),O=n(6),k=n(2);function S(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,l,a=[],c=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=r.call(n)).done)&&(a.push(o.value),a.length!==t);c=!0);}catch(e){s=!0,i=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(s)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return C(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return C(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var D=function(e){var t=e.children;return t&&0!==t.length?i.a.createElement("ul",{className:"panel-list-children"},t.map((function(e){return i.a.createElement("li",{key:null==e?void 0:e.key},e)}))):null};D.propTypes={children:l.a.oneOfType([l.a.arrayOf(l.a.node),l.a.node])};var N=i.a.memo((function(e){var t=e.iconGlyph,n=e.labelHeader,o=e.onDoubleClick,r=e.onClick,l=e.useI18String,a=e.textColor,s=e.isActive;return i.a.createElement(i.a.Fragment,null,t&&i.a.createElement("div",{className:"panel-list-icon-container"},i.a.createElement(f.a,{glyph:t})),i.a.createElement("div",{className:"panel-list-text-container"},i.a.createElement("div",{className:"panel-list-label-header"},i.a.createElement(p.a,{style:{color:a||"inherit"},ariaLabel:n,label:n,onDoubleClick:o,onClick:r,className:c()({"set-focus":s}),useI18String:l}))))}));N.displayName="PanelItemContent",N.propTypes={iconGlyph:l.a.string,labelHeader:l.a.string.isRequired,onDoubleClick:l.a.func,onClick:l.a.func,useI18String:l.a.bool,textColor:l.a.string,isActive:l.a.bool};var M=function(e){var t=e.checkboxOptions,n=e.children,r=e.contentMenuFlyoutOptions,l=void 0===r?{}:r,a=e.contextMenuMoreButtonOptions,f=void 0===a?{}:a,m=e.description,h=e.enableMoreOptionsContextMenuFlyout,g=e.iconGlyph,v=e.labelHeader,y=e.useI18String,w=void 0===y||y,C=e.onDoubleClick,M=void 0===C?function(){}:C,j=e.onClick,A=void 0===j?function(){}:j,I=e.expanded,T=e.setIsExpandedHandler,F=e.textColor,L=e.isActive,P=Object(o.useRef)(),R=b(P),_=S(Object(o.useState)(null!=I&&I),2),B=_[0],H=_[1],V=Object(s.a)().t,G=Object(O.d)(),U=l.shouldHideDeleteButton,W=void 0!==U&&U,q=l.currentFlyout,z=l.flyoutSelector,J=l.type,$=l.handleOnClick,K=f.flyoutToggleElement,Q=f.moreOptionsDataElement,X=t&&!t.disabled||!1;return i.a.createElement("div",{"data-element":"panelListItem",className:"panel-list-item",ref:P},i.a.createElement("div",{className:c()({"panel-list-grid":!0,"grid-with-2-rows":m,"grid-with-1-row":!m,"grid-with-3-columns":!g&&!t,"grid-with-4-columns":g||t,"grid-with-5-columns":g&&t})},i.a.createElement("div",{className:c()("panel-list-row".concat(t?" with-checkbox":""),"focusable-container")},X&&i.a.createElement("div",{style:{"--checkbox-left":"".concat(-32*R+4,"px")},className:"checkbox"},i.a.createElement(d.a,{role:"checkbox",id:null==t?void 0:t.id,"aria-label":null==t?void 0:t.ariaLabel,"aria-checked":null==t?void 0:t.checked,checked:null==t?void 0:t.checked,onChange:null==t?void 0:t.onChange})),i.a.createElement("div",{onClick:function(){H(!B),T&&T(!B)},className:c()({"chevron-container":!0,toggled:B,visible:n&&n.length>0})},i.a.createElement(p.a,{img:"icon-chevron-right",className:"panel-list-button",ariaExpanded:B,ariaLabel:"".concat(V(B?"action.collapse":"action.expand")," ").concat(v)})),i.a.createElement(N,{iconGlyph:g,labelHeader:v,onDoubleClick:M,onClick:A,useI18String:w,textColor:F,isActive:L}),h&&i.a.createElement("div",{className:"panel-list-more-options"},i.a.createElement(u.a,{className:"toggle-more-button",title:"".concat(V("option.searchPanel.moreOptions")," ").concat(v),toggleElement:K,dataElement:Q,img:"icon-tool-more",disabled:!1,onClick:function(){var e={dataElement:z,className:"MoreOptionsContextMenuFlyout",items:E(x.a,J,W,$,x.b)};G(q?k.a.updateFlyout(z,e):k.a.addFlyout(e)),G(k.a.setFlyoutToggleElement(Q)),G(k.a.toggleElement(z))}}))),m&&i.a.createElement("div",{className:"panel-list-description"},m)),B&&i.a.createElement(D,null,n))};M.propTypes={checkboxOptions:l.a.shape({id:l.a.string,checked:l.a.bool,onChange:l.a.func,ariaLabel:l.a.string,disabled:l.a.bool}),useI18String:l.a.bool,iconGlyph:l.a.string,labelHeader:l.a.string.isRequired,description:l.a.string,enableMoreOptionsContextMenuFlyout:l.a.bool,children:l.a.node,onDoubleClick:l.a.func,onClick:l.a.func,expanded:l.a.bool,textColor:l.a.string,setIsExpandedHandler:l.a.func,contentMenuFlyoutOptions:l.a.shape({shouldHideDeleteButton:l.a.bool,currentFlyout:l.a.object,flyoutSelector:l.a.string,type:l.a.string,handleOnClick:l.a.func}),contextMenuMoreButtonOptions:l.a.shape({flyoutToggleElement:l.a.string,moreOptionsDataElement:l.a.string}),isActive:l.a.bool};t.a=M},1612:function(e,t,n){var o=n(32),i=n(1613);"string"==typeof(i=i.__esModule?i.default:i)&&(i=[[e.i,i,""]]);var r={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const i=t[o];if(0===o)i.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);i.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(i,r);e.exports=i.locals||{}},1613:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,'.panel-list-item{width:100%;display:flex;flex-direction:column;position:relative;box-sizing:border-box;list-style-type:none}.panel-list-item ul{list-style-type:none;margin:0}.panel-list-item li::marker{content:"";margin:0}.panel-list-grid{display:grid;align-items:center;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;margin-top:8px;margin-bottom:8px}.panel-list-grid.grid-with-1-row{grid-template-rows:auto}.panel-list-grid.grid-with-2-rows{grid-template-rows:auto auto}.panel-list-grid.grid-with-3-columns{grid-template-columns:auto minmax(0,1fr) auto}.panel-list-grid.grid-with-4-columns{grid-template-columns:auto auto minmax(0,1fr) auto}.panel-list-grid.grid-with-5-columns{grid-template-columns:auto auto auto minmax(0,1fr) auto}.panel-list-grid:hover .panel-list-more-options,.panel-list-grid[focus-within] .panel-list-more-options{visibility:visible}.panel-list-grid:focus-within .panel-list-more-options,.panel-list-grid:hover .panel-list-more-options{visibility:visible}.panel-list-row{display:contents}.panel-list-row.with-checkbox{padding-left:32px}.panel-list-row .checkbox{margin:0;position:relative;left:0;left:var(--checkbox-left,0)}.panel-list-row .chevron-container{min-width:24px;transition:transform .1s ease;visibility:hidden}.panel-list-row .chevron-container.toggled{transform:rotate(90deg)}.panel-list-row .chevron-container.visible{visibility:visible}.panel-list-row .chevron-container:hover{cursor:pointer;border:none;border-radius:4px;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.panel-list-row .chevron-container .Button{width:24px;height:24px}.panel-list-row .chevron-container .Button .Icon{width:12px;height:12px}.panel-list-row .panel-list-icon-container .Icon{width:24px;height:24px}.panel-list-row .panel-list-text-container{grid-area:1/-3/auto/-2;display:flex;flex-direction:row;height:24px}.panel-list-row .panel-list-text-container .panel-list-label-header{align-content:center;margin:0;width:100%}.panel-list-row .panel-list-text-container .panel-list-label-header .set-focus{color:#2c73ab}.panel-list-row .panel-list-text-container .panel-list-label-header .Button{display:flex;width:auto;max-width:100%;height:100%;padding:2px 0 2px 4px;justify-content:start}.panel-list-row .panel-list-text-container .panel-list-label-header .Button:focus{color:#2c73ab}.panel-list-row .panel-list-text-container .panel-list-label-header .Button:hover{cursor:pointer;border:none;border-radius:4px;box-shadow:unset;color:var(--blue-6)}.panel-list-row .panel-list-text-container .panel-list-label-header .Button span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:inline-block;flex-grow:1}.panel-list-row .panel-list-more-options{grid-area:1/-2/auto/-1;display:flex;justify-content:flex-end;margin-left:2px;visibility:hidden}.panel-list-row .panel-list-more-options .Button{width:24px;height:24px;min-width:24px}.panel-list-row .panel-list-more-options .Button:focus{color:var(--blue-6)}.panel-list-row .panel-list-more-options .Button .Icon{width:12px;height:12px}.panel-list-description{grid-area:2/-3/auto/-2;display:flex;align-items:center;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;padding:2px 0 2px 4px;height:24px}.panel-list-children{padding-left:32px}',""])}}]);
//# sourceMappingURL=chunk.4.js.map