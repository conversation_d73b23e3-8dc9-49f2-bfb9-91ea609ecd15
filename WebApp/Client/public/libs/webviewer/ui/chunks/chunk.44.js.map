{"version": 3, "sources": ["webpack:///./src/ui/src/components/ListSeparator/ListSeparator.js", "webpack:///./src/ui/src/components/ListSeparator/index.js", "webpack:///./src/ui/src/components/ListSeparator/ListSeparator.scss?fd29", "webpack:///./src/ui/src/components/ListSeparator/ListSeparator.scss", "webpack:///./src/ui/src/helpers/mapValidationResponseToTranslation.js", "webpack:///./src/ui/src/components/ModularComponents/IndexPanel/IndexPanel.scss?427c", "webpack:///./src/ui/src/components/ModularComponents/IndexPanel/IndexPanel.scss", "webpack:///./src/ui/src/components/ModularComponents/IndexPanelContent/IndexPanelContent.scss?6d9a", "webpack:///./src/ui/src/components/ModularComponents/IndexPanelContent/IndexPanelContent.scss", "webpack:///./src/ui/src/components/ModularComponents/IndexPanelContent/IndexPanelContent.js", "webpack:///./src/ui/src/components/ModularComponents/IndexPanelContent/index.js", "webpack:///./src/ui/src/components/ModularComponents/IndexPanel/IndexPanel.js", "webpack:///./src/ui/src/components/ModularComponents/IndexPanel/IndexPanelContainer.js", "webpack:///./src/ui/src/components/ModularComponents/IndexPanel/index.js"], "names": ["propTypes", "renderContent", "PropTypes", "func", "children", "node", "ListSeparator", "props", "content", "className", "React", "memo", "api", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "errorType", "isMultiSelectionMode", "bool", "fieldName", "string", "widgetId", "icon", "setSelected", "isActive", "handleDeletion", "isSubLevel", "childWidgets", "array", "selectingWidgets", "IndexPanelContent", "forwardRef", "ref", "defaultFiledName", "t", "useTranslation", "useState", "isDefault", "setIsDefault", "isEditing", "setIsEditing", "setFieldName", "<PERSON><PERSON><PERSON><PERSON>", "setIsValid", "validationMessage", "setValidationMessage", "inputRef", "useRef", "dispatch", "useDispatch", "onSaveFieldName", "validationResponse", "formFieldCreationManager", "core", "getFormFieldCreationManager", "validatedResponse", "widget", "getAnnotationById", "child", "mapValidationResponseToTranslation", "onCloseRenaming", "useEffect", "current", "focus", "select", "flyoutSelector", "DataElements", "BOOKMARK_OUTLINE_FLYOUT", "currentFlyout", "useSelector", "state", "selectors", "getFlyout", "contextMenuMoreButtonOptions", "moreOptionsDataElement", "flyoutToggleElement", "contentMenuFlyoutOptions", "shouldHideDeleteButton", "type", "handleOnClick", "val", "menuTypes", "OPENFORMFIELDPANEL", "actions", "openElement", "FORM_FIELD_PANEL", "RENAME", "DELETE", "checkboxOptions", "id", "checked", "onChange", "e", "target", "key", "PanelListItem", "iconGlyph", "labelHeader", "enableMoreOptionsContextMenuFlyout", "onDoubleClick", "onClick", "map", "getDataWithKey", "mapAnnotationToKey", "Id", "includes", "Input", "name", "aria-label", "value", "onKeyDown", "stopPropagation", "<PERSON><PERSON><PERSON><PERSON>", "onBlur", "relatedTarget", "preventDefault", "messageText", "message", "<PERSON><PERSON>", "label", "isSubmitType", "disabled", "displayName", "widgets", "arrayOf", "object", "isRequired", "IndexPanel", "isElementOpen", "INDEX_PANEL", "isElementDisabled", "isInDesktopOnlyMode", "isOpen", "isDisabled", "setMultiSelectionMode", "setSelectingWidgets", "showIndexPanel", "isMobile", "isMobileSize", "listRef", "containerRef", "singleSelectedNoteId", "selectedWidgets", "getSelectedAnnotations", "filter", "annot", "Core", "Annotations", "WidgetAnnotation", "scrollToRow", "onAnnotationSelected", "annotations", "action", "deselectedWidgets", "prevSelectedWidgets", "addEventListener", "removeEventListener", "element", "parent", "parentRect", "getBoundingClientRect", "childRect", "top", "clientHeight", "isElementViewable", "scrollIntoView", "behavior", "block", "inline", "dataToRender", "reduce", "arr", "field", "getField", "constructRadioWidgetGroupByPageNum", "sortedPageNumbers", "Object", "keys", "sort", "a", "b", "deleteAnnotations", "handleSetSelected", "selectWidgetsId", "childrenWidgetIds", "annots", "deselectAllAnnotations", "selectAnnotations", "jumpToAnnotation", "handleMultiSelection", "force", "isSelected", "selectAnnotation", "handleSingleSelection", "NoFields", "Icon", "glyph", "isRadioGroupSelected", "ids", "generateRefList", "closeIndexPanel", "closeElement", "DataElementWrapper", "dataElement", "pageNumber", "img", "parentDataElement", "IndexPanelContainer", "undefined", "shallowEqual", "setWidgets", "annotationChangedListener", "defaultWidgets", "getAnnotationsList", "passProps"], "mappings": "gIAKMA,G,QAAY,CAChBC,cAAeC,IAAUC,KACzBC,SAAUF,IAAUG,OAGhBC,EAAiB,SAASC,GAC9B,IAAMC,EAAUD,EAAMN,cAAgBM,EAAMN,gBAAkBM,EAAMH,SACpE,OAAO,wBAAIK,UAAU,iBAAiBD,IAGxCF,EAAcN,UAAYA,EAEXU,UAAMC,KAAKL,GCfXA,O,qBCFf,IAAIM,EAAM,EAAQ,IACFJ,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQK,WAAaL,EAAQM,QAAUN,KAG/CA,EAAU,CAAC,CAACO,EAAOC,EAAIR,EAAS,MAG9C,IAAIS,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP3B,EAAIJ,EAASS,GAI1BF,EAAO2B,QAAUlC,EAAQmC,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,0kCAA2kC,KAGpmC0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,kCCVR,gBACb,OADyB,EAATC,WAEd,IAAK,QACH,MAAO,8CACT,IAAK,YACH,MAAO,qD,qBCLb,IAAIhC,EAAM,EAAQ,IACFJ,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQK,WAAaL,EAAQM,QAAUN,KAG/CA,EAAU,CAAC,CAACO,EAAOC,EAAIR,EAAS,MAG9C,IAAIS,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP3B,EAAIJ,EAASS,GAI1BF,EAAO2B,QAAUlC,EAAQmC,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,wmOAAymO,KAGloO0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI/B,EAAM,EAAQ,IACFJ,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQK,WAAaL,EAAQM,QAAUN,KAG/CA,EAAU,CAAC,CAACO,EAAOC,EAAIR,EAAS,MAG9C,IAAIS,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP3B,EAAIJ,EAASS,GAI1BF,EAAO2B,QAAUlC,EAAQmC,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,4oKAA6oK,M,s4CCWtqK,IAAMhB,EAAY,CAChB6C,qBAAsB3C,IAAU4C,KAChCC,UAAW7C,IAAU8C,OACrBC,SAAU/C,IAAU8C,OACpBE,KAAMhD,IAAU8C,OAChBG,YAAajD,IAAUC,KACvBiD,SAAUlD,IAAU4C,KACpBO,eAAgBnD,IAAUC,KAC1BmD,WAAYpD,IAAU4C,KACtBS,aAAcrD,IAAUsD,MACxBC,iBAAkBvD,IAAUsD,OAGxBE,EAAoBC,sBAAW,WAWlCC,GAAQ,IAVTN,EAAU,EAAVA,WACAT,EAAoB,EAApBA,qBACWgB,EAAgB,EAA3Bd,UACAE,EAAQ,EAARA,SACAC,EAAI,EAAJA,KACAC,EAAW,EAAXA,YACAC,EAAQ,EAARA,SACAC,EAAc,EAAdA,eACAE,EAAY,EAAZA,aACAE,EAAgB,EAAhBA,iBAGOK,EAAqB,EAAhBC,cAAgB,GAApB,GACyC,IAAfC,oBAAS,GAAM,GAA1CC,EAAS,KAAEC,EAAY,KACmB,IAAfF,oBAAS,GAAM,GAA1CG,EAAS,KAAEC,EAAY,KAC8B,IAA1BJ,mBAASH,GAAiB,GAArDd,EAAS,KAAEsB,EAAY,KACc,IAAdL,oBAAS,GAAK,GAArCM,EAAO,KAAEC,EAAU,KACoC,IAAZP,mBAAS,IAAG,GAAvDQ,EAAiB,KAAEC,EAAoB,KACxCC,EAAWC,mBACXC,EAAWC,cAoBXC,EAAkB,WACtB,IAEIC,EAFEC,EAA2BC,IAAKC,8BAClCC,EAAoB,KAExB,GAAKlC,EAIE,CACL,IAAMmC,EAASH,IAAKI,kBAAkBpC,GACtCkC,EAAoBH,EAAyBX,aAAae,EAAQrC,QALlEQ,EAAavB,SAAQ,SAACsD,GACpBH,EAAoBH,EAAyBX,aAAaiB,EAAOvC,MAMrEwB,EAAWY,EAAkBb,SAC7BS,EAAqBQ,YAAmCJ,GACxDV,EAAqBM,GACjBI,EAAkBb,SACpBkB,KAIEA,EAAkB,WACtBrB,GAAaE,EAAaR,GAC1BO,GAAa,GACbG,GAAW,IAabkB,qBAAU,WACJ1C,IAAcc,GAChBQ,EAAaR,KAEd,CAACA,IAEJ4B,qBAAU,WACJtB,IACFO,EAASgB,QAAQC,QACjBjB,EAASgB,QAAQE,UAKjB1B,GAHGC,KAKJ,CAACA,IAGJ,IAAM0B,EAAiB,GAAH,OAAMC,IAAaC,wBAAuB,YAAI9C,GAC5D+C,EAAgBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,UAAUF,EAAOL,MAyBlEQ,EAA+B,CACnCC,uBAAwB,2BAAF,OAA6BrD,GACnDsD,oBAAqBV,GAGjBW,EAA2B,CAC/BC,wBAAwB,EACxBT,gBACAH,iBACAa,KAAMzD,EAAW,aAAe,oBAChC0D,cAjCoB,SAACC,GACrB,OAAQA,GACN,KAAKC,IAAUC,mBACblC,EAASmC,IAAQC,YAAYlB,IAAamB,mBAC1C9D,EAAYF,GAAU,GACtB,MACF,KAAK4D,IAAUK,OACb9C,GAAa,GACb,MACF,KAAKyC,IAAUM,OACb,IAAKlE,EAIH,YAHAM,EAAavB,SAAQ,SAACsD,GACpBjC,EAAeiC,EAAU,OAI7BjC,EAAeJ,MAoBfmE,EAAkB,CACtBC,GAAIpE,EACJqE,QAASlE,EACTmE,SAAU,SAACC,GACJvE,EAILE,EAAYF,EAAUuE,EAAEC,OAAOH,SAH7BnE,EAAYF,EAAUuE,EAAEC,OAAOH,QAAS/D,IAK5C,aAAc,uBACd,eAAgBH,GAqBlB,OACE,yBACE3C,UAAU,uBACViH,IAAKzE,EACLW,IAAKA,GAEJK,GAAa,kBAAC0D,EAAA,EAAa,CAC1BC,UAAW1E,EACX2E,YAAavE,EAAaL,EAAWY,EACrCiE,oCAAoC,EACpCC,cAAe,kBAAM3D,GAAa,IAClC4D,QAAS,kBAAOnF,GAAwBM,EAAYF,IACpDoD,6BAA8BA,EAC9BG,yBAA0BA,EAC1BY,gBAAiBvE,GAAwBuE,GAAmB,KAC5DhE,SAAUA,GAETG,aAAY,EAAZA,EAAc0E,KAAI,SAAC7C,GAClB,OApCc,SAACA,GACrB,IAAMlC,EAAOgF,YAAeC,YAAmB/C,IAASlC,KAChDH,EAAkBqC,EAAlBrC,UAAWqF,EAAOhD,EAAPgD,GACnB,OACE,kBAAC,EAAiB,CAChBV,IAAKU,EACLvF,qBAAsBA,EACtBE,UAAWA,EACXG,KAAMA,EACND,SAAUmF,EACVjF,YAAaA,EACbC,SAAUK,EAAiB4E,SAASD,GACpC/E,eAAgBA,EAChBC,YAAY,IAuBHrD,CAAcmF,OAGxBjB,GACC,yBAAK1D,UAAU,gCACb,yBAAKA,UAAU,yBACb,yBAAKA,UAAU,iCACb,kBAAC6H,EAAA,EAAK,CACJ5B,KAAK,OACL6B,KAAK,aACL3E,IAAKc,EACLjE,UAAU,mDACV+H,aAAY1E,EAAE,eACd2E,MAAO1F,EACP2F,UAhLQ,SAAClB,GACP,UAAVA,EAAEE,MACJF,EAAEmB,kBACGxE,GACHW,KAGU,WAAV0C,EAAEE,KACJlC,KAyKU+B,SApKgB,SAACC,GAC7BjD,GAAW,GACXF,EAAamD,EAAEC,OAAOgB,QAmKVG,UAAU,QACVC,OAvIO,SAACrB,GACDA,EAAEsB,gBAAkBtB,EAAEsB,cAAcrI,UAAU4H,SAAS,4BAC1Eb,EAAEsB,cAAcrI,UAAU4H,SAAS,8BAEjCb,EAAEuB,iBAGJvD,KAiIYwD,YAAc1E,EAAiC,GAAvBR,EAAEU,GAC1ByE,QAAU3E,EAAsB,UAAZ,aAGxB,yBAAK7D,UAAU,gCACb,kBAACyI,EAAA,EAAM,CACLzI,UAAU,4BACV0I,MAAOrF,EAAE,iBACTkE,QAASxC,IAEX,kBAAC0D,EAAA,EAAM,CACLzI,UAAU,0BACV0I,MAAOrF,EAAE,eACTsF,cAAY,EACZpB,QAASlD,EACTuE,UAAW/E,GAAWvB,IAAcc,WAUpDH,EAAkB4F,YAAc,oBAChC5F,EAAkB1D,UAAYA,EAEf0D,ICjQAA,EDiQAA,E,ohEE/Of,IAAM1D,EAAY,CAChBuJ,QAASrJ,IAAUsJ,QAAQtJ,IAAUuJ,QAAQC,YAGzCC,EAAa,SAAH,GAAoB,IAAdJ,EAAO,EAAPA,QASlB,IAJEtD,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUyD,cAAc1D,EAAOJ,IAAa+D,aAC5C1D,IAAU2D,kBAAkB5D,EAAOJ,IAAa+D,aAChD1D,IAAU4D,oBAAoB7D,OAC9B,GAPA8D,EAAM,KACNC,EAAU,KACVF,EAAmB,KAOdjG,EAAqB,EAAhBC,cAAgB,GAApB,GAC6D,IAAfC,oBAAS,GAAM,GAA9DnB,EAAoB,KAAEqH,EAAqB,KACU,IAAZlG,mBAAS,IAAG,GAArDP,EAAgB,KAAE0G,EAAmB,KACtCC,GAAkBH,GAAcD,EAChCK,EAAWC,cACX1F,EAAWC,cACX0F,EAAU5F,iBAAO,IACjB6F,EAAe7F,mBACf8F,EAAuB9F,kBAAQ,GAErCc,qBAAU,WACR,IAAMiF,EAAkBzF,IAAK0F,yBAAyBC,QAAO,SAACC,GAAK,OAAKA,aAAiB1J,OAAO2J,KAAKC,YAAYC,oBAAkB/C,KAAI,SAAC7C,GAAM,OAAKA,EAAOgD,MAC1J+B,EAAoBO,GAChBA,EAAgBhJ,SAClB+I,EAAqB/E,QAAUgF,EAAgB,MAEhD,IAEHjF,qBAAU,WACJhC,EAAiB/B,SAA4C,IAAlC+I,EAAqB/E,QAClDuF,EAAYR,EAAqB/E,SAEjC+E,EAAqB/E,SAAW,IAEjC,CAACjC,IAEJ,IAAMyH,EAAuB,SAACC,EAAaC,GACzC,GAAe,aAAXA,EAAuB,CACzB,IAAMV,EAAkBS,EAAYP,QAAO,SAACC,GAAK,OAAKA,aAAiB1J,OAAO2J,KAAKC,YAAYC,oBAC5F/C,KAAI,SAAC7C,GAAM,OAAKA,EAAOgD,MAC1B+B,EAAoB,EAAIO,IACpBA,EAAgBhJ,SAClB+I,EAAqB/E,QAAUgF,EAAgB,SAE5C,GAAe,eAAXU,EAAyB,CAClC,IAAMC,EAAoBF,EAAYP,QAAO,SAACC,GAAK,OAAKA,aAAiB1J,OAAO2J,KAAKC,YAAYC,oBAAkB/C,KAAI,SAAC7C,GAAM,OAAKA,EAAOgD,MAC1I+B,GAAoB,SAACmB,GAAmB,OAAKA,EAAoBV,QAAO,SAACvD,GAAE,OAAMgE,EAAkBhD,SAAShB,WAIhH5B,qBAAU,WAGR,OAFAR,IAAKsG,iBAAiB,qBAAsBL,GAErC,WACLjG,IAAKuG,oBAAoB,qBAAsBN,MAEhD,CAACrI,IAEJ,IAcMoI,EAAc,SAACvD,GACnB,IAAMpC,EAAQiF,EAAQ7E,QAAQgC,GACzBpC,IAhBmB,SAACmG,GACzB,IAAMC,EAASlB,EAAa9E,QAC5B,IAAKgG,EACH,OAAO,EAET,IAAMC,EAAaD,EAAOE,wBACpBC,EAAYJ,EAAQG,wBAE1B,OACEC,EAAUC,KAAOH,EAAWG,KAC5BD,EAAUC,KAAOH,EAAWG,IAAMJ,EAAOK,aASxBC,CAAkB1G,IAEnCA,EAAM2G,eAAe,CAAEC,SAAU,SAAUC,MAAO,UAAWC,OAAQ,YA0BnEC,EAAe9C,EAAQ+C,QAAO,SAACC,EAAKnH,GACnCmH,EAAInH,EAAmB,cAC1BmH,EAAInH,EAAmB,YAAK,IAC7B,IACkD,EADlD,IACyBmH,EAAInH,EAAmB,aAAE,IAAnD,IAAK,EAAL,qBAAqD,CACnD,GADoB,QACO,YAAMA,EAAkB,UACjD,OAAOmH,GAEV,8BAGD,OADAA,GADAnH,EA/ByC,SAACA,GAC1C,IAAMoH,EAAQpH,EAAOqH,WACflJ,EAAe,GAQrB,QAPgBiJ,aAAK,EAALA,EAAOjD,UAAW,IAC1BvH,SAAQ,SAACsD,GACXA,EAAkB,aAAMF,EAAmB,YAG/C7B,EAAarB,KAAKoD,MAEhB/B,EAAa7B,QAAU,EAClB0D,EAES,CAChB,UAAaA,EAAkB,UAC/B,GAAM,KACN,WAAcA,EAAmB,WACjC7B,gBAcOmJ,CAAmCtH,IACrB,YAAGlD,KAAKkD,GACxBmH,IACN,IAEGI,EAAoBC,OAAOC,KAAKR,GAAcS,MAAK,SAACC,EAAGC,GAAC,OAAKD,EAAIC,KAEjE3J,EAAiB,SAACJ,GACtB,GAAIA,EACFgC,IAAKgI,kBAAkB,CAAChI,IAAKI,kBAAkBpC,SADjD,CAIA,IAAMsG,EAAU9F,EAAiBwE,KAAI,SAACZ,GAAE,OAAKpC,IAAKI,kBAAkBgC,MACpEpC,IAAKgI,kBAAkB1D,KAgDnB2D,EAAoB,SAAC7F,EAAIT,EAAK2C,GAClCkB,EAAqB/E,QAAU2B,EAC1BxE,EApBsB,SAACwE,EAAIT,EAAK2C,GACrC,IAAI4D,EAAkB,GACtB,GAAK9F,EAIH8F,EAAkBvG,EAAM,CAACS,GAAE,SAAK5D,IAAoBA,EAAiBmH,QAAO,SAAC3H,GAAQ,OAAKA,IAAaoE,SAJhG,CACP,IAAM+F,EAAoB7D,EAAQtB,KAAI,SAAC7C,GAAM,OAAKA,EAAW,MAC7D+H,EAAkBvG,EAAM,GAAH,SAAOwG,GAAiB,EAAK3J,IAAoBA,EAAiBmH,QAAO,SAAC3H,GAAQ,OAAMmK,EAAkB/E,SAASpF,MAI1IkH,EAAoBgD,GACpB,IAAME,EAASF,EAAgBlF,KAAI,SAAChF,GAAQ,OAAKgC,IAAKI,kBAAkBpC,MAGxE,GAFAgC,IAAKqI,yBACLrI,IAAKsI,kBAAkBF,GACnBzG,EAAK,CACP,IAAMxB,EAASH,IAAKI,kBAAkBgC,GAAMkC,EAAQ,GAAO,IAC3DtE,IAAKuI,iBAAiBpI,IAUxBqI,CAAqBpG,EAAIT,EAAK2C,GAnDF,SAAClC,GAAsB,IAAlBqG,EAAQ,UAAH,8CAChCC,EAAalK,EAAiB4E,SAAShB,GACvCjC,EAASH,IAAKI,kBAAkBgC,GACtCpC,IAAKqI,0BACDK,GAAeD,GAInBvD,EAAoB,CAAC9C,IACrBpC,IAAK2I,iBAAiB3I,IAAKI,kBAAkBgC,IAC7CpC,IAAKuI,iBAAiBpI,IALpB+E,EAAoB,IA2CpB0D,CAAsBxG,EAAIT,IAgBxBkH,EACJ,yBAAKrN,UAAU,aACb,6BACE,kBAACsN,EAAA,EAAI,CAACtN,UAAU,aAAauN,MAAM,2CAErC,yBAAKvN,UAAU,OAAOqD,EAAE,oCAsBtB7D,EAAgB,SAACmF,GACrB,IAAMrC,EAAYqC,EAAkB,UAC9BlC,EAAOkC,EAAO7B,aAAe,iBAAmB2E,YAAeC,YAAmB/C,IAASlC,KAC3FD,EAAWmC,EAAW,GACtB7B,EAAe6B,EAAO7B,cAAgB,KACtCH,EAAWH,EAAWQ,EAAiB4E,SAASpF,GAd3B,SAACsG,GAAY,IACZ,EADY,IACnBA,GAAO,IAA5B,IAAK,EAAL,qBAA8B,KAAnBnE,EAAM,QACf,IAAK3B,EAAiB4E,SAASjD,EAAW,IACxC,OAAO,GAEV,8BACD,OAAO,EAQ2D6I,CAAqB1K,GACjF6J,EAAoB7J,EAAeA,EAAa0E,KAAI,SAAC3C,GAAK,OAAKA,EAAU,MAAK,GACpF,OACE,kBAAC,EAAiB,CAChBoC,IAAKtC,EAAW,GAChBxB,IAAK,SAAC6H,GAAO,OA/CK,SAACA,EAASpE,EAAI6G,GAC/B7G,EAKHkD,EAAQ7E,QAAQ2B,GAAMoE,EAJtByC,EAAIlM,SAAQ,SAACoG,GACXmC,EAAQ7E,QAAQ0C,GAAMqD,KA4CJ0C,CAAgB1C,EAASxI,EAAUmK,IACrDvK,qBAAsBA,EACtBE,UAAWA,EACXG,KAAMA,EACND,SAAUA,EACVE,YAAa+J,EACb9J,SAAUA,EACVC,eAAgBA,EAChBE,aAAcA,EACdE,iBAAkBA,KAKlB2K,EAAkB,WACtBxJ,EAASmC,IAAQsH,aAAavI,IAAa+D,eAa7C,OAAQO,EAGJ,yBAAK3J,UAAU,yBACb,kBAAC6N,EAAA,EAAkB,CACjB7N,UAAU,qBACV8N,YAAY,uBAEVxE,GAAuBM,GAhB7B,yBAAK5J,UAAU,mBACb,4BAAQA,UAAU,uBAAuBuH,QAASoG,GAChD,kBAACL,EAAA,EAAI,CAACC,MAAM,sBAAsBvN,UAAU,iBAe5C,kBAAC6N,EAAA,EAAkB,CACjB7N,UAAU,kBACV8N,YAAY,+BAEZ,kBAACD,EAAA,EAAkB,CACjB7N,UAAU,iBACV8N,YAAY,8BAEZ,8BAAOzK,EAAE,uCAA6C,KAAGyF,EAAQ7H,OAAO,KAG1E,kBAAC4M,EAAA,EAAkB,CACjBC,YAAY,4BAEV1L,GACA,kBAACqG,EAAA,EAAM,CACLzI,UAAU,uBACV0I,MAAOrF,EAAE,eACTuF,SAA6B,IAAnBE,EAAQ7H,OAClBsG,QAhJc,WAC5BkC,GAAsB,GACtBC,EAAoB,IACpBlF,IAAKqI,yBACL7C,EAAqB/E,SAAW,KA+IrB7C,GACC,kBAACqG,EAAA,EAAM,CACLzI,UAAU,uBACV0I,MAAOrF,EAAE,uCACTkE,QAhJc,WAC5BkC,GAAsB,GACtBC,EAAoB,IACpBlF,IAAKqI,yBACL7C,EAAqB/E,SAAW,QAmJ5B,kBAAC4I,EAAA,EAAkB,CACjB7N,UAAU,aACV8N,YAAY,cACZ3K,IAAK4G,GAEJjB,EAAQ7H,OAvGSiL,EAAkB1E,KAAI,SAACuG,GAC/C,OACE,wBAAI/N,UAAU,uBAAuBiH,IAAK8G,GACxC,kBAAClO,EAAA,EAAa,CAACL,cAAe,2BAAS6D,EAAE,sBAAqB,YAAI0K,MACjEnC,EAAamC,GAAYvG,IAAIhI,OAmGK6N,GAElCjL,GAAwB,kBAACyL,EAAA,EAAkB,CAC1C7N,UAAU,qBACV8N,YAAY,sBAEZ,kBAACrF,EAAA,EAAM,CACLzI,UAAU,yBACVgO,IAAI,mBACJpF,SAAsC,IAA5B5F,EAAiB/B,OAC3BsG,QAAS,kBAAM3E,SAxDvB,MA+DJsG,EAAW3J,UAAYA,EAER2J,Q,kwCCjVf,IAAM3J,EAAY,CAChB0O,kBAAmBxO,IAAU8C,OAC7BuL,YAAarO,IAAU8C,OAAO0G,YAGhC,SAASiF,EAAoBpO,GAC3B,MAAuDA,EAA/CmO,yBAAiB,IAAG,OAAAE,EAAS,EAAEL,EAAgBhO,EAAhBgO,YAErCvE,EAMD,EALG/D,aACF,SAACC,GAAK,MAAK,CACTC,IAAUyD,cAAc1D,EAAOwI,GAAqBH,GAAezI,IAAa+D,gBAElFgF,KACD,GANO,GAOkC,IAAZ7K,mBAAS,IAAG,GAAnCuF,EAAO,KAAEuF,EAAU,KAE1BrJ,qBAAU,WACR,IAAMsJ,EAA4B,WAChC,IAAMC,EAAiB/J,IAAKgK,qBAAqBrE,QAAO,SAACC,GAAK,OAAKA,aAAiB1J,OAAO2J,KAAKC,YAAYC,oBAC5G8D,EAAWE,IAIb,OADA/J,IAAKsG,iBAAiB,oBAAqBwD,GACpC,WACL9J,IAAKuG,oBAAoB,oBAAqBuD,MAE/C,IAEHtJ,qBAAU,WACR,GAAIuE,EAAQ,CACV,IAAMgF,EAAiB/J,IAAKgK,qBAAqBrE,QAAO,SAACC,GAAK,OAAKA,aAAiB1J,OAAO2J,KAAKC,YAAYC,oBAC5G8D,EAAWE,QAEXF,EAAW,MAEZ,CAAC9E,IAEJ,IAAMkF,EAAY,CAChB3F,WAGF,OACE,kBAAC,EAAU,KAAKhJ,EAAW2O,IAI/BP,EAAoB3O,UAAYA,EAEjB2O,QC5DAA", "file": "chunks/chunk.44.js", "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\n\nimport './ListSeparator.scss';\n\nconst propTypes = {\n  renderContent: PropTypes.func,\n  children: PropTypes.node,\n};\n\nconst ListSeparator = (function(props) {\n  const content = props.renderContent ? props.renderContent() : props.children;\n  return <h4 className=\"ListSeparator\">{content}</h4>;\n});\n\nListSeparator.propTypes = propTypes;\n\nexport default React.memo(ListSeparator);\n", "import ListSeparator from './ListSeparator';\n\nexport default ListSeparator;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ListSeparator.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ListSeparator{margin-top:16px;margin-bottom:8px;font-family:Lato;font-weight:500;color:var(--list-separator-color);-webkit-user-select:none;-moz-user-select:none;user-select:none}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "export default ({ errorType }) => {\n  switch (errorType) {\n    case 'empty':\n      return 'formField.formFieldPopup.invalidField.empty';\n    case 'duplicate':\n      return 'formField.formFieldPopup.invalidField.duplicate';\n  }\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./IndexPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.index-panel-container{z-index:65;flex:1;display:flex;flex-direction:column;position:relative;overflow:hidden}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container{padding:var(--padding);padding-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px;margin-bottom:8px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container .close-icon-container .close-icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container{padding:var(--padding);padding-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px;margin-bottom:8px}.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container .close-icon-container .close-icon{width:24px;height:24px}}.index-panel-container .index-page-container{padding:0;margin:0}.index-panel-container .index-page-container .ListSeparator{color:var(--gray-9);font-weight:700}.index-panel-container .index-panel-footer{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);display:flex;justify-content:center;align-items:center}.index-panel-container .title-container{display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center;padding-bottom:var(--padding-tiny)}.index-panel-container .title-container .field-control-button{width:auto}.index-panel-container .title-container .field-control-button span{color:var(--blue-5)}.index-panel-container .title-container .field-control-button span:hover{color:var(--blue-6)}.index-panel-container .title-container .field-control-button,.index-panel-container .title-container .field-control-button.disabled,.index-panel-container .title-container .field-control-button[disabled]{color:var(--secondary-button-text)}.index-panel-container .title-container .field-control-button.disabled,.index-panel-container .title-container .field-control-button[disabled]{opacity:.5}.index-panel-container .title-container .field-control-button.disabled span,.index-panel-container .title-container .field-control-button[disabled] span{color:inherit}.index-panel-container .title-container .field-control-button:not(.disabled):active,.index-panel-container .title-container .field-control-button:not(.disabled):focus,.index-panel-container .title-container .field-control-button:not(.disabled):hover,.index-panel-container .title-container .field-control-button:not([disabled]):active,.index-panel-container .title-container .field-control-button:not([disabled]):focus,.index-panel-container .title-container .field-control-button:not([disabled]):hover{color:var(--blue-6)}.index-panel-container .fields-counter{font-size:16px}.index-panel-container .fields-counter span{font-weight:700}.index-panel-container .multi-selection-button{width:auto;padding:7px}.index-panel-container .multi-selection-button .Icon{width:18px;height:18px}.index-panel-container .multi-selection-button:not(:first-child){margin-left:var(--padding-tiny)}.index-panel-container .multi-selection-button.disabled:hover,.index-panel-container .multi-selection-button[disabled]:hover{border:none;box-shadow:none}.index-panel-container .multi-selection-button:not(.disabled):hover,.index-panel-container .multi-selection-button:not([disabled]):hover{background-color:var(--gray-2);border:1px solid var(--blue-6)}.index-panel-container .IndexPanel{width:100%;display:flex;flex-direction:column;position:relative;flex-grow:1;overflow:auto;padding:16px 0 0}.index-panel-container .IndexPanel .multi-select-place-holder{height:72px}.index-panel-container .IndexPanel .no-fields{display:flex;flex-direction:column;align-items:center}.index-panel-container .IndexPanel .no-fields .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .IndexPanel .no-fields .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .IndexPanel .no-fields .msg{line-height:15px;width:146px}}.index-panel-container .IndexPanel .no-fields .empty-icon,.index-panel-container .IndexPanel .no-fields .empty-icon svg{width:65px;height:83px}.index-panel-container .IndexPanel .no-fields .empty-icon *{fill:var(--gray-5);color:var(--gray-5)}.index-panel-container .IndexPanel .no-results{display:flex;flex-direction:column;align-items:center;padding-right:18px}.index-panel-container .IndexPanel .no-results .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .IndexPanel .no-results .msg{line-height:15px;width:92px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .IndexPanel .no-results .msg{line-height:15px;width:92px}}.index-panel-container .IndexPanel .no-results .empty-icon,.index-panel-container .IndexPanel .no-results .empty-icon svg{width:65px;height:83px}.index-panel-container .IndexPanel .no-results .empty-icon *{fill:var(--border);color:var(--border)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./IndexPanelContent.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".index-drag-container .index-panel-single-container{display:flex;flex-flow:row nowrap;align-items:center;border-radius:4px;margin-top:6px;margin-bottom:6px;padding:var(--padding-small) var(--padding-tiny);min-height:32px}.index-drag-container .index-panel-single-container .index-panel_content-container{display:flex;flex-flow:row nowrap;flex-grow:1;flex-shrink:1;align-items:center}.index-drag-container .index-panel-single-container .index-panel_content-container .ui__input__messageText{margin-left:8px;font-size:13px;font-style:normal;font-weight:400;line-height:normal;color:var(--red)}.index-drag-container .index-panel-single-container .index-panel_content-container .ui__input--message-warning{border-color:var(--red)}.index-drag-container .index-panel-single-container .index-panel-label-row{flex-grow:1;flex-shrink:1;display:flex;flex-flow:row wrap;align-items:flex-start;position:relative;overflow:hidden;padding-left:var(--padding-large)}.index-drag-container .index-panel-single-container .index-panel-label{font-weight:600;flex:1 0 100%;margin-bottom:var(--padding-small)}.index-drag-container .index-panel-single-container .type-icon-container{padding-right:13px;align-self:start;height:30px;display:flex;flex-direction:row;align-items:center}.index-drag-container .index-panel-single-container .index-panel-more-button{display:none;flex-grow:0;flex-shrink:0;width:auto;height:auto;margin-left:var(--padding-tiny);margin-right:0}.index-drag-container .index-panel-single-container .index-panel-more-button .Icon{width:14px;height:14px}.index-drag-container .index-panel-single-container.default{padding:var(--padding-small) 0;border:1px solid transparent}.index-drag-container .index-panel-single-container.default.hover,.index-drag-container .index-panel-single-container.default:hover{cursor:pointer}.index-drag-container .index-panel-single-container.default.hover .index-panel-more-button,.index-drag-container .index-panel-single-container.default:hover .index-panel-more-button{display:flex}.index-drag-container .index-panel-single-container.default:hover{border:1px solid var(--blue-6)}.index-drag-container .index-panel-single-container.default.hover,.index-drag-container .index-panel-single-container.default.selected{background-color:var(--popup-button-active)}.index-drag-container .index-panel-single-container.default.selected{border:1px solid var(--blue-5)}.index-drag-container .index-panel-single-container.editing{padding:var(--padding-medium) 20px var(--padding-medium) 0;background-color:var(--outline-selected)}.index-drag-container .index-panel-single-container .index-panel-checkbox{flex-grow:0;flex-shrink:0;margin-top:1px;margin-bottom:0;margin-right:var(--padding-small)}.index-drag-container .index-panel-single-container .index-panel-text{background-color:inherit;border:none;text-align:left;padding:0;margin:0;cursor:pointer;display:table}.index-drag-container .index-panel-single-container .index-panel-text span{display:table-cell;vertical-align:middle}.index-drag-container .index-panel-single-container .index-panel-input,.index-drag-container .index-panel-single-container .index-panel-text{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 18px);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.index-drag-container .index-panel-single-container .index-panel-outline-input{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 18px);color:var(--text-color);width:calc(100% - var(--padding-large));padding:var(--padding-small)}.index-drag-container .index-panel-single-container .index-panel-outline-input:focus{border-color:var(--outline-color)}.index-drag-container .index-panel-single-container .index-panel-outline-input::-moz-placeholder{color:var(--placeholder-text)}.index-drag-container .index-panel-single-container .index-panel-outline-input::placeholder{color:var(--placeholder-text)}.index-drag-container .index-panel-single-container .index-panel-editing-controls{flex-basis:100%;display:flex;flex-flow:row wrap;justify-content:flex-end;align-items:center;margin-top:var(--padding-medium)}.index-drag-container .index-panel-single-container .index-panel-cancel-button,.index-drag-container .index-panel-single-container .index-panel-save-button{width:auto;padding:6px var(--padding)}.index-drag-container .index-panel-single-container .index-panel-cancel-button{color:var(--secondary-button-text)}.index-drag-container .index-panel-single-container .index-panel-cancel-button:hover{color:var(--secondary-button-hover)}.index-drag-container .index-panel-single-container .index-panel-save-button{color:var(--primary-button-text);background-color:var(--primary-button);margin-left:var(--padding-tiny);border-radius:4px}.index-drag-container .index-panel-single-container .index-panel-save-button:hover{background-color:var(--primary-button-hover)}.index-drag-container .index-panel-single-container .index-panel-save-button.disabled,.index-drag-container .index-panel-single-container .index-panel-save-button:disabled{cursor:not-allowed;background-color:var(--blue-5)!important;opacity:.5}.index-drag-container .index-panel-single-container .index-panel-save-button.disabled span,.index-drag-container .index-panel-single-container .index-panel-save-button:disabled span{color:var(--gray-0)}\", \"\"]);\n\n// exports\n", "import React, { forwardRef, useEffect, useState, useRef, } from 'react';\nimport './IndexPanelContent.scss';\nimport actions from 'actions';\nimport Button from 'components/Button';\nimport core from 'core';\nimport { Input } from '@pdftron/webviewer-react-toolkit';\nimport mapValidationResponseToTranslation from 'src/helpers/mapValidationResponseToTranslation';\nimport { getDataWithKey, mapAnnotationToKey } from 'constants/map';\nimport PropTypes from 'prop-types';\nimport DataElements from 'constants/dataElement';\nimport { useTranslation } from 'react-i18next';\nimport PanelListItem from 'src/components/PanelListItem';\nimport { menuTypes } from 'helpers/outlineFlyoutHelper';\nimport selectors from 'selectors';\nimport { useSelector, useDispatch } from 'react-redux';\n\nconst propTypes = {\n  isMultiSelectionMode: PropTypes.bool,\n  fieldName: PropTypes.string,\n  widgetId: PropTypes.string,\n  icon: PropTypes.string,\n  setSelected: PropTypes.func,\n  isActive: PropTypes.bool,\n  handleDeletion: PropTypes.func,\n  isSubLevel: PropTypes.bool,\n  childWidgets: PropTypes.array,\n  selectingWidgets: PropTypes.array,\n};\n\nconst IndexPanelContent = forwardRef(({\n  isSubLevel,\n  isMultiSelectionMode,\n  fieldName: defaultFiledName,\n  widgetId,\n  icon,\n  setSelected,\n  isActive,\n  handleDeletion,\n  childWidgets,\n  selectingWidgets,\n}, ref) => {\n\n  const [t] = useTranslation();\n  const [isDefault, setIsDefault] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n  const [fieldName, setFieldName] = useState(defaultFiledName);\n  const [isValid, setIsValid] = useState(true);\n  const [validationMessage, setValidationMessage] = useState('');\n  const inputRef = useRef();\n  const dispatch = useDispatch();\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      e.stopPropagation();\n      if ((isEditing)) {\n        onSaveFieldName();\n      }\n    }\n    if (e.key === 'Escape') {\n      onCloseRenaming();\n    }\n  };\n\n\n  const handleFieldNameChange = (e) => {\n    setIsValid(true);\n    setFieldName(e.target.value);\n  };\n\n  const onSaveFieldName = () => {\n    const formFieldCreationManager = core.getFormFieldCreationManager();\n    let validatedResponse = null;\n    let validationResponse = null;\n    if (!widgetId) {\n      childWidgets.forEach((child) => {\n        validatedResponse = formFieldCreationManager.setFieldName(child, fieldName);\n      });\n    } else {\n      const widget = core.getAnnotationById(widgetId);\n      validatedResponse = formFieldCreationManager.setFieldName(widget, fieldName);\n    }\n    setIsValid(validatedResponse.isValid);\n    validationResponse = mapValidationResponseToTranslation(validatedResponse);\n    setValidationMessage(validationResponse);\n    if (validatedResponse.isValid) {\n      onCloseRenaming();\n    }\n  };\n\n  const onCloseRenaming = () => {\n    isEditing && setFieldName(defaultFiledName);\n    setIsEditing(false);\n    setIsValid(true);\n  };\n\n  const handleOnBlur = (e) => {\n    const isClickBtn = e.relatedTarget && (e.relatedTarget.className.includes('index-panel-save-button') ||\n    e.relatedTarget.className.includes('index-panel-cancel-button'));\n    if (isClickBtn) {\n      e.preventDefault();\n      return;\n    }\n    onCloseRenaming();\n  };\n\n  useEffect(() => {\n    if (fieldName !== defaultFiledName) {\n      setFieldName(defaultFiledName);\n    }\n  }, [defaultFiledName]);\n\n  useEffect(() => {\n    if (isEditing) {\n      inputRef.current.focus();\n      inputRef.current.select();\n    }\n    if (!isEditing) {\n      setIsDefault(true);\n    } else {\n      setIsDefault(false);\n    }\n  }, [isEditing]);\n\n\n  const flyoutSelector = `${DataElements.BOOKMARK_OUTLINE_FLYOUT}-${widgetId}`;\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n\n  const handleOnClick = (val) => {\n    switch (val) {\n      case menuTypes.OPENFORMFIELDPANEL:\n        dispatch(actions.openElement(DataElements.FORM_FIELD_PANEL));\n        setSelected(widgetId, true);\n        break;\n      case menuTypes.RENAME:\n        setIsEditing(true);\n        break;\n      case menuTypes.DELETE:\n        if (!widgetId) {\n          childWidgets.forEach((child) => {\n            handleDeletion(child['Id']);\n          });\n          return;\n        }\n        handleDeletion(widgetId);\n        break;\n      default:\n        break;\n    }\n  };\n\n  const contextMenuMoreButtonOptions = {\n    moreOptionsDataElement: `index-panel-more-button-${widgetId}`,\n    flyoutToggleElement: flyoutSelector,\n  };\n\n  const contentMenuFlyoutOptions = {\n    shouldHideDeleteButton: false,\n    currentFlyout,\n    flyoutSelector,\n    type: widgetId ? 'indexPanel' : 'indexPanel.folder',\n    handleOnClick,\n  };\n\n  const checkboxOptions = {\n    id: widgetId,\n    checked: isActive,\n    onChange: (e) => {\n      if (!widgetId) {\n        setSelected(widgetId, e.target.checked, childWidgets);\n        return;\n      }\n      setSelected(widgetId, e.target.checked);\n    },\n    'aria-label': 'index panel checkbox',\n    'aria-checked': isActive,\n  };\n\n  const renderContent = (widget) => {\n    const icon = getDataWithKey(mapAnnotationToKey(widget)).icon;\n    const { fieldName, Id } = widget;\n    return (\n      <IndexPanelContent\n        key={Id}\n        isMultiSelectionMode={isMultiSelectionMode}\n        fieldName={fieldName}\n        icon={icon}\n        widgetId={Id}\n        setSelected={setSelected}\n        isActive={selectingWidgets.includes(Id)}\n        handleDeletion={handleDeletion}\n        isSubLevel={true}\n      />\n    );\n  };\n\n  return (\n    <div\n      className=\"index-drag-container\"\n      key={widgetId}\n      ref={ref}\n    >\n      {isDefault && <PanelListItem\n        iconGlyph={icon}\n        labelHeader={isSubLevel ? widgetId : defaultFiledName}\n        enableMoreOptionsContextMenuFlyout={true}\n        onDoubleClick={() => setIsEditing(true)}\n        onClick={() => !isMultiSelectionMode && setSelected(widgetId)}\n        contextMenuMoreButtonOptions={contextMenuMoreButtonOptions}\n        contentMenuFlyoutOptions={contentMenuFlyoutOptions}\n        checkboxOptions={isMultiSelectionMode && checkboxOptions || null}\n        isActive={isActive}\n      >\n        {childWidgets?.map((widget) => {\n          return renderContent(widget);\n        })}\n      </PanelListItem>}\n      {isEditing && (\n        <div className='index-panel-single-container'>\n          <div className=\"index-panel-label-row\">\n            <div className='index-panel_content-container'>\n              <Input\n                type=\"text\"\n                name=\"field-name\"\n                ref={inputRef}\n                className=\"index-panel-outline-input index-panel-text-input\"\n                aria-label={t('action.name')}\n                value={fieldName}\n                onKeyDown={handleKeyDown}\n                onChange={handleFieldNameChange}\n                fillWidth=\"false\"\n                onBlur={handleOnBlur}\n                messageText={!isValid ? t(validationMessage) : ''}\n                message={!isValid ? 'warning' : 'default'}\n              />\n            </div>\n            <div className=\"index-panel-editing-controls\">\n              <Button\n                className=\"index-panel-cancel-button\"\n                label={t('action.cancel')}\n                onClick={onCloseRenaming}\n              />\n              <Button\n                className=\"index-panel-save-button\"\n                label={t('action.save')}\n                isSubmitType\n                onClick={onSaveFieldName}\n                disabled={!isValid || fieldName === defaultFiledName}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n});\n\nIndexPanelContent.displayName = 'IndexPanelContent';\nIndexPanelContent.propTypes = propTypes;\n\nexport default IndexPanelContent;", "import IndexPanelContent from './IndexPanelContent';\n\nexport default IndexPanelContent;", "import React, {\n  useEffect,\n  useRef,\n  useState,\n} from 'react';\nimport selectors from 'selectors';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector, useDispatch } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport core from 'core';\nimport actions from 'actions';\nimport Icon from 'components/Icon';\nimport Button from 'components/Button';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport IndexPanelContent from 'components/ModularComponents/IndexPanelContent';\nimport DataElements from 'constants/dataElement';\nimport { getDataWithKey, mapAnnotationToKey } from 'constants/map';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport ListSeparator from '../../ListSeparator';\n\nconst propTypes = {\n  widgets: PropTypes.arrayOf(PropTypes.object).isRequired,\n};\n\nconst IndexPanel = ({ widgets }) => {\n  const [\n    isOpen,\n    isDisabled,\n    isInDesktopOnlyMode,\n  ] = useSelector((state) => [\n    selectors.isElementOpen(state, DataElements.INDEX_PANEL),\n    selectors.isElementDisabled(state, DataElements.INDEX_PANEL),\n    selectors.isInDesktopOnlyMode(state),\n  ]);\n\n  const [t] = useTranslation();\n  const [isMultiSelectionMode, setMultiSelectionMode] = useState(false);\n  const [selectingWidgets, setSelectingWidgets] = useState([]);\n  const showIndexPanel = !isDisabled && isOpen;\n  const isMobile = isMobileSize();\n  const dispatch = useDispatch();\n  const listRef = useRef({});\n  const containerRef = useRef();\n  const singleSelectedNoteId = useRef(-1);\n\n  useEffect(() => {\n    const selectedWidgets = core.getSelectedAnnotations().filter((annot) => annot instanceof window.Core.Annotations.WidgetAnnotation).map((widget) => widget.Id);\n    setSelectingWidgets(selectedWidgets);\n    if (selectedWidgets.length) {\n      singleSelectedNoteId.current = selectedWidgets[0];\n    }\n  }, []);\n\n  useEffect(() => {\n    if (selectingWidgets.length && singleSelectedNoteId.current !== -1) {\n      scrollToRow(singleSelectedNoteId.current);\n    } else {\n      singleSelectedNoteId.current = -1;\n    }\n  }, [selectingWidgets]);\n\n  const onAnnotationSelected = (annotations, action) => {\n    if (action === 'selected') {\n      const selectedWidgets = annotations.filter((annot) => annot instanceof window.Core.Annotations.WidgetAnnotation)\n        .map((widget) => widget.Id);\n      setSelectingWidgets([...selectedWidgets]);\n      if (selectedWidgets.length) {\n        singleSelectedNoteId.current = selectedWidgets[0];\n      }\n    } else if (action === 'deselected') {\n      const deselectedWidgets = annotations.filter((annot) => annot instanceof window.Core.Annotations.WidgetAnnotation).map((widget) => widget.Id);\n      setSelectingWidgets((prevSelectedWidgets) => prevSelectedWidgets.filter((id) => !deselectedWidgets.includes(id)));\n    }\n  };\n\n  useEffect(() => {\n    core.addEventListener('annotationSelected', onAnnotationSelected);\n\n    return () => {\n      core.removeEventListener('annotationSelected', onAnnotationSelected);\n    };\n  }, [isMultiSelectionMode]);\n\n  const isElementViewable = (element) => {\n    const parent = containerRef.current;\n    if (!parent) {\n      return false;\n    }\n    const parentRect = parent.getBoundingClientRect();\n    const childRect = element.getBoundingClientRect();\n\n    return (\n      childRect.top >= parentRect.top &&\n      childRect.top <= parentRect.top + parent.clientHeight\n    );\n  };\n\n  const scrollToRow = (key) => {\n    const child = listRef.current[key];\n    if (!child) {\n      return;\n    }\n    const isViewable = isElementViewable(child);\n    if (!isViewable) {\n      child.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });\n    }\n  };\n\n  const constructRadioWidgetGroupByPageNum = (widget) => {\n    const field = widget.getField();\n    const childWidgets = [];\n    const widgets = field?.widgets || [];\n    widgets.forEach((child) => {\n      if (child['PageNumber'] !== widget['PageNumber']) {\n        return;\n      }\n      childWidgets.push(child);\n    });\n    if (childWidgets.length <= 1) {\n      return widget;\n    }\n    const newWidget = {\n      'fieldName': widget['fieldName'],\n      'Id': null,\n      'PageNumber': widget['PageNumber'],\n      childWidgets,\n    };\n    return newWidget;\n  };\n\n  const dataToRender = widgets.reduce((arr, widget) => {\n    if (!arr[widget['PageNumber']]) {\n      arr[widget['PageNumber']] = [];\n    }\n    for (const childWidget of arr[widget['PageNumber']]) {\n      if (childWidget['fieldName'] === widget['fieldName']) {\n        return arr;\n      }\n    }\n    widget = constructRadioWidgetGroupByPageNum(widget);\n    arr[widget['PageNumber']].push(widget);\n    return arr;\n  }, {});\n\n  const sortedPageNumbers = Object.keys(dataToRender).sort((a, b) => a - b);\n\n  const handleDeletion = (widgetId) => {\n    if (widgetId) {\n      core.deleteAnnotations([core.getAnnotationById(widgetId)]);\n      return;\n    }\n    const widgets = selectingWidgets.map((id) => core.getAnnotationById(id));\n    core.deleteAnnotations(widgets);\n  };\n\n  const handleSingleSelection = (id, force = false) => {\n    const isSelected = selectingWidgets.includes(id);\n    const widget = core.getAnnotationById(id);\n    core.deselectAllAnnotations();\n    if (isSelected && !force) {\n      setSelectingWidgets([]);\n      return;\n    }\n    setSelectingWidgets([id]);\n    core.selectAnnotation(core.getAnnotationById(id));\n    core.jumpToAnnotation(widget);\n  };\n\n  const handleEditButtonClick = () => {\n    setMultiSelectionMode(true);\n    setSelectingWidgets([]);\n    core.deselectAllAnnotations();\n    singleSelectedNoteId.current = -1;\n  };\n\n  const handleDoneButtonClick = () => {\n    setMultiSelectionMode(false);\n    setSelectingWidgets([]);\n    core.deselectAllAnnotations();\n    singleSelectedNoteId.current = -1;\n  };\n\n  const handleMultiSelection = (id, val, widgets) => {\n    let selectWidgetsId = [];\n    if (!id) {\n      const childrenWidgetIds = widgets.map((widget) => widget['Id']);\n      selectWidgetsId = val ? [...childrenWidgetIds, ...selectingWidgets] : selectingWidgets.filter((widgetId) => !childrenWidgetIds.includes(widgetId));\n    } else {\n      selectWidgetsId = val ? [id, ...selectingWidgets] : selectingWidgets.filter((widgetId) => widgetId !== id);\n    }\n    setSelectingWidgets(selectWidgetsId);\n    const annots = selectWidgetsId.map((widgetId) => core.getAnnotationById(widgetId));\n    core.deselectAllAnnotations();\n    core.selectAnnotations(annots);\n    if (val) {\n      const widget = core.getAnnotationById(id || widgets[0]['Id']);\n      core.jumpToAnnotation(widget);\n    }\n  };\n\n  const handleSetSelected = (id, val, widgets) => {\n    singleSelectedNoteId.current = id;\n    if (!isMultiSelectionMode) {\n      handleSingleSelection(id, val);\n      return;\n    }\n    handleMultiSelection(id, val, widgets);\n  };\n\n  const generateRefList = (element, id, ids) => {\n    if (!id) {\n      ids.forEach((Id) => {\n        listRef.current[Id] = element;\n      });\n    } else {\n      listRef.current[id] = element;\n    }\n  };\n\n  const NoFields = (\n    <div className=\"no-fields\">\n      <div>\n        <Icon className=\"empty-icon\" glyph=\"illustration - empty state - outlines\" />\n      </div>\n      <div className=\"msg\">{t('formField.indexPanel.notFields')}</div>\n    </div>\n  );\n\n  const showFields = () => (sortedPageNumbers.map((pageNumber) => {\n    return (\n      <ul className=\"index-page-container\" key={pageNumber}>\n        <ListSeparator renderContent={() => `${t('option.shared.page')} ${pageNumber}`} />\n        {dataToRender[pageNumber].map(renderContent)}\n      </ul>\n    );\n  }));\n\n  const isRadioGroupSelected = (widgets) => {\n    for (const widget of widgets) {\n      if (!selectingWidgets.includes(widget['Id'])) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  const renderContent = (widget) => {\n    const fieldName = widget['fieldName'];\n    const icon = widget.childWidgets ? 'ic-folder-open' : getDataWithKey(mapAnnotationToKey(widget)).icon;\n    const widgetId = widget['Id'];\n    const childWidgets = widget.childWidgets || null;\n    const isActive = widgetId ? selectingWidgets.includes(widgetId) : isRadioGroupSelected(childWidgets);\n    const childrenWidgetIds = childWidgets ? childWidgets.map((child) => child['Id']) : [];\n    return (\n      <IndexPanelContent\n        key={widget['Id']}\n        ref={(element) => generateRefList(element, widgetId, childrenWidgetIds)}\n        isMultiSelectionMode={isMultiSelectionMode}\n        fieldName={fieldName}\n        icon={icon}\n        widgetId={widgetId}\n        setSelected={handleSetSelected}\n        isActive={isActive}\n        handleDeletion={handleDeletion}\n        childWidgets={childWidgets}\n        selectingWidgets={selectingWidgets}\n      />\n    );\n  };\n\n  const closeIndexPanel = () => {\n    dispatch(actions.closeElement(DataElements.INDEX_PANEL));\n  };\n\n  const renderMobileCloseButton = () => {\n    return (\n      <div className=\"close-container\">\n        <button className=\"close-icon-container\" onClick={closeIndexPanel}>\n          <Icon glyph=\"ic_close_black_24px\" className=\"close-icon\" />\n        </button>\n      </div>\n    );\n  };\n\n  return !showIndexPanel ?\n    null :\n    (\n      <div className='index-panel-container'>\n        <DataElementWrapper\n          className='index-panel-header'\n          dataElement='index-panel-header'\n        >\n          {!isInDesktopOnlyMode && isMobile && renderMobileCloseButton()}\n          <DataElementWrapper\n            className=\"title-container\"\n            dataElement=\"index-panel-title-container\"\n          >\n            <DataElementWrapper\n              className=\"fields-counter\"\n              dataElement=\"index-panel-fields-counter\"\n            >\n              <span>{t('formField.indexPanel.formFieldList')}</span> ({widgets.length})\n\n            </DataElementWrapper>\n            <DataElementWrapper\n              dataElement=\"form-field-multi-select\"\n            >\n              {!isMultiSelectionMode &&\n                <Button\n                  className=\"field-control-button\"\n                  label={t('action.edit')}\n                  disabled={widgets.length === 0}\n                  onClick={handleEditButtonClick}\n                />\n              }\n              {isMultiSelectionMode &&\n                <Button\n                  className=\"field-control-button\"\n                  label={t('option.bookmarkOutlineControls.done')}\n                  onClick={handleDoneButtonClick}\n                />\n              }\n            </DataElementWrapper>\n          </DataElementWrapper>\n        </DataElementWrapper>\n\n        <DataElementWrapper\n          className=\"IndexPanel\"\n          dataElement=\"index-panel\"\n          ref={containerRef}\n        >\n          {widgets.length ? showFields() : NoFields}\n        </DataElementWrapper>\n        {isMultiSelectionMode && <DataElementWrapper\n          className=\"index-panel-footer\"\n          dataElement=\"index-panel-footer\"\n        >\n          <Button\n            className=\"multi-selection-button\"\n            img=\"icon-delete-line\"\n            disabled={selectingWidgets.length === 0}\n            onClick={() => handleDeletion()}\n          />\n        </DataElementWrapper>}\n      </div>\n    );\n};\n\nIndexPanel.propTypes = propTypes;\n\nexport default IndexPanel;", "import React,\n{\n  useEffect,\n  useState\n} from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport './IndexPanel.scss';\nimport core from 'core';\nimport DataElements from 'constants/dataElement';\nimport IndexPanel from './IndexPanel';\nimport PropTypes from 'prop-types';\nimport selectors from 'selectors';\n\nconst propTypes = {\n  parentDataElement: PropTypes.string,\n  dataElement: PropTypes.string.isRequired,\n};\n\nfunction IndexPanelContainer(props) {\n  const { parentDataElement = undefined, dataElement } = props;\n  const [\n    isOpen,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, parentDataElement || dataElement || DataElements.INDEX_PANEL),\n    ],\n    shallowEqual,\n  );\n  const [widgets, setWidgets] = useState([]);\n\n  useEffect(() => {\n    const annotationChangedListener = () => {\n      const defaultWidgets = core.getAnnotationsList().filter((annot) => annot instanceof window.Core.Annotations.WidgetAnnotation);\n      setWidgets(defaultWidgets);\n    };\n\n    core.addEventListener('annotationChanged', annotationChangedListener);\n    return () => {\n      core.removeEventListener('annotationChanged', annotationChangedListener);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (isOpen) {\n      const defaultWidgets = core.getAnnotationsList().filter((annot) => annot instanceof window.Core.Annotations.WidgetAnnotation);\n      setWidgets(defaultWidgets);\n    } else {\n      setWidgets([]);\n    }\n  }, [isOpen]);\n\n  const passProps = {\n    widgets,\n  };\n\n  return (\n    <IndexPanel {...props} {...passProps} />\n  );\n}\n\nIndexPanelContainer.propTypes = propTypes;\n\nexport default IndexPanelContainer;\n", "import IndexPanelContainer from './IndexPanelContainer';\n\nexport default IndexPanelContainer;\n"], "sourceRoot": ""}