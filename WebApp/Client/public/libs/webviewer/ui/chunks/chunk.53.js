(window.webpackJsonp=window.webpackJsonp||[]).push([[53],{1951:function(e,t,n){var r=n(32),o=n(1952);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,a);e.exports=o.locals||{}},1952:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".sheet-tab{cursor:pointer;border-bottom:none;border-top-right-radius:4px;border-top-left-radius:4px;min-width:170px;height:40px;position:relative;border-right:1px solid transparent}.sheet-tab:hover{background:var(--faded-component-background)}.sheet-tab.no-left-border .tab-body{border-right:1px solid transparent}.sheet-tab .tab-body{height:24px;display:flex;padding:0 8px;width:100%;position:absolute;top:50%;transform:translateY(-50%);border-right:1px solid var(--border)}.sheet-tab .tab-body.input-mode{padding-left:5px;padding-right:6px}.sheet-tab .tab-body .sheet-label{width:100%;text-align:left;justify-content:left;height:24px}.sheet-tab .tab-body input.input-error{border-color:red}.sheet-tab.active{border:1px solid var(--border);border-bottom:none;background:var(--gray-0)}.sheet-tab.active .tab-body{border-right:1px solid transparent}.sheet-tab .sheet-options .ToggleElementButton button{width:24px;height:24px;min-width:24px}.sheet-tab .sheet-options .ToggleElementButton button .Icon{width:16px;height:14px}.sheet-tab .sheet-options .more-options-icon{height:24px;width:24px}.sheet-tab .sheet-options .more-options-icon .Icon{width:16px;height:16px}",""])},1953:function(e,t,n){var r=n(32),o=n(1954);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,a);e.exports=o.locals||{}},1954:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".Flyout[\\:has\\(.SheetOptionsFlyout\\)]{z-index:130}.Flyout:has(.SheetOptionsFlyout){z-index:130}.SpreadsheetSwitcher{padding:0}.GenericFileTab{display:flex;padding-left:4px;padding-top:4px}.GenericFileTab .dropdown-menu{border:1px solid var(--border);margin-top:3px;margin-left:5px}.GenericFileTab .dropdown-menu .Icon{position:absolute}.GenericFileTab .add-sheet-tab{margin-top:3px;margin-left:5px}.GenericFileTab .add-sheet-tab .Icon{width:14px;height:14px}",""])},1984:function(e,t,n){"use strict";n.r(t);n(15),n(19),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(16),n(20),n(18),n(26),n(27),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46);var r=n(0),o=n.n(r),a=n(1),i=(n(41),n(104),n(96),n(110),n(6)),l=n(429),c=n(526),u=n(48),s=n(98),d=n(43),f=n(5),b=n(149),p=n(3),h=n(2),m=n(4),y=n.n(m),g=function(e){var t=e.id,n=void 0===t?"":t,o=e.additionalTabs,a=e.tabsForReference,l=e.onClick,c=e.activeItem,u=Object(i.d)(),s=Object(i.e)((function(e){return p.a.getFlyout(e,n)}));return Object(r.useLayoutEffect)((function(){var e={dataElement:n,className:"AdditionalTabsFlyout",items:o.map((function(e){var t=e.name;return{label:t,title:t,option:t,disabled:e.disabled,isActive:t===c,dataElement:Symbol(t).toString(),onClick:function(){return l(t,e.sheetIndex)}}}))};u(s?h.a.updateFlyout(e.dataElement,e):h.a.addFlyout(e))}),[a,c]),null};g.propTypes={id:y.a.string,additionalTabs:y.a.arrayOf(y.a.shape({name:y.a.string,sheetIndex:y.a.number,disabled:y.a.bool})),tabsForReference:y.a.array,onClick:y.a.func,activeItem:y.a.string};var v=g,O=(n(153),n(107),n(17)),S=n.n(O);n(36);function w(e){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function j(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){x(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function x(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==w(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==w(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===w(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var T=function(e,t){return{label:"action.".concat(e.toLowerCase()),title:"action.".concat(e.toLowerCase()),option:e,dataElement:t,disabled:!0}},A=[T("Rename","sheetTabRenameOption"),T("Delete","sheetTabDeleteOption")],C=function(e){var t=e.sheetId,n=e.handleClick,o=e.sheetCount,a=Object(i.d)(),l="".concat(f.a.SHEET_TAB_OPTIONS_FLYOUT,"-").concat(t),c=Object(i.e)((function(e){return p.a.getFlyout(e,l)}));return Object(r.useLayoutEffect)((function(){var e={dataElement:l,className:"TabOptionsFlyout",items:A.map((function(e){var t="Delete"===e.option&&1===o||e.disabled;return j(j({},e),{},{onClick:function(){return n(e.option)},disabled:t})}))};a(c?h.a.updateFlyout(e.dataElement,e):h.a.addFlyout(e))}),[o]),null};C.propTypes={sheetId:y.a.string,handleClick:y.a.func,sheetCount:y.a.number};var I=C;function k(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var M=function(e){var t=e.handleClick,n=void 0===t?function(){}:t,r=e.id,a=e.onToggle,i=e.label,c=e.disabled,u=e.sheetCount,d=k(Object(l.a)(),1)[0];return o.a.createElement(o.a.Fragment,null,o.a.createElement(s.a,{dataElement:"options-".concat(r),title:d("option.searchPanel.moreOptions"),img:"icon-tools-more-vertical",onToggle:function(e){e&&a(i)},disabled:c,toggleElement:"".concat(f.a.SHEET_TAB_OPTIONS_FLYOUT,"-").concat(r)}),o.a.createElement(I,{sheetId:r,sheetCount:u,handleClick:function(e){return n(r,i,e)}}))};M.propTypes={id:y.a.string,label:y.a.string,onToggle:y.a.func,handleClick:y.a.func,disabled:y.a.bool,sheetCount:y.a.number};var P=M;n(1951);function N(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return D(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return D(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var F={sheet:y.a.any.isRequired,sheetCount:y.a.number,activeSheetLabel:y.a.string.isRequired,setLabelBeingEdited:y.a.func.isRequired,setActiveSheet:y.a.func.isRequired,onClick:y.a.func.isRequired,isEditMode:y.a.bool.isRequired,validateName:y.a.func,noRightBorder:y.a.bool,isReadOnlyMode:y.a.bool},L=function(e){var t=e.sheet,n=e.activeSheetLabel,a=e.onClick,c=e.sheetCount,s=e.setLabelBeingEdited,d=e.setActiveSheet,f=e.isEditMode,b=e.noRightBorder,p=e.validateName,m=e.isReadOnlyMode,y=t.sheetIndex,g=t.name,v=t.disabled,O=g===n,w=N(Object(r.useState)(""),2),E=w[0],j=w[1],x=N(Object(r.useState)(!1),2),T=x[0],A=x[1],C=Object(l.a)().t,I=Object(r.useRef)(),k=Object(i.d)();Object(r.useEffect)((function(){E&&I.current&&I.current.focus()}),[E]);var R=function(){if(T)return t={message:C("".concat(e=""===E?"warning.sheetTabRenameIssueTwo":"warning.sheetTabRenameIssueOne",".message")),title:C("".concat(e,".title")),confirmBtnText:C("action.ok"),onConfirm:function(){return I.current.focus()}},void k(h.a.showWarningMessage(t));var e,t;s(null),A(!1),j("")},M=null;return M=f?o.a.createElement("input",{type:"text",className:S()({"input-error":T}),ref:I,value:E,onChange:function(e){return t=g,n=e.target.value,r=p(t,n),o=""===n.trim(),A(r||o),void j(n);var t,n,r,o},onBlur:R,onKeyDown:function(e){"Enter"!==e.key||T||R()}}):o.a.createElement(o.a.Fragment,null,o.a.createElement(u.a,{role:"tab",ariaControls:"document-container",ariaSelected:O,ariaLabel:g,className:"sheet-label",onClick:function(e){return a(e,g,y)},title:g,label:g,useI18String:!1,disabled:v}),o.a.createElement("div",{className:"sheet-options"},o.a.createElement(P,{id:g.replace(/\s+/g,"-").toLowerCase(),label:g,sheetCount:c,onToggle:function(e){return t=e,n=y,s(null),A(!1),void d(t,n);var t,n},handleClick:function(e,t,n){if("Rename"===n)j(o=t),A(!1),s(o);else if("Delete"===n){var r={message:C("warning.sheetTabDeleteMessage.message"),title:C("warning.sheetTabDeleteMessage.title"),confirmBtnText:C("action.ok"),secondaryBtnText:C("action.cancel"),onConfirm:function(){},onSecondary:function(){}};k(h.a.showWarningMessage(r))}var o},disabled:m}))),o.a.createElement("div",{className:S()({active:O,"no-left-border":b,disabled:v},"sheet-tab")},o.a.createElement("div",{className:S()({"input-mode":f},"tab-body")},M))};L.propTypes=F;var B=o.a.memo(L);n(1953);function _(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return q(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return q(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var H=function(e){var t,n=e.tabs,a=void 0===n?[]:n,h=e.activeSheetIndex,m=void 0===h?0:h,y=e.setActiveSheet,g=void 0===y?function(){}:y,O=(null===(t=a[m])||void 0===t?void 0:t.name)||"",S=Object(c.a)().width,w=Object(r.useMemo)((function(){return Math.floor((S-80)/170)}),[S]),E=Object(l.a)().t,j=_(Object(r.useState)(""),2),x=j[0],T=j[1],A=Object(i.e)(p.a.getSpreadsheetEditorEditMode)===b.d.VIEW_ONLY,C=function(e,t){T(null),g(e,t)},I=function(e,t,n){e.preventDefault(),e.stopPropagation(),C(t,n)},k=_(Object(r.useMemo)((function(){return[a.slice(0,w),a.slice(w)]}),[a,w]),2),R=k[0],M=k[1],P=R.map((function(e){return o.a.createElement(B,{key:e.sheetIndex,sheet:e,sheetCount:a.length,activeSheetLabel:O,onClick:I,isEditMode:x===e.name,setLabelBeingEdited:T,setActiveSheet:g,noRightBorder:a[e.sheetIndex+1]&&a[e.sheetIndex+1].name===O,validateName:function(){},isReadOnlyMode:A})})),N=Object(r.useMemo)((function(){return M.some((function(e){return e.name===O}))}),[M,O]);return o.a.createElement("div",{className:"SpreadsheetSwitcher ModularHeader BottomHeader stroke start"},o.a.createElement("div",{className:"GenericFileTab",role:"tablist"},P,(null==M?void 0:M.length)>0?o.a.createElement(s.a,{className:"dropdown-menu tab-dropdown-button",dataElement:"tabTrigger",title:E("message.showMore"),toggleElement:f.a.ADDITIONAL_SPREADSHEET_TABS_MENU,label:M.length.toString()},N&&o.a.createElement(d.a,{glyph:"icon-active-indicator"})):null,o.a.createElement(u.a,{className:"add-sheet-tab",title:"action.addSheet",img:"icon-menu-add",onClick:function(){},dataElement:"addTabButton",tabIndex:-1,label:"",ariaLabel:E("action.addSheet"),disabled:A}),(null==M?void 0:M.length)>0&&o.a.createElement(v,{id:f.a.ADDITIONAL_SPREADSHEET_TABS_MENU,additionalTabs:M,tabsForReference:a,onClick:C,activeItem:O})))};H.propTypes={tabs:y.a.arrayOf(y.a.shape({name:y.a.string,sheetIndex:y.a.number,disabled:y.a.bool})),activeSheetIndex:y.a.number,setActiveSheet:y.a.func};var U=H,W=n(183);function G(e){return(G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function V(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?V(Object(n),!0).forEach((function(t){$(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):V(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function $(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==G(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==G(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===G(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function z(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return J(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return J(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function K(e){var t=z(Object(r.useState)(null),2),n=t[0],i=t[1],l=z(Object(r.useState)([]),2),c=l[0],u=l[1],s=z(Object(r.useState)(0),2),d=s[0],f=s[1];Object(r.useEffect)((function(){var e=a.a.getDocumentViewer(),t=function(t){var n=e.getDocument().getSpreadsheetEditorDocument().getWorkbook();i(n),u(function(e){for(var t=e.sheetCount,n=[],r=0;r<t;r++){var o=e.getSheetAt(r);n.push({name:o.name,sheetIndex:r})}return n}(n)),f(t.getSheetIndex())};return a.a.addEventListener("activeSheetChanged",t),function(){a.a.removeEventListener("activeSheetChanged",t)}}),[]);var b=Object(r.useCallback)((function(){u([]),f(0)}),[]);Object(W.a)(b);var p=Y(Y({},e),{},{tabs:c,activeSheetIndex:d,setActiveSheet:function(e,t){n.getSheetAt(t)&&(n.setActiveSheet(t),f(t))}});return o.a.createElement(U,p)}K.propTypes={};var Q=K;t.default=Q}}]);
//# sourceMappingURL=chunk.53.js.map