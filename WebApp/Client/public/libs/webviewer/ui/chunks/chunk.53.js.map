{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/SheetTab/SheetTab.scss?ba6c", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/SheetTab/SheetTab.scss", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/SpreadsheetSwitcher.scss?97bc", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/SpreadsheetSwitcher.scss", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/AdditionalTabsFlyout.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/TabOptionsFlyout.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/TabOptionsButton.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/SheetTab/SheetTab.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/SpreadsheetSwitcher.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/SpreadsheetSwitcherContainer.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetSwitcher/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "AdditionalTabsFlyout", "props", "id", "additionalTabs", "tabsForReference", "onClick", "activeItem", "dispatch", "useDispatch", "currentFlyout", "useSelector", "state", "selectors", "getFlyout", "useLayoutEffect", "noteStateFlyout", "dataElement", "className", "items", "map", "item", "tabLabel", "name", "label", "title", "option", "disabled", "isActive", "Symbol", "toString", "sheetIndex", "actions", "updateFlyout", "addFlyout", "propTypes", "PropTypes", "string", "arrayOf", "shape", "number", "bool", "array", "func", "createFlyoutItem", "toLowerCase", "sheetTabOptionsFlyoutItems", "TabOptionsFlyout", "sheetId", "handleClick", "sheetCount", "flyoutSelector", "DataElements", "SHEET_TAB_OPTIONS_FLYOUT", "isDisabled", "TabOptions", "onToggle", "t", "useTranslation", "ToggleElementButton", "img", "toggleElement", "sheet", "any", "isRequired", "activeSheetLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gEdited", "setActiveSheet", "isEditMode", "validateName", "noRightBorder", "isReadOnlyMode", "SheetTab", "useState", "inputValue", "setInputValue", "isInputError", "setIsInputError", "inputRef", "useRef", "useEffect", "current", "focus", "onInputBlur", "warning", "message", "<PERSON><PERSON><PERSON>", "confirmBtnText", "onConfirm", "showWarningMessage", "component", "type", "classNames", "ref", "value", "onChange", "e", "current<PERSON><PERSON><PERSON>", "new<PERSON>abel", "target", "isDuplicate", "isEmpty", "trim", "onBlur", "onKeyDown", "key", "<PERSON><PERSON>", "role", "ariaControls", "ariaSelected", "aria<PERSON><PERSON><PERSON>", "useI18String", "replace", "newActiveLabel", "tabIndex", "sheetName", "text", "secondaryBtnText", "onSecondary", "React", "memo", "SpreadsheetSwitcher", "tabs", "activeSheetIndex", "width", "useWindowDimensions", "breakpoint", "useMemo", "Math", "floor", "labelBeingEdited", "getSpreadsheetEditorEditMode", "SpreadsheetEditorEditMode", "VIEW_ONLY", "updateActiveTab", "handleTabNameClick", "index", "preventDefault", "stopPropagation", "slice", "slicedTabs", "flyoutTabs", "tabElements", "isActiveSheetInFlyout", "some", "ADDITIONAL_SPREADSHEET_TABS_MENU", "Icon", "glyph", "SpreadsheetSwitcherContainer", "workbook", "setWorkbook", "sheets", "setSheets", "setActiveSheetIndex", "documentViewer", "core", "getDocumentViewer", "onSpreadsheetEditorSheetChanged", "event", "workbookInstance", "getDocument", "getSpreadsheetEditorDocument", "getWorkbook", "sheetArray", "getSheetAt", "getSheetsFromWorkbook", "getSheetIndex", "addEventListener", "removeEventListener", "handleDocumentUnloaded", "useCallback", "useOnDocumentUnloaded", "ownProps"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAwE,IAK3FR,KAAK,CAACnB,EAAOC,EAAI,gpCAAipC,M,qBCL1qC,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,gdAAid,M,uUCCpe4B,EAAuB,SAACC,GAC5B,MAMIA,EALFC,UAAE,IAAG,KAAE,EACPC,EAIEF,EAJFE,eACAC,EAGEH,EAHFG,iBACAC,EAEEJ,EAFFI,QACAC,EACEL,EADFK,WAEIC,EAAWC,cAEXC,EAAgBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,UAAUF,EAAOT,MA2BxE,OAzBAY,2BAAgB,WACd,IAAMC,EAAkB,CACtBC,YAAad,EACbe,UAAW,uBACXC,MAAOf,EAAegB,KAAI,SAACC,GACzB,IAAMC,EAAWD,EAAKE,KAEtB,MAAO,CACLC,MAAOF,EACPG,MAAOH,EACPI,OAAQJ,EACRK,SAAUN,EAAKM,SACfC,SAAUN,IAAaf,EACvBU,YAAaY,OAAOP,GAAUQ,WAC9BxB,QAAS,kBAAMA,EAAQgB,EAAUD,EAAKU,kBAO1CvB,EAHGE,EAGMsB,IAAQC,aAAajB,EAAgBC,YAAaD,GAFlDgB,IAAQE,UAAUlB,MAI5B,CAACX,EAAkBE,IAEf,MAGTN,EAAqBkC,UAAY,CAC/BhC,GAAIiC,IAAUC,OACdjC,eAAgBgC,IAAUE,QAAQF,IAAUG,MAAM,CAChDhB,KAAMa,IAAUC,OAChBN,WAAYK,IAAUI,OACtBb,SAAUS,IAAUK,QAEtBpC,iBAAkB+B,IAAUM,MAC5BpC,QAAS8B,IAAUO,KACnBpC,WAAY6B,IAAUC,QAGTpC,Q,0wCCnDf,IAAM2C,EAAmB,SAAClB,EAAQT,GAAW,MAAM,CACjDO,MAAO,UAAF,OAAYE,EAAOmB,eACxBpB,MAAO,UAAF,OAAYC,EAAOmB,eACxBnB,SACAT,cACAU,UAAU,IAGCmB,EAA6B,CACxCF,EAAiB,SAAU,wBAC3BA,EAAiB,SAAU,yBAGvBG,EAAmB,SAAC7C,GACxB,IACE8C,EAGE9C,EAHF8C,QACAC,EAEE/C,EAFF+C,YACAC,EACEhD,EADFgD,WAGI1C,EAAWC,cACX0C,EAAiB,GAAH,OAAMC,IAAaC,yBAAwB,YAAIL,GAC7DtC,EAAgBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,UAAUF,EAAOuC,MA0BxE,OAFApC,2BAtBe,WACb,IAAMC,EAAkB,CACtBC,YAAakC,EACbjC,UAAW,mBACXC,MAAO2B,EAA2B1B,KAAI,SAACC,GACrC,IAAIiC,EAA8B,WAAhBjC,EAAKK,QAAsC,IAAfwB,GAAqB7B,EAAKM,SAExE,OAAO,EAAP,KACKN,GAAI,IACPf,QAAS,kBAAM2C,EAAY5B,EAAKK,SAChCC,SAAU2B,QAQd9C,EAHGE,EAGMsB,IAAQC,aAAajB,EAAgBC,YAAaD,GAFlDgB,IAAQE,UAAUlB,MAMP,CAACkC,IAElB,MAGTH,EAAiBZ,UAAa,CAC5Ba,QAASZ,IAAUC,OACnBY,YAAab,IAAUO,KACvBO,WAAYd,IAAUI,QAGTO,Q,+hCCxDf,IAAMQ,EAAa,SAACrD,GAClB,MAOIA,EANF+C,mBAAW,IAAG,eAAS,EACvB9C,EAKED,EALFC,GACAqD,EAIEtD,EAJFsD,SACAhC,EAGEtB,EAHFsB,MACAG,EAEEzB,EAFFyB,SACAuB,EACEhD,EADFgD,WAGKO,EAAqB,EAAhBC,cAAgB,GAApB,GAGR,OACE,oCACE,kBAACC,EAAA,EAAmB,CAClB1C,YAAW,kBAAad,GACxBsB,MAAOgC,EAAE,kCACTG,IAPO,2BAQPJ,SAAU,SAAC5B,GACLA,GACF4B,EAAShC,IAGbG,SAAUA,EACVkC,cAAa,UAAKT,IAAaC,yBAAwB,YAAIlD,KAE7D,kBAAC,EAAkB,CACjB6C,QAAS7C,EACT+C,WAAYA,EACZD,YAAa,SAACvB,GAAM,OAAKuB,EAAY9C,EAAIqB,EAAOE,QAMxD6B,EAAWpB,UAAY,CACrBhC,GAAIiC,IAAUC,OACdb,MAAOY,IAAUC,OACjBmB,SAAUpB,IAAUO,KACpBM,YAAab,IAAUO,KACvBhB,SAAUS,IAAUK,KACpBS,WAAYd,IAAUI,QAGTe,Q,uiCC3Cf,IAAMpB,EAAY,CAChB2B,MAAO1B,IAAU2B,IAAIC,WACrBd,WAAYd,IAAUI,OACtByB,iBAAkB7B,IAAUC,OAAO2B,WACnCE,oBAAqB9B,IAAUO,KAAKqB,WACpCG,eAAgB/B,IAAUO,KAAKqB,WAC/B1D,QAAS8B,IAAUO,KAAKqB,WACxBI,WAAYhC,IAAUK,KAAKuB,WAC3BK,aAAcjC,IAAUO,KACxB2B,cAAelC,IAAUK,KACzB8B,eAAgBnC,IAAUK,MAGtB+B,EAAW,SAAH,GAWR,IAVJV,EAAK,EAALA,MACAG,EAAgB,EAAhBA,iBACA3D,EAAO,EAAPA,QACA4C,EAAU,EAAVA,WACAgB,EAAmB,EAAnBA,oBACAC,EAAc,EAAdA,eACAC,EAAU,EAAVA,WACAE,EAAa,EAAbA,cACAD,EAAY,EAAZA,aACAE,EAAc,EAAdA,eAEQxC,EAAsC+B,EAAtC/B,WAAkBP,EAAoBsC,EAA1BvC,KAAaI,EAAamC,EAAbnC,SAE3BC,EAAWJ,IAAUyC,EAEqB,IAAZQ,mBAAS,IAAG,GAAzCC,EAAU,KAAEC,EAAa,KACuB,IAAfF,oBAAS,GAAM,GAAhDG,EAAY,KAAEC,EAAe,KAC5BpB,EAAMC,cAAND,EACFqB,EAAWC,mBACXvE,EAAWC,cAEjBuE,qBAAU,WACJN,GAAcI,EAASG,SACzBH,EAASG,QAAQC,UAElB,CAACR,IAEJ,IAgCMS,EAAc,WAClB,GAAIP,EAEF,OAZIQ,EAAU,CACdC,QAAS5B,EAAE,GAAD,OAFN6B,EADyB,KAAfZ,EACa,iCAAmC,iCAEtC,aACxBjD,MAAOgC,EAAE,GAAD,OAAI6B,EAAU,WACtBC,eAAgB9B,EAAE,aAClB+B,UAAW,kBAAMV,EAASG,QAAQC,eAEpC1E,EAASwB,IAAQyD,mBAAmBL,IATZ,IAElBE,EACAF,EAcNlB,EAAoB,MACpBW,GAAgB,GAChBF,EAAc,KA6BZe,EAAY,KAwChB,OAtCEA,EADEtB,EAEA,2BAAOuB,KAAK,OACVzE,UAAW0E,IAAW,CAAE,cAAehB,IACvCiB,IAAKf,EACLgB,MAAOpB,EACPqB,SAAU,SAACC,GAAC,OA3EKC,EA2EczE,EA3EA0E,EA2EOF,EAAEG,OAAOL,MA1E7CM,EAAc/B,EAAa4B,EAAcC,GACzCG,EAA8B,KAApBH,EAASI,OAEzBzB,EAAgBuB,GAAeC,QAC/B1B,EAAcuB,GALM,IAACD,EAAcC,EAC7BE,EACAC,GA0EFE,OAAQpB,EACRqB,UAnCY,SAACR,GACH,UAAVA,EAAES,KAAoB7B,GACxBO,OAsCA,oCACE,kBAACuB,EAAA,EAAM,CACLC,KAAK,MACLC,aAAc,qBACdC,aAAcjF,EACdkF,UAAWtF,EACXN,UAAW,cACXZ,QAAS,SAAC0F,GAAC,OAAK1F,EAAQ0F,EAAGxE,EAAOO,IAClCN,MAAOD,EACPA,MAAOA,EACPuF,cAAc,EACdpF,SAAUA,IAEZ,yBAAKT,UAAU,iBACb,kBAAC,EAAU,CAACf,GAAIqB,EAAMwF,QAAQ,OAAQ,KAAKnE,cACzCrB,MAAOA,EACP0B,WAAYA,EACZM,SAAU,SAACjC,GAAI,OA3FA0F,EA2FqB1F,EA3FL2F,EA2FWnF,EA1FlDmC,EAAoB,MACpBW,GAAgB,QAChBV,EAAe8C,EAAgBC,GAHT,IAACD,EAAgBC,GA4F/BjE,YApDuB,SAAC9C,EAAIgH,EAAWzF,GAC/C,GAAe,WAAXA,EAlCJiD,EAD6ByC,EAoCLD,GAlCxBtC,GAAgB,GAChBX,EAAoBkD,QAkCb,GAAe,WAAX1F,EAAqB,CAC9B,IAIM0D,EAAU,CACdC,QALc5B,EAAE,yCAMhBhC,MALYgC,EAAE,uCAMd8B,eALqB9B,EAAE,aAMvB4D,iBALuB5D,EAAE,iBAMzB+B,UAAW,aACX8B,YAAa,cAEf9G,EAASwB,IAAQyD,mBAAmBL,IAlDV,IAACgC,GAuFrBzF,SAAU4C,MAOZ,yBAAKrD,UAAW0E,IAAW,CACjC,OAAUhE,EACV,iBAAkB0C,EAClB,SAAY3C,GACX,cAED,yBAAKT,UAAW0E,IAAW,CAAE,aAAcxB,GAAa,aACrDsB,KAMPlB,EAASrC,UAAYA,EAENoF,UAAMC,KAAKhD,G,uiCC9J1B,IAAMiD,EAAsB,SAACvH,GAAU,MACrC,EAAuEA,EAA/DwH,YAAI,IAAG,KAAE,IAAsDxH,EAApDyH,wBAAgB,IAAG,IAAC,IAAgCzH,EAA9BiE,sBAAc,IAAG,eAAQ,EAC5DF,GAAyC,QAAtB,EAAAyD,EAAKC,UAAiB,aAAtB,EAAwBpG,OAAQ,GACjDqG,EAAUC,cAAVD,MACFE,EAAaC,mBAAQ,kBAAMC,KAAKC,OAAOL,EAAQ,IAAM,OAAM,CAACA,IAC1DnE,EAAMC,cAAND,EACoD,IAAZgB,mBAAS,IAAG,GAArDyD,EAAgB,KAAEhE,EAAmB,KAGtCK,EAD4B5D,YAAYE,IAAUsH,gCACHC,IAA0BC,UAEzEC,EAAkB,SAACrB,EAAgBC,GACvChD,EAAoB,MACpBC,EAAe8C,EAAgBC,IAG3BqB,EAAqB,SAACvC,EAAGxE,EAAOgH,GACpCxC,EAAEyC,iBACFzC,EAAE0C,kBACFJ,EAAgB9G,EAAOgH,IAQH,IAFWT,mBAAQ,WACvC,MAAO,CAACL,EAAKiB,MAAM,EAAGb,GAAaJ,EAAKiB,MAAMb,MAC7C,CAACJ,EAAMI,IAAY,GAFfc,EAAU,KAAEC,EAAU,KAIvBC,EAAcF,EAAWxH,KAAI,SAACC,GAAI,OACtC,kBAAC,EAAQ,CACPoF,IAAKpF,EAAKU,WACV+B,MAAOzC,EACP6B,WAAYwE,EAAK3I,OACjBkF,iBAAkBA,EAClB3D,QAASiI,EACTnE,WAAY8D,IAAqB7G,EAAKE,KACtC2C,oBAAqBA,EACrBC,eAAgBA,EAChBG,cAAeoD,EAAKrG,EAAKU,WAAa,IAAM2F,EAAKrG,EAAKU,WAAa,GAAGR,OAAS0C,EAC/EI,aAAc,aACdE,eAAgBA,OAIdwE,EAAwBhB,mBAAQ,WACpC,OAAOc,EAAWG,MAAK,SAAC3H,GAAI,OAAKA,EAAKE,OAAS0C,OAC9C,CAAC4E,EAAY5E,IAEhB,OACE,yBAAK/C,UAAU,+DACb,yBAAKA,UAAW,iBAAkByF,KAAK,WACpCmC,GAEED,aAAU,EAAVA,EAAY9J,QAAS,EAElB,kBAAC4E,EAAA,EAAmB,CAClBzC,UAAU,oCACVD,YAAY,aACZQ,MAAOgC,EAAE,oBACTI,cAAeT,IAAa6F,iCAC5BzH,MAAOqH,EAAW9J,OAAO+C,YAEvBiH,GAA2B,kBAACG,EAAA,EAAI,CAACC,MAAM,2BAEzC,KAER,kBAACzC,EAAA,EAAM,CACLxF,UAAU,gBACVO,MAAM,kBACNmC,IAAI,gBACJtD,QAjDqB,aAkDrBW,YAAa,eACbiG,UAAW,EACX1F,MAAO,GACPsF,UAAWrD,EAAE,mBACb9B,SAAU4C,KAEXsE,aAAU,EAAVA,EAAY9J,QAAS,GAElB,kBAAC,EAAoB,CACnBoB,GAAIiD,IAAa6F,iCACjB7I,eAAgByI,EAChBxI,iBAAkBqH,EAClBpH,QAASgI,EACT/H,WAAY0D,OAS1BwD,EAAoBtF,UAAY,CAC9BuF,KAAMtF,IAAUE,QAAQF,IAAUG,MAAM,CACtChB,KAAMa,IAAUC,OAChBN,WAAYK,IAAUI,OACtBb,SAAUS,IAAUK,QAEtBkF,iBAAkBvF,IAAUI,OAC5B2B,eAAgB/B,IAAUO,MAGb8E,Q,2wECnHf,SAAS2B,EAA6BlJ,GACpC,IAA8C,IAAduE,mBAAS,MAAK,GAAvC4E,EAAQ,KAAEC,EAAW,KACY,IAAZ7E,mBAAS,IAAG,GAAjC8E,EAAM,KAAEC,EAAS,KACmC,IAAX/E,mBAAS,GAAE,GAApDkD,EAAgB,KAAE8B,EAAmB,KAY5CzE,qBAAU,WACR,IAAM0E,EAAiBC,IAAKC,oBAEtBC,EAAkC,SAACC,GACvC,IACMC,EADML,EAAeM,cAAcC,+BACZC,cAE7BZ,EAAYS,GACZP,EAlB0B,SAACO,GAG7B,IAFA,IAAI7G,EAAa6G,EAAiB7G,WAC5BiH,EAAa,GACV9L,EAAI,EAAGA,EAAI6E,EAAY7E,IAAK,CACnC,IAAMyF,EAAQiG,EAAiBK,WAAW/L,GAC1C8L,EAAW5K,KAAK,CAAEgC,KAAMuC,EAAMvC,KAAMQ,WAAY1D,IAElD,OAAO8L,EAWKE,CAAsBN,IAChCN,EAAoBK,EAAMQ,kBAI5B,OADAX,IAAKY,iBAAiB,qBAAsBV,GACrC,WACLF,IAAKa,oBAAoB,qBAAsBX,MAEhD,IAEH,IAAMY,EAAyBC,uBAAY,WACzClB,EAAU,IACVC,EAAoB,KACnB,IACHkB,YAAsBF,GAEtB,IAAMG,EAAW,OACZ1K,GAAK,IACRwH,KAAM6B,EACN5B,mBACAxD,eAAgB,SAAC5C,EAAMiH,GACjBa,EAASe,WAAW5B,KACtBa,EAASlF,eAAeqE,GACxBiB,EAAoBjB,OAK1B,OAAQ,kBAAC,EAAwBoC,GAGnCxB,EAA6BjH,UAAY,GAE1BiH,QC3DAA", "file": "chunks/chunk.53.js", "sourcesContent": ["var api = require(\"!../../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../../node_modules/css-loader/index.js!../../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../../node_modules/sass-loader/dist/cjs.js!./SheetTab.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".sheet-tab{cursor:pointer;border-bottom:none;border-top-right-radius:4px;border-top-left-radius:4px;min-width:170px;height:40px;position:relative;border-right:1px solid transparent}.sheet-tab:hover{background:var(--faded-component-background)}.sheet-tab.no-left-border .tab-body{border-right:1px solid transparent}.sheet-tab .tab-body{height:24px;display:flex;padding:0 8px;width:100%;position:absolute;top:50%;transform:translateY(-50%);border-right:1px solid var(--border)}.sheet-tab .tab-body.input-mode{padding-left:5px;padding-right:6px}.sheet-tab .tab-body .sheet-label{width:100%;text-align:left;justify-content:left;height:24px}.sheet-tab .tab-body input.input-error{border-color:red}.sheet-tab.active{border:1px solid var(--border);border-bottom:none;background:var(--gray-0)}.sheet-tab.active .tab-body{border-right:1px solid transparent}.sheet-tab .sheet-options .ToggleElementButton button{width:24px;height:24px;min-width:24px}.sheet-tab .sheet-options .ToggleElementButton button .Icon{width:16px;height:14px}.sheet-tab .sheet-options .more-options-icon{height:24px;width:24px}.sheet-tab .sheet-options .more-options-icon .Icon{width:16px;height:16px}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./SpreadsheetSwitcher.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".Flyout[\\\\:has\\\\(.SheetOptionsFlyout\\\\)]{z-index:130}.Flyout:has(.SheetOptionsFlyout){z-index:130}.SpreadsheetSwitcher{padding:0}.GenericFileTab{display:flex;padding-left:4px;padding-top:4px}.GenericFileTab .dropdown-menu{border:1px solid var(--border);margin-top:3px;margin-left:5px}.GenericFileTab .dropdown-menu .Icon{position:absolute}.GenericFileTab .add-sheet-tab{margin-top:3px;margin-left:5px}.GenericFileTab .add-sheet-tab .Icon{width:14px;height:14px}\", \"\"]);\n\n// exports\n", "import { useLayoutEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport PropTypes from 'prop-types';\n\nconst AdditionalTabsFlyout = (props) => {\n  const {\n    id = '',\n    additionalTabs,\n    tabsForReference,\n    onClick,\n    activeItem,\n  } = props;\n  const dispatch = useDispatch();\n\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, id));\n\n  useLayoutEffect(() => {\n    const noteStateFlyout = {\n      dataElement: id,\n      className: 'AdditionalTabsFlyout',\n      items: additionalTabs.map((item) => {\n        const tabLabel = item.name;\n\n        return {\n          label: tabLabel,\n          title: tabLabel,\n          option: tabLabel,\n          disabled: item.disabled,\n          isActive: tabLabel === activeItem,\n          dataElement: Symbol(tabLabel).toString(),\n          onClick: () => onClick(tabLabel, item.sheetIndex),\n        };\n      })\n    };\n    if (!currentFlyout) {\n      dispatch(actions.addFlyout(noteStateFlyout));\n    } else {\n      dispatch(actions.updateFlyout(noteStateFlyout.dataElement, noteStateFlyout));\n    }\n  }, [tabsForReference, activeItem]);\n\n  return null;\n};\n\nAdditionalTabsFlyout.propTypes = {\n  id: PropTypes.string,\n  additionalTabs: PropTypes.arrayOf(PropTypes.shape({\n    name: PropTypes.string,\n    sheetIndex: PropTypes.number,\n    disabled: PropTypes.bool,\n  })),\n  tabsForReference: PropTypes.array,\n  onClick: PropTypes.func,\n  activeItem: PropTypes.string,\n};\n\nexport default AdditionalTabsFlyout;\n\n\n", "import { useLayoutEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport DataElements from 'src/constants/dataElement';\nimport actions from 'actions';\nimport selectors from 'selectors';\n\nconst createFlyoutItem = (option, dataElement) => ({\n  label: `action.${option.toLowerCase()}`,\n  title: `action.${option.toLowerCase()}`,\n  option,\n  dataElement,\n  disabled: true,\n});\n\nexport const sheetTabOptionsFlyoutItems = [\n  createFlyoutItem('Rename', 'sheetTabRenameOption'),\n  createFlyoutItem('Delete', 'sheetTabDeleteOption'),\n];\n\nconst TabOptionsFlyout = (props) => {\n  const {\n    sheetId,\n    handleClick,\n    sheetCount,\n  } = props;\n\n  const dispatch = useDispatch();\n  const flyoutSelector = `${DataElements.SHEET_TAB_OPTIONS_FLYOUT}-${sheetId}`;\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n\n  const update = () => {\n    const noteStateFlyout = {\n      dataElement: flyoutSelector,\n      className: 'TabOptionsFlyout',\n      items: sheetTabOptionsFlyoutItems.map((item) => {\n        let isDisabled = (item.option === 'Delete' && sheetCount === 1) || item.disabled;\n\n        return {\n          ...item,\n          onClick: () => handleClick(item.option),\n          disabled: isDisabled,\n        };\n      }),\n    };\n\n    if (!currentFlyout) {\n      dispatch(actions.addFlyout(noteStateFlyout));\n    } else {\n      dispatch(actions.updateFlyout(noteStateFlyout.dataElement, noteStateFlyout));\n    }\n  };\n\n  useLayoutEffect(update, [sheetCount]);\n\n  return null;\n};\n\nTabOptionsFlyout.propTypes  = {\n  sheetId: PropTypes.string,\n  handleClick: PropTypes.func,\n  sheetCount: PropTypes.number,\n};\n\nexport default TabOptionsFlyout;", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\nimport { useTranslation } from 'react-i18next';\nimport SheetOptionsFlyout from './TabOptionsFlyout';\nimport DataElements from 'src/constants/dataElement';\n\n\nconst TabOptions = (props) => {\n  const {\n    handleClick = () => { },\n    id,\n    onToggle,\n    label,\n    disabled,\n    sheetCount,\n  } = props;\n\n  const [t] = useTranslation();\n  const icon = 'icon-tools-more-vertical';\n\n  return (\n    <>\n      <ToggleElementButton\n        dataElement={`options-${id}`}\n        title={t('option.searchPanel.moreOptions')}\n        img={icon}\n        onToggle={(isActive) => {\n          if (isActive) {\n            onToggle(label);\n          }\n        }}\n        disabled={disabled}\n        toggleElement={`${DataElements.SHEET_TAB_OPTIONS_FLYOUT}-${id}`}\n      />\n      <SheetOptionsFlyout\n        sheetId={id}\n        sheetCount={sheetCount}\n        handleClick={(option) => handleClick(id, label, option)}\n      />\n    </>\n  );\n};\n\nTabOptions.propTypes = {\n  id: PropTypes.string,\n  label: PropTypes.string,\n  onToggle: PropTypes.func,\n  handleClick: PropTypes.func,\n  disabled: PropTypes.bool,\n  sheetCount: PropTypes.number,\n};\n\nexport default TabOptions;\n", "import React, { useEffect, useState, useRef } from 'react';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport PropTypes from 'prop-types';\nimport Button from 'components/Button';\nimport classNames from 'classnames';\nimport TabOptions from '../TabOptionsButton';\nimport { useTranslation } from 'react-i18next';\nimport './SheetTab.scss';\n\nconst propTypes = {\n  sheet: PropTypes.any.isRequired,\n  sheetCount: PropTypes.number,\n  activeSheetLabel: PropTypes.string.isRequired,\n  setLabelBeingEdited: PropTypes.func.isRequired,\n  setActiveSheet: PropTypes.func.isRequired,\n  onClick: PropTypes.func.isRequired,\n  isEditMode: PropTypes.bool.isRequired,\n  validateName: PropTypes.func,\n  noRightBorder: PropTypes.bool,\n  isReadOnlyMode: PropTypes.bool,\n};\n\nconst SheetTab = ({\n  sheet,\n  activeSheetLabel,\n  onClick,\n  sheetCount,\n  setLabelBeingEdited,\n  setActiveSheet,\n  isEditMode,\n  noRightBorder,\n  validateName,\n  isReadOnlyMode,\n}) => {\n  const { sheetIndex, name: label, disabled } = sheet;\n\n  const isActive = label === activeSheetLabel;\n\n  const [inputValue, setInputValue] = useState('');\n  const [isInputError, setIsInputError] = useState(false);\n  const { t } = useTranslation();\n  const inputRef = useRef();\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (inputValue && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [inputValue]);\n\n  const onInputChange = (currentLabel, newLabel) => {\n    const isDuplicate = validateName(currentLabel, newLabel);\n    const isEmpty = newLabel.trim() === '';\n\n    setIsInputError(isDuplicate || isEmpty);\n    setInputValue(newLabel);\n  };\n\n  const updateActiveTab = (newActiveLabel, tabIndex) => {\n    setLabelBeingEdited(null);\n    setIsInputError(false);\n    setActiveSheet(newActiveLabel, tabIndex);\n  };\n\n  const startLabelEditingMode = (text) => {\n    setInputValue(text);\n    setIsInputError(false);\n    setLabelBeingEdited(text);\n  };\n\n  const inputErrorWarning = () => {\n    const isEmpty = inputValue === '';\n    const warningKey = isEmpty ? 'warning.sheetTabRenameIssueTwo' : 'warning.sheetTabRenameIssueOne';\n    const warning = {\n      message: t(`${warningKey}.message`),\n      title: t(`${warningKey}.title`),\n      confirmBtnText: t('action.ok'),\n      onConfirm: () => inputRef.current.focus(),\n    };\n    dispatch(actions.showWarningMessage(warning));\n  };\n\n  const onInputBlur = () => {\n    if (isInputError) {\n      inputErrorWarning();\n      return;\n    }\n    setLabelBeingEdited(null);\n    setIsInputError(false);\n    setInputValue('');\n  };\n\n  const onKeyDown = (e) => {\n    if (e.key === 'Enter' && !isInputError) {\n      onInputBlur();\n    }\n  };\n\n  const handleTabOptionSelection = (id, sheetName, option) => {\n    if (option === 'Rename') {\n      startLabelEditingMode(sheetName);\n    } else if (option === 'Delete') {\n      const message = t('warning.sheetTabDeleteMessage.message');\n      const title = t('warning.sheetTabDeleteMessage.title');\n      const confirmBtnText = t('action.ok');\n      const secondaryBtnText = t('action.cancel');\n      const warning = {\n        message,\n        title,\n        confirmBtnText,\n        secondaryBtnText,\n        onConfirm: () => { },\n        onSecondary: () => { },\n      };\n      dispatch(actions.showWarningMessage(warning));\n    }\n  };\n\n  let component = null;\n  if (isEditMode) {\n    component = (\n      <input type='text'\n        className={classNames({ 'input-error': isInputError })}\n        ref={inputRef}\n        value={inputValue}\n        onChange={(e) => onInputChange(label, e.target.value)}\n        onBlur={onInputBlur}\n        onKeyDown={onKeyDown}\n      />\n    );\n  } else {\n    component = (\n      <>\n        <Button\n          role='tab'\n          ariaControls={'document-container'}\n          ariaSelected={isActive}\n          ariaLabel={label}\n          className={'sheet-label'}\n          onClick={(e) => onClick(e, label, sheetIndex)}\n          title={label}\n          label={label}\n          useI18String={false}\n          disabled={disabled}\n        />\n        <div className='sheet-options'>\n          <TabOptions id={label.replace(/\\s+/g, '-').toLowerCase()}\n            label={label}\n            sheetCount={sheetCount}\n            onToggle={(name) => updateActiveTab(name, sheetIndex)}\n            handleClick={handleTabOptionSelection}\n            disabled={isReadOnlyMode}\n          />\n        </div>\n      </>\n    );\n  }\n\n  return (<div className={classNames({\n    'active': isActive,\n    'no-left-border': noRightBorder,\n    'disabled': disabled,\n  }, 'sheet-tab')}\n  >\n    <div className={classNames({ 'input-mode': isEditMode },'tab-body')}>\n      {component}\n    </div>\n  </div>);\n\n};\n\nSheetTab.propTypes = propTypes;\n\nexport default React.memo(SheetTab);", "import React, { useState, useMemo } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport useWindowDimensions from 'helpers/useWindowsDimensions';\nimport Button from 'components/Button';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\nimport Icon from 'components/Icon';\nimport DataElements from 'constants/dataElement';\nimport { SpreadsheetEditorEditMode } from 'constants/spreadsheetEditor';\nimport AdditionalTabsFlyout from './AdditionalTabsFlyout';\nimport SheetTab from './SheetTab/SheetTab';\nimport './SpreadsheetSwitcher.scss';\nimport PropTypes from 'prop-types';\nimport selectors from 'selectors';\n\n\nconst SpreadsheetSwitcher = (props) => {\n  const { tabs = [], activeSheetIndex = 0, setActiveSheet = () => {} } = props;\n  const activeSheetLabel = tabs[activeSheetIndex]?.name || '';\n  const { width } = useWindowDimensions();\n  const breakpoint = useMemo(() => Math.floor((width - 80) / 170), [width]);\n  const { t } = useTranslation();\n  const [labelBeingEdited, setLabelBeingEdited] = useState('');\n\n  const spreadsheetEditorEditMode = useSelector(selectors.getSpreadsheetEditorEditMode);\n  const isReadOnlyMode = spreadsheetEditorEditMode === SpreadsheetEditorEditMode.VIEW_ONLY;\n\n  const updateActiveTab = (newActiveLabel, tabIndex) => {\n    setLabelBeingEdited(null);\n    setActiveSheet(newActiveLabel, tabIndex);\n  };\n\n  const handleTabNameClick = (e, label, index) => {\n    e.preventDefault();\n    e.stopPropagation();\n    updateActiveTab(label, index);\n  };\n\n  const handleAddSheetButton = () => { };\n\n  // Break the sheet tabs into two, one regular view, and one into flyout\n  const [slicedTabs, flyoutTabs] = useMemo(() => {\n    return [tabs.slice(0, breakpoint), tabs.slice(breakpoint)];\n  }, [tabs, breakpoint]);\n\n  const tabElements = slicedTabs.map((item) => (\n    <SheetTab\n      key={item.sheetIndex}\n      sheet={item}\n      sheetCount={tabs.length}\n      activeSheetLabel={activeSheetLabel}\n      onClick={handleTabNameClick}\n      isEditMode={labelBeingEdited === item.name}\n      setLabelBeingEdited={setLabelBeingEdited}\n      setActiveSheet={setActiveSheet}\n      noRightBorder={tabs[item.sheetIndex + 1] && tabs[item.sheetIndex + 1].name === activeSheetLabel}\n      validateName={() => {}}\n      isReadOnlyMode={isReadOnlyMode}\n    />\n  ));\n\n  const isActiveSheetInFlyout = useMemo(() => {\n    return flyoutTabs.some((item) => item.name === activeSheetLabel);\n  }, [flyoutTabs, activeSheetLabel]);\n\n  return (\n    <div className=\"SpreadsheetSwitcher ModularHeader BottomHeader stroke start\">\n      <div className={'GenericFileTab'} role='tablist'>\n        {tabElements}\n        {\n          (flyoutTabs?.length > 0) ?\n            (\n              <ToggleElementButton\n                className=\"dropdown-menu tab-dropdown-button\"\n                dataElement=\"tabTrigger\"\n                title={t('message.showMore')}\n                toggleElement={DataElements.ADDITIONAL_SPREADSHEET_TABS_MENU}\n                label={flyoutTabs.length.toString()}\n              >\n                {(isActiveSheetInFlyout) && (<Icon glyph=\"icon-active-indicator\"></Icon>)}\n              </ToggleElementButton>\n            ) : null\n        }\n        <Button\n          className=\"add-sheet-tab\"\n          title=\"action.addSheet\"\n          img=\"icon-menu-add\"\n          onClick={handleAddSheetButton}\n          dataElement={'addTabButton'}\n          tabIndex={-1}\n          label={''}\n          ariaLabel={t('action.addSheet')}\n          disabled={isReadOnlyMode}\n        />\n        {flyoutTabs?.length > 0 &&\n          (\n            <AdditionalTabsFlyout\n              id={DataElements.ADDITIONAL_SPREADSHEET_TABS_MENU}\n              additionalTabs={flyoutTabs}\n              tabsForReference={tabs}\n              onClick={updateActiveTab}\n              activeItem={activeSheetLabel}\n            />\n          )\n        }\n      </div>\n    </div>\n  );\n};\n\nSpreadsheetSwitcher.propTypes = {\n  tabs: PropTypes.arrayOf(PropTypes.shape({\n    name: PropTypes.string,\n    sheetIndex: PropTypes.number,\n    disabled: PropTypes.bool,\n  })),\n  activeSheetIndex: PropTypes.number,\n  setActiveSheet: PropTypes.func,\n};\n\nexport default SpreadsheetSwitcher;", "import React, { useState, useEffect, useCallback } from 'react';\nimport core from 'core';\nimport SpreadsheetSwitcher from './SpreadsheetSwitcher';\nimport useOnDocumentUnloaded from 'hooks/useOnDocumentUnloaded';\n\nfunction SpreadsheetSwitcherContainer(props) {\n  const [workbook, setWorkbook] = useState(null);\n  const [sheets, setSheets] = useState([]);\n  const [activeSheetIndex, setActiveSheetIndex] = useState(0);\n\n  const getSheetsFromWorkbook = (workbookInstance) => {\n    let sheetCount = workbookInstance.sheetCount;\n    const sheetArray = [];\n    for (let i = 0; i < sheetCount; i++) {\n      const sheet = workbookInstance.getSheetAt(i);\n      sheetArray.push({ name: sheet.name, sheetIndex: i });\n    }\n    return sheetArray;\n  };\n\n  useEffect(() => {\n    const documentViewer = core.getDocumentViewer();\n\n    const onSpreadsheetEditorSheetChanged = (event) => {\n      const doc = documentViewer.getDocument().getSpreadsheetEditorDocument();\n      const workbookInstance = doc.getWorkbook();\n\n      setWorkbook(workbookInstance);\n      setSheets(getSheetsFromWorkbook(workbookInstance));\n      setActiveSheetIndex(event.getSheetIndex());\n    };\n\n    core.addEventListener('activeSheetChanged', onSpreadsheetEditorSheetChanged);\n    return () => {\n      core.removeEventListener('activeSheetChanged', onSpreadsheetEditorSheetChanged);\n    };\n  }, []);\n\n  const handleDocumentUnloaded = useCallback(() => {\n    setSheets([]);\n    setActiveSheetIndex(0);\n  }, []);\n  useOnDocumentUnloaded(handleDocumentUnloaded);\n\n  const ownProps = {\n    ...props,\n    tabs: sheets,\n    activeSheetIndex,\n    setActiveSheet: (name, index) => {\n      if (workbook.getSheetAt(index)) {\n        workbook.setActiveSheet(index);\n        setActiveSheetIndex(index);\n      }\n    }\n  };\n\n  return (<SpreadsheetSwitcher {...ownProps} />);\n}\n\nSpreadsheetSwitcherContainer.propTypes = { };\n\nexport default SpreadsheetSwitcherContainer;", "import SpreadsheetSwitcherContainer from './SpreadsheetSwitcherContainer';\n\nexport default SpreadsheetSwitcherContainer;"], "sourceRoot": ""}