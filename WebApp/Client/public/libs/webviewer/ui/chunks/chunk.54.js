(window.webpackJsonp=window.webpackJsonp||[]).push([[54],{1861:function(e,t,n){var o=n(32),r=n(1862);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1862:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.FileListPanel{box-sizing:border-box;display:flex;flex-direction:column;align-items:center;width:100%;height:100%;overflow:auto;padding-top:20px;padding-bottom:20px;background-color:var(--document-background-color);border-radius:4px}.FileListPanel ul{display:flex;flex-wrap:wrap;justify-content:center;align-content:flex-start;list-style:none;margin:0;padding:16px;height:30em;width:100%}.FileListPanel ul li.selected{border:1px solid #48a4e0}.FileListPanel ul li{background:var(--gray-1);border:1px solid var(--blue-1);box-sizing:border-box;border-radius:6px;width:100%;height:60px;margin:4px 0;padding:8px 10px;display:flex;flex-direction:row;cursor:pointer}.FileListPanel ul li:hover{border:1px solid #48a4e0}.FileListPanel ul li .li-div{background:#fff;width:42px;height:100%;float:left;border-radius:4px;position:relative}.FileListPanel ul li .li-div-img{position:absolute;top:50%;left:50%;transform:translateY(-50%) translateX(-50%);width:28px}.FileListPanel ul li .li-div-img.with-border{height:34px;border:1px solid var(--blue-1)}.FileListPanel ul li .li-div-txt{margin-left:10px;line-height:40px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1866:function(e,t,n){var o=n(32),r=n(1867);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1867:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.fileSelectedPanel.container{width:791px}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel.container{width:494px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel.container .page-number-input{width:92px}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel.container{width:494px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel.container .page-number-input{width:92px}}.fileSelectedPanel .modal-container .wrapper .header-container{padding-bottom:16px!important}.fileSelectedPanel .header .left-header{display:flex;align-items:center;grid-gap:4px;gap:4px}.fileSelectedPanel .header .Button{justify-content:center}.fileSelectedPanel .header .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.fileSelectedPanel .page-number-error{margin-top:8px;font-size:13px;color:var(--color-message-error)}.fileSelectedPanel .page-replacement-text{padding:0 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .page-replacement-text{padding:0;display:flex;justify-content:center;align-items:center}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .page-replacement-text{padding:0;display:flex;justify-content:center;align-items:center}}.fileSelectedPanel .modal-body{padding:16px}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .thumb-card{width:132px}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .thumb-card{width:132px}}.fileSelectedPanel .modal-body .modal-body-container{display:flex}.fileSelectedPanel .modal-body .modal-body-container.isLoading{justify-content:center;align-items:center}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container{padding:18px;height:calc(100vh - 296px)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .thumb-card{flex:0 0 calc(50% - 10px);box-sizing:border-box}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .thumb-card-img{height:110px;width:83px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .thumb-body{align-items:center}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .thumb-image{width:100%;padding-right:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .ui__choice__input{position:absolute;top:6px;right:6px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .ui__choice{margin:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container{padding:18px;height:calc(100vh - 296px)}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .thumb-card{flex:0 0 calc(50% - 10px);box-sizing:border-box}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .thumb-card-img{height:110px;width:83px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .thumb-body{align-items:center}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .thumb-image{width:100%;padding-right:0}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .ui__choice__input{position:absolute;top:6px;right:6px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .ui__choice{margin:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body{height:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body{height:100%}}.fileSelectedPanel .replace-page-input-container{height:60px;margin-bottom:8px;display:flex;align-items:baseline}.fileSelectedPanel .replace-page-input-container :first-child{padding-left:0}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input-container{justify-content:space-between}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input-container :first-child{padding-right:8px;padding-left:0}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input-container{justify-content:space-between}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input-container :first-child{padding-right:8px;padding-left:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input-container{display:grid;grid-template-columns:auto auto auto;grid-gap:16px;grid-row-gap:24px;row-gap:24px;margin-bottom:32px;height:88px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input-container .page-number-error{margin-top:4px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input-container{display:grid;grid-template-columns:auto auto auto;grid-gap:16px;grid-row-gap:24px;row-gap:24px;margin-bottom:32px;height:88px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input-container .page-number-error{margin-top:4px}}.fileSelectedPanel .replace-page-input{padding-left:8px;padding-right:8px}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input{padding-right:0;padding-left:4px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input .page-replace-doc-name{word-break:break-all}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input{padding-right:0;padding-left:4px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input .page-replace-doc-name{word-break:break-all}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input{display:flex;justify-content:left;align-items:center;padding-left:0;padding-right:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input{display:flex;justify-content:left;align-items:center;padding-left:0;padding-right:0}}.fileSelectedPanel .page-replace-doc-name{font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .header-container{height:72px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .header-container{height:72px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .deselect-thumbnails{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .deselect-thumbnails{font-size:13px}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1980:function(e,t,n){"use strict";n.r(t);n(19),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(16),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46);var o=n(0),r=n.n(o),i=n(3),a=n(2),l=n(6),c=(n(78),n(440),n(110),n(56),n(64),n(65),n(66),n(67),n(37),n(39),n(40),n(63),n(17)),p=n.n(c),d=n(1),s=n(429),u=(n(89),n(41),n(1861),function(e){var t=e.defaultValue,n=e.onFileSelect,o=e.list,i=void 0===o?[]:o,a=(t||{}).id,l=i.map((function(e,t){var o=a===e.id,l=p()({selected:o}),c=function(e){if(e.hasOwnProperty("thumbnail")){var t=null,n=e.thumbnail;n.url?t=n.url:n.toDataURL&&(t=n.toDataURL());var o=r.a.createElement("img",{src:t,className:"li-div-img"});return r.a.createElement("div",{className:"li-div"},o)}return null}(e);return r.a.createElement("li",{tabIndex:"0",key:t,onClick:function(){return function(e){n(i.find((function(t){return t.id===e})))}(e.id)},onKeyDown:function(t){return function(e,t){"Enter"===e.key&&n(i.find((function(e){return e.id===t})))}(t,e.id)},className:l},c,r.a.createElement("div",{className:"li-div-txt"},e.filename))}));return r.a.createElement("div",{className:"FileListPanel"},r.a.createElement("ul",null,l))}),m=n(1774),f=n(448),b=n(132),h=n(48),y=(n(60),n(44),n(114),n(36),n(117),n(442)),g=(n(1685),n(1866),n(1769)),w=n(61),v=n(341);function x(e){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function P(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?P(Object(n),!0).forEach((function(t){E(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):P(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function E(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==x(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==x(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===x(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,p=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){p=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(p)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return j(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return j(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var k=r.a.forwardRef((function(e,t){var n=e.closeThisModal,i=e.clearLoadedFile,a=e.pageIndicesToReplace,l=e.sourceDocument,c=e.replacePagesHandler,d=e.documentInViewer,u=e.closeModalWarning,m=O(Object(s.a)(),1)[0],f=O(Object(o.useState)(a.map((function(e){return e+1}))),2),b=f[0],x=f[1],P=O(Object(o.useState)([]),2),j=P[0],k=P[1],A=O(Object(o.useState)({}),2),N=A[0],L=A[1],F=O(Object(o.useState)(!0),2),_=F[0],T=F[1],R=O(Object(o.useState)(null),2),I=R[0],D=R[1],C=O(Object(o.useState)(null),2),M=C[0],H=C[1],B=O(Object(o.useState)(0),2),G=B[0],z=B[1],W=O(Object(o.useState)(""),2),V=W[0],U=W[1],q=O(Object(o.useState)(""),2),$=q[0],Y=q[1],J=Object(w.d)(),K=Object(w.b)(),X=function(){var e=[];for(var t in N)N[t]&&e.push(parseInt(t));return e};Object(o.useEffect)((function(){if(l){for(var e=l.getPageCount(),t={},n=1;n<=e;n++)t[n]=!0;L(t)}}),[l]),Object(o.useEffect)((function(){k(X())}),[N]),Object(o.useEffect)((function(){function e(e){return e.length>25?J&&!K?'"'.concat(e.slice(0,4),"...").concat(e.slice(e.length),'"'):'"'.concat(e.slice(0,10),"...").concat(e.slice(e.length-10),'"'):'"'.concat(e,'"')}if(l){var t=l.getPageCount();z(t),D(e(l.getFilename())),H(e(d.getFilename()))}}),[l]);var Q=function(e){e.length>0&&Y("");var t=e.reduce((function(e,t){return S(S({},e),{},E({},t,!0))}),{});L(S({},t))},Z=function(){u()},ee=d.getPageCount();return r.a.createElement("div",{className:"fileSelectedPanel container",onMouseDown:function(e){return e.stopPropagation()},ref:t},r.a.createElement(v.a,{title:m("component.pageReplaceModalTitle"),closeButtonDataElement:"pageReplacementModalClose",onCloseClick:Z,swipeToClose:!0,closeHandler:Z,backButtonDataElement:"insertFromFileBackButton",onBackClick:i},r.a.createElement("div",{className:"modal-body"},r.a.createElement("div",{className:"replace-page-input-container"},r.a.createElement("div",{className:"page-replacement-text"},m("option.pageReplacementModal.pageReplaceInputLabel"),":"),r.a.createElement("div",{className:"replace-page-input-current-doc-containers"},r.a.createElement(y.a,{selectedPageNumbers:b,pageCount:ee,onSelectedPageNumbersChange:function(e){e.length>0&&(U(""),x(e))},onBlurHandler:x,onError:function(e){e&&U("".concat(m("message.errorPageNumber")," ").concat(ee))},pageNumberError:V})),r.a.createElement("div",{className:"replace-page-input"},r.a.createElement("span",{className:"page-replace-doc-name"},M)),r.a.createElement("div",{className:"page-replacement-text"},m("option.pageReplacementModal.pageReplaceInputFromSource"),":"),r.a.createElement(y.a,{selectedPageNumbers:j,pageCount:G,onSelectedPageNumbersChange:function(e){e.length>0?(k(e),Q(e)):X()},onBlurHandler:Q,onError:function(e){e&&Y("".concat(m("message.errorPageNumber")," ").concat(G))},pageNumberError:$}),r.a.createElement("div",{className:"replace-page-input"},r.a.createElement("span",{className:"page-replace-doc-name"},I))),r.a.createElement("div",{className:p()("modal-body-container",{isLoading:_})},r.a.createElement(g.a,{document:l,onThumbnailSelected:function(e){void 0===N[e]?N[e]=!0:N[e]=!N[e],L(S({},N))},selectedThumbnails:N,onfileLoadedHandler:T}))),r.a.createElement("div",{className:"page-replacement-divider"}),r.a.createElement("div",{className:p()("footer",{isFileSelected:!_})},r.a.createElement("button",{className:p()("deselect-thumbnails",{disabled:_}),onClick:function(){L({})},disabled:_},m("action.deselectAll")),r.a.createElement(h.a,{className:"modal-btn replace-btn",onClick:function(){return e=X(),c(l,b,e),void n();var e},label:m("action.replace"),disabled:function(){if(b.length<1||V||$)return!0;for(var e in N)if(N[e])return!1;return!0}()}))))}));k.displayName="FileSelectedPanel";var A=k,N=n(120);function L(){return(L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}var F=r.a.forwardRef((function(e,t){var n=d.a.getDocument();return r.a.createElement(A,L({},e,{documentInViewer:n,replacePagesHandler:N.n,ref:t}))}));F.displayName="FileSelectedPanelContainer";var _=F,T=n(21);function R(e){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function I(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==R(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==R(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===R(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function D(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */D=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function p(e,t,n,r){var i=t&&t.prototype instanceof u?t:u,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(e,n,l)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=p;var s={};function u(){}function m(){}function f(){}var b={};c(b,i,(function(){return this}));var h=Object.getPrototypeOf,y=h&&h(h(j([])));y&&y!==t&&n.call(y,i)&&(b=y);var g=f.prototype=u.prototype=Object.create(b);function w(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function v(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var c=d(e[r],e,i);if("throw"!==c.type){var p=c.arg,s=p.value;return s&&"object"==R(s)&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(s).then((function(e){p.value=e,a(p)}),(function(e){return o("throw",e,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return k()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=P(a,n);if(l){if(l===s)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=d(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===s)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function P(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,P(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),s;var r=d(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,s;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,s):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,s)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function j(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:k}}function k(){return{value:void 0,done:!0}}return m.prototype=f,o(g,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:m,configurable:!0}),m.displayName=c(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,l,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},w(v.prototype),c(v.prototype,a,(function(){return this})),e.AsyncIterator=v,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new v(p(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},w(g),c(g,l,"Generator"),c(g,i,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=j,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,s):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),s},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),s}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;E(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:j(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),s}},e}function C(e,t,n,o,r,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}function M(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){C(i,o,r,a,l,"next",e)}function l(e){C(i,o,r,a,l,"throw",e)}a(void 0)}))}}function H(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,p=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){p=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(p)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return B(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return B(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var G=new RegExp(/^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+$/,"m"),z={loadAsPDF:!0},W=function(e){var t=e.closeModal,n=e.selectableFiles,a=e.isOpen,c=e.selectedThumbnailPageIndexes,y=e.selectedTab,g=H(Object(s.a)(),1)[0],w=H(Object(o.useState)({}),2),x=w[0],P=w[1],S=H(Object(o.useState)(null),2),E=S[0],O=S[1],j=H(Object(o.useState)(!1),2),k=j[0],A=j[1],L=H(Object(o.useState)(null),2),F=L[0],R=L[1],C=Object(l.d)(),B=Object(l.e)((function(e){var t;return null===(t=i.a.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI}));Object(o.useEffect)((function(){a&&F!==y&&R(y)}),[a,F,y]);var W=function(){O(null),A(!1);var e=Object(T.a)().querySelector("#".concat("pageReplacementFileInputId"));e&&(e.value=null),t(),R(null),P({})},V=function(){return Object(N.d)(W,C)},U=p()({Modal:!0,PageReplacementModal:!0,open:a,closed:!a,"modular-ui":B}),q=x[F],$=function(){var e=M(D().mark((function e(){var t;return D().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(A(!0),!q||"customFileListPanelButton"!==F){e.next=9;break}if(!q.onSelect){e.next=7;break}return e.next=5,q.onSelect();case 5:t=e.sent,O(t);case 7:e.next=14;break;case 9:if(!q){e.next=14;break}return e.next=12,d.a.createDocument(q,z);case 12:t=e.sent,O(t);case 14:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Y=function(){var e=M(D().mark((function e(t){var n;return D().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t instanceof Object(T.c)().instance.Core.Document)){e.next=4;break}n=t,e.next=7;break;case 4:return e.next=6,d.a.createDocument(t,z);case 6:n=e.sent;case 7:O(n),A(!0);case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),J=void 0===q;"urlInputPanelButton"!==F||G.test(q)||(J=!0);var K,X=function(){O(null),A(!1)};return a?r.a.createElement("div",{className:U,"data-element":"pageReplacementModal",onMouseDown:k?V:W,id:"pageReplacementModal"},k?r.a.createElement(_,{closeThisModal:W,clearLoadedFile:X,pageIndicesToReplace:c,sourceDocument:E,closeModalWarning:V}):(K=n&&n.length>0,r.a.createElement("div",{className:"container tabs",onMouseDown:function(e){return e.stopPropagation()}},r.a.createElement(v.a,{isOpen:a,title:g("component.pageReplaceModalTitle"),closeButtonDataElement:"pageReplacementModalClose",onCloseClick:W,swipeToClose:!0,closeHandler:W},r.a.createElement("div",{className:"swipe-indicator"}),r.a.createElement(b.d,{className:"page-replacement-tabs",id:"pageReplacementModal"},r.a.createElement("div",{className:"tabs-header-container"},r.a.createElement("div",{className:"tab-list"},K&&r.a.createElement(r.a.Fragment,null,r.a.createElement(b.a,{dataElement:"customFileListPanelButton"},r.a.createElement("button",{className:"tab-options-button"},g("option.pageReplacementModal.yourFiles"))),r.a.createElement("div",{className:"tab-options-divider"})),r.a.createElement(b.a,{dataElement:"urlInputPanelButton"},r.a.createElement("button",{className:"tab-options-button"},g("link.url"))),r.a.createElement("div",{className:"tab-options-divider"}),r.a.createElement(b.a,{dataElement:"filePickerPanelButton"},r.a.createElement("button",{className:"tab-options-button"},g("option.pageReplacementModal.localFile"))))),r.a.createElement(b.c,{dataElement:"customFileListPanel"},r.a.createElement("div",{className:"panel-body"},r.a.createElement(u,{onFileSelect:function(e){P(I({},F,e))},list:n,defaultValue:q}))),r.a.createElement(b.c,{dataElement:"urlInputPanel"},r.a.createElement("div",{className:"panel-body"},r.a.createElement(m.a,{onFileSelect:function(e){P(I({},F,e))},defaultValue:x.urlInputPanelButton}))),r.a.createElement(b.c,{dataElement:"filePickerPanel"},r.a.createElement("div",{className:"panel-body upload"},r.a.createElement(f.a,{fileInputId:"pageReplacementFileInputId",onFileProcessed:function(e){return Y(e)}})))),r.a.createElement("div",{className:"page-replacement-divider"}),r.a.createElement("div",{className:"footer"},r.a.createElement(h.a,{className:p()("modal-btn",{noFile:J}),onClick:function(){return J?null:$()},label:g("action.select"),disabled:J})))))):null};function V(e){return(V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var U=["closePageReplacement"];function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach((function(t){Y(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Y(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==V(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==V(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===V(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},i=Object.keys(e);for(o=0;o<i.length;o++)n=i[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}var K=function(e){var t=e.closePageReplacement,n=J(e,U),i=Object(o.useCallback)((function(){t()}),[t]),a=$($({},n),{},{closeModal:i});return r.a.createElement(W,a)},X=n(5);function Q(e){return(Q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(n),!0).forEach((function(t){te(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Z(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function te(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Q(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==Q(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Q(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ne(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,p=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){p=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(p)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return oe(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return oe(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var re=function(e){var t=Object(l.d)(),n=Object(l.e)((function(e){return i.a.getPageReplacementFileList(e)})),o=ne(Object(l.e)((function(e){return[i.a.isElementOpen(e,X.a.PAGE_REPLACEMENT_MODAL)]})),1)[0],c=Object(l.e)((function(e){return i.a.getSelectedThumbnailPageIndexes(e)})),p=Object(l.e)((function(e){return i.a.getSelectedTab(e,X.a.PAGE_REPLACEMENT_MODAL)})),d=ee(ee({},e),{},{closePageReplacement:function(){t(a.a.closeElement(X.a.PAGE_REPLACEMENT_MODAL))},selectableFiles:n,isOpen:o,selectedThumbnailPageIndexes:c,selectedTab:p});return r.a.createElement(K,d)};t.default=re}}]);
//# sourceMappingURL=chunk.54.js.map