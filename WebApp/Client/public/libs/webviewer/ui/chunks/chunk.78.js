(window.webpackJsonp=window.webpackJsonp||[]).push([[78],{1732:function(e,t,n){var r=n(32),i=n(1733);"string"==typeof(i=i.__esModule?i.default:i)&&(i=[[e.i,i,""]]);var o={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const i=t[r];if(0===r)i.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);i.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(i,o);e.exports=i.locals||{}},1733:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SignatureAddButton{width:auto;padding:8px;color:var(--blue-5);border:1px solid var(--blue-5);border-radius:4px;margin:16px 0}.SignatureAddButton:not(.disabled):hover{color:var(--primary-button-hover);border-color:var(--primary-button-hover)}.SignatureAddButton.disabled{color:var(--blue-5);opacity:.5;cursor:not-allowed}.SignatureAddButton.disabled span{color:var(--blue-5)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureAddButton{margin:16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureAddButton:not(.disabled):hover{color:var(--blue-5);border:1px solid var(--blue-5)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureAddButton{margin:16px}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureAddButton:not(.disabled):hover{color:var(--blue-5);border:1px solid var(--blue-5)}}.SignatureListPanel{height:100%;display:flex;flex-direction:column}.SignatureListPanel .signature-list-panel-header{margin-top:0;font-size:16px;font-weight:700}.SignatureListPanel .signature-header{margin:16px 0;font-size:14px;font-weight:700}.SignatureListPanel .signature-list{padding:16px 2px 0;overflow:auto}.SignatureListPanel .signature-row{display:flex;flex-direction:row;height:48px;justify-content:space-between;align-items:center;margin-top:8px}.SignatureListPanel .signature-row .icon-button{padding:0;border:none;background-color:transparent;display:flex;align-items:center;justify-content:center;width:34px;height:34px;border-radius:4px;cursor:pointer}:host(:not([data-tabbing=true])) .SignatureListPanel .signature-row .icon-button,html:not([data-tabbing=true]) .SignatureListPanel .signature-row .icon-button{outline:none}.SignatureListPanel .signature-row .icon-button:hover{border:1px solid var(--blue-6);background:var(--gray-2)}.SignatureListPanel .signature-list-header{display:flex;flex-direction:row;align-items:center}.SignatureListPanel .signature-list-header .signature-title{flex-grow:2;margin-right:8px;min-width:160px;font-weight:var(--font-weight-bold)}.SignatureListPanel .signature-list-header .initials-title{flex-grow:1;max-width:65px;font-weight:var(--font-weight-bold)}.SignatureListPanel .signature-list-header .delete-spacer{width:40px}.SignatureListPanel .signature-row-container{display:flex;flex-direction:row;align-items:center;flex-grow:1}.SignatureListPanel .signature-row-container .signature-row-content{border:none;background-color:transparent;height:40px;padding:4px 8px;border:1px solid var(--lighter-border);border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-right:8px;background-color:var(--gray-0);flex-grow:2}:host(:not([data-tabbing=true])) .SignatureListPanel .signature-row-container .signature-row-content,html:not([data-tabbing=true]) .SignatureListPanel .signature-row-container .signature-row-content{outline:none}.SignatureListPanel .signature-row-container .signature-row-content img{max-width:100%;max-height:100%}.SignatureListPanel .signature-row-container .signature-row-content.interactable:hover{border-color:var(--blue-6)}.SignatureListPanel .signature-row-container .signature-row-content.interactable.active{background:var(--gray-2);border-color:var(--blue-5)}.SignatureListPanel .signature-row-container .signature-row-content.active{background:var(--tools-overlay-button-active)}.SignatureListPanel .signature-row-container .signature-row-content.focus-visible,.SignatureListPanel .signature-row-container .signature-row-content:focus-visible{outline:var(--focus-visible-outline)!important}.SignatureListPanel .signature-row-container .signature-row-content .signature-button-icon{width:20px;height:20px;margin-top:2px}.SignatureListPanel .signature-row-container .initials{max-width:65px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel{width:100%;position:relative}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel.hideAddButton .SignatureAddButton,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .initials-title,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-title,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel.small-size .Divider{display:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-list-panel-header,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-row{padding:0 16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-list{padding-bottom:16px;position:relative}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-list .signature-row{justify-content:center;margin-top:0;margin-bottom:8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel{width:100%;position:relative}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel.hideAddButton .SignatureAddButton,.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .initials-title,.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-title,.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel.small-size .Divider{display:none}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-list-panel-header,.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-row{padding:0 16px}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-list{padding-bottom:16px;position:relative}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-list .signature-row{justify-content:center;margin-top:0;margin-bottom:8px}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1768:function(e,t,n){"use strict";n.r(t);n(28),n(8),n(41),n(15),n(23),n(24),n(97),n(19),n(12),n(13),n(14),n(10),n(9),n(11),n(16),n(20),n(18),n(56),n(22),n(64),n(65),n(66),n(67),n(37),n(39),n(40),n(63),n(26),n(27),n(25);var r=n(0),i=n.n(r),o=n(429),a=n(6),l=n(76),u=n(72),s=n(5),c=n(115),d=n(31),p=n(61),f=n(102),g=n(3),h=n(2),m=n(17),v=n.n(m),b=(n(1732),n(445)),y=(n(104),n(36),n(4)),w=n.n(y),S=n(48);function x(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,l=[],u=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,i=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return L(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return L(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var A=i.a.memo((function(e){var t=e.index,n=e.onFullSignatureSetHandler,r=e.onInitialsSetHandler,o=e.isActive,a=e.altText,l=e.fullSignature,u=e.initials,s=e.isHoveredForDeletion,d=e.signatureMode,p=function(e,n,r){return i.a.createElement("button",{className:v()("signature-row-content",{interactable:e,active:o&&r===d,"removal-hovered":s},"".concat(r===c.a.FULL_SIGNATURE?"full-signature":"initials")),onClick:function(){return e(t)}},i.a.createElement("img",{alt:a,src:n}))};return i.a.createElement("div",{className:"signature-row-container"},p(n,null==l?void 0:l.imgSrc,c.a.FULL_SIGNATURE),u&&p(r,u.imgSrc,c.a.INITIALS))}));A.displayName="SignatureRowContent";var E=function(e){var t,n=e.savedSignatures,r=e.onFullSignatureSetHandler,a=e.onInitialsSetHandler,l=e.deleteHandler,u=e.currentlySelectedSignature,s=e.isDeleteDisabled,c=e.signatureMode,f=e.panelSize,g=Object(o.a)().t,h=x(i.a.useState(null),2),m=h[0],v=h[1],b=Object(p.b)();return n.length>0?i.a.createElement("div",{className:"signature-list"},(t=n.some((function(e){return e.initials})),i.a.createElement("div",{className:"signature-list-header"},i.a.createElement("div",{className:"signature-title"},g("signatureListPanel.signatureList.signature")),t&&i.a.createElement("div",{className:"initials-title"},g("signatureListPanel.signatureList.initials")),i.a.createElement("div",{className:"delete-spacer"}))),n.map((function(e,t){return[e,t]})).map((function(e){var t=x(e,2),n=t[0],o=n.fullSignature,p=n.initials,h=t[1],y=!f||f!==d.a.SMALL_SIZE,w=b&&f===d.a.SMALL_SIZE;return y||w&&u===h?i.a.createElement("div",{key:h,className:"signature-row"},i.a.createElement(A,{index:h,fullSignature:o,initials:p,onFullSignatureSetHandler:r,onInitialsSetHandler:a,isActive:u===h,altText:"".concat(g("option.toolsOverlay.signatureAltText")," ").concat(h+1),isHoveredForDeletion:m===h,signatureMode:c}),!s&&i.a.createElement(S.a,{className:"icon-button",img:"icon-delete-line",ariaLabel:g("action.delete"),dataElement:"defaultSignatureDeleteButton",onMouseOver:function(){return v(h)},onMouseLeave:function(){return v(null)},onClick:function(){l(h),v(null)}})):null}))):null};E.displayName="SavedSignatures",E.propTypes={panelSize:w.a.oneOf(Object.values(d.a))};var P=i.a.memo(E),I=n(99);function j(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,l=[],u=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,i=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return k(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return k(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var O=function(e){var t=e.isDisabled,n=Object(a.d)(),r=j(Object(o.a)(),1)[0],l=Object(I.a)((function(){t||(n(h.a.setSignatureMode(c.a.FULL_SIGNATURE)),n(h.a.openElement(s.a.SIGNATURE_MODAL)))})),u=Object(a.e)((function(e){return g.a.getIsInitialsModeEnabled(e)}))?"signatureListPanel.newSignatureAndInitial":"signatureListPanel.newSignature";return i.a.createElement(S.a,{className:v()("SignatureAddButton",{disabled:t}),label:r(u),dataElement:s.a.SIGNATURE_ADD_BUTTON,onClick:l})},T=i.a.memo(O),N=n(1),_=n(54);function M(e){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function D(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==M(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==M(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===M(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function H(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */H=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,i){var o=t&&t.prototype instanceof p?t:p,a=Object.create(o.prototype),l=new E(i||[]);return r(a,"_invoke",{value:S(e,n,l)}),a}function c(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function p(){}function f(){}function g(){}var h={};u(h,o,(function(){return this}));var m=Object.getPrototypeOf,v=m&&m(m(P([])));v&&v!==t&&n.call(v,o)&&(h=v);var b=g.prototype=p.prototype=Object.create(h);function y(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var i;r(this,"_invoke",{value:function(r,o){function a(){return new t((function(i,a){!function r(i,o,a,l){var u=c(e[i],e,o);if("throw"!==u.type){var s=u.arg,d=s.value;return d&&"object"==M(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,l)}),(function(e){r("throw",e,a,l)})):t.resolve(d).then((function(e){s.value=e,a(s)}),(function(e){return r("throw",e,a,l)}))}l(u.arg)}(r,o,i,a)}))}return i=i?i.then(a,a):a()}})}function S(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return I()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var l=x(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=c(e,t,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===d)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}function x(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var i=c(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,d;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function P(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:I}}function I(){return{value:void 0,done:!0}}return f.prototype=g,r(b,"constructor",{value:g,configurable:!0}),r(g,"constructor",{value:f,configurable:!0}),f.displayName=u(g,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,u(e,l,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},y(w.prototype),u(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new w(s(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},y(b),u(b,l,"Generator"),u(b,o,(function(){return this})),u(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=P,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(A),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),u=n.call(o,"finallyLoc");if(l&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),A(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;A(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:P(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function B(e,t,n,r,i,o,a){try{var l=e[o](a),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(r,i)}function F(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){B(o,r,i,a,l,"next",e)}function l(e){B(o,r,i,a,l,"throw",e)}a(void 0)}))}}function G(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=U(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){l=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(l)throw o}}}}function C(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,l=[],u=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,i=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return l}}(e,t)||U(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function U(e,t){if(e){if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?R(e,t):void 0}}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var z=function(e){var t=e.panelSize,n=C(Object(o.a)(),1)[0],m=Object(p.b)(),y=Object(a.e)(g.a.getSavedSignatures,a.c),w=Object(a.e)(g.a.getMaxSignaturesCount),S=Object(a.e)(g.a.getDisplayedSignaturesFilterFunction),x=Object(a.e)((function(e){return g.a.isElementDisabled(e,"defaultSignatureDeleteButton")})),L=Object(a.e)(g.a.getSavedInitials,a.c),A=Object(a.e)(g.a.getSelectedDisplayedSignatureIndex),E=Object(a.e)(g.a.getSignatureMode),I=Object(a.e)(g.a.getMobilePanelSize),j=Object(a.f)(),k="AnnotationCreateSignature",O=N.a.getToolsFromAllDocumentViewers(k);Object(r.useEffect)((function(){return function(){var e,t=G(O);try{for(t.s();!(e=t.n()).done;){var n=e.value;n.clearLocation(),n.setSignature(null),n.setInitials(null)}}catch(e){t.e(e)}finally{t.f()}}}),[]);var M=C(i.a.useState([]),2),B=M[0],U=M[1];Object(r.useEffect)((function(){var e=y.filter((function(e,t){return S(e,t)})).map((function(e,t){return{fullSignature:e,initials:L[t]||null}}));U(e)}),[y,L,S]),Object(r.useEffect)((function(){I!==d.a.SMALL_SIZE&&m&&R(h.a.setMobilePanelSize(d.a.SMALL_SIZE))}),[A]),Object(r.useEffect)((function(){var e=function(e){var t=N.a.getToolMode(),n=null==t?void 0:t.name,r=e.detail,i=r.element,o=r.isVisible;i!==d.e.SIGNATURE_LIST||o||n!==k&&n!==u.a||Object(f.a)(j,u.a)};return window.addEventListener(_.a.VISIBILITY_CHANGED,e),function(){window.removeEventListener(_.a.VISIBILITY_CHANGED,e)}}),[]);var R=Object(a.d)(),z=Object(r.useCallback)(function(){var e=F(H().mark((function e(t){var n,r,i,o,a;return H().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=B[t].fullSignature,r=n.annotation,R(h.a.setSelectedDisplayedSignatureIndex(t)),N.a.setToolMode(k),i=G(O),e.prev=5,i.s();case 7:if((o=i.n()).done){e.next=21;break}return a=o.value,e.next=11,a.setSignature(r);case 11:if(!a.hasLocation()){e.next=16;break}return e.next=14,a.addSignature();case 14:e.next=19;break;case 16:return e.next=18,a.showPreview();case 18:R(h.a.setSignatureMode(c.a.FULL_SIGNATURE));case 19:e.next=7;break;case 21:e.next=26;break;case 23:e.prev=23,e.t0=e.catch(5),i.e(e.t0);case 26:return e.prev=26,i.f(),e.finish(26);case 29:case"end":return e.stop()}}),e,null,[[5,23,26,29]])})));return function(t){return e.apply(this,arguments)}}(),[B]),V=Object(r.useCallback)(function(){var e=F(H().mark((function e(t){var n,r,i,o,a;return H().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=B[t].initials,r=n.annotation,R(h.a.setSelectedDisplayedSignatureIndex(t)),N.a.setToolMode(k),i=G(O),e.prev=5,i.s();case 7:if((o=i.n()).done){e.next=22;break}return a=o.value,e.next=11,a.setInitials(r);case 11:if(!a.hasLocation()){e.next=17;break}return e.next=14,a.addInitials();case 14:R(h.a.setSignatureMode(c.a.FULL_SIGNATURE)),e.next=20;break;case 17:return e.next=19,a.showInitialsPreview();case 19:R(h.a.setSignatureMode(c.a.INITIALS));case 20:e.next=7;break;case 22:e.next=27;break;case 24:e.prev=24,e.t0=e.catch(5),i.e(e.t0);case 27:return e.prev=27,i.f(),e.finish(27);case 30:case"end":return e.stop()}}),e,null,[[5,24,27,30]])})));return function(t){return e.apply(this,arguments)}}(),[B]),Z=Object(r.useCallback)(function(){var e=F(H().mark((function e(t){var n,r,i;return H().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:O[0].deleteSavedInitials(t),O[0].deleteSavedSignature(t),n=O[0].getSavedSignatures(),(r=A===t)&&(O.forEach((function(e){e.hidePreview(),e.setSignature(null),e.setInitials(null)})),N.a.setToolMode(u.a)),0===n.length?R(h.a.setSelectedDisplayedSignatureIndex(null)):(i=A,(r||t<A)&&(i=Math.max(0,A-1)),R(h.a.setSelectedDisplayedSignatureIndex(i)));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[A]);return i.a.createElement(l.a,{dataElement:s.a.SIGNATURE_LIST_PANEL,className:v()(D({Panel:!0,SignatureListPanel:!0,hideAddButton:y.length&&t===d.a.SMALL_SIZE},t,!0))},i.a.createElement("h2",{className:"signature-list-panel-header"},n("signatureListPanel.header")),i.a.createElement(T,{isDisabled:B.length>=w}),i.a.createElement(b.a,null),i.a.createElement(P,{savedSignatures:B,onFullSignatureSetHandler:z,onInitialsSetHandler:V,deleteHandler:Z,currentlySelectedSignature:A,isDeleteDisabled:x,signatureMode:E,panelSize:t}))};z.propTypes={panelSize:w.a.oneOf(Object.values(d.a))};var V=z;t.default=V}}]);
//# sourceMappingURL=chunk.78.js.map