{"version": 3, "sources": ["webpack:///./src/ui/src/components/SignatureListPanel/SignatureListPanel.scss?93e8", "webpack:///./src/ui/src/components/SignatureListPanel/SignatureListPanel.scss", "webpack:///./src/ui/src/components/SignatureListPanel/SavedSignatures.js", "webpack:///./src/ui/src/components/SignatureListPanel/SignatureAddButton.js", "webpack:///./src/ui/src/components/SignatureListPanel/SignatureListPanel.js", "webpack:///./src/ui/src/components/SignatureListPanel/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "SignatureRowContent", "React", "memo", "index", "onFullSignatureSetHandler", "onInitialsSetHandler", "isActive", "altText", "fullSignature", "initials", "isHoveredForDeletion", "signatureMode", "creatSignatureButton", "handler", "imgSrc", "signatureType", "className", "classNames", "SignatureModes", "FULL_SIGNATURE", "onClick", "alt", "src", "INITIALS", "displayName", "SavedSignatures", "props", "renderInitialsHeader", "savedSignatures", "delete<PERSON><PERSON><PERSON>", "currentlySelectedSignature", "isDeleteDisabled", "panelSize", "t", "useTranslation", "useState", "hoveredIndexToDelete", "setHoveredIndexToDelete", "isMobile", "isMobileSize", "some", "map", "signatureObject", "savedSignatureIndex", "isPanelSizeLarge", "PANEL_SIZES", "SMALL_SIZE", "isMobileSizeWithSmallPanel", "key", "<PERSON><PERSON>", "img", "aria<PERSON><PERSON><PERSON>", "dataElement", "onMouseOver", "onMouseLeave", "propTypes", "PropTypes", "oneOf", "Object", "values", "SignatureAddButton", "isDisabled", "dispatch", "useDispatch", "openSignatureModalWithFocus", "useFocusHandler", "actions", "setSignatureMode", "openElement", "DataElements", "SIGNATURE_MODAL", "buttonLabel", "useSelector", "state", "selectors", "getIsInitialsModeEnabled", "disabled", "label", "SIGNATURE_ADD_BUTTON", "SignatureListPanel", "getSavedSignatures", "shallowEqual", "maxSignaturesCount", "getMaxSignaturesCount", "displayedSignaturesFilterFunction", "getDisplayedSignaturesFilterFunction", "isSignatureDeleteButtonDisabled", "isElementDisabled", "savedInitials", "getSavedInitials", "selectedSignatureIndex", "getSelectedDisplayedSignatureIndex", "getSignatureMode", "mobilePanelSize", "getMobilePanelSize", "store", "useStore", "TOOL_NAME", "signatureToolArray", "core", "getToolsFromAllDocumentViewers", "useEffect", "signatureTool", "clearLocation", "setSignature", "setInitials", "savedSignaturesAndInitials", "setSavedSignaturesAndInitials", "mergedSignaturesAndInitals", "filter", "signature", "setMobilePanelSize", "onVisibilityChanged", "e", "activeTool", "getToolMode", "activeToolName", "name", "detail", "element", "isVisible", "panelNames", "SIGNATURE_LIST", "defaultTool", "setToolModeAndGroup", "addEventListener", "Events", "VISIBILITY_CHANGED", "removeEventListener", "useCallback", "annotation", "setSelectedDisplayedSignatureIndex", "setToolMode", "hasLocation", "addSignature", "showPreview", "addInitials", "showInitialsPreview", "deleteSignatureAndInitials", "deleteSavedInitials", "deleteSavedSignature", "remainingSignatures", "isDeletingSelectedSignature", "hidePreview", "newIndex", "Math", "max", "DataElementWrapper", "SIGNATURE_LIST_PANEL", "Divider"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,8nNAA+nN,KAGxpN0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,g7CCDvB,IAAMC,EAAsBC,IAAMC,MAAK,YAUjC,IATJC,EAAK,EAALA,MACAC,EAAyB,EAAzBA,0BACAC,EAAoB,EAApBA,qBACAC,EAAQ,EAARA,SACAC,EAAO,EAAPA,QACAC,EAAa,EAAbA,cACAC,EAAQ,EAARA,SACAC,EAAoB,EAApBA,qBACAC,EAAa,EAAbA,cAEMC,EAAuB,SAACC,EAASC,EAAQC,GAAa,OAC1D,4BACEC,UAAWC,IAAW,wBAAyB,CAC7C,aAAgBJ,EAChB,OAAUP,GAAYS,IAAkBJ,EACxC,kBAAmBD,GAClB,GAAF,OAAKK,IAAkBG,IAAeC,eAAiB,iBAAmB,aAC3EC,QAAS,kBAAMP,EAAQV,KAEvB,yBAAKkB,IAAKd,EAASe,IAAKR,MAI5B,OACE,yBAAKE,UAAU,2BACZJ,EAAqBR,EAA2BI,aAAa,EAAbA,EAAeM,OAAQI,IAAeC,gBACtFV,GAAYG,EAAqBP,EAAsBI,EAASK,OAAQI,IAAeK,cAK9FvB,EAAoBwB,YAAc,sBAGlC,IAAMC,EAAkB,SAACC,GACvB,IAeQC,EAdNC,EAQEF,EARFE,gBACAxB,EAOEsB,EAPFtB,0BACAC,EAMEqB,EANFrB,qBACAwB,EAKEH,EALFG,cACAC,EAIEJ,EAJFI,2BACAC,EAGEL,EAHFK,iBACApB,EAEEe,EAFFf,cACAqB,EACEN,EADFM,UAGMC,EAAMC,cAAND,EACoE,IAApBhC,IAAMkC,SAAS,MAAK,GAArEC,EAAoB,KAAEC,EAAuB,KAa9CC,EAAWC,cAEjB,OAAIX,EAAgB9C,OAAS,EACnB,yBAAKkC,UAAU,mBAbjBW,EAAuBC,EAAgBY,MAAK,YAAW,SAAR/B,YAEnD,yBAAKO,UAAU,yBACb,yBAAKA,UAAU,mBAAmBiB,EAAE,+CACnCN,GAAwB,yBAAKX,UAAU,kBAAkBiB,EAAE,8CAC5D,yBAAKjB,UAAU,oBAWfY,EAEGa,KAAI,SAACC,EAAiBvC,GAAK,MAAK,CAACuC,EAAiBvC,MAClDsC,KAAI,YAAwD,oBAApDjC,EAAa,EAAbA,cAAeC,EAAQ,EAARA,SAAYkC,EAAmB,KAC/CC,GAAoBZ,GAAaA,IAAca,IAAYC,WAC3DC,EAA6BT,GAAYN,IAAca,IAAYC,WACzE,OAAIF,GAAqBG,GAA8BjB,IAA+Ba,EAC5E,yBACNK,IAAKL,EACL3B,UAAU,iBAEV,kBAAChB,EAAmB,CAClBG,MAAOwC,EACPnC,cAAeA,EACfC,SAAUA,EACVL,0BAA2BA,EAC3BC,qBAAsBA,EACtBC,SAAUwB,IAA+Ba,EACzCpC,QAAO,UAAK0B,EAAE,wCAAuC,YAAIU,EAAsB,GAC/EjC,qBAAsB0B,IAAyBO,EAC/ChC,cAAeA,KAEfoB,GACA,kBAACkB,EAAA,EAAM,CACLjC,UAAU,cACVkC,IAAI,mBACJC,UAAWlB,EAAE,iBACbmB,YAAY,+BACZC,YAAa,kBAAMhB,EAAwBM,IAC3CW,aAAc,kBAAMjB,EAAwB,OAC5CjB,QAAS,WACPS,EAAcc,GACdN,EAAwB,UAM3B,SAKV,MAGTZ,EAAgBD,YAAc,kBAC9BC,EAAgB8B,UAAY,CAC1BvB,UAAWwB,IAAUC,MAAMC,OAAOC,OAAOd,OAG5B5C,UAAMC,KAAKuB,G,uiCCpH1B,IAAMmC,EAAqB,SAAH,GAAuB,IAAjBC,EAAU,EAAVA,WACtBC,EAAWC,cACV9B,EAAqB,EAAhBC,cAAgB,GAApB,GASF8B,EAA8BC,aAPT,WACpBJ,IACHC,EAASI,IAAQC,iBAAiBjD,IAAeC,iBACjD2C,EAASI,IAAQE,YAAYC,IAAaC,sBAOxCC,EADwBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,yBAAyBF,MAC5C,4CAA8C,kCAE1F,OACE,kBAACxB,EAAA,EAAM,CACLjC,UAAWC,IACT,qBACA,CAAE2D,SAAUf,IAEdgB,MAAO5C,EAAEsC,GACTnB,YAAaiB,IAAaS,qBAC1B1D,QAAS4C,KAIA/D,MAAMC,KAAK0D,G,o1BCtC1B,8lGAAAxF,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4uBAAAA,EAAA,gDAAAA,GAAA,oCAAAA,OAAA,qYAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,ygBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAoBA,IAAM2G,EAAqB,SAAH,GAAsB,IAAhB/C,EAAS,EAATA,UACrBC,EAAqB,EAAhBC,cAAgB,GAApB,GACFI,EAAWC,cAEXX,EAAkB4C,YAAYE,IAAUM,mBAAoBC,KAC5DC,EAAqBV,YAAYE,IAAUS,uBAC3CC,EAAoCZ,YAAYE,IAAUW,sCAC1DC,EAAkCd,aAAY,SAACC,GAAK,OAAKC,IAAUa,kBAAkBd,EAAO,mCAC5Fe,EAAgBhB,YAAYE,IAAUe,iBAAkBR,KACxDS,EAAyBlB,YAAYE,IAAUiB,oCAC/ChF,EAAgB6D,YAAYE,IAAUkB,kBACtCC,EAAkBrB,YAAYE,IAAUoB,oBAExCC,EAAQC,cACRC,EAAY,4BACZC,EAAqBC,IAAKC,+BAA+BH,GAE/DI,qBAAU,WACR,OAAO,WAAM,IACmC,EADnC,IACiBH,GAAkB,IAA9C,IAAK,EAAL,qBAAgD,KAArCI,EAAa,QACtBA,EAAcC,gBACdD,EAAcE,aAAa,MAC3BF,EAAcG,YAAY,OAC3B,kCAEF,IAGH,IAAsF,IAAlBxG,IAAMkC,SAAS,IAAG,GAA/EuE,EAA0B,KAAEC,EAA6B,KAChEN,qBAAU,WACR,IACMO,EADsBhF,EAAgBiF,QAAO,SAACC,EAAW3G,GAAK,OAAKiF,EAAkC0B,EAAW3G,MAC/DsC,KAAI,SAACqE,EAAW3G,GACrE,MAAO,CACLK,cAAesG,EACfrG,SAAU+E,EAAcrF,IAAU,SAGtCwG,EAA8BC,KAC7B,CAAChF,EAAiB4D,EAAeJ,IAEpCiB,qBAAU,WACJR,IAAoBhD,IAAYC,YAAcR,GAChDwB,EAASI,IAAQ6C,mBAAmBlE,IAAYC,eAEjD,CAAC4C,IAEJW,qBAAU,WACR,IAAMW,EAAsB,SAACC,GAC3B,IAAMC,EAAaf,IAAKgB,cAClBC,EAAiBF,aAAU,EAAVA,EAAYG,KACnC,EAA+BJ,EAAEK,OAAzBC,EAAO,EAAPA,QAASC,EAAS,EAATA,UACbD,IAAYE,IAAWC,gBAAmBF,GACxCJ,IAAmBnB,GAAamB,IAAmBO,KACrDC,YAAoB7B,EAAO4B,MAMjC,OADApJ,OAAOsJ,iBAAiBC,IAAOC,mBAAoBf,GAC5C,WACLzI,OAAOyJ,oBAAoBF,IAAOC,mBAAoBf,MAEvD,IAEH,IAAMlD,EAAWC,cAEXyC,EAAeyB,sBAAW,6BAAC,WAAO9H,GAAK,+EACnCK,EAAkBkG,EAA2BvG,GAA7CK,cACA0H,EAAe1H,EAAf0H,WACRpE,EAASI,IAAQiE,mCAAmChI,IACpDgG,IAAKiC,YAAYnC,GAAW,IACAC,GAAkB,yDAAtB,OAAbI,EAAa,kBAChBA,EAAcE,aAAa0B,GAAW,YACxC5B,EAAc+B,cAAe,CAAF,iCACvB/B,EAAcgC,eAAc,iDAE5BhC,EAAciC,cAAa,QACjCzE,EAASI,IAAQC,iBAAiBjD,IAAeC,iBAAiB,uMAGvE,mDAd+B,GAc7B,CAACuF,IAEED,EAAcwB,sBAAW,6BAAE,WAAO9H,GAAK,+EACnCM,EAAaiG,EAA2BvG,GAAxCM,SACAyH,EAAezH,EAAfyH,WACRpE,EAASI,IAAQiE,mCAAmChI,IACpDgG,IAAKiC,YAAYnC,GAAW,IACAC,GAAkB,yDAAtB,OAAbI,EAAa,kBAChBA,EAAcG,YAAYyB,GAAW,YACvC5B,EAAc+B,cAAe,CAAF,iCACvB/B,EAAckC,cAAa,QAGjC1E,EAASI,IAAQC,iBAAiBjD,IAAeC,iBAAiB,yCAE5DmF,EAAcmC,sBAAqB,QACzC3E,EAASI,IAAQC,iBAAiBjD,IAAeK,WAAW,uMAGjE,mDAjB8B,GAiB3B,CAACmF,IAECgC,EAA6BT,sBAAW,6BAAC,WAAO9H,GAAK,2EACzD+F,EAAmB,GAAGyC,oBAAoBxI,GAC1C+F,EAAmB,GAAG0C,qBAAqBzI,GACrC0I,EAAsB3C,EAAmB,GAAGlB,sBAC5C8D,EAA8BpD,IAA2BvF,KAG7D+F,EAAmB9G,SAAQ,SAACkH,GAC1BA,EAAcyC,cACdzC,EAAcE,aAAa,MAC3BF,EAAcG,YAAY,SAE5BN,IAAKiC,YAAYT,MAGgB,IAA/BkB,EAAoB/J,OACtBgF,EAASI,IAAQiE,mCAAmC,QAEhDa,EAAWtD,GACXoD,GAA+B3I,EAAQuF,KACzCsD,EAAWC,KAAKC,IAAI,EAAGxD,EAAyB,IAElD5B,EAASI,IAAQiE,mCAAmCa,KACrD,2CACF,mDAxB6C,GAwB3C,CAACtD,IAGJ,OACE,kBAACyD,EAAA,EAAkB,CAAC/F,YAAaiB,IAAa+E,qBAAsBpI,UAClEC,IAAW,EAAD,CACR,OAAS,EACT,oBAAsB,EACtB,cAAiBW,EAAgB9C,QAAUkD,IAAca,IAAYC,YACpEd,GAAY,KAGf,wBAAIhB,UAAU,+BACXiB,EAAE,8BAEL,kBAAC,EAAkB,CAAC4B,WAAY6C,EAA2B5H,QAAUoG,IACrE,kBAACmE,EAAA,EAAO,MACR,kBAAC,EAAe,CACdzH,gBAAiB8E,EACjBtG,0BAA2BoG,EAC3BnG,qBAAsBoG,EACtB5E,cAAe6G,EACf5G,2BAA4B4D,EAC5B3D,iBAAkBuD,EAClB3E,cAAeA,EACfqB,UAAWA,MAKnB+C,EAAmBxB,UAAY,CAC7BvB,UAAWwB,IAAUC,MAAMC,OAAOC,OAAOd,OAG5BkC,QClLAA", "file": "chunks/chunk.78.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./SignatureListPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SignatureAddButton{width:auto;padding:8px;color:var(--blue-5);border:1px solid var(--blue-5);border-radius:4px;margin:16px 0}.SignatureAddButton:not(.disabled):hover{color:var(--primary-button-hover);border-color:var(--primary-button-hover)}.SignatureAddButton.disabled{color:var(--blue-5);opacity:.5;cursor:not-allowed}.SignatureAddButton.disabled span{color:var(--blue-5)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureAddButton{margin:16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureAddButton:not(.disabled):hover{color:var(--blue-5);border:1px solid var(--blue-5)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureAddButton{margin:16px}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureAddButton:not(.disabled):hover{color:var(--blue-5);border:1px solid var(--blue-5)}}.SignatureListPanel{height:100%;display:flex;flex-direction:column}.SignatureListPanel .signature-list-panel-header{margin-top:0;font-size:16px;font-weight:700}.SignatureListPanel .signature-header{margin:16px 0;font-size:14px;font-weight:700}.SignatureListPanel .signature-list{padding:16px 2px 0;overflow:auto}.SignatureListPanel .signature-row{display:flex;flex-direction:row;height:48px;justify-content:space-between;align-items:center;margin-top:8px}.SignatureListPanel .signature-row .icon-button{padding:0;border:none;background-color:transparent;display:flex;align-items:center;justify-content:center;width:34px;height:34px;border-radius:4px;cursor:pointer}:host(:not([data-tabbing=true])) .SignatureListPanel .signature-row .icon-button,html:not([data-tabbing=true]) .SignatureListPanel .signature-row .icon-button{outline:none}.SignatureListPanel .signature-row .icon-button:hover{border:1px solid var(--blue-6);background:var(--gray-2)}.SignatureListPanel .signature-list-header{display:flex;flex-direction:row;align-items:center}.SignatureListPanel .signature-list-header .signature-title{flex-grow:2;margin-right:8px;min-width:160px;font-weight:var(--font-weight-bold)}.SignatureListPanel .signature-list-header .initials-title{flex-grow:1;max-width:65px;font-weight:var(--font-weight-bold)}.SignatureListPanel .signature-list-header .delete-spacer{width:40px}.SignatureListPanel .signature-row-container{display:flex;flex-direction:row;align-items:center;flex-grow:1}.SignatureListPanel .signature-row-container .signature-row-content{border:none;background-color:transparent;height:40px;padding:4px 8px;border:1px solid var(--lighter-border);border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-right:8px;background-color:var(--gray-0);flex-grow:2}:host(:not([data-tabbing=true])) .SignatureListPanel .signature-row-container .signature-row-content,html:not([data-tabbing=true]) .SignatureListPanel .signature-row-container .signature-row-content{outline:none}.SignatureListPanel .signature-row-container .signature-row-content img{max-width:100%;max-height:100%}.SignatureListPanel .signature-row-container .signature-row-content.interactable:hover{border-color:var(--blue-6)}.SignatureListPanel .signature-row-container .signature-row-content.interactable.active{background:var(--gray-2);border-color:var(--blue-5)}.SignatureListPanel .signature-row-container .signature-row-content.active{background:var(--tools-overlay-button-active)}.SignatureListPanel .signature-row-container .signature-row-content.focus-visible,.SignatureListPanel .signature-row-container .signature-row-content:focus-visible{outline:var(--focus-visible-outline)!important}.SignatureListPanel .signature-row-container .signature-row-content .signature-button-icon{width:20px;height:20px;margin-top:2px}.SignatureListPanel .signature-row-container .initials{max-width:65px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel{width:100%;position:relative}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel.hideAddButton .SignatureAddButton,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .initials-title,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-title,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel.small-size .Divider{display:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-list-panel-header,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-row{padding:0 16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-list{padding-bottom:16px;position:relative}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureListPanel .signature-list .signature-row{justify-content:center;margin-top:0;margin-bottom:8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel{width:100%;position:relative}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel.hideAddButton .SignatureAddButton,.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .initials-title,.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-title,.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel.small-size .Divider{display:none}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-list-panel-header,.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-row{padding:0 16px}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-list{padding-bottom:16px;position:relative}.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureListPanel .signature-list .signature-row{justify-content:center;margin-top:0;margin-bottom:8px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\nimport SignatureModes from 'constants/signatureModes';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport { PANEL_SIZES } from 'constants/panel';\nimport PropTypes from 'prop-types';\nimport Button from 'components/Button';\n\nconst SignatureRowContent = React.memo(({\n  index,\n  onFullSignatureSetHandler,\n  onInitialsSetHandler,\n  isActive,\n  altText,\n  fullSignature,\n  initials,\n  isHoveredForDeletion,\n  signatureMode,\n}) => {\n  const creatSignatureButton = (handler, imgSrc, signatureType) => (\n    <button\n      className={classNames('signature-row-content', {\n        'interactable': handler,\n        'active': isActive && signatureType === signatureMode,\n        'removal-hovered': isHoveredForDeletion,\n      }, `${signatureType === SignatureModes.FULL_SIGNATURE ? 'full-signature' : 'initials'}`)}\n      onClick={() => handler(index)}\n    >\n      <img alt={altText} src={imgSrc} />\n    </button>\n  );\n\n  return (\n    <div className='signature-row-container'>\n      {creatSignatureButton(onFullSignatureSetHandler, fullSignature?.imgSrc, SignatureModes.FULL_SIGNATURE)}\n      {initials && creatSignatureButton(onInitialsSetHandler, initials.imgSrc, SignatureModes.INITIALS)}\n    </div>\n  );\n});\n\nSignatureRowContent.displayName = 'SignatureRowContent';\n\n\nconst SavedSignatures = (props) => {\n  const {\n    savedSignatures,\n    onFullSignatureSetHandler,\n    onInitialsSetHandler,\n    deleteHandler,\n    currentlySelectedSignature,\n    isDeleteDisabled,\n    signatureMode,\n    panelSize,\n  } = props;\n\n  const { t } = useTranslation();\n  const [hoveredIndexToDelete, setHoveredIndexToDelete] = React.useState(null);\n\n  const renderSignatureListHeader = () => {\n    const renderInitialsHeader = savedSignatures.some(({ initials }) => initials);\n    return (\n      <div className='signature-list-header'>\n        <div className='signature-title'>{t('signatureListPanel.signatureList.signature')}</div>\n        {renderInitialsHeader && <div className='initials-title'>{t('signatureListPanel.signatureList.initials')}</div>}\n        <div className='delete-spacer'></div>\n      </div>\n    );\n  };\n\n  const isMobile = isMobileSize();\n\n  if (savedSignatures.length > 0) {\n    return (<div className='signature-list'>\n      {renderSignatureListHeader()}\n      {\n        savedSignatures\n          // Need to keep the index information from the original signature list\n          .map((signatureObject, index) => [signatureObject, index])\n          .map(([{ fullSignature, initials }, savedSignatureIndex]) => {\n            const isPanelSizeLarge = !panelSize || panelSize !== PANEL_SIZES.SMALL_SIZE;\n            const isMobileSizeWithSmallPanel = isMobile && panelSize === PANEL_SIZES.SMALL_SIZE;\n            if (isPanelSizeLarge || (isMobileSizeWithSmallPanel && currentlySelectedSignature === savedSignatureIndex)) {\n              return (<div\n                key={savedSignatureIndex}\n                className=\"signature-row\"\n              >\n                <SignatureRowContent\n                  index={savedSignatureIndex}\n                  fullSignature={fullSignature}\n                  initials={initials}\n                  onFullSignatureSetHandler={onFullSignatureSetHandler}\n                  onInitialsSetHandler={onInitialsSetHandler}\n                  isActive={currentlySelectedSignature === savedSignatureIndex}\n                  altText={`${t('option.toolsOverlay.signatureAltText')} ${savedSignatureIndex + 1}`}\n                  isHoveredForDeletion={hoveredIndexToDelete === savedSignatureIndex}\n                  signatureMode={signatureMode}\n                />\n                {!isDeleteDisabled && (\n                  <Button\n                    className=\"icon-button\"\n                    img=\"icon-delete-line\"\n                    ariaLabel={t('action.delete')}\n                    dataElement=\"defaultSignatureDeleteButton\"\n                    onMouseOver={() => setHoveredIndexToDelete(savedSignatureIndex)}\n                    onMouseLeave={() => setHoveredIndexToDelete(null)}\n                    onClick={() => {\n                      deleteHandler(savedSignatureIndex);\n                      setHoveredIndexToDelete(null);\n                    }}\n                  />\n                )}\n              </div>);\n            }\n            return null;\n          })\n      }\n    </div>);\n  }\n  return null;\n};\n\nSavedSignatures.displayName = 'SavedSignatures';\nSavedSignatures.propTypes = {\n  panelSize: PropTypes.oneOf(Object.values(PANEL_SIZES)),\n};\n\nexport default React.memo(SavedSignatures);", "import React from 'react';\nimport Button from '../Button';\nimport classNames from 'classnames';\nimport SignatureModes from 'constants/signatureModes';\nimport { useDispatch, useSelector } from 'react-redux';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { useTranslation } from 'react-i18next';\nimport DataElements from 'constants/dataElement';\nimport useFocusHandler from 'hooks/useFocusHandler';\n\nconst SignatureAddButton = ({ isDisabled }) => {\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const openSignatureModal = () => {\n    if (!isDisabled) {\n      dispatch(actions.setSignatureMode(SignatureModes.FULL_SIGNATURE));\n      dispatch(actions.openElement(DataElements.SIGNATURE_MODAL));\n    }\n  };\n\n  const openSignatureModalWithFocus = useFocusHandler(openSignatureModal);\n\n  const isInitialsModeEnabled = useSelector((state) => selectors.getIsInitialsModeEnabled(state));\n  const buttonLabel = isInitialsModeEnabled ? 'signatureListPanel.newSignatureAndInitial' : 'signatureListPanel.newSignature';\n\n  return (\n    <Button\n      className={classNames(\n        'SignatureAddButton',\n        { disabled: isDisabled },\n      )}\n      label={t(buttonLabel)}\n      dataElement={DataElements.SIGNATURE_ADD_BUTTON}\n      onClick={openSignatureModalWithFocus} />\n  );\n};\n\nexport default React.memo(SignatureAddButton);", "import React, { useEffect, useCallback } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useDispatch, useSelector, useStore, shallowEqual } from 'react-redux';\nimport DataElementWrapper from '../DataElementWrapper';\nimport defaultTool from 'constants/defaultTool';\nimport DataElements from 'constants/dataElement';\nimport SignatureModes from 'constants/signatureModes';\nimport { PANEL_SIZES, panelNames } from 'constants/panel';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport setToolModeAndGroup from 'helpers/setToolModeAndGroup';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport classNames from 'classnames';\nimport './SignatureListPanel.scss';\nimport Divider from '../ModularComponents/Divider';\nimport SavedSignatures from './SavedSignatures';\nimport SignatureAddButton from './SignatureAddButton';\nimport core from 'core';\nimport PropTypes from 'prop-types';\nimport Events from 'constants/events';\n\nconst SignatureListPanel = ({ panelSize }) => {\n  const [t] = useTranslation();\n  const isMobile = isMobileSize();\n\n  const savedSignatures = useSelector(selectors.getSavedSignatures, shallowEqual);\n  const maxSignaturesCount = useSelector(selectors.getMaxSignaturesCount);\n  const displayedSignaturesFilterFunction = useSelector(selectors.getDisplayedSignaturesFilterFunction);\n  const isSignatureDeleteButtonDisabled = useSelector((state) => selectors.isElementDisabled(state, 'defaultSignatureDeleteButton'));\n  const savedInitials = useSelector(selectors.getSavedInitials, shallowEqual);\n  const selectedSignatureIndex = useSelector(selectors.getSelectedDisplayedSignatureIndex);\n  const signatureMode = useSelector(selectors.getSignatureMode);\n  const mobilePanelSize = useSelector(selectors.getMobilePanelSize);\n\n  const store = useStore();\n  const TOOL_NAME = 'AnnotationCreateSignature';\n  const signatureToolArray = core.getToolsFromAllDocumentViewers(TOOL_NAME);\n\n  useEffect(() => {\n    return () => {\n      for (const signatureTool of signatureToolArray) {\n        signatureTool.clearLocation();\n        signatureTool.setSignature(null);\n        signatureTool.setInitials(null);\n      }\n    };\n  }, []);\n\n  // Saved signatures and initials are now in a single object. Merge them\n  const [savedSignaturesAndInitials, setSavedSignaturesAndInitials] = React.useState([]);\n  useEffect(() => {\n    const signaturesToDisplay = savedSignatures.filter((signature, index) => displayedSignaturesFilterFunction(signature, index));\n    const mergedSignaturesAndInitals = signaturesToDisplay.map((signature, index) => {\n      return {\n        fullSignature: signature,\n        initials: savedInitials[index] || null,\n      };\n    });\n    setSavedSignaturesAndInitials(mergedSignaturesAndInitals);\n  }, [savedSignatures, savedInitials, displayedSignaturesFilterFunction]);\n\n  useEffect(() => {\n    if (mobilePanelSize !== PANEL_SIZES.SMALL_SIZE && isMobile) {\n      dispatch(actions.setMobilePanelSize(PANEL_SIZES.SMALL_SIZE));\n    }\n  }, [selectedSignatureIndex]);\n\n  useEffect(() => {\n    const onVisibilityChanged = (e) => {\n      const activeTool = core.getToolMode();\n      const activeToolName = activeTool?.name;\n      const { element, isVisible } = e.detail;\n      if (element === panelNames.SIGNATURE_LIST && !isVisible) {\n        if (activeToolName === TOOL_NAME || activeToolName === defaultTool) {\n          setToolModeAndGroup(store, defaultTool);\n        }\n      }\n    };\n\n    window.addEventListener(Events.VISIBILITY_CHANGED, onVisibilityChanged);\n    return () => {\n      window.removeEventListener(Events.VISIBILITY_CHANGED, onVisibilityChanged);\n    };\n  }, []);\n\n  const dispatch = useDispatch();\n\n  const setSignature = useCallback(async (index) => {\n    const { fullSignature } = savedSignaturesAndInitials[index];\n    const { annotation } = fullSignature;\n    dispatch(actions.setSelectedDisplayedSignatureIndex(index));\n    core.setToolMode(TOOL_NAME);\n    for (const signatureTool of signatureToolArray) {\n      await signatureTool.setSignature(annotation);\n      if (signatureTool.hasLocation()) {\n        await signatureTool.addSignature();\n      } else {\n        await signatureTool.showPreview();\n        dispatch(actions.setSignatureMode(SignatureModes.FULL_SIGNATURE));\n      }\n    }\n  }, [savedSignaturesAndInitials]);\n\n  const setInitials = useCallback((async (index) => {\n    const { initials } = savedSignaturesAndInitials[index];\n    const { annotation } = initials;\n    dispatch(actions.setSelectedDisplayedSignatureIndex(index));\n    core.setToolMode(TOOL_NAME);\n    for (const signatureTool of signatureToolArray) {\n      await signatureTool.setInitials(annotation);\n      if (signatureTool.hasLocation()) {\n        await signatureTool.addInitials();\n        // Default mode is fullSignature. If we dont reset it here there can be a bug where\n        // we preview the initials, but apply the full signature\n        dispatch(actions.setSignatureMode(SignatureModes.FULL_SIGNATURE));\n      } else {\n        await signatureTool.showInitialsPreview();\n        dispatch(actions.setSignatureMode(SignatureModes.INITIALS));\n      }\n    }\n  }), [savedSignaturesAndInitials]);\n\n  const deleteSignatureAndInitials = useCallback(async (index) => {\n    signatureToolArray[0].deleteSavedInitials(index);\n    signatureToolArray[0].deleteSavedSignature(index);\n    const remainingSignatures = signatureToolArray[0].getSavedSignatures();\n    const isDeletingSelectedSignature = selectedSignatureIndex === index;\n\n    if (isDeletingSelectedSignature) {\n      signatureToolArray.forEach((signatureTool) => {\n        signatureTool.hidePreview();\n        signatureTool.setSignature(null);\n        signatureTool.setInitials(null);\n      });\n      core.setToolMode(defaultTool);\n    }\n\n    if (remainingSignatures.length === 0) {\n      dispatch(actions.setSelectedDisplayedSignatureIndex(null));\n    } else {\n      let newIndex = selectedSignatureIndex;\n      if (isDeletingSelectedSignature || index < selectedSignatureIndex) {\n        newIndex = Math.max(0, selectedSignatureIndex - 1);\n      }\n      dispatch(actions.setSelectedDisplayedSignatureIndex(newIndex));\n    }\n  }, [selectedSignatureIndex]);\n\n\n  return (\n    <DataElementWrapper dataElement={DataElements.SIGNATURE_LIST_PANEL} className={\n      classNames({\n        'Panel': true,\n        'SignatureListPanel': true,\n        'hideAddButton': savedSignatures.length && panelSize === PANEL_SIZES.SMALL_SIZE,\n        [panelSize]: true,\n      })\n    }>\n      <h2 className='signature-list-panel-header'>\n        {t('signatureListPanel.header')}\n      </h2>\n      <SignatureAddButton isDisabled={savedSignaturesAndInitials.length >= maxSignaturesCount} />\n      <Divider />\n      <SavedSignatures\n        savedSignatures={savedSignaturesAndInitials}\n        onFullSignatureSetHandler={setSignature}\n        onInitialsSetHandler={setInitials}\n        deleteHandler={deleteSignatureAndInitials}\n        currentlySelectedSignature={selectedSignatureIndex}\n        isDeleteDisabled={isSignatureDeleteButtonDisabled}\n        signatureMode={signatureMode}\n        panelSize={panelSize} />\n    </DataElementWrapper>\n  );\n};\n\nSignatureListPanel.propTypes = {\n  panelSize: PropTypes.oneOf(Object.values(PANEL_SIZES)),\n};\n\nexport default SignatureListPanel;", "import SignatureListPanel from './SignatureListPanel';\n\nexport default SignatureListPanel;"], "sourceRoot": ""}