{"version": 3, "sources": ["webpack:///./src/ui/src/components/LeftPanelOverlay/ThumbnailMoreOptionsPopup.js"], "names": ["ThumbnailMoreOptionsPopup", "selectedPageIndexes", "useSelector", "state", "selectors", "getSelectedThumbnailPageIndexes", "menu", "DataElements", "THUMBNAILS_CONTROL_MANIPULATE_POPUP", "trigger", "THUMBNAILS_CONTROL_MANIPULATE_POPUP_TRIGGER", "pageNumbers", "map", "i", "warn"], "mappings": "qKAuBeA,UAhBmB,WAChC,IAAMC,EAAsBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,gCAAgCF,MAE7F,OACE,kBAAC,IAAU,CACTG,KAAMC,IAAaC,oCACnBC,QAASF,IAAaG,6CAEtB,kBAAC,IAAwB,CACvBC,YAAaV,EAAoBW,KAAI,SAACC,GAAC,OAAKA,EAAI,KAChDC,MAAI", "file": "chunks/chunk.85.js", "sourcesContent": ["import React from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport FlyoutMenu from 'components/FlyoutMenu/FlyoutMenu';\nimport DataElements from 'constants/dataElement';\nimport PageManipulationControls from '../PageManipulationOverlay/PageManipulationControls';\n\nconst ThumbnailMoreOptionsPopup = () => {\n  const selectedPageIndexes = useSelector((state) => selectors.getSelectedThumbnailPageIndexes(state));\n\n  return (\n    <FlyoutMenu\n      menu={DataElements.THUMBNAILS_CONTROL_MANIPULATE_POPUP}\n      trigger={DataElements.THUMBNAILS_CONTROL_MANIPULATE_POPUP_TRIGGER}\n    >\n      <PageManipulationControls\n        pageNumbers={selectedPageIndexes.map((i) => i + 1)}\n        warn\n      />\n    </FlyoutMenu>\n  );\n};\n\nexport default ThumbnailMoreOptionsPopup;\n"], "sourceRoot": ""}