(window.webpackJsonp=window.webpackJsonp||[]).push([[91],{1971:function(e,a,t){"use strict";t.r(a);t(36),t(16),t(60),t(44);var n=t(0),o=t.n(n),r=t(84),s=t(4),l=t.n(s),c=t(6),i=t(5),y=t(2),m=t(68),p=t(71);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var u={formatType:l.a.string,isFlyoutItem:l.a.bool,secondaryLabel:l.a.string,style:l.a.object,className:l.a.string},b=Object(n.forwardRef)((function(e,a){var t=e.isFlyoutItem,n=e.formatType,s=e.secondaryLabel,l=e.style,u=e.className,b=Object(c.d)(),f="".concat(n.charAt(0).toLowerCase()).concat(n.slice(1)),O=p.b[f],g=O.dataElement,E=O.icon,w=O.title,L=function(){g===i.a.CELL_FORMAT_MORE_BUTTON&&(b(y.a.setFlyoutToggleElement(g)),b(y.a.toggleElement(i.a.CELL_FORMAT_MORE_FLYOUT)))};return t?o.a.createElement(m.a,d({},e,{ref:a,onClick:L,secondaryLabel:s,additionalClass:""})):o.a.createElement(r.a,{key:n,isActive:!1,onClick:L,dataElement:g,title:w,img:E,ariaPressed:!1,style:l,className:u})}));b.propTypes=u,b.displayName="CellFormatButton",a.default=b}}]);
//# sourceMappingURL=chunk.91.js.map