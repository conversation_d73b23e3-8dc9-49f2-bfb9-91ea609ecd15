{"repository": {"vcs": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": ""}, "config": {"analyzer": {"skip_excluded": true}, "excludes": {"paths": [{"pattern": "*Tests/**", "reason": "TEST_OF", "comment": "This directory contains test data which are not distributed."}], "scopes": [{"pattern": "devDependencies", "reason": "TEST_DEPENDENCY_OF", "comment": "Packages for development and testing only."}]}}}, "analyzer": {"start_time": "2023-09-26T14:25:29.606271838Z", "end_time": "2023-09-26T14:27:25.868040514Z", "environment": {"ort_version": "DOCKER-SNAPSHOT", "java_version": "********", "os": "Linux", "processors": 8, "max_memory": **********, "variables": {"JAVA_HOME": "/opt/java/openjdk", "ANDROID_HOME": "/opt/android-sdk"}, "tool_versions": {"NPM": "8.15.1"}}, "config": {"allow_dynamic_versions": false, "skip_excluded": true}, "result": {"projects": [{"id": "NPM::vite-components:0.0.0", "definition_file_path": "WebAppComponents/ClientApp/package.json", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "WebAppComponents/ClientApp"}, "homepage_url": "", "scope_names": ["dependencies"]}, {"id": "NuGet::FileSystemFile/FileSystemFile.csproj:", "definition_file_path": "FileSystemFile/FileSystemFile.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "FileSystemFile"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::GoogleGeoData/GoogleGeoData.csproj:", "definition_file_path": "GoogleGeoData/GoogleGeoData.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "GoogleGeoData"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::MessageBus/MessageBus.csproj:", "definition_file_path": "MessageBus/MessageBus.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "MessageBus"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::OsmGeoData/OsmGeoData.csproj:", "definition_file_path": "OsmGeoData/OsmGeoData.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "OsmGeoData"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::Storage/Storage.csproj:", "definition_file_path": "Storage/Storage.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "Storage"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::WebApp/WebApp.csproj:", "definition_file_path": "WebApp/WebApp.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "WebApp"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::WebAppComponents/WebAppComponents.csproj:", "definition_file_path": "WebAppComponents/WebAppComponents.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "WebAppComponents"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::WebAppMigrations/WebAppMigrations.csproj:", "definition_file_path": "WebAppMigrations/WebAppMigrations.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "WebAppMigrations"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::ZitadelApi/ZitadelApi.csproj:", "definition_file_path": "ZitadelApi/ZitadelApi.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": "ZitadelApi"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "Unmanaged::WebApp:a6d5d937f96598e270ca438deab9ab8fe0919d00", "definition_file_path": "", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebApp", "revision": "a6d5d937f96598e270ca438deab9ab8fe0919d00", "path": ""}, "homepage_url": "", "scope_names": []}], "packages": [{"id": "NPM::date-fns:2.30.0", "purl": "pkg:npm/date-fns@2.30.0", "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Modern JavaScript date utility library", "homepage_url": "https://github.com/date-fns/date-fns#readme", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/date-fns/-/date-fns-2.30.0.tgz", "hash": {"value": "f367e644839ff57894ec6ac480de40cae4b0f4d0", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/date-fns/date-fns.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/date-fns/date-fns.git", "revision": "", "path": ""}}, {"id": "NPM::lit:2.8.0", "purl": "pkg:npm/lit@2.8.0", "authors": ["Google LLC"], "declared_licenses": ["BSD-3-<PERSON><PERSON>"], "declared_licenses_processed": {"spdx_expression": "BSD-3-<PERSON><PERSON>"}, "description": "A library for building fast, lightweight web components", "homepage_url": "https://lit.dev/", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/lit/-/lit-2.8.0.tgz", "hash": {"value": "4d838ae03059bf9cafa06e5c61d8acc0081e974e", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/lit/lit.git", "revision": "51c431ee2a4c28a6e1a2fe6b0069ad24c7df0df3", "path": "packages/lit"}, "vcs_processed": {"type": "Git", "url": "https://github.com/lit/lit.git", "revision": "51c431ee2a4c28a6e1a2fe6b0069ad24c7df0df3", "path": "packages/lit"}}, {"id": "NPM::lit-element:3.3.3", "purl": "pkg:npm/lit-element@3.3.3", "authors": ["Google LLC"], "declared_licenses": ["BSD-3-<PERSON><PERSON>"], "declared_licenses_processed": {"spdx_expression": "BSD-3-<PERSON><PERSON>"}, "description": "A simple base class for creating fast, lightweight web components", "homepage_url": "https://lit.dev/", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/lit-element/-/lit-element-3.3.3.tgz", "hash": {"value": "10bc19702b96ef5416cf7a70177255bfb17b3209", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/lit/lit.git", "revision": "51c431ee2a4c28a6e1a2fe6b0069ad24c7df0df3", "path": "packages/lit-element"}, "vcs_processed": {"type": "Git", "url": "https://github.com/lit/lit.git", "revision": "51c431ee2a4c28a6e1a2fe6b0069ad24c7df0df3", "path": "packages/lit-element"}}, {"id": "NPM::lit-html:2.8.0", "purl": "pkg:npm/lit-html@2.8.0", "authors": ["Google LLC"], "declared_licenses": ["BSD-3-<PERSON><PERSON>"], "declared_licenses_processed": {"spdx_expression": "BSD-3-<PERSON><PERSON>"}, "description": "HTML templates literals in JavaScript", "homepage_url": "https://lit.dev/", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/lit-html/-/lit-html-2.8.0.tgz", "hash": {"value": "96456a4bb4ee717b9a7d2f94562a16509d39bffa", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/lit/lit.git", "revision": "51c431ee2a4c28a6e1a2fe6b0069ad24c7df0df3", "path": "packages/lit-html"}, "vcs_processed": {"type": "Git", "url": "https://github.com/lit/lit.git", "revision": "51c431ee2a4c28a6e1a2fe6b0069ad24c7df0df3", "path": "packages/lit-html"}}, {"id": "NPM::regenerator-runtime:0.14.0", "purl": "pkg:npm/regenerator-runtime@0.14.0", "authors": ["<PERSON>"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "homepage_url": "https://github.com/facebook/regenerator/tree/main#readme", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.0.tgz", "hash": {"value": "5e19d68eb12d486f797e15a3c6a918f7cec5eb45", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/facebook/regenerator.git#main", "revision": "314889174f08cd754421cdbaa3529c27fac8826a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/facebook/regenerator.git", "revision": "314889174f08cd754421cdbaa3529c27fac8826a", "path": ""}}, {"id": "NPM:@babel:runtime:7.22.10", "purl": "pkg:npm/%40babel/runtime@7.22.10", "authors": ["The Babel Team"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "babel's modular runtime helpers", "homepage_url": "https://babel.dev/docs/en/next/babel-runtime", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.10.tgz", "hash": {"value": "ae3e9631fd947cb7e3610d3e9d8fef5f76696682", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "https://github.com/babel/babel.git", "revision": "", "path": "packages/babel-runtime"}, "vcs_processed": {"type": "Git", "url": "https://github.com/babel/babel.git", "revision": "", "path": "packages/babel-runtime"}}, {"id": "NPM:@fortawesome:fontawesome-common-types:6.4.2", "purl": "pkg:npm/%40fortawesome/fontawesome-common-types@6.4.2", "authors": ["The Font Awesome Team"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "The iconic font, CSS, and SVG framework", "homepage_url": "https://fontawesome.com", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/@fortawesome/fontawesome-common-types/-/fontawesome-common-types-6.4.2.tgz", "hash": {"value": "1766039cad33f8ad87f9467b98e0d18fbc8f01c5", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/FortAwesome/Font-Awesome.git", "revision": "c85df5af6abc698707f3c9c0f6a8f78ec2cdf5f7", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/FortAwesome/Font-Awesome.git", "revision": "c85df5af6abc698707f3c9c0f6a8f78ec2cdf5f7", "path": ""}}, {"id": "NPM:@fortawesome:fontawesome-free:6.4.2", "purl": "pkg:npm/%40fortawesome/fontawesome-free@6.4.2", "authors": ["The Font Awesome Team"], "declared_licenses": ["(CC-BY-4.0 AND OFL-1.1 AND MIT)"], "declared_licenses_processed": {"spdx_expression": "CC-BY-4.0 AND MIT AND OFL-1.1"}, "description": "The iconic font, CSS, and SVG framework", "homepage_url": "https://fontawesome.com", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/@fortawesome/fontawesome-free/-/fontawesome-free-6.4.2.tgz", "hash": {"value": "36b6a9cb5ffbecdf89815c94d0c0ffa489ac5ecb", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/FortAwesome/Font-Awesome.git", "revision": "c85df5af6abc698707f3c9c0f6a8f78ec2cdf5f7", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/FortAwesome/Font-Awesome.git", "revision": "c85df5af6abc698707f3c9c0f6a8f78ec2cdf5f7", "path": ""}}, {"id": "NPM:@fortawesome:fontawesome-svg-core:6.4.2", "purl": "pkg:npm/%40fortawesome/fontawesome-svg-core@6.4.2", "authors": ["The Font Awesome Team"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "The iconic font, CSS, and SVG framework", "homepage_url": "https://fontawesome.com", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/@fortawesome/fontawesome-svg-core/-/fontawesome-svg-core-6.4.2.tgz", "hash": {"value": "37f4507d5ec645c8b50df6db14eced32a6f9be09", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/FortAwesome/Font-Awesome.git", "revision": "c85df5af6abc698707f3c9c0f6a8f78ec2cdf5f7", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/FortAwesome/Font-Awesome.git", "revision": "c85df5af6abc698707f3c9c0f6a8f78ec2cdf5f7", "path": ""}}, {"id": "NPM:@fortawesome:free-solid-svg-icons:6.4.2", "purl": "pkg:npm/%40fortawesome/free-solid-svg-icons@6.4.2", "authors": ["The Font Awesome Team"], "declared_licenses": ["(CC-BY-4.0 AND MIT)"], "declared_licenses_processed": {"spdx_expression": "CC-BY-4.0 AND MIT"}, "description": "The iconic font, CSS, and SVG framework", "homepage_url": "https://fontawesome.com", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/@fortawesome/free-solid-svg-icons/-/free-solid-svg-icons-6.4.2.tgz", "hash": {"value": "33a02c4cb6aa28abea7bc082a9626b7922099df4", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/FortAwesome/Font-Awesome.git", "revision": "c85df5af6abc698707f3c9c0f6a8f78ec2cdf5f7", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/FortAwesome/Font-Awesome.git", "revision": "c85df5af6abc698707f3c9c0f6a8f78ec2cdf5f7", "path": ""}}, {"id": "NPM:@lit:reactive-element:1.6.3", "purl": "pkg:npm/%40lit/reactive-element@1.6.3", "authors": ["Google LLC"], "declared_licenses": ["BSD-3-<PERSON><PERSON>"], "declared_licenses_processed": {"spdx_expression": "BSD-3-<PERSON><PERSON>"}, "description": "A simple low level base class for creating fast, lightweight web components", "homepage_url": "https://lit.dev/", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/@lit/reactive-element/-/reactive-element-1.6.3.tgz", "hash": {"value": "25b4eece2592132845d303e091bad9b04cdcfe03", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/lit/lit.git", "revision": "51c431ee2a4c28a6e1a2fe6b0069ad24c7df0df3", "path": "packages/reactive-element"}, "vcs_processed": {"type": "Git", "url": "https://github.com/lit/lit.git", "revision": "51c431ee2a4c28a6e1a2fe6b0069ad24c7df0df3", "path": "packages/reactive-element"}}, {"id": "NPM:@lit-labs:ssr-dom-shim:1.1.1", "purl": "pkg:npm/%40lit-labs/ssr-dom-shim@1.1.1", "authors": ["Google LLC"], "declared_licenses": ["BSD-3-<PERSON><PERSON>"], "declared_licenses_processed": {"spdx_expression": "BSD-3-<PERSON><PERSON>"}, "description": "DOM shim for Lit Server Side Rendering (SSR)", "homepage_url": "https://github.com/lit/lit/tree/main/packages/labs/ssr-dom-shim", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/@lit-labs/ssr-dom-shim/-/ssr-dom-shim-1.1.1.tgz", "hash": {"value": "64df34e2f12e68e78ac57e571d25ec07fa460ca9", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/lit/lit.git", "revision": "36544f5e3eda494a0eebaec85ce691f9e6e5c2ad", "path": "packages/labs/ssr-dom-shim"}, "vcs_processed": {"type": "Git", "url": "https://github.com/lit/lit.git", "revision": "36544f5e3eda494a0eebaec85ce691f9e6e5c2ad", "path": "packages/labs/ssr-dom-shim"}}, {"id": "NPM:@open-wc:lit-helpers:0.6.0", "purl": "pkg:npm/%40open-wc/lit-helpers@0.6.0", "authors": ["open-wc"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Helpers and utils for lit-html and lit-element.", "homepage_url": "https://github.com/open-wc/open-wc/tree/master/packages/lit-helpers", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/@open-wc/lit-helpers/-/lit-helpers-0.6.0.tgz", "hash": {"value": "05bd6fbffa37cae138f6786141cd40f8359f2970", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "git+https://github.com/open-wc/open-wc.git", "revision": "57ddb3ccfff6b00468d3a7ebabbc15cfe966f7a9", "path": "packages/lit-helpers"}, "vcs_processed": {"type": "Git", "url": "https://github.com/open-wc/open-wc.git", "revision": "57ddb3ccfff6b00468d3a7ebabbc15cfe966f7a9", "path": "packages/lit-helpers"}}, {"id": "NPM:@types:trusted-types:2.0.3", "purl": "pkg:npm/%40types/trusted-types@2.0.3", "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "TypeScript definitions for trusted-types", "homepage_url": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/trusted-types", "binary_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "source_artifact": {"url": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.3.tgz", "hash": {"value": "a136f83b0758698df454e328759dbd3d44555311", "algorithm": "SHA-1"}}, "vcs": {"type": "Git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "revision": "", "path": "types/trusted-types"}, "vcs_processed": {"type": "Git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "revision": "", "path": "types/trusted-types"}}, {"id": "NuGet::Dapper:2.0.123", "purl": "pkg:nuget/Dapper@2.0.123", "authors": ["<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "A high performance Micro-ORM supporting SQL Server, MySQL, Sqlite, SqlCE, Firebird etc..", "homepage_url": "https://github.com/DapperLib/Dapper", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/dapper/2.0.123/dapper.2.0.123.nupkg", "hash": {"value": "fb0dfca328dc8c4a0f561f48fb4e9a2a2d370ab05389f6cc02075eb58409295f373e3eea93916e4529f46db6fab5809de33910b758486441c8d34a4d3f78b4f5", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/DapperLib/Dapper", "revision": "2a837ad7d036671ac9a75c9945bb970aa41b7de9", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/DapperLib/Dapper.git", "revision": "2a837ad7d036671ac9a75c9945bb970aa41b7de9", "path": ""}}, {"id": "NuGet::FluentMigrator:3.3.2", "purl": "pkg:nuget/FluentMigrator@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator/3.3.2/fluentmigrator.3.3.2.nupkg", "hash": {"value": "5edffe5b42e91f165f99eab21116c760726973696505161c3e6ddf2b640d5ca1ed5e25990198ae9983e331c526a1149e9bf14816fb2c58813b7c585cbedbfce3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet::Grpc:2.46.6", "purl": "pkg:nuget/Grpc@2.46.6", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Metapackage for gRPC C#", "homepage_url": "https://github.com/grpc/grpc", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc/2.46.6/grpc.2.46.6.nupkg", "hash": {"value": "07a744a2c4a0c9266f15805439f83d2e014e697574774d63df78e848df4d18dc70aaa81804951e5a577e2571fceffedf0e1deaf4f6845dbb223778067619a566", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc.git", "revision": "", "path": ""}}, {"id": "NuGet::IdentityModel:6.0.0", "purl": "pkg:nuget/IdentityModel@6.0.0", "authors": ["<PERSON><PERSON>,<PERSON>"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "OpenID Connect & OAuth 2.0 client library", "homepage_url": "https://github.com/IdentityModel/IdentityModel", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/identitymodel/6.0.0/identitymodel.6.0.0.nupkg", "hash": {"value": "d12653771f18c56621909879a10493da4109f9b6d474a5eb023d5d47d21c9fda7252b473d715788d24f200469fd5f93c3b1156f25e1be1f6eb0cc1240026959b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/IdentityModel/IdentityModel", "revision": "2c10f4b0f75a2b1cc3e69d9bae8298394cc093a1", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/IdentityModel/IdentityModel.git", "revision": "2c10f4b0f75a2b1cc3e69d9bae8298394cc093a1", "path": ""}}, {"id": "NuGet::Npgsql:8.0.0-preview.4", "purl": "pkg:nuget/Npgsql@8.0.0-preview.4", "authors": ["<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>"], "declared_licenses": ["PostgreSQL"], "declared_licenses_processed": {"spdx_expression": "PostgreSQL"}, "description": "Npgsql is the open source .NET data provider for PostgreSQL.", "homepage_url": "https://github.com/npgsql/npgsql", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/npgsql/8.0.0-preview.4/npgsql.8.0.0-preview.4.nupkg", "hash": {"value": "a6cf443ba62d8c3007643c544208429e545a8a3bfc470a83f3aefe9a419d6988b58a8b012b0a268d4d92429ac24f6cc5076b5bd8fc4abe7e0733ba2a72b3fdda", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/npgsql/npgsql", "revision": "03f3c85f5f3e34ce62d02fd6430a4950ca379629", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/npgsql/npgsql.git", "revision": "03f3c85f5f3e34ce62d02fd6430a4950ca379629", "path": ""}}, {"id": "NuGet::Serilog:2.12.0", "purl": "pkg:nuget/Serilog@2.12.0", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Simple .NET logging with fully-structured events", "homepage_url": "https://serilog.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog/2.12.0/serilog.2.12.0.nupkg", "hash": {"value": "3546b98340b8399a8ca778b9b82072833ade67b2a016fe261f5a93a3f9afda222a1f432b146dc06edddb68e6bd32bffa4a9aacaae03205c365b43f88044111fd", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog.git", "revision": "9f863012f54c3d61ca5f660c646e4f06f0bec4fa", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog.git", "revision": "9f863012f54c3d61ca5f660c646e4f06f0bec4fa", "path": ""}}, {"id": "NuGet::Serilog:3.0.1", "purl": "pkg:nuget/Serilog@3.0.1", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Simple .NET logging with fully-structured events", "homepage_url": "https://serilog.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog/3.0.1/serilog.3.0.1.nupkg", "hash": {"value": "a75812368248026026257a0642d6fa7be99ae1b766810b5559e9baa004b268fc3e6ad0ffefac795455aecb9c016cab41dab3dc50eedb399f34086b793ea49883", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog.git", "revision": "5e93f0de0da3266489f1b71872810c369198783c", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog.git", "revision": "5e93f0de0da3266489f1b71872810c369198783c", "path": ""}}, {"id": "NuGet::Serilog:3.0.2-dev-02044", "purl": "pkg:nuget/Serilog@3.0.2-dev-02044", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Simple .NET logging with fully-structured events", "homepage_url": "https://serilog.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog/3.0.2-dev-02044/serilog.3.0.2-dev-02044.nupkg", "hash": {"value": "f6a3a1178a79b0fb3bef5ed085ed04c034bc71c0af0ed95ee2f396625f9ab5daed088ca9494722d81a7902e2aaa1d28d97a9c44eb0652521688b6ea1c10e5baf", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog.git", "revision": "a28a392e96fd35521f7998fdf2d77994d39a7db0", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog.git", "revision": "a28a392e96fd35521f7998fdf2d77994d39a7db0", "path": ""}}, {"id": "NuGet::SharpCompress:0.33.0", "purl": "pkg:nuget/SharpCompress@0.33.0", "authors": ["<PERSON>"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "SharpCompress is a compression library for NET Standard 2.0/2.1/NET 6.0/NET 7.0 that can unrar, decompress 7zip, decompress xz, zip/unzip, tar/untar lzip/unlzip, bzip2/unbzip2 and gzip/ungzip with forward-only reading and file random access APIs. Write support for zip/tar/bzip2/gzip is implemented.", "homepage_url": "https://github.com/adamhathcock/sharpcompress", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/sharpcompress/0.33.0/sharpcompress.0.33.0.nupkg", "hash": {"value": "c5faa771877a00f5eeac90f54c6300660eb6b6edd5dc8d78e99b56566ee120d94a3f453be9c27abe5a0a88d10e3821389467e316d83dfad0833f4447e23c1004", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/adamhathcock/sharpcompress", "revision": "813d9eace361eb6a8f005dd05aa6f7c1cc3bbbbe", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/adamhathcock/sharpcompress.git", "revision": "813d9eace361eb6a8f005dd05aa6f7c1cc3bbbbe", "path": ""}}, {"id": "NuGet::SqlKata:3.0.0-beta", "purl": "pkg:nuget/SqlKata@3.0.0-beta", "authors": ["<PERSON>"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "A powerful Dynamic Sql Query Builder supporting Sql Server, MySql, PostgreSql, Oracle and Firebird", "homepage_url": "https://github.com/sqlkata/querybuilder", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/sqlkata/3.0.0-beta/sqlkata.3.0.0-beta.nupkg", "hash": {"value": "6ad5e9e56d192524cfe092f9a9a7f3429650f5a5da9ea4387dac3a2d2a0d3693003d237f5c28c826a50d15aa1d7904142d0a3e0c1ad7c13bb4daf29cb008ee24", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/sqlkata/querybuilder", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/sqlkata/querybuilder.git", "revision": "", "path": ""}}, {"id": "NuGet::Zitadel:5.2.25", "purl": "pkg:nuget/Zitadel@5.2.25", "authors": ["<PERSON>, smartive AG"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "This package contains the library for authentication and authorization", "homepage_url": "https://github.com/smartive/zitadel-net", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/zitadel/5.2.25/zitadel.5.2.25.nupkg", "hash": {"value": "1cfe852513922df632f7cc8fab1347d406d39912d1a8e23b3a2fe4036022b37094abdcb414c6248bae4283e71939e15fb875296c9eb951d7d0d8e162a8c7fd10", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/smartive/zitadel-net.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/smartive/zitadel-net.git", "revision": "", "path": ""}}, {"id": "NuGet::jose-jwt:4.1.0", "purl": "pkg:nuget/jose-jwt@4.1.0", "authors": ["<PERSON>"], "declared_licenses": ["https://raw.github.com/dvse<PERSON><PERSON><PERSON>/jose-jwt/master/LICENSE"], "declared_licenses_processed": {"unmapped": ["https://raw.github.com/dvse<PERSON><PERSON><PERSON>/jose-jwt/master/LICENSE"]}, "description": "Javascript Object Signing and Encryption (JOSE), JSON Web Token (JWT), JSON Web Encryption (JWE) and JSON Web Key (JWK) Implementation for .NET ", "homepage_url": "https://github.com/dvse<PERSON><PERSON><PERSON>/jose-jwt", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/jose-jwt/4.1.0/jose-jwt.4.1.0.nupkg", "hash": {"value": "777a131853accf1188cb6eb49eaf6dac4f20d99c6bce28750edc90cb7934e315d788ab95207559eee7f243335611a0454e830faa2ac4fbdd803b4386fe225766", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dvse<PERSON><PERSON><PERSON>/jose-jwt.git", "revision": "", "path": ""}}, {"id": "NuGet:Azure:Core:1.25.0", "purl": "pkg:nuget/Azure.Core@1.25.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "This is the implementation of the Azure Client Pipeline", "homepage_url": "https://github.com/Azure/azure-sdk-for-net/blob/Azure.Core_1.25.0/sdk/core/Azure.Core/README.md", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/azure.core/1.25.0/azure.core.1.25.0.nupkg", "hash": {"value": "16eaf2dc43fefefff80512dba079e89876f4a947d00d0e4678866872530b63a429cf946afcd494b048ee9c6c0d7b35ae265c3491bb7454beceabb960b9301a58", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/Azure/azure-sdk-for-net", "revision": "c8aaee521e662ddfb238d5ad1f2f9a79233f97f6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/Azure/azure-sdk-for-net.git", "revision": "c8aaee521e662ddfb238d5ad1f2f9a79233f97f6", "path": ""}}, {"id": "NuGet:Azure:Identity:1.7.0", "purl": "pkg:nuget/Azure.Identity@1.7.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "This is the implementation of the Azure SDK Client Library for Azure Identity", "homepage_url": "https://github.com/Azure/azure-sdk-for-net/blob/Azure.Identity_1.7.0/sdk/identity/Azure.Identity/README.md", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/azure.identity/1.7.0/azure.identity.1.7.0.nupkg", "hash": {"value": "542dbf71e5bef13e1c9e4f873d9147bc0b385b5a2ee9514f8c99c5b0368d7ea29607b0925e21e9f4538e1fd5716a9cc399c1f01e5bda80aa1971a88179ebbda3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/Azure/azure-sdk-for-net", "revision": "3627e3cbc75c628f659d033ed9270c9d02ab9038", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/Azure/azure-sdk-for-net.git", "revision": "3627e3cbc75c628f659d033ed9270c9d02ab9038", "path": ""}}, {"id": "NuGet:Ben:Demystifier:0.4.1", "purl": "pkg:nuget/Ben.Demystifier@0.4.1", "authors": ["<PERSON>"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "High performance understanding for stack traces (Make error logs more productive)", "homepage_url": "https://github.com/benaadams/Ben.Demystifier", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/ben.demystifier/0.4.1/ben.demystifier.0.4.1.nupkg", "hash": {"value": "b7d069c5f3afdd117a2f7c5dca62eed015b1074bd0bceca6d28bbf160e91cc3974f1a9e26ef7ff6ea4ca3a63468618486a7547e5cbe94b9573779bbf1b3ebd93", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/benaadams/Ben.Demystifier", "revision": "ca0293534037c88f29a4d4460de6d9c62f4ee9ad", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/benaadams/Ben.Demystifier.git", "revision": "ca0293534037c88f29a4d4460de6d9c62f4ee9ad", "path": ""}}, {"id": "NuGet:BouncyCastle:Cryptography:2.2.1", "purl": "pkg:nuget/BouncyCastle.Cryptography@2.2.1", "authors": ["Legion of the Bouncy Castle Inc."], "declared_licenses": ["https://www.nuget.org/packages/BouncyCastle.Cryptography/2.2.1/license"], "declared_licenses_processed": {"unmapped": ["https://www.nuget.org/packages/BouncyCastle.Cryptography/2.2.1/license"]}, "description": "BouncyCastle.NET is a popular cryptography library for .NET", "homepage_url": "https://www.bouncycastle.org/csharp/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/bouncycastle.cryptography/2.2.1/bouncycastle.cryptography.2.2.1.nupkg", "hash": {"value": "99d01493b88538a17dd516acfcf3091acd08ebca169d03d93128135534afeec54cdad1417befefbdffcca308b2bc4330f47bf9443db4c84cb8c9770b4df9193d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/bcgit/bc-csharp", "revision": "b9c0074fb1b1b210182bba31d236664ea9ca37a8", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/bcgit/bc-csharp.git", "revision": "b9c0074fb1b1b210182bba31d236664ea9ca37a8", "path": ""}}, {"id": "NuGet:Elastic:CommonSchema:8.6.1", "purl": "pkg:nuget/Elastic.CommonSchema@8.6.1", "authors": ["Elastic and contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Maps Elastic Common Schema (ECS) to .NET types including (de)serialization using System.Text.Json", "homepage_url": "https://github.com/elastic/ecs-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/elastic.commonschema/8.6.1/elastic.commonschema.8.6.1.nupkg", "hash": {"value": "7c79fa525476dcc024e4c8b31bd6e9a1783c03612fa5abda377b201735eb0d09eb4ac78a0e3883129e37bb740c1851fb427eab864f1b1a65bd59917c12ec7694", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/elastic/ecs-dotnet", "revision": "88f2bc81a0b7440e4059e323e610bb03df61862c", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/elastic/ecs-dotnet.git", "revision": "88f2bc81a0b7440e4059e323e610bb03df61862c", "path": ""}}, {"id": "NuGet:Elastic.CommonSchema:Serilog:8.6.1", "purl": "pkg:nuget/Elastic.CommonSchema.Serilog@8.6.1", "authors": ["Elastic and contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Serilog TextFormatter that formats log events in accordance with Elastic Common Schema (ECS).", "homepage_url": "https://github.com/elastic/ecs-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/elastic.commonschema.serilog/8.6.1/elastic.commonschema.serilog.8.6.1.nupkg", "hash": {"value": "4fb21858852bc3a52f01a6ebda68d0998beb82d28baae6983634c313e398412d42ea8ea441b82146d5e9750236b2c189f64b59ed4e6cc8cf649b57c82cce5fd4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/elastic/ecs-dotnet", "revision": "88f2bc81a0b7440e4059e323e610bb03df61862c", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/elastic/ecs-dotnet.git", "revision": "88f2bc81a0b7440e4059e323e610bb03df61862c", "path": ""}}, {"id": "NuGet:FluentMigrator:Abstractions:3.3.2", "purl": "pkg:nuget/FluentMigrator.Abstractions@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.abstractions/3.3.2/fluentmigrator.abstractions.3.3.2.nupkg", "hash": {"value": "26f3e805f8827d06a771765f8755d87bffcee83ae2840c59a41c64ca314c8cb4fdfbb14d2e3a561941b12212a80990dd4b1fa063c8cc8e39d0762158d0ce7dec", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator:Runner:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner/3.3.2/fluentmigrator.runner.3.3.2.nupkg", "hash": {"value": "a5a4585a510934a4cddba4260653c562519cc443372092109a682d860d74640b27f7405b4782d7ada4c7b19eb8166d4bf1126221bd20c58c7de9496a43d433cc", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Extensions:Oracle:3.3.2", "purl": "pkg:nuget/FluentMigrator.Extensions.Oracle@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.extensions.oracle/3.3.2/fluentmigrator.extensions.oracle.3.3.2.nupkg", "hash": {"value": "f6d6a545f38f94d3c7749460ac22c49edbc8d54262cf1569bcee384c8c21a0762e0b9b685b8b23825ab74622a17218a29c1dbf61fa0a43247d366ef2b8f5c53a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Extensions:Postgres:3.3.2", "purl": "pkg:nuget/FluentMigrator.Extensions.Postgres@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.extensions.postgres/3.3.2/fluentmigrator.extensions.postgres.3.3.2.nupkg", "hash": {"value": "1857a0149d14e7b8182baaa1056c0862277522c84b01050f3dc4b1d9f7c8c4d577620bb44ecf8d832d53b6c8d1dd7a3cb160bd8484066505d6d97a4d162ea33c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Extensions:SqlAnywhere:3.3.2", "purl": "pkg:nuget/FluentMigrator.Extensions.SqlAnywhere@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.extensions.sqlanywhere/3.3.2/fluentmigrator.extensions.sqlanywhere.3.3.2.nupkg", "hash": {"value": "93c5bf7346b2d21680cea95d88d024210f93eb6913c16348e0be0f0e349c289e49bcbfa7ac6b9c4beeba7365c85f83d714fadfd663766af45992b584d2f35713", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Extensions:SqlServer:3.3.2", "purl": "pkg:nuget/FluentMigrator.Extensions.SqlServer@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.extensions.sqlserver/3.3.2/fluentmigrator.extensions.sqlserver.3.3.2.nupkg", "hash": {"value": "9ff93fc56a5e858363d6c059bdcc2ea82fe27a0d74852cf7b608c672e981af3d63f2bb10f9a8d66a7b9460436ed92b9464c9cf6edf38bea913ef05dde45eefb8", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:Core:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.Core@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.core/3.3.2/fluentmigrator.runner.core.3.3.2.nupkg", "hash": {"value": "ed8a9575c786f413e4adcff6b594e9103ae5cc0a5d675582ff7c4a3242eb61d5a08ee5c17f57cb4601bd5504558a96974e46ecc8513dd65a3faf94c54dde967f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:Db2:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.Db2@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.db2/3.3.2/fluentmigrator.runner.db2.3.3.2.nupkg", "hash": {"value": "cc67cce86f0fc6d95ff9981b64a2ec2d80b9e3f5464b89927839a9d50fb132f48c0bcd201f161de7df0714632c2c98eea819d4f17543a45d8988d3dd40a3847a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:Firebird:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.Firebird@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.firebird/3.3.2/fluentmigrator.runner.firebird.3.3.2.nupkg", "hash": {"value": "f5887393126c2af16128d82661ca8de0f36e7be72564f05214fdac59ae84ed34cdd30731f7493b9dd2ccc8e144cc68ef258ac3f3fff691c41b460c721406e3ea", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:<PERSON><PERSON>:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.Hana@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.hana/3.3.2/fluentmigrator.runner.hana.3.3.2.nupkg", "hash": {"value": "ab232102761685905d05ca3b5bb3bd6106a6a33d597bed320797c9f9094c82b8a14a2a3d55d5d0b2a86096321d7273acebab96f75b6359b15c7ac72569b6a0cf", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:MySql:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.MySql@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.mysql/3.3.2/fluentmigrator.runner.mysql.3.3.2.nupkg", "hash": {"value": "dfa5291cf4ba38f90b7b76683e6d208f30d59bf48603abba95f0030073e7239bd5f77645189f03aa7fea88bcf7d05991e559c41b21ea4f9d7cc4350881e1efbf", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:Oracle:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.Oracle@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.oracle/3.3.2/fluentmigrator.runner.oracle.3.3.2.nupkg", "hash": {"value": "db0eff06bfa960ab598d1ddd3916d94945cfbd634153e1f43a4e191c23782c4498cd49a37ce65c219af19586b142066ece518e4fa163847fc1d76cfca4103866", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:<PERSON><PERSON><PERSON>:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.Postgres@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.postgres/3.3.2/fluentmigrator.runner.postgres.3.3.2.nupkg", "hash": {"value": "26652c8e24d94c5b15d60622a802a702ebf3612a4020d9b6ded0792b66b70fb33e8411d37ab2bd0a1f863d8c0b7a63a06a39a6849a5a4f4e929536f02e52bced", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:<PERSON>hi<PERSON>:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.Redshift@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.redshift/3.3.2/fluentmigrator.runner.redshift.3.3.2.nupkg", "hash": {"value": "76cdd98b2fdff09c3e06eb1d01cafa81506e80e651d7ae1065def9245fe2d28e9ed98ef1815a067c786e55b57dd3177334a83621a1e3c21f4f02afb272d2def1", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:SQLite:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.SQLite@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.sqlite/3.3.2/fluentmigrator.runner.sqlite.3.3.2.nupkg", "hash": {"value": "8f2c27dda81ea06307b364b2bcaf80463ec27d67356d8b918dc443a60d8083a3aa39b026505f8952cbf6edc0bc440bd246b67356de0f07082a400feb26c9e4c2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:SqlAnywhere:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.SqlAnywhere@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.sqlanywhere/3.3.2/fluentmigrator.runner.sqlanywhere.3.3.2.nupkg", "hash": {"value": "c4aeaafcf7c551c41ba669c36952de00fa17b90c9f473624045716c0514c0d48df77e7443fc09c9b4eecac68fe33235f017881bba8276936ce3880b04cd25bf4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:SqlServer:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.SqlServer@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.sqlserver/3.3.2/fluentmigrator.runner.sqlserver.3.3.2.nupkg", "hash": {"value": "0bd2cc0f254dedb54e4b0d5771a513619b82cb92d29d98b34dbeb98b0923b25eda2fa9ed833fd07d86cb5d60181bc42874482d80517aa16538deb44108ae3b82", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:FluentMigrator.Runner:SqlServerCe:3.3.2", "purl": "pkg:nuget/FluentMigrator.Runner.SqlServerCe@3.3.2", "authors": ["<PERSON>,<PERSON>,<PERSON>,<PERSON>"], "declared_licenses": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"], "declared_licenses_processed": {"unmapped": ["https://github.com/fluentmigrator/fluentmigrator/blob/master/LICENSE.txt"]}, "description": "FluentMigrator is a database migration framework for .NET written in C#. The basic idea is that you can create migrations which are simply classes that derive from the Migration base class and have a Migration attribute with a unique version number attached to them. Upon executing FluentMigrator, you tell it which version to migrate to and it will run all necessary migrations in order to bring your database up to that version. In addition to forward migration support, FluentMigrator also supports different ways to execute the migrations along with selective migrations called profiles and executing arbitrary SQL.", "homepage_url": "https://github.com/fluentmigrator/fluentmigrator/wiki", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/fluentmigrator.runner.sqlserverce/3.3.2/fluentmigrator.runner.sqlserverce.3.3.2.nupkg", "hash": {"value": "a77050caecefe9e0edbc78a7495d55dba1790fbc4e43e1b572c80100f96534b98d475105bf55d148e9f2b11c97e6dd7f5a63a38fa1419a462c0e97badd0596bc", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/fluentmigrator/fluentmigrator.git", "revision": "", "path": ""}}, {"id": "NuGet:Google:Protobuf:3.24.3", "purl": "pkg:nuget/Google.Protobuf@3.24.3", "authors": ["Google Inc."], "declared_licenses": ["BSD-3-<PERSON><PERSON>"], "declared_licenses_processed": {"spdx_expression": "BSD-3-<PERSON><PERSON>"}, "description": "C# runtime library for Protocol Buffers - Google's data interchange format.", "homepage_url": "https://github.com/protocolbuffers/protobuf", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/google.protobuf/3.24.3/google.protobuf.3.24.3.nupkg", "hash": {"value": "9d781986be58d826dd4386014a87fa1dfee7f39a7be65a6460540aa4c822e4c817502bda150658d1b540c1c174d80d78563581c3e4b4b659ccf320a2c64ce516", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/protocolbuffers/protobuf.git", "revision": "e3e94a372fd50fd084f87ea6491cf18969c1fa79", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/protocolbuffers/protobuf.git", "revision": "e3e94a372fd50fd084f87ea6491cf18969c1fa79", "path": ""}}, {"id": "NuGet:Google.Api:CommonProtos:2.10.0", "purl": "pkg:nuget/Google.Api.CommonProtos@2.10.0", "authors": ["Google LLC"], "declared_licenses": ["BSD-3-<PERSON><PERSON>"], "declared_licenses_processed": {"spdx_expression": "BSD-3-<PERSON><PERSON>"}, "description": "Common Protocol Buffer messages for Google APIs", "homepage_url": "https://github.com/googleapis/gax-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/google.api.commonprotos/2.10.0/google.api.commonprotos.2.10.0.nupkg", "hash": {"value": "e12f75856912642619e3f5f76d5aaee516328bbb1fc9ffe316a423820c569151ce499130a890827cc2fa24b198c185b338051aff393c9fe727e2fa75a39b2fe4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/googleapis/gax-dotnet", "revision": "f75385343c9437d34cee002d793ddf9eddfbdc67", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/googleapis/gax-dotnet.git", "revision": "f75385343c9437d34cee002d793ddf9eddfbdc67", "path": ""}}, {"id": "NuGet:Grpc:Core:2.46.6", "purl": "pkg:nuget/Grpc.Core@2.46.6", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "C# implementation of gRPC based on native gRPC C-core library.", "homepage_url": "https://github.com/grpc/grpc", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.core/2.46.6/grpc.core.2.46.6.nupkg", "hash": {"value": "112b86358d372b03d5c954f4356dcc3ba3c66f0334b91ffddc0d8abdf21cf1b7fee35e83c435fe8b84d4d6a9c2e5cd4715da5e8d398cd05da5142e05adbc20ae", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc.git", "revision": "cdc97245c7977e85bb0234941f5c6cd9cb7accdf", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc.git", "revision": "cdc97245c7977e85bb0234941f5c6cd9cb7accdf", "path": ""}}, {"id": "NuGet:Grpc.Core:Api:2.57.0", "purl": "pkg:nuget/Grpc.Core.Api@2.57.0", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "gRPC C# Surface API", "homepage_url": "https://github.com/grpc/grpc-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.core.api/2.57.0/grpc.core.api.2.57.0.nupkg", "hash": {"value": "3180d5611f002ed23b5abd210751a562cac15f219ad538eedfd85f151d596d0d2a3fef2a148bcef34e3796e7649b7657bad69faf17a48c7bc7c16a5b34fa610d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}}, {"id": "NuGet:Grpc.Net:Client:2.57.0", "purl": "pkg:nuget/Grpc.Net.Client@2.57.0", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": ".NET client for gRPC", "homepage_url": "https://github.com/grpc/grpc-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.net.client/2.57.0/grpc.net.client.2.57.0.nupkg", "hash": {"value": "e311c35552d1407167621c28bb95dc2de12d184793e1fb19dafdd5d2bcf0c027f0452b0ad628357576eb65b892733a71c77885c26750214c22a0ffc0f9c41e3c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}}, {"id": "NuGet:Grpc.Net:ClientFactory:2.57.0", "purl": "pkg:nuget/Grpc.Net.ClientFactory@2.57.0", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "HttpClientFactory integration the for gRPC .NET client", "homepage_url": "https://github.com/grpc/grpc-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.net.clientfactory/2.57.0/grpc.net.clientfactory.2.57.0.nupkg", "hash": {"value": "c8fbde8122449557414650aaf5377c976fc06414183a276dc6233a7c166a6276155fc1c05448745bcd25fed2b449a23ced4d29d6d2893234e85e37e3f639c8d1", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}}, {"id": "NuGet:Grpc.Net:Common:2.57.0", "purl": "pkg:nuget/Grpc.Net.Common@2.57.0", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Infrastructure for common functionality in gRPC", "homepage_url": "https://github.com/grpc/grpc-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.net.common/2.57.0/grpc.net.common.2.57.0.nupkg", "hash": {"value": "30ae93fb69ec008ce333d61eb565f14271b1fb52d4fe12b0b407f1927db858d862e9bc7a147aec24f5b708d338caf450253291dba553f38b4ca968d2de8d62a1", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}}, {"id": "NuGet:Humanizer:Core:2.8.26", "purl": "pkg:nuget/Humanizer.Core@2.8.26", "authors": ["<PERSON><PERSON>, <PERSON>"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Humanizer core package that contains the library and the neutral language (English) resources", "homepage_url": "https://github.com/Humanizr/Humanizer", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/humanizer.core/2.8.26/humanizer.core.2.8.26.nupkg", "hash": {"value": "85d0e6f2ed05acf128ad5d6a5c0f96d350c247dcde357e5fc1ee5f5c5532ce603f2632f1323b7bfd6ebbdab48e5888d61c6903e9c6583852e3220ff43be6bc92", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/Humanizr/Humanizer", "revision": "7f07276dab51366f54def454d01f0e0de8c918fa", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/Humanizr/Humanizer.git", "revision": "7f07276dab51366f54def454d01f0e0de8c918fa", "path": ""}}, {"id": "NuGet:IdentityModel.AspNetCore:OAuth2Introspection:6.2.0", "purl": "pkg:nuget/IdentityModel.AspNetCore.OAuth2Introspection@6.2.0", "authors": ["<PERSON><PERSON>,<PERSON>"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "ASP.NET Core authentication handler for validating tokens using OAuth 2.0 introspection", "homepage_url": "https://github.com/IdentityModel/IdentityModel.AspNetCore.OAuth2Introspection", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/identitymodel.aspnetcore.oauth2introspection/6.2.0/identitymodel.aspnetcore.oauth2introspection.6.2.0.nupkg", "hash": {"value": "604156c2b0ddc895ee023ba9e5d194de38700e83b6593f3521dafde90bb9397dc743ee3fc47504975dbbb0102e3c1dafefb92093f8ab7f5f4d73765bf32ba520", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/IdentityModel/IdentityModel.AspNetCore.OAuth2Introspection", "revision": "b71fa438bc566e3b527547969dd29302084d116d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/IdentityModel/IdentityModel.AspNetCore.OAuth2Introspection.git", "revision": "b71fa438bc566e3b527547969dd29302084d116d", "path": ""}}, {"id": "NuGet:JetBrains:Annotations:2023.2.0", "purl": "pkg:nuget/JetBrains.Annotations@2023.2.0", "authors": ["JetBrains"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "JetBrains.Annotations help reduce false positive warnings, explicitly declare purity and nullability in your code, deal with implicit usages of members, support special semantics of APIs in ASP.NET and XAML frameworks and otherwise increase accuracy of JetBrains Rider and ReSharper code inspections.", "homepage_url": "https://www.jetbrains.com/help/resharper/Code_Analysis__Code_Annotations.html", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/jetbrains.annotations/2023.2.0/jetbrains.annotations.2023.2.0.nupkg", "hash": {"value": "9d15d78e7d9b723891017e41bdc28997089a5ecd31dc8f6a1899d879912e1691c2269c6097546efcd5b15eb629140ba231d08b06559ed7eb944e30dc59c83164", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/JetBrains/JetBrains.Annotations.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/JetBrains/JetBrains.Annotations.git", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft:CSharp:4.5.0", "purl": "pkg:nuget/Microsoft.CSharp@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides support for compilation and code generation, including dynamic, using the C# language.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.csharp/4.5.0/microsoft.csharp.4.5.0.nupkg", "hash": {"value": "c9659e4db182cc13a544f583088c624d95b579c66231b6a8d194fdeca28459d061acbbd4a94f11773921cee091433be8c73c6547bbf2b4ee3738e805764c6fea", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft:EntityFrameworkCore:8.0.0-preview.6.23329.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore@8.0.0-preview.6.23329.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Entity Framework Core is a modern object-database mapper for .NET. It supports LINQ queries, change tracking, updates, and schema migrations. EF Core works with SQL Server, Azure SQL Database, SQLite, Azure Cosmos DB, MySQL, PostgreSQL, and other databases through a provider plugin API.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore/8.0.0-preview.6.23329.4/microsoft.entityframeworkcore.8.0.0-preview.6.23329.4.nupkg", "hash": {"value": "4695ea0b0c318d647a574f366b8a057e73c1a3321d5ad93d29aabf5be3c34071f46647c13b01aa983ffcb854ee9dda163b928ad41cf576a86514dda0e9918abf", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}}, {"id": "NuGet:Microsoft:EntityFrameworkCore:8.0.0-preview.7.23375.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore@8.0.0-preview.7.23375.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Entity Framework Core is a modern object-database mapper for .NET. It supports LINQ queries, change tracking, updates, and schema migrations. EF Core works with SQL Server, Azure SQL Database, SQLite, Azure Cosmos DB, MySQL, PostgreSQL, and other databases through a provider plugin API.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore/8.0.0-preview.7.23375.4/microsoft.entityframeworkcore.8.0.0-preview.7.23375.4.nupkg", "hash": {"value": "73f200b34304f7add78096ad5c4d31ea7cf7c5f430de411ab0e6391b05118d91a9a2156e5bc7d22afec2f49dbf7169e86acb1a74fb7a6d87b28ab97f0e7823ea", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}}, {"id": "NuGet:Microsoft:OpenApi:1.2.3", "purl": "pkg:nuget/Microsoft.OpenApi@1.2.3", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/Microsoft/OpenAPI.NET/master/LICENSE"], "declared_licenses_processed": {"unmapped": ["https://raw.githubusercontent.com/Microsoft/OpenAPI.NET/master/LICENSE"]}, "description": ".NET models with JSON and YAML writers for OpenAPI specification", "homepage_url": "https://github.com/Microsoft/OpenAPI.NET", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.openapi/1.2.3/microsoft.openapi.1.2.3.nupkg", "hash": {"value": "9d8710a99c1a976014c0a4a837d021fa58fd13d5c1d573861e357c43ab1b4ea73ea85cf72afab0a3dfa5738908ade7ef6904e2d280ffff2ad9cfc8868c228461", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/Microsoft/OpenAPI.NET.git", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core common types used by the various authentication middleware components.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication/2.2.0/microsoft.aspnetcore.authentication.2.2.0.nupkg", "hash": {"value": "2ae7b5eec16e748fad92a13ccec88f5ba288ff26272084a1f1157af5bf92a6b57c9f1fb222ba2d4f6227bf8afa5b0e5f64dd030038f6cca4b4b463595d771968", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Security", "revision": "93926543f8469614c2feb23de8a8c0561b8b2463", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Security.git", "revision": "93926543f8469614c2feb23de8a8c0561b8b2463", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core common types used by the various authentication components.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication.abstractions/2.2.0/microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg", "hash": {"value": "67ad256e07bd55ae00e8450c137617f11b861269b3919035984c715237eba9bff71a599ca59c8c0d07b6978fd498400274a4907a40942323d5e3aad413bd2b58", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication.Core:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication.Core@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core common types used by the various authentication middleware components.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication.core/2.2.0/microsoft.aspnetcore.authentication.core.2.2.0.nupkg", "hash": {"value": "5313aa9320cc68cd97d4f25a0796b47cef3a4aa05887166e52c7abeaa67e1fbf202ea35ae056aa925efc21c116575f6856aac263410534e8feda05d63e4d856b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication.JwtBearer:7.0.11", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication.JwtBearer@7.0.11", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "ASP.NET Core middleware that enables an application to receive an OpenID Connect bearer token.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication.jwtbearer/7.0.11/microsoft.aspnetcore.authentication.jwtbearer.7.0.11.nupkg", "hash": {"value": "e63cb7f2c3603ea0f355b942560d1835db65df5da35818389f205502d1a4d79857992c06f84cc85a5ecfc216aea402890387bcf63f798b98042e00d7b2866a8f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/aspnetcore", "revision": "70048beee894074e9718206a0017d9b080ee66ff", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/aspnetcore.git", "revision": "70048beee894074e9718206a0017d9b080ee66ff", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication.OpenIdConnect:7.0.11", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication.OpenIdConnect@7.0.11", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "ASP.NET Core middleware that enables an application to support the OpenID Connect authentication workflow.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication.openidconnect/7.0.11/microsoft.aspnetcore.authentication.openidconnect.7.0.11.nupkg", "hash": {"value": "2af1c45cc43ced732ad1fec3c6bb4399cb00526d099382c5dae592dcd2d226923dbfb16b5f3e88075ae207d3fe514328a36b42f70aee570aceb9ff4c9efe0c5d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/aspnetcore", "revision": "70048beee894074e9718206a0017d9b080ee66ff", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/aspnetcore.git", "revision": "70048beee894074e9718206a0017d9b080ee66ff", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Cryptography.Internal:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Cryptography.Internal@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "Infrastructure for ASP.NET Core cryptographic packages. Applications and libraries should not reference this package directly.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.cryptography.internal/2.2.0/microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg", "hash": {"value": "861ff64140528a4899be92549763a48511be752e29e9ab5afbf63f794ad711577fbe65d24ebab80db5b57dbe925415e1061c10cc7c2b741cdc374b14a8ef38e3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/AspNetCore", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/AspNetCore.git", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:DataProtection:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.DataProtection@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core logic to protect and unprotect data, similar to DPAPI.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.dataprotection/2.2.0/microsoft.aspnetcore.dataprotection.2.2.0.nupkg", "hash": {"value": "948093ee5134878fb19a97e69c4bdab6d64753cb41ac1576db9a2da1b406a487e07f493b578a8357b350d57f187e979463f8bc12820da72b9887f7835d6e009c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/AspNetCore", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/AspNetCore.git", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:DataProtection.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.DataProtection.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core data protection abstractions.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.dataprotection.abstractions/2.2.0/microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg", "hash": {"value": "674106d80f31ac355d3647e1633531cd5705290535e6a075867e4f62d741789e2c5ad1013a43526bf72e69751bfea6dee9a9958a82ad3125dcbddb826aadd0b3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/AspNetCore", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/AspNetCore.git", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Hosting.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Hosting.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core hosting and startup abstractions for web applications.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.hosting.abstractions/2.2.0/microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg", "hash": {"value": "a900d6a07c05f80066c536abcbfa44a545fb7f5842a10864d110e726e325399f9d3c5a7fbaf93ef6cc5953ac3f73c10bf633e57ee60e3902463c44d4bff34d5c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Hosting", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Hosting.git", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Hosting.Server.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Hosting.Server.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core hosting server abstractions for web applications.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.hosting.server.abstractions/2.2.0/microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg", "hash": {"value": "a7875388fd4efc7469c0cc5801345279e9331d3bf9a70877af13d0c2d6cedb5ff17f49db78246cc05b57436289c1db7ba617c36782bd1e6702be2045d498192e", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Hosting", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Hosting.git", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core default HTTP feature implementations.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http/2.2.0/microsoft.aspnetcore.http.2.2.0.nupkg", "hash": {"value": "0a7ec82d94908a42a725be258447d5c7193e0ac437e99c55cceaf30d49aa18ea7d0493f72ea558613a3fdaf1a04254a8dc60f847c7c37c07e55fcb3dbd7973fe", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http.Abstractions:2.1.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http.Abstractions@2.1.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/Home/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/Home/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core HTTP object model for HTTP requests and responses and also common extension methods for registering middleware in an IApplicationBuilder.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http.abstractions/2.1.0/microsoft.aspnetcore.http.abstractions.2.1.0.nupkg", "hash": {"value": "c54a4b7ae5220aa10827dd7be40d8408486c0625ea720a588981ee65fe9bc29bc62768574015565261b101d036224b2e44946cf9a3b9a0404634ace1e7fae601", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "95a44cda4b956a8a57115be2a9b564b0961f5577", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "95a44cda4b956a8a57115be2a9b564b0961f5577", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core HTTP object model for HTTP requests and responses and also common extension methods for registering middleware in an IApplicationBuilder.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http.abstractions/2.2.0/microsoft.aspnetcore.http.abstractions.2.2.0.nupkg", "hash": {"value": "d0b334c186ecbd89d6d7593caa1d030d6104c7fa7a41a0eaa5c2e53328433a6356f4a829ad187d8214c009f78e0a8d6146a764b54329eaa82ecce2a2c482f5df", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http.Extensions:2.1.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http.Extensions@2.1.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/Home/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/Home/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core common extension methods for HTTP abstractions, HTTP headers, HTTP request/response, and session state.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http.extensions/2.1.0/microsoft.aspnetcore.http.extensions.2.1.0.nupkg", "hash": {"value": "315ddf6dead32f1a20fb2e5a0f9d3f27bbf39ca6284341be64c990b3216f360d74c07c35d8d78ad74a63c5b98677c42ddbecadd6c29fc194d90812d524dabf64", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "95a44cda4b956a8a57115be2a9b564b0961f5577", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "95a44cda4b956a8a57115be2a9b564b0961f5577", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http.Extensions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http.Extensions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core common extension methods for HTTP abstractions, HTTP headers, HTTP request/response, and session state.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http.extensions/2.2.0/microsoft.aspnetcore.http.extensions.2.2.0.nupkg", "hash": {"value": "c177c1ebcb29dd7fbafd32d41d51797a2b1c9fd886a6d184c23272938c816db380549102139f7f294ba7eac4fe26535c342584584d12ec4c3ec8c0fa6e6debad", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http.Features:2.1.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http.Features@2.1.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/Home/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/Home/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core HTTP feature interface definitions.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http.features/2.1.0/microsoft.aspnetcore.http.features.2.1.0.nupkg", "hash": {"value": "83e9794caa76d20fb33ab96ca554cfd60920cc390c6724a7f199248a12d78ddd9076c3e9300319d9e2b101fe9397e124a11157f1281424238f1f37287c811993", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "95a44cda4b956a8a57115be2a9b564b0961f5577", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "95a44cda4b956a8a57115be2a9b564b0961f5577", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http.Features:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http.Features@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core HTTP feature interface definitions.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http.features/2.2.0/microsoft.aspnetcore.http.features.2.2.0.nupkg", "hash": {"value": "8efbbbe3f90e8080c29c01fd5eedbfc084eb49a803a752f8389fa94ebc56cce76935cee50ed7cfaf2c017bb87d094900c7ff3de4bf62debef4bdfe6582806b73", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:SpaProxy:8.0.0-preview.7.23375.9", "purl": "pkg:nuget/Microsoft.AspNetCore.SpaProxy@8.0.0-preview.7.23375.9", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Helpers for launching the SPA CLI proxy automatically when the application starts in ASP.NET MVC Core.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.spaproxy/8.0.0-preview.7.23375.9/microsoft.aspnetcore.spaproxy.8.0.0-preview.7.23375.9.nupkg", "hash": {"value": "b292963d01af291c66659624566ca69558701df339e3ae6881808a58f6c1c5595609285eacf7eae2999e29e6388fe8161ae3349899c4c495cc15257ceb292d60", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/aspnetcore", "revision": "8df730db29a670c853e3cff67fe70eb1cf6e8af7", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/aspnetcore.git", "revision": "8df730db29a670c853e3cff67fe70eb1cf6e8af7", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:WebUtilities:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.WebUtilities@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core utilities, such as for working with forms, multipart messages, and query strings.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.webutilities/2.2.0/microsoft.aspnetcore.webutilities.2.2.0.nupkg", "hash": {"value": "f3f97e3bfde44c1563354731bd872063f7e72d07f4d46cc6f159a8012761618ba798a21c0a29a4f9a63a4af57fdedb90055919255fa9707090d7e5f28c4a9fc9", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.Bcl:AsyncInterfaces:1.1.1", "purl": "pkg:nuget/Microsoft.Bcl.AsyncInterfaces@1.1.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides the IAsyncEnumerable<T> and IAsyncDisposable interfaces and helper types for .NET Standard 2.0. This package is not required starting with .NET Standard 2.1 and .NET Core 3.0.", "homepage_url": "https://github.com/dotnet/corefx", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.bcl.asyncinterfaces/1.1.1/microsoft.bcl.asyncinterfaces.1.1.1.nupkg", "hash": {"value": "4cce7ecad99baae70b1f815cd047431f284a466342e4456f929bef24891f45d600d90037f0d45ca97b1b8a453b51145dc215b9807ac1ce8b2b8cf45630334204", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/corefx.git", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.Data:SqlClient:5.1.1", "purl": "pkg:nuget/Microsoft.Data.SqlClient@5.1.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides the data provider for SQL Server. These classes provide access to versions of SQL Server and encapsulate database-specific protocols, including tabular data stream (TDS)", "homepage_url": "https://aka.ms/sqlclientproject", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.data.sqlclient/5.1.1/microsoft.data.sqlclient.5.1.1.nupkg", "hash": {"value": "c8f42a12fd3062da9f2421dab898ad70fbc7019de7e02633ac943faf2919ec1ea8cedf43e30af4e2275c89d3a3e6da23c9c126d361418b898d26d2d9486edafe", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/sqlclient", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/sqlclient.git", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.Data:SqlClient.SNI.runtime:5.1.0", "purl": "pkg:nuget/Microsoft.Data.SqlClient.SNI.runtime@5.1.0", "authors": ["Microsoft"], "declared_licenses": ["https://www.nuget.org/packages/Microsoft.Data.SqlClient.SNI.runtime/5.1.0/license"], "declared_licenses_processed": {"unmapped": ["https://www.nuget.org/packages/Microsoft.Data.SqlClient.SNI.runtime/5.1.0/license"]}, "description": "Internal implementation package not meant for direct consumption. Please do not reference directly.", "homepage_url": "https://aka.ms/sqlclientproject", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.data.sqlclient.sni.runtime/5.1.0/microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg", "hash": {"value": "4c5acc97a8021fb032c0791ef3427bdf28390ecaebdeb5fb038fc7406dd0d3c52eda5f18f5d15013bd9b7accb120b247947903c7fa39878b42127cefa11eaa22", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:Abstractions:8.0.0-preview.6.23329.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.Abstractions@8.0.0-preview.6.23329.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides abstractions and attributes that are used to configure Entity Framework Core", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.abstractions/8.0.0-preview.6.23329.4/microsoft.entityframeworkcore.abstractions.8.0.0-preview.6.23329.4.nupkg", "hash": {"value": "edcf3cc04a94d2fc21f084dd32d6ec3be5c0b444bd9d11e9de83c0795431bc1ce93f52355ac1b3f9a000144736be6a562b43421445690c2ad106d9a0d51c17ef", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:Abstractions:8.0.0-preview.7.23375.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.Abstractions@8.0.0-preview.7.23375.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides abstractions and attributes that are used to configure Entity Framework Core", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.abstractions/8.0.0-preview.7.23375.4/microsoft.entityframeworkcore.abstractions.8.0.0-preview.7.23375.4.nupkg", "hash": {"value": "7955ed5f85e1435032bb3f1e948228ac2b4483c49973668090793ada216c8926d023c5d34560084c050eff036a5589e89ef0176a170e3e01c06707ff1a4d32e0", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:Analyzers:8.0.0-preview.6.23329.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.Analyzers@8.0.0-preview.6.23329.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "CSharp Analyzers for Entity Framework Core.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.analyzers/8.0.0-preview.6.23329.4/microsoft.entityframeworkcore.analyzers.8.0.0-preview.6.23329.4.nupkg", "hash": {"value": "796f8a7c66d806330d415658ca1ee2340bd94b8fe52a1fb97e047dcb5193c255a5426416b29381383a1a851f718c49f971c83b64813d473f42b9255a8cde1d3c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:Analyzers:8.0.0-preview.7.23375.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.Analyzers@8.0.0-preview.7.23375.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "CSharp Analyzers for Entity Framework Core.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.analyzers/8.0.0-preview.7.23375.4/microsoft.entityframeworkcore.analyzers.8.0.0-preview.7.23375.4.nupkg", "hash": {"value": "0703845069b245973cf6903d847bc80c81345b13f40c3d765861f4389902615c70f751b8b83bd32972f5ebc20cf87f77edce8e586ceef7e3470ffc7cbe36bbc9", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:Relational:8.0.0-preview.6.23329.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.Relational@8.0.0-preview.6.23329.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Shared Entity Framework Core components for relational database providers.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.relational/8.0.0-preview.6.23329.4/microsoft.entityframeworkcore.relational.8.0.0-preview.6.23329.4.nupkg", "hash": {"value": "d0aeb92da5021e8742072f234d9212f793827b5ee5b440587547e72767fb1d8afac792e1ad7eafa7ccecc96f3fd463ad13a38764d99a753f00ef8d23ff1f6ee7", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:Relational:8.0.0-preview.7.23375.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.Relational@8.0.0-preview.7.23375.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Shared Entity Framework Core components for relational database providers.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.relational/8.0.0-preview.7.23375.4/microsoft.entityframeworkcore.relational.8.0.0-preview.7.23375.4.nupkg", "hash": {"value": "f5ef8a85064cf260a57173519887e4ff589ddbdf172b5515538dbedf6862b1ad1f281588c6f53c4317395d989dcb09ca1848fee142f9c81e12a34cedd911a18a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:SqlServer:8.0.0-preview.6.23329.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.SqlServer@8.0.0-preview.6.23329.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Microsoft SQL Server database provider for Entity Framework Core.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.sqlserver/8.0.0-preview.6.23329.4/microsoft.entityframeworkcore.sqlserver.8.0.0-preview.6.23329.4.nupkg", "hash": {"value": "a5c11bf6785bef6b4bade107d4dd1d0771c501a1e11f8cf1eba9c198420d5a973b7837a45ea3d16438fa62d07822821397def026109bd4bc4f3a3997d048b18a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:SqlServer:8.0.0-preview.7.23375.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.SqlServer@8.0.0-preview.7.23375.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Microsoft SQL Server database provider for Entity Framework Core.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.sqlserver/8.0.0-preview.7.23375.4/microsoft.entityframeworkcore.sqlserver.8.0.0-preview.7.23375.4.nupkg", "hash": {"value": "49c3285a92001e13b12f1cc63d0ead041f1fa90793e4c8e7ba2821cddc4c0bcd78ad7dcded9b5c2eb68c659c36b23aaa955d8b77ff32e5de6897d51c20c1be1f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "397dc68aa9599f3fd7bf04b5e447614dfd3f90c2", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Caching.Abstractions:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Caching.Abstractions@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Caching abstractions for in-memory cache and distributed cache.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.caching.abstractions/8.0.0-preview.6.23329.7/microsoft.extensions.caching.abstractions.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "0d56a5151fbcb9884132922ada41c170bf7c0219c361a5c3cc39619dea06ce3599ba2dc72c096d916508c3516966b9a88a2227e5135654a3384f8f098c62beeb", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Caching.Abstractions:8.0.0-preview.7.23375.6", "purl": "pkg:nuget/Microsoft.Extensions.Caching.Abstractions@8.0.0-preview.7.23375.6", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Caching abstractions for in-memory cache and distributed cache.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.caching.abstractions/8.0.0-preview.7.23375.6/microsoft.extensions.caching.abstractions.8.0.0-preview.7.23375.6.nupkg", "hash": {"value": "2a54a020282596ae6c0ee5c6cc82fbca9b43d1261f532962c41ab594b212a242b5a65d677d1f0fd8adc69a6eee7cda31d09ca7d9c2e58ed7470886d5c5d13771", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Caching.Memory:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Caching.Memory@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "In-memory cache implementation of Microsoft.Extensions.Caching.Memory.IMemoryCache.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.caching.memory/8.0.0-preview.6.23329.7/microsoft.extensions.caching.memory.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "2d1aa42de3f4335239736b40684af62efb01ce4be1920990ce7f541aa560ed4368599b2bf90fab5139eeb37d35231881ba133981d31e8c33738069d91a215959", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Caching.Memory:8.0.0-preview.7.23375.6", "purl": "pkg:nuget/Microsoft.Extensions.Caching.Memory@8.0.0-preview.7.23375.6", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "In-memory cache implementation of Microsoft.Extensions.Caching.Memory.IMemoryCache.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.caching.memory/8.0.0-preview.7.23375.6/microsoft.extensions.caching.memory.8.0.0-preview.7.23375.6.nupkg", "hash": {"value": "cebb7699e2a69201595c591b32556ab8f84fc618809005310f135f9a0f2aec41ad29e5437be6eb140eb75b245623b4ee0207cffcb16a370ab6e9d1001fbf4f37", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Configuration:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Configuration@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Implementation of key-value pair based configuration for Microsoft.Extensions.Configuration. Includes the memory configuration provider.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.configuration/8.0.0-preview.6.23329.7/microsoft.extensions.configuration.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "83218d651dda58e61f0b309cb2a6b7b447654b78dcd7e340393d25a8830af81f0647213318610de97ade12eb3756845fa8c8f02d3a65f36de991e814c153d93b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Configuration.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.Configuration.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "Abstractions of key-value pair based configuration.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.configuration.abstractions/2.2.0/microsoft.extensions.configuration.abstractions.2.2.0.nupkg", "hash": {"value": "33abb870c597c4fb6ac89274de87b3caa4ce4664a04cfa5ada06512a41060975c78721a73357078cf4f3ab9b1ebd84b554910c6f64d98fad2601cfa10e83cf71", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Extensions", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Extensions.git", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Configuration.Abstractions:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Configuration.Abstractions@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides abstractions of key-value pair based configuration. Interfaces defined in this package are implemented by classes in Microsoft.Extensions.Configuration and other configuration packages.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.configuration.abstractions/8.0.0-preview.6.23329.7/microsoft.extensions.configuration.abstractions.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "cccf58228881087d472f85d5e481394ccd427de4ef02d1f7273f398d8f1f7af4505207995e8b367261ecf705c75720b68a0f912b48708f0c43f8c535d247cf26", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Configuration.Abstractions:8.0.0-preview.7.23375.6", "purl": "pkg:nuget/Microsoft.Extensions.Configuration.Abstractions@8.0.0-preview.7.23375.6", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides abstractions of key-value pair based configuration. Interfaces defined in this package are implemented by classes in Microsoft.Extensions.Configuration and other configuration packages.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.configuration.abstractions/8.0.0-preview.7.23375.6/microsoft.extensions.configuration.abstractions.8.0.0-preview.7.23375.6.nupkg", "hash": {"value": "f2502aa396bcfbb3838f5d7bd63ca58304e6e2c0399de0bc31a4535104605cd8365290a372416c731ac6fa5f7b8c661c7fa567f1aea13cd7a03030e22c669ec3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Configuration.Binder:7.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Configuration.Binder@7.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Functionality to bind an object to data in configuration providers for Microsoft.Extensions.Configuration.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.configuration.binder/7.0.0/microsoft.extensions.configuration.binder.7.0.0.nupkg", "hash": {"value": "0ee17ec5c5e9ae952eb1e2d3ddfe0da6b1ab3d3603131d81349dfccd8e6538731cd59e0d6f1c0f12ad08ebe9724cacd7d71d12f12776df9d9dd1b73095f53bc9", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Configuration.FileExtensions:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Configuration.FileExtensions@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides a base class for file-based configuration providers used with Microsoft.Extensions.Configuration and extension methods for configuring them.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.configuration.fileextensions/8.0.0-preview.6.23329.7/microsoft.extensions.configuration.fileextensions.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "a214f26162c9708d4b6883302ef799af146d49c9d93ec2d54ec2f8c2113865214c66e5cbe8620c94d29682fa4f0b1171109cc87edffac299c42d3f2fb15d0503", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Configuration.Json:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Configuration.Json@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "JSON configuration provider implementation for Microsoft.Extensions.Configuration. This package enables you to read your application's settings from a JSON file. You can use JsonConfigurationExtensions.AddJsonFile extension method on IConfigurationBuilder to add the JSON configuration provider to the configuration builder.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.configuration.json/8.0.0-preview.6.23329.7/microsoft.extensions.configuration.json.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "c75172b1182ffc65f576f93cb2e9e010c72222d64fc1807a3330cfabcd4d43c147bb9a4cf8a77c72cb0263a214c4a13f4613752400a62ff917ebadf95e3c0097", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Default implementation of dependency injection for Microsoft.Extensions.DependencyInjection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection/6.0.0/microsoft.extensions.dependencyinjection.6.0.0.nupkg", "hash": {"value": "c25eefa020a785d8608f8c5bc1b7d479aa3f9bb3295aac41a1b6b81440ee8c2d686f28ab5f7c9bd8c762152ad8957cc71b7abfa7d4b6c85592bfc27e5155928a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Default implementation of dependency injection for Microsoft.Extensions.DependencyInjection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection/8.0.0-preview.6.23329.7/microsoft.extensions.dependencyinjection.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "b56a3ec7329639b5e55b758b19840883975ec400de1aa9a1570b01037b511cdacf785e31a410309ed3bb9c33d4f9614cebff8fa4b341139d902fea26330046a5", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection:8.0.0-preview.7.23375.6", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection@8.0.0-preview.7.23375.6", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Default implementation of dependency injection for Microsoft.Extensions.DependencyInjection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection/8.0.0-preview.7.23375.6/microsoft.extensions.dependencyinjection.8.0.0-preview.7.23375.6.nupkg", "hash": {"value": "c90de032e71c754fba8b99bdcea0cc0d045af93da2fb5459bc3bad6db99a55e57667ea183af57816a8cb6742d555d3be52e6eb2ba0a0859941305f6841b3050b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection.Abstractions@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Abstractions for dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection.abstractions/6.0.0/microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg", "hash": {"value": "015a392a362c81b35143b19c5da4eac0928cc2f2e13329ab2e945a15a5c9dea077cd66b0c467c75f2dfe6f90c3e0bf2ecdc059d75096bac39b4156a53f997bd2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection.Abstractions@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Abstractions for dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection.abstractions/8.0.0-preview.6.23329.7/microsoft.extensions.dependencyinjection.abstractions.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "31bd3c13b2c3d0ffca44b0eba91df50f76a9c29ec488f2dae80af8d8cad52159c3eef96dea84ba353c7927fed6db80f4a30d695e0aa648766d0965c1d3461c7c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:8.0.0-preview.7.23375.6", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection.Abstractions@8.0.0-preview.7.23375.6", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Abstractions for dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection.abstractions/8.0.0-preview.7.23375.6/microsoft.extensions.dependencyinjection.abstractions.8.0.0-preview.7.23375.6.nupkg", "hash": {"value": "ae1bd4a17a66f35665d6411ae57f00a4ef17a6c514227d0e726003b0fc3996faa3bec1e6e427d4e7bf772855ff610e7eff6750d9afe5368f226278e84c9c0c14", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyModel:7.0.0", "purl": "pkg:nuget/Microsoft.Extensions.DependencyModel@7.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides abstractions for reading `.deps` files. When a .NET application is compiled, the SDK generates a JSON manifest file (`<ApplicationName>.deps.json`) that contains information about application dependencies. You can use `Microsoft.Extensions.DependencyModel` to read information from this manifest at run time. This is useful when you want to dynamically compile code (for example, using Roslyn Emit API) referencing the same dependencies as your main application.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencymodel/7.0.0/microsoft.extensions.dependencymodel.7.0.0.nupkg", "hash": {"value": "c575af9fc4ba02476519eb28ec00408f09707f0bcaec0a6ee2f1e226bf170c61ec390d8d4a1403650cf6afcead97e22befebf285c1444e696019fb4c215e33f8", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:FileProviders.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.FileProviders.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "Abstractions of files and directories.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.fileproviders.abstractions/2.2.0/microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg", "hash": {"value": "df7bd3ca28f301511f6ee345b6cebc47b6d6d36709322c36d4c16030193e5cbfad85c6efdf7e4f543d7e0dd312bcde9ee437804783a63d246c288afce98938aa", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Extensions", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Extensions.git", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:FileProviders.Abstractions:7.0.0", "purl": "pkg:nuget/Microsoft.Extensions.FileProviders.Abstractions@7.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Abstractions of files and directories.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.fileproviders.abstractions/7.0.0/microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg", "hash": {"value": "d6554c450e93e49d83144d6fd3c072d493fc2baf5da75c66e72750b3e74f25ad2604dd01e838c4da7da63363905d1393d6a2dab7b21ea10d9bf55d00d8de3174", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:FileProviders.Abstractions:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.FileProviders.Abstractions@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Abstractions of files and directories.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.fileproviders.abstractions/8.0.0-preview.6.23329.7/microsoft.extensions.fileproviders.abstractions.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "12dbdd3f70e4cfaa5caaf0695f3a4c866ffc99b514be8a84b7c12a72995f6b7dbb0a20b49a42843ead0c18fcec20261f02dd84f88d5645e678a8c41015a1479c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:FileProviders.Physical:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.FileProviders.Physical@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "File provider for physical files for Microsoft.Extensions.FileProviders.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.fileproviders.physical/8.0.0-preview.6.23329.7/microsoft.extensions.fileproviders.physical.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "bfaae2d00a3ddf9d917513a067c2d50e298c7d8ab3a58ab762630fb71e64faffbf90fa229e7a79d48e2ebf4aeba2ddbeb3a93e31a8ddcf110c5b9f0f3775a707", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:FileSystemGlobbing:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.FileSystemGlobbing@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "File system globbing to find files matching a specified pattern.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.filesystemglobbing/8.0.0-preview.6.23329.7/microsoft.extensions.filesystemglobbing.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "5d346f3d9ae4abdc673f9d4927cc2dac60dd0ffbe5a34b29fda0f148715d5a9791ce2a09e14e155ba5cebccbfd9d6cff895719fa03fbb71dd97ec4172fea4efe", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Hosting.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.Hosting.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": ".NET Core hosting and startup abstractions for applications.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.hosting.abstractions/2.2.0/microsoft.extensions.hosting.abstractions.2.2.0.nupkg", "hash": {"value": "52ea6fd1f020315060c7ac4a9e59b84d1ac3f9a4c14015c2af54310c54442bea10e30ac6da50849887aa9f46214b5ec7f13f9b44a5d3c850420792eb3997a55b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Hosting", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Hosting.git", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Hosting.Abstractions:7.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Hosting.Abstractions@7.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Hosting and startup abstractions for applications.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.hosting.abstractions/7.0.0/microsoft.extensions.hosting.abstractions.7.0.0.nupkg", "hash": {"value": "f1488e6a55dafc51dfa3e73cc5dd04396f92a867aa7e675d42a3b28027482de4ec440f9272ff7990b81fdc6b7d6a8483bbf9de3af7c80d636cfe7bc351a6b02a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Http:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Http@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "The HttpClient factory is a pattern for configuring and retrieving named HttpClients in a composable way. The HttpClient factory provides extensibility to plug in DelegatingHandlers that address cross-cutting concerns such as service location, load balancing, and reliability. The default HttpClient factory provides built-in diagnostics and logging and manages the lifetimes of connections in a performant way.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.http/6.0.0/microsoft.extensions.http.6.0.0.nupkg", "hash": {"value": "c2fe14af71f407e6260219e9686d21b1a9a80421946850c57afb818c0cfd48f312ab9b4ff780402c7bdc753acd25260095953c780a5cdfa85debf14605f1e1f4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Logging@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging infrastructure default implementation for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging/6.0.0/microsoft.extensions.logging.6.0.0.nupkg", "hash": {"value": "f5dec590a353b7fbb35fda799c083dc07857c70fa0d46daec0e00c4950a8d65f42c6f399a86706e2d97ee51fb5042be27f35637673a2479dc4f623590d597311", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Logging@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging infrastructure default implementation for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging/8.0.0-preview.6.23329.7/microsoft.extensions.logging.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "89df8f8f710340151f8aa85e8176275feb1f40f3531e382206123e43e3dcd71eb5b2a5d03228e7a4c18d144b115febfdc22d34db2874e1d81ad05de325b7455c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging:8.0.0-preview.7.23375.6", "purl": "pkg:nuget/Microsoft.Extensions.Logging@8.0.0-preview.7.23375.6", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging infrastructure default implementation for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging/8.0.0-preview.7.23375.6/microsoft.extensions.logging.8.0.0-preview.7.23375.6.nupkg", "hash": {"value": "2e6de64ff9932df124d07393d61adcb7aef236b64cded5f7efdc486dced167a7dab1ff5b334daa6abd3e2e2107b38d7dbd229d59a85e1b9b3dcd27079fc5fd11", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging.Abstractions:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Logging.Abstractions@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging abstractions for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging.abstractions/6.0.0/microsoft.extensions.logging.abstractions.6.0.0.nupkg", "hash": {"value": "bfb1b4b98242104803d1a65a1a051d0b8e481fbc987fa2f4b58a610ab459b4d24e8753c515c32a376dd2c6804d1ce2d39624b972a81c68e92481958e1a8a31df", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging.Abstractions:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Logging.Abstractions@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging abstractions for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging.abstractions/8.0.0-preview.6.23329.7/microsoft.extensions.logging.abstractions.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "0f979f724eb68978eb0c01b36e293ba295c6a5dec3ae1e545d717ff9a4667ed8aea75057d5dbc4e9a46f35d809df2652fa375c0a588289cbd3c7ca7dece7a449", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging.Abstractions:8.0.0-preview.7.23375.6", "purl": "pkg:nuget/Microsoft.Extensions.Logging.Abstractions@8.0.0-preview.7.23375.6", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging abstractions for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging.abstractions/8.0.0-preview.7.23375.6/microsoft.extensions.logging.abstractions.8.0.0-preview.7.23375.6.nupkg", "hash": {"value": "76d2bace0cacdb8dc2ce518a2da057139bae1ebd715fff99862a53c9f9d742ca19025217b292c83e42ab2a5ea21d1f21fae5dc681e217ba1081dfc517fce2653", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:ObjectPool:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.ObjectPool@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "A simple object pool implementation.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.objectpool/2.2.0/microsoft.extensions.objectpool.2.2.0.nupkg", "hash": {"value": "c954c18d32b3d4b2be3ae89e9b4dc498c35f78e1d9db3028a6d29e634418dcd00877c96e5938ff589a8692aaa27c3a54a420ea7f79bd44917e65fed40e72cd92", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Extensions", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Extensions.git", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Options:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Options@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides a strongly typed way of specifying and accessing settings using dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.options/6.0.0/microsoft.extensions.options.6.0.0.nupkg", "hash": {"value": "3c34452bac02193264b0563654ba5282a74b03ede2b675ad25fa85ebd915bdcca857158153eb4a4a024065eb4e9d7bc1ebbd23485493cd895c4d049aba4a5dd2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Options:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Options@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides a strongly typed way of specifying and accessing settings using dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.options/8.0.0-preview.6.23329.7/microsoft.extensions.options.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "c0285feae1f248edcba506a01007c72240abcf03b4b445d3709310749a45dd0d0f5a93d0884dfa046819a2e406df014eafc4d4799c49e45daee609ffdc3761cd", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Options:8.0.0-preview.7.23375.6", "purl": "pkg:nuget/Microsoft.Extensions.Options@8.0.0-preview.7.23375.6", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides a strongly typed way of specifying and accessing settings using dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.options/8.0.0-preview.7.23375.6/microsoft.extensions.options.8.0.0-preview.7.23375.6.nupkg", "hash": {"value": "d53b221f0d081e4500c153ec039245c8ab0bd3d8a7c256614d25ecae86d051176e694ab162b3076d92ce502e6e5e7e1e936962e6e505f9e17a7643208874b18c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Primitives:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Primitives@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Primitives shared by framework extensions. Commonly used types include:", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.primitives/6.0.0/microsoft.extensions.primitives.6.0.0.nupkg", "hash": {"value": "0b2697f35557aeff0784b10ae6a4eafd7601bf706121ed6584a61879ec6e494514ec7a3e0da0aa6baa99f3f716f69030ec7c4c82f657c8dfdbacb637bac4547f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Primitives:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Primitives@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Primitives shared by framework extensions. Commonly used types include:", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.primitives/8.0.0-preview.6.23329.7/microsoft.extensions.primitives.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "00e45aa2fdb0391ab6e3d199dc4625a39a51918bb1aaff7c303046e42dddb943b71c2419400f64ae4f350425095b17de73620267e0ff3732e5859df88e0db657", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Primitives:8.0.0-preview.7.23375.6", "purl": "pkg:nuget/Microsoft.Extensions.Primitives@8.0.0-preview.7.23375.6", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Primitives shared by framework extensions. Commonly used types include:", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.primitives/8.0.0-preview.7.23375.6/microsoft.extensions.primitives.8.0.0-preview.7.23375.6.nupkg", "hash": {"value": "8b0918471b8e3bf10ef3f39898eab2973d05c363ebe00e62ec198d7608e1bc0b9aac6f50679b760cec6e27365e85aa6e09889b94c62826f2c63a8ce8c75aa92d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "65b696cf5e7599ad68107138a1acb643d1cedd9d", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:WebEncoders:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.WebEncoders@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "Contains registration and configuration APIs to add the core framework encoders to a dependency injection container.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.webencoders/2.2.0/microsoft.extensions.webencoders.2.2.0.nupkg", "hash": {"value": "f481d3d75bea1116b97bc72d8d59394d6b7ad2708d85f30a1a9ba2c3f870fc567b509a7eb22e31db068533c34faf1f50a0d4b8bd7131e056a324ba656b6109b1", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HtmlAbstractions", "revision": "6c5ca90d81f9013a8652da4c1752bd0b4d18e908", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HtmlAbstractions.git", "revision": "6c5ca90d81f9013a8652da4c1752bd0b4d18e908", "path": ""}}, {"id": "NuGet:Microsoft.Identity:Client:4.47.2", "purl": "pkg:nuget/Microsoft.Identity.Client@4.47.2", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "This package contains the binaries of the Microsoft Authentication Library for .NET (MSAL.NET).", "homepage_url": "https://go.microsoft.com/fwlink/?linkid=844761", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identity.client/4.47.2/microsoft.identity.client.4.47.2.nupkg", "hash": {"value": "8840a0d44cb3012b3fb40ff390ce98ced1dcee0075143e6fe848a03013187ea2389aae302f00c7757956674c86e266584549704258d04629adee9cc00e0bfedb", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/microsoft-authentication-library-for-dotnet", "revision": "a03f4c4795f779f9b93bbab45c9228f167fed0a8", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/microsoft-authentication-library-for-dotnet.git", "revision": "a03f4c4795f779f9b93bbab45c9228f167fed0a8", "path": ""}}, {"id": "NuGet:Microsoft.Identity:Client.Extensions.Msal:2.19.3", "purl": "pkg:nuget/Microsoft.Identity.Client.Extensions.Msal@2.19.3", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "This package contains extensions to Microsoft Authentication Library for .NET (MSAL.NET)", "homepage_url": "https://github.com/AzureAD/microsoft-authentication-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identity.client.extensions.msal/2.19.3/microsoft.identity.client.extensions.msal.2.19.3.nupkg", "hash": {"value": "1e85643709620051fc51cd1922354770a99daff75a7e97bd36fc6354e28e74e342ff6f1e9ea30cb962b02bbfd36d385abf443b492d2f694541d406cc58ca136f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/microsoft-authentication-extensions-for-dotnet", "revision": "8aa012d71e8a0a687633beb6fe3d18bd0046c068", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/microsoft-authentication-extensions-for-dotnet.git", "revision": "8aa012d71e8a0a687633beb6fe3d18bd0046c068", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Abstractions:6.24.0", "purl": "pkg:nuget/Microsoft.IdentityModel.Abstractions@6.24.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "A package containing thin abstractions for Microsoft.IdentityModel.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.abstractions/6.24.0/microsoft.identitymodel.abstractions.6.24.0.nupkg", "hash": {"value": "1868d58d62769cc74b3feff70a0b209781bd1822633d4df7f3414346be9b29c7fb903f2d1ffd303c98239c206f9d2101e3888d0be8c73b4dd48aecf04ca09282", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:JsonWebTokens:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.JsonWebTokens@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for creating, serializing and validating JSON Web Tokens.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.jsonwebtokens/6.15.1/microsoft.identitymodel.jsonwebtokens.6.15.1.nupkg", "hash": {"value": "0568d6ba981e8daddf7a0c72421bd9c6905d9783f4a8305c9c1cd48e431bc85bfda0d7cafe30af3452bbc4850f4d0b736a69b42c4605ce476701178c8e40fda7", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:JsonWebTokens:6.24.0", "purl": "pkg:nuget/Microsoft.IdentityModel.JsonWebTokens@6.24.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for creating, serializing and validating JSON Web Tokens.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.jsonwebtokens/6.24.0/microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg", "hash": {"value": "cabe6292e616b42e0ccf1a9f9735c65bb28fe06413d520bdfd6b44eb65bc55f3792ad2ed7eb23f78a26b76a6b38c638469a45d83498b08fcc1147f2e0c497be6", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Logging:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.Logging@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes Event Source based logging support.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.logging/6.15.1/microsoft.identitymodel.logging.6.15.1.nupkg", "hash": {"value": "4c7d956407e8a8ca02134bced7d42e6ab98c783c091c04f13ddef691378678eddcb251a3d55bcc3de9ee9bd28de2396afd87d889b03ea952355c072ff69e8598", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Logging:6.24.0", "purl": "pkg:nuget/Microsoft.IdentityModel.Logging@6.24.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes Event Source based logging support.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.logging/6.24.0/microsoft.identitymodel.logging.6.24.0.nupkg", "hash": {"value": "4fe10193906fd1b31df1793eab4bf74ce52daa76307844f1949322e5ade4d32228ef35ecb659faaa2ff68f2f41d6304d6039c67f3078b34e5b0a481199409231", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Protocols:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.Protocols@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides base protocol support for OpenIdConnect and WsFederation.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.protocols/6.15.1/microsoft.identitymodel.protocols.6.15.1.nupkg", "hash": {"value": "ff9c3a15efb8c5d0c982d5f3e4afa65e5e55b38947a50d283cfcda28717525f3ec7ff194d25882fda6ed6e998d46f4f3753d33c5d9d69fa6cbe51b8bb9bcf319", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Protocols:6.24.0", "purl": "pkg:nuget/Microsoft.IdentityModel.Protocols@6.24.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides base protocol support for OpenIdConnect and WsFederation.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.protocols/6.24.0/microsoft.identitymodel.protocols.6.24.0.nupkg", "hash": {"value": "11d992901d754e696e26b39911a43ead18617f67e0917bd2acd4a69a99b3fd127f3e8421078851ed9d5bd6f320c53182cdfa1a4254927c916aeef7b4f02114a7", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Protocols.OpenIdConnect:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.Protocols.OpenIdConnect@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for OpenIdConnect protocol.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.protocols.openidconnect/6.15.1/microsoft.identitymodel.protocols.openidconnect.6.15.1.nupkg", "hash": {"value": "5b664d6989b4f6a419bea87f932b67c5904bb3df8edaa5facedff0cf8b6e3b4e0d5b13c97779d573a17f37c7b59a63462d476de7fafdbad7987225f865ec7830", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Protocols.OpenIdConnect:6.24.0", "purl": "pkg:nuget/Microsoft.IdentityModel.Protocols.OpenIdConnect@6.24.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for OpenIdConnect protocol.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.protocols.openidconnect/6.24.0/microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg", "hash": {"value": "03d065518edaa704b9cbfb63a0191f314a262cedbff13f5340f88fcaf25c508859d9ef5ed145033b033b4001e506c34a0f99a02342e58e57b964a5f436a5c580", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Tokens:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.Tokens@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for SecurityTokens, Cryptographic operations: Signing, Verifying Signatures, Encryption.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.tokens/6.15.1/microsoft.identitymodel.tokens.6.15.1.nupkg", "hash": {"value": "6ced20e8df3a9a1beede10af5b89cb8f00d9f1fd8cae4330ed89cc0d4fa668db69d0f5fa4169a59bccfd848c4043ac6d963866ccd133b14b109e851693706573", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Tokens:6.24.0", "purl": "pkg:nuget/Microsoft.IdentityModel.Tokens@6.24.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for SecurityTokens, Cryptographic operations: Signing, Verifying Signatures, Encryption.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.tokens/6.24.0/microsoft.identitymodel.tokens.6.24.0.nupkg", "hash": {"value": "a46d49c48f2a58cacc716f92e992cdb889c762528885230d366b2db67f2e6cf7cb95a073facb3b4d8c1e4bba69a8e66ebebe12c8bc0b2497e2ece6f8b896994d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}}, {"id": "NuGet:Microsoft.NETCore:Platforms:1.1.0", "purl": "pkg:nuget/Microsoft.NETCore.Platforms@1.1.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides runtime information required to resolve target framework, platform, and runtime specific implementations of .NETCore packages. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.netcore.platforms/1.1.0/microsoft.netcore.platforms.1.1.0.nupkg", "hash": {"value": "6bf892c274596fe2c7164e3d8503b24e187f64d0b7bec6d9b05eb95f04086fceb7a85ea6b2685d42dc465c52f6f0e6f636c0b3fddac48f6f0125dfd83e92d106", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.NETCore:Platforms:2.0.0", "purl": "pkg:nuget/Microsoft.NETCore.Platforms@2.0.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides runtime information required to resolve target framework, platform, and runtime specific implementations of .NETCore packages. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.netcore.platforms/2.0.0/microsoft.netcore.platforms.2.0.0.nupkg", "hash": {"value": "0827f83639833a88ac7bb1408a3d953ee1c880a2acbbaf7abe44f084e90f5507cbb13981d962c57d0e3278ee5476d93c143eb7e9404cc7a63d7a8bf324a4fbe8", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.NETCore:Targets:1.1.0", "purl": "pkg:nuget/Microsoft.NETCore.Targets@1.1.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides supporting infrastructure for portable projects: support identifiers that define framework and runtime for support targets and packages that reference the minimum supported package versions when targeting these. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.netcore.targets/1.1.0/microsoft.netcore.targets.1.1.0.nupkg", "hash": {"value": "1ef033a68688aab9997ec1c0378acb1638b4afb618e533fcaf749d93389737ba94f4a0a94481becdf701c7e988ae2fe390136a8eae225887ee60db45063490fe", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.Net:Http.Headers:2.1.0", "purl": "pkg:nuget/Microsoft.Net.Http.Headers@2.1.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/Home/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/Home/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "HTTP header parser implementations.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.net.http.headers/2.1.0/microsoft.net.http.headers.2.1.0.nupkg", "hash": {"value": "c088baa745974bbb37e900c88417e3ce63acf50ec0c9c3a3228c75dd29fe20e224549719c83c2078c71008ec0ca86ab3374737c78d89a715cbbeee1f92570877", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "95a44cda4b956a8a57115be2a9b564b0961f5577", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "95a44cda4b956a8a57115be2a9b564b0961f5577", "path": ""}}, {"id": "NuGet:Microsoft.Net:Http.Headers:2.2.0", "purl": "pkg:nuget/Microsoft.Net.Http.Headers@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "HTTP header parser implementations.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.net.http.headers/2.2.0/microsoft.net.http.headers.2.2.0.nupkg", "hash": {"value": "7a28a6cce28280cc8751347aeb4e190d90e97dcfb930ad1524010fb01a3cc85f7a6415c7452d8445eed29327dfa0437cb33ff434b6b43059e06f90f03b04ea65", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.SqlServer:Server:1.0.0", "purl": "pkg:nuget/Microsoft.SqlServer.Server@1.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "This is a helper library for Microsoft.Data.SqlClient enabling cross framework support of UDT types.", "homepage_url": "https://aka.ms/sqlclientproject", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.sqlserver.server/1.0.0/microsoft.sqlserver.server.1.0.0.nupkg", "hash": {"value": "1bd90b08c94fadf3bb6112c95f6d34a6e1251f4710823fa995cba2ecd657146a1e594de338edb6e18ad351e300052e824ec81ed02f48d3ff2d8585169a9a4260", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/sqlclient", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/sqlclient.git", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.Win32:Registry:4.5.0", "purl": "pkg:nuget/Microsoft.Win32.Registry@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides support for accessing and modifying the Windows Registry.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.win32.registry/4.5.0/microsoft.win32.registry.4.5.0.nupkg", "hash": {"value": "2ca99fd058a083064184da33d12f72a5c43412151d426c309e2284a5df4b722b2f26dc72616ab6452a24ed81693839b756861d47eea27d60d7db7ff6749ab664", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.Win32:SystemEvents:6.0.0", "purl": "pkg:nuget/Microsoft.Win32.SystemEvents@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides access to Windows system event notifications.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.win32.systemevents/6.0.0/microsoft.win32.systemevents.6.0.0.nupkg", "hash": {"value": "5e274ace996c3eba63099ed5116f9dc39f69f684f7c1e7623c28c3c73988b75c67dfcc929a50a761f0222df243dd540720a6e588e91dfa784f81bfce7a893875", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Newtonsoft:<PERSON>son:13.0.3", "purl": "pkg:nuget/Newtonsoft.Json@13.0.3", "authors": ["<PERSON>-<PERSON>"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Json.NET is a popular high-performance JSON framework for .NET", "homepage_url": "https://www.newtonsoft.com/json", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/newtonsoft.json/13.0.3/newtonsoft.json.13.0.3.nupkg", "hash": {"value": "99b252bc77d1c5f5f7b51fd4ea7d5653e9961d7b3061cf9207f8643a9c7cc9965eebc84d6467f2989bb4723b1a244915cc232a78f894e8b748ca882a7c89fb92", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/JamesNK/Newtonsoft.Json", "revision": "0a2e291c0d9c0c7675d445703e51750363a549ef", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/JamesNK/Newtonsoft.Json.git", "revision": "0a2e291c0d9c0c7675d445703e51750363a549ef", "path": ""}}, {"id": "NuGet:Npgsql.EntityFrameworkCore:PostgreSQL:8.0.0-preview.7", "purl": "pkg:nuget/Npgsql.EntityFrameworkCore.PostgreSQL@8.0.0-preview.7", "authors": ["<PERSON>,<PERSON>,<PERSON><PERSON>"], "declared_licenses": ["PostgreSQL"], "declared_licenses_processed": {"spdx_expression": "PostgreSQL"}, "description": "PostgreSQL/Npgsql provider for Entity Framework Core.", "homepage_url": "https://github.com/npgsql/efcore.pg", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/npgsql.entityframeworkcore.postgresql/8.0.0-preview.7/npgsql.entityframeworkcore.postgresql.8.0.0-preview.7.nupkg", "hash": {"value": "e7fd5bbe5d71316b24e0a274d1ef2c84ee8fe8f872e1d4805a2d9f943f67365f53c6b72596b2ed258962a69840e3751b8d22d810cbd713701a16f198484dd69f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/npgsql/efcore.pg", "revision": "defe9bc26c3b005e4e8c624381701961dfcc401b", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/npgsql/efcore.pg.git", "revision": "defe9bc26c3b005e4e8c624381701961dfcc401b", "path": ""}}, {"id": "NuGet:Serilog:AspNetCore:7.0.0", "purl": "pkg:nuget/Serilog.AspNetCore@7.0.0", "authors": ["Microsoft,Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Serilog support for ASP.NET Core logging", "homepage_url": "https://github.com/serilog/serilog-aspnetcore", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.aspnetcore/7.0.0/serilog.aspnetcore.7.0.0.nupkg", "hash": {"value": "1f30a5a235020e8a6dfd426a2da5b957b8fb8587657004d7c30b0356f27639a3a5565db69cbe2b5bb94f0c5964affd1918dda4086686f3b23246fc67bf2287c9", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog-aspnetcore", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-aspnetcore.git", "revision": "", "path": ""}}, {"id": "NuGet:Serilog.Enrichers:Environment:2.3.0-dev-00793", "purl": "pkg:nuget/Serilog.Enrichers.Environment@2.3.0-dev-00793", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Enrich <PERSON> log events with properties from System.Environment.", "homepage_url": "http://serilog.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.enrichers.environment/2.3.0-dev-00793/serilog.enrichers.environment.2.3.0-dev-00793.nupkg", "hash": {"value": "b19bf346e5260bf52af46fdb1af642cd0d20388e44abc7b75d9cd9d3d5002c36a534201ab64b20f7c9e3a01abfdcbe990167d53b68fe99ad248c7b20d46da223", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog-enrichers-environment", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-enrichers-environment.git", "revision": "", "path": ""}}, {"id": "NuGet:Serilog.Extensions:Hosting:7.0.0", "purl": "pkg:nuget/Serilog.Extensions.Hosting@7.0.0", "authors": ["Microsoft,Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Serilog support for .NET Core logging in hosted services", "homepage_url": "https://github.com/serilog/serilog-extensions-hosting", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.extensions.hosting/7.0.0/serilog.extensions.hosting.7.0.0.nupkg", "hash": {"value": "6fcc24095a64f92197f97f8d41e6e1168e41726f1f4c81a431e5051ee3851270d1e235b0d64d0729563a9c29ffb22dcd38e603639500756dd6f530d23e6a9080", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog-extensions-hosting", "revision": "c26df53764c28973f0cdd9866a1ef33aee689a44", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-extensions-hosting.git", "revision": "c26df53764c28973f0cdd9866a1ef33aee689a44", "path": ""}}, {"id": "NuGet:Serilog.Extensions:Logging:7.0.0", "purl": "pkg:nuget/Serilog.Extensions.Logging@7.0.0", "authors": ["Microsoft,Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Low-level Serilog provider for Microsoft.Extensions.Logging", "homepage_url": "https://github.com/serilog/serilog-extensions-logging", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.extensions.logging/7.0.0/serilog.extensions.logging.7.0.0.nupkg", "hash": {"value": "7c6c4bc53e993f5e808ad5e9d3e721061d1934ee0ea3124946d145aa574dae1a0b8d3fd0472f25b01dfcfc97b25593d3df574bfa0de4771f4f101d10b1bd68f0", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog-extensions-logging.git", "revision": "710777b14a66a7c70e6cbe7f72ee52703ca58b21", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-extensions-logging.git", "revision": "710777b14a66a7c70e6cbe7f72ee52703ca58b21", "path": ""}}, {"id": "NuGet:Serilog.Formatting:Compact:1.1.0", "purl": "pkg:nuget/Serilog.Formatting.Compact@1.1.0", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "A simple, compact JSON-based event format for Serilog.", "homepage_url": "https://github.com/serilog/serilog-formatting-compact", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.formatting.compact/1.1.0/serilog.formatting.compact.1.1.0.nupkg", "hash": {"value": "8b1a37ffdfaab34ffcd0ffe0262561e65987f8a9187e810c1a43559c598e441e8ecd83b83e1c7388575595f8bcf1951e788ea610a65385f6291228a5293a7a8b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-formatting-compact.git", "revision": "", "path": ""}}, {"id": "NuGet:Serilog.Settings:Configuration:7.0.0", "purl": "pkg:nuget/Serilog.Settings.Configuration@7.0.0", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Microsoft.Extensions.Configuration (appsettings.json) support for Serilog.", "homepage_url": "", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.settings.configuration/7.0.0/serilog.settings.configuration.7.0.0.nupkg", "hash": {"value": "f69c46b6aee8da433160f1733213630cb6d2aed466533bc7bba2cacbafe0bc6890ab735eec03016ee633633c025fbc72f9a19a21e46c13d60f1b9354b1399a78", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog-settings-configuration.git", "revision": "e8783352aff2f46b52d41c4232621c9d4b9ee2d7", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-settings-configuration.git", "revision": "e8783352aff2f46b52d41c4232621c9d4b9ee2d7", "path": ""}}, {"id": "NuGet:Serilog.Sinks:Console:4.0.1", "purl": "pkg:nuget/Serilog.Sinks.Console@4.0.1", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "A Serilog sink that writes log events to the console/terminal.", "homepage_url": "https://github.com/serilog/serilog-sinks-console", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.sinks.console/4.0.1/serilog.sinks.console.4.0.1.nupkg", "hash": {"value": "fe74a57683bf12e8126e8158526445f2f110ff24a83b06f516e587e2e0f1db0f917259a8bd1420a917c943106820296e063ee7e3ea7517b5d0e355358e9c8134", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog-sinks-console", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-sinks-console.git", "revision": "", "path": ""}}, {"id": "NuGet:Serilog.Sinks:Console:4.1.0", "purl": "pkg:nuget/Serilog.Sinks.Console@4.1.0", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "A Serilog sink that writes log events to the console/terminal.", "homepage_url": "https://github.com/serilog/serilog-sinks-console", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.sinks.console/4.1.0/serilog.sinks.console.4.1.0.nupkg", "hash": {"value": "3ce2d33bf374074657f175402e07929b5952c1345ceedce8a6162b8fb98ab123a2b9ce9daa6fe2309ef1b5e59ff1f59ac60017b89fd89767276dcb00ea5573f3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog-sinks-console", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-sinks-console.git", "revision": "", "path": ""}}, {"id": "NuGet:Serilog.Sinks:Debug:2.0.0", "purl": "pkg:nuget/Serilog.Sinks.Debug@2.0.0", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "A Serilog sink that writes log events to the debug output window.", "homepage_url": "https://github.com/serilog/serilog-sinks-debug", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.sinks.debug/2.0.0/serilog.sinks.debug.2.0.0.nupkg", "hash": {"value": "fbddb39441be29aee4077c487e321ab0c3a167adc74f698115a5412d989e4d33c2a8d1cd9fcb96b312c567cb293d23f8431c936d9647691e019600a405c5cc6d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog-sinks-debug", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-sinks-debug.git", "revision": "", "path": ""}}, {"id": "NuGet:Serilog.Sinks:File:5.0.0", "purl": "pkg:nuget/Serilog.Sinks.File@5.0.0", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Write Serilog events to text files in plain or JSON format.", "homepage_url": "https://serilog.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog.sinks.file/5.0.0/serilog.sinks.file.5.0.0.nupkg", "hash": {"value": "e0139b1c37bbc6e8dcf4b44f696fae1212c7793a69d599d3a555f69d2ceaa92f2417a0d4d2845d80ea8be494d4fd994841b916b197f8dc597afb6a6d91528356", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog-sinks-file", "revision": "7eb21bd4d35d0b8b7d13e6a15851c9903ea9a468", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog-sinks-file.git", "revision": "7eb21bd4d35d0b8b7d13e6a15851c9903ea9a468", "path": ""}}, {"id": "NuGet:SqlKata:Execution:3.0.0-beta", "purl": "pkg:nuget/SqlKata.Execution@3.0.0-beta", "authors": ["<PERSON>"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Adds the execution capabilities for SqlKata", "homepage_url": "https://github.com/sqlkata/querybuilder", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/sqlkata.execution/3.0.0-beta/sqlkata.execution.3.0.0-beta.nupkg", "hash": {"value": "eadf9829d8a3794a20118654dd5d6b9c8b4e859f231bb934d55083dba92cf0fcbfca37021639c9f5b70bc7dfe008aabf4c0ba5fb50db4f3aed12606582dfcdf2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/sqlkata/querybuilder", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/sqlkata/querybuilder.git", "revision": "", "path": ""}}, {"id": "NuGet:Swashbuckle.AspNetCore:Swagger:6.5.0", "purl": "pkg:nuget/Swashbuckle.AspNetCore.Swagger@6.5.0", "authors": ["Swashbuckle.AspNetCore.Swagger"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Middleware to expose Swagger JSON endpoints from APIs built on ASP.NET Core", "homepage_url": "https://github.com/domaindrivendev/Swashbuckle.AspNetCore", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/swashbuckle.aspnetcore.swagger/6.5.0/swashbuckle.aspnetcore.swagger.6.5.0.nupkg", "hash": {"value": "679703c1d87b5788a0cb71eba024d4f6808a4ada223eae79e68954e55c016458419ec5d28bf371742e8795ca7ef13d395193ba68d8fb1314a119b0b0f2040d80", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/domaindrivendev/Swashbuckle.AspNetCore.git", "revision": "8f363f7359cb1cb8fa5de5195ec6d97aefaa16b3", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/domaindrivendev/Swashbuckle.AspNetCore.git", "revision": "8f363f7359cb1cb8fa5de5195ec6d97aefaa16b3", "path": ""}}, {"id": "NuGet:Swashbuckle.AspNetCore:SwaggerGen:6.5.0", "purl": "pkg:nuget/Swashbuckle.AspNetCore.SwaggerGen@6.5.0", "authors": ["Swashbuckle.AspNetCore.SwaggerGen"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Swagger Generator for APIs built on ASP.NET Core", "homepage_url": "https://github.com/domaindrivendev/Swashbuckle.AspNetCore", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/swashbuckle.aspnetcore.swaggergen/6.5.0/swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg", "hash": {"value": "9a3de8f8a06acfa7f07e5e96339d7e4d404da7b203a895acc0d2b805d29f6a89df7a4ef3aa56be1dc5fbd93fab899feef3d5c396cdda7e695c96f393450c0124", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/domaindrivendev/Swashbuckle.AspNetCore.git", "revision": "8f363f7359cb1cb8fa5de5195ec6d97aefaa16b3", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/domaindrivendev/Swashbuckle.AspNetCore.git", "revision": "8f363f7359cb1cb8fa5de5195ec6d97aefaa16b3", "path": ""}}, {"id": "NuGet:Swashbuckle.AspNetCore:SwaggerUI:6.5.0", "purl": "pkg:nuget/Swashbuckle.AspNetCore.SwaggerUI@6.5.0", "authors": ["Swashbuckle.AspNetCore.SwaggerUI"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Middleware to expose an embedded version of the swagger-ui from an ASP.NET Core application", "homepage_url": "https://github.com/domaindrivendev/Swashbuckle.AspNetCore", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/swashbuckle.aspnetcore.swaggerui/6.5.0/swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg", "hash": {"value": "9b3bd0ed6308aac9c43bdabe81ea5ee2c8e6592051a9ef302478825d6444df57419178329a67c86588c20a31acd486bce7f2b99528cf3b703117ae90ce308858", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/domaindrivendev/Swashbuckle.AspNetCore.git", "revision": "8f363f7359cb1cb8fa5de5195ec6d97aefaa16b3", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/domaindrivendev/Swashbuckle.AspNetCore.git", "revision": "8f363f7359cb1cb8fa5de5195ec6d97aefaa16b3", "path": ""}}, {"id": "NuGet:System:Buffers:4.5.0", "purl": "pkg:nuget/System.Buffers@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides resource pooling of any type for performance-critical applications that allocate and deallocate objects frequently.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.buffers/4.5.0/system.buffers.4.5.0.nupkg", "hash": {"value": "d3dc8bfd088f103648f492e0d11d0e7067bfd327059baf50375e830af5e4aa4228ac20b563610ac24e4abd295f3261ac7be2dc2a40f71fe0ab6bb7c59311d712", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Collections:4.3.0", "purl": "pkg:nuget/System.Collections@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes that define generic collections, which allow developers to create strongly typed collections that provide better type safety and performance than non-generic strongly typed collections.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.collections/4.3.0/system.collections.4.3.0.nupkg", "hash": {"value": "ca7b952d30da1487ca4e43aa522817b5ee26e7e10537062810112fc67a7512766c39d402f394bb0426d1108bbcf9bbb64e9ce1f5af736ef215a51a35e55f051b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Globalization:4.3.0", "purl": "pkg:nuget/System.Globalization@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes that define culture-related information, including language, country/region, calendars in use, format patterns for dates, currency, and numbers, and sort order for strings.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.globalization/4.3.0/system.globalization.4.3.0.nupkg", "hash": {"value": "823d2ba308cb073b40a3146ecccd0d9fd7b1615ac3fbefb16f73d873e411fd81c3bdc87df206d3dc7e2f14c9cd53aafca684a3570c25471280aada8de805ece2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:IO:4.3.0", "purl": "pkg:nuget/System.IO@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides base input and output (I/O) types, including System.IO.Stream, System.IO.StreamReader and System.IO.StreamWriter, that allow reading and writing to data streams", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.io/4.3.0/system.io.4.3.0.nupkg", "hash": {"value": "bfca5a21e3e1986b9765b13dc6fbcd6f8b89e4c1383855d1d7ef256bf1bf2f51889769db5365859dd7606fbf6454add4daeb3bab56994ffb98fd1d03fe8bc1e6", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Linq:4.3.0", "purl": "pkg:nuget/System.Linq@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes and interfaces that supports queries that use Language-Integrated Query (LINQ).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.linq/4.3.0/system.linq.4.3.0.nupkg", "hash": {"value": "eacc7fe1ec526f405f5ba0e671f616d0e5be9c1828d543a9e2f8c65df4099d6b2ea4a9fa2cdae4f34b170dc37142f60e267e137ca39f350281ed70d2dc620458", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Memory:4.5.3", "purl": "pkg:nuget/System.Memory@4.5.3", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides types for efficient representation and pooling of managed, stack, and native memory segments and sequences of such segments, along with primitives to parse and format UTF-8 encoded text stored in those memory segments.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.memory/4.5.3/system.memory.4.5.3.nupkg", "hash": {"value": "70fce15a52cc76aacbae05c8e89e2e398d1d32903f63f640a7dd4a3e5747f2c7a887d4bfd22f2a2e40274906cf91648dfd169734fb7c74eb9b4f72614084e1db", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Memory:4.5.4", "purl": "pkg:nuget/System.Memory@4.5.4", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides types for efficient representation and pooling of managed, stack, and native memory segments and sequences of such segments, along with primitives to parse and format UTF-8 encoded text stored in those memory segments.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.memory/4.5.4/system.memory.4.5.4.nupkg", "hash": {"value": "8ece5491eb0fe332bc190f40cf76b3beee0c5f996325034861be221fdb0ff02fd59e4f7020b3c4a1f29a457f76ff76c4c95d46d38555e4f48c7a3bf172d87966", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Reflection:4.3.0", "purl": "pkg:nuget/System.Reflection@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides types that retrieve information about assemblies, modules, members, parameters, and other entities in managed code by examining their metadata. These types also can be used to manipulate instances of loaded types, for example to hook up events or to invoke methods.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.reflection/4.3.0/system.reflection.4.3.0.nupkg", "hash": {"value": "2325b67ed60dce0302807064f25422cbe1b7fb275b539b44fba3c4a8ce4926f21d78529a5c34b31c03d80d110f7bace9af9589d457266beac014220057af8333", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Runtime:4.3.0", "purl": "pkg:nuget/System.Runtime@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides the fundamental primitives, classes and base classes that define commonly-used value and reference data types, events and event handlers, interfaces, attributes, and exceptions. This packages represents the core package, and provides the minimal set of types required to build a managed application.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime/4.3.0/system.runtime.4.3.0.nupkg", "hash": {"value": "92ab2249f08073cfafdc4cfbd7db36d651ad871b8d8ba961006982187de374bf4a30af93f15f73b05af343f7a70cbd484b04d646570587636ae72171eb0714fb", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Threading:4.3.0", "purl": "pkg:nuget/System.Threading@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides the fundamental synchronization primitives, including System.Threading.Monitor and System.Threading.Mutex, that are required when writing asynchronous code.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.threading/4.3.0/system.threading.4.3.0.nupkg", "hash": {"value": "97a2751bdce69faaf9c54f834a9fd5c60c7a786faa52f420769828dbc9b5804c1f3721ba1ea945ea1d844835d909810f9e782c9a44d0faaecccb230c4cd95a88", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:ValueTuple:4.4.0", "purl": "pkg:nuget/System.ValueTuple@4.4.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides the System.ValueTuple structs, which implement the underlying types for tuples in C# and Visual Basic.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.valuetuple/4.4.0/system.valuetuple.4.4.0.nupkg", "hash": {"value": "841974ff23eab4c9f0aeca82612c58af801bbe8f614956267fee791278843320843c944cd551abf28fa41de1c9744aa0afac2b40c859ec7a306267a3aee5d2b6", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Collections:Concurrent:4.3.0", "purl": "pkg:nuget/System.Collections.Concurrent@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides several thread-safe collection classes that should be used in place of the corresponding types in the System.Collections.NonGeneric and System.Collections packages whenever multiple threads are accessing the collection concurrently.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.collections.concurrent/4.3.0/system.collections.concurrent.4.3.0.nupkg", "hash": {"value": "35c1aa3e636216fe5dc2ebeb504293e69ad6355d26e22453af060af94d8279faa93bdcfe127aecb0b316c7e7d9185bcac72e994984efdb7f2d8515f1f55cf682", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.ComponentModel:Annotations:4.4.1", "purl": "pkg:nuget/System.ComponentModel.Annotations@4.4.1", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides attributes that are used to define metadata for objects used as data sources.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.componentmodel.annotations/4.4.1/system.componentmodel.annotations.4.4.1.nupkg", "hash": {"value": "ffc5d2473ddc81248f11d895e9416195f93afc55fd3f5d0c0236b85cf0b2302cfbff4613aa3b428787b48e32d838224bf232bb3bfbe1b548d8a354ce4ae3e781", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Configuration:ConfigurationManager:6.0.1", "purl": "pkg:nuget/System.Configuration.ConfigurationManager@6.0.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides types that support using configuration files.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.configuration.configurationmanager/6.0.1/system.configuration.configurationmanager.6.0.1.nupkg", "hash": {"value": "80983931d4f1155bf191fadad6025d4fde3535f1b96fa9cea424eaf7c85aeca8280ed2822e1e1826c30324ec03eaa704c307ccb5c5fab26314416290378d77b9", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "163a63591cf9e9b682063cf3995948c2b885a042", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "163a63591cf9e9b682063cf3995948c2b885a042", "path": ""}}, {"id": "NuGet:System.Diagnostics:Debug:4.3.0", "purl": "pkg:nuget/System.Diagnostics.Debug@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes and attributes that allows basic interaction with a debugger.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.diagnostics.debug/4.3.0/system.diagnostics.debug.4.3.0.nupkg", "hash": {"value": "6c58fe1e3618e7f87684c1cea7efc7d3b19bd7df8d2535f9e27b62c52f441f11b67b21225d6bcd62f409e02c2a16231c4db19be33b8fab5b9b0a5c8660ddab24", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Diagnostics:DiagnosticSource:6.0.0", "purl": "pkg:nuget/System.Diagnostics.DiagnosticSource@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides Classes that allow you to decouple code logging rich (unserializable) diagnostics/telemetry (e.g. framework) from code that consumes it (e.g. tools)", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.diagnostics.diagnosticsource/6.0.0/system.diagnostics.diagnosticsource.6.0.0.nupkg", "hash": {"value": "7589e6a3ae9b8ad7c7c4b8cd054d8b3e9e839fdf27ee147293b64a195cda00fc36307cbee3474bc5fc3bb2eb3132459f2f70bffda245fbf50300f807d9885466", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Diagnostics:Tracing:4.3.0", "purl": "pkg:nuget/System.Diagnostics.Tracing@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides class that enable you to create high performance tracing events to be captured by event tracing for Windows (ETW).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.diagnostics.tracing/4.3.0/system.diagnostics.tracing.4.3.0.nupkg", "hash": {"value": "d0a5d30e261cd45b7dfab02b7ffbd76b64e0c9b892ed826ea61481c983c0208b05b69981cd79e91cd4e5811e1cd4c3cea06a1afce05811ece58be5e4c20169ea", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Drawing:Common:6.0.0", "purl": "pkg:nuget/System.Drawing.Common@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides access to GDI+ graphics functionality.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.drawing.common/6.0.0/system.drawing.common.6.0.0.nupkg", "hash": {"value": "d61f0a3e01c3eac15f13fc1ba04a2c7ce4eac956400b2faa361fecabd3836d49d5bd344f3985ee3d94cdc3f6a72b8e07e423cdb2965b4f5ca2222b5de32988e4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Formats:Asn1:5.0.0", "purl": "pkg:nuget/System.Formats.Asn1@5.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides classes that can read and write the ASN.1 BER, CER, and DER data formats.", "homepage_url": "https://github.com/dotnet/runtime", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.formats.asn1/5.0.0/system.formats.asn1.5.0.0.nupkg", "hash": {"value": "01c6999e0a8984f4ce94cb731748cd88113e5bd4411031bd281fe1ef637f973090af49d00515e00fd87ce9801711c23a3119ecbdb2d1fe17247ac9e55b434b99", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+git://github.com/dotnet/runtime", "revision": "cf258a14b70ad9069470a108f13765e0e5988f51", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "cf258a14b70ad9069470a108f13765e0e5988f51", "path": ""}}, {"id": "NuGet:System.IdentityModel:Tokens.Jwt:6.15.1", "purl": "pkg:nuget/System.IdentityModel.Tokens.Jwt@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for creating, serializing and validating JSON Web Tokens.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.identitymodel.tokens.jwt/6.15.1/system.identitymodel.tokens.jwt.6.15.1.nupkg", "hash": {"value": "f28e616fc965aae6fe142f08450e8eafc6414f1c9d305bc008543ebef5fe25484482883df1b7e28b72acbcb7186c63026b97571099feffe5ba59c28b2c6b3232", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:System.IdentityModel:Tokens.Jwt:6.24.0", "purl": "pkg:nuget/System.IdentityModel.Tokens.Jwt@6.24.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for creating, serializing and validating JSON Web Tokens.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.identitymodel.tokens.jwt/6.24.0/system.identitymodel.tokens.jwt.6.24.0.nupkg", "hash": {"value": "ac3fa8a6acff0ac15fb4205762893c196d8a60ae90940662e13e64859485ec859df180832783a49fc27a8ac48f4f7835e0344abbee0ec40bae0006a3bf66d6f3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "779de3802c12c2b5331424e6079e76b06183757a", "path": ""}}, {"id": "NuGet:System.Memory:Data:1.0.2", "purl": "pkg:nuget/System.Memory.Data@1.0.2", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Contains the BinaryData type, which is useful for converting between strings, streams, JSON, and bytes.", "homepage_url": "https://github.com/Azure/azure-sdk-for-net/blob/System.Memory.Data_1.0.2/sdk/core/System.Memory.Data/README.md", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.memory.data/1.0.2/system.memory.data.1.0.2.nupkg", "hash": {"value": "089ebd3f3d9bc6f3c24c8f081087a03ec5a655d4b914f4612633b7d45ed0f8ba8404978fd6ff1892aa45bf7fade0bd806dfff3a0c27f19500785262d333e3220", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/Azure/azure-sdk-for-net", "revision": "7e3cf643977591e9041f4c628fd4d28237398e0b", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/Azure/azure-sdk-for-net.git", "revision": "7e3cf643977591e9041f4c628fd4d28237398e0b", "path": ""}}, {"id": "NuGet:System.Numerics:Vectors:4.5.0", "purl": "pkg:nuget/System.Numerics.Vectors@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides hardware-accelerated numeric types, suitable for high-performance processing and graphics applications.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.numerics.vectors/4.5.0/system.numerics.vectors.4.5.0.nupkg", "hash": {"value": "9c04ec0530f608aaf801837a791b33857e2ca6d2265a6049c01fd4e972825967e709cad3070f174829b7400f608e9a641d3afc3a45d4636d4c47dd43dd0657b3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Reflection:Metadata:5.0.0", "purl": "pkg:nuget/System.Reflection.Metadata@5.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "This packages provides a low-level .NET (ECMA-335) metadata reader and writer. It's geared for performance and is the ideal choice for building higher-level libraries that intend to provide their own object model, such as compilers.", "homepage_url": "https://github.com/dotnet/runtime", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.reflection.metadata/5.0.0/system.reflection.metadata.5.0.0.nupkg", "hash": {"value": "3b74e3e491eee87a8410f5b9a2e556233d9919267f6a054da7a4c9c34b6916b07c77ea9ef8cceb5b7c3361e7394e502cc3c9a09247c6a06bb58509e82554e527", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+git://github.com/dotnet/runtime", "revision": "cf258a14b70ad9069470a108f13765e0e5988f51", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "cf258a14b70ad9069470a108f13765e0e5988f51", "path": ""}}, {"id": "NuGet:System.Reflection:Primitives:4.3.0", "purl": "pkg:nuget/System.Reflection.Primitives@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides common enumerations for reflection-based libraries.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg", "hash": {"value": "d4b9cc905f5a5cab900206338e889068bf66c18ee863a29d68eff3cde2ccca734112a2a851f2e2e5388a21ec28005fa19317c64d9b23923b05d6344be2e49eaa", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Resources:ResourceManager:4.3.0", "purl": "pkg:nuget/System.Resources.ResourceManager@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes and attributes that allow developers to create, store, and manage various culture-specific resources used in an application.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.resources.resourcemanager/4.3.0/system.resources.resourcemanager.4.3.0.nupkg", "hash": {"value": "9067db28f1c48d08fc52ad40a608f88c14ad9112646741ddaf426fdfe68bed61ab01954b179461e61d187371600c1e6e5c36c788993f5a105a64f5702a6b81d4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Runtime:Caching:6.0.0", "purl": "pkg:nuget/System.Runtime.Caching@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides classes to use caching facilities.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.caching/6.0.0/system.runtime.caching.6.0.0.nupkg", "hash": {"value": "001810a2563bfc28561bc3e3e84072b0c8ed8f508354937b696796b975e1fb2eda4be1cc8cec31d4e662fecfff76250752b762cc02279a05ab6b2e39832e4442", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Runtime:CompilerServices.Unsafe:6.0.0", "purl": "pkg:nuget/System.Runtime.CompilerServices.Unsafe@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides the System.Runtime.CompilerServices.Unsafe class, which provides generic, low-level functionality for manipulating pointers.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg", "hash": {"value": "d4057301be4ec4936f24b9ce003b5ec4d99681ab6d9b65d5393dd38d04cdec37784aaa12c1a8b50ac3767ed878dae425749490773fec01e734f93cf1045822b3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Runtime:Extensions:4.3.0", "purl": "pkg:nuget/System.Runtime.Extensions@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides commonly-used classes for performing mathematical functions, conversions, string comparisons and querying environment information.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.extensions/4.3.0/system.runtime.extensions.4.3.0.nupkg", "hash": {"value": "680a32b19c2bd5026f8687aa5382aea4f432b4f032f8bde299facb618c56d57369adef7f7cc8e60ad82ae3c12e5dd50772491363bf8044c778778628a6605bbc", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Runtime:Handles:4.3.0", "purl": "pkg:nuget/System.Runtime.Handles@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides base classes, including System.Runtime.InteropServices.CriticalHandle and System.Runtime.InteropServices.SafeHandle, for types that represent operating system handles.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.handles/4.3.0/system.runtime.handles.4.3.0.nupkg", "hash": {"value": "0a5baf1dd554bf9e01bcb4ce082cb26ee82b783364feb47cba730faeecd70edc528efad0394dcce11f37d7f9507f8608f15629ebaf051906bfd3513e46af0f11", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Runtime:InteropServices:4.3.0", "purl": "pkg:nuget/System.Runtime.InteropServices@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides types that support COM interop and platform invoke services.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.interopservices/4.3.0/system.runtime.interopservices.4.3.0.nupkg", "hash": {"value": "650799c3e654efbb9ad67157c9c60ce46f288a81597be37ce2a0bf5d4835044065ef3f65b997328cbbbbfb81f4c89b8d7e7d61380880019deee6eb3f963f70d9", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Runtime:Numerics:4.3.0", "purl": "pkg:nuget/System.Runtime.Numerics@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides the numeric types System.Numerics.BigInteger and System.Numerics.Complex, which complement the numeric primitives, such as System.Byte, System.Double and System.Int32.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.numerics/4.3.0/system.runtime.numerics.4.3.0.nupkg", "hash": {"value": "3e347faa8e7ec484d481e53b1c219fe1ce346ae8278a214b4508cf0e233c1627bd9c6c6c7c654e8c1f4143271838ddd9593f63a1043577ad87c40e392af7fd34", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:AccessControl:4.5.0", "purl": "pkg:nuget/System.Security.AccessControl@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides base classes that enable managing access and audit control lists on securable objects.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.accesscontrol/4.5.0/system.security.accesscontrol.4.5.0.nupkg", "hash": {"value": "e9142d713f93c8380b505b009e699d7d144674b60ac526469123ce774e76b6f605c4e4cc6906fa00d970846a99b4d3b9d8fa2c682a17bbbb9ab459deba303198", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:AccessControl:6.0.0", "purl": "pkg:nuget/System.Security.AccessControl@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides base classes that enable managing access and audit control lists on securable objects.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.accesscontrol/6.0.0/system.security.accesscontrol.6.0.0.nupkg", "hash": {"value": "64a36a103b954ab4b7e8a76c0e876579bd484c308e444c2d915fb9a0fd05ad63614501ed235c544afc9b431cb8a4cf0f0715b8ed414e85958e6d68579168fb45", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Algorithms:4.3.1", "purl": "pkg:nuget/System.Security.Cryptography.Algorithms@4.3.1", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides base types for cryptographic algorithms, including hashing, encryption, and signing operations.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.algorithms/4.3.1/system.security.cryptography.algorithms.4.3.1.nupkg", "hash": {"value": "34b02b60b69a54e1310b511512e3210a49bfb0584c50f80b59c586d7180ad973e3b5419f7a55783837884395460db3e0db13c8fb27de6d170b8d2dab1c90adcf", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Cng:4.7.0", "purl": "pkg:nuget/System.Security.Cryptography.Cng@4.7.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides cryptographic algorithm implementations and key management with Windows Cryptographic Next Generation API (CNG).", "homepage_url": "https://github.com/dotnet/corefx", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.cng/4.7.0/system.security.cryptography.cng.4.7.0.nupkg", "hash": {"value": "b0ee54be292ea15b02b82d9925399065deb6dae5aa1bb71771bb9467e8f53882b26a0ddc6ff43121b4d2999c5858399e61a779e04d14a4f4e8e0dfcf8baebbba", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/corefx.git", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Cng:5.0.0", "purl": "pkg:nuget/System.Security.Cryptography.Cng@5.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides cryptographic algorithm implementations and key management with Windows Cryptographic Next Generation API (CNG).", "homepage_url": "https://github.com/dotnet/runtime", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.cng/5.0.0/system.security.cryptography.cng.5.0.0.nupkg", "hash": {"value": "b6bbe402495487333eff3f5b3e71a60cbce66ef0f49752250ba829151a738c62f295382d4cfaa6f14bfbb670c14ee05039b8c4641c4d4b46c28488bacd0095f7", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+git://github.com/dotnet/runtime", "revision": "cf258a14b70ad9069470a108f13765e0e5988f51", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "cf258a14b70ad9069470a108f13765e0e5988f51", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Encoding:4.3.0", "purl": "pkg:nuget/System.Security.Cryptography.Encoding@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides types for representing Abstract Syntax Notation One (ASN.1)-encoded data.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.encoding/4.3.0/system.security.cryptography.encoding.4.3.0.nupkg", "hash": {"value": "5c26add23e63542f37506f5fa1f72e8980f03743d529cd8e583d1054b8d8a579fb773fa035a00d9073db84db6be4f47cac340d1ebc6d23dd761dbdbd600075e0", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Pkcs:4.5.0", "purl": "pkg:nuget/System.Security.Cryptography.Pkcs@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides support for PKCS and CMS algorithms.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.pkcs/4.5.0/system.security.cryptography.pkcs.4.5.0.nupkg", "hash": {"value": "a4fb3d1528ef5f2ae99b9e393277815021a68c556fae06bec09d7348f5757d8dff478c39341197a5eaf1d9d63ce58be80efa2db3e99b2f00c0a8d6ff511f037a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Primitives:4.3.0", "purl": "pkg:nuget/System.Security.Cryptography.Primitives@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides common types for the cryptographic libraries.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.primitives/4.3.0/system.security.cryptography.primitives.4.3.0.nupkg", "hash": {"value": "5ad8273f998ebb9cca2f7bd03143d3f6d57b5d560657b26d6f4e78d038010fb30c379a23a27c08730f15c9b66f4ba565a06984ec246dfc79acf1a741b0dd4347", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.ProtectedData:6.0.0", "purl": "pkg:nuget/System.Security.Cryptography.ProtectedData@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides access to Windows Data Protection Api.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.protecteddata/6.0.0/system.security.cryptography.protecteddata.6.0.0.nupkg", "hash": {"value": "489b5dab0abfadfb8bc2d0437de83a1447918071949440e766db701c81c3518de6a38a3e0f699706b06d591ab5393c7bc0b2eaa81c15bff156339248e6c35730", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Xml:4.5.0", "purl": "pkg:nuget/System.Security.Cryptography.Xml@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides classes to support the creation and validation of XML digital signatures. The classes in this namespace implement the World Wide Web Consortium Recommendation, \"XML-Signature Syntax and Processing\", described at http://www.w3.org/TR/xmldsig-core/.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.xml/4.5.0/system.security.cryptography.xml.4.5.0.nupkg", "hash": {"value": "5fefed68b5d3298ce22af584e19132869634410d7b7e6f5e5bbd1cbcc084e8b02c598a9dbfbd3c8b41c9792572effe51e79e3b191fc21063a352462ae9b25f3e", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Permissions:4.5.0", "purl": "pkg:nuget/System.Security.Permissions@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides types supporting Code Access Security (CAS). ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.permissions/4.5.0/system.security.permissions.4.5.0.nupkg", "hash": {"value": "3be7bda9a9924c9e183890a99cb82c7fd15319fb3126fd8e9f539d62486677f5c0e40611bd46ad933ed1a77752f5747bae156e9259e3493d5d46830aecde1c1b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Permissions:6.0.0", "purl": "pkg:nuget/System.Security.Permissions@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides types supporting Code Access Security (CAS).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.permissions/6.0.0/system.security.permissions.6.0.0.nupkg", "hash": {"value": "d4f2172cc3b164f104fa2e3a330b62f2a15f50e050a91659db5728f28d4d5d6ca8660eec3a4f922090181a54bc1e9f6634ca49750398360727d1bc59db620278", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Security:Principal.Windows:4.5.0", "purl": "pkg:nuget/System.Security.Principal.Windows@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides classes for retrieving the current Windows user and for interacting with Windows users and groups.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.principal.windows/4.5.0/system.security.principal.windows.4.5.0.nupkg", "hash": {"value": "86cdb3178b4e437578890b6d5672eb9d1fe2f003abac082ed869a9e3f8cd684ffee618995838f6d052bf9bf396dc8b5d8bd5c3bea7f9e56cc7922598b4e49436", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Principal.Windows:5.0.0", "purl": "pkg:nuget/System.Security.Principal.Windows@5.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides classes for retrieving the current Windows user and for interacting with Windows users and groups.", "homepage_url": "https://github.com/dotnet/runtime", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.principal.windows/5.0.0/system.security.principal.windows.5.0.0.nupkg", "hash": {"value": "44a920aaaf22b2172d41319bb57ab2b8e1a4531d5f02192a6f53a81d875125195b60ba0b5a44a45981d137fd7b0f3a65b12959b5fd97afc0578cd84ef27467cd", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+git://github.com/dotnet/runtime", "revision": "cf258a14b70ad9069470a108f13765e0e5988f51", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "cf258a14b70ad9069470a108f13765e0e5988f51", "path": ""}}, {"id": "NuGet:System.Text:Encoding:4.3.0", "purl": "pkg:nuget/System.Text.Encoding@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides base abstract encoding classes for converting blocks of characters to and from blocks of bytes.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg", "hash": {"value": "6ff7feec7313a7121f795ec7d376e4b8728c17294219fafdfd4ea078f9df1455b4685f0b3962c3810098e95d68594a8392c0b799d36ec8284cd6fcbd4cfe2c67", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Text:Encoding.CodePages:6.0.0", "purl": "pkg:nuget/System.Text.Encoding.CodePages@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides support for code-page based encodings, including Windows-1252, Shift-JIS, and GB2312.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.encoding.codepages/6.0.0/system.text.encoding.codepages.6.0.0.nupkg", "hash": {"value": "ec873a95ec517de2c5a5364ada30974ddd5e0fafef2ad2517609a1900b5059d35757536fd073805001fa68d5b56a3d4647010a96c9eb233b1d172a3b45fbe4a9", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Text:Encodings.Web:4.5.0", "purl": "pkg:nuget/System.Text.Encodings.Web@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides types for encoding and escaping strings for use in JavaScript, HyperText Markup Language (HTML), and uniform resource locators (URL).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.encodings.web/4.5.0/system.text.encodings.web.4.5.0.nupkg", "hash": {"value": "f802fbbcfe00a5f552092c6987033f7cd794a7b8a3ed6fc6b9b7378c12bdc081b94a7ced869447a4a79322eb47457973ba497daa07c6a94ca64388cf9282a279", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Text:Encodings.Web:6.0.0", "purl": "pkg:nuget/System.Text.Encodings.Web@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides types for encoding and escaping strings for use in JavaScript, HyperText Markup Language (HTML), and uniform resource locators (URL).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.encodings.web/6.0.0/system.text.encodings.web.6.0.0.nupkg", "hash": {"value": "0f26afeeaa709ea1f05ef87058408dd9df640c869d7398b2c9c270268ddf21a9208cd7d2bfa1f7fbd8a5ceab735dd22d470a3689627c9c4fadc0ea5fe76237fa", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Text:Encodings.Web:7.0.0", "purl": "pkg:nuget/System.Text.Encodings.Web@7.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides types for encoding and escaping strings for use in JavaScript, HyperText Markup Language (HTML), and uniform resource locators (URL).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.encodings.web/7.0.0/system.text.encodings.web.7.0.0.nupkg", "hash": {"value": "d164c15df021a99d18ed0c39b6b7c0290b7f948d8f09bf07140b47bae6403f1cb9a822c1504aabd7a6094367ad9fcf8ced1ea186b0662a51815ebbb37a3b0434", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}}, {"id": "NuGet:System.Text:Encodings.Web:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/System.Text.Encodings.Web@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides types for encoding and escaping strings for use in JavaScript, HyperText Markup Language (HTML), and uniform resource locators (URL).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.encodings.web/8.0.0-preview.6.23329.7/system.text.encodings.web.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "78af3088932a1637946b4835045bb5eff69d69da48c6e7f43c833e863e0cc24e63df3d7117dad737c0a91c042381b438a1b43a0c22ba407acb5e5957e57878f6", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:System.Text:<PERSON>son:4.7.2", "purl": "pkg:nuget/System.Text.Json@4.7.2", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides high-performance and low-allocating types that serialize objects to JavaScript Object Notation (JSON) text and deserialize JSON text to objects, with UTF-8 support built-in. Also provides types to read and write JSON text encoded as UTF-8, and to create an in-memory document object model (DOM), that is read-only, for random access of the JSON elements within a structured view of the data.", "homepage_url": "https://github.com/dotnet/corefx", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.json/4.7.2/system.text.json.4.7.2.nupkg", "hash": {"value": "345d15c74a9ac6ddabeeba349b9ba027e48b423972984ff202ea74b5413dd502236817746a82485c840a0cfc818179b94548731db5f361b07a4d11bdda20f50f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/corefx.git", "revision": "", "path": ""}}, {"id": "NuGet:System.Text:<PERSON>son:5.0.2", "purl": "pkg:nuget/System.Text.Json@5.0.2", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides high-performance and low-allocating types that serialize objects to JavaScript Object Notation (JSON) text and deserialize JSON text to objects, with UTF-8 support built-in. Also provides types to read and write JSON text encoded as UTF-8, and to create an in-memory document object model (DOM), that is read-only, for random access of the JSON elements within a structured view of the data.", "homepage_url": "https://github.com/dotnet/runtime", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.json/5.0.2/system.text.json.5.0.2.nupkg", "hash": {"value": "3d32f887630b6ca12a67ff5313a484989a75b71221d068f360f42b5866df279b206cfae95f36fdb0ea177b7722779d730c113ed8a08b77de5eff4e0988daf44d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+git://github.com/dotnet/runtime", "revision": "2f740adc1457e8a28c1c072993b66f515977eb51", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "2f740adc1457e8a28c1c072993b66f515977eb51", "path": ""}}, {"id": "NuGet:System.Text:<PERSON>son:7.0.0", "purl": "pkg:nuget/System.Text.Json@7.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides high-performance and low-allocating types that serialize objects to JavaScript Object Notation (JSON) text and deserialize JSON text to objects, with UTF-8 support built-in. Also provides types to read and write JSON text encoded as UTF-8, and to create an in-memory document object model (DOM), that is read-only, for random access of the JSON elements within a structured view of the data.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.json/7.0.0/system.text.json.7.0.0.nupkg", "hash": {"value": "2628eda57e6b44c8e2401624cf4c0f9265dabedec04eb408187e0aadf4278c038e8c7c6fc1082799f7a20e205e69c7788b44de323e82565f19436e7ffb5ab41f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "d099f075e45d2aa6007a22b71b45a08758559f80", "path": ""}}, {"id": "NuGet:System.Text:<PERSON>son:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/System.Text.Json@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides high-performance and low-allocating types that serialize objects to JavaScript Object Notation (JSON) text and deserialize JSON text to objects, with UTF-8 support built-in. Also provides types to read and write JSON text encoded as UTF-8, and to create an in-memory document object model (DOM), that is read-only, for random access of the JSON elements within a structured view of the data.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.json/8.0.0-preview.6.23329.7/system.text.json.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "e6641e7e53780704a71d40eeea24a49b1688a901f26e5a166876919cd15b42c15bc834ab14daf85901455e1de78a396743c5f3ea24895cac0e6bda8ff8209de2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:System.Threading:Tasks:4.3.0", "purl": "pkg:nuget/System.Threading.Tasks@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides types that simplify the work of writing concurrent and asynchronous code.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg", "hash": {"value": "7d488ff82cb20a3b3cef6380f2dae5ea9f7baa66bf75ad711aade1e3301b25993ccf2694e33c847ea5b9bdb90ff34c46fcd8a6ba7d6f95605ba0c124ed7c5d13", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Threading:Tasks.Extensions:4.5.4", "purl": "pkg:nuget/System.Threading.Tasks.Extensions@4.5.4", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides additional types that simplify the work of writing concurrent and asynchronous code.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.threading.tasks.extensions/4.5.4/system.threading.tasks.extensions.4.5.4.nupkg", "hash": {"value": "68052086e77d3c7198737a3da163d67740b7c44f93250c39659b3bf21b6547a9abf64cbf40481f5c78f24361af3aaf47d52d188b371554a0928a7f7665c1fc14", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Windows:Extensions:6.0.0", "purl": "pkg:nuget/System.Windows.Extensions@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides miscellaneous Windows-specific types", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.windows.extensions/6.0.0/system.windows.extensions.6.0.0.nupkg", "hash": {"value": "f51eec8166f97b5fcea24816ec737c24d5c5a5cb145ef2d33277c9a16044f40bc3fb97b4cfe7f9a23af704ede91586c6abd2acf00b277538bb304d77a1ca54f0", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Zitadel:gRPC:5.2.25", "purl": "pkg:nuget/Zitadel.gRPC@5.2.25", "authors": ["<PERSON>, smartive AG"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "This package contains the compiled *.proto definitions of the zitadel repository.", "homepage_url": "https://github.com/smartive/zitadel-net", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/zitadel.grpc/5.2.25/zitadel.grpc.5.2.25.nupkg", "hash": {"value": "d1d4b8b58fceb25d25e010c854b972478032a49735c4785f9f2280f0bfa8596278162a6b47ff0a3eb381153f60ba9d9b1d4a91a22003f2f79d759f56b18feb6b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/smartive/zitadel-net.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/smartive/zitadel-net.git", "revision": "", "path": ""}}, {"id": "NuGet:runtime.debian:8-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "8f071552ee042f0cba39b1ba0a1205cf73de447d662995bae68f857a5946f7d154c029a79e37469081675687873c8bf2b9efe57f5cbd660c366b1ca51823f7f2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.fedora:23-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "a135ca0f4f5a49319b5a52b7f4338f8a5fc4387edf26f29e6cbf63a3c3a37b2b5c51c9caf562ec41e470fba281060362465bc56915be782d6c75778aa6195e46", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.fedora:24-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "2f24e2cba88a96bb23848e1404878e4478a65642387b7b76aa4007587fe7c4d8208cbde53d3ed65f8d0d71cd688bfc16be66dc5f7bcf84c7b2ccf1b3c505b0b4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.native:System.Security.Cryptography.Apple:4.3.1", "purl": "pkg:nuget/runtime.native.System.Security.Cryptography.Apple@4.3.1", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.native.system.security.cryptography.apple/4.3.1/runtime.native.system.security.cryptography.apple.4.3.1.nupkg", "hash": {"value": "0be0195264011605d1fcdc45f34ab44b2ed2818896f82dc2708b80ef27747f515758dd7ac612114a65cdf76214486c7f79b18ddd799a7ae55466977e36e63f8d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.native:System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.native.system.security.cryptography.openssl/4.3.2/runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "a34ad2dbe67efcae97fcbea57af386b30660a98ab8229a56c0dca241316e673cf7a26e19c6efb6b7117cc271fdf208741ba6f8447ae254c91acba3ddb7d2923a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.opensuse:13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "ce0873e07000df98e640bd265e969d4a9236535cee2699c5363f3ab297557b0d756260557995f2ee163cff05fc2ba922d66ba0e4cb28521f841e7d546ab3b63e", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.opensuse:42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "bf754c1a66cd70dc1bd38d54fe675e9dd470417ebba62e2f79e278be8f06cc3496ff58ed90d30b5dd4d3efea9accbd09eb17cd87be882951c0fdfb833c371f70", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.osx:10.10-x64.runtime.native.System.Security.Cryptography.Apple:4.3.1", "purl": "pkg:nuget/runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple@4.3.1", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.1/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.1.nupkg", "hash": {"value": "61578fe9771c6c97a7d64dec8df9ed8ea592faf01c56ddfb556dde06e66f8c7ed9953990cf7b7a9fc80b361df8d8878348133ed3a48f62d06a3f5ae80252290c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.osx:10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "0a38f25e8773b58155b5d3f94f849b93353d0809da56228b8ebab5c976e6458ca50eb5a38acca4c8940678e6e9521fb57ae487337f7cbf2ea7893ae9e3f43935", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.rhel:7-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "2ae9db4b719b31fa7e40c60f52c70038fc8668e029cf4e1d120fde8c295631d6b08207d7018a22937b79546016c560c894e27dd6ebc01d5e0f677567e6b2c4f2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.ubuntu:14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "cd4b7ba744de80086521ab67cad2db3085d488388d3d9cb83d9946389f0f4c784539bf3a4ffb8d4f3347c5c7813aadef95b355fd2563e30c948a883c27b95287", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.ubuntu:16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "d7fc28a9f600e471edce0989c01c485d4e2a7e99551f531413afa75039a4004d4e2c27e88976d65432635a321d86316a3c6cdaebc7b2fefa42141b64f4f10d66", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.ubuntu:16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "5fe0e6a878eff59cfe24a8d57af51140576d8e0fec4988e4892c233c47b3a3eed27dec072a6c0d55dd615777cd9ce3fe545c5353b4a95289376ad0b9408ed4be", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}], "dependency_graphs": {"NPM": {"packages": ["NPM::date-fns:2.30.0", "NPM::lit-element:3.3.3", "NPM::lit-html:2.8.0", "NPM::lit:2.8.0", "NPM::regenerator-runtime:0.14.0", "NPM:@babel:runtime:7.22.10", "NPM:@fortawesome:fontawesome-common-types:6.4.2", "NPM:@fortawesome:fontawesome-free:6.4.2", "NPM:@fortawesome:fontawesome-svg-core:6.4.2", "NPM:@fortawesome:free-solid-svg-icons:6.4.2", "NPM:@lit-labs:ssr-dom-shim:1.1.1", "NPM:@lit:reactive-element:1.6.3", "NPM:@open-wc:lit-helpers:0.6.0", "NPM:@types:trusted-types:2.0.3"], "scopes": {":vite-components:0.0.0:dependencies": [{"root": 0}, {"root": 3}, {"root": 7}, {"root": 8}, {"root": 9}, {"root": 12}]}, "nodes": [{"pkg": 4}, {"pkg": 5}, {}, {"pkg": 10}, {"pkg": 11}, {"pkg": 13}, {"pkg": 2}, {"pkg": 1}, {"pkg": 3}, {"pkg": 7}, {"pkg": 6}, {"pkg": 8}, {"pkg": 9}, {"pkg": 12}], "edges": [{"from": 1, "to": 0}, {"from": 2, "to": 1}, {"from": 4, "to": 3}, {"from": 6, "to": 5}, {"from": 7, "to": 4}, {"from": 7, "to": 3}, {"from": 7, "to": 6}, {"from": 8, "to": 4}, {"from": 8, "to": 7}, {"from": 8, "to": 6}, {"from": 11, "to": 10}, {"from": 12, "to": 10}]}, "NuGet": {"packages": ["NuGet::Dapper:2.0.123", "NuGet::FluentMigrator:3.3.2", "NuGet::Grpc:2.46.6", "NuGet::IdentityModel:6.0.0", "NuGet::Npgsql:8.0.0-preview.4", "NuGet::Serilog:2.12.0", "NuGet::Serilog:3.0.1", "NuGet::Serilog:3.0.2-dev-02044", "NuGet::SharpCompress:0.33.0", "NuGet::SqlKata:3.0.0-beta", "NuGet::Zitadel:5.2.25", "NuGet::jose-jwt:4.1.0", "NuGet:Azure:Core:1.25.0", "NuGet:Azure:Identity:1.7.0", "NuGet:Ben:Demystifier:0.4.1", "NuGet:BouncyCastle:Cryptography:2.2.1", "NuGet:Elastic.CommonSchema:Serilog:8.6.1", "NuGet:Elastic:CommonSchema:8.6.1", "NuGet:FluentMigrator.Extensions:Oracle:3.3.2", "NuGet:FluentMigrator.Extensions:Postgres:3.3.2", "NuGet:FluentMigrator.Extensions:SqlAnywhere:3.3.2", "NuGet:FluentMigrator.Extensions:SqlServer:3.3.2", "NuGet:FluentMigrator.Runner:Core:3.3.2", "NuGet:FluentMigrator.Runner:Db2:3.3.2", "NuGet:FluentMigrator.Runner:Firebird:3.3.2", "NuGet:FluentMigrator.Runner:<PERSON><PERSON>:3.3.2", "NuGet:FluentMigrator.Runner:MySql:3.3.2", "NuGet:FluentMigrator.Runner:Oracle:3.3.2", "NuGet:FluentMigrator.Runner:<PERSON><PERSON><PERSON>:3.3.2", "NuGet:FluentMigrator.Runner:<PERSON>hi<PERSON>:3.3.2", "NuGet:FluentMigrator.Runner:SQLite:3.3.2", "NuGet:FluentMigrator.Runner:SqlAnywhere:3.3.2", "NuGet:FluentMigrator.Runner:SqlServer:3.3.2", "NuGet:FluentMigrator.Runner:SqlServerCe:3.3.2", "NuGet:FluentMigrator:Abstractions:3.3.2", "NuGet:FluentMigrator:Runner:3.3.2", "NuGet:Google.Api:CommonProtos:2.10.0", "NuGet:Google:Protobuf:3.24.3", "NuGet:Grpc.Core:Api:2.57.0", "NuGet:Grpc.Net:Client:2.57.0", "NuGet:Grpc.Net:ClientFactory:2.57.0", "NuGet:Grpc.Net:Common:2.57.0", "NuGet:Grpc:Core:2.46.6", "NuGet:Humanizer:Core:2.8.26", "NuGet:IdentityModel.AspNetCore:OAuth2Introspection:6.2.0", "NuGet:JetBrains:Annotations:2023.2.0", "NuGet:Microsoft.AspNetCore:Authentication.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:Authentication.Core:2.2.0", "NuGet:Microsoft.AspNetCore:Authentication.JwtBearer:7.0.11", "NuGet:Microsoft.AspNetCore:Authentication.OpenIdConnect:7.0.11", "NuGet:Microsoft.AspNetCore:Authentication:2.2.0", "NuGet:Microsoft.AspNetCore:Cryptography.Internal:2.2.0", "NuGet:Microsoft.AspNetCore:DataProtection.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:DataProtection:2.2.0", "NuGet:Microsoft.AspNetCore:Hosting.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:Hosting.Server.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:Http.Abstractions:2.1.0", "NuGet:Microsoft.AspNetCore:Http.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:Http.Extensions:2.1.0", "NuGet:Microsoft.AspNetCore:Http.Extensions:2.2.0", "NuGet:Microsoft.AspNetCore:Http.Features:2.1.0", "NuGet:Microsoft.AspNetCore:Http.Features:2.2.0", "NuGet:Microsoft.AspNetCore:Http:2.2.0", "NuGet:Microsoft.AspNetCore:SpaProxy:8.0.0-preview.7.23375.9", "NuGet:Microsoft.AspNetCore:WebUtilities:2.2.0", "NuGet:Microsoft.Bcl:AsyncInterfaces:1.1.1", "NuGet:Microsoft.Data:SqlClient.SNI.runtime:5.1.0", "NuGet:Microsoft.Data:SqlClient:5.1.1", "NuGet:Microsoft.EntityFrameworkCore:Abstractions:8.0.0-preview.6.23329.4", "NuGet:Microsoft.EntityFrameworkCore:Abstractions:8.0.0-preview.7.23375.4", "NuGet:Microsoft.EntityFrameworkCore:Analyzers:8.0.0-preview.6.23329.4", "NuGet:Microsoft.EntityFrameworkCore:Analyzers:8.0.0-preview.7.23375.4", "NuGet:Microsoft.EntityFrameworkCore:Relational:8.0.0-preview.6.23329.4", "NuGet:Microsoft.EntityFrameworkCore:Relational:8.0.0-preview.7.23375.4", "NuGet:Microsoft.EntityFrameworkCore:SqlServer:8.0.0-preview.6.23329.4", "NuGet:Microsoft.EntityFrameworkCore:SqlServer:8.0.0-preview.7.23375.4", "NuGet:Microsoft.Extensions:Caching.Abstractions:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Caching.Abstractions:8.0.0-preview.7.23375.6", "NuGet:Microsoft.Extensions:Caching.Memory:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Caching.Memory:8.0.0-preview.7.23375.6", "NuGet:Microsoft.Extensions:Configuration.Abstractions:2.2.0", "NuGet:Microsoft.Extensions:Configuration.Abstractions:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Configuration.Abstractions:8.0.0-preview.7.23375.6", "NuGet:Microsoft.Extensions:Configuration.Binder:7.0.0", "NuGet:Microsoft.Extensions:Configuration.FileExtensions:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Configuration.Json:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Configuration:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:6.0.0", "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:8.0.0-preview.7.23375.6", "NuGet:Microsoft.Extensions:DependencyInjection:6.0.0", "NuGet:Microsoft.Extensions:DependencyInjection:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:DependencyInjection:8.0.0-preview.7.23375.6", "NuGet:Microsoft.Extensions:DependencyModel:7.0.0", "NuGet:Microsoft.Extensions:FileProviders.Abstractions:2.2.0", "NuGet:Microsoft.Extensions:FileProviders.Abstractions:7.0.0", "NuGet:Microsoft.Extensions:FileProviders.Abstractions:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:FileProviders.Physical:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:FileSystemGlobbing:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Hosting.Abstractions:2.2.0", "NuGet:Microsoft.Extensions:Hosting.Abstractions:7.0.0", "NuGet:Microsoft.Extensions:Http:6.0.0", "NuGet:Microsoft.Extensions:Logging.Abstractions:6.0.0", "NuGet:Microsoft.Extensions:Logging.Abstractions:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Logging.Abstractions:8.0.0-preview.7.23375.6", "NuGet:Microsoft.Extensions:Logging:6.0.0", "NuGet:Microsoft.Extensions:Logging:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Logging:8.0.0-preview.7.23375.6", "NuGet:Microsoft.Extensions:ObjectPool:2.2.0", "NuGet:Microsoft.Extensions:Options:6.0.0", "NuGet:Microsoft.Extensions:Options:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Options:8.0.0-preview.7.23375.6", "NuGet:Microsoft.Extensions:Primitives:6.0.0", "NuGet:Microsoft.Extensions:Primitives:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Primitives:8.0.0-preview.7.23375.6", "NuGet:Microsoft.Extensions:WebEncoders:2.2.0", "NuGet:Microsoft.Identity:Client.Extensions.Msal:2.19.3", "NuGet:Microsoft.Identity:Client:4.47.2", "NuGet:Microsoft.IdentityModel:Abstractions:6.24.0", "NuGet:Microsoft.IdentityModel:JsonWebTokens:6.15.1", "NuGet:Microsoft.IdentityModel:JsonWebTokens:6.24.0", "NuGet:Microsoft.IdentityModel:Logging:6.15.1", "NuGet:Microsoft.IdentityModel:Logging:6.24.0", "NuGet:Microsoft.IdentityModel:Protocols.OpenIdConnect:6.15.1", "NuGet:Microsoft.IdentityModel:Protocols.OpenIdConnect:6.24.0", "NuGet:Microsoft.IdentityModel:Protocols:6.15.1", "NuGet:Microsoft.IdentityModel:Protocols:6.24.0", "NuGet:Microsoft.IdentityModel:Tokens:6.15.1", "NuGet:Microsoft.IdentityModel:Tokens:6.24.0", "NuGet:Microsoft.NETCore:Platforms:1.1.0", "NuGet:Microsoft.NETCore:Platforms:2.0.0", "NuGet:Microsoft.NETCore:Targets:1.1.0", "NuGet:Microsoft.Net:Http.Headers:2.1.0", "NuGet:Microsoft.Net:Http.Headers:2.2.0", "NuGet:Microsoft.SqlServer:Server:1.0.0", "NuGet:Microsoft.Win32:Registry:4.5.0", "NuGet:Microsoft.Win32:SystemEvents:6.0.0", "NuGet:Microsoft:CSharp:4.5.0", "NuGet:Microsoft:EntityFrameworkCore:8.0.0-preview.6.23329.4", "NuGet:Microsoft:EntityFrameworkCore:8.0.0-preview.7.23375.4", "NuGet:Microsoft:OpenApi:1.2.3", "NuGet:Newtonsoft:<PERSON>son:13.0.3", "NuGet:Npgsql.EntityFrameworkCore:PostgreSQL:8.0.0-preview.7", "NuGet:Serilog.Enrichers:Environment:2.3.0-dev-00793", "NuGet:Serilog.Extensions:Hosting:7.0.0", "NuGet:Serilog.Extensions:Logging:7.0.0", "NuGet:Serilog.Formatting:Compact:1.1.0", "NuGet:Serilog.Settings:Configuration:7.0.0", "NuGet:Serilog.Sinks:Console:4.0.1", "NuGet:Serilog.Sinks:Console:4.1.0", "NuGet:Serilog.Sinks:Debug:2.0.0", "NuGet:Serilog.Sinks:File:5.0.0", "NuGet:Serilog:AspNetCore:7.0.0", "NuGet:SqlKata:Execution:3.0.0-beta", "NuGet:Swashbuckle.AspNetCore:Swagger:6.5.0", "NuGet:Swashbuckle.AspNetCore:SwaggerGen:6.5.0", "NuGet:Swashbuckle.AspNetCore:SwaggerUI:6.5.0", "NuGet:System.Collections:Concurrent:4.3.0", "NuGet:System.ComponentModel:Annotations:4.4.1", "NuGet:System.Configuration:ConfigurationManager:6.0.1", "NuGet:System.Diagnostics:Debug:4.3.0", "NuGet:System.Diagnostics:DiagnosticSource:6.0.0", "NuGet:System.Diagnostics:Tracing:4.3.0", "NuGet:System.Drawing:Common:6.0.0", "NuGet:System.Formats:Asn1:5.0.0", "NuGet:System.IdentityModel:Tokens.Jwt:6.15.1", "NuGet:System.IdentityModel:Tokens.Jwt:6.24.0", "NuGet:System.Memory:Data:1.0.2", "NuGet:System.Numerics:Vectors:4.5.0", "NuGet:System.Reflection:Metadata:5.0.0", "NuGet:System.Reflection:Primitives:4.3.0", "NuGet:System.Resources:ResourceManager:4.3.0", "NuGet:System.Runtime:Caching:6.0.0", "NuGet:System.Runtime:CompilerServices.Unsafe:6.0.0", "NuGet:System.Runtime:Extensions:4.3.0", "NuGet:System.Runtime:Handles:4.3.0", "NuGet:System.Runtime:InteropServices:4.3.0", "NuGet:System.Runtime:Numerics:4.3.0", "NuGet:System.Security:AccessControl:4.5.0", "NuGet:System.Security:AccessControl:6.0.0", "NuGet:System.Security:Cryptography.Algorithms:4.3.1", "NuGet:System.Security:Cryptography.Cng:4.7.0", "NuGet:System.Security:Cryptography.Cng:5.0.0", "NuGet:System.Security:Cryptography.Encoding:4.3.0", "NuGet:System.Security:Cryptography.Pkcs:4.5.0", "NuGet:System.Security:Cryptography.Primitives:4.3.0", "NuGet:System.Security:Cryptography.ProtectedData:6.0.0", "NuGet:System.Security:Cryptography.Xml:4.5.0", "NuGet:System.Security:Permissions:4.5.0", "NuGet:System.Security:Permissions:6.0.0", "NuGet:System.Security:Principal.Windows:4.5.0", "NuGet:System.Security:Principal.Windows:5.0.0", "NuGet:System.Text:Encoding.CodePages:6.0.0", "NuGet:System.Text:Encoding:4.3.0", "NuGet:System.Text:Encodings.Web:4.5.0", "NuGet:System.Text:Encodings.Web:6.0.0", "NuGet:System.Text:Encodings.Web:7.0.0", "NuGet:System.Text:Encodings.Web:8.0.0-preview.6.23329.7", "NuGet:System.Text:<PERSON>son:4.7.2", "NuGet:System.Text:<PERSON>son:5.0.2", "NuGet:System.Text:<PERSON>son:7.0.0", "NuGet:System.Text:<PERSON>son:8.0.0-preview.6.23329.7", "NuGet:System.Threading:Tasks.Extensions:4.5.4", "NuGet:System.Threading:Tasks:4.3.0", "NuGet:System.Windows:Extensions:6.0.0", "NuGet:System:Buffers:4.5.0", "NuGet:System:Collections:4.3.0", "NuGet:System:Globalization:4.3.0", "NuGet:System:IO:4.3.0", "NuGet:System:Linq:4.3.0", "NuGet:System:Memory:4.5.3", "NuGet:System:Memory:4.5.4", "NuGet:System:Reflection:4.3.0", "NuGet:System:Runtime:4.3.0", "NuGet:System:Threading:4.3.0", "NuGet:System:ValueTuple:4.4.0", "NuGet:Zitadel:gRPC:5.2.25", "NuGet:runtime.debian:8-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.fedora:23-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.fedora:24-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.native:System.Security.Cryptography.Apple:4.3.1", "NuGet:runtime.native:System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.opensuse:13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.opensuse:42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.osx:10.10-x64.runtime.native.System.Security.Cryptography.Apple:4.3.1", "NuGet:runtime.osx:10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.rhel:7-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.ubuntu:14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.ubuntu:16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.ubuntu:16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2"], "scopes": {":FileSystemFile/FileSystemFile.csproj::net8.0": [{"root": 6}, {"root": 149}], ":GoogleGeoData/GoogleGeoData.csproj::net8.0": [{"root": 141}], ":OsmGeoData/OsmGeoData.csproj::net8.0": [{"root": 141}], ":Storage/Storage.csproj::net8.0": [{"root": 0}, {"root": 1}, {"root": 6}, {"root": 8}, {"root": 9}, {"root": 12}, {"root": 13}, {"root": 18}, {"root": 19}, {"root": 20}, {"root": 21}, {"root": 22}, {"root": 23}, {"root": 24}, {"root": 25}, {"root": 26}, {"root": 27}, {"root": 28}, {"root": 29}, {"root": 30}, {"root": 31}, {"root": 32}, {"root": 33}, {"root": 34}, {"root": 35}, {"root": 43}, {"root": 65}, {"root": 66}, {"root": 67}, {"root": 68}, {"root": 70}, {"root": 72}, {"root": 74}, {"root": 76}, {"root": 78}, {"root": 81}, {"root": 88}, {"root": 91}, {"root": 103}, {"root": 106}, {"root": 110}, {"root": 113}, {"root": 116}, {"root": 117}, {"root": 118}, {"root": 120}, {"root": 122}, {"root": 124}, {"root": 126}, {"root": 128}, {"root": 129}, {"root": 131}, {"root": 134}, {"root": 136}, {"root": 137}, {"root": 138}, {"root": 141}, {"root": 149}, {"root": 153}, {"root": 157}, {"root": 158}, {"root": 159}, {"root": 160}, {"root": 161}, {"root": 162}, {"root": 163}, {"root": 164}, {"root": 166}, {"root": 167}, {"root": 168}, {"root": 170}, {"root": 171}, {"root": 172}, {"root": 173}, {"root": 174}, {"root": 179}, {"root": 182}, {"root": 186}, {"root": 189}, {"root": 191}, {"root": 192}, {"root": 193}, {"root": 195}, {"root": 198}, {"root": 202}, {"root": 203}, {"root": 204}, {"root": 206}, {"root": 207}, {"root": 208}, {"root": 211}, {"root": 212}, {"root": 213}, {"root": 214}, {"root": 215}], ":WebApp/WebApp.csproj::net8.0": [{"root": 4}, {"root": 5}, {"root": 12}, {"root": 13}, {"root": 14}, {"root": 16}, {"root": 17}, {"root": 45}, {"root": 56}, {"root": 58}, {"root": 60}, {"root": 65}, {"root": 66}, {"root": 67}, {"root": 69}, {"root": 71}, {"root": 73}, {"root": 75}, {"root": 77}, {"root": 79}, {"root": 82}, {"root": 83}, {"root": 89}, {"root": 92}, {"root": 93}, {"root": 95}, {"root": 100}, {"root": 104}, {"root": 107}, {"root": 111}, {"root": 114}, {"root": 116}, {"root": 117}, {"root": 118}, {"root": 120}, {"root": 122}, {"root": 124}, {"root": 126}, {"root": 128}, {"root": 129}, {"root": 131}, {"root": 132}, {"root": 134}, {"root": 136}, {"root": 137}, {"root": 139}, {"root": 140}, {"root": 142}, {"root": 143}, {"root": 144}, {"root": 145}, {"root": 146}, {"root": 147}, {"root": 148}, {"root": 150}, {"root": 151}, {"root": 152}, {"root": 154}, {"root": 155}, {"root": 156}, {"root": 159}, {"root": 161}, {"root": 163}, {"root": 164}, {"root": 166}, {"root": 167}, {"root": 168}, {"root": 169}, {"root": 172}, {"root": 173}, {"root": 179}, {"root": 182}, {"root": 186}, {"root": 189}, {"root": 191}, {"root": 192}, {"root": 193}, {"root": 196}, {"root": 200}, {"root": 202}, {"root": 204}, {"root": 205}, {"root": 211}, {"root": 213}], ":WebAppComponents/WebAppComponents.csproj::net8.0": [{"root": 63}], ":WebAppMigrations/WebAppMigrations.csproj::net8.0": [{"root": 4}, {"root": 7}, {"root": 12}, {"root": 13}, {"root": 65}, {"root": 66}, {"root": 67}, {"root": 69}, {"root": 71}, {"root": 73}, {"root": 75}, {"root": 77}, {"root": 79}, {"root": 82}, {"root": 84}, {"root": 85}, {"root": 86}, {"root": 89}, {"root": 92}, {"root": 96}, {"root": 97}, {"root": 98}, {"root": 104}, {"root": 107}, {"root": 111}, {"root": 114}, {"root": 116}, {"root": 117}, {"root": 118}, {"root": 120}, {"root": 122}, {"root": 124}, {"root": 126}, {"root": 128}, {"root": 129}, {"root": 131}, {"root": 134}, {"root": 136}, {"root": 137}, {"root": 139}, {"root": 142}, {"root": 159}, {"root": 161}, {"root": 163}, {"root": 164}, {"root": 166}, {"root": 167}, {"root": 168}, {"root": 172}, {"root": 173}, {"root": 179}, {"root": 182}, {"root": 186}, {"root": 189}, {"root": 191}, {"root": 192}, {"root": 193}, {"root": 197}, {"root": 201}, {"root": 202}, {"root": 204}, {"root": 211}, {"root": 213}], ":ZitadelApi/ZitadelApi.csproj::net8.0": [{"root": 2}, {"root": 3}, {"root": 10}, {"root": 11}, {"root": 15}, {"root": 36}, {"root": 37}, {"root": 38}, {"root": 39}, {"root": 40}, {"root": 41}, {"root": 42}, {"root": 44}, {"root": 46}, {"root": 47}, {"root": 48}, {"root": 49}, {"root": 50}, {"root": 51}, {"root": 52}, {"root": 53}, {"root": 54}, {"root": 55}, {"root": 57}, {"root": 59}, {"root": 61}, {"root": 62}, {"root": 64}, {"root": 80}, {"root": 87}, {"root": 90}, {"root": 94}, {"root": 99}, {"root": 101}, {"root": 102}, {"root": 105}, {"root": 108}, {"root": 109}, {"root": 112}, {"root": 115}, {"root": 119}, {"root": 121}, {"root": 123}, {"root": 125}, {"root": 127}, {"root": 130}, {"root": 131}, {"root": 133}, {"root": 135}, {"root": 137}, {"root": 157}, {"root": 160}, {"root": 161}, {"root": 162}, {"root": 165}, {"root": 170}, {"root": 171}, {"root": 173}, {"root": 174}, {"root": 175}, {"root": 176}, {"root": 177}, {"root": 178}, {"root": 180}, {"root": 181}, {"root": 183}, {"root": 184}, {"root": 185}, {"root": 187}, {"root": 188}, {"root": 190}, {"root": 193}, {"root": 194}, {"root": 199}, {"root": 203}, {"root": 205}, {"root": 206}, {"root": 207}, {"root": 208}, {"root": 209}, {"root": 210}, {"root": 212}, {"root": 213}, {"root": 214}, {"root": 216}, {"root": 217}, {"root": 218}, {"root": 219}, {"root": 220}, {"root": 221}, {"root": 222}, {"root": 223}, {"root": 224}, {"root": 225}, {"root": 226}, {"root": 227}, {"root": 228}, {"root": 229}]}, "nodes": [{"pkg": 141}, {}, {"pkg": 1}, {"pkg": 6}, {"pkg": 8}, {"pkg": 9}, {"pkg": 12}, {"pkg": 13}, {"pkg": 18}, {"pkg": 19}, {"pkg": 20}, {"pkg": 21}, {"pkg": 22}, {"pkg": 23}, {"pkg": 24}, {"pkg": 25}, {"pkg": 26}, {"pkg": 27}, {"pkg": 28}, {"pkg": 29}, {"pkg": 30}, {"pkg": 31}, {"pkg": 32}, {"pkg": 33}, {"pkg": 34}, {"pkg": 35}, {"pkg": 43}, {"pkg": 65}, {"pkg": 66}, {"pkg": 67}, {"pkg": 68}, {"pkg": 70}, {"pkg": 72}, {"pkg": 74}, {"pkg": 76}, {"pkg": 78}, {"pkg": 81}, {"pkg": 88}, {"pkg": 91}, {"pkg": 103}, {"pkg": 106}, {"pkg": 110}, {"pkg": 113}, {"pkg": 116}, {"pkg": 117}, {"pkg": 118}, {"pkg": 120}, {"pkg": 122}, {"pkg": 124}, {"pkg": 126}, {"pkg": 128}, {"pkg": 129}, {"pkg": 131}, {"pkg": 134}, {"pkg": 136}, {"pkg": 137}, {"pkg": 138}, {"pkg": 149}, {"pkg": 153}, {"pkg": 157}, {"pkg": 158}, {"pkg": 159}, {"pkg": 160}, {"pkg": 161}, {"pkg": 162}, {"pkg": 163}, {"pkg": 164}, {"pkg": 166}, {"pkg": 167}, {"pkg": 168}, {"pkg": 170}, {"pkg": 171}, {"pkg": 172}, {"pkg": 173}, {"pkg": 174}, {"pkg": 179}, {"pkg": 182}, {"pkg": 186}, {"pkg": 189}, {"pkg": 191}, {"pkg": 192}, {"pkg": 193}, {"pkg": 195}, {"pkg": 198}, {"pkg": 202}, {"pkg": 203}, {"pkg": 204}, {"pkg": 206}, {"pkg": 207}, {"pkg": 208}, {"pkg": 211}, {"pkg": 212}, {"pkg": 213}, {"pkg": 214}, {"pkg": 215}, {"pkg": 2}, {"pkg": 3}, {"pkg": 10}, {"pkg": 11}, {"pkg": 15}, {"pkg": 36}, {"pkg": 37}, {"pkg": 38}, {"pkg": 39}, {"pkg": 40}, {"pkg": 41}, {"pkg": 42}, {"pkg": 44}, {"pkg": 46}, {"pkg": 47}, {"pkg": 48}, {"pkg": 49}, {"pkg": 50}, {"pkg": 51}, {"pkg": 52}, {"pkg": 53}, {"pkg": 54}, {"pkg": 55}, {"pkg": 57}, {"pkg": 59}, {"pkg": 61}, {"pkg": 62}, {"pkg": 64}, {"pkg": 80}, {"pkg": 87}, {"pkg": 90}, {"pkg": 94}, {"pkg": 99}, {"pkg": 101}, {"pkg": 102}, {"pkg": 105}, {"pkg": 108}, {"pkg": 109}, {"pkg": 112}, {"pkg": 115}, {"pkg": 119}, {"pkg": 121}, {"pkg": 123}, {"pkg": 125}, {"pkg": 127}, {"pkg": 130}, {"pkg": 133}, {"pkg": 135}, {"pkg": 165}, {"pkg": 175}, {"pkg": 176}, {"pkg": 177}, {"pkg": 178}, {"pkg": 180}, {"pkg": 181}, {"pkg": 183}, {"pkg": 184}, {"pkg": 185}, {"pkg": 187}, {"pkg": 188}, {"pkg": 190}, {"pkg": 194}, {"pkg": 199}, {"pkg": 205}, {"pkg": 209}, {"pkg": 210}, {"pkg": 216}, {"pkg": 217}, {"pkg": 218}, {"pkg": 219}, {"pkg": 220}, {"pkg": 221}, {"pkg": 222}, {"pkg": 223}, {"pkg": 224}, {"pkg": 225}, {"pkg": 226}, {"pkg": 227}, {"pkg": 228}, {"pkg": 229}, {"pkg": 4}, {"pkg": 7}, {"pkg": 69}, {"pkg": 71}, {"pkg": 73}, {"pkg": 75}, {"pkg": 77}, {"pkg": 79}, {"pkg": 82}, {"pkg": 84}, {"pkg": 85}, {"pkg": 86}, {"pkg": 89}, {"pkg": 92}, {"pkg": 96}, {"pkg": 97}, {"pkg": 98}, {"pkg": 104}, {"pkg": 107}, {"pkg": 111}, {"pkg": 114}, {"pkg": 139}, {"pkg": 142}, {"pkg": 197}, {"pkg": 201}, {"pkg": 5}, {"pkg": 14}, {"pkg": 16}, {"pkg": 17}, {"pkg": 45}, {"pkg": 56}, {"pkg": 58}, {"pkg": 60}, {"pkg": 83}, {"pkg": 93}, {"pkg": 95}, {"pkg": 100}, {"pkg": 132}, {"pkg": 140}, {"pkg": 143}, {"pkg": 144}, {"pkg": 145}, {"pkg": 146}, {"pkg": 147}, {"pkg": 148}, {"pkg": 150}, {"pkg": 151}, {"pkg": 152}, {"pkg": 154}, {"pkg": 155}, {"pkg": 156}, {"pkg": 169}, {"pkg": 196}, {"pkg": 200}, {"pkg": 63}], "edges": []}, "Unmanaged": {"nodes": [], "edges": []}}}}, "scanner": null, "advisor": null, "evaluator": null, "resolved_configuration": {"package_curations": [{"provider": {"id": "DefaultDir"}, "curations": []}, {"provider": {"id": "DefaultFile"}, "curations": []}]}}