/**
 * Retrieves a display string for a data field type
 * @param data DataFieldDTO
 */
export function getTypeLabel(data: Record<string, any>): string {
	if (data.type == null)
		return ''

	const labelParts = [data.type.toUpperCase()]
	if (data.type.toLowerCase() === 'string')
		labelParts.push(` (${data.length})`)
	if (data.multi)
		labelParts.push('[+]')
	return labelParts.join('')
}

/**
 * Retrieves a html icon for a specific data field 'fieldType'
 * @param data DataFieldDTO
 */
export function getFieldTypeIcon(data: Record<string, any>) {
	let icon = 'pen-field'
	if (data.systemField)
		icon = 'gears'
	if (data.lookupSourceId)
		icon = 'link-simple'

	return `<i class="fa-light fa-${icon}"></i>`
}