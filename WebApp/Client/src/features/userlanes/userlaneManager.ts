/**
 * User lane Manager Module (Class-based Implementation)
 *
 * This module provides comprehensive functionality for managing and running interactive
 * and automated user lanes within the application. It supports three types of tours:
 *
 * 1. Test Tours: Fully automated tours that execute predefined steps and report results
 * 2. Introduction Tours: Automatically perform actions to demonstrate features
 * 3. Guide Tours: Interactive tours that wait for user actions before proceeding
 *
 * Tours are stored in localStorage and executed when the page loads.
 */

import I18n from '../../services/translations';

/**
 * Represents a single step in a tour
 */
export interface TourStep {
  id?: string;
  title: string;
  description?: string;
  element: string;
  onHighlightStarted: TourAction[];
  speed?: number;
}

/**
 * Represents an action to be performed during a tour step
 */
export interface TourAction {
  id?: string;
  actionType: 'Click' | 'SetValue' | 'OpenListItem';
  target: string;
  targetValue?: string;
  speed?: number;
}

/**
 * Represents a complete tour with multiple steps
 */
export interface TestTourItem {
  id: string;
  name: string;
  url: string;
  steps: TourStep[];
  completed?: boolean;
  result?: string;
  tourId?: string;
  status?: boolean;  // Tracks whether this specific tour has been run
}

/**
 * Detailed information about a step's execution
 */
export interface StepDetail {
  tourId: string;
  stepId: string;
  actionId: string | null;
  title: string;
  startTime: Date | null;
  endTime: Date | null;
  duration: number | null;
  isComplete: boolean;
  notFound: boolean;
}

/**
 * Formatted step result for API submission
 */
export interface FormattedStepResult {
  Title: string;
  UserlaneStepActionId: string | null;
  StartTime: string | null;
  EndTime: string | null;
  Duration: number;
  Complete: boolean;
  Found: boolean;
  Result: "Pass" | "Fail";
}

/**
 * Complete results data for a tour execution
 */
export interface ResultsData {
  UserlaneId: string;
  Results: FormattedStepResult[];
  StartTime: string;
  EndTime: string;
  Runtime: number;
  Status: "PASS" | "FAIL";
}

/**
 * Utility class for translating UI text
 */
class UserlaneTranslator {
  /**
   * Translates a key using the I18n service
   * @param key - Translation key
   * @returns Translated text or the key itself if no translation exists
   */
  public static translate(key: string): string {
    return I18n.translate(key, key);
  }
}

/**
 * Utility class for DOM element actions
 */
class UserlaneDomActions {
  /**
   * Waits until the element is visible, enabled, and not covered, or times out
   */
  public static async waitForInteractable(
    element: HTMLElement,
    timeout: number = 10000,
    pollingInterval: number = 100
  ): Promise<void> {
    const start = Date.now();
    return new Promise((resolve, reject) => {
      const check = () => {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0 &&
          window.getComputedStyle(element).visibility !== 'hidden' &&
          window.getComputedStyle(element).display !== 'none';
        const isDisabled = (element as HTMLButtonElement).disabled !== undefined
          ? (element as HTMLButtonElement).disabled
          : element.hasAttribute('disabled');
        let isCovered = false;
        if (isVisible) {
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;
          const topElement = document.elementFromPoint(centerX, centerY);
          isCovered = topElement !== element && !element.contains(topElement);
        }
        if (isVisible && !isDisabled && !isCovered) {
          resolve();
        } else if (Date.now() - start > timeout) {
          reject(new Error('Element did not become interactable in time.'));
        } else {
          setTimeout(check, pollingInterval);
        }
      };
      check();
    });
  }

  public static async clickButton(element: HTMLElement): Promise<void> {

    try {
      await this.waitForInteractable(element);
      // Wait 2 seconds before firing the event
      await new Promise(resolve => setTimeout(resolve, 2000));
      // Try dispatching a real click event with composed: true for shadow DOM
      const eventOptions = { bubbles: true, cancelable: true, composed: true, view: window };
      element.focus();
      element.dispatchEvent(new MouseEvent('pointerdown', eventOptions));
      element.dispatchEvent(new MouseEvent('mousedown', eventOptions));
      element.dispatchEvent(new MouseEvent('mouseup', eventOptions));
      element.dispatchEvent(new MouseEvent('click', eventOptions));
    } catch (err) {
      console.warn('Element is not interactable after waiting:', element, err);
      throw new Error('Element is not interactable');
    }
  }

  /**
   * Sets the value of an input element
   * @param target - The input element
   * @param value - The value to set
   */
  public static setValue(target: HTMLInputElement, value: string): void {
    target.value = value;
  }
}

/**
 * Utility class for finding DOM elements
 */

class UserlaneElementFinder {
	
	/**
   * Waits for an element to appear in the DOM
   * @param selector - Element ID to wait for
   * @param onFail - Callback to execute on failure
   * @returns Promise resolving to the found element
   */
  public static waitForElement(selector: string, onFail: () => void): Promise<HTMLElement> {
    return new Promise((resolve, reject) => {
      const timeout = 10000; // Maximum wait time (10 seconds)
      const pollingInterval = 100; // Check interval (100ms)
      let elapsedTime = 0;
			
      const interval = setInterval(() => {
        const element = UserlaneElementFinder.findElementDeep(document.body, selector);
        if (element) {
          clearInterval(interval);
          resolve(element);
          return;
        }

        elapsedTime += pollingInterval;
        if (elapsedTime >= timeout) {
          clearInterval(interval);
          onFail();
          reject(new Error(`Element not found: ${selector}`));
        }
      }, pollingInterval);
    });
  }


	/**
   * Special handler for finding elements within table components (shadow DOM)
   * @param selector - Row number or identifier
   * @param onFail - Callback to execute on failure
   * @returns Promise resolving to the found table row
   */
  public static waitForTableElement(selector: string, onFail: () => void): Promise<HTMLElement> {
    return new Promise((resolve, reject) => {
      const timeout = 12000; // Maximum wait time (12 seconds)
      const initialDelay = 5000; // Initial delay to allow table to render
      const pollingInterval = 100; // Check interval (100ms)
      let elapsedTime = 0;

      setTimeout(() => {
        const overlay = document.getElementById('overlay');
        const isOpen = overlay?.hasAttribute('open') || false;

        const tableElement = document.getElementById(document.getElementsByTagName("lvl-table")[0].id)
          ?.shadowRoot?.children[1]
          .getElementsByClassName('table')[1];

        if (!tableElement) {
          reject(new Error('Table element not found'));
          return;
        }

        const interval = setInterval(() => {
          if (!isOpen) {
            const tableRowCount = tableElement.childElementCount;
            let selectedRow: number;

            if (isNaN(Number(selector))) {
              selectedRow = tableRowCount;
            } else {
              const selectorNum = Number(selector);
              selectedRow = (selectorNum < 1 || selectorNum > tableRowCount)
                ? tableRowCount
                : selectorNum;
            }

            clearInterval(interval);
            resolve(tableElement.children[selectedRow-1] as HTMLElement);
            return;
          }

          elapsedTime += pollingInterval;
          if (elapsedTime >= timeout - initialDelay) {
            clearInterval(interval);
            onFail();
            reject(new Error(`Element not found: ${selector}`));
          }
        }, pollingInterval);
      }, initialDelay);
    });
  }

  /**
   * Recursively searches for an element by ID in the light DOM and all shadow DOMs
   * @param root - The root node to start searching from
   * @param id - The element ID to search for
   * @returns The found HTMLElement or null
   */
  public static findElementDeep(root: Node, id: string): HTMLElement | null {
    if (root instanceof HTMLElement && root.id === id) {
      return root;
    }
    // Search shadow root if present
    if (root instanceof Element && root.shadowRoot) {
      const found = this.findElementDeep(root.shadowRoot, id);
      if (found) return found;
    }
    // Search children
    for (const child of Array.from(root.childNodes)) {
      const found = this.findElementDeep(child, id);
      if (found) return found;
    }
    return null;
  }

  /**
   * Waits for an element with the given ID to become visible in the DOM.
   * Uses MutationObserver and polling for robustness.
   */
  public static waitForElementVisible(
    id: string,
    timeout: number = 10000,
    pollingInterval: number = 100
  ): Promise<HTMLElement> {
    return new Promise((resolve, reject) => {
      let found: HTMLElement | null = null;
      let elapsed = 0;
      let observer: MutationObserver | null = null;

      function isVisible(el: HTMLElement) {
        const rect = el.getBoundingClientRect();
        const style = window.getComputedStyle(el);
        return (
          rect.width > 0 &&
          rect.height > 0 &&
          style.visibility !== 'hidden' &&
          style.display !== 'none'
        );
      }

      function check() {
        found = UserlaneElementFinder.findElementDeep(document.body, id);
        if (found && isVisible(found)) {
          if (observer) observer.disconnect();
          resolve(found);
          return true;
        }
        return false;
      }

      // Initial check
      if (check()) return;

      // Polling fallback
      const poll = setInterval(() => {
        elapsed += pollingInterval;
        if (check()) {
          clearInterval(poll);
        } else if (elapsed >= timeout) {
          if (observer) observer.disconnect();
          clearInterval(poll);
          reject(new Error('Element did not become visible in time.'));
        }
      }, pollingInterval);

      // MutationObserver for DOM changes
      observer = new MutationObserver(() => {
        if (check()) {
          clearInterval(poll);
        }
      });
      observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class', 'hidden'],
      });
    });
  }

  /**
   * Waits for the window to finish loading
   */
  public static waitForWindowLoad(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', () => resolve(), { once: true });
      }
    });
  }
}

/**
 * Class for storing and retrieving tour data
 */
class UserlaneTourStorage {
  private static readonly TEST_TOUR_KEY = 'lvl-userlane-test';
  private static readonly INTRODUCTION_TOUR_KEY = 'introduction';
  private static readonly GUIDE_TOUR_KEY = 'guide';

  /**
   * Gets test tour data from localStorage
   */
  public static getTestTour(): string | null {
    return localStorage.getItem(this.TEST_TOUR_KEY);
  }

  /**
   * Gets introduction tour data from localStorage
   */
  public static getIntroductionTour(): string | null {
    return localStorage.getItem(this.INTRODUCTION_TOUR_KEY);
  }

  /**
   * Gets guide tour data from localStorage
   */
  public static getGuideTour(): string | null {
    return localStorage.getItem(this.GUIDE_TOUR_KEY);
  }

  /**
   * Updates test tour data in localStorage with status tracking
   */
  public static updateTestTour(data: TestTourItem[]): void {
    // Add status to each tour if not present
    const toursWithStatus = data.map(tour => ({
      ...tour,
      status: tour.status ?? false
    }));
    localStorage.setItem(this.TEST_TOUR_KEY, JSON.stringify(toursWithStatus));
  }

  /**
   * Clears all tour data from localStorage
   */
  public static clearTours(): void {
    localStorage.removeItem(this.TEST_TOUR_KEY);
    localStorage.removeItem(this.INTRODUCTION_TOUR_KEY);
    localStorage.removeItem(this.GUIDE_TOUR_KEY);
  }
}

/**
 * Class for displaying test results
 */
class UserlaneResultsReporter {
  /**
   * Creates and displays a dialog showing test results
   * @param testResults - Array of completed test tours with results
   */
  public static displayResultsInDialog(testResults: TestTourItem[]): void {
    const parser = new DOMParser();

    // Generate HTML for each test result
    const results = testResults.map((testResultTable) => {
      // Skip if no result data is available
      if (!testResultTable.result) return '';

      // Parse the stored result data
      const resultData = JSON.parse(testResultTable.result) as ResultsData;

      // Extract the relevant fields for display
      const tableRows = resultData.Results.map((step) => ({
        title: step.Title,
        duration: step.Duration,
        result: step.Result
      }));

      // Convert to JSON string for the lvl-table component
      const parsedData = JSON.stringify(tableRows).toString();
      return `<h1 style='text-align: center; padding-top: 10px; margin: auto'>Userlane: ${testResultTable.name}</h1>
              <lvl-multi-data-view>
                  <lvl-table rows='${parsedData}'>
                      <lvl-table-data-column name='title' label='${UserlaneTranslator.translate("title")}'></lvl-table-data-column>
                      <lvl-table-data-column name='duration' label='${UserlaneTranslator.translate("duration")}'></lvl-table-data-column>
                      <lvl-table-data-column name='result' label='${UserlaneTranslator.translate("results")}'></lvl-table-data-column>
                  </lvl-table>
              </lvl-multi-data-view>`;
    }).filter(Boolean).join('');

    const dialogHTML = `
        <lvl-dialog name="userlane-test-results" heading='${UserlaneTranslator.translate("testResults")}' width="700" modal="true" open="true">
        <lvl-button id="dialogClose" slot="button-left" data-action="close" type="secondary" color="info" label='${UserlaneTranslator.translate("abort")}'></lvl-button>
					<div style="flex: 1;width: auto;height: auto;max-width: 100%;max-height: 100%;background-color: var(--clr-background-lvl-0);border-radius: 0.4rem;margin: 1.4rem 0 1.4rem 1.4rem;padding: 1rem;">
							<lvl-section style="max-height: 100%; height: max-content; flex: 1;" max-height="450" calc-height ignore-overflow >
									${results}
							</lvl-section>
					 <div/>
        </lvl-dialog>
    `;

    const dialogElement = parser.parseFromString(dialogHTML, 'text/html').body.firstChild as HTMLElement;

    // Create a semi-transparent overlay for the dialog
    const layover = document.createElement("div");
    layover.appendChild(dialogElement);

    const content = document.getElementById('content');
    if (content) {
      content.append(layover);

      // Remove borders from lvl-section elements by injecting styles into their shadow DOM
      const lvlSections = layover.querySelectorAll('lvl-section');
      lvlSections.forEach(section => {
        if (section && section.shadowRoot) {
          const style = document.createElement('style');
          style.textContent = `
            section {
              box-shadow: none !important;
            }
          `;
          section.shadowRoot.appendChild(style);
        }
      });

      const closeButton = document.getElementById('dialogClose');
      if (closeButton) {
        closeButton.addEventListener("click", () => {
          content.removeChild(layover);
        });
      }

      // Also, remove overlay when the dialog is closed by any means
      dialogElement.addEventListener('close', () => {
        if (content.contains(layover)) {
          content.removeChild(layover);
        }
      });

      // Optionally, clicking outside the dialog (on the overlay) closes the dialog
      layover.addEventListener('click', (e) => {
        if (e.target === layover) {
          if (typeof (dialogElement as any).close === 'function') {
            (dialogElement as any).close();
          } else {
            // Fallback: remove overlay if the dialog can't be closed programmatically
            if (content.contains(layover)) {
              content.removeChild(layover);
            }
          }
        }
      });
    }
  }
}

/**
 * Class for running automated test tours
 */
class UserlaneTestTourRunner {
  private currentTest = 0;
  private tourStartTime: Date = new Date(); // Initialize with the current date
  private overallTestResult: "PASS" | "FAIL" = "PASS";

  /**
   * Runs the test tour
   */
  public async run(): Promise<void> {
    await UserlaneElementFinder.waitForWindowLoad();
    this.executeTestTour();
  }

  /**
   * Sends step execution results to the server
   * @param stepDetails - Array of step execution details
   */
  private async postStepResults(stepDetails: StepDetail[]): Promise<void> {
    // Format the step details for the API
    const formattedResults: FormattedStepResult[] = stepDetails.map((step) => ({
      Title: step.title,
      UserlaneStepActionId: step.actionId || null,
      StartTime: step.startTime?.toISOString() || null,
      EndTime: step.endTime?.toISOString() || null,
      Duration: step.duration || 0,
      Complete: step.isComplete,
      Found: !step.notFound,
      Result: step.isComplete && !step.notFound ? "Pass" : "Fail",
    }));

    const testTourData: TestTourItem[] = JSON.parse(UserlaneTourStorage.getTestTour() || '[]');
    const currentTestData = testTourData[this.currentTest];

    const data: ResultsData = {
      UserlaneId: currentTestData.id,
      Results: formattedResults,
      StartTime: this.tourStartTime.toISOString(),
      EndTime: new Date().toISOString(),
      Runtime: new Date().getTime() - this.tourStartTime.getTime(),
      Status: this.overallTestResult,
    };

    try {
      // Send POST request to the server
      const response = await fetch('/Api/Userlane/Results', {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        let testInformation: TestTourItem[] = JSON.parse(UserlaneTourStorage.getTestTour() || '[]');
        testInformation[this.currentTest].completed = true;
        testInformation[this.currentTest].result = JSON.stringify(data);
        testInformation[this.currentTest].status = true;  // Mark this tour as a run
        UserlaneTourStorage.updateTestTour(testInformation);

        this.currentTest++;
        if (this.currentTest === testInformation.length) {
          UserlaneResultsReporter.displayResultsInDialog(testInformation);
          UserlaneTourStorage.clearTours();
        } else {
          window.location.href = testInformation[this.currentTest].url;
        }
      } else {
        console.error("Failed to post step results. Server responded with status:", response.status);
      }
    } catch (error) {
      console.error("An error occurred while posting step results:", error);
    }
  }

  /**
   * Executes a single test step with all its actions
   * @param stepIndex - Index of the step to execute
   * @param steps - Array of all steps in the tour
   * @param stepDetails - Array to track execution details
   */
  private async runStep(stepIndex: number, steps: TourStep[], stepDetails: StepDetail[]): Promise<void> {
    if (stepIndex >= steps.length) {
      await this.postStepResults(stepDetails);
      return;
    }

    const currentStep = steps[stepIndex];
    const actions = currentStep.onHighlightStarted || [];

    // Record step start time
    stepDetails[stepIndex].startTime = new Date();

    if (actions.length > 0) {
      for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {
        const action = actions[actionIndex];
        try {
          let element: HTMLElement | null = null;
          let foundAndAccessible = false;
          for (let attempt = 0; attempt < 10; attempt++) {
            element = null; // Ensure a fresh find each time
            if (action.target === "createRecord") {
              element = document.querySelector('lvl-query-view-action-bar')?.shadowRoot?.querySelector('lvl-button') as HTMLElement;
              if (element) {
                element.click();
                // Mark step as complete and return immediately
                stepDetails[stepIndex].actionId = action.id || `action-${actionIndex}`;
                this.completeStep(stepIndex, steps, stepDetails);
                return;
              }
            } else {
              element = UserlaneElementFinder.findElementDeep(document.body, action.target);
              if (element) {
                // Check accessibility
                const rect = element.getBoundingClientRect();
                const style = window.getComputedStyle(element);
                const isVisible = rect.width > 0 && rect.height > 0 && style.visibility !== 'hidden' && style.display !== 'none';
                const isDisabled = (element as HTMLButtonElement).disabled !== undefined
                  ? (element as HTMLButtonElement).disabled
                  : element.hasAttribute('disabled');
                let isCovered = false;
                if (isVisible) {
                  const centerX = rect.left + rect.width / 2;
                  const centerY = rect.top + rect.height / 2;
                  const topElement = document.elementFromPoint(centerX, centerY);
                  isCovered = topElement !== element && !element.contains(topElement);
                }
                if (isVisible && !isDisabled && !isCovered) {
                  foundAndAccessible = true;
                  break;
                }
              }
            }
            // Wait 1 second before the next attempt
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          if (!foundAndAccessible && action.target !== "createRecord" || !element) {
            this.overallTestResult = "FAIL";
            stepDetails[stepIndex].actionId = action.id || `action-${actionIndex}`;
            this.failStep(stepIndex, steps, stepDetails);
            return;
          }

          // Update action ID for the current action (for reporting)
          stepDetails[stepIndex].actionId = action.id || `action-${actionIndex}`;

          console.log("Performing action: ", action.actionType);
          if(action.actionType === "SetValue") {
            console.log("Setting value: ", action.target);
            const inputEl = document.getElementById(action.target) as HTMLInputElement;
            if (inputEl) {
              inputEl.value = action.targetValue?.toString() || '';
            }
          }

          // Perform the required action based on the action type
          if (action.actionType === "Click") {
            try {
              if (action.target !== "createRecord") {
                await UserlaneDomActions.clickButton(element);
              }
            } catch (clickError) {
              stepDetails[stepIndex].actionId = action.id || `action-${actionIndex}`;
              this.failStep(stepIndex, steps, stepDetails);
              return;
            }
          } else if (action.actionType === "OpenListItem") {
            try {
              await UserlaneDomActions.clickButton(element);
            } catch (clickError) {
              stepDetails[stepIndex].actionId = action.id || `action-${actionIndex}`;
              this.failStep(stepIndex, steps, stepDetails);
              return;
            }
          }

          // Mark the step as complete after all actions are done
          this.completeStep(stepIndex, steps, stepDetails);
        } catch (error) {
          console.error(error);
          stepDetails[stepIndex].actionId = action.id || `action-${actionIndex}`;
          this.failStep(stepIndex, steps, stepDetails);
          return;
        }
      }
    } else {
      // If no actions, mark a step as complete immediately
      this.completeStep(stepIndex, steps, stepDetails);
    }
  }

  /**
   * Marks a step as successfully completed and moves to the next step
   * @param index - Index of the step to mark as complete
   * @param steps - Array of all steps in the tour
   * @param stepDetails - Array to track execution details
   */
  private completeStep(index: number, steps: TourStep[], stepDetails: StepDetail[]): void {
    stepDetails[index].endTime = new Date();
    stepDetails[index].duration = (stepDetails[index].endTime as Date).getTime() -
      (stepDetails[index].startTime as Date).getTime();
    stepDetails[index].isComplete = true;
    this.runStep(index + 1, steps, stepDetails);
  }

  /**
   * Marks a step as failed and moves to the next step
   * @param index - Index of the step to mark as failed
   * @param steps - Array of all steps in the tour
   * @param stepDetails - Array to track execution details
   */
  private failStep(index: number, steps: TourStep[], stepDetails: StepDetail[]): void {
    stepDetails[index].notFound = true;
    stepDetails[index].endTime = new Date();
    stepDetails[index].duration = (stepDetails[index].endTime as Date).getTime() -
      (stepDetails[index].startTime as Date).getTime();
    stepDetails[index].isComplete = false;
    this.overallTestResult = "FAIL";
    this.runStep(index + 1, steps, stepDetails);
  }

  /**
   * The main execution flow for test tours
   * Finds the first incomplete test, prepares step details, and starts execution
   */
  private executeTestTour(): void {
    let numberOfStepsCompleted = 0;
    let testTourObject: TestTourItem | undefined;
    const testTourData: TestTourItem[] = JSON.parse(UserlaneTourStorage.getTestTour() || '[]');

    // Find the first incomplete test in the test data
    while (typeof testTourObject === 'undefined' && this.currentTest < testTourData.length) {
      const checkTest = testTourData[this.currentTest];
      // If the test is not marked as completed, select it for execution
      if (typeof checkTest.completed === 'undefined' || !checkTest.completed) {
        testTourObject = checkTest;
      } else {
        // Otherwise, move to the next test
        this.currentTest++;
      }
    }

    // If no incomplete tests found, exit
    if (typeof testTourObject === 'undefined') {
      return;
    }

    const steps = testTourObject.steps;
    const tourId = testTourObject.tourId || "N/A";
    this.tourStartTime = new Date();

    // Initialize tracking details for each step in the tour
    const stepDetails: StepDetail[] = steps.map((step, index) => ({
      tourId,
      stepId: step.id || `step-${index}`,  // Use the provided ID or generate one
      actionId: null,                      // Will be set during execution
      title: step.title || `Step ${index + 1}`,
      startTime: null,                     // Will be set when a step starts
      endTime: null,                       // Will be set when a step completes
      duration: null,                      // Will be calculated on completion
      isComplete: false,                   // Tracks step completion status
      notFound: false,                     // Tracks if elements were found
    }));

    // Start the first step
    this.runStep(numberOfStepsCompleted, steps, stepDetails);
  }
}

/**
 * Class for running guided tours (introduction and guide tours)
 */
class UserlaneGuidedTourRunner {
  private static readonly TOUR_START_DELAY = 4000; // Delay before starting tours (ms)
  private tourType: 'introduction' | 'guide';

  /**
   * Creates a new User laneGuidedTourRunner
   * @param tourType - Type of guided tour to run
   */
  constructor(tourType: 'introduction' | 'guide') {
    this.tourType = tourType;
  }

  /**
   * Runs the guided tour
   */
  public async run(): Promise<void> {
    await UserlaneElementFinder.waitForWindowLoad();
    const tourData = this.tourType === 'introduction'
      ? UserlaneTourStorage.getIntroductionTour()
      : UserlaneTourStorage.getGuideTour();

    if (!tourData) return;

    const configOverrides = this.tourType === 'introduction'
      ? {
          onHighlighted: () => {
            let hasNotRun = true;
            if (hasNotRun) {
              setTimeout(() => {
                (window as any).TestTour.moveNext();
              }, 4000);
            }
          }
        }
      : { showButtons: ['none'] };

    this.initializeTour(tourData, configOverrides);
  }

  /**
   * Creates tour step objects from JSON data for use with the driver.js library
   * @param tourData - JSON string containing tour step data
   * @returns Array of formatted tour steps for driver.js
   */
  private createTourSteps(tourData: string): any[] {
    const tourSteps: TourStep[] = JSON.parse(tourData).steps;
    return tourSteps.map((step) => ({
      element: step.element === '""' ? null : `#${step.element}`,
      popover: { title: step.title, description: step.description },
      onHighlightStarted: () => {
        // Process each action defined for this step
        (step.onHighlightStarted || []).forEach((action) => {
          const targetElement = document.getElementById(action.target);
          if (!targetElement) return; // Skip if an element not found

          if (this.tourType === 'guide') {
            // For guide tours, wait for user interaction
            if (action.actionType === 'Click') {
              // Add a one-time click listener to advance tour after user clicks
              targetElement.addEventListener('click', () => {
                setTimeout(() => (window as any).TestTour.moveNext(), 2000);
              }, { once: true });
            }

            if (action.actionType === 'SetValue' && targetElement instanceof HTMLInputElement) {
              // Add one-time change listener to advance tour after user inputs value
              targetElement.addEventListener('change', () => {
                setTimeout(() => (window as any).TestTour.moveNext(), step.speed || 0);
              }, { once: true });
            }
          } else if (this.tourType === 'introduction') {
            // For introduction tours, automatically perform actions
            if (action.actionType === 'Click') {
              // Auto-click the element after a specified delay
              setTimeout(() => {
                targetElement.click();
              }, action.speed || 0);
            }

            if (action.actionType === 'SetValue' && targetElement instanceof HTMLInputElement) {
              // Add listener to advance tour after the value is set
              targetElement.addEventListener('change', () => {
                setTimeout(() => (window as any).TestTour.moveNext(), step.speed || 0);
              }, { once: true });
            }
          }
        });

        // If no actions defined, automatically advance after delay
        if ((step.onHighlightStarted || []).length === 0) {
          setTimeout(() => (window as any).TestTour.moveNext(), 4000);
        }
      },
    }));
  }

  /**
   * Initializes and starts a tour using the driver.js library
   * @param tourData - JSON string containing tour step data
   * @param configOverrides - Optional configuration overrides for driver.js
   */
  private initializeTour(tourData: string, configOverrides: object = {}): void {
    const steps = this.createTourSteps(tourData);
    // Create a new driver.js tour instance with the default configuration
    const tour = new (window as any).driver({
      showProgress: true,      // Show a progress indicator
      smoothScroll: true,      // Enable smooth scrolling to elements
      allowClose: false,       // Prevent user from closing the tour
      keyboardControl: false,  // Disable keyboard navigation
      steps,                   // Tour steps created from the data
      onDestroyStarted: () => {
        UserlaneTourStorage.clearTours();  // Clean up localStorage when the tour ends
        tour.destroy();            // Properly destroy the tour instance
      },
      ...configOverrides,      // Apply any custom configuration
    });

    // Start the tour after a delay to allow the page to fully load
    setTimeout(() => tour.drive(), UserlaneGuidedTourRunner.TOUR_START_DELAY);

    // Make the tour instance globally accessible for step navigation
    (window as any).TestTour = tour;
  }
}

/**
 * Main class for managing tours
 * Initializes and runs the appropriate tour based on localStorage data
 */
class UserlaneTourManager {
  /**
   * Initializes and starts the appropriate tour
   * Priority order:
   * 1. Test tours (fully automated)
   * 2. Introduction tours (guided with automatic actions)
   * 3. Guide tours (interactive, waiting for user actions)
   */
  public static initialize(): void {
    // Check for available tours in localStorage
    const testTour = UserlaneTourStorage.getTestTour();
    const introductionTour = UserlaneTourStorage.getIntroductionTour();
    const guideTour = UserlaneTourStorage.getGuideTour();

    // Start the appropriate tour based on priority
    if (testTour) {
      // Run an automated test tour
      const testTourRunner = new UserlaneTestTourRunner();
      testTourRunner.run();
    } else if (introductionTour) {
      // Run introduction tour
      const introTourRunner = new UserlaneGuidedTourRunner('introduction');
      introTourRunner.run();
    } else if (guideTour) {
      // Run guide tour
      const guideTourRunner = new UserlaneGuidedTourRunner('guide');
      guideTourRunner.run();
    }
  }
}


declare global {
  interface Window {
    UserlaneManager: UserlaneTourManager
  }
}

const UserlaneManager = new UserlaneTourManager()
window.UserlaneManager = UserlaneManager

// Export all classes for external use
export {
  UserlaneTourManager,
  UserlaneTestTourRunner,
  UserlaneGuidedTourRunner,
  UserlaneResultsReporter,
  UserlaneElementFinder,
  UserlaneDomActions,
  UserlaneTranslator,
  UserlaneTourStorage
};
