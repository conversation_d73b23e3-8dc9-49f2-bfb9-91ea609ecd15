// looking for default color scheme in localstorage and os settings
import type { Button } from 'level-components'

type BroadcastNavigationData = {
	type: 'switch-mode',
	darkMode: boolean
}

const lightSwitch = document.getElementById('color-scheme-switch') as Button
const isDarkModeStored = window.localStorage.getItem('color-scheme') != null

// register a broadcast-channel and catch session posts
const broadcastChannel = new BroadcastChannel('lvl-broadcast:color-scheme')
broadcastChannel.onmessage = async (event) => {
	const broadcastData = event.data as BroadcastNavigationData
	switch (broadcastData.type) {
		case 'switch-mode':
			setColorScheme(broadcastData.darkMode)
			break
	}
}

// no mode currently stored? handle via OS settings
if (!isDarkModeStored) {
	const handleOsColorSchemeChange = (event: MediaQueryListEvent) => {
		// if toggle was used once, then we ignore OS changes
		if (window.localStorage.getItem('color-scheme') != null) {
			window.matchMedia('(prefers-color-scheme: dark)').removeEventListener('change', handleOsColorSchemeChange)
			return
		}
		setColorScheme(event.matches)
	}
	window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', handleOsColorSchemeChange)
}

// store dark mode in local storage and switch to it via css
lightSwitch.addEventListener('click', event => {
	const mode = (event.target as Button).icon === 'moon'
	setColorScheme(!mode)
	window.localStorage.setItem('color-scheme', mode ? 'light' : 'dark')
	
	// notify other tabs about scheming
	const message: BroadcastNavigationData = { type: 'switch-mode', darkMode: !mode}
	broadcastChannel.postMessage(message)
})

function setColorScheme(darkMode: boolean) {
	document.documentElement.dataset['scheme'] = darkMode ? 'dark' : 'light'
	lightSwitch.icon = darkMode ? 'moon' : 'sun-bright'
}
