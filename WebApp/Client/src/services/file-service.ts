import type { But<PERSON>, <PERSON>, Toaster, Viewer, Dialog } from 'level-components'
import { MessageType } from 'level-components'
import Component from './component-service'
import FormInstance from './form-service'
import I18n from './translations'
import { LevelResponse } from 'level-components/types'
import Dropzone, { DropzoneFile } from 'dropzone'

export type CreatePageOptions = {
	target: HTMLElement
	dataSourceId: string
	createPageId: string
	defaultValues?: Record<string, any>
	file?: DropzoneFile
	embedded?: boolean
	allowFile?: boolean
	allowMultiFile?: boolean
	saveButtonLabel?: string
}

export type DropzoneOptions = {
	target: HTMLElement
	dataSourceId: string
	createPageId: string
	defaultValues?: Record<string, any> | Function
	embedded?: boolean
	acceptedFiles?: string[]
	openCreatePage?: boolean
}

class FileService {
	private formatNumberHR(number: number, fractionDigits = 0) {
		return number.toLocaleString(navigator.language, {
			useGrouping: true,
			minimumFractionDigits: fractionDigits,
			maximumFractionDigits: fractionDigits,
		})
	}

	private formatFileSizeHR(filesize: number) {
		filesize = filesize / 1024
		if (filesize < 1024)
			return this.formatNumberHR(filesize) + ' KB'
		filesize = filesize / 1024
		if (filesize < 1024)
			return this.formatNumberHR(filesize, 2) + ' MB'
		filesize = filesize / 1024
		if (filesize < 1024)
			return this.formatNumberHR(filesize, 2) + ' GB'
	}

	private getExtension(filename: string) {
		if (filename && filename.lastIndexOf('.') > -1)
			return filename.substring(filename.lastIndexOf('.') + 1).toUpperCase()
		return ''
	}

	private getFileIcon(filename: string): string {
		const extension = this.getExtension(filename).toLowerCase()
		switch (extension) {
			case 'jpg':
			case 'jpeg':
				return 'fa-file-jpg'
			case 'png':
				return 'fa-file-png'
			case 'gif':
				return 'fa-file-gif'
			case 'webp':
				return 'fa-file-image'
			case 'pdf':
				return 'fa-file-pdf'
			case 'doc':
			case 'docx':
				return 'fa-file-word'
			case 'xls':
			case 'xlsx':
				return 'fa-file-excel'
			case 'ppt':
			case 'pptx':
				return 'fa-presentation-screen'
			case 'txt':
			case 'json':
				return 'fa-file-lines'
			case 'xml':
				return 'fa-file-xml'
			case 'msi':
			case 'exe':
				return 'fa-cube'
			case 'zip':
				return 'fa-file-zip'
			default:
				return 'fa-file'
		}
	}

	public async showCreatePage(options: CreatePageOptions): Promise<boolean> {
		// apply some default option values
		options.embedded = options.embedded === true
		options.allowFile = options.allowFile === true

		const Page = window.Page
		const pageUrl = window.location.href

		if (!Page.createSlideOut) {
			console.warn("Unable to show create page. Missing create slide-out.")
			return false
		}

		// if the slideout is not part of the DOM, we are in a weird situation like for example the openseadragon fullscreen mode
		// -> lets remember the current parent of the slideout, move it to our active DOM and re-append it to its former parent later
		let createSlideOutParent: HTMLElement | undefined
		if (!document.body.contains(Page.createSlideOut)) {
			createSlideOutParent = Page.createSlideOut.parentElement ?? undefined
			document.querySelector('body')?.append(Page.createSlideOut)
			Page.createSlideOut.style.setProperty('--header-height', '0')
		}

		// prepare promise result
		let promiseResolve: Function
		let promiseResolved: boolean = false
		const externalPromise = new Promise<boolean>((resolve) => {
			promiseResolve = (value: boolean) => {
				if (!promiseResolved) {
					// re-append createSlideOut to original parent
					if (createSlideOutParent && Page.createSlideOut) {
						Page.createSlideOut.style.removeProperty('--header-height')
						createSlideOutParent.append(Page.createSlideOut)
					}
					resolve(value)
				}
				promiseResolved = true
			}
		})

		if (!options.embedded)
			Page.setPanelInfo(Page.getMainPageUrl() + '/Create')

		let fileQueue = Page.createSlideOut.querySelector('div.fileQueue')
		let elementPreview = Page.createSlideOut.querySelector('div.element-preview')
		let createButton = Page.createSlideOut.querySelector<Button>('[data-action=save]')

		if (!fileQueue || !elementPreview || !createButton)
			return false
		
		if (options.allowMultiFile !== false)
			fileQueue.classList.remove('hidden')
		else
			fileQueue.classList.add('hidden')

		// start file viewer loading animation and update element preview
		const fileDropzone = Page.createSlideOut.querySelector<HTMLElement>('.file-dropzone')
		if (options.file) {
			this.blockFileDropzone(fileDropzone)
			this.updateElementPreview(options.file)
		} else {
			elementPreview.querySelector('i')!.setAttribute('class', 'file-icon fal fa-memo-pad')
			elementPreview.querySelector('label')!.innerText = I18n.translate('newDataset')
			elementPreview.querySelector('legend')!.innerText = ''
			elementPreview.removeAttribute('fileUploadId')
			elementPreview.querySelector('.file-edit-button')!.setAttribute('disabled', '')
			elementPreview.querySelector('.file-edit-button')!.setAttribute('hidden', '')
			elementPreview?.querySelector('.file-upload-button')?.removeAttribute('hidden')
		}

		if (options.allowFile)
			Page.createSlideOut.classList.add('allow-file')
		else
			Page.createSlideOut.classList.remove('allow-file')

		// load create page (if needed) and update page info
		await Page.createSlideOut.loading
		let createForm = Page.createSlideOut.querySelector('lvl-form')
		if ((createForm == null || createForm.id != `create-form-${options.createPageId}`) && !Page.createSlideOut.open) {
			Page.createSlideOut.loading = new Promise<void>((resolve, reject) => {
				(async () => {
					try {
						if (!fileDropzone || !Page.createSlideOut)
							return

						// destroy old dropzone if there is any
						// @ts-ignore
						fileDropzone.parentElement.dropzone?.destroy()
						fileDropzone.parentElement!.querySelector('.dropzone-preview')?.remove()

						// init new dropzone
						if (options.allowFile)
							this.initDropzone({
								target: fileDropzone.parentElement!,
								dataSourceId: options.dataSourceId,
								createPageId: options.createPageId,
								defaultValues: options.defaultValues,
								embedded: options.embedded,
								openCreatePage: false
							}).then()

						Page.createSlideOut!.skeleton = true
						await Page.showCreatePanel(`/Api/Pages/${options.createPageId}/Render`)
						createForm = Page.createSlideOut!.querySelector<Form>('lvl-form')!

						// fill external default values
						if (options.defaultValues) {
							await Component.waitForComponentInitialization(createForm as HTMLElement)
							createForm.setValues(options.defaultValues, true)
						}

						// we need to set the url again because inside Page.showCreatePanel the URL gets rewritten
						if (options.embedded)
							Page.setPanelInfo(pageUrl)
						else
							Page.setPanelInfo(Page.getMainPageUrl() + '/Create')

						// disable skeleton
						Page.createSlideOut.skeleton = false

						if (!createButton)
							return resolve()

						if (options.saveButtonLabel)
							createButton.label = options.saveButtonLabel

						resolve()
					} catch (err) {
						reject(err)
					}
				})()
			})
		} else if (!Page.createSlideOut.open) {
			await Component.waitForComponentInitialization(createForm as HTMLElement)
			if (createForm) {
				createForm.reset(true)

				// re-fill external default values
				if (options.defaultValues)
					createForm.setValues(options.defaultValues, true)
				
				// update preview target of internal blueprint(s)
				let blueprint = createForm.querySelector('lvl-blueprint')
				if (blueprint && options.defaultValues)
					blueprint.previewTarget = options.defaultValues['AnnotationX'] && options.defaultValues['AnnotationY'] ? { ...blueprint.previewTarget, x: options.defaultValues['AnnotationX'], y: options.defaultValues['AnnotationY']} : undefined
			}
			Page.createSlideOut.open = true
		}

		// set create button event
		createButton.onClick = async () => {
			let url = `/Api/DataSources/${options.dataSourceId}/Elements`
			if (elementPreview.hasAttribute('fileUploadId'))
				url += '?fileUploadId=' + elementPreview.getAttribute('fileUploadId')
			const result = await FormInstance.storeData(createForm!, url, 'POST', true, I18n.translate('elementIsSaved'))
			if (result && !result.error) {
				Page.createSlideOut!.open = false
				promiseResolve(true)
			}
		}

		// return false on close (if not resolved successfully yet)
		Page.createSlideOut.addEventListener('dialog-close', () => {
			promiseResolve(false)
		}, {once: true})

		return externalPromise
	}

	private updateElementPreview(file: DropzoneFile) {
		const Page = window.Page
		let elementPreview = Page.createSlideOut?.querySelector('div.element-preview')
		let createButton = Page.createSlideOut?.querySelector<Button>('[data-action=save]')

		if (!elementPreview || !createButton)
			return

		elementPreview.setAttribute('id', file.upload?.uuid ?? '')
		elementPreview.querySelector('i')!.setAttribute('class', 'file-icon fal fa-arrow-up-from-bracket')
		elementPreview.querySelector('label')!.innerText = file.name
		let legend = this.formatFileSizeHR(file.size)
		let extension = this.getExtension(file.name)
		if (extension)
			legend+= ' · '+extension
		elementPreview.querySelector('legend')!.innerText = legend ?? ''
		if (createButton && elementPreview.hasAttribute('progress'))
			createButton.disabled = true

		const uploadInfo = Page.createSlideOut!.querySelector('.file-dropzone__upload')
		if (!uploadInfo)
			return

		uploadInfo.querySelector('.file-upload-filename')!.innerHTML = file.name
		uploadInfo.querySelector('.file-size-total')!.innerHTML = this.formatFileSizeHR(file.size) ?? ''
		uploadInfo.querySelector('.file-type')!.innerHTML = this.getExtension(file.name)
	}

	public async initDropzone(options: DropzoneOptions) {
		// apply some default option values
		options.embedded = options.embedded === true
		options.openCreatePage = options.openCreatePage !== false

		const Page = window.Page
		options.acceptedFiles = options.acceptedFiles?.map((extension) => {
			return extension.toUpperCase()
		}) ?? []

		const markAsFailed = (file: DropzoneFile, errorMessage: string) => {
			console.error(`failed to upload file "${file.name}".`, errorMessage)

			let elementPreview = Page.createSlideOut?.querySelector(`.element-preview[id="${file.upload?.uuid}"]`)
			if (elementPreview) {
				elementPreview.classList.add('error')
				elementPreview.querySelector('legend')!.innerText = I18n.translate('errorText')
			}

			if (uploadInfo) {
				uploadInfo.classList.add('error')
				uploadInfo.querySelector<HTMLElement>('.file-upload-status')!.innerText = I18n.translate('error')
				uploadInfo.querySelector('lvl-button')!.setAttribute('label', I18n.translate('tryAgain'))
			}

			let createForm = Page.createSlideOut?.querySelector('lvl-form')
			createForm?.unblockFileDataFields()
		}

		// append preview
		const preview = document.createElement('div')
		preview.classList.add('dropzone-preview')
		preview.innerHTML = `<div><i class="fal fa-arrow-down-to-line"></i><span>${I18n.translate('dropFilesToUpload')}</span></div>`
		options.target.append(preview)

		const uploadInfo = Page.createSlideOut?.querySelector<HTMLElement>('.file-dropzone__upload')
		const fileViewer = Page.createSlideOut?.querySelector<Viewer>('.filePreview .file-viewer')
		const fileDropzone = Page.createSlideOut?.querySelector<HTMLElement>('.file-dropzone')
		const uploadAbortButton = Page.createSlideOut?.querySelector<Button>('.file-upload-abort')

		if (!uploadInfo || !uploadAbortButton || !fileViewer || !fileDropzone)
			return

		// wait for dropzone to be available
		/*
		if (!window.hasOwnProperty('Dropzone')) {
			const start = new Date().getTime()
			while (!window.hasOwnProperty('Dropzone') && new Date().getTime() - 1000 < start) {
				await new Promise(resolve => setTimeout(resolve, 50))
			}
		}
		*/

		const fileInstance = this
		const slideOut = options.target.closest<HTMLElement>('lvl-slide-out')

		// init dropzone
		const dropzone = new Dropzone(options.target, {
			url: `/Api/DataSources/${options.dataSourceId}/Files`,
			maxFilesize: 1024, // 1 GiB
			createImageThumbnails: false,
			hiddenInputContainer: options.target,
			clickable: slideOut ? [
				slideOut.querySelector<HTMLElement>('.file-upload-button')!,
				slideOut.querySelector<HTMLElement>('.file-upload-start')!
			] : false, // buttons are only available if we are inside a slide out
			addedfile: function (file: DropzoneFile) {

				const toaster = document.getElementById('toaster') as Toaster
				if (dropzone.files.length > 1) {
					const heading = I18n.translate('fileDialog/multiFile')
					toaster?.notifySimple({ heading: heading, type: MessageType.Warning })
					dropzone.removeFile(file)
					return
				}

				// is the extension allowed?
				const extension = fileInstance.getExtension(file.name).toUpperCase()
				if (options.acceptedFiles && options.acceptedFiles.length > 0 && options.acceptedFiles.indexOf(extension) == -1) {
					const heading = I18n.translate('fileDialog/notAllowedHeading')
					let content = I18n.translate('fileDialog/notAllowedExplanationPlaceholder')
					content = content.replace(/\{0}/gm, extension)
					toaster?.notify({
						heading: heading,
						content: content,
						type: MessageType.Warning,
					})
					dropzone.removeFile(file)
					return
				}

				// open create mask?
				if (options.openCreatePage) {
					let defaultValues: Record<string, any> | undefined = typeof options.defaultValues === 'function' ? options.defaultValues() : options.defaultValues
					fileInstance.showCreatePage({
						target: options.target,
						dataSourceId: options.dataSourceId,
						createPageId: options.createPageId,
						defaultValues: defaultValues,
						file: file,
						embedded: options.embedded,
						allowFile: true
					}).then((success) => {
						if (success)
							document.querySelector('lvl-multi-data-view')?.reload()
					})
				} else { 	// if create mask is already open, just update the file preview
					const fileDropzone = Page.createSlideOut?.querySelector<HTMLElement>('.file-dropzone')
					fileInstance.blockFileDropzone(fileDropzone)
					fileInstance.updateElementPreview(file)
				}

				let createForm = Page.createSlideOut?.querySelector('lvl-form')
				createForm?.blockFileDataFields()

				uploadAbortButton.onClick = () => {
					dropzone.removeFile(file)

					createForm?.unblockFileDataFields()
					const elementPreview = Page.createSlideOut?.querySelector<HTMLElement>(`.element-preview[id="${file.upload?.uuid}"]`)
					if (!elementPreview)
						return

					const failed = elementPreview.classList.contains('error')
					fileInstance.resetElementPreview(elementPreview)
					fileInstance.resetFileDropzone(fileDropzone)
					fileInstance.resetUploadInfo(uploadInfo!)

					uploadAbortButton.onClick = undefined

					// retry if failed
					if (failed)
						fileDropzone.querySelector('lvl-button')?.dispatchEvent(new Event('click'))
				}
				uploadAbortButton.disabled = false
				uploadAbortButton.label = I18n.translate('abort')
			},
			uploadprogress: function (file: DropzoneFile, progress: number, bytesSent: number) {
				let elementPreview = Page.createSlideOut?.querySelector<HTMLElement>(`.element-preview[id="${file.upload?.uuid}"]`)
				elementPreview?.setAttribute('progress', '')
				elementPreview?.style.setProperty('--progress', progress.toString())

				uploadInfo.querySelector<HTMLElement>('.file-size-uploaded')!.innerText = fileInstance.formatFileSizeHR(bytesSent) ?? ''
				uploadInfo.querySelector<HTMLElement>('.file-upload-percent')!.innerText = Math.round(progress).toString()
				uploadInfo.querySelector<HTMLElement>('.file-upload-progress')?.style.setProperty('--progress', progress.toString())
			},
			success: async (file: DropzoneFile) => {
				const response: LevelResponse<Record<string,any>> | undefined = file.xhr?.response ? JSON.parse(file.xhr.response) : undefined
				uploadAbortButton.onClick = undefined

				if (response?.error) {
					let createForm = Page.createSlideOut?.querySelector('lvl-form')
					createForm?.unblockFileDataFields()
					return markAsFailed(file, response.error.errorMessage)
				}

				let elementPreview = Page.createSlideOut?.querySelector<HTMLElement>(`.element-preview[id="${file.upload?.uuid}"]`)
				elementPreview?.removeAttribute('progress')
				elementPreview?.style.removeProperty('--progress')
				elementPreview?.querySelector('i')?.setAttribute('class', 'file-icon fal ' + this.getFileIcon(file.name))
				let fileUploadId = response?.data ? response.data["fileId"] : ''
				elementPreview?.setAttribute('fileUploadId', fileUploadId ?? '')
				elementPreview?.querySelector('.file-edit-button')?.removeAttribute('disabled')
				elementPreview?.querySelector('.file-edit-button')?.removeAttribute('hidden')
				elementPreview?.querySelector('.file-upload-button')?.setAttribute('hidden', '')
				Page.createSlideOut?.querySelector('[data-action=save]')?.removeAttribute('disabled')

				// disable abort button
				uploadInfo.classList.add('success')
				uploadInfo.querySelector<HTMLElement>('.file-upload-status')!.innerText = I18n.translate('finished')
				uploadInfo.querySelector('lvl-button')!.disabled = true

				// update viewer
				fileViewer.classList.add('enabled')
				fileViewer.querySelector('apryse-webviewer')?.remove()
				fileViewer.loadNewDocument(options.dataSourceId, fileUploadId, file.name.substring(file.name.lastIndexOf('.') + 1))

				// update form with data
				await Page.createSlideOut?.loading
				let createForm = Page.createSlideOut?.querySelector('lvl-form')
				if(response?.data && response.data.mailData)
					createForm?.setMailData(response.data.mailData)
				createForm?.unblockFileDataFields()

				// remove file from queue (we don't need it anymore)
				dropzone.removeFile(file)
			},
			error: (file: DropzoneFile, message: string | Error, xhr: XMLHttpRequest) => {
				const response = xhr.response as LevelResponse
				markAsFailed(file, response.error?.errorMessage ?? (message instanceof Error ? message.message : message) ?? '')
			},
			canceled: () => {
				// nothing to do (just prevent the default behaviour which is to throw an error)
			},
		})
		Page.createSlideOut?.addEventListener('dialog-close', () => {
			// reset length to 0 (removes entries)
			dropzone.files.length = 0

			this.resetFileDropzone(fileDropzone)
			this.resetUploadInfo(uploadInfo)
		})
		Page.getPageChangeSignal().addEventListener('abort', () => {
			options.target.classList.remove('dropzone-init-done')
			dropzone.destroy()
			preview.remove()
		})
		options.target.classList.add('dropzone-init-done')
	}

	public async initDropzoneDetail(target: HTMLElement, form: Form, dataSourceId: string, elementId: string, acceptedFiles: string[] = []) {
		const Page = window.Page
		acceptedFiles.map(extension => extension.toUpperCase())

		const markAsFailed = (file: DropzoneFile, errorMessage: string) => {
			console.error(`failed to upload file "${file.name}".`, errorMessage)

			if (uploadInfo) {
				uploadInfo.classList.add('error')
				uploadInfo.querySelector<HTMLElement>('.file-upload-status')!.innerText = I18n.translate('error')
				uploadInfo.querySelector('lvl-button')!.setAttribute('label', I18n.translate('tryAgain'))
			}

			form.unblockFileDataFields()
		}

		let preview = target.querySelector<HTMLElement>('div.dropzone-preview')
		if (preview == null) {
			// append preview
			preview = document.createElement('div')
			preview.classList.add('dropzone-preview')
			preview.innerHTML = `<div><i class="fal fa-arrow-down-to-line"></i><span>${I18n.translate('dropFilesToUpload')}</span></div>`
			target.append(preview)
		}

		const uploadInfo = target.querySelector<HTMLElement>('.file-dropzone__upload')
		const fileViewer = target.querySelector<HTMLElement>('.filePreview .file-viewer')
		const fileDropzone = target.querySelector<HTMLElement>('.file-dropzone')
		const uploadAbortButton = target.querySelector<Button>('.file-upload-abort')

		if (!uploadInfo || !uploadAbortButton || !fileViewer || !fileDropzone)
			return

		const fileInstance = this

		// init dropzone
		let fileWasDropped = false
		let viewerWasEmpty = false
		const dropzone = new Dropzone(target, {
			url: `/Api/DataSources/${dataSourceId}/Files`,
			maxFilesize: 1024, // 1 GiB
			createImageThumbnails: false,
			autoProcessQueue: false,
			hiddenInputContainer: target,
			clickable: [
				target.querySelector<HTMLElement>('.file-upload-start')!,
				target.querySelector<HTMLElement>('lvl-button[data-action="start-update"]')!
			],
			drop: function(){
				fileWasDropped = true
				viewerWasEmpty = target.querySelector('lvl-viewer')?.classList.contains('enabled') == false
			},
			addedfile: async function (file: DropzoneFile) {
				if(fileWasDropped && viewerWasEmpty) {
					target.classList.remove('dz-drag-hover')
				}
				if(fileWasDropped && !viewerWasEmpty) {
					fileWasDropped = false
					const fileUpdateDialog = Page.getFileUpdateDialog()
					fileUpdateDialog.showOnce()

					function getPromiseFromDialog() : Promise<boolean> {
						return new Promise<boolean>((resolve) => {
							fileUpdateDialog.addEventListener('dialog-discard', () => {
								resolve(true)
							})
							fileUpdateDialog.addEventListener('dialog-abort', () => {
								resolve(true)
							})
							fileUpdateDialog.addEventListener('dialog-update', () => {
								resolve(false)
							})
						})
					}
					const canceledUpload = await getPromiseFromDialog()
					target.classList.remove('dz-drag-hover')
					if(canceledUpload){
						dropzone.removeFile(file)
						return
					}
				}
				const toaster = document.getElementById('toaster') as Toaster
				if (dropzone.files.length > 1) {
					const heading = I18n.translate('fileDialog/multiFile')
					toaster?.notifySimple({ heading: heading, type: MessageType.Warning })
					dropzone.removeFile(file)
					return
				}

				// is the extension allowed?
				const extension = fileInstance.getExtension(file.name).toUpperCase()
				if (acceptedFiles.length > 0 && acceptedFiles.indexOf(extension) == -1) {
					const heading = I18n.translate('fileDialog/notAllowedHeading')
					let content = I18n.translate('fileDialog/notAllowedExplanationPlaceholder')
					content = content.replace(/\{0}/gm, extension)
					toaster?.notify({
						heading: heading,
						content: content,
						type: MessageType.Warning,
					})
					dropzone.removeFile(file)
					return
				}

				const fileDropzone = target.querySelector<HTMLElement>('.file-dropzone')
				fileInstance.blockFileDropzone(fileDropzone)
				fileInstance.updateElementPreview(file)

				form.blockFileDataFields()

				uploadAbortButton.onClick = () => {
					dropzone.removeFile(file)

					form.unblockFileDataFields()
					const elementPreview = target.querySelector<HTMLElement>(`.element-preview[id="${file.upload?.uuid}"]`)
					if (!elementPreview)
						return

					const failed = elementPreview.classList.contains('error')
					fileInstance.resetElementPreview(elementPreview)
					fileInstance.resetFileDropzone(fileDropzone!)
					fileInstance.resetUploadInfo(uploadInfo!)

					uploadAbortButton.onClick = undefined

					// retry if failed
					if (failed)
						fileDropzone!.querySelector('lvl-button')?.dispatchEvent(new Event('click'))
				}
				uploadAbortButton.disabled = false
				uploadAbortButton.label = I18n.translate('abort')
				dropzone.uploadFile(file)
			},
			uploadprogress: function (file: DropzoneFile, progress: number, bytesSent: number) {
				let elementPreview = target.querySelector<HTMLElement>(`.element-preview[id="${file.upload?.uuid}"]`)
				elementPreview?.setAttribute('progress', '')
				elementPreview?.style.setProperty('--progress', progress.toString())

				uploadInfo.querySelector<HTMLElement>('.file-size-uploaded')!.innerText = fileInstance.formatFileSizeHR(bytesSent) ?? ''
				uploadInfo.querySelector<HTMLElement>('.file-upload-percent')!.innerText = Math.round(progress).toString()
				uploadInfo.querySelector<HTMLElement>('.file-upload-progress')?.style.setProperty('--progress', progress.toString())
			},
			success: async (file: DropzoneFile) => {
				const response: LevelResponse<Record<string,any>> | undefined = file.xhr?.response ? JSON.parse(file.xhr.response) : undefined
				uploadAbortButton.onClick = undefined

				if (response?.error) {
					form.unblockFileDataFields()
					return markAsFailed(file, response.error.errorMessage)
				}

				if(file.upload == null) {
					form.unblockFileDataFields()
					return markAsFailed(file, "File could not be uploaded.")
				}

				let fileUploadId = response?.data ? response.data["fileId"] : ''

				// disable abort button
				uploadInfo.classList.add('success')
				uploadInfo.querySelector<HTMLElement>('.file-upload-status')!.innerText = I18n.translate('finished')
				uploadInfo.querySelector('lvl-button')!.disabled = true

				// update element with file
				let url = `/Api/DataSources/${dataSourceId}/Elements/${elementId}?fileUploadId=${fileUploadId}`
				const result = await FormInstance.storeData(form, url, 'PATCH', false, '', true)

				if(result == null || result.error){
					form.unblockFileDataFields()
					return markAsFailed(file, "Element could not be updated with file.")
				}

				const fileId = result.data["fileInfo"]["fileId"]

				// update viewer
				fileViewer.classList.add('enabled')
				fileViewer.querySelector('apryse-webviewer')?.remove()
				const viewer = document.querySelector<Viewer>('lvl-viewer')!
				viewer.loadNewDocument(dataSourceId, fileId, file.name.substring(file.name.lastIndexOf('.') + 1))

				// update form with data
				if(response?.data && response.data.mailData)
					form.setMailData(response.data.mailData)
				form.unblockFileDataFields()

				this.showFileOptionsButton()

				// remove file from queue (we don't need it anymore)
				dropzone.removeFile(file)
			},
			error: (file: DropzoneFile, message: string | Error, xhr: XMLHttpRequest) => {
				const response = xhr.response as LevelResponse
				markAsFailed(file, response.error?.errorMessage ?? (message instanceof Error ? message.message : message) ?? '')
			},
			canceled: () => {
				// nothing to do (just prevent the default behaviour which is to throw an error)
			},
		})

		target.addEventListener('dialog-close', () => {
			// reset length to 0 (removes entries)
			dropzone.files.length = 0

			this.resetFileDropzone(fileDropzone)
			this.resetUploadInfo(uploadInfo)
		})
		Page.getPageChangeSignal().addEventListener('abort', () => {
			target.classList.remove('dropzone-init-done')
			dropzone.destroy()
			preview.remove()
		})
		target.classList.add('dropzone-init-done')
	}

	public resetElementPreview(elementPreview: HTMLElement) {
		elementPreview.classList.remove('error')
		elementPreview.removeAttribute('progress')
		elementPreview.style.removeProperty('--progress')
		elementPreview.querySelector('i')!.setAttribute('class', 'file-icon fal fa-memo-pad')
		elementPreview.querySelector('label')!.innerText = I18n.translate('newDataset')
		elementPreview.querySelector('legend')!.innerText = ''
		elementPreview.removeAttribute('fileUploadId')
	}

	public resetDropzone(target: HTMLElement) {
		const fileDropzone = target.querySelector<HTMLElement>('.file-dropzone')
		const uploadInfo = target.querySelector<HTMLElement>('.file-dropzone__upload')

		if(fileDropzone)
			this.resetFileDropzone(fileDropzone)

		if(uploadInfo)
			this.resetUploadInfo(uploadInfo)
	}

	public async initFileOptionsButton(target: HTMLElement, form: Form, dataSourceId: string, elementId: string){
		const cutFileButton = document.querySelector('lvl-menu-item[data-action="cut-file"]')
		if(cutFileButton == null)
			return

		const uploadFileButton = document.querySelector('lvl-menu-item[data-action="update-file"]')
		if(uploadFileButton == null)
			return

		const preview = target.querySelector("#detail-view-file-dropzone")
		const fileViewer = target.querySelector('.filePreview .file-viewer')
		const dialog = target.querySelector<Dialog>('lvl-dialog')
		if(dialog == null)
			return

		const startCutButton = dialog.querySelector<Button>('lvl-button[data-action="start-cut"]')!

		const showDeleteDialog = () => {
			dialog.heading = I18n.translate('fileDialog/cutHeading')
			const dialogForm = dialog.querySelector<Form>('lvl-form')!
			dialogForm.removeContent()
			const warning = I18n.translate('fileDialog/cutHeader')
			dialogForm.insertAdjacentHTML('beforeend', `<h3>${warning}</h3>`)
			const explanation = I18n.translate('fileDialog/cutExplanation')
			dialogForm.insertAdjacentHTML('beforeend', `<label>${explanation}</label>`)

			startUpdateButton.hidden = true
			startCutButton.hidden = false
			startCutButton.addEventListener('click', handleCutFileClick, { signal: window.Page.getPageChangeSignal() })

			dialog.open = true
		}
		cutFileButton.addEventListener('click', showDeleteDialog, { signal: window.Page.getPageChangeSignal() })
		const handleCutFileClick = async () => {
			form.blockFileDataFields()
			const responseData = await FormInstance.storeData(form, `/Api/DataSources/${dataSourceId}/Elements/${elementId}/RemoveFile`, 'PATCH', false, '', true)
			if (responseData != null){
				form.unblockFileDataFields()

				if(fileViewer != null){
					fileViewer.classList.remove('enabled')
					fileViewer.querySelector('apryse-webviewer')?.remove()
				}

				if(preview != null){
					preview.classList.remove('hidden')
				}

				this.resetDropzone(target)
				this.hideFileOptionsButton()
			}
			dialog.open = false
			startCutButton.removeEventListener('click', handleCutFileClick)
			startCutButton.hidden = true
		}

		const startUpdateButton = dialog.querySelector<Button>('lvl-button[data-action="start-update"]')!
		const showUpdateDialog = () => {
			dialog.heading = I18n.translate('fileDialog/updateHeading')
			const dialogForm = dialog.querySelector<Form>('lvl-form')!
			dialogForm.removeContent()
			const warning = I18n.translate('fileDialog/updateHeader')
			dialogForm.insertAdjacentHTML('beforeend', `<h3>${warning}</h3>`)
			const explanation = I18n.translate('fileDialog/updateExplanation')
			dialogForm.insertAdjacentHTML('beforeend', `<label>${explanation}</label>`)

			startCutButton.hidden = true
			startUpdateButton.hidden = false
			startUpdateButton?.addEventListener('click', handleUpdateFileClick, { signal: window.Page.getPageChangeSignal() })

			dialog.open = true
		}
		uploadFileButton.addEventListener('click', showUpdateDialog, { signal: window.Page.getPageChangeSignal() })

		const handleUpdateFileClick = async () => {
			dialog.open = false
			startUpdateButton.removeEventListener('click', handleUpdateFileClick)
			startUpdateButton.hidden = true
		}


		fileViewer?.addEventListener('action-button:click', (event) => {
			const customEvent = event as CustomEvent<string>
			if(customEvent.detail == "viewer-delete"){
				showDeleteDialog()
			}
			if(customEvent.detail == "viewer-update"){
				showUpdateDialog()
			}
		})
	}

	public showFileOptionsButton(){
		window.Page.buttonConfig.fileOptionsButton.hidden = false
		window.Page.buttonConfig.fileOptionsButton.skeleton = false
	}

	public hideFileOptionsButton(){
		window.Page.buttonConfig.fileOptionsButton.hidden = true
		window.Page.buttonConfig.fileOptionsButton.skeleton = true
	}

	private blockFileDropzone(fileDropzone?: HTMLElement | null) {
		if (!fileDropzone)
			return
		fileDropzone.classList.remove('empty')
		fileDropzone.classList.add('uploading')
	}

	private resetFileDropzone(fileDropzone: HTMLElement) {
		fileDropzone.classList.remove('uploading')
		fileDropzone.classList.add('empty')
		fileDropzone.querySelector('.file-dropzone__upload')!.classList.remove('error')
	}

	public resetUploadInfo(uploadInfo: HTMLElement) {
		uploadInfo.classList.remove('success')
		uploadInfo.querySelector<HTMLElement>('.file-upload-status')!.innerText = I18n.translate('uploading') + ' ...'
		uploadInfo.querySelector<HTMLElement>('.file-upload-filename')!.innerHTML = ''
		uploadInfo.querySelector<HTMLElement>('.file-size-total')!.innerHTML = '0'
		uploadInfo.querySelector<HTMLElement>('.file-type')!.innerHTML = ''
		uploadInfo.querySelector<HTMLElement>('.file-size-uploaded')!.innerText = '0'
		uploadInfo.querySelector<HTMLElement>('.file-upload-percent')!.innerText = '0'
		uploadInfo.querySelector<HTMLElement>('.file-upload-progress')!.style.removeProperty('--progress')
	}
}

declare global {
	interface Window {
		FileManager: FileService
	}
}

const FileManager = new FileService()
window.FileManager = FileManager
export default FileManager