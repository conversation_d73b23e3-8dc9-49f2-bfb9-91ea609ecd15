import {
	UserlaneTourManager,
	UserlaneTourStorage,
	TestTourItem,
	TourStep
} from '../features/userlanes/userlaneManager';


// Type definitions
interface TourData {
	id: string;
	name: string;
	url: string;
	steps: TourStep[];
	status: boolean;
}

interface UserlaneServiceType {
	initialize(): void;
	startIntroductionTour(userlaneId: string): Promise<void>;
	startGuideTour(userlaneId: string): Promise<void>;
	getTourStatus(): {
		testTour: string | null;
		introductionTour: string | null;
		guideTour: string | null;
	};
	runSelectedTests(testInformation: TestTourItem[]): Promise<void>;
}

class UserlaneService implements UserlaneServiceType {
	private readonly TOUR_START_DELAY = 4000;
	private tourChangeController = new AbortController();

	constructor() {
		this.addTourEventHandling();
	}

	public getTourChangeSignal(): AbortSignal {
		return this.tourChangeController.signal;
	}

	private addTourEventHandling() {
		document.addEventListener('tour:started', () => {
			// Handle tour start
		}, { signal: this.getTourChangeSignal() });

		document.addEventListener('tour:ended', () => {
			this.clearTours();
		}, { signal: this.getTourChangeSignal() });
	}

	/**
	 * Initialize and start the appropriate tour
	 */
	public initialize(): void {
		this.tourChangeController.abort();
		this.tourChangeController = new AbortController();
		this.addTourEventHandling();
		UserlaneTourManager.initialize();
	}

	/**
	 * Start an introduction tour
	 * @param userlaneId - The ID of the userlane to run
	 */
	public async startIntroductionTour(userlaneId: string): Promise<void> {
		try {
			const response = await window.Page.getJSON<TourData>(`/Api/Userlane/RunTour/${userlaneId}`);
			if (!response.data) {
				throw new Error('No tour data received');
			}

			localStorage.setItem('introductionTour', JSON.stringify([{
				id: userlaneId,
				name: response.data.name,
				url: response.data.url,
				steps: response.data.steps,
				status: false
			}]));

			setTimeout(() => this.initialize(), this.TOUR_START_DELAY);
		} catch (error) {
			console.error('Failed to start introduction tour:', error);
			(window.Page as any).showErrorToast('Tour Error', 'Failed to start introduction tour');
		}
	}

	/**
	 * Start a guide tour
	 * @param userlaneId - The ID of the userlane to run
	 */
	public async startGuideTour(userlaneId: string): Promise<void> {
		try {
			const response = await window.Page.getJSON<TourData>(`/Api/Userlane/RunTour/${userlaneId}`);
			if (!response.data) {
				throw new Error('No tour data received');
			}

			localStorage.setItem('guideTour', JSON.stringify([{
				id: userlaneId,
				name: response.data.name,
				url: response.data.url,
				steps: response.data.steps,
				status: false
			}]));

			setTimeout(() => this.initialize(), this.TOUR_START_DELAY);
		} catch (error) {
			console.error('Failed to start guide tour:', error);
			(window.Page as any).showErrorToast('Tour Error', 'Failed to start guide tour');
		}
	}

	/**
	 * Get the current tour status
	 */
	public getTourStatus() {
		return {
			testTour: UserlaneTourStorage.getTestTour(),
			introductionTour: UserlaneTourStorage.getIntroductionTour(),
			guideTour: UserlaneTourStorage.getGuideTour()
		};
	}

	/**
	 * Clear all tours
	 */
	private clearTours(): void {
		UserlaneTourStorage.clearTours();
	}

	public async runSelectedTests(testInformation: TestTourItem[]): Promise<void> {
		try {
			if (testInformation.length < 1) {
				throw new Error('No test information provided');
			}
			
			UserlaneTourStorage.updateTestTour(testInformation);
			window.Page.openNewTab(testInformation[0].url, true);
		} catch (error) {
			console.error('Failed to run selected tests:', error);
			(window.Page as any).showErrorToast('Tour Error', error instanceof Error ? error.message : 'Failed to run selected tests');
		}
	}
}

// Create and export the global instance
const UserlaneInstance = new UserlaneService();

// Add type declaration for the global window object
declare global {
	interface Window {
		Userlane: UserlaneServiceType;
	}
}

// Make it available globally
window.Userlane = UserlaneInstance;
