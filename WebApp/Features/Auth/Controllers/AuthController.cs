using System.Diagnostics.CodeAnalysis;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Microsoft.AspNetCore.Mvc;
using ILogger = Serilog.ILogger;

namespace Levelbuild.Frontend.WebApp.Features.Auth.Controllers;

/// <summary>
/// Controller for a users authentication progress
/// </summary>
[Route("[controller]/[action]")]
[ApiExplorerSettings(IgnoreApi = true), ExcludeFromCodeCoverage]
public class AuthController : Controller
{
	private readonly ILogger _logger;
	private readonly UserManager _userManager;

	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="userManager"></param>
	public AuthController(ILogManager logManager, UserManager userManager)
	{
		_logger = logManager.GetLoggerForClass<AuthController>();
		_userManager = userManager;
	}
	
	#region Actions

	/// <summary>
	/// Logs the user out of the Webclient and Zitadel.
	/// </summary>
	[HttpGet]
	public IActionResult Logout() {
		// Nothing for now - just used as an entry point for Nginx to redirect to the real logout pages.
		return Ok();
	}
	
	/// <summary>
	/// Impersonate a given user.
	/// </summary>
	/// <param name="userId"></param>
	[HttpPost("{userId}")]
	public async Task<IActionResult> Impersonate(Guid userId)
	{
		try
		{
			await _userManager.ImpersonateUserAsync(userId);

			return Redirect("/");
		}
		catch (Exception e)
		{
			_logger.Error(e, "User impersonation failed");
			return BadRequest(e.Message);
		}
	}
	
	/// <summary>
	/// Stops impersonating and redirects back to dashboard.
	/// </summary>
	[HttpGet]
	public async Task<IActionResult> EndImpersonation()
	{
		if (await _userManager.CheckForImpersonationAsync())
			await _userManager.StopImpersonationAsync();
		
		return Redirect("/");
	}
	
	/// <summary>
	/// Checks if a given user can be impersonated.
	/// </summary>
	[HttpGet("{userId}")]
	public async Task<IActionResult> CheckImpersonationRights(Guid userId)
	{
		if (await _userManager.CanUserBeImpersonatedAsync(userId))
			return Ok();
		
		return Unauthorized();
	}

	#endregion
	
}