using System.Text.Json;
using Levelbuild.Frontend.WebApp.Features.Auth.Dtos;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.Services;

namespace Levelbuild.Frontend.WebApp.Features.Auth.Services;

/// <summary>
/// Class that handles caching of user impersonation info.
/// </summary>
public class UserImpersonationCache
{
	#region Fields

	private readonly IRedisAccessService _redisAccessService;

	#endregion

	#region Constructor

	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="redisAccessService"></param>
	public UserImpersonationCache(IRedisAccessService redisAccessService)
	{
		_redisAccessService = redisAccessService;
	}

	#endregion

	#region Methods
	
	/// <summary>
	/// Checks if the given user is impersonating some other user right now.
	/// </summary>
	/// <param name="userId"></param>
	/// <returns>True if given user id belongs to an actor.</returns>
	public async Task<bool> IsActorAsync(Guid userId)
	{
		return await _redisAccessService.HashExistsAsync(RedisConstants.UserImpersonationKey, userId.ToString());
	}

	/// <summary>
	/// Adds impersonation info.
	/// </summary>
	/// <param name="userImpersonationInfo"></param>
	public async Task AddAsync(UserImpersonationInfoDto userImpersonationInfo)
	{
		var jsonString = JsonSerializer.Serialize(userImpersonationInfo);
		
		await _redisAccessService.HashSetAsync(RedisConstants.UserImpersonationKey, userImpersonationInfo.ActorId.ToString(), jsonString);
	}
	
	/// <summary>
	/// Gets impersonation info-
	/// </summary>
	/// <param name="actorId"></param>
	/// <returns></returns>
	public async Task<UserImpersonationInfoDto?> GetAsync(Guid actorId)
	{
		var jsonString = await _redisAccessService.HashGetAsync(RedisConstants.UserImpersonationKey, actorId.ToString());
		
		if (jsonString.IsNullOrEmpty)
			return null;
		
		return JsonSerializer.Deserialize<UserImpersonationInfoDto>(jsonString!);
	}

	/// <summary>
	/// Tries to get an impersonation info.
	/// </summary>
	/// <param name="actorId"></param>
	/// <returns>A success flag and the impersonation info if retrieved successfully.</returns>
	public async Task<(bool Success, UserImpersonationInfoDto? UserImpersonationInfo)> TryGetAsync(Guid actorId)
	{
		var userImpersonationInfo = await GetAsync(actorId);

		if (userImpersonationInfo == null)
			return (false, userImpersonationInfo);

		return (true, userImpersonationInfo);
	}
	
	/// <summary>
    /// Removes impersonation info.
    /// </summary>
    /// <param name="actorId"></param>
	public async Task RemoveAsync(Guid actorId)
	{
		await _redisAccessService.HashDeleteAsync(RedisConstants.UserImpersonationKey, actorId.ToString());
	}
	
	#endregion
}