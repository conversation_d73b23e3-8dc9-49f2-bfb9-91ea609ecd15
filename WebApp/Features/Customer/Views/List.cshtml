@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("Customer", "");
	List<BaseColumnComponentTagHelper> columns =
	[
		new ListColumnComponentTagHelper()
		{
			Type = ListColumnType.Flag,
			Name = "enabled",
			WithConverter = true
		},
		new ListDataColumnComponentTagHelper()
		{
			Name = "displayName",
		},

		new ListColumnComponentTagHelper()
		{
			Type = ListColumnType.OptionMenu,
			Name = "options",
			WithConverter = true
		}
	];
	
	List<DropdownMenuItemComponentTagHelper> activeColumns =
	[
		new()
		{
			Action = "Sync",
			Label = localizer["sync"],
			IconLeft = "rotate",
			ClickFunction = "syncCustomer"
		},
		new()
		{
			Action = "Deactivate",
			Label = localizer["deactivate"],
			IconLeft = "power-off",
			ClickFunction = "deactivateCustomer"
		},
		new()
		{
			Action = "Delete",
			Label = localizer["delete"],
			IconLeft = "trash",
			IconLeftColor = "var(--clr-signal-error)",
			ClickFunction = "deleteCustomer"
		}
	];
	
	List<DropdownMenuItemComponentTagHelper> inactiveColumns =
	[
		new()
		{
			Action = "Activate",
			Label = localizer["reactivate"],
			IconLeft = "power-off",
			ClickFunction = "activateCustomer"
		},
		new()
		{
			Action = "Delete",
			Label = localizer["delete"],
			IconLeft = "trash",
			IconLeftColor = "var(--clr-signal-error)",
			ClickFunction = "deleteCustomer"
		}
	];
	
	List<QueryParamSortingDto> sortings =
	[
		new() { OrderColumn = "DisplayName", Direction = SortDirection.Asc }
	];
}
<style>
	div.dialog-main-content {
		margin: var(--size-spacing-l);
	}
</style>
<script type="module" defer>
	Page.setTitle('@localizer["list/pageTitle"]')
	Page.setMainPage('/Admin/Customers')
	Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: Page.getMainPageUrl() }])

	Page.buttonConfig.syncAll = true
	Page.buttonConfig.syncAllButton.addEventListener('click', async () => {
		const toaster = document.getElementById('toaster')
		const response = await fetch(`/Api/Customers/SyncAllUsers`, { method: 'POST' })
		const json = await response.json()
		const responseData = json.data;
		toaster.notify({heading: responseData.heading ?? null, type: (responseData.successCounter === responseData.totalCount ? 'success' : 'info'), content: `${responseData.message}`})
	}, { signal: Page.getPageChangeSignal() })
	Page.buttonConfig.syncAllButton.skeleton = false

	const syncDialog = document.querySelector('lvl-dialog[name=customer-sync-dialog]')
	const dialogContent = syncDialog.querySelector('lvl-checkbox-group')

	Page.buttonConfig.import = true
	Page.buttonConfig.importButton.addEventListener('click', async () => {
		dialogContent.innerHTML = ""
		const json = await Page.getJSON(`/Api/Customers/SyncPreview`)

		for (const [key, value] of Object.entries(json.data)) {
			dialogContent.insertAdjacentHTML('beforeend', `<lvl-checkbox value="${key}">${value}</lvl-checkbox>`)
		}

		syncDialog.open = true
	})
	Page.buttonConfig.importButton.skeleton = false

	syncDialog.querySelector('.dialog-button--primary').addEventListener('click', async () => {
		const enumeration = document.getElementById('customer-list')
		const customerIds = dialogContent.value
		const toaster = document.getElementById('toaster')
		let formData = new FormData();
		formData.append('customerIds', customerIds)
		const response = await fetch(`/Api/Customers/Sync/Batch`, { 
			method: 'POST',
			body: formData
		})
		const json = await response.json()
		const responseData = json.data

		syncDialog.open = false
		enumeration.reload()
		toaster.notify({heading: responseData.heading ?? null, type: (responseData.successCounter === responseData.totalCount ? 'success' : 'info'), content: `${responseData.message}`})
	});

	syncDialog.querySelector('.dialog-button--tertiary').addEventListener('click', async () => {
		syncDialog.open = false
	});

	const renderCustomerIcon = (data) => {
		return `<i class="fa-light fa-building"></i>`
	}

	const renderOptionMenu = (data) => {
		let optionMenuHtml
		if (data.enabled) {
			optionMenuHtml = `
				<lvl-menu class="filter-menu">
					@foreach (var option in activeColumns)
					{
						@: @Html.Raw(option.CreateTagHelperOutput())
					}
				</lvl-menu>`
		} else {
			optionMenuHtml = `
				<lvl-menu class="filter-menu">
					@foreach (var option in inactiveColumns)
					{
						@: @Html.Raw(option.CreateTagHelperOutput())
					}
				</lvl-menu>`
		}
		return optionMenuHtml
	}

	document.querySelector('lvl-list-column[name="enabled"]').converter = renderCustomerIcon
	document.querySelector('lvl-list-column[name="options"]').converter = renderOptionMenu

</script>
<vc:admin-list-page entity="Customer" route-name="Customers" localizer="@localizer" display-property-name="displayName" columns="@columns"
                    sorting="@sortings" open-on-row-click="true" active-column="enabled"></vc:admin-list-page>
<vc:create-panel entity="Customer" route-name="Customers" localizer="@localizer"></vc:create-panel>
@* @TODO: add searchbar to header and implement filtering *@
<lvl-dialog name="customer-sync-dialog" heading="@localizer["syncDialog/heading"]" icon="cloud-arrow-down" modal="true" style="display:none;" custom-keydown-handler>
	<div class="dialog-main-content">
		<checkbox-group-component name="customer-sync-values" max-columns="2"></checkbox-group-component>
	</div>
	<button-component slot="button-left" class="dialog-button--tertiary" type="ButtonType.Tertiary" label="@localizer["syncDialog/abort"]"></button-component>
	<button-component slot="button-right" class="dialog-button--primary" type="ButtonType.Primary" label="@localizer["syncDialog/synchronize"]"></button-component>
</lvl-dialog>

<script>
	async function syncCustomer(event) {
		const listLine = this.closest('lvl-list-line')
		const enumeration = document.getElementById('customer-list')

		const list = enumeration.querySelector('lvl-list')

		const position = Number(listLine.dataset['position'])
		const currentRow = list.rows[position]

		this.closest('lvl-dropdown')?.removeAttribute('open')

		const response = await fetch(`/Api/Customers/${currentRow.id}/SyncUsers`, { method: 'POST' })
		if (response.ok)
			enumeration.reload()
	}

	async function activateCustomer(event) {
		const listLine = event.target.closest('lvl-list-line')
		const enumeration = document.getElementById('customer-list')

		const list = enumeration.querySelector('lvl-list')

		const position = Number(listLine.dataset['position'])
		const currentRow = list.rows[position]

		this.closest('lvl-dropdown')?.removeAttribute('open')

		listLine.active = false
		const response = await fetch(`/Api/Customers/${currentRow.id}/Activate`, { method: 'POST' })
		if (response.ok)
			enumeration.reload() // -> change to list line reload	
		else
			listLine.active = true
	}

	async function deactivateCustomer(event) {
		const listLine = event.target.closest('lvl-list-line')
		const enumeration = document.getElementById('customer-list')

		const list = enumeration.querySelector('lvl-list')

		const position = Number(listLine.dataset['position'])
		const currentRow = list.rows[position]

		this.closest('lvl-dropdown')?.removeAttribute('open')

		const response = await fetch(`/Api/Customers/${currentRow.id}/Deactivate`, { method: 'POST' })
		if (response.ok)
			enumeration.reload() // -> only reload listLine, when implemented
	}

	async function deleteCustomer(event) {
		const listLine = this.closest('lvl-list-line')
		const columnEnumeration = document.getElementById('customer-list')
		const columnList = columnEnumeration.querySelector('lvl-list')
		if (!listLine || listLine.tagName !== 'LVL-LIST-LINE'){
			console.error('List line not found. Column cannot be removed')
			return
		}

		const position = Number(listLine.dataset['position'])
		const currentRow = columnList.rows[position]
		
		const callback = async (elementId) => {
			Overlay.showWait("@localizer["deleting"]")
			const response = await fetch(`/Api/Customers/${elementId}`, { method: 'DELETE' })
			if (response.ok)
				columnEnumeration.reload() // -> only reload listLine, when implemented
			Overlay.hideWait()
		}	
		
		Page.showDeletionWarningToast('@localizer["deletionWarningHeading"]', '@localizer["deletionWarning"]', async () => await callback(currentRow.id), '@localizer["ok"]', '@localizer["abort"]')
	}
</script>