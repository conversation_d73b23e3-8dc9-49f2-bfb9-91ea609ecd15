using System.Text.Json;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.DataField.Controllers;

/// <summary>
/// Controller for the configuration of data field columns
/// </summary>
public class DataFieldColumnController : AdminController<DataFieldColumnDto>
{
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="versionReader">injected VersionReader</param>
	public DataFieldColumnController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : 
		base(logManager, logManager.GetLoggerForClass<DataFieldColumnController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		
	}
	
	#region Actions
	
	/// <inheritdoc />
	[HttpGet("/Api/DataFieldColumns/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		try
		{
			var query = DatabaseContext.DataFieldColumns
				.Include(column => column.DisplayField);
			
			var queryResult = GetQueryResult<DataFieldColumnEntity, DataFieldColumnDto>(query, parameters);
			
			// check for cols without position
			var defectCols = queryResult.Rows.Where(sorting => sorting.Position == null).ToList();
			if (defectCols.Count > 0)
			{
				ReorderSortings(defectCols.FirstOrDefault()!.DataFieldId);
				
				// select again
				queryResult = GetQueryResult<DataFieldColumnEntity, DataFieldColumnDto>(query, parameters);
			}
			
			var json = JsonSerializer.SerializeToElement(queryResult, HeaderSerializer);
			return GetOkResponse(ServerResponsePayload.FromJson(json));
		}
		catch (Exception e)
		{
			var message = $"{nameof(DataFieldColumnEntity)} list could not be loaded.";
			Logger.Error(e, "{Message} Stacktrace: {Exception}", message, e);
			
			return GetBadRequestResponse(message);
		}
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/DataFieldColumns/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<DataFieldColumnEntity, DataFieldColumnDto>(DatabaseContext.DataFieldColumns, id);
	}
	
	/// <inheritdoc />
	[HttpPost("/Api/DataFieldColumns/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] DataFieldColumnDto configurationDto)
	{
		// calculate position
		configurationDto.Position = await DatabaseContext.DataFieldColumns.CountAsync(column => column.DataFieldId == configurationDto.DataFieldId) + 1;
		
		if (await DatabaseContext.DataFieldColumns.AnyAsync(entity => entity.DataFieldId == configurationDto.DataFieldId && entity.DisplayFieldId == configurationDto.DataFieldId))
			return GetBadRequestResponse(
				$"Column for field {configurationDto.DataFieldId} with display field {configurationDto.DataFieldId} already exists");
		
		// Touch DataField
		var field = await DatabaseContext.DataFields
			.Include(dataField => dataField.DataSource)
			.FirstOrDefaultAsync(dataField => dataField.Id == configurationDto.DataFieldId);
		field?.Touch();
		
		return await HandleCreateRequestAsync(DatabaseContext.DataFieldColumns, configurationDto);
	}
	
	/// <inheritdoc />
	[HttpPatch("/Api/DataFieldColumns/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] DataFieldColumnDto configurationDto)
	{
		var entity = await DatabaseContext.DataFieldColumns
			.Include(column => column.DataField)
			.FirstOrDefaultAsync(column => column.Id == id);
		var oldPosition = entity?.Position;
		var result = await HandleUpdateRequestAsync(DatabaseContext.DataFieldColumns, id, configurationDto);
		if (entity != null && result.Result is OkObjectResult)
		{
			// Touch DataField
			entity.DataField?.Touch();

			// Reorder remaining columns
			if (configurationDto.Position != null && oldPosition != configurationDto.Position)
				ReorderSortings(entity.DataFieldId, entity.Id, oldPosition, configurationDto.Position);
		}

		return result;
	}
	
	/// <inheritdoc />
	[HttpDelete("/Api/DataFieldColumns/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		var entity = DatabaseContext.DataFieldColumns
			.Include(column => column.DataField)
			.FirstOrDefault(column => column.Id == id);
		var result = HandleDeleteRequest(DatabaseContext.DataFieldColumns, id);
		if (entity != null && result.Result is OkObjectResult)
		{
			// Touch DataField
			entity.DataField?.Touch();
			
			// Reorder remaining columns
			ReorderSortings(entity.DataFieldId);
		}
		return result;
	}
	
	#endregion

	private void ReorderSortings(Guid dataFieldId, Guid? updatedElementId = null, int? oldPosition = null, int? newPosition = null)
	{
		var queryable = DatabaseContext.DataFieldColumns.Where(column => column.DataFieldId == dataFieldId).OrderBy(column => column.Position);
		
		// if a specific element position gets updated, this position comes first!
		if (updatedElementId != null)
		{
			var changedElementFirst = oldPosition > newPosition;
			queryable = queryable.ThenBy(sorting => sorting.Id == updatedElementId.Value ? changedElementFirst ? 0 : 2 : 1);
		}

		var columns = queryable.ToList();
		foreach (var (sorting, index) in columns.Select((item, index) => (item, index)))
		{
			sorting.Position = index+1;
		}
		DatabaseContext.SaveChanges();
	}
}