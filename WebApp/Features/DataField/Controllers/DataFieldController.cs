using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Frontend.WebApp.Features.DataField.ViewModels;
using Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.DataField.Controllers;

/// <summary>
/// Controller for the configuration view of data fields
/// </summary>
[Route("[controller]")]
public class DataFieldController : AdminController<DataFieldDto>
{
	/// <inheritdoc />
	public DataFieldController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
							   IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) :
		base(logManager, logManager.GetLoggerForClass<DataFieldController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
	}
	
	#region Views

	/// <summary>
	/// Renders the detail view with help of the data field dto
	/// </summary>
	/// <param name="dataStoreSlug">readable identifier for a specific data store</param>
	/// <param name="dataSourceSlug">readable identifier for a specific data source</param>
	/// <param name="slug">readable identifier for a specific data field</param>
	/// <param name="dataFieldType">identifier for the type of the data field (DataField, LookupField or VirtualField</param>
	/// <param name="dataType">identifier for the data-type of the field</param>
	/// <param name="inputDataType">identifier for the type of the input</param>
	/// <param name="systemField">is the field a systemField?</param>
	/// <param name="autoGenerated">is the field a auto generated field?</param>
	/// <param name="multi">is the field a multi value field?</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataFields/Edit")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/DataSources/{dataSourceSlug}/{menu}/{slug}")]
	public IActionResult Detail(string dataStoreSlug, string dataSourceSlug, string slug, [FromQuery(Name = "fieldType")] DataFieldType? dataFieldType,
								[FromQuery(Name = "dataType")] DataType? dataType, [FromQuery(Name= "inputDataType")] InputDataType? inputDataType, 
								[FromQuery(Name = "systemField")] bool systemField = false, [FromQuery(Name = "autoGenerated")] bool autoGenerated = false,
								[FromQuery(Name = "multi")] bool multi = false)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(dataSourceSlug) || string.IsNullOrEmpty(slug))
		{
			return CachedPartial() ?? RenderPartial(new DataFieldForm(ViewType.Edit) { DataFieldType = dataFieldType, DataType = dataType,
														SystemField = systemField, AutoGenerated = autoGenerated, Multi = multi, InputDataType = inputDataType});
		}
		
		var dataStoreId = DatabaseContext.DataStoreConfigs.FirstOrDefault(config => config.Slug == dataStoreSlug)?.Id;
		var dataSourceDto = DatabaseContext.DataSources.FirstOrDefault(source => source.DataStoreId == dataStoreId && source.Slug == dataSourceSlug)?.ToDto();
		var dataSourceId = dataSourceDto?.Id;
		
		DataFieldEntity? dataField =
			DatabaseContext.DataFields.Include(config => config.DataSource)
				.Include(fields => fields.LookupSource)
				.Include(fields => fields.LookupDisplayField)
				.Include(fields => fields.VirtualLookupField)
				.Include(fields => fields.VirtualDataField)
				.FirstOrDefault(config => config.Slug == slug.ToLower() && config.DataSourceId == dataSourceId);
		if (dataField == null)
			throw new ElementNotFoundException($"DataField configuration with slug: {slug} could not be found");
		
		return RenderPartial(new DataFieldForm(ViewType.Edit)
		{
			DataField = dataField.ToDto(),
			DataFieldType = dataField.FieldType,
			DataType = dataField.Type,
			SystemField = dataField.SystemField,
			Multi = dataField.Multi,
		}, "~/Features/DataSource/Views/Detail.cshtml", new DataSourceForm(ViewType.Edit)
		{
			DataSource = dataSourceDto
		});
	}
	
	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataFields/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/DataSources/{dataSourceSlug}/{menu}/Create")]
	public IActionResult Create(string dataStoreSlug, string dataSourceSlug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(dataSourceSlug))
		{
			return CachedPartial() ?? RenderPartial(new DataFieldForm());
		}
		
		var dataStoreId = DatabaseContext.DataStoreConfigs.FirstOrDefault(config => config.Slug == dataStoreSlug)?.Id;
		var dataSource = DatabaseContext.DataSources.FirstOrDefault(source => source.DataStoreId == dataStoreId && source.Slug == dataSourceSlug.ToLower())?.ToDto();
		return RenderPartial(new DataFieldForm()
		{
			DataField = new DataFieldDto() { DataSourceId = dataSource?.Id }
		}, "~/Features/DataSource/Views/Detail.cshtml", new DataSourceForm(ViewType.Edit)
		{
			DataSource = dataSource
		});
	}
	
	#endregion
	
	#region Actions
	
	/// <inheritdoc />
	[HttpGet("/Api/DataFields/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		return HandleQueryRequest<DataFieldEntity, DataFieldDto>(DatabaseContext.DataFields, parameters);
	}
	
	/// <summary>
	/// Returns a mutated list of data fields which can be referenced by a virtual data field as JSON.
	/// </summary>
	/// <param name="lookupFieldId">readable identifier for a specific lookup data field used for determining the available fields</param>
	/// <param name="virtualFieldId">readable identifier for a specific lookup or virtual data field that is excluded from the available fields</param>
	/// <param name="parameters">an object to query a Subset of data fields</param>
	[HttpGet("/Api/DataFields/{lookupFieldId}/LookupableFields")]
	[HttpGet("/Api/DataFields/{lookupFieldId}/LookupableFields/{virtualFieldId}")]
	public ActionResult<FrontendResponse> LookupableFields(Guid lookupFieldId, Guid virtualFieldId, [FromQuery] QueryParamsDto parameters)
	{
		var lookupField = DatabaseContext.DataFields.Find(lookupFieldId);
		if (lookupField == null || lookupField.FieldType != DataFieldType.LookupField)
			return GetNotFoundResponse($"Lookup Data Field #{lookupFieldId} does not exist");

		var exclusionList = new List<Guid>();
		if (virtualFieldId != Guid.Empty)
		{
			var virtualField = DatabaseContext.DataFields.Find(virtualFieldId);
			if (virtualField != null && virtualField.FieldType == DataFieldType.VirtualField)
			{
				if(virtualField.ReferencingVirtualFieldIds != null)
					exclusionList.AddRange(virtualField.ReferencingVirtualFieldIds);
				exclusionList.Add(virtualFieldId);
			}
		}
		
		
		var query = DatabaseContext.DataFields.Include(field => field.DataSource).ThenInclude(source => source!.DataStore).Where(dataField => dataField.DataSourceId == lookupField.LookupSourceId &&
																  !dataField.SystemField && !exclusionList.Contains(dataField.Id));
		
		return HandleAutocompleteRequest(query, parameters,
										 nameof(DataFieldEntity.Id),
										 nameof(DataFieldEntity.Name),
										 new PropertyPathList<DataFieldEntity>()
										 {
											 nameof(DataFieldEntity.Name), 
											 nameof(DataFieldEntity.Type),	 
											 nameof(DataFieldEntity.Slug),
											 (DataFieldEntity field) => field.DataSource!.Slug,
											 (DataFieldEntity field) => field.DataSource!.DataStore.Slug,
										 });
	}
	
	/// <summary>
	/// Returns a mutated list of data fields which can be used as additional display column
	/// </summary>
	/// <param name="id">data field id</param>
	/// <param name="parameters">an object to query a Subset of data fields</param>
	[HttpGet("/Api/DataFields/{id:guid}/LookupFields")]
	public ActionResult<FrontendResponse> LookupFields(Guid id, QueryParamsDto parameters)
	{
		var dataField = DatabaseContext.DataFields.Include(field => field.Columns)
			.FirstOrDefault(field => field.Id == id);
		
		if (dataField == null || dataField.FieldType != DataFieldType.LookupField)
			return GetNotFoundResponse($"Data Field #{dataField} does not exist or is not a lookup field");
		
		// collect currently occupied field id's
		var currentFieldIds = dataField.Columns.Select(field => field.DisplayFieldId);
		var query = DatabaseContext.DataFields.Where(field => field.DataSourceId == dataField.LookupSourceId && !currentFieldIds.Contains(field.Id));
		return HandleAutocompleteRequest(query, parameters,
										 nameof(DataFieldEntity.Id),
										 nameof(DataFieldEntity.Name),
										 new PropertyPathList<DataFieldEntity>()
										 {
											 nameof(DataFieldEntity.Name), 
											 nameof(DataFieldEntity.Type)
										 });
	}
	
	/// <summary>
	/// Returns all currently configured display fields for this data field
	/// </summary>
	/// <param name="id">data field id</param>
	/// <param name="parameters">an object to query a Subset of data fields</param>
	[HttpGet("/Api/DataFields/{id:guid}/DisplayFields")]
	public ActionResult<FrontendResponse> DisplayFields(Guid id, QueryParamsDto parameters)
	{
		var dataField = DatabaseContext.DataFields.Include(field => field.Columns)
			.FirstOrDefault(field => field.Id == id);
		
		if (dataField == null || dataField.FieldType != DataFieldType.LookupField)
			return GetNotFoundResponse($"Data Field #{dataField} does not exist or is not a lookup field");
		
		// collect currently occupied field id's
		var currentFieldIds = dataField.Columns.Select(field => field.Id);
		
		var query = DatabaseContext.DataFields.Where(field => field.DataSourceId == dataField.LookupSourceId &&
															  field.FieldType == DataFieldType.DataField && !field.SystemField &&
															  !currentFieldIds.Contains(field.Id) 
			//&& !field.Name.StartsWith("Feld") && !field.Name.StartsWith("Sys")
		);
		return HandleAutocompleteRequest(query, parameters,
										 nameof(DataFieldEntity.Id),
										 nameof(DataFieldEntity.Name),
										 new PropertyPathList<DataFieldEntity>()
										 {
											 nameof(DataFieldEntity.Name), 
											 nameof(DataFieldEntity.Type)
										 });
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/DataFields/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		var dataFields = DatabaseContext.DataFields
			.Include(fields => fields.LookupSource)
			.Include(fields => fields.LookupDisplayField)
			.Include(fields => fields.VirtualDataField)
			.Include(fields => fields.VirtualLookupField);
		return HandleGetRequest<DataFieldEntity, DataFieldDto>(dataFields, id);
	}
	
	/// <inheritdoc />
	[HttpPost("/Api/DataFields/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] DataFieldDto configurationDto)
	{
		if (configurationDto.FieldType == DataFieldType.VirtualField)
		{
			return await HandleCreateRequestAsync(DatabaseContext.DataFields, configurationDto, afterSaveAction: UpdateReferencingFields);
			
			void UpdateReferencingFields(DataFieldEntity entity)
			{
				entity.LoadVirtualData(DatabaseContext);
				entity.PushReferencingVirtualIds();
				entity.BuildVirtualDataStoreQueryName();
				DatabaseContext.SaveChanges();
			}
		}
		
		return await HandleCreateRequestAsync(DatabaseContext.DataFields, configurationDto, CreateAction);

		void CreateAction(DataFieldEntity entity)
		{
			var storageEntity = entity.CreateField();
			if(!entity.Unique)
				entity.DefaultValue ??= storageEntity.DefaultValue?.ToString();
			DatabaseContext.SaveChanges();
			DatabaseContext.DataSources.Find(entity.DataSourceId)?.Touch();
		}
	}
	
	/// <inheritdoc />
	[HttpPatch("/Api/DataFields/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] DataFieldDto configurationDto)
	{
		DataFieldEntity? originalInstance = null;
		switch (configurationDto.FieldType)
		{
			case DataFieldType.DataField:
				originalInstance = await DatabaseContext.DataFields.FindAsync(id);
				break;
			case DataFieldType.LookupField:
				originalInstance = await DatabaseContext.DataFields
					.Include(fields => fields.LookupSource)
					.Include(fields => fields.LookupDisplayField)
					.FirstOrDefaultAsync(field => field.Id == id);
				break;
			case DataFieldType.VirtualField:
				originalInstance = await DatabaseContext.DataFields
					.Include(fields => fields.VirtualDataField)
					.Include(fields => fields.VirtualLookupField)
					.FirstOrDefaultAsync(field => field.Id == id);
				break;
		}
		
		if (originalInstance == null)
			return GetNotFoundResponse($"DataField#{id} does not exist");
		
		var originalName = originalInstance.Name;
		var originalVirtualLookupFieldId = originalInstance.VirtualLookupFieldId;
		var originalVirtualDataFieldId = originalInstance.VirtualDataFieldId;
		var originalLookupDisplayFieldId = originalInstance.LookupDisplayFieldId;
		
		if (configurationDto.FieldType == DataFieldType.VirtualField)
		{
			var hasNotChanged = configurationDto.VirtualDataFieldId == originalVirtualDataFieldId &&
							 configurationDto.VirtualLookupFieldId == originalVirtualLookupFieldId;
			return await HandleUpdateRequestAsync(DatabaseContext.DataFields, originalInstance, configurationDto, LoadUpdateReferenceAction);
			
			void LoadUpdateReferenceAction(DataFieldEntity entity)
			{
				if(hasNotChanged)
					return;
				
				entity.LoadVirtualData(DatabaseContext, true);
				originalInstance.RemoveReferencingVirtualIds();
				entity.PushReferencingVirtualIds();
				entity.SetVirtualData(entity.GetVirtualDataField(DatabaseContext), true);
				entity.ForwardVirtualDataFieldChanges(DatabaseContext);
			}
		}

		return await HandleUpdateRequestAsync(DatabaseContext.DataFields, originalInstance, configurationDto, UpdateAction);

		void UpdateAction(DataFieldEntity entity)
		{
			var nameHasChanged = entity.Name != originalName;
			if (nameHasChanged)
				entity.RenameField(originalName, entity.Name);
			
			var storageEntity = entity.UpdateField();
			// save in case default-value was set to null
			DatabaseContext.SaveChanges();
			if(!entity.Unique)
				entity.DefaultValue ??= storageEntity.DefaultValue?.ToString();
			entity.LoadLookupData(DatabaseContext);
			entity.ForwardVirtualDataFieldChanges(DatabaseContext, nameHasChanged || entity.LookupDisplayFieldId != originalLookupDisplayFieldId);
			DatabaseContext.DataSources.Find(entity.DataSourceId)?.Touch();
		}
	}

	/// <inheritdoc />
	[HttpDelete("/Api/DataFields/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		var dataField = DatabaseContext.DataFields.Find(id);
		if (dataField?.FieldType == DataFieldType.VirtualField)
		{
			dataField.LoadVirtualData(DatabaseContext);
			return HandleDeleteRequest(DatabaseContext.DataFields, id, RemoveReferencingFields);
			
			void RemoveReferencingFields(DataFieldEntity entity)
			{
				dataField.RemoveReferencingVirtualIds();
				dataField.SetVirtualData(null);
				dataField.ForwardVirtualDataFieldChanges(DatabaseContext);
			}
		}
		
		return HandleDeleteRequest(DatabaseContext.DataFields, id, DeleteAction);

		void DeleteAction(DataFieldEntity entity)
		{
			List<DataFieldEntity> referencingFields = new();
			switch (entity.FieldType)
			{
				case DataFieldType.DataField:
					referencingFields = DatabaseContext.DataFields.Where(field => field.VirtualDataFieldId == entity.Id || field.LookupDisplayFieldId == entity.Id).ToList();
					break;
				case DataFieldType.LookupField:
					referencingFields = DatabaseContext.DataFields.Where(field => field.VirtualLookupFieldId == entity.Id || field.VirtualDataFieldId == entity.Id).ToList();
					break;
			}
			referencingFields.ForEach(referencingField =>
			{
				if (referencingField.FieldType == DataFieldType.LookupField)
					referencingField.LookupDisplayFieldId = null;

				referencingField.SetVirtualData(null);
				referencingField.ForwardVirtualDataFieldChanges(DatabaseContext);
			});
			
			entity.RemoveField();
			DatabaseContext.DataSources.Find(entity.DataSourceId)?.Touch();
		}
	}
	
	#endregion
}