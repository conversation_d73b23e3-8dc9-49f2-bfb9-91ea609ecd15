{"detail": {"de": {"name": "Name", "dataSourceName": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Kommentar", "type": "<PERSON><PERSON>"}, "en": {"name": "Name", "dataSourceName": "Data source", "comment": "Comment", "type": "Type"}, "tab1": {"de": {"label": "Allgemein"}, "en": {"label": "General"}}, "tab2": {"de": {"label": "<PERSON><PERSON>", "description": "Das Feld ist in folgenden Masken enthalten.", "Type": "<PERSON><PERSON>"}, "en": {"label": "Masks", "description": "The field exists in the following masks.", "Type": "Type"}}, "tab3": {"de": {"label": "Daten-<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> dieses Feld wird aus folgenden Datenquellen zugegriffen.", "Module": "<PERSON><PERSON><PERSON>", "DataSource": "<PERSON><PERSON><PERSON><PERSON>", "DataField": "<PERSON><PERSON><PERSON>"}, "en": {"label": "Data Access", "description": "This field is accessed from the following data sources.", "Module": "<PERSON><PERSON><PERSON>", "DataSource": "Data Source", "DataField": "Data Field"}}}, "list": {"de": {"newItem": "<PERSON><PERSON><PERSON>"}, "en": {"newItem": "New Data Field"}}, "de": {"Name": "Name", "DataType": "Datentyp", "FieldType": "Feldtyp", "InputDataType": "Eingabetyp", "Length": "Zei<PERSON>länge", "DecimalPlaces": "Nachkommastellen", "RichText": "Rich-Text", "Mandatory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Unique": "Eindeutig", "Multi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LookupSource": "Referenztabelle (NST)", "LookupDisplayField": "referenziertes Anzeigefeld", "VirtualLookupField": "<PERSON><PERSON><PERSON><PERSON>feld", "VirtualDataField": "referenziertes Anzeigefeld", "Nullable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SystemField": "<PERSON><PERSON>", "AutoGenerated": "Automatisch erzeugt", "columnView": "Mehrspaltige Ansicht", "filterSelf": "Selbstrefrenzierung verhindern", "addColumn": "Spalte hinzufügen", "additionalColumns": "Zusätzliche Anzeigespalten", "additionalColumnLegend": "Die hier konfigurierten Spalten werden als Zusatzinformationen innerhalb von Auswahlfeldern angezeigt.", "DefaultValue": "Vorgabewert", "Sign": "Einheit", "sectionFilters": "Filterbedingungen", "formatType": "Format", "onOff": "An/Aus", "yesNo": "<PERSON><PERSON>/<PERSON><PERSON>", "customText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "Standard", "labelTrue": "Ja-<PERSON><PERSON>", "labelFalse": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "en": {"Name": "Name", "DataType": "Data Type", "FieldType": "Field Type", "InputDataType": "Input type", "Length": "Character Length", "DecimalPlaces": "Decimal Places", "RichText": "Rich Text", "Mandatory": "Mandatory", "Unique": "Unique", "Multi": "Multivalued Field", "LookupSource": "Reference Table", "LookupDisplayField": "Reference Display Field", "VirtualLookupField": "Reference Field", "VirtualDataField": "Reference Display Field", "Nullable": "Nullable Values", "SystemField": "System Field", "AutoGenerated": "Automatically generated", "columnView": "Multi-Column View", "filterSelf": "Prevent self referencing", "addColumn": "Add Column", "additionalColumns": "Additional Display Columns", "additionalColumnLegend": "The columns configured here are displayed as additional information within selection fields.", "DefaultValue": "Default Value", "Sign": "Unit symbol", "sectionFilters": "Filter conditions", "formatType": "Format", "onOff": "On/Off", "yesNo": "Yes/No", "customText": "Custom", "default": "<PERSON><PERSON><PERSON>", "labelTrue": "Yes value", "labelFalse": "No value"}}