@using Humanizer
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Microsoft.OpenApi.Extensions
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@model Levelbuild.Frontend.WebApp.Features.DataField.ViewModels.DataFieldForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("DataField", "detail");
	var typeLocalizer = LocalizerFactory.Create("DataFieldType", "");
	var inputTypeLocalizer = LocalizerFactory.Create("InputDataType", "");
	var scriptLocalizer = LocalizerFactory.Create("ScriptMessages", "");
}

<script type="module" defer>
	const form = document.getElementById("data-field-detail-form")

	const lookupSource = form.querySelector('[name="lookupSource"]')
	const lookupDisplayField = form.querySelector('[name="lookupDisplayField"]')
	const virtualLookupField = form.querySelector('[name="virtualLookupField"]')
	const virtualDataField = form.querySelector('[name="virtualDataField"]')
	const systemField = form.querySelector('[name="systemField"]')
	const autoGenerated = form.querySelector('[name="autoGenerate"]')

	await Component.waitForComponentInitialization('lvl-autocomplete')
	await Component.waitForComponentInitialization(systemField)
	await Component.waitForComponentInitialization(autoGenerated)

	//set urls of autocomplete components
	const dataStoreId = Page.getFormData()["dataStoreId"]
	const dataSourceId = Page.getFormData()["id"]
	const lookupUrl = `/Api/DataStores/${dataStoreId}/DataSources`
	lookupSource.url = lookupUrl
	const virtualUrl = `/Api/DataSources/${dataSourceId}/LookupFields`
	virtualLookupField.url = virtualUrl
	
	const showIfMapping = new Map()
	showIfMapping.set("type", [[{option: "fieldType", compareValue: "DataField"}]]);
	showIfMapping.set("inputDataType", [[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "String"}]]);
	showIfMapping.set("mandatory", [[{option: "fieldType", compareValue: "DataField"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}], [{option: "fieldType", compareValue: "LookupField"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}]]);
	showIfMapping.set("unique", [[{option: "fieldType", compareValue: "DataField"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}], [{option: "fieldType", compareValue: "LookupField"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}]]);
	showIfMapping.set("multi", [[{option: "fieldType", compareValue: "DataField"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}]]);
	showIfMapping.set("length", [[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "String"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}]]);
	showIfMapping.set("decimalPlaces", [[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "Double"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}]]);
	showIfMapping.set("formatType", [[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "Boolean"}]]);
	showIfMapping.set("labelTrue", [[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "Boolean"}, {option: "formatType", compareValue: "CustomText"}]]);
	showIfMapping.set("labelFalse", [[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "Boolean"}, {option: "formatType", compareValue: "CustomText"}]]);
	showIfMapping.set("richText", [[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "Text"}]]);
	showIfMapping.set("lookupSource", [[{option: "fieldType", compareValue: "LookupField"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}]]);
	showIfMapping.set("lookupDisplayField", [[{option: "fieldType", compareValue: "LookupField"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}]]);
	showIfMapping.set("virtualLookupField", [[{option: "fieldType", compareValue: "VirtualField"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}]]);
	showIfMapping.set("virtualDataField", [[{option: "fieldType", compareValue: "VirtualField"}, {option: "systemField", compareValue: false}, {option: "autoGenerated", compareValue: false}]]);
	showIfMapping.set("systemField", [[{option: "systemField", compareValue: true}]]);
	showIfMapping.set("autoGenerated", [[{option: "autoGenerated", compareValue: true}]]);
	showIfMapping.set("sign", [
		[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "Integer"}],
		[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "Long"}],
		[{option: "fieldType", compareValue: "DataField"}, {option: "type", compareValue: "Double"}]
	]);
	@if (Model.DataFieldType == DataFieldType.LookupField)
	{
		<text>
			showIfMapping.set("filterSelf", [[{option: "lookupSource", compareValue: dataSourceId}]])
		</text>
	}	

	Page.handleConditionalVisibility(form, showIfMapping)
	
	//fill in autocomplete values on refresh
	@if (Model.DataField != null)
	{
		if (Model.DataFieldType == DataFieldType.LookupField)
		{
			<text>
				let lookupSourceData = {id:"@(Model.DataField.LookupSource?.Id)", name: "@(Model.DataField.LookupSource?.Name)"}
				lookupSource.value = lookupSourceData
				lookupSource.updateResetMarker()
				let lookupDisplayFieldData = {id:"@(Model.DataField.LookupDisplayField?.Id)", name: "@(Model.DataField.LookupDisplayField?.Name)"}
				lookupDisplayField.value = lookupDisplayFieldData
				lookupDisplayField.updateResetMarker()
			</text>
		}

		if (Model.DataFieldType == DataFieldType.VirtualField)
		{
			<text>
				let virtualLookupFieldData = {id:"@(Model.DataField.VirtualLookupField?.Id)", name: "@(Model.DataField.VirtualLookupField?.Name)"}
				virtualLookupField.value = virtualLookupFieldData
				virtualLookupField.updateResetMarker()
				let virtualDataFieldData = {id:"@(Model.DataField.VirtualDataField?.Id)", name: "@(Model.DataField.VirtualDataField?.Name)"}
				virtualDataField.value = virtualDataFieldData
				virtualDataField.updateResetMarker()
			</text>
		}
	}
	
	//add delete button functionality
	const fieldList = document.getElementById('data-field-list')
	const deleteButton = document.getElementById("data-field-delete-button")
	deleteButton?.addEventListener('click', () => Form.handleDeleteButtonClick(form, '/Api/DataFields/', fieldList, true), { signal: Page.getPageChangeSignal() })
</script>
@{
	var useSkeleton = Model is { ViewType: ViewType.Edit, DataField: null };
}
<tab-bar-component skeleton="@(useSkeleton)">
	<tab-component label="@(localizer["tab1/label"])">
		<form-component class="form" skeleton="@(useSkeleton)" id="data-field-detail-form">
			<config-section label="@localizer["tab1/label"]">
				<div class="form__item">
					<input type="hidden" class="item__value" id="data-field-id" name="id" value="@(Model.DataField?.Id)"/>
				</div>
				<div class="form__item">
					<config-label target="data-field-name" label="@localizer["Name"]"></config-label>
					<input-component id="data-field-name" name="name" class="item__value" type="InputDataType.Translation" translation-prefix="/DataField" 
					                 placeholder="@localizer["pleaseEnter"]" readonly="@(Model.SystemField || Model.AutoGenerated)"
					                 value="@(Model.DataField?.Name ?? "")" validator="Validator.StorageFieldReserved" required></input-component>
				</div>
				<div class="form__item block__item">
					<config-label target="field-type" label="@localizer["FieldType"]"></config-label>
					<button-group-component class="item__value" id="field-type" name="fieldType" readonly="true" value="@(Model.DataFieldType)">
						<button-component tooltip="@typeLocalizer[DataFieldType.DataField + "Tooltip"]" label="@typeLocalizer[DataFieldType.DataField.ToString()]" value="@(Model.FieldTypes[0])"></button-component>
						<button-component tooltip="@typeLocalizer[DataFieldType.LookupField + "Tooltip"]" label="@typeLocalizer[DataFieldType.LookupField.ToString()]" value="@(Model.FieldTypes[1])"></button-component>
						<button-component tooltip="@typeLocalizer[DataFieldType.VirtualField + "Tooltip"]" label="@typeLocalizer[DataFieldType.VirtualField.ToString()]" value="@(Model.FieldTypes[2])"></button-component>
					</button-group-component>
				</div>
				@{
					Func<DataType, bool> whereStatement = Model.DataType switch
					{
						DataType.Boolean => type => type is DataType.Boolean or DataType.String or DataType.Text,
						DataType.Date => type => type is DataType.Date or DataType.String or DataType.Text,
						DataType.String => type => type is DataType.String or DataType.Text,
						DataType.Text => type => type == DataType.Text,
						DataType.Double => type => type is DataType.Double or DataType.String or DataType.Text,
						DataType.Integer => type => type is DataType.Integer or DataType.String or DataType.Text or DataType.Double or DataType.Long,
						DataType.Time => type => type is DataType.Time or DataType.String or DataType.Text,
						DataType.TimeFixed => type => type is DataType.TimeFixed or DataType.String or DataType.Text,
						DataType.DateTime => type => type is DataType.DateTime or DataType.String or DataType.Text,
						DataType.DateTimeFixed => type => type is DataType.DateTimeFixed or DataType.String or DataType.Text,
						DataType.Long => type => type is DataType.Long or DataType.String or DataType.Text or DataType.Double,
						DataType.Guid => type => type is DataType.Guid,
						_ => _ => false
					};

					List<AutocompleteOptionDefinition> typeOptions = Model.DataStoreTypes.Where(whereStatement).Select(type => new AutocompleteOptionDefinition(type.ToString(), type.GetDisplayName())).ToList();
				}
				<div class="form__item">
					<config-label target="data-type" label="@localizer["DataType"]"></config-label>
					<autocomplete-component
						type="InputDataType.String"
						id="data-type"
						options="typeOptions"
						name="type"
						value="@(Model.DataType)"
						class="item__value"
						placeholder="@localizer["pleaseChoose"]"
						required="true"
						readonly="@(Model.DataFieldType != DataFieldType.DataField || Model.SystemField || Model.AutoGenerated)">
					</autocomplete-component>
				</div>
				@{
					var inputDataTypeOptions = Enum.GetValues<InputDataType>().Where(type => type.IsSubtype() && type.DataType() == DataType.String)
						.Select(type => new AutocompleteOptionDefinition(type.ToString(), inputTypeLocalizer[type.GetDisplayName() + "Tooltip"])).ToList();
					inputDataTypeOptions.Insert(0, new AutocompleteOptionDefinition("Default", inputTypeLocalizer["DefaultTooltip"]));
				}
				<div class="form__item">
					<config-label target="input-data-type" label="@localizer["InputDataType"]"></config-label>
					<autocomplete-component
						type="InputDataType.String"
						id="input-data-type"
						options="@inputDataTypeOptions"
						name="inputDataType"
						class="item__value"
						value="@(Model.DataField?.InputDataType?.GetDisplayName())"
						default="Default"
						required="true">
					</autocomplete-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-length" label="@localizer["Length"]"></config-label>
					<input-component id="data-field-length" name="length" class="item__value" value="@(Model.DataField?.Length ?? 0)" required></input-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-decimal-places" label="@localizer["DecimalPlaces"]"></config-label>
					<input-component id="data-field-decimal-places" name="decimalPlaces" class="item__value" value="@(Model.DataField?.DecimalPlaces ?? 2)" required></input-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-sign" label="@localizer["Sign"]"></config-label>
					<input-component id="data-field-sign" name="sign" class="item__value" readonly="@(Model.SystemField || Model.AutoGenerated)" value="@(Model.DataField?.Sign ?? "")"></input-component>
				</div>
				<div class="form__item">
					<config-label label="@localizer["formatType"]"></config-label>
					<button-group-component name="formatType" class="item__value" value="@(Model.DataField?.FormatType?.GetDisplayName() ?? DataFieldFormatType.Default.GetDisplayName())">
						<button-component label="@localizer[DataFieldFormatType.Default.ToString().Camelize()]" value="@(DataFieldFormatType.Default)"></button-component>
						<button-component label="@localizer[DataFieldFormatType.OnOff.ToString().Camelize()]" value="@(DataFieldFormatType.OnOff)"></button-component>
						<button-component label="@localizer[DataFieldFormatType.YesNo.ToString().Camelize()]" value="@(DataFieldFormatType.YesNo)"></button-component>
						<button-component label="@localizer[DataFieldFormatType.CustomText.ToString().Camelize()]" value="@(DataFieldFormatType.CustomText)"></button-component>
					</button-group-component>
				</div>
				<div class="form__item">
					<config-label label="@localizer["labelTrue"]"></config-label>
					<input-component name="labelTrue" type="InputDataType.Translation" class="item__value" translation-prefix="/DataField" value="@(Model.DataField?.LabelTrue ?? "")" required="true"></input-component>
				</div>
				<div class="form__item">
					<config-label label="@localizer["labelFalse"]"></config-label>
					<input-component name="labelFalse" type="InputDataType.Translation" class="item__value" translation-prefix="/DataField" value="@(Model.DataField?.LabelFalse ?? "")" required="true"></input-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-default-value" label="@localizer["DefaultValue"]"></config-label>
					<input-component id="data-field-default-value" name="defaultValue" class="item__value" readonly="@(Model.SystemField || Model.AutoGenerated)" value="@(Model.DataField?.DefaultValue ?? "")"></input-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-rich-text" label="@localizer["RichText"]"></config-label>
					<toggle-component id="data-field-rich-text" name="richText" class="item__value" value="@(Model.DataField?.RichText ?? false)"></toggle-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-mandatory" label="@localizer["Mandatory"]"></config-label>
					<toggle-component id="data-field-mandatory" name="mandatory" class="item__value" value="@(Model.DataField?.Mandatory ?? false)"></toggle-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-unique" label="@localizer["Unique"]"></config-label>
					<toggle-component id="data-field-unique" name="unique" class="item__value" readonly="@(Model.DataFieldType == Model.FieldTypes[1])" value="@(Model.DataField?.Unique ?? false)"></toggle-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-multi" label="@localizer["Multi"]"></config-label>
					<toggle-component id="data-field-multi" name="multi" class="item__value" value="@(Model.Multi)" readonly="@(Model.Multi)"></toggle-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-system-field" label="@localizer["SystemField"]"></config-label>
					<toggle-component class="item__value" id="data-field-system-field" name="systemField" readonly value="@(Model.SystemField)"></toggle-component>
				</div>
				<div class="form__item">
					<config-label target="data-field-auto-generated" label="@localizer["AutoGenerated"]"></config-label>
					<toggle-component class="item__value" id="data-field-auto-generated" name="autoGenerated" readonly value="@(Model.AutoGenerated)"></toggle-component>
				</div>

				<div class="form__item">
					<config-label target="data-field-lookup-source" label="@localizer["LookupSource"]"></config-label>
					@{
						var dataSourceColumns = new List<AutocompleteColumnDefinition>
						{
							new("name", localizer["name"], DataType.String),
							new("comment", localizer["comment"], DataType.String, false, true)
						};
						<autocomplete-component type="InputDataType.Guid" id="data-field-lookup-source" nullable="true"
						                        columns="dataSourceColumns" name="lookupSource" output-name="lookupSourceId"
						                        class="item__value" placeholder="@localizer["pleaseChoose"]" readonly="true" required="true"
						                        edit-link-url="/Admin/DataStores/{dataStore.Slug}/DataSources/{slug}">
						</autocomplete-component>
						<script>
							document.getElementById("data-field-detail-form").querySelector('[name="lookupSource"]').addEventListener("change", (event) => {
								if (!event.target.value)
									return
								
								const lookupDisplayAutocomplete = document.getElementById("data-field-detail-form").querySelector('[name="lookupDisplayField"]')
								const newUrl  = `/Api/DataSources/${event.target.value}/DataFields?type=DataField&excludeSystemFields=true`
								if (lookupDisplayAutocomplete.url && lookupDisplayAutocomplete.url !== newUrl)
									lookupDisplayAutocomplete.clear()
								lookupDisplayAutocomplete.url = newUrl

								const filterConfig = document.querySelector('#filter-config')
								const filterConfigFieldUrl = event.target.value ? `/Api/DataSources/${event.target.value}/DataFields` : ''
								filterConfig?.setAttribute('field-url', filterConfigFieldUrl)
							});
						</script>
					}
				</div>
				<div class="form__item">
					<config-label target="data-field-lookup-display-field" label="@localizer["LookupDisplayField"]"></config-label>
					@{
						var dataSourceFieldColumns = new List<AutocompleteColumnDefinition>
						{
							new("name", localizer["name"], DataType.String),
							new("type", localizer["type"], DataType.String)
						};
						<autocomplete-component type="InputDataType.Guid" id="data-field-lookup-display-field" nullable="true" 
						                        url="@(Model.DataField != null ? $"/Api/DataSources/{Model.DataField.LookupSourceId}/DataFields?type=DataField&excludeSystemFields=true" : "")"
						                        columns="dataSourceFieldColumns" name="lookupDisplayField" output-name="lookupDisplayFieldId"
						                        class="item__value" placeholder="@localizer["pleaseChoose"]" required="true"
						                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}">
						</autocomplete-component>
					}
				</div>

				<div class="form__item">
					<config-label target="data-field-virtual-lookup-field" label="@localizer["VirtualLookupField"]"></config-label>
					@{
						var dataVirtualLookupFieldColumns = new List<AutocompleteColumnDefinition>
						{
							new("name", localizer["name"], DataType.String),
							new("dataSource.Name", localizer["dataSourceName"], DataType.String),
							new("dataSource.Comment", localizer["comment"], DataType.String, false, true)
						};
						<autocomplete-component type="InputDataType.Guid" id="data-field-virtual-lookup-field" nullable="true"
						                        columns="dataVirtualLookupFieldColumns" name="virtualLookupField" output-name="virtualLookupFieldId"
						                        class="item__value" placeholder="@localizer["pleaseChoose"]" required="true"
						                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}">
						</autocomplete-component>
						<script>
							document.getElementById("data-field-detail-form").querySelector('[name="virtualLookupField"]').addEventListener("change", (event) => {
								if (!event.target.value)
									return

								const virtualDataAutocomplete = document.getElementById("data-field-detail-form").querySelector('[name="virtualDataField"]')

								const id = document.getElementById("data-field-detail-form").querySelector('[name="id"]').value
								const newUrl = `/Api/DataFields/${event.target.value}/LookupableFields/${id}`
								if (virtualDataAutocomplete.url && virtualDataAutocomplete.url !== newUrl)
									virtualDataAutocomplete.clear()
								virtualDataAutocomplete.url = newUrl
							});
						</script>
					}
				</div>
				<div class="form__item">
					<config-label target="data-field-virtual-data-field" label="@localizer["VirtualDataField"]"></config-label>
					@{
						var dataFieldColumns = new List<AutocompleteColumnDefinition>
						{
							new("name", localizer["name"], DataType.String),
							new("type", localizer["type"], DataType.String)
						};
						<autocomplete-component type="InputDataType.Guid" id="data-field-virtual-data-field" nullable="true" 
						                        url="@(Model.DataField?.VirtualLookupFieldId != null ? $"/Api/DataFields/{Model.DataField.VirtualLookupFieldId}/LookupableFields/{Model.DataField?.Id}" : "")"
						                        columns="dataFieldColumns" name="virtualDataField" output-name="virtualDataFieldId"
						                        class="item__value" placeholder="@localizer["pleaseChoose"]" required="true"
						                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}">
						</autocomplete-component>
					}
				</div>
			</config-section>

			@if (Model.DataFieldType == DataFieldType.LookupField)
			{
				<section-component id="column-section" class="section-span-all" heading="@localizer["additionalColumns"]">
					@{
						var additionalColumnDefinition = new List<AutocompleteColumnDefinition>
						{
							new("name", localizer["name"], DataType.String),
							new("type", localizer["type"], DataType.String)
						};
						var columnFilters = new List<QueryParamFilterDto>
						{
							new() { FilterColumn = "DataFieldId", CompareValue = Model.DataField?.Id }
						};
						var columnSortings = new List<QueryParamSortingDto>()
						{
							new() { OrderColumn = "Position", Direction = SortDirection.Asc }
						};
					}
					<legend>@localizer["additionalColumnLegend"]</legend>
					<autocomplete-component id="display-field-selector" name="displayFieldSelector" type="InputDataType.Guid" nullable="true" 
					                        placeholder="@localizer["addColumn"]"
					                        Url="@(Model.DataField != null ? $"/Api/DataFields/{Model.DataField.Id}/LookupFields" : "")" columns="additionalColumnDefinition">
					</autocomplete-component>
					<enumeration-component id="data-field-column-list" url="@(Model.DataField != null ? "/Api/DataFieldColumns/" : "")" filters="@columnFilters" sorting="@columnSortings">
						<list-component size="LineSize.Small">
							<list-data-column-component name="displayFieldName"></list-data-column-component>
							<list-column-component name="delete" with-converter width="30"></list-column-component>
						</list-component>
					</enumeration-component>
					<script>
						document.querySelector('#data-field-id').addEventListener('change', async (event) => {
							document.querySelector('#display-field-selector').setAttribute('url', `/Api/DataFields/${event.target.value}/LookupFields`)
							
							const columnList = document.querySelector('#data-field-column-list')
							columnList.setAttribute('url', `/Api/DataFieldColumns/`)
							columnList.setAttribute('filters', `[{"filterColumn":"DataFieldId", "operator":"Equals", "compareValue":"${event.target.value}"}]`)

							const filterConfig = document.querySelector('#filter-config')
							filterConfig.setAttribute('parent-element-value', event.target.value)

							await Component.waitForComponentInitialization(filterConfig)
							filterConfig?.clear()
						})
						document.querySelector('#display-field-selector').addEventListener('change', (event) => {
							if (!event.target.value)
								return

							const dataFieldId = document.querySelector('#data-field-id').value
							Page.createEntry(`/Api/DataFieldColumns/`, { "DataFieldId":dataFieldId, "DisplayFieldId":event.target.value }).then(() => {
								document.querySelector('#data-field-column-list').reload()
							})
							event.target.clear()					
						})
						document.querySelector('#data-field-column-list lvl-list-column[name="delete"]').converter = (data) => {
							return `<i class="fa-light fa-trash clickable" style="color: var(--clr-signal-error);width:3rem;height:3.2rem;line-height:3.2rem;" onclick="deleteDataFieldColumn('${data.id}')"></i>`
						}
						document.querySelector('#data-field-column-list').addEventListener('query-view:changed', (event) => {
							document.querySelector('#column-section').subtitle = event.target.apiController.rows.length
						})
						async function deleteDataFieldColumn(columnId) {
							Page.deleteEntry(`/Api/DataFieldColumns/${columnId}`).then(() => {
								document.querySelector('#data-field-column-list').reload()
								document.querySelector('#display-field-selector').clear()
							})
						}
					</script>
					<div class="form__item">
						<config-label target="data-field-column-view" label="@localizer["columnView"]"></config-label>
						<toggle-component id="data-field-column-view" name="columnView" class="item__value" value="@(Model.DataField?.ColumnView ?? false)"></toggle-component>
					</div>
				</section-component>
				<config-section label="@localizer["sectionFilters"]" ignore-overflow>
					<div class="form__item">
						<config-label target="data-field-filter-self" label="@localizer["filterSelf"]"></config-label>
						<toggle-component id="data-field-filter-self" name="filterSelf" class="item__value" value="@(Model.DataField?.FilterSelf ?? false)"></toggle-component>
					</div>
					<filter-config-component id="filter-config" url="/Api/DataFieldFilters" parent-element-key="DataFieldId" parent-element-value="@(Model.DataField?.Id)"
					                         field-url="@(Model.DataField != null ? $"/Api/DataSources/{Model.DataField.LookupSourceId}/DataFields" : "")">
					</filter-config-component>
				</config-section>
			}
		</form-component>
		<button-component class="@(Model.SystemField || Model.AutoGenerated ? "hide" : "")" data-action="delete" id="data-field-delete-button" label="@localizer["deleteButton"]" icon="trash" type="ButtonType.Secondary" color="ColorState.Error"></button-component>
	</tab-component>
	<tab-component label="@(localizer["tab2/label"])">
		<span>@localizer["tab2/description"]</span>
		<list-component label-column-icon="arrow-up-right-from-square">
			<list-data-column-component name="Id" hidden></list-data-column-component>
			<list-data-column-component name="Name"></list-data-column-component>
			<list-data-column-component name="Type" label="@localizer["tab2/Type"]"></list-data-column-component>
		</list-component>
	</tab-component>
	<tab-component label="@(localizer["tab3/label"])">
		<span>@localizer["tab3/description"]</span>
		<list-component label-column-icon="arrow-up-right-from-square">
			<list-data-column-component name="Id" hidden></list-data-column-component>
			<list-data-column-component name="Module" label="@localizer["tab3/Module"]"></list-data-column-component>
			<list-data-column-component name="DataSource" label="@localizer["tab3/DataSource"]"></list-data-column-component>
			<list-data-column-component name="DataField" label="@localizer["tab3/DataField"]"></list-data-column-component>
		</list-component>
	</tab-component>
</tab-bar-component>