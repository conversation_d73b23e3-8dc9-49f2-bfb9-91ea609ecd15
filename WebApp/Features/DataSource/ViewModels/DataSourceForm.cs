using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels;

[ExcludeFromCodeCoverage]
public class DataSourceForm
{
	public ViewType ViewType { get; init; }
	
	public DataSourceDto? DataSource { get; init; }

	public bool NavigateOverModule { get; init; } = false;
	
	public Guid? DataStoreId { get; init; }
	
	public Guid? ModuleId { get; init; }
	
	public string? ModuleName { get; init; }

	public DataSourceForm(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}