@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminDetailPage
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@model Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels.DataSourceForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("DataSource", "");
	List<MenuItem> menuItems =
	[
		new MenuItem("basic", "BasicSettings", "screwdriver-wrench"),
		new MenuItem("fields", "Fields", "input-text"),
		new MenuItem("pages", "Pages", "table-layout"),
		new MenuItem("workflows", "Workflows", "arrow-progress"),
		new MenuItem("elementActions", "ElementActions", "play")
	];

	List<MenuInfo> menuInfos =
	[
		MenuInfo.GetLinkInstance("info-dataStore-link", "dataStore", Model.DataSource?.DataStore?.Name ?? "", $"/Admin/DataStores/{Model.DataSource?.DataStore?.Slug}"),
		MenuInfo.GetTextInstance("info-createdBy", "createdBy", Model.DataSource?.CreatedBy ?? "", InputDataType.String,Model.DataSource?.Created?.ToString("o"), InputDataType.DateTime),
		MenuInfo.GetTextInstance("info-lastModifiedBy", "lastModifiedBy", Model.DataSource?.LastModifiedBy ?? "", InputDataType.String,Model.DataSource?.LastModified?.ToString("o"), InputDataType.DateTime),
		MenuInfo.GetLinkInstance("info-module-link", "module", Model.DataSource?.ModuleName ?? "", $"/Admin/DataStores/{Model.DataSource?.DataStore?.Slug}/Modules/{Model.DataSource?.Module?.Slug}"),
	];

	string currentMenu = ViewData["targetMenu"] == null ? "BasicSettings" : ViewData["targetMenu"]!.ToString()!;
}
<script type="module" defer>
	@if (Model.DataSource != null)
	{
		if (Model.NavigateOverModule)
		{
			<text>
			Page.setMainPage(`/Admin/DataStores/@(Model.DataSource.DataStore?.Slug)/Modules/@(Model.DataSource.Module?.Slug)/DataSources/@(Model.DataSource.Slug)`, '@(currentMenu)')
			Page.setBreadcrumbs([
					{ label: '@localizer["list/modules"]', url: `/Admin/DataStores/@(Model.DataSource!.DataStore?.Slug)/Modules` }, 
					{ label: '@(Model.DataSource.Module?.Name)', url: `/Admin/DataStores/@(Model.DataSource!.DataStore?.Slug)/Modules/@(Model.DataSource.Module?.Slug)` }, 
					{ label: '@(Model.DataSource?.Name)', url: `/Admin/DataStores/@(Model.DataSource!.DataStore?.Slug)/Modules/@(Model.DataSource.Module?.Slug)/DataSources/@(Model.DataSource.Slug)` }
			], true)
			</text>
		}
		else
		{
			<text>
			Page.setMainPage(`/Admin/DataStores/@(Model.DataSource.DataStore?.Slug)/DataSources/@(Model.DataSource.Slug)`, '@(currentMenu)')
			Page.setBreadcrumbs([
					{ label: '@localizer["list/pageTitle"]', url: `/Admin/DataStores/@(Model.DataSource!.DataStore?.Slug)/DataSources` }, 
					{ label: '@(Model.DataSource?.Name)', url: `/Admin/DataStores/@(Model.DataSource!.DataStore?.Slug)/DataSources/@(Model.DataSource.Slug)` }
			], true)
			</text>
		}
	}
</script>
<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="DataSource" route-name="DataSources" menu-items="@menuItems" menu-infos="@menuInfos" skeleton="@(Model.DataSource == null)" width="250"></vc:basic-menu>
<vc:admin-detail-page model="@Model" entity="DataSource" route-name="DataSources" title="@(Model.DataSource?.Name)" menu-item="@currentMenu" show-default-buttons="@(Model.DataSource != null)"></vc:admin-detail-page>
