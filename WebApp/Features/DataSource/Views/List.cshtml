@using System.Text.Json
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.DataStoreConfig
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Microsoft.OpenApi.Extensions
@model Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels.DataSourceList
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("DataSource", "");
	var typeLocalizer = LocalizerFactory.Create("DataSourceType", "");
	var storeSlug = ViewContext.RouteData.Values["dataStoreSlug"]?.ToString();
	DataStoreConfigDto? selectedDataStore = Model.DataStores.FirstOrDefault(dataStoreConfig => dataStoreConfig.Slug == storeSlug);
	IList<BaseColumnComponentTagHelper> columns = [
		new ListDataColumnComponentTagHelper() { Name = "name", Label = localizer["list/name"] },
		new ListDataColumnComponentTagHelper() { Name = "typeName", Label = localizer["list/type"] },
		new ListDataColumnComponentTagHelper() { Name = "moduleName", Label = localizer["list/module"], ReferenceField = "module.name"}
	];
	List<QueryParamSortingDto> sortings = [new() { OrderColumn = "Name", Direction = SortDirection.Asc }];
	
	var typeValues = Enum.GetValues(typeof(DataSourceType))
		.Cast<DataSourceType>()
		.Select(type => new FilterFieldQueryItemResultDto(){ Value = type, Label = typeLocalizer[type.GetDisplayName()]})
		.OrderBy(item => item.Label)
		.ToList();
	IList<FilterPanelSectionComponentTagHelper> sections =
	[
		new() { Name = "name", Label = localizer["name"], MultiValue = true },
		new() { Name = "type", Label = localizer["type"], MultiValue = true, ValuePreview = true, Type = InputDataType.Enum, PossibleValues = typeValues },
		new() { Name = "module.name", Label = localizer["module"], MultiValue = true, ValuePreview = true },
		new() { Name = "lastModified", Label = localizer["lastModified"], Type = InputDataType.Date },
		new() { Name = "lastModifiedBy", Label = localizer["lastModifiedBy"], ValuePreview = true }
	];
	List<MenuInfo> menuInfos = [MenuInfo.GetLinkInstance("info-backend-link", "dataStore", selectedDataStore?.Name ?? "", $"/Admin/DataStores/{selectedDataStore?.Slug}")];
	List<MenuItem> menuItems = Model.DataStores.Select(dataStore => new MenuItem(dataStore.Name ?? "", dataStore.Id!.Value.ToString(), null, dataStore.Slug == storeSlug, dataStore.Enabled, dataStore)).ToList();
}

@await Html.PartialAsync("_SyncDialog")
<script type="module" defer>
	// Save data needs to be done first!
	@if (selectedDataStore != null)
	{
		@:Page.saveFormData(@(Html.Raw(JsonSerializer.Serialize(selectedDataStore, ConfigHelper.DefaultJsonOptions))))
		@:Page.setMainPage('/Admin/DataStores/@(selectedDataStore.Slug)/DataSources')
		@:Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: Page.getMainPageUrl() }])
	}
	Page.globalSearchbar.disabled = false
</script>
<vc:basic-menu type="@BasicMenuType.ListFilter" entity="DataSource" base-route="Admin/DataStores/-/DataSources" route-name="DataSources" menu-items="@menuItems" menu-infos="@menuInfos" show-state="true"></vc:basic-menu>
<vc:filter-panel entity="DataSource" route-name="DataSources" sections="@sections" in-admin-config="true"></vc:filter-panel>
<vc:admin-list-page entity="DataSource" route-name="DataSources" columns="@columns" sorting="@sortings" localizer="@localizer" filter-by-menu="true" parent-property-name="dataStoreId" open-on-row-click="true"></vc:admin-list-page>
<vc:create-panel entity="DataSource" route-name="DataSources" localizer="@localizer" parent-property-name="dataStoreId"></vc:create-panel>
<script type="module" defer>
	// link button in the info part of the menu has to open the selected data store config
	document.querySelector('#info-backend-link .menu-info__link')?.addEventListener('click', () => {
		window.open(`/Admin/DataStores/${Page.getFormData()?.slug ?? ''}`, '_blank')
	})

	const menu = document.getElementById('data-source-menu');
	@if (selectedDataStore == null)
	{
		<text>
			// no backends available
			if(Page.getFormData()?.name == null) {
				document.querySelector('[data-action=add]').disabled = true
				const infoBackendLabel = document.querySelector('#info-backend-link span')
				if (infoBackendLabel)
					infoBackendLabel.textContent = '@localizer["menu/info/noBackend"]'
				const infoLinkButton = document.querySelector('#info-backend-link .menu-info__link')
				infoLinkButton.classList.add('hide')
				menu?.querySelector('.menu__no-data lvl-button')?.addEventListener('click', () => window.open('/Admin/DataStores', '_blank'))
			} else {
				setBackend()
			}
		</text>
	}

	// add sync request button
	Page.buttonConfig.sync = true
	Page.buttonConfig.syncButton.disabled = false
	Page.buttonConfig.syncButton.addEventListener('click', async () => {
		const storeId = Page.getFormData()?.id
		await window.Sync.syncDataStore(storeId)
	}, { signal: Page.getPageChangeSignal() })
	Page.buttonConfig.syncButton.skeleton = false
	
	menu?.addEventListener('nav-item:click', async () => setBackend())

	document.getElementById('data-source-list')?.addEventListener('list-row:click', async customEvent => {
		if (customEvent?.detail == null || customEvent.detail.rowData == null)
			return

		const rowData = customEvent.detail.rowData
		const menu = document.getElementById("data-source-menu")
		await Component.waitForComponentInitialization('lvl-side-nav')
		const dataStoreSlug = Page.getValueByPath(rowData, "dataSource.dataStore.slug")
		const moduleSlug = Page.getValueByPath(rowData, "dataSource.module.slug")

		// Backend
		menu.setInfoData(rowData, "info-dataStore-link-left", "dataSource.dataStore.name")
		menu.setInfoUrl("info-dataStore-link-right", `/Admin/DataStores/${dataStoreSlug}`)

		// CreatedBy
		menu.setInfoData(rowData, "info-createdBy-left", "createdBy")
		menu.setInfoData(rowData, "info-createdBy-right", "created")

		// LastModifiedby
		menu.setInfoData(rowData, "info-lastModifiedBy-left", "lastModifiedBy")
		menu.setInfoData(rowData, "info-lastModifiedBy-right", "lastModified")
		
		if (moduleSlug != null){
			// Module
			menu.setInfoData(rowData, "info-module-link-left", "page.dataSource.moduleName")
			menu.setInfoUrl("info-module-link-right", `/Admin/DataStores/${dataStoreSlug}/Modules/${moduleSlug}`)
		}

		// set a few form element attributes
		const form = document.getElementById('data-source-form')
		if (form) {
			form.querySelector('[name=storagePath]').setAttribute('placeholder', rowData['name'] ?? '')
			form.querySelector('[name="annotationKeyField"]').url =  `/Api/DataSources/${Page.getFormData().annotationSourceId}/AnnotationKeyFields?parentSourceId=${Page.getFormData().id}`
		}
	})

	function setBackend() {
		const backendLabel = document.querySelector('#info-backend-link span')
		if (backendLabel)
			backendLabel.textContent = Page.getFormData()?.name

		const linkButton = document.querySelector('#info-backend-link .menu-info__link')
		if (linkButton){
			linkButton.dataset['href'] = `/Admin/DataStores/${Page.getFormData()?.slug ?? ''}`
			linkButton.classList.remove('hide')
		}
	}
</script>