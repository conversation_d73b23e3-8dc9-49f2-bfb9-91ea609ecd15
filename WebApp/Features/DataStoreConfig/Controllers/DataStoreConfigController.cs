using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataStoreConfig;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Domain.Storage;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.Module;
using Levelbuild.Frontend.WebApp.Features.DataStoreConfig.Extensions;
using Levelbuild.Frontend.WebApp.Features.DataStoreConfig.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Frontend.WebApp.Features.DataStoreConfig.Controllers;

/// <summary>
/// Controller for the configuration view of data stores
/// </summary>
public class DataStoreConfigController : AdminController<DataStoreConfigDto>
{
	private IStringLocalizer? _localizer;
	
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="userManager">user manager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="versionReader">injected VersionReader</param>
	public DataStoreConfigController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
									 IExtendedStringLocalizerFactory localizerFactory,
									 IVersionReader versionReader) :
		base(logManager, logManager.GetLoggerForClass<DataStoreConfigController>(), contextFactory, userManager, localizerFactory, versionReader) { }

	#region Views

	/// <summary>
	/// Renders the list view with help of the list dto
	/// </summary>
	/// <returns>rendered list view</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataStores/")]
	public IActionResult List()
	{
		return CachedPartial() ?? RenderPartial();
	}

	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataStores/Create")]
	public IActionResult Create()
	{
		return CachedPartial() ?? RenderPartial(new DataStoreConfigForm(), "List", new DataStoreConfigList());
	}

	/// <summary>
	/// Renders the detail view with help of the data store config dto
	/// </summary>
	/// <param name="slug">readable identifier for a specific data store config</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataStores/Edit/{menu?}")]
	[HttpGet("/Admin/DataStores/{slug?}/{menu?}")]
	public IActionResult Detail(string? slug)
	{
		if (string.IsNullOrEmpty(slug) || string.IsNullOrEmpty(slug))
		{
			return CachedPartial() ?? RenderPartial(new DataStoreConfigForm(ViewType.Edit));
		}

		DataStoreConfigEntity? dataStoreConfiguration =
			DatabaseContext.DataStoreConfigs.FirstOrDefault(config => config.Slug == slug.ToLower());
		if (dataStoreConfiguration == null)
			throw new ElementNotFoundException($"DataStore configuration with slug: {slug} could not be found");

		return RenderPartial(new DataStoreConfigForm(ViewType.Edit)
		{
			DataStoreConfig = dataStoreConfiguration.ToDto(),
			DataStoreInfo = RequestDataStoreInfo(dataStoreConfiguration.Type)
		});
	}

	#endregion

	#region Actions

	/// <summary>
	/// Delivers a json with all infos from a specific data store. Will be used to gets their custom configuration fields. 
	/// </summary>
	/// <param name="type">Enum which identifies the data store</param>
	[HttpGet("/Api/DataStores/info/{type}")]
	public ActionResult<FrontendResponse> GetDataStoreInfo(DataStoreType? type)
	{
		if (!type.HasValue)
		{
			const string message = "No Configuration template for this data store type available.";
			Logger.Warning(message);

			return GetBadRequestResponse(message);
		}

		// TODO: Input Type Enums are not well formed and only just numbers
		try
		{
			var dataStoreInfo = RequestDataStoreInfo(type.Value);
			return GetOkResponse(dataStoreInfo);
		}
		catch (Exception e)
		{
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <inheritdoc />
	[HttpGet("/Api/DataStores/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		return HandleQueryRequest<DataStoreConfigEntity, DataStoreConfigDto>(DatabaseContext.DataStoreConfigs, parameters);
	}

	/// <summary>
	/// Delivers a json with a list of all dataSources for the dataStore matching the parameters 
	/// </summary>
	/// <param name="dataStoreId">id of the datastore</param>
	/// <param name="parameters">query parameter</param>
	/// <returns></returns>
	[HttpGet("/Api/DataStores/{dataStoreId}/DataSources")]
	public ActionResult<FrontendResponse> DataSources(Guid dataStoreId, QueryParamsDto parameters)
	{
		var query = DatabaseContext.DataSources.Include(source => source.DataStore).Where(dataSource => dataSource.DataStoreId == dataStoreId);
		return HandleAutocompleteRequest(query, parameters,
										 nameof(DataSourceEntity.Id),
										 nameof(DataSourceEntity.Name),
										 new PropertyPathList<DataSourceEntity>()
										 {
											 nameof(DataSourceEntity.Name),
											 nameof(DataSourceEntity.Comment),
											 nameof(DataSourceEntity.Slug),
											 (DataSourceEntity source) => source.DataStore.Slug,
										 });
	}
	
	/// <summary>
	/// Delivers a json with a list of all modules for the dataStore matching the parameters 
	/// </summary>
	/// <param name="dataStoreId">id of the datastore</param>
	/// <param name="parameters">query parameter</param>
	/// <returns></returns>
	[HttpGet("/Api/DataStores/{dataStoreId}/Modules")]
	public ActionResult<FrontendResponse> Modules(Guid dataStoreId, QueryParamsDto parameters)
	{
		var query = DatabaseContext.Modules.Include(module => module.DataStore).Where(module => module.DataStoreId == dataStoreId);
		return HandleAutocompleteRequest(query, parameters,
										 nameof(ModuleEntity.Id),
										 nameof(ModuleEntity.Name),
										 new PropertyPathList<ModuleEntity>()
										 {
											 nameof(ModuleEntity.Name),
											 nameof(ModuleEntity.Description),
											 nameof(ModuleEntity.Slug),
											 (ModuleEntity module) => module.DataStore.Slug,
										 });
	}

	/// <inheritdoc />
	[HttpGet("/Api/DataStores/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<DataStoreConfigEntity, DataStoreConfigDto>(DatabaseContext.DataStoreConfigs, id);
	}

	/// <inheritdoc />
	[HttpPost("/Api/DataStores/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] DataStoreConfigDto configurationDto)
	{
		return await HandleCreateRequestAsync(DatabaseContext.DataStoreConfigs, configurationDto,
											  _ => RequestDataStoreInfo(configurationDto.Type).Validate(configurationDto));
	}

	/// <inheritdoc />
	[HttpPatch("/Api/DataStores/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] DataStoreConfigDto configurationDto)
	{
		return await HandleUpdateRequestAsync(DatabaseContext.DataStoreConfigs, id, configurationDto, entity =>
		{
			// Type has always been there and can't be changed afterward
			configurationDto.Type = entity.Type;
			RequestDataStoreInfo(configurationDto.Type).Validate(configurationDto, true);
		});
	}

	/// <inheritdoc />
	[HttpDelete("/Api/DataStores/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		return HandleDeleteRequest(DatabaseContext.DataStoreConfigs, id, entity =>
		{
			// @TODO: Refactor it so that manuel delete statements are not needed anymore - same in DataSource Delete
			// Without this, Backends with a GalleryView and (Sub)TitleField cannot be deleted 
			DatabaseContext.DataFields
				.Include(dataField => dataField.DataSource)
				.Where(dataField => dataField.DataSource != null && dataField.DataSource.DataStoreId == entity.Id).ExecuteDelete();
		});
	}

	#endregion

	#region Sync
	
	/// <summary>
	/// Adds all data sources that are present in the Storage but not in the WebApp to the WebApp. 
	/// </summary>
	/// <param name="id">Guid which identifies the data store</param>
	/// <returns>OK Response for successful request and BAD Response when an error occurred.</returns>
	[HttpGet("/Api/DataStores/{id:guid}/SyncMissingSources")]
	public ActionResult<FrontendResponse> SyncSources(Guid id)
	{
		try
		{
			var dataStore = DatabaseContext.DataStoreConfigs.Find(id);

			if (dataStore == null)
				return GetNotFoundResponse($"No dataStore found for id: {id}");
			
			var storageSources = dataStore.GetDataSources();

			var response = new BatchActionResultDto<string>
			{
				ErrorList = new()
			};
			
			foreach (var storageSource in storageSources)
			{
				try
				{
					if (DatabaseContext.DataSources.FirstOrDefault(source => source.DataStoreId == id && source.Name == storageSource) != null)
						continue;

					dataStore.AddStorageSource(DatabaseContext, storageSource);
					response.SuccessCount++;
				}
				catch (Exception e)
				{
					Logger.Error(e, $"A DataSource could not be synced.");
					response.ErrorList.Add(storageSource);
					response.ErrorCount++;
				}
			}

			response.TotalCount = response.SuccessCount + response.ErrorCount;
			_localizer ??= StringLocalizerFactory.Create("DataSource", "");
			response.Heading = _localizer["syncDialog/notification/addedSources/success", response.SuccessCount, response.TotalCount];
			if (response.ErrorCount > 0)
				response.Message = _localizer["syncDialog/notification/addedSources/error"];

			return GetOkResponse(response);
		}
		catch (Exception e)
		{
			Logger.Error(e, $"DataSources could not be synced.");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Returns data sources that are present in the WebApp but not in the Storage. 
	/// </summary>
	/// <param name="id">Guid which identifies the data store</param>
	/// <returns>OK Response for successful request and BAD Response when an error occurred.</returns>
	[HttpGet("/Api/DataStores/{id:guid}/Sync")]
	public ActionResult<FrontendResponse> GetSourcesToRemove(Guid id)
	{
		try
		{
			var dataStore = DatabaseContext.DataStoreConfigs.Find(id);

			if (dataStore == null)
				return GetNotFoundResponse($"No dataStore found for id: {id}");

			var sources = dataStore.GetSourcesToSync(DatabaseContext);

			var response = new SourcesToSyncDto
			{
				Sources = sources.Select(sourceDto => new SourceToSyncDto()
				{
					Id = sourceDto.Id,
					Name = sourceDto.Name
				}).ToList()
			};

			return GetOkResponse(response);
		}
		catch (Exception e)
		{
			Logger.Error(e, $"DataSources to sync could not be loaded.");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Removes data sources from the WebApp.
	/// </summary>
	/// <param name="id">Guid which identifies the data store</param>
	/// <param name="sources">data source that should be removed</param>
	/// <returns>OK Response for successful request and BAD Response when an error occurred.</returns>
	[HttpDelete("/Api/DataStores/{id:guid}/Sync")]
	public ActionResult<FrontendResponse> RemoveSources(Guid id, [FromBody] SourcesToSyncDto sources)
	{
		try
		{
			var dataStore = DatabaseContext.DataStoreConfigs.Find(id);

			if (dataStore == null)
				return GetNotFoundResponse($"No dataStore found for id: {id}");

			var response = new BatchActionResultDto<string>
			{
				ErrorList = new()
			};

			DataSourceEntity? sourceEntity = null;
			foreach (var source in sources.Sources)
			{
				try
				{
					sourceEntity = DatabaseContext.DataSources.Find(source.Id);
					if (sourceEntity == null)
					{
						response.ErrorCount++;
						response.ErrorList.Add(sourceEntity?.Name ?? source.Id.ToString());
						continue;
					}

					DatabaseContext.DataSources.Remove(sourceEntity);
					DatabaseContext.SaveChanges();
					response.SuccessCount++;
				}
				catch (Exception e)
				{
					response.ErrorCount++;
					response.ErrorList.Add(sourceEntity?.Name ?? source.Id.ToString());
					Logger.Error(e, $"Failed to remove data source.");
				}
			}
			
			response.TotalCount = response.SuccessCount + response.ErrorCount;
			_localizer ??= StringLocalizerFactory.Create("DataSource", "");
			response.Heading = _localizer["syncDialog/notification/sources/success", response.SuccessCount, response.TotalCount];
			if (response.ErrorCount > 0)
				response.Message = _localizer["syncDialog/notification/sources/error"];

			return GetOkResponse(response);
		}
		catch (Exception e)
		{
			Logger.Error(e, $"DataSources could not be removed.");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Returns data sources that are present in both WebApp and Storage with a list of their fields that are present in the WebApp but not in the Storage.
	/// </summary>
	/// <param name="id">Guid which identifies the data store</param>
	/// <returns>OK Response for successful request and BAD Response when an error occurred.</returns>
	[HttpGet("/Api/DataStores/{id:guid}/SyncWithFields")]
	public ActionResult<FrontendResponse> GetFieldsToRemoveOfSources(Guid id)
	{
		try
		{
			var dataStore = DatabaseContext.DataStoreConfigs.Find(id);

			if (dataStore == null)
				return GetNotFoundResponse($"No dataStore found for id: {id}");

			var sourcesToSync = dataStore.GetSourcesToSync(DatabaseContext);
			var sourcesWithFieldsToSync = DatabaseContext.DataSources.Where(source => source.DataStoreId == id && !sourcesToSync.Contains(source)).ToList();

			var response = new SourcesWithFieldsToSyncDto();
			foreach (var source in sourcesWithFieldsToSync)
			{
				var fieldsToSync = source.GetFieldsToSync(DatabaseContext);
				if (fieldsToSync.IsNullOrEmpty())
					continue;

				var fields = fieldsToSync.Select(field => new FieldToSyncDto { Id = field.Id, Name = field.Name, Type = field.Type.ToString() }).ToList();
				response.Sources.Add(new SourcesWithFieldsToSyncDto.SourceWithFieldsToSync{SourceId = source.Id, SourceName =source.Name, Fields = fields});
			}

			return GetOkResponse(response);
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Fields to sync could not be loaded.");
			return GetBadRequestResponse(e.Message);
		}
	}

	#endregion

	private DataStoreInfo RequestDataStoreInfo(DataStoreType? dataStoreType)
	{
		if (!dataStoreType.HasValue)
		{
			throw new ArgumentNullException($"DataStoreType cannot be null");
		}

		var info = dataStoreType switch
		{
			DataStoreType.Storage => Storage.GetInfo(),
			DataStoreType.Weather => new DataStoreInfo("Weather", "Dummy Info", new Version(0, 0), new List<DataStoreConfigurationGroup>()),
			_ => throw new ArgumentOutOfRangeException($"DataStore type is not supported {dataStoreType.Value.GetLabel()}")
		};

		// localize info object before delivery
		info.Localize(StringLocalizerFactory.Create("DataStoreType/" + dataStoreType.ToString(), ""));

		return info;
	}
}