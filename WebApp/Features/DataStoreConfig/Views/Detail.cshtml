@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Services
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminDetailPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@model Levelbuild.Frontend.WebApp.Features.DataStoreConfig.ViewModels.DataStoreConfigForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@inject IAssetService AssetService
@addTagHelper *, WebApp

@{
	IList<MenuItem> menuItems = new List<MenuItem>()
	{
		new("basic", "BasicSettings", "screwdriver-wrench"),
		new("config", "Configuration", "sliders"),
		new("context", "DataStoreContexts", "building")
	};
	var localizer = LocalizerFactory.Create("DataStoreConfig", "");
	string currentMenu = ViewData["targetMenu"] == null ? "BasicSettings" : ViewData["targetMenu"]!.ToString()!;
}
<script type="module" defer>
	import { appendConfigurationOptionsToForm, queryDataStoreInfo } from '@AssetService.SolvePath("/data-store-config/configuration.ts")'

	@if (Model.DataStoreConfig != null)
	{
		@:Page.setMainPage(`/Admin/DataStores/@(Model.DataStoreConfig.Slug)`, '@(currentMenu)')
		@:Page.setBreadcrumbs([{ label: '@(localizer["list/pageTitle"])', url: '/Admin/DataStores' }, { label: '@(Model.DataStoreConfig.Name)', url: `/Admin/DataStores/@(Model.DataStoreConfig.Slug)` }])
	}
	
	document.getElementById('data-store-config-menu')?.addEventListener('menu:view-loaded', async (event) => {
		const menuItem = event.target
		if (menuItem.value === 'Configuration') {
			let typeJson = await queryDataStoreInfo(Page.getFormData().type)
			appendConfigurationOptionsToForm(Page, typeJson, Page.getFormData())
		}
	})
</script>
<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="DataStoreConfig" route-name="DataStores" menu-items="@menuItems" skeleton="@(Model.DataStoreConfig == null)"></vc:basic-menu>
<vc:admin-detail-page entity="DataStoreConfig" route-name="DataStores" model="@Model" title="@Model.DataStoreConfig?.Name" menu-item="@currentMenu" show-default-buttons="@(Model.DataStoreConfig != null)"></vc:admin-detail-page>