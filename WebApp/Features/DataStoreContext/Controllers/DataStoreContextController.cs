using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataStoreContext;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Domain.Storage;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.DataStoreContext;
using Levelbuild.Frontend.WebApp.Features.DataStoreConfig.ViewModels;
using Levelbuild.Frontend.WebApp.Features.DataStoreContext.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.DataStoreContext.Controllers;

/// <summary>
/// Controller for the configuration view of data store contexts
/// </summary>
public class DataStoreContextController : AdminController<DataStoreContextDto>
{
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager">The log manager.</param>
	/// <param name="contextFactory">The DB context factory.</param>
	/// <param name="userManager">The user manager.</param>
	/// <param name="localizerFactory">The string localizer factory.</param>
	/// <param name="versionReader">injected VersionReader</param>
	public DataStoreContextController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : 
		base(logManager, logManager.GetLoggerForClass<DataStoreContextController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		
	}
	
	#region Views
	
	/// <summary>
	/// Renders the detail view with help of the data store context dto
	/// </summary>
	/// <param name="dataStoreSlug">readable identifier for a specific data store</param>
	/// <param name="slug">readable identifier for a specific data store context</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataStoreContexts/Edit")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/{menu}/{slug}")]
	public IActionResult Detail(string? dataStoreSlug, string? slug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(slug))
		{
			Request.Query.TryGetValue("type", out var dataStoreType);
			if (dataStoreType.ToString().Equals("storage", StringComparison.CurrentCultureIgnoreCase))
			{
				return this.CachedPartial() ?? this.RenderPartial(new DataStoreContextForm(ViewType.Edit)
				{
					DataStoreInfo = RequestDataStoreContextInfo(DataStoreType.Storage)
				});
			}
			
			return this.CachedPartial() ?? this.RenderPartial(new DataStoreContextForm(ViewType.Edit));
		}
		
		DataStoreConfigEntity? dataStoreConfiguration = DatabaseContext.DataStoreConfigs.FirstOrDefault(config => config.Slug == dataStoreSlug);
		if (dataStoreConfiguration == null)
			throw new ElementNotFoundException($"DataStore configuration with slug: {dataStoreSlug} could not be found");
		
		var dataStoreId = dataStoreConfiguration.Id;
		var dataStoreDto = DatabaseContext.DataStoreConfigs.FirstOrDefault(dataStore => dataStore.Id == dataStoreId && dataStore.Slug == dataStoreSlug)
			?.ToDto();
		var dataStoreConfigId = dataStoreDto?.Id;
		
		DataStoreContextEntity? dataStoreContext =
			DatabaseContext.DataStoreContexts.Include(context => context.DataStore).Include(context => context.Customer)
				.FirstOrDefault(context => context.Slug == slug.ToLower() && context.DataStoreId == dataStoreConfigId);
		if (dataStoreContext == null)
			throw new ElementNotFoundException($"DataField configuration with slug: {slug} could not be found");
		
		return this.RenderPartial(new DataStoreContextForm(ViewType.Edit)
		{
			DataStoreContext = dataStoreContext.ToDto(),
			DataStoreInfo = RequestDataStoreContextInfo(dataStoreConfiguration.Type),
			DataStoreId = dataStoreId
		}, "~/Features/DataStoreConfig/Views/Detail.cshtml", new DataStoreConfigForm()
		{
			DataStoreConfig = dataStoreDto,
		});
	}
	
	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataStoreContexts/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/{menu}/Create")]
	public IActionResult Create(string dataStoreSlug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug))
		{
			Request.Query.TryGetValue("type", out var dataStoreType);
			if (dataStoreType.ToString().Equals("storage", StringComparison.CurrentCultureIgnoreCase))
			{
				return this.CachedPartial() ?? this.RenderPartial(new DataStoreContextForm
				{
					DataStoreInfo = RequestDataStoreContextInfo(DataStoreType.Storage)
				});
			}
			
			return this.CachedPartial() ?? this.RenderPartial(new DataStoreContextForm());
		}
		
		DataStoreConfigEntity? dataStoreConfiguration = DatabaseContext.DataStoreConfigs.FirstOrDefault(config => config.Slug == dataStoreSlug);
		if (dataStoreConfiguration == null)
			throw new ElementNotFoundException($"DataStore configuration with slug: {dataStoreSlug} could not be found");
		
		var dataStoreId = dataStoreConfiguration.Id;
		var dataStore = DatabaseContext.DataStoreConfigs.FirstOrDefault(dataStore => dataStore.Id == dataStoreId)?.ToDto();
		return this.RenderPartial(new DataStoreContextForm()
		{
			DataStoreContext = new DataStoreContextDto() { DataStoreId = dataStore?.Id },
			DataStoreInfo = RequestDataStoreContextInfo(dataStoreConfiguration.Type),
			DataStoreId = dataStoreId
		}, "~/Features/DataStoreConfig/Views/Detail.cshtml", new DataStoreConfigForm()
		{
			DataStoreConfig = dataStore
		});
	}
	
	#endregion
	
	#region Actions
	
	/// <summary>
	/// Delivers a json with all infos from a specific data store context. Will be used to gets their custom configuration fields. 
	/// </summary>
	/// <param name="guid">Guid of the parent datastore</param>
	[HttpGet("/Api/DataStoreContexts/info/{guid}")]
	public ActionResult<FrontendResponse> GetDataStoreContextInfo(Guid? guid)
	{
		var type = DatabaseContext.DataStoreConfigs.FirstOrDefault(config => config.Id == guid)?.Type;
		
		if (!type.HasValue)
		{
			const string message = "No Configuration template for this data store type available.";
			Logger.Warning(message);
			
			return GetBadRequestResponse(message);
		}
		
		// TODO: Input Type Enums are not well formed and only just numbers
		try
		{
			var dataStoreInfo = RequestDataStoreContextInfo(type.Value);
			return GetOkResponse(dataStoreInfo);
		}
		catch (Exception e)
		{
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/DataStoreContexts/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		return HandleQueryRequest<DataStoreContextEntity, DataStoreContextDto>(DatabaseContext.DataStoreContexts.Include(context => context.Customer),
																					parameters);
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/DataStoreContexts/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<DataStoreContextEntity, DataStoreContextDto>(DatabaseContext.DataStoreContexts.Include(context => context.Customer), id);
	}
	
	/// <inheritdoc />
	[HttpPost("/Api/DataStoreContexts/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] DataStoreContextDto configurationDto)
	{
		return await HandleCreateRequestAsync(DatabaseContext.DataStoreContexts, configurationDto, CreateAction);

		void CreateAction(DataStoreContextEntity entity)
		{
			// ReSharper disable once NullCoalescingConditionIsAlwaysNotNullAccordingToAPIContract
			entity.DataStore ??= DatabaseContext.DataStoreConfigs.Find(entity.DataStoreId)!;
			
			switch (entity.DataStore.Type)
			{
				case DataStoreType.Storage:
					entity.CreateContext();
					return;
				case DataStoreType.Weather:
				default:
					return;
			}
		}
	}
	
	/// <inheritdoc />
	[HttpPatch("/Api/DataStoreContexts/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] DataStoreContextDto configurationDto)
	{
		var entity = DatabaseContext.DataStoreContexts
			.Include(context => context.DataStore)
			.Include(context => context.Customer)
			.FirstOrDefault(element => element.Id == id);
		if (entity == null)
			return GetNotFoundResponse($"DataStoreContext#{id} does not exist");
		return await HandleUpdateRequestAsync(DatabaseContext.DataStoreContexts, entity, configurationDto, UpdateAction);

		void UpdateAction(DataStoreContextEntity updateEntity)
		{
			switch (updateEntity.DataStore.Type)
			{
				case DataStoreType.Storage:
					updateEntity.UpdateContext();
					return;
				default:
					return;
			}
		}
	}

	/// <inheritdoc />
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		return DeleteContext(id);
	}

	/// <inheritdoc cref="Delete"/>
	[HttpDelete("/Api/DataStoreContexts/{id:guid}")]
	public ActionResult<FrontendResponse> DeleteContext(Guid id, bool forceDeleteDb = false)
	{
		return HandleDeleteRequest(DatabaseContext.DataStoreContexts, id, DeleteAction);

		void DeleteAction(DataStoreContextEntity entity)
		{
			// ReSharper disable once NullCoalescingConditionIsAlwaysNotNullAccordingToAPIContract
			entity.DataStore ??= DatabaseContext.DataStoreConfigs.Find(entity.DataStoreId)!;

			switch (entity.DataStore.Type)
			{
				case DataStoreType.Storage:
					entity.RemoveContext(forceDeleteDb);
					return;
				default:
					return;
			}
		}
	}
	
	#endregion
	
	private DataStoreInfo RequestDataStoreContextInfo(DataStoreType dataStoreType)
	{
		var info = dataStoreType switch
		{
			DataStoreType.Storage => Storage.GetContextInfo(),
			_ => throw new ArgumentOutOfRangeException($"DataStore type is not supported {dataStoreType.GetLabel()}")
		};
		
		info.Localize(StringLocalizerFactory.Create("DataStoreType/" + dataStoreType.ToString() + "/Context", ""));
		return info;
	}
}