using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.FrontendDtos.DataStoreContext;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.DataStoreContext.ViewModels;

[ExcludeFromCodeCoverage]
public class DataStoreContextForm
{
	public ViewType ViewType { get; init; }
	public DataStoreContextDto? DataStoreContext { get; init; }
	
	public DataStoreInfo? DataStoreInfo { get; init; }
	
	public Guid? DataStoreId { get; init; }
	
	public DataStoreContextForm(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}