using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Migrations.Postgres;

namespace Levelbuild.Frontend.WebApp.Features.Database.Constants;

/// <summary>
/// Same as <see cref="Levelbuild.Core.EntityInterface.Constants.DatabaseProvider"/>, but extended by the migration project's assembly name.
/// </summary>
public record ExtendedDatabaseProvider : DatabaseProvider
{
	/// <summary>
	/// Name of Postgres extended by the migration project's assembly name.
	/// </summary>
	public new static ExtendedDatabaseProvider Postgres = new(nameof(Postgres), typeof(Marker).Assembly.GetName().Name!);
	
	/// <summary>
	/// Name of SqlServer extended by the migration project's assembly name.
	/// </summary>
	public new static ExtendedDatabaseProvider SqlServer = new(nameof(SqlServer), typeof(Migrations.SqlServer.Marker).Assembly.GetName().Name!);

	/// <summary>
	/// The project's assembly name.
	/// </summary>
	public string Assembly { get; private init; }

	private ExtendedDatabaseProvider(string name, string assembly) : base(name)
	{
		Assembly = assembly;
	}
}