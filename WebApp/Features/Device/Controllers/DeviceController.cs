using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Device;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Device;
using Levelbuild.Frontend.WebApp.Features.Device.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Frontend.WebApp.Features.Device.Controllers;

/// <summary>
/// Controller for user's devices
/// </summary>
public class DeviceController : AdminController<DeviceDto>
{
	/// <inheritdoc />
	public DeviceController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory stringLocalizerFactory, IVersionReader versionReader) : 
		base(logManager, logManager.GetLoggerForClass<DeviceController>(), contextFactory, userManager, stringLocalizerFactory, versionReader)
	{
		
	}
	
	#region Views
	
	/// <summary>
	/// Renders the detail view
	/// </summary>
	/// <param name="userSlug">readable identifier for a specific user that the device belongs to</param>
	/// <param name="slug">readable identifier for a specific device</param>
	/// <param name="id">identifier for a specific device</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Devices/Edit")]
	[HttpGet("/Admin/Users/<USER>/Devices/{slug}")]
	[HttpGet("/Admin/Users/<USER>/{id:guid}")]
	public IActionResult Detail(string userSlug, string? slug, Guid? id)
	{
		DeviceEntity? device = null;
		try
		{
			var user = DatabaseContext.Users
				.Include(user => user.Devices)
				.FirstOrDefault(user => user.Slug == userSlug.ToLower());
			
			if(user == null)
				return new NotFoundObjectResult($"User with slug: {userSlug} could not be found");
			
			if (slug.IsNullOrEmpty())
			{
				device = user.Devices.FirstOrDefault(userDevice => userDevice.Id == id);
				if (device == null)
					return new NotFoundObjectResult($"Device with id: {id} could not be found");
			}
			else
			{
				device = user.Devices.FirstOrDefault(userDevice => userDevice.Slug == slug!.ToLower());
				if (device == null)
					return new NotFoundObjectResult($"Device with slug: {slug} could not be found");
			}
			
		}
		catch (Exception e)
		{
			Logger.Error(e, "Device configuration could not be loaded");
		}
		
		return RenderPartial(new DeviceForm(device!.ToDto()));
	}
	
	#endregion
	
	#region Actions
	
	/// <inheritdoc />
	[HttpGet("/Api/Devices/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		return HandleQueryRequest<DeviceEntity, DeviceDto>(DatabaseContext.Devices, parameters);
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/Devices/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<DeviceEntity, DeviceDto>(DatabaseContext.Devices, id);
	}
	
	/// <inheritdoc />
	[HttpPost("/Api/Devices/")]
	public override async Task<ActionResult<FrontendResponse>> Create(DeviceDto dto)
	{
		return await HandleCreateRequestAsync(DatabaseContext.Devices, dto);
	}
	
	/// <inheritdoc />
	[HttpPatch("/Api/Devices/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, DeviceDto dto)
	{
		return await HandleUpdateRequestAsync(DatabaseContext.Devices, id, dto);
	}
	
	/// <inheritdoc />
	[HttpDelete("/Api/Devices/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		return HandleDeleteRequest(DatabaseContext.Devices, id);
	}
	
	#endregion
}