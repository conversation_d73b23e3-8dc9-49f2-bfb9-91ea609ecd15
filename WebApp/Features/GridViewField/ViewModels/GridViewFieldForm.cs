using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.GridViewField.ViewModels;

[ExcludeFromCodeCoverage]
public class GridViewFieldForm
{
	public ViewType ViewType { get; init; }
	
	public GridViewFieldDto? GridViewField { get; init; }
	
	public Guid? GridViewId { get; init; }
	
	public InputDataType? DataType { get; init; }
	
	public GridViewFieldForm(ViewType viewType = ViewType.Create, InputDataType? dataType = null)
	{
		ViewType = viewType;
		DataType = dataType;
	}
}