@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.GridViewField.ViewModels.GridViewFieldForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("GridViewField", "detail");
	var fieldLocalizer = LocalizerFactory.Create("DataField", "");
	var formName = Model.DataType != null ? $"grid-view-{Model.DataType.ToString()!.ToLower()}-field-form" : "grid-view-field-form";
}
<script type="module" defer>
	const form = document.getElementById('@(formName)')
	const showIfMapping = new Map()

	Page.handleConditionalVisibility(form, showIfMapping)

	const dataField = form.querySelector('[name=dataField]')
	dataField.addEventListener('change', () => {
		if(!dataField.columnValues || Object.keys(dataField.columnValues).length === 0)
			return

		form.querySelector('[name=isRichText]').value = dataField.columnValues.richText === true
	})

</script>
<form-component id="@(formName)" skeleton="@(Model is { ViewType: ViewType.Edit, GridViewField: null })">
	<config-section label="@localizer["sectionConfig"]">
		<div class="form__item">
			<input type="hidden" class="item__value" name="id" value="@Model.GridViewField?.Id"/>
			<input type="hidden" class="item__value" name="dataType" value="@Model.GridViewField?.DataType"/>
			<input-component type="InputDataType.Boolean" name="isRichText" value="@Model.GridViewField?.IsRichText" class="hide"></input-component>
		</div>
		@if (Model.DataType != InputDataType.Annotation)
		{
			<div class="form__item block__item">
				<config-label target="data-type" label="@localizer["dataType"]"></config-label>
				<input-component id="data-type" name="dataTypeTranslated" value="@(Model.GridViewField?.DataTypeTranslated)" readonly="true"></input-component>
			</div>
			<div class="form__item block__item">
				<config-label target="label" label="@localizer["label"]"></config-label>
				<input-component type="InputDataType.Translation" translation-prefix="/GridViewField" id="label" class="item__value" name="label" value="@(Model.GridViewField?.Label)" required="true">
				</input-component>
				<legend>@localizer["labelLegend"]</legend>
			</div>
		}
		<div class="form__item block__item">
			@{
				List<AutocompleteColumnDefinition> autocompleteColumns =
				[
					new(name: "name", label: @fieldLocalizer["Name"], DataType.String),
					new(name: "richText", label: @fieldLocalizer["RichText"], type: DataType.Boolean, hidden: true)
				];
			}
			<config-label target="data-field" label="@localizer[Model.DataType == InputDataType.Annotation ? "blueprintConnection" : "dataField"]"></config-label>
			<autocomplete-component type="InputDataType.String" id="data-field" name="dataField" output-name="dataFieldId" columns="@autocompleteColumns"
			                        value="@(Model.GridViewField?.DataFieldId)" class="item__value" required="true"
			                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}">
			</autocomplete-component>
			<script type="module" defer>
				const form = document.querySelector('#@(formName)')
				let lastLabelValue
				form.querySelector('#data-field').addEventListener('change', (event) => {
					const labelField =  form.querySelector('#label')
					if (event.target.displayValue && labelField && (!labelField.value || labelField.value === lastLabelValue)) {
						labelField.value = event.target.displayValue
						lastLabelValue = labelField.value
					}
				})
			</script>
		</div>
		@if (Model.DataType != InputDataType.Annotation)
		{
			<div class="form__item">
				<config-label target="required" label="@localizer["required"]"></config-label>
				<toggle-component id="required" name="required" value="@Model.GridViewField?.Required"></toggle-component>
			</div>
			<div class="form__item block__item">
				<config-label target="placeholder" label="@localizer["placeholder"]"></config-label>
				<input-component type="InputDataType.Translation" id="placeholder" class="item__value" name="placeholder"
				                 value="@(Model.GridViewField?.Placeholder)" translation-prefix="/GridViewField">
				</input-component>
				<legend>@localizer["placeholderLegend"]</legend>
			</div>
			<div class="form__item block__item">
				<config-label target="defaultValue" label="@localizer["defaultValue"]"></config-label>
				<input-component id="default-value" class="item__value" name="defaultValue" value="@(Model.GridViewField?.DefaultValue)"></input-component>
				<legend>@localizer["placeholderDefaultValue"]</legend>
			</div>
			<div class="form__item block__item">
				<config-label target="help-text" label="@localizer["helpText"]"></config-label>
				<input-component type="InputDataType.Translation" id="help-text" class="item__value" name="helpText"
				                 value="@(Model.GridViewField?.HelpText)" translation-prefix="/GridViewField">
				</input-component>
				<legend>@localizer["helpTextLegend"]</legend>
			</div>
			@* TODO: reenable as soon as UX has decided what to do with it :) *@
			<!--div class="form__item block__item">
			<config-label target="font-color" label="@localizer["fontColor"]"></config-label>
			<input-component type="InputDataType.Color" id="font-color" class="item__value" name="fontColor"
			                 value="@(Model.GridViewField?.FontColor)">
			</input-component>
			</div-->
			<div class="form__item">
				<config-label target="readonly" label="@localizer["readonly"]"></config-label>
				<toggle-component id="readonly" name="readonly" value="@Model.GridViewField?.Readonly"></toggle-component>
			</div>
		}
	</config-section>
</form-component>
<button-component data-action="delete" label="@localizer["deleteButton"]" icon="trash" type="ButtonType.Secondary" color="ColorState.Error"></button-component>