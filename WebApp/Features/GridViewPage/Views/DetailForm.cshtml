@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.GridViewPage.ViewModels.GridViewPageForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("GridViewPage", "detail");
	var referenceTypeLocalizer = LocalizerFactory.Create("GridViewPageReferenceType", "");
	var multiData = true;
}
<script type="module" defer>
	const form = document.getElementById('grid-view-page-form')

	const showIfMapping = new Map()
	showIfMapping.set("allowOpenNewTab", [[{option: "gridViewPageType", compareValue: "Tile"}]]);
	showIfMapping.set("withThousandsSeparators", [[{option: "gridViewPageType", compareValue: "Tile"}]]);
	showIfMapping.set("divider", [[{option: "gridViewPageType", compareValue: "Tile"}]]);
	showIfMapping.set("icon", [[{option: "gridViewPageType", compareValue: "Tile"}]]);
	showIfMapping.set("maxHeight", [[{option: "gridViewPageType", compareValue: "Page"}]]);
	showIfMapping.set("allowCreate", [[{option: "gridViewPageType", compareValue: "Page"}]]);
	showIfMapping.set("allowMaximize", [[{option: "gridViewPageType", compareValue: "Page"}]]);

	Page.handleConditionalVisibility(form, showIfMapping)
	
	const keyFieldAutocomplete = form.querySelector('#key-field')
	const viewAutocomplete = form.querySelector('#embedded-view')
	const filterConfig = form.querySelector('#embedded-filter-config')
	document.getElementById('embedded-page').addEventListener('change', async (event) => {
		if (keyFieldAutocomplete) {
			let currentUrl = keyFieldAutocomplete.getAttribute('url')
			let newUrl = `/Api/DataSources/${Page.getFormData().page.dataSourceId}/EmbeddedPages/${event.target.value}/KeyFields`

			const referenceType = document.getElementById('reference-type')
			const referenceField = document.getElementById('reference-field')
			if (referenceType.value === 'ForeignElement')
				newUrl += '?referenceField=' + referenceField.value

			if (currentUrl !== newUrl)
				keyFieldAutocomplete.clear()
			keyFieldAutocomplete.setAttribute('url', newUrl)
		}

		if (viewAutocomplete) {
			let currentUrl = viewAutocomplete.getAttribute('url')
			let newUrl = `/Api/EmbeddedPages/${event.target.value}/Views`
			if (currentUrl !== newUrl)
				viewAutocomplete.clear()
			viewAutocomplete.setAttribute('url', newUrl)
		}

		const filterConfigUrl = event.target.value ? `/Api/GridViewPageFilters?embeddedPageId=${event.target.value}` : ''
		filterConfig?.setAttribute('url', filterConfigUrl)
		filterConfig?.setAttribute('parent-element-value', form.querySelector('input[name=id]').value)
		
		const filterConfigFieldUrl = event.target.value ? `/Api/EmbeddedPages/${event.target.value}/Fields` : ''
		filterConfig?.setAttribute('field-url', filterConfigFieldUrl)

		await Component.waitForComponentInitialization(filterConfig)
		filterConfig?.clear()
	})
</script>
<form-component id="grid-view-page-form" skeleton="@(Model is { ViewType: ViewType.Edit, GridViewPage: null })">
	<config-section label="@localizer["sectionConfig"]">
		<div class="form__item">
			<input type="hidden" class="item__value" name="id" value="@Model.GridViewPage?.Id"/>
			<input type="hidden" class="item__value" name="gridViewPageType" value="@Model.GridViewPage?.GridViewPageType"/>
		</div>
		<div class="form__item">
			<config-label target="reference-type" label="@localizer["referenceType"]" description="@localizer["referenceTypeLegend"]"></config-label>
			<button-group-component id="reference-type" name="referenceType">
				<button-component label="@referenceTypeLocalizer[GridViewPageReferenceType.Self.ToString()]" value="@GridViewPageReferenceType.Self"></button-component>
				<button-component label="@referenceTypeLocalizer[GridViewPageReferenceType.ForeignElement.ToString()]" value="@GridViewPageReferenceType.ForeignElement"></button-component>
			</button-group-component>
			<script type="module">
				document.getElementById('reference-type').addEventListener('change', (event) => {
					const referenceField = document.getElementById('reference-field')
					const embeddedPage = document.getElementById('embedded-page')
					const embeddedPageUrl = embeddedPage.url
					
					switch (event.target.value) {
						case "Self":
							referenceField.clear()
							let newUrl = `/Api/DataSources/${window.Page.getFormData().page.dataSourceId}/EmbeddedPages?type=MultiData`
							if (embeddedPageUrl !== newUrl) {
								embeddedPage.url = newUrl
								embeddedPage.clear()
							}
							referenceField.readonly = true
							break
						case "ForeignElement":
							if (embeddedPageUrl.indexOf('&referenceField=') === -1) {
								embeddedPage.url = `/Api/DataSources/${window.Page.getFormData().page.dataSourceId}/EmbeddedPages?type=MultiData&referenceField=`
								embeddedPage.clear()
							}
							referenceField.readonly = false
					}
				})
			</script>
		</div>
		<div class="form__item">
			<config-label target="reference-field" label="@localizer["referenceField"]" description="@localizer["referenceFieldLegend"]"></config-label>
			<autocomplete-component type="InputDataType.String" id="reference-field" name="referenceField" output-name="referenceFieldId"
			                        value="@(Model.GridViewPage?.ReferenceFieldId)" class="item__value" required="true" readonly="true"
			                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}">
			</autocomplete-component>
			<script>
				document.getElementById('reference-field').addEventListener('change', (event) => {
					if (document.getElementById('reference-type').value !== "ForeignElement")
						return
					
					const embeddedPage = document.getElementById('embedded-page')
					let oldUrl = embeddedPage.url
					let newUrl = `/Api/DataSources/${window.Page.getFormData().page.dataSourceId}/EmbeddedPages?type=MultiData&referenceField=${event.target.value}`
					if (oldUrl !== newUrl) {
						embeddedPage.url = newUrl
						embeddedPage.clear()
					}
				})
			</script>
		</div>
		<div class="form__item">
			<config-label target="embedded-page" label="@localizer["embeddedPage"]" description="@localizer["embeddedPageLegend"]"></config-label>
			<autocomplete-component type="InputDataType.String" id="embedded-page" name="embeddedPage" output-name="embeddedPageId"
			                        value="@(Model.GridViewPage?.EmbeddedPageId)" class="item__value" required="true" url="/Api/EmbeddedPages"
			                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/Pages/{slug}">
			</autocomplete-component>
		</div>
		<div class="form__item">
			<config-label target="embedded-view" label="@localizer[multiData ? "embeddedView" : "embeddedTab"]" description="@(multiData ? @localizer["embeddedViewLegend"]: "")"></config-label>
			<autocomplete-component type="InputDataType.String" id="embedded-view" name="embeddedView" output-name="embeddedViewId"
			                        value="@(Model.GridViewPage?.EmbeddedViewId)" class="item__value" required="true"
			                        edit-link-url="/Admin/DataStores/{page.DataSource.DataStore.Slug}/Pages/{page.Slug}/Views/{slug}">
			</autocomplete-component>
		</div>
		@if (!multiData)
		{
			<div class="form__item">
				<config-label target="embedded-section" label="@localizer["embeddedSection"]"></config-label>
				<autocomplete-component type="InputDataType.String" id="embedded-section" name="embeddedSection" output-name="embeddedSectionId"
				                        value="@(Model.GridViewPage?.EmbeddedSectionId)" class="item__value" required="true">
				</autocomplete-component>
			</div>
		}
		<div class="form__item">
			<config-label target="key-field" label="@localizer["keyField"]" description="@localizer["keyFieldLegend"]"></config-label>
			<autocomplete-component type="InputDataType.String" id="key-field" name="keyField" output-name="keyFieldId"
			                        value="@(Model.GridViewPage?.KeyFieldId)" class="item__value" required="true"
			                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}">
			</autocomplete-component>
		</div>
		<div class="form__item">
			<config-label target="page-title" label="@localizer["title"]" description="@localizer["titleLegend"]"></config-label>
			<input-component type="InputDataType.Translation" id="page-title" class="item__value" name="title" value="@(Model.GridViewPage?.Title)"
			                 translation-prefix="/GridViewPage">
			</input-component>
		</div>
		<div class="form__item">
			<config-label label="@localizer["maxHeight"]"></config-label>
			<input-component type="InputDataType.Integer" class="item__value" name="maxHeight" value="@(Model.GridViewPage?.MaxHeight)"></input-component>
		</div>
		<div class="form__item">
			<config-label target="page-allow-create" label="@localizer["allowCreate"]"></config-label>
			<toggle-component id="page-allow-create" name="allowCreate" value="@(Model.GridViewPage?.AllowCreate)"></toggle-component>
		</div>
		<div class="form__item">
			<config-label target="page-allow-maximize" description="@localizer["allowMaximizeLegend"]" label="@localizer["allowMaximize"]"></config-label>
			<toggle-component id="page-allow-maximize" name="allowMaximize" value="@(Model.GridViewPage?.AllowMaximize)"></toggle-component>
		</div>

		<div class="form__item">
			<config-label target="tile-allow-open-new-tab" label="@localizer["allowOpenNewTab"]" description="@localizer["allowOpenNewTabLegend"]"></config-label>
			<toggle-component id="tile-allow-open-new-tab" class="item__value" name="allowOpenNewTab" value="@(Model.GridViewPage?.AllowOpenNewTab)">
			</toggle-component>
		</div>
		<div class="form__item">
			<config-label target="tile-with-thousand-separators" label="@localizer["withThousandSeparators"]" description="@localizer["withThousandSeparatorsLegend"]"></config-label>
			<toggle-component id="tile-with-thousand-separators" class="item__value" name="withThousandsSeparators" value="@(Model.GridViewPage?.WithThousandSeparators)">
			</toggle-component>
		</div>
		<div class="form__item">
			<config-label target="tile-divider" label="@localizer["divider"]" description="@localizer["dividerLegend"]"></config-label>
			<input-component type="InputDataType.Integer" id="tile-divider" class="item__value" name="divider" value="@(Model.GridViewPage?.Divider ?? 1)">
			</input-component>
		</div>
		<div class="form__item">
			<config-label target="tile-icon" label="@localizer["icon"]" description="@localizer["iconLegend"]"></config-label>
			<input-component type="InputDataType.Icon" id="tile-icon" class="item__value" name="icon" value="@(Model.GridViewPage?.Icon)">
			</input-component>
		</div>
		
	</config-section>
	<config-section label="@localizer["sectionFilters"]" ignore-overflow>
		<filter-config-component id="embedded-filter-config" create-url="/Api/GridViewPageFilters" parent-element-key="PageId"></filter-config-component>
	</config-section>
</form-component>
<button-component data-action="delete" label="@localizer["deleteButton"]" icon="trash" type="ButtonType.Secondary" color="ColorState.Error"></button-component>