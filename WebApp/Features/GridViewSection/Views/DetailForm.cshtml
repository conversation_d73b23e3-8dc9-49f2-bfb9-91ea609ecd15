@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.GridViewSection.ViewModels.GridViewSectionForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("GridViewSection", "detail");
}
<form-component id="grid-view-section-form" skeleton="@(Model is { ViewType: ViewType.Edit, GridViewSection: null })">
	<config-section label="@localizer["sectionConfig"]">
		<div class="form__item">
			<input type="hidden" class="item__value" name="id" value="@Model.GridViewSection?.Id"/>
		</div>
		<div class="form__item">
			<label for="section-title">@localizer["title"]</label>
			<input-component type="InputDataType.Translation" required="true" id="section-title" class="item__value" name="title"
			                 value="@(Model.GridViewSection?.Title)" translation-prefix="/GridViewSection">
			</input-component>
		</div>
		<div class="form__item">
			<label for="section-show-title">@localizer["showTitle"]</label>
			<toggle-component id="section-show-title" name="showTitle" value="@(Model.GridViewSection?.ShowTitle)"></toggle-component>
		</div>
		<div class="form__item">
			<label for="section-allow-minimize">@localizer["allowMinimize"]</label>
			<toggle-component id="section-allow-minimize" name="allowMinimize" value="@(Model.GridViewSection?.AllowMinimize)"></toggle-component>
		</div>
		<div class="form__item">
			<label for="section-start-minimized">@localizer["startMinimized"]</label>
			<toggle-component id="section-start-minimized" name="startMinimized" value="@(Model.GridViewSection?.StartMinimized)"></toggle-component>
		</div>
	</config-section>
</form-component>
<button-component data-action="delete" label="@localizer["deleteButton"]" icon="trash" type="ButtonType.Secondary" color="ColorState.Error"></button-component>