using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.GridViewText.ViewModels;

[ExcludeFromCodeCoverage]
public class GridViewTextForm
{
	public ViewType ViewType { get; init; }
	
	public GridViewTextType? TextType { get; init; }
	
	public GridViewTextDto? GridViewText { get; init; }
	
	public GridViewTextForm(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}