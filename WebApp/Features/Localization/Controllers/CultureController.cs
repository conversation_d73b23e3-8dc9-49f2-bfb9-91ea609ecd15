using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Localization;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Localization;
using Levelbuild.Frontend.WebApp.Features.Localization.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.Attributes;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.Localization.Controllers;

/// <summary>
/// Controller for the configuration of cultures
/// </summary>
[Feature("Localization")]
public class CultureController : AdminController<CultureDto>
{
	
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="versionReader">injected VersionReader</param>
	public CultureController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : 
		base(logManager, logManager.GetLoggerForClass<CultureController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		
	}

	#region Views

	/// <summary>
	/// Renders the list view with help of the list dto
	/// </summary>
	/// <returns>rendered list view</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Cultures/")]
	public IActionResult List()
	{
		return CachedPartial() ?? RenderPartial();
	}
	
	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Cultures/Create")]
	public IActionResult Create()
	{
		// ReSharper disable once Mvc.ViewNotResolved
		return this.CachedPartial() ?? this.RenderPartial(new CultureForm(DatabaseContext), "List");
	}
	
	/// <summary>
	/// Renders the detail view with help of the culture dto
	/// </summary>
	/// <param name="slug">readable identifier for a specific culture</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Cultures/Edit")]
	[HttpGet("/Admin/Cultures/{slug}")]
	public IActionResult Detail(string? slug)
	{
		if (string.IsNullOrEmpty(slug))
		{
			return this.CachedPartial() ?? this.RenderPartial(new CultureForm(DatabaseContext, ViewType.Edit));
		}

		CultureEntity? entity = DatabaseContext.Cultures.FirstOrDefault(culture => culture.Slug == slug.ToLower());
		if (entity == null)
			throw new ElementNotFoundException($"Culture configuration with slug: {slug} could not be found");

		return this.RenderPartial(new CultureForm(DatabaseContext, ViewType.Edit)
		{
			Culture = entity.ToDto(),
		});
	}

	#endregion
	
	#region Actions

	/// <inheritdoc />
	[HttpGet("/Api/Cultures/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		// include Translations as well as Users to make it possible to count them
		var query = DatabaseContext.Cultures
			.Include(culture => culture.Translations)
			.Include(culture => culture.Users);
		
		return HandleQueryRequest<CultureEntity, CultureDto>(query, parameters);
	}
	
	// TODO: find a better path for the url?
	/// <summary>
	/// Returns all available cultures for use inside an autocomplete
	/// </summary>
	/// <param name="term">search term</param>
	/// <returns></returns>
	[HttpGet("/Api/Cultures/autocomplete")]
	public ActionResult<FrontendResponse> Cultures([FromQuery(Name = "term")] string term)
	{
		var elementList = DatabaseContext.Cultures.ToList().Select(cultureEntity => new AutocompleteDto()
		{
			Value = cultureEntity.Id,
			Label = cultureEntity.ToDto().DisplayName
		}).Where(entry => !entry.Value.Equals("") && (string.IsNullOrEmpty(term) || term.Equals(entry.Value.ToString()) || (entry.Label?.ToString() != null && entry.Label.ToString()!.ToLower().Contains(term.ToLower())))).ToList();
		var elementCount = elementList.Count;
		
		var queryResult = new AutocompleteQueryResultDto()
		{
			Rows = elementList,
			Count = elementCount,
			CountTotal = elementCount
		};
		return GetOkResponse(queryResult);
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/Cultures/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<CultureEntity, CultureDto>(DatabaseContext.Cultures, id);
	}

	/// <inheritdoc />
	[HttpPost("/Api/Cultures/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] CultureDto cultureDto)
	{
		return await HandleCreateRequestAsync(DatabaseContext.Cultures, cultureDto);
	}
	
	/// <inheritdoc />
	[HttpPatch("/Api/Cultures/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] CultureDto configurationDto)
	{
		return await HandleUpdateRequestAsync(DatabaseContext.Cultures, id, configurationDto);
	}
	
	/// <inheritdoc />
	[HttpDelete("/Api/Cultures/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		return HandleDeleteRequest(DatabaseContext.Cultures, id);
	}

	#endregion
}