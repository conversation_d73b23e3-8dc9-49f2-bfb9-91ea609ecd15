using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using Levelbuild.Core.FrontendDtos.Localization;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.Localization.ViewModels;

[ExcludeFromCodeCoverage]
public class CultureForm
{
	public ViewType ViewType { get; init; }

	public CultureDto? Culture { get; init; }
	
	public ICollection<CultureInfo> ExistingCultureInfos { get; init; }

	public CultureForm(CoreDatabaseContext dbContext, ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
		ExistingCultureInfos = dbContext.Cultures.Select(culture => culture.ToInfo()).ToList();
	}
}