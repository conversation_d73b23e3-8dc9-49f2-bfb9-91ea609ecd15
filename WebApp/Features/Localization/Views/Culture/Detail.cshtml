@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminDetailPage
@model Levelbuild.Frontend.WebApp.Features.Localization.ViewModels.CultureForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("Culture", "");
}
<script type="module" defer>
	@if (Model.Culture != null)
	{
		@:Page.setMainPage(`/Admin/DataStores/@(Model.Culture.Slug)`, null)
		@:Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: '/Admin/Cultures' }, { label: '@Model.Culture!.Name', url: `/Admin/Cultures/@Model.Culture.Slug` }])
	}
</script>
<vc:admin-detail-page entity="Culture" route-name="Cultures" model="@Model" view-name="~/Features/Localization/Views/Culture/_DetailForm.cshtml" title="@Model.Culture?.Name" show-default-buttons="@(Model.Culture != null)"></vc:admin-detail-page>