@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminDetailPage
@model Levelbuild.Frontend.WebApp.Features.Localization.ViewModels.TranslationForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("Translation", "");
}
<script type="module" defer>
	@if (Model.Translation != null)
	{
		<text>
			Page.setMainPage(`/Admin/Cultures/@(Model.CultureSlug)/Translations/@(Model.Translation.Id)`, null)
			Page.setBreadcrumbs([
				{ label: '@localizer["list/pageTitle"]', url: '/Admin/Cultures/@(Model.CultureSlug)/Translations' }, 
				{ label: '@Model.Translation.Key', url: `/Admin/Cultures/@(Model.CultureSlug)/Translations/@(Model.Translation.Id)` }
			])
		</text>
	}
</script>
<vc:admin-detail-page entity="Translation" route-name="Translations" model="@Model" view-name="~/Features/Localization/Views/Translation/_DetailForm.cshtml" title="@Model.Translation?.Key" show-default-buttons="@(Model.Translation != null)"></vc:admin-detail-page>