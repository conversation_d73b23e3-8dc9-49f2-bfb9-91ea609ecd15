using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Logging;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.LoggerConfig;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.ViewModels;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.LoggerConfig.Controllers;

/// <summary>
/// Controller for the configuration view of loggers
/// </summary>
public class LoggerConfigController : AdminController<LoggerConfigDto>
{
	/// <inheritdoc />
	public LoggerConfigController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : 
		base(logManager, logManager.GetLoggerForClass<LoggerConfigController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		
	}

	#region Views

	/// <summary>
	/// Renders the list view with help of the list dto
	/// </summary>
	/// <returns>rendered list view</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/LoggerConfigs/")]
	public IActionResult List()
	{
		return this.CachedPartial() ?? this.RenderPartial();
	}

	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/LoggerConfigs/Create")]
	public IActionResult Create()
	{
		return this.CachedPartial() ?? this.RenderPartial(new LoggerConfigForm(), "List");
	}

	/// <summary>
	/// Renders the detail view with help of the logger config dto
	/// </summary>
	/// <param name="slug">readable identifier for a specific logger config</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/LoggerConfigs/Edit")]
	[HttpGet("/Admin/LoggerConfigs/{slug}")]
	public IActionResult Detail(string? slug)
	{
		if (string.IsNullOrEmpty(slug))
		{
			return this.CachedPartial() ?? this.RenderPartial(new LoggerConfigForm(ViewType.Edit));
		}

		LoggerConfigEntity? loggerConfiguration = DatabaseContext.LoggerConfigs.FirstOrDefault(config => config.Slug == slug.ToLower());
		if (loggerConfiguration == null)
			throw new ElementNotFoundException($"LoggerConfig configuration with slug: {slug} could not be found");

		return this.RenderPartial(new LoggerConfigForm(ViewType.Edit)
		{
			LoggerConfig = loggerConfiguration
		});
	}

	#endregion

	#region Actions

	/// <inheritdoc />
	[HttpGet("/Api/LoggerConfigs/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		return HandleQueryRequest<LoggerConfigEntity, LoggerConfigDto>(DatabaseContext.LoggerConfigs, parameters);
	}

	/// <inheritdoc />
	[HttpGet("/Api/LoggerConfigs/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<LoggerConfigEntity, LoggerConfigDto>(DatabaseContext.LoggerConfigs, id);
	}

	/// <inheritdoc />
	[HttpPost("/Api/LoggerConfigs/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] LoggerConfigDto loggerConfigurationDto)
	{
		//TODO: Change to ControllerExtension Call and Discuss LogManager Changes via DTO and not with Entity 
		try
		{
			if (string.IsNullOrEmpty(loggerConfigurationDto.LoggerSource))
				return GetBadRequestResponse("Property name is not valid.");


			if (!IsUniquePropertyValue(DatabaseContext.LoggerConfigs, "LoggerSource", loggerConfigurationDto.LoggerSource))
				throw new Exception($"LoggerConfig with name: {loggerConfigurationDto.LoggerSource} already exists.");

			LoggerConfigEntity loggerConfiguration = LoggerConfigEntity.FromDto(loggerConfigurationDto);

			DatabaseContext.LoggerConfigs.Add(loggerConfiguration);
			await DatabaseContext.SaveChangesAsync();

			if (loggerConfiguration.IsActive)
			{
				LogManager.UpdateLogger(loggerConfiguration);
			}

			return GetOkResponse(loggerConfiguration.ToDto());
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Logger Configuration could not be stored.");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <inheritdoc />
	[HttpPatch("/Api/LoggerConfigs/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] LoggerConfigDto configurationDto)
	{
		try
		{
			if (!string.IsNullOrEmpty(configurationDto.LoggerSource) &&
				!IsUniquePropertyValue(DatabaseContext.LoggerConfigs, "LoggerSource", configurationDto.LoggerSource, id))
				throw new Exception($"LoggerConfig with name: {configurationDto.LoggerSource} already exists.");

			var loggerConfiguration = DatabaseContext.LoggerConfigs.FirstOrDefault(config => config.Id == id);
			if (loggerConfiguration == null)
				return GetNotFoundResponse($"Logger configuration with Id: {configurationDto.Id} could not be found");
			loggerConfiguration.UpdatePartial(configurationDto);
			await DatabaseContext.SaveChangesAsync();

			LogManager.UpdateLogger(loggerConfiguration);

			return GetOkResponse(loggerConfiguration.ToDto());
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Logger Configuration could not be stored.");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <inheritdoc />
	[HttpDelete("/Api/LoggerConfigs/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		try
		{
			var loggerConfiguration = DatabaseContext.LoggerConfigs.FirstOrDefault(config => config.Id == id);
			if (loggerConfiguration == null)
				return GetNotFoundResponse($"Logger Configuration: {id} does not exist.");
			DatabaseContext.LoggerConfigs.Remove(loggerConfiguration);
			DatabaseContext.SaveChanges();

			return GetOkResponse();
		}
		catch (Exception e)
		{
			Logger.Error(e, "Logger Configuration could not be deleted");
			return GetBadRequestResponse(e.Message);
		}
	}

	#endregion
}