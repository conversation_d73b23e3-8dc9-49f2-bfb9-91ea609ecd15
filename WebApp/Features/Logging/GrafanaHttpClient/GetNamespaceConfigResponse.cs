namespace Levelbuild.Frontend.WebApp.Features.Logging.GrafanaHttpClient;

/// <summary>
/// Response of GetNamespaceConfig
/// </summary>
public class GetNamespaceConfigResponse
{
	/// <summary>
	/// The status of the response.
	/// </summary>
	public string? Status { get; set; }
	
	/// <summary>
	/// The dictionary containing the labels for each namespace
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public List<IDictionary<string, string>>? Data { get; set;  }
}