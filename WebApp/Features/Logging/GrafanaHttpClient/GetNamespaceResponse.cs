// ReSharper disable AutoPropertyCanBeMadeGetOnly.Global
// ReSharper disable UnusedAutoPropertyAccessor.Global
namespace Levelbuild.Frontend.WebApp.Features.Logging.GrafanaHttpClient;

/// <summary>
/// Response of GetNamespace
/// </summary>
public class GetNamespaceResponse
{
	/// <summary>
	/// The status of the response
	/// </summary>
	public string? Status { get; set; }
	
	/// <summary>
	/// All possible namespaces
	/// </summary>
	public List<string>? Data { get; set; }
	

	/// <summary>
	/// Initializes a new instance of the <see cref="GetNamespaceResponse"/> class.
	/// </summary>
	/// <param name="status">The status of the response.</param>
	/// <param name="data">The list of all possible namespaces.</param>
	public GetNamespaceResponse(string status, List<string> data)
	{
		Status = status;
		Data = data;
	}
}