using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Shared;

namespace Levelbuild.Frontend.WebApp.Features.Logging.ViewModels;

/// <summary>
/// View model for a single log line
/// </summary>
public class LogLine : EntityDto
{
	/// <summary>
	/// The namespace of the k8s cluster
	/// </summary>
	public string K8SNamespace { get; set; }
	
	/// <summary>
	/// The timestamp of the log line
	/// </summary>
	public DateTime Timestamp { get; set; }
	
	/// <summary>
	/// The loglevel of the log line
	/// </summary>
	public string Level { get; set; }
	
	/// <summary>
	/// The log line content
	/// </summary>
	public string Value { get; set; }

	/// <summary>
	/// Additional tags
	/// </summary>
	public IDictionary<string, string> Tags { get; set; }

	/// <summary>
	/// Constructor for the log line view model
	/// </summary>
	/// <param name="k8SNamespace">The namespace of the k8s cluster</param>
	/// <param name="timestamp">The timestamp of the log line</param>
	/// <param name="level">The loglevel of the log line</param>
	/// <param name="value">The log line content</param>
	/// <param name="tags">Additional tags</param>
	/// <param name="entity">The underlying persistent entity. If set, the entity will be used to generate an id for the log line.</param>
	/// <param name="excludedProperties">Properties of the entity to exclude from the id generation process</param>
	public LogLine(string k8SNamespace, DateTime timestamp, string level, string value, IDictionary<string, string> tags, IPersistentEntity? entity = null, params string[] excludedProperties) : base(entity, excludedProperties)
	{
		K8SNamespace = k8SNamespace;
		Timestamp = timestamp;
		Level = level;
		Value = value;
		Tags = tags;
	}
}