using Levelbuild.Core.FrontendDtos.Shared;

namespace Levelbuild.Frontend.WebApp.Features.Logging.ViewModels;

/// <summary>
/// Provides possible filter options
/// </summary>
public class LogsConfig : IResponseObject
{
	/// <summary>
	/// Constructor for the configuration of logs
	/// </summary>
	/// <param name="labels">All tags grouped by namespace</param>
	/// <param name="namespaceConfigs">All tags grouped by namespace including all possible values</param>
	/// <param name="namespacesFilteredThroughWhitelist">Count of removed namespaces</param>
	public LogsConfig(IDictionary<string,IList<string>> labels, IDictionary<string, IList<IDictionary<string, string>>> namespaceConfigs, int namespacesFilteredThroughWhitelist)
	{
		Labels = labels;
		NamespaceConfigs = namespaceConfigs;
		NamespacesFilteredThroughWhitelist = namespacesFilteredThroughWhitelist;
	}
	
	/// <summary>
	/// All tags grouped by namespace
	/// </summary>
	public IDictionary<string,IList<string>> Labels { get; init; }
	
	/// <summary>
	/// All tags grouped by namespace including all possible values
	/// </summary>
	public IDictionary<string, IList<IDictionary<string, string>>> NamespaceConfigs { get; init; }
	
	/// <summary>
	/// Count of removed namespaces
	/// </summary>
	public int NamespacesFilteredThroughWhitelist { get; init; }
}