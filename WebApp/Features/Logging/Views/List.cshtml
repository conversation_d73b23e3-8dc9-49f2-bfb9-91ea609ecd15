@using System.Text.Json
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@model Levelbuild.Frontend.WebApp.Features.Logging.ViewModels.LogList
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

<!--suppress CssUnresolvedCustomProperty, CssNoGenericFontName -->
<style>
	#log-info-panel .content {
		display: flex;
		flex-direction: column;
		gap: var(--size-spacing-l);
	}

	#log-info-panel .content-fields {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		row-gap:  var(--size-spacing-l);
		column-gap:  var(--size-spacing-m);
	}

	#log-info-panel .output-field {
		display:  flex;
		flex-direction: column;
		gap: var(--size-spacing-m);

		& label {
			font-size: var(--size-text-m);
			color: var(--clr-text-secondary);
		}

		& span {
			font-size: var(--size-text-m);
			color: var(--clr-text-primary-positiv);
		}
	}

	#log-info-panel .output-field:last-child {
		flex-grow: 1;
	}

	#log-info-panel textarea {
		min-height: 10rem;
		resize: none;
		flex-grow: 1;
		outline: none;
		padding: 8px 2px 8px 10px;
		font-family: var(--font-family-default);
		font-size: var(--size-text-m);
		line-height: 1.5em;
		border-radius: var(--size-radius-m);
		background-color: transparent;
		color: var(--cp-clr-text-primary-positiv);
		overflow-y: scroll;
		overscroll-behavior: contain;
	}

	#log-info-panel textarea:disabled {
		cursor: text;
	}

	#log-info-panel textarea:hover {
		-webkit-mask-position: left top;
	}

	#log-info-panel textarea::-webkit-scrollbar-thumb {
		background-color: var(--cp-clr-background-lvl-2);
		border: 0.4rem solid transparent;
		border-radius: var(--size-radius-l);
		background-clip: padding-box;
	}

	#log-info-panel textarea::-webkit-scrollbar-thumb:hover {
		background-color: var(--cp-clr-background-lvl-3);
	}

	#log-info-panel textarea::-webkit-scrollbar {
		width: 12px;
	}

	#log-info-panel textarea::-webkit-scrollbar-corner {
		background: transparent;
	}
</style>

@{
	var localizer = LocalizerFactory.Create("Logging", "");
	var namespaceSlug = ViewContext.RouteData.Values["namespaceSlug"]?.ToString();
	
	List<QueryParamSortingDto> sortings = [new QueryParamSortingDto() { OrderColumn = "timestamp", Direction = SortDirection.Desc }];
	var menuItems = Model.Namespaces != null ? 
		                Model.Namespaces.Select(logNamespace => new MenuItem(logNamespace.DisplayValue, logNamespace.K8SNamespace, null, 
		                                                                     logNamespace.Slug == namespaceSlug, null, logNamespace)).ToList() 
		                : [];
	var selectedNamespace = Model.Namespaces != null ? Model.Namespaces.FirstOrDefault(logNamespace => logNamespace.Slug == namespaceSlug) ?? Model.Namespaces.First() : null;

	var filters = new List<QueryParamFilterDto>
	{
		new() { FilterColumn = "K8SNamespace", CompareValue = selectedNamespace?.K8SNamespace }
	};
}

<vc:basic-menu type="@BasicMenuType.ListFilter" entity="Logging" base-route="/Admin/Logging/-"  route-name="Logging" menu-items="@menuItems"></vc:basic-menu>
<div class="list-view">
	<multi-data-view-component id="multi-view-logs" url="/Api/Logging/" filters="@filters" height="100%" width="100%" sorting="@sortings" limit="50">
		<table-component id="log-list" identity-column="id">
			<table-data-column-component name="timestamp" label="@localizer["timestamp"]" type="InputDataType.DateTime" width="160"></table-data-column-component>
			<table-data-column-component name="level" label="@localizer["level"]" type="InputDataType.Enum" width="80"></table-data-column-component>
			@if (selectedNamespace != null)
			{
				@foreach (var label in selectedNamespace.Labels)
				{
					<table-data-column-component name="@label" label="@localizer[label]" width="160"></table-data-column-component>
				}
			}
			<table-data-column-component name="value" label="@localizer["value"]"></table-data-column-component>
		</table-component>
	</multi-data-view-component>
</div>
<slide-out-component id="log-info-panel" class="side-panel" position="@(Alignment.Right)" heading="@(localizer["showItem"])" anchor icon="circle-info" width="600">
	<div class="content">
		<div class="content-fields">
			<div class="output-field"><label for="log-info-k8SNamespace">@localizer["k8SNamespace"]</label><span id="log-info-k8SNamespace" name="namespace"></span></div>
			<div class="output-field"><label for="log-info-timestamp">@localizer["timestamp"]</label><span><lvl-value-formatter id="log-info-timestamp" name="timestamp" type="@(DataType.DateTime.GetTypeAsString())"></lvl-value-formatter></span></div>
			<div class="output-field"><label for="log-info-level">@localizer["level"]</label><span id="log-info-level" name="level"></span></div>
			@if (selectedNamespace != null)
			{
				@foreach (var label in selectedNamespace.Labels)
				{
					@:<div class="output-field"><label for="log-info-@label">@localizer[label]</label><span id="log-info-@label" name="@label"></span></div>
				}
		}
		</div>
		<div class="output-field"><label for="log-info-value">@localizer["value"]</label><textarea id="log-info-value" name="value" disabled="disabled"></textarea></div>
	</div>
</slide-out-component>


<script type="module" defer>
	@if (selectedNamespace != null)
	{
		@:Page.saveFormData(@(Html.Raw(JsonSerializer.Serialize(selectedNamespace, ConfigHelper.DefaultJsonOptions))))
		@:Page.setMainPage('/Admin/Logging/@(selectedNamespace.K8SNamespace)')
	}
	Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: Page.getMainPageUrl() }])
	Page.setTitle('@localizer["list/pageTitle"]')

	const multiView = document.getElementById('multi-view-logs')
	const tableInstance = multiView.querySelector('#log-list')
	Page.registerEditPanel(document.getElementById('log-info-panel'))
	
	tableInstance.onRowClick = async (record) => {
		Page.editSlideOut.open = true;
		for (let entry in record.data) {
			if (entry === "timestamp" || entry === "value")
				Page.editSlideOut.querySelector("#log-info-" + entry).value = record.data[entry]
			else
				Page.editSlideOut.querySelector("#log-info-" + entry).textContent = record.data[entry]
		}
	}

	//TODO: Build switch as soon as multiple namespaces exist
	@*document.getElementById('logging-menu')?.addEventListener('parent-data:changed', () => multiView.filters = getFilterData())
	
	function getFilterData() {
		return [{
			filterColumn: 'k8SNamespace',
			operator: @(Html.Raw(JsonSerializer.Serialize(QueryParamFilterOperator.Equal, jsonOptions))),
			compareValue: parentPropertyValue,
		}]
	}*@
</script>
