using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.FileUploads;

namespace Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Utils
{
    /// <summary>
    /// Utility class for WOPI file updates that follows the standard file upload flow
    /// </summary>
    public static class DirectWopiFileUpdater
    {
        private static readonly ILogger _logger =
            LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger("DirectWopiFileUpdater");

		/// <summary>
        /// Updates a file in the datastore using the standard file upload flow with a custom DataStoreFileStream
        /// </summary>
        /// <param name="fileRecord">The file record entity</param>
        /// <param name="dataSource">The data source entity</param>
        /// <param name="fileStream">The file stream (already configured with preserveSysFileNameAndType if needed)</param>
        /// <param name="elementId">The element ID (optional, will be extracted from fileRecord if not provided)</param>
        /// <param name="origin">The operation origin</param>
        /// <returns>True if the update was successful, false otherwise</returns>
		private static async Task<bool> UpdateFileDirectlyAsync(
            FileUploadsEntity? fileRecord,
            DataSourceEntity? dataSource,
            DataStoreFileStream? fileStream,
            string elementId,
            DataStoreOperationOrigin origin)
        {
            if (fileRecord == null)
            {
                _logger.LogWarning("UpdateFileDirectlyAsync called with null fileRecord");
                return false;
            }

            if (dataSource == null)
            {
                _logger.LogWarning("UpdateFileDirectlyAsync called with null dataSource");
                return false;
            }

            if (fileStream == null)
            {
                _logger.LogWarning("UpdateFileDirectlyAsync called with null fileStream");
                return false;
            }

            // Extract element ID if not provided
            elementId = GetElementId(fileRecord, elementId);

			try
            {
                // Upload the file to get a temporary file ID
                var fileUploadId = await dataSource.UploadFileAsync(fileStream);
                if (string.IsNullOrEmpty(fileUploadId))
                {
                    _logger.LogWarning("Failed to upload file to get temporary file ID for file {FileName}", fileRecord.FileName);
                    return false;
                }

                // Update the file using the temporary ID
                var updateResult = await dataSource.UpdateFileAsync(elementId, fileUploadId, origin);
                if (updateResult == null)
                {
                    _logger.LogWarning("Failed to update file {FileName} with temporary ID {FileUploadId}", fileRecord.FileName, fileUploadId);
                    return false;
                }

                // Update the file record's revision - always generate a new revision
                fileRecord.Revision = Guid.NewGuid();

                // Update the file record's last modified timestamp
                fileRecord.LastModified = DateTime.UtcNow;

                // Update the filename while preserving the original format
                string newExtension = Path.GetExtension(fileStream.Name);
                string originalFileName = fileRecord.FileName;

                // Determine the new filename based on the original format
                string newFileName = DetermineNewFileName(originalFileName, elementId, newExtension, fileStream.Name);

                // Only update the FileName if it's different
                if (!string.Equals(fileRecord.FileName, newFileName, StringComparison.OrdinalIgnoreCase))
                {
                    fileRecord.FileName = newFileName;
                }

                _logger?.LogInformation("Successfully updated file {FileName} in data source {DataSourceName}", fileRecord.FileName, dataSource.Name);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating file {FileName} in data source {DataSourceName}", fileRecord.FileName, dataSource.Name);
                return false;
            }
        }

		/// <summary>
        /// Updates a file directly at its physical path without using temporary storage
        /// </summary>
        /// <param name="fileRecord">The file record entity</param>
        /// <param name="dataSource">The data source entity</param>
        /// <param name="fileContent">The file content stream</param>
        /// <param name="fileName">The file name</param>
        /// <param name="elementId">The element ID (optional, will be extracted from fileRecord if not provided)</param>
        /// <param name="origin">The operation origin</param>
        /// <param name="preserveSysFileNameAndType">Whether to preserve the original SysFileName and SysFileType values</param>
        /// <returns>True if the update was successful, false otherwise</returns>
        public static async Task<bool> UpdateFileDirectlyAtPhysicalPathAsync(
            FileUploadsEntity? fileRecord,
            DataSourceEntity? dataSource,
            Stream fileContent,
            string fileName,
            string elementId,
            DataStoreOperationOrigin origin,
            bool preserveSysFileNameAndType = false)
        {
			if (fileRecord == null || dataSource == null || fileContent == null || string.IsNullOrEmpty(fileName))
			{
				_logger.LogWarning("UpdateFileDirectlyAtPhysicalPathAsync called with null/empty parameters: " +
					$"fileRecord={fileRecord == null}, dataSource={dataSource == null}, " +
					$"fileContent={fileContent == null}, fileName={string.IsNullOrEmpty(fileName)}");
				return false;
			}

            // Extract element ID if not provided
            elementId = GetElementId(fileRecord, elementId);

			try
            {
                // Get the file from the datastore to find its physical path
                var existingFile = await dataSource.GetFileAsync(fileRecord.FileName);
                if (existingFile == null)
                {
                    _logger.LogWarning("Failed to get existing file {FileName} from data source {DataSourceName}", fileRecord.FileName, dataSource.Name);
                    return false;
                }

                // Determine which filename to use based on preserveSysFileNameAndType flag
                string fileNameToUse = preserveSysFileNameAndType ? existingFile.Name : fileName;

                // Update the file record's revision
                fileRecord.Revision = Guid.NewGuid();

                // Update the file record's last modified timestamp
                fileRecord.LastModified = DateTime.UtcNow;

                // Update the filename while preserving the original format
                string newExtension = Path.GetExtension(fileNameToUse);
                string originalFileName = fileRecord.FileName;
                _logger.LogDebug("Original filename: {FileName}", originalFileName);

                // Determine the new filename based on the original format
                string newFileName = DetermineNewFileName(originalFileName, elementId, newExtension, fileNameToUse);

                // Only update the FileName if it's different
                if (!string.Equals(fileRecord.FileName, newFileName, StringComparison.OrdinalIgnoreCase))
                {
                    fileRecord.FileName = newFileName;
                }

                // Create a DataStoreFileStream
                var preservedFileStream = new DataStoreFileStream(
                    fileNameToUse,
                    DateTime.UtcNow,
                    fileContent,
                    fileContent.Length);

                return await UpdateFileDirectlyAsync(
                    fileRecord,
                    dataSource,
                    preservedFileStream,
                    elementId,
                    origin);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating file directly at physical path for file {FileName} in data source {DataSourceName}",
                    fileRecord.FileName, dataSource.Name);
                return false;
            }
        }

        /// <summary>
        /// Extracts or determines the element ID from the file record
        /// </summary>
        /// <param name="fileRecord">The file record entity</param>
        /// <param name="elementId">The element ID (if already provided)</param>
        /// <returns>The element ID to use</returns>
        private static string GetElementId(FileUploadsEntity? fileRecord, string elementId)
        {
            if (!string.IsNullOrEmpty(elementId))
            {
                return elementId;
            }

            // Try to extract the element ID from the file ID if it contains an underscore
            if (fileRecord.FileName.Contains("_"))
            {
                // The element ID is the part before the first underscore
                return fileRecord.FileName.Split('_')[0];
            }

            // Fall back to using the FileName as the element ID
            return fileRecord.FileName;
        }

        /// <summary>
        /// Determines the new filename based on the original format
        /// </summary>
        /// <param name="originalFileName">The original file name</param>
        /// <param name="elementId">The element ID</param>
        /// <param name="newExtension">The new file extension</param>
        /// <param name="sourceName">The source file name</param>
        /// <returns>The new file name</returns>
        private static string DetermineNewFileName(string originalFileName, string elementId, string newExtension, string sourceName)
        {
            // Check if the original filename is in the format elementId_revisionNumber_data
            bool isFileIdFormat = false;

            if (originalFileName.Contains("_"))
            {
                string[] parts = originalFileName.Split('_');
                if (parts.Length >= 3)
                {
                    isFileIdFormat = true;
                }
            }

            if (isFileIdFormat)
            {
                // Preserve the file ID format but update the extension if needed
                string originalExtension = Path.GetExtension(originalFileName);
                if (string.IsNullOrEmpty(originalExtension))
                {
                    // If there's no extension, keep the original filename as is
                    return originalFileName;
                }

                // Replace the extension while preserving the file ID format
                string nameWithoutExt = originalFileName.Substring(0, originalFileName.Length - originalExtension.Length);
                return nameWithoutExt + newExtension;
            }

            // If the original filename is not in the file ID format, use the new filename
            // but ensure it has the element ID prefix if the original had it
            string newFileNameWithoutExt = Path.GetFileNameWithoutExtension(sourceName);

            if (originalFileName.StartsWith(elementId + "_"))
            {
                // Preserve the element ID prefix
                return elementId + "_" + newFileNameWithoutExt + newExtension;
            }

            // Use the new filename as is
            return newFileNameWithoutExt + newExtension;
        }
    }
}
