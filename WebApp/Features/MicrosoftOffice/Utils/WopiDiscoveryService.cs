using System.Xml.Linq;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Models;
using Microsoft.Extensions.Caching.Memory;

namespace Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Utils
{
    /// <summary>
    /// Service for handling WOPI discovery operations
    /// </summary>
    public class WopiDiscoveryService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<WopiDiscoveryService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly TimeSpan _defaultCacheExpiration;

        // Discovery URL for Office Online
        private const string DiscoveryUrl = "https://onenote.officeapps.live.com/hosting/discovery";

        // Cache keys
        private const string DiscoveryXmlCacheKey = "WopiDiscoveryXml";
        private const string CustomDiscoveryXmlCacheKey = "CustomWopiDiscoveryXml";
        private const string DiscoveryObjectCacheKey = "WopiDiscoveryObject";

        public WopiDiscoveryService(
            IMemoryCache cache,
            ILogger<WopiDiscoveryService> logger,
            IHttpClientFactory httpClientFactory)
        {
            _cache = cache;
            _logger = logger;
            _httpClientFactory = httpClientFactory;
            _defaultCacheExpiration = TimeSpan.FromHours(24); // Cache for 24 hours by default
        }

        /// <summary>
        /// Gets the WOPI discovery object with all actions
        /// </summary>
        /// <param name="forceRefresh">Whether to force a refresh of the cached data</param>
        /// <returns>A WopiDiscovery object containing all actions</returns>
        public async Task<WopiDiscovery> GetDiscoveryInfoAsync(bool forceRefresh = false)
        {
            // Try to get from cache first
            if (!forceRefresh && _cache.TryGetValue(DiscoveryObjectCacheKey, out WopiDiscovery cachedDiscovery))
            {
                return cachedDiscovery!;
            }

            try
            {
                // Get the raw discovery XML
                string xmlString = await FetchRawDiscoveryXmlAsync();

                // Parse the XML into a WopiDiscovery object
                var discovery = ParseDiscoveryXml(xmlString);

                // Cache the discovery object
                var cacheOptions = new MemoryCacheEntryOptions()
                    .SetAbsoluteExpiration(_defaultCacheExpiration)
                    .SetPriority(CacheItemPriority.High);

                _cache.Set(DiscoveryObjectCacheKey, discovery, cacheOptions);

                return discovery;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching WOPI discovery information");
                return CreateDefaultDiscovery();
            }
        }

        /// <summary>
        /// Gets the discovery XML for the standard discovery endpoint
        /// </summary>
        /// <param name="baseUrl">The base URL of the application</param>
        /// <param name="forceRefresh">Whether to force a refresh of the cached data</param>
        /// <returns>The discovery XML as a string</returns>
        public async Task<string> GetDiscoveryXmlAsync(string baseUrl, bool forceRefresh = false)
        {
            // Try to get from cache first
            if (!forceRefresh && _cache.TryGetValue(DiscoveryXmlCacheKey, out string cachedXml))
            {
                return cachedXml;
            }

            // Fetch the raw XML from Microsoft
            string rawXml = await FetchRawDiscoveryXmlAsync();

            // Process the XML (no customization for standard endpoint)
            string processedXml = ProcessDiscoveryXml(rawXml, baseUrl, false);

            // Cache the processed XML
            var cacheOptions = new MemoryCacheEntryOptions()
                .SetAbsoluteExpiration(_defaultCacheExpiration)
                .SetPriority(CacheItemPriority.High);

            _cache.Set(DiscoveryXmlCacheKey, processedXml, cacheOptions);

            return processedXml;

        }

        /// <summary>
        /// Gets the discovery XML for the custom discovery endpoint
        /// </summary>
        /// <param name="baseUrl">The base URL of the application</param>
        /// <param name="forceRefresh">Whether to force a refresh of the cached data</param>
        /// <returns>The custom discovery XML as a string</returns>
        public async Task<string> GetCustomDiscoveryXmlAsync(string baseUrl, bool forceRefresh = false)
        {
            // Try to get from cache first
            if (!forceRefresh && _cache.TryGetValue(CustomDiscoveryXmlCacheKey, out string cachedXml))
            {
                return cachedXml;
            }

            // Fetch the raw XML from Microsoft
            string rawXml = await FetchRawDiscoveryXmlAsync();

            // Process the XML with customization for our endpoint
            string processedXml = ProcessDiscoveryXml(rawXml, baseUrl, true);

            // Cache the processed XML
            var cacheOptions = new MemoryCacheEntryOptions()
                .SetAbsoluteExpiration(_defaultCacheExpiration)
                .SetPriority(CacheItemPriority.High);

            _cache.Set(CustomDiscoveryXmlCacheKey, processedXml, cacheOptions);

            return processedXml;

        }

        /// <summary>
        /// Invalidates all discovery caches
        /// </summary>
        public void InvalidateCache()
        {
            _cache.Remove(DiscoveryXmlCacheKey);
            _cache.Remove(CustomDiscoveryXmlCacheKey);
            _cache.Remove(DiscoveryObjectCacheKey);
        }

        /// <summary>
        /// Fetches the raw discovery XML from Microsoft's WOPI discovery endpoint
        /// </summary>
        /// <returns>The raw discovery XML as a string</returns>
        private async Task<string> FetchRawDiscoveryXmlAsync()
        {
            if (_httpClientFactory == null)
            {
                _logger.LogWarning("HttpClientFactory is null");
                throw new InvalidOperationException("HttpClientFactory is not available");
            }

            // Use IHttpClientFactory to get a client
            var client = _httpClientFactory.CreateClient();
            client.Timeout = TimeSpan.FromSeconds(30); // Set a reasonable timeout

            try
            {
                using (var response = await client.GetAsync(DiscoveryUrl))
                {
                    response.EnsureSuccessStatusCode();
                    string xmlContent = await response.Content.ReadAsStringAsync();
                    return xmlContent;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching raw WOPI discovery XML from {DiscoveryUrl}", DiscoveryUrl);
                throw;
            }

        }

        /// <summary>
        /// Processes the discovery XML to replace template parameters with our specific values
        /// </summary>
        /// <param name="rawXml">The raw XML from Microsoft's discovery endpoint</param>
        /// <param name="baseUrl">The base URL of our application</param>
        /// <param name="isCustom">Whether this is for the custom discovery endpoint</param>
        /// <returns>The processed discovery XML</returns>
        private string ProcessDiscoveryXml(string rawXml, string baseUrl, bool isCustom)
        {
            try
            {
                // Parse the XML
                var xmlDoc = XDocument.Parse(rawXml);

                // Find all action elements
                var actions = xmlDoc.Descendants("action");
                foreach (var action in actions)
                {
                    // Get the urlsrc attribute
                    var urlsrcAttr = action.Attribute("urlsrc");
                    if (urlsrcAttr != null)
                    {
                        string urlsrc = urlsrcAttr.Value;

                        // For custom discovery, we need to ensure the WOPISrc parameter points to our application
                        if (isCustom)
                        {
                            // Prepare the WOPISrc parameter to point to our application
                            string wopiFilesPath = $"{baseUrl}/wopi/files/";

                            // Replace any existing WOPISrc parameter or template
                            if (urlsrc.Contains("WOPISrc="))
                            {
                                // Replace existing WOPISrc parameter
                                urlsrc = System.Text.RegularExpressions.Regex.Replace(
                                    urlsrc,
                                    "WOPISrc=[^&]*",
                                    $"WOPISrc={Uri.EscapeDataString(wopiFilesPath)}");
                            }
                            else if (urlsrc.Contains("<wopisrc=WOPI_SOURCE&>"))
                            {
                                // Replace template parameter
                                urlsrc = urlsrc.Replace("<wopisrc=WOPI_SOURCE&>", $"WOPISrc={Uri.EscapeDataString(wopiFilesPath)}&");
                            }
                            else
                            {
                                // Add WOPISrc parameter if it doesn't exist
                                urlsrc += (urlsrc.Contains("?") ? "&" : "?") + $"WOPISrc={Uri.EscapeDataString(wopiFilesPath)}";
                            }
                        }

                        // Update the urlsrc attribute
                        urlsrcAttr.Value = urlsrc;
                    }
                }

                // Convert back to string
                return xmlDoc.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing WOPI discovery XML");
                return rawXml;
            }
        }

        /// <summary>
        /// Parses the discovery XML into a WopiDiscovery object
        /// </summary>
        /// <param name="xmlString">The discovery XML as a string</param>
        /// <returns>A WopiDiscovery object containing all actions</returns>
        private WopiDiscovery ParseDiscoveryXml(string xmlString)
        {
            var actions = new List<WopiAction>();

            // Parse the XML
            var discoXml = XDocument.Parse(xmlString);

            // Convert the discovery XML into list of WopiAction
            var xapps = discoXml.Descendants("app");
            foreach (var xapp in xapps)
            {
                // Parse the actions for the app
                var xactions = xapp.Descendants("action");
                foreach (var xaction in xactions)
                {
                    actions.Add(new WopiAction()
                    {
                        app = xapp.Attribute("name")?.Value,
                        favIconUrl = xapp.Attribute("favIconUrl")?.Value,
                        checkLicense = xapp.Attribute("checkLicense") != null ? Convert.ToBoolean(xapp.Attribute("checkLicense").Value) : false,
                        name = xaction.Attribute("name")?.Value,
                        ext = (xaction.Attribute("ext") != null) ? xaction.Attribute("ext").Value : String.Empty,
                        progid = (xaction.Attribute("progid") != null) ? xaction.Attribute("progid").Value : String.Empty,
                        isDefault = (xaction.Attribute("default") != null) ? true : false,
                        urlsrc = xaction.Attribute("urlsrc")?.Value,
                        requires = (xaction.Attribute("requires") != null) ? xaction.Attribute("requires").Value : String.Empty
                    });
                }
            }

            return new WopiDiscovery { Actions = actions };
        }

        /// <summary>
        /// Creates a default WopiDiscovery object with embedded actions
        /// </summary>
        /// <returns>A WopiDiscovery object with default actions</returns>
        private WopiDiscovery CreateDefaultDiscovery()
        {

            var discovery = new WopiDiscovery();
            var actions = new List<WopiAction>();

            // Word actions
            actions.Add(new WopiAction
            {
                app = "Word",
                name = "view",
                ext = "docx",
                urlsrc = "https://word-view.officeapps.live.com/wv/wordviewerframe.aspx?<ui=UI_LLCC&><rs=DC_LLCC&><dchat=DISABLE_CHAT&><hid=HOST_SESSION_ID&><sc=SESSION_CONTEXT&><wopisrc=WOPI_SOURCE&><access_token=ACCESS_TOKEN&><showpagestats=PERFSTATS&><IsLicensedUser=BUSINESS_USER&><actnavid=ACTIVITY_NAVIGATION_ID&>",
                isDefault = true
            });

            actions.Add(new WopiAction
            {
                app = "Word",
                name = "edit",
                ext = "docx",
                urlsrc = "https://word-edit.officeapps.live.com/we/wordeditorframe.aspx?<ui=UI_LLCC&><rs=DC_LLCC&><dchat=DISABLE_CHAT&><hid=HOST_SESSION_ID&><sc=SESSION_CONTEXT&><wopisrc=WOPI_SOURCE&><access_token=ACCESS_TOKEN&><showpagestats=PERFSTATS&><IsLicensedUser=BUSINESS_USER&><actnavid=ACTIVITY_NAVIGATION_ID&>",
                isDefault = false,
                requires = "locks,update"
            });

            // WOPI Test actions
            actions.Add(new WopiAction
            {
                app = "Word",
                name = "view",
                ext = "wopitest",
                urlsrc = "https://word-view.officeapps.live.com/wv/wordviewerframe.aspx?<ui=UI_LLCC&><rs=DC_LLCC&><dchat=DISABLE_CHAT&><hid=HOST_SESSION_ID&><sc=SESSION_CONTEXT&><wopisrc=WOPI_SOURCE&><access_token=ACCESS_TOKEN&><showpagestats=PERFSTATS&><IsLicensedUser=BUSINESS_USER&><actnavid=ACTIVITY_NAVIGATION_ID&>",
                isDefault = true
            });

            actions.Add(new WopiAction
            {
                app = "Word",
                name = "edit",
                ext = "wopitest",
                urlsrc = "https://word-edit.officeapps.live.com/we/wordeditorframe.aspx?<ui=UI_LLCC&><rs=DC_LLCC&><dchat=DISABLE_CHAT&><hid=HOST_SESSION_ID&><sc=SESSION_CONTEXT&><wopisrc=WOPI_SOURCE&><access_token=ACCESS_TOKEN&><showpagestats=PERFSTATS&><IsLicensedUser=BUSINESS_USER&><actnavid=ACTIVITY_NAVIGATION_ID&>",
                isDefault = false,
                requires = "locks,update"
            });

            // Excel actions
            actions.Add(new WopiAction
            {
                app = "Excel",
                name = "view",
                ext = "xlsx",
                urlsrc = "https://excel.officeapps.live.com/x/_layouts/xlviewerinternal.aspx?<ui=UI_LLCC&><rs=DC_LLCC&><dchat=DISABLE_CHAT&><hid=HOST_SESSION_ID&><sc=SESSION_CONTEXT&><wopisrc=WOPI_SOURCE&><access_token=ACCESS_TOKEN&><showpagestats=PERFSTATS&><IsLicensedUser=BUSINESS_USER&><actnavid=ACTIVITY_NAVIGATION_ID&>",
                isDefault = true
            });

            actions.Add(new WopiAction
            {
                app = "Excel",
                name = "edit",
                ext = "xlsx",
                urlsrc = "https://excel.officeapps.live.com/x/_layouts/xlviewerinternal.aspx?edit=1&<ui=UI_LLCC&><rs=DC_LLCC&><dchat=DISABLE_CHAT&><hid=HOST_SESSION_ID&><sc=SESSION_CONTEXT&><wopisrc=WOPI_SOURCE&><access_token=ACCESS_TOKEN&><showpagestats=PERFSTATS&><IsLicensedUser=BUSINESS_USER&><actnavid=ACTIVITY_NAVIGATION_ID&>",
                isDefault = false,
                requires = "locks,update"
            });

            // PowerPoint actions
            actions.Add(new WopiAction
            {
                app = "PowerPoint",
                name = "view",
                ext = "pptx",
                urlsrc = "https://powerpoint.officeapps.live.com/p/PowerPointFrame.aspx?<ui=UI_LLCC&><rs=DC_LLCC&><dchat=DISABLE_CHAT&><hid=HOST_SESSION_ID&><sc=SESSION_CONTEXT&><wopisrc=WOPI_SOURCE&><access_token=ACCESS_TOKEN&><showpagestats=PERFSTATS&><IsLicensedUser=BUSINESS_USER&><actnavid=ACTIVITY_NAVIGATION_ID&>",
                isDefault = true
            });

            actions.Add(new WopiAction
            {
                app = "PowerPoint",
                name = "edit",
                ext = "pptx",
                urlsrc = "https://powerpoint.officeapps.live.com/p/PowerPointFrame.aspx?edit=1&<ui=UI_LLCC&><rs=DC_LLCC&><dchat=DISABLE_CHAT&><hid=HOST_SESSION_ID&><sc=SESSION_CONTEXT&><wopisrc=WOPI_SOURCE&><access_token=ACCESS_TOKEN&><showpagestats=PERFSTATS&><IsLicensedUser=BUSINESS_USER&><actnavid=ACTIVITY_NAVIGATION_ID&>",
                isDefault = false,
                requires = "locks,update"
            });

            discovery.Actions = actions;
            return discovery;
        }
    }
}
