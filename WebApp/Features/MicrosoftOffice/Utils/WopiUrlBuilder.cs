using System.Globalization;
using System.Text;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Models;

namespace Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Utils
{
    /// <summary>
    /// Helper class for building Office Online URLs for WOPI integration
    /// </summary>
    public class WopiUrlBuilder
    {
        private readonly string _urlTemplate;
        private readonly string _wopiSrc;
        private readonly string _accessToken;
        private readonly Dictionary<string, string> _parameters;

        /// <summary>
        /// Creates a new instance of the WopiUrlBuilder
        /// </summary>
        /// <param name="urlTemplate">The URL template from the WOPI discovery action</param>
        /// <param name="wopiSrc">The WOPI source URL for the file</param>
        /// <param name="accessToken">The access token for the file</param>
        public WopiUrlBuilder(string urlTemplate, string wopiSrc, string accessToken)
        {
            _urlTemplate = urlTemplate;
            _wopiSrc = wopiSrc;
            _accessToken = accessToken;
            _parameters = new Dictionary<string, string>();

            // Set default parameters using current culture
            string currentCulture = CultureInfo.CurrentCulture.Name;
            SetParameter("ui", currentCulture);
            SetParameter("rs", currentCulture);
            SetParameter("dchat", "DISABLE_CHAT");
            SetParameter("hid", Guid.NewGuid().ToString());
            SetParameter("sc", "SESSION_CONTEXT");
            SetParameter("WOPISrc", Uri.EscapeDataString(wopiSrc));
            SetParameter("access_token", Uri.EscapeDataString(accessToken));
            SetParameter("IsLicensedUser", "1");
            SetParameter("actnavid", Guid.NewGuid().ToString());
        }

        /// <summary>
        /// Sets a parameter for the Office Online URL
        /// </summary>
        /// <param name="name">The parameter name</param>
        /// <param name="value">The parameter value</param>
        /// <returns>The WopiUrlBuilder instance for method chaining</returns>
        public WopiUrlBuilder SetParameter(string name, string value)
        {
            _parameters[name] = value;
            return this;
        }

        /// <summary>
        /// Builds the Office Online URL
        /// </summary>
        /// <returns>The fully constructed Office Online URL</returns>
        public string Build()
        {
            // Start with the URL template
            string url = _urlTemplate;

            // Replace placeholders in the URL template
            url = url.Replace("<ui=UI_LLCC&>", $"ui={_parameters["ui"]}&");
            url = url.Replace("<rs=DC_LLCC&>", $"rs={_parameters["rs"]}&");
            url = url.Replace("<dchat=DISABLE_CHAT&>", $"dchat={_parameters["dchat"]}&");
            url = url.Replace("<hid=HOST_SESSION_ID&>", $"hid={_parameters["hid"]}&");
            url = url.Replace("<sc=SESSION_CONTEXT&>", $"sc={_parameters["sc"]}&");
            url = url.Replace("<wopisrc=WOPI_SOURCE&>", $"WOPISrc={_parameters["WOPISrc"]}&");
            url = url.Replace("<access_token=ACCESS_TOKEN&>", $"access_token={_parameters["access_token"]}&");
            url = url.Replace("<showpagestats=PERFSTATS&>", "");
            url = url.Replace("<IsLicensedUser=BUSINESS_USER&>", $"IsLicensedUser={_parameters["IsLicensedUser"]}&");
            url = url.Replace("<actnavid=ACTIVITY_NAVIGATION_ID&>", $"actnavid={_parameters["actnavid"]}&");

            // Add any additional parameters that weren't in the template
            StringBuilder additionalParams = new StringBuilder();
            foreach (var param in _parameters.Where(p => !url.Contains($"{p.Key}=")))
            {
                additionalParams.Append($"&{param.Key}={param.Value}");
            }

            // Append additional parameters if any
            if (additionalParams.Length > 0)
            {
                url += additionalParams.ToString();
            }

            // Remove any trailing '&' characters
            url = url.TrimEnd('&');

            return url;
        }

        /// <summary>
        /// Creates a builder for a view action
        /// </summary>
        /// <param name="action">The WOPI action from discovery</param>
        /// <param name="wopiSrc">The WOPI source URL for the file</param>
        /// <param name="accessToken">The access token for the file</param>
        /// <returns>A configured WopiUrlBuilder instance</returns>
        public static WopiUrlBuilder CreateViewBuilder(WopiAction action, string wopiSrc, string accessToken)
        {
            return new WopiUrlBuilder(action.urlsrc, wopiSrc, accessToken);
        }

        /// <summary>
        /// Creates a builder for an edit action
        /// </summary>
        /// <param name="action">The WOPI action from discovery</param>
        /// <param name="wopiSrc">The WOPI source URL for the file</param>
        /// <param name="accessToken">The access token for the file</param>
        /// <returns>A configured WopiUrlBuilder instance</returns>
        public static WopiUrlBuilder CreateEditBuilder(WopiAction action, string wopiSrc, string accessToken)
        {
            var builder = new WopiUrlBuilder(action.urlsrc, wopiSrc, accessToken);

            // Add any edit-specific parameters here

            return builder;
        }
    }
}
