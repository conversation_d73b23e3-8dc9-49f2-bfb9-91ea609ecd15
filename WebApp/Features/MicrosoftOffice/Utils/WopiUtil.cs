using Microsoft.Extensions.Caching.Memory;

namespace Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Utils
{
    public static class WopiUtil
    {
		
		/// <summary>
        /// Validates the WOPI Proof on an incoming WOPI request
        /// </summary>
        public async static Task<bool> ValidateWopiProof(HttpContext context, IMemoryCache memoryCache)
        {
			return true;
        }
		
    }

    /// <summary>
    /// Contains valid WOPI response headers
    /// </summary>
    public class WopiResponseHeaders
    {
        // WOPI Header Constants
        public const string LOCK = "X-WOPI-Lock";
        public const string LOCK_FAILURE_REASON = "X-WOPI-LockFailureReason";
        public const string ITEM_VERSION = "X-WOPI-ItemVersion";
    }

    /// <summary>
    /// Contains valid WOPI request headers
    /// </summary>
    public class WopiRequestHeaders
    {
        // WOPI Header Constants
        public const string LOCK = "X-WOPI-Lock";
        public const string OLD_LOCK = "X-WOPI-OldLock";
        public const string OVERRIDE = "X-WOPI-Override";
	}
	
	/// <summary>
	/// Represents a WOPI request
	/// </summary>
	public class WopiRequest
	{
		public WopiRequestType RequestType { get; set; }
		public string AccessToken { get; set; }
		public string Id { get; set; }
		public string Extension { get; set; }
		
		public Guid DataSourceId { get; set; }
	}

	/// <summary>
	/// Enum of WOPI request types
	/// </summary>
	public enum WopiRequestType
	{
		None,
		CheckFileInfo,
		GetFile,
		Lock,
		GetLock,
		RefreshLock,
		Unlock,
		UnlockAndRelock,
		PutFile,
		PutUserInfo
	}
	
	    /// <summary>
    /// Utility class for handling Microsoft Office file extensions
    /// </summary>
    public static class OfficeFileExtensions
    {
        /// <summary>
        /// Array of supported Microsoft Office document extensions (without the dot)
        /// </summary>
        public static readonly string[] SupportedOfficeExtensions = { "doc", "docx", "xls", "xlsx", "ppt", "pptx" };

        /// <summary>
        /// Array of Word document extensions (without the dot)
        /// </summary>
        public static readonly string[] WordExtensions = { "doc", "docx" };

        /// <summary>
        /// Array of Excel document extensions (without the dot)
        /// </summary>
        public static readonly string[] ExcelExtensions = { "xls", "xlsx", "csv" };

        /// <summary>
        /// Array of PowerPoint document extensions (without the dot)
        /// </summary>
        public static readonly string[] PowerPointExtensions = { "ppt", "pptx" };

        /// <summary>
        /// Checks if the given file extension is a supported Office document type
        /// </summary>
        /// <param name="extension">The file extension (with or without the dot)</param>
        /// <returns>True if the extension is supported, false otherwise</returns>
        public static bool IsSupportedOfficeExtension(string extension)
        {
            if (string.IsNullOrEmpty(extension))
                return false;

            // Remove the dot if present
            string ext = extension.TrimStart('.').ToLower();
            return SupportedOfficeExtensions.Contains(ext);
        }
	}
}
