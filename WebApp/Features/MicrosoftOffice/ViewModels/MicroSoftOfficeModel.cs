using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Dto;
using System.IO;
using System.Linq;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Utils;

namespace Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.ViewModels;

/// <summary>
/// Model for Microsoft Office integration
/// </summary>
[ExcludeFromCodeCoverage]
public class MicrosoftOfficeModel
{
	/// <summary>
	/// The authentication code for WOPI access
	/// </summary>
	public string WopiCode { get; set; }

	/// <summary>
	/// The file identifier
	/// </summary>
	public string FileId { get; set; }

	/// <summary>
	/// The data source identifier
	/// </summary>
	public Guid DataSourceId { get; set; }

	/// <summary>
	/// The file name
	/// </summary>
	public string FileName { get; set; }

	/// <summary>
	/// The complete Office Online action URL including the WOPISrc parameter
	/// </summary>
	public string ActionUrl { get; set; }

	/// <summary>
	/// The access token time-to-live in milliseconds since epoch (January 1, 1970)
	/// </summary>
	public long AccessTokenTtl { get; set; }

	/// <summary>
	/// Gets the file extension from the File property
	/// </summary>
	public string FileExtension => Path.GetExtension(FileName ?? string.Empty).TrimStart('.');

	/// <summary>
	/// Checks if the file is a supported Office document type
	/// </summary>
	public bool IsOfficeDocument
	{
		get
		{
			return OfficeFileExtensions.IsSupportedOfficeExtension(FileExtension);
		}
	}

	/// <summary>
	/// Initializes a new instance of the MicrosoftOfficeModel class
	/// </summary>
	public MicrosoftOfficeModel()
	{
		// Initialize properties
		WopiCode = string.Empty;
		FileId = string.Empty;
		FileName = string.Empty;
		ActionUrl = string.Empty;
	}
}