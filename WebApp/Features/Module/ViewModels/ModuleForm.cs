using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Module;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;

namespace Levelbuild.Frontend.WebApp.Features.Module.ViewModels;

[ExcludeFromCodeCoverage]
public class ModuleForm
{
	public ViewType ViewType { get; init; }
	
	public ModuleDto? Module { get; init; }
	
	public ModuleForm(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}