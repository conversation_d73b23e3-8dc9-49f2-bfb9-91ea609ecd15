@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@model Levelbuild.Frontend.WebApp.Features.Module.ViewModels.ModuleForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("Module", "");
	List<MenuItem> menuItems =
	[
		new("basic", "BasicSettings", "screwdriver-wrench"),
		new("userlane", "UserlaneTypes", "bars") 
		{
			Items = new List<MenuItem>
			{
				new("tests", "Tests", "vial"),
				new("tours", "Tours", "stairs")
			}
		}
	];

	List<MenuInfo> menuInfos =
	[
		MenuInfo.GetLinkInstance("info-dataStore-link", "dataStore", Model.Module?.DataStore?.Name ?? "", $"/Admin/DataStores/{Model.Module?.DataStore?.Slug}"),
	];

	string currentMenu = ViewData["targetMenu"] == null ? "BasicSettings" : ViewData["targetMenu"]!.ToString()!;
}
<script type="module" defer>
	@if (Model.Module != null)
	{
		<text>
	Page.setMainPage(`/Admin/DataStores/@(Model.Module.DataStore?.Slug)/Modules/@(Model.Module.Slug)`, '@(currentMenu)')
	Page.setBreadcrumbs([
		{ label: '@localizer["list/pageTitle"]', url: `/Admin/DataStores/@(Model.Module!.DataStore?.Slug)/Modules` },
		{ label: '@(Model.Module?.Name)', url: `/Admin/DataStores/@(Model.Module!.DataStore?.Slug)/Modules/@(Model.Module.Slug)` }
	], true)
	</text>
	}
</script>
<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="Module" route-name="Modules" menu-items="@menuItems" menu-infos="@menuInfos" skeleton="@(Model.Module == null)" width="250"></vc:basic-menu>
<vc:admin-detail-page model="@Model" entity="Module" route-name="Modules" title="@(Model.Module?.Name)" menu-item="@currentMenu" show-default-buttons="@(Model.Module != null)"></vc:admin-detail-page>