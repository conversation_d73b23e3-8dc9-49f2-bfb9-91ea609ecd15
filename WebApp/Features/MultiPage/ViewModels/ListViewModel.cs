using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

namespace Levelbuild.Frontend.WebApp.Features.MultiPage.ViewModels;

/// <summary>
/// Dto for list view
/// </summary>
public class ListViewModel
{
	/// <summary>
	/// Dto of the page view configuration
	/// </summary>
	public required ListViewDto View { get; init; }
	
	/// <summary>
	/// Page DTO which is the parent element of the view 
	/// </summary>
	public MultiDataPageDto? Page => (MultiDataPageDto?)View.Page;

	/// <summary>
	/// Display this view with an embedded appearance? 
	/// </summary>
	public bool Embedded { get; init; }
	
	/// <summary>
	/// Configuration of the embedded container
	/// </summary>
	public GridViewPageDto? GridViewPage { get; init; }
}