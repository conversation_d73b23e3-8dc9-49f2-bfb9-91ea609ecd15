@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Microsoft.IdentityModel.Tokens
@model Levelbuild.Frontend.WebApp.Features.MultiPage.ViewModels.ListViewModel
@inject IExtendedStringLocalizerFactory LocalizerFactory

@if (Model.View.Columns is { Count: > 0 })
{
	var expertMode = Model.View.ExpertMode ?? false;
	<table-component embedded="@Model.Embedded" identity-column="id" allow-favorite="@(Model.Page?.DataSource?.Favor ?? false)" allow-inactive="@(Model.Page?.DataSource?.Inactive ?? false)" 
	                 clickable="@(Model.Page?.DetailPage != null)" expert-mode="@(expertMode)">
		<table-select-column-component></table-select-column-component>
		@if (Model.View.Page?.DataSource?.Workflows?.Count > 0)
		{
			<table-workflow-column-component name="workflow"></table-workflow-column-component>
		}
		@if (Model.View.ShowPreview == true)
		{
			<table-thumbnail-column-component name="thumbnail"></table-thumbnail-column-component>
		}
		@{
			var columnNumber = 1;
		}
		@foreach (var column in Model.View.Columns.Where(column => column.Field != null).OrderBy(column => column.Position))
		{
			var fieldLocalizer = LocalizerFactory.Create("ListViewColumn", "", true);
			<table-data-column-component
				label="@(fieldLocalizer[!column.Label.IsNullOrEmpty() ? column.Label! : column.Field!.Name!])"
				name="@(column.Field!.Name!)"
				type="@(InputDataType?)(column.Field.FieldType == DataFieldType.LookupField ? column.Field.LookupDisplayField?.Type : column.Field.Type)"
				sign="@(column.Field.FieldType == DataFieldType.LookupField ? column.Field.LookupDisplayField?.Sign : column.Field.Sign)"
				multi-value="@(column.Field.Multi ?? false)"
				decimal-places="@(column.Field.FieldType == DataFieldType.LookupField ? column.Field.LookupDisplayField?.DecimalPlaces : column.Field.DecimalPlaces)"
				with-thousand-separators="@(column.Field.Type == DataType.Integer || column.Field.Type == DataType.Double || column.Field.Type == DataType.Long)"
				rich-text="@(column.Field.RichText.HasValue && column.Field.RichText.Value)"
				sticky="@(expertMode && columnNumber == Model.View.StickyColumnCount)"
				hidden="@(expertMode && !(column.Display ?? false))">
			</table-data-column-component>
			columnNumber++;
		}
	</table-component>
}
