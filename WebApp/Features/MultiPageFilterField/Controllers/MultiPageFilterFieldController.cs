using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Page.MultiData;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.MultiPageFilterField.ViewModels;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.Page.Controllers;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.MultiPageFilterField.Controllers;

/// <summary>
/// Controller for the configuration view of list view columns
/// </summary>
public class MultiPageFilterFieldController : AdminController<MultiPageFilterFieldDto>
{
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="versionReader">injected VersionReader</param>
	public MultiPageFilterFieldController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : 
		base(logManager, logManager.GetLoggerForClass<MultiPageFilterFieldController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		
	}
	
	#region Views
	
	/// <summary>
	/// Renders the detail (edit) view
	/// </summary>
	/// <param name="dataStoreSlug">readable identifier for a specific datastore</param>
	/// <param name="pageSlug">readable identifier for a specific culture</param>
	/// <param name="filterFieldGuid">readable identifier for a specific filter field</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/MultiPageFilterFields/Edit")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Pages/{pageSlug}/FilterFields/{filterFieldGuid:guid}")]
	public IActionResult Detail(string? dataStoreSlug, string? pageSlug, Guid? filterFieldGuid)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(pageSlug) || !filterFieldGuid.HasValue)
		{
			return CachedPartial() ?? RenderPartial(new MultiPageFilterFieldForm());
		}

		Request.RouteValues.Add("menu", "FilterFields");
		var filterFieldEntity = DatabaseContext.MultiPageFilterFields
			.Include(filterField => filterField.Field)
			.FirstOrDefault(filterField => filterField.Id == filterFieldGuid);
		
		if (filterFieldEntity == null)
			throw new ElementNotFoundException($"FilterField configuration with id: {filterFieldGuid} could not be found");
		return RenderPartial(
			new MultiPageFilterFieldForm()
			{
				FilterField = filterFieldEntity.ToDto()
			},
			"~/Features/Page/Views/Detail.cshtml", AdminPageController.GetPageFormByType(DatabaseContext, PageType.MultiData, filterFieldEntity.PageId)
		);
	}
	
	#endregion
	
	#region Actions
	
	/// <inheritdoc />
	[HttpGet("/Api/MultiPageFilterFields/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		try
		{
			// include Field in order to get Field.Name
			var query = DatabaseContext.MultiPageFilterFields
				.Include(filterFields => filterFields.Field);
			
			var queryResult = GetQueryResult<MultiPageFilterFieldEntity, MultiPageFilterFieldDto>(query, parameters);
			
			// check for cols without position
			var defectCols = queryResult.Rows.Where(filterFields => filterFields.Position == null).ToList();
			if (defectCols.Count > 0)
			{
				ReorderSortings(defectCols.FirstOrDefault()!.PageId);
				
				// select again
				queryResult = GetQueryResult<MultiPageFilterFieldEntity, MultiPageFilterFieldDto>(query, parameters);
			}
			
			var json = JsonSerializer.SerializeToElement(queryResult, HeaderSerializer);
			return GetOkResponse(ServerResponsePayload.FromJson(json)); }
		catch (Exception e)
		{
			var message = $"{nameof(MultiPageFilterFieldEntity)} list could not be loaded.";
			Logger.Error(e, "{Message} Stacktrace: {Exception}", message, e);
			
			return GetBadRequestResponse(message);
		}
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/MultiPageFilterFields/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		// include Field in order to get Field.Name
		var query = DatabaseContext.MultiPageFilterFields
			.Include(filterFields => filterFields.Field);
		
		return HandleGetRequest<MultiPageFilterFieldEntity, MultiPageFilterFieldDto>(query, id);
	}
	
	/// <inheritdoc />
	[HttpPost("/Api/MultiPageFilterFields/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] MultiPageFilterFieldDto configurationDto)
	{
		// calculate position
		configurationDto.Position = await DatabaseContext.MultiPageFilterFields.CountAsync(filterFields => filterFields.PageId == configurationDto.PageId) + 1;
		
		if (await DatabaseContext.MultiPageFilterFields.AnyAsync(entity => entity.FieldId == configurationDto.FieldId && entity.PageId == configurationDto.PageId))
			return GetBadRequestResponse(
				$"Sorting with field id {configurationDto.FieldId} and list view id {configurationDto.PageId} already exists");
		
		return await HandleCreateRequestAsync(DatabaseContext.MultiPageFilterFields, configurationDto, entity => DatabaseContext.Pages.Find(entity.PageId)?.Touch());
	}
	
	/// <inheritdoc />
	[HttpPatch("/Api/MultiPageFilterFields/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] MultiPageFilterFieldDto configurationDto)
	{
		var entity = await DatabaseContext.MultiPageFilterFields.Include(field => field.Page).FirstOrDefaultAsync(field => field.Id == id);
		var oldPosition = entity?.Position;
		var result = await HandleUpdateRequestAsync(DatabaseContext.MultiPageFilterFields, id, configurationDto);
		if (entity == null || result.Result is not OkObjectResult) 
			return result;
		
		if(configurationDto.Position != null && oldPosition != configurationDto.Position)
			ReorderSortings(entity.PageId, entity.Id, oldPosition, configurationDto.Position);
			
		// touch page
		entity.Page?.Touch();
		return result;
	}
	
	/// <inheritdoc />
	[HttpDelete("/Api/MultiPageFilterFields/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		var entity = DatabaseContext.MultiPageFilterFields.Include(field => field.Page).FirstOrDefault(field => field.Id == id);
		var result = HandleDeleteRequest(DatabaseContext.MultiPageFilterFields, id);
		if (entity == null || result.Result is not OkObjectResult) 
			return result;
		
		ReorderSortings(entity.PageId);
			
		// touch page
		entity.Page?.Touch();
		return result;
	}
	
	#endregion

	private void ReorderSortings(Guid pageId, Guid? updatedElementId = null, int? oldPosition = null, int? newPosition = null)
	{
		var queryable = DatabaseContext.MultiPageFilterFields.Where(filterFields => filterFields.PageId == pageId).OrderBy(filterFields => filterFields.Position);
		
		// if a specific element position gets updated, this position comes first!
		if (updatedElementId != null)
		{
			var changedElementFirst = oldPosition > newPosition;
			queryable = queryable.ThenBy(filterFields => filterFields.Id == updatedElementId.Value ? changedElementFirst ? 0 : 2 : 1);
		}

		var columns = queryable.ToList();
		foreach (var (filterFields, index) in columns.Select((item, index) => (item, index)))
		{
			filterFields.Position = index+1;
		}
		DatabaseContext.SaveChanges();
	}
}