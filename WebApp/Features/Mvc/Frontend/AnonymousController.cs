using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Authorization;
using ILogger = Serilog.ILogger;

namespace Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;

/// <summary>
/// Base class for all controllers that don't require authentication/authorization.
/// </summary>
[AllowAnonymous]
public abstract class AnonymousController : FrontendController
{
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="logger"></param>
	/// <param name="versionReader"></param>
	protected AnonymousController(ILogManager logManager, ILogger logger, IVersionReader versionReader) : base(logManager, logger, versionReader)
	{
		// Nothing for now.
	}
}