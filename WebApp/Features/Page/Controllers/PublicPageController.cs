using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Annotations;
using Levelbuild.Core.FrontendDtos.CachedDeepZoom;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.Page.MultiData;
using Levelbuild.Entities.Features.Page.SingleData;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.MultiPage.Controllers;
using Levelbuild.Frontend.WebApp.Features.MultiPage.ViewModels;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Services.Files;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NGuid;

namespace Levelbuild.Frontend.WebApp.Features.Page.Controllers;

/// <summary>
/// Controller for the user view of pages
/// </summary>
public class PublicPageController : FrontendController
{
	private readonly CoreDatabaseContext _databaseContext;
	
	private readonly IDeepZoomHelperService _deepZoomHelperService;
	
	private IExtendedStringLocalizerFactory _stringLocalizerFactory;

	/// <inheritdoc />
	public PublicPageController(ILogManager logManager, IVersionReader versionReader, IDbContextFactory<CoreDatabaseContext> contextFactory,
								IDeepZoomHelperService deepZoomHelperService, IExtendedStringLocalizerFactory stringLocalizerFactory) : base(
		logManager, logManager.GetLoggerForClass<PublicPageController>(), versionReader)
	{
		_databaseContext = contextFactory.CreateDbContext();
		_deepZoomHelperService = deepZoomHelperService;
		_stringLocalizerFactory = stringLocalizerFactory;
	}

	/// <summary>
	/// Renders a configured page
	/// </summary>
	/// <param name="pageId">optional pageId</param>
	/// <param name="pageSlug">readable identifier for a specific page</param>
	/// <param name="elementId">optional id of the element to display inside the page</param>
	/// <param name="pageViewSlug">optional readable identifier of the page view to render</param>
	/// <param name="embeddedPageId">optional embedded pageId</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	/// TODO: split into separate actions for different use-cases 
	[ExcludeFromCodeCoverage]
	[HttpGet("/Api/Pages/{pageId}/Render")]
	[HttpGet("/Public/Pages/{pageSlug}/Create")]
	[HttpGet("/Public/Pages/{pageSlug}/{elementId?}/EmbeddedPages/{embeddedPageId?}")]
	[HttpGet("/Public/Pages/{pageSlug}/{elementId?}/EmbeddedPages/{embeddedPageId?}/Create")]
	[HttpGet("/Public/Pages/{pageSlug}/{elementId?}/{pageViewSlug=overview}")]
	public IActionResult Visualization(Guid? pageId, string? pageSlug, string? elementId, string? pageViewSlug, Guid? embeddedPageId)
	{
		if (string.IsNullOrEmpty(pageSlug) && pageId == null)
			throw new Exception("either pageSlug or pageId is required in order to render a page");

		GridViewPageEntity? gridViewPageInstance = embeddedPageId != null
													   ? _databaseContext.GridViewPages
														   .Include(page => page.EmbeddedPage)
														   .Include(page => page.KeyField)
														   .Include(page => page.ReferenceField)
														   .FirstOrDefault(page => page.Id == embeddedPageId)
													   : null;
		PageEntity? mainPageInstance = pageId != null
										   ? _databaseContext.Pages.Find(pageId)
										   : _databaseContext.Pages.FirstOrDefault(
											   page => page.Slug == (string.IsNullOrEmpty(pageSlug) ? "" : pageSlug.ToLower()));
		if (mainPageInstance == null)
			throw new ElementNotFoundException(pageId != null
												   ? $"Page with id: {pageId} could not be found"
												   : $"Page with slug: {pageSlug} could not be found");

		PageEntity pageInstance = gridViewPageInstance?.EmbeddedPage ?? mainPageInstance;

		if (IsPartialRequest() && elementId == null)
		{
			var eTag = VersionReader.GetSemanticVersion() + ":" + pageInstance.Revision + ":" + CultureInfo.CurrentCulture;
			var requestTag = Request.Headers.IfNoneMatch;
			if (eTag == requestTag)
				return StatusCode(304);

			Response.Headers.CacheControl = "max-age=0";
			Response.Headers.ETag = eTag;
		}

		switch (pageInstance.Type)
		{
			case PageType.Create:
				CreatePageDto? createPage = _databaseContext.CreatePages
					.Include(page => page.DataSource)
						.ThenInclude(dataSource => dataSource.Fields)
					.AsSplitQuery()
					.FirstOrDefault(page => page.Id == pageInstance.Id)?.ToDto();

				if (createPage == null)
					throw new ElementNotFoundException($"CreatePage with Id: {pageInstance.Id} could not be found");

				var gridView = _databaseContext.GridViews
					.Include(view => view.Sections)
						.ThenInclude(section => section.Fields)
							.ThenInclude(field => field.DataField)
								.ThenInclude(dataField => dataField!.Columns)
									.ThenInclude(column => column.DisplayField)
					.Include(view => view.Sections)
						.ThenInclude(section => section.Fields)
							.ThenInclude(field => field.DataField)
								.ThenInclude(dataField => dataField!.LookupDisplayField)
					.Include(view => view.Sections)
						.ThenInclude(section => section.Fields)
							.ThenInclude(field => field.DataField)
								.ThenInclude(dataField => dataField!.Filters)
									.ThenInclude(filter => filter.FilterField)
					.Include(view => view.Sections)
						.ThenInclude(section => section.Texts)
					.AsSplitQuery()
					.FirstOrDefault(gridView => gridView.Id == createPage.DefaultViewId);

				createPage.CreateForm = gridView?.ToDto();

				if (createPage.CreateForm == null)
					throw new ElementNotFoundException($"GridView with Id: {createPage.DefaultViewId} could not be found");

				return PartialView("~/Features/SinglePage/Views/CreatePageVisualization.cshtml", createPage);
			case PageType.SingleData:
				SingleDataPageEntity? singlePageEntity;
				DataStoreElement? elementInstance = null;
				CachedDeepZoomDto? deepZoomInfo = null;
				AnnotationSourceDto? annotationSource = null;
				string? blueprintSourceId = null;
				IList<WorkflowInfoDto>? workflowInfos = null;
				if (elementId != null)
				{
					singlePageEntity = _databaseContext.SingleDataPages
						.Include(page => page.Views)
						.Include(page => page.DataSource)
							.ThenInclude(dataSource => dataSource.Fields)
							.ThenInclude(field => field.LookupDisplayField)
						.Include(page => page.DataSource)
							.ThenInclude(dataSource => dataSource.DataStore)
						.Include(page => page.DataSource.DefaultDetailPage)
						.Include(page => page.DataSource.Workflows)
							.ThenInclude(workflow => workflow.Nodes)
						.Include(page => page.HeaderElements)
						.AsSplitQuery()
						.FirstOrDefault(page => page.Id == pageInstance.Id);

					if (singlePageEntity == null)
						throw new ElementNotFoundException($"SingleDataPage with Id: {pageInstance.Id} could not be found");

					var lookupFields = singlePageEntity.DataSource.Fields.Where(field => field.LookupDisplayField != null).ToList();
					var queryFields = lookupFields.Select(field => new DataStoreQueryField(field.Name + "." + field.LookupDisplayField!.Name)).ToList();

					var virtualFields =
						singlePageEntity.DataSource.Fields.Where(field => field is
						{
							FieldType: DataFieldType.VirtualField, HasVirtualData: true
						}).ToList();
					foreach (var field in virtualFields)
					{
						if (field.Type == DataType.Guid)
						{
							var queryName = field.VirtualDataStoreQueryName;
							if (queryName == null)
								continue;
							var queryParts = queryName.Split('.');
							queryParts[^1] = "Id";
							var queryId = string.Join('.', queryParts);
			
							queryFields.Add(new DataStoreQueryField(queryName, field.Name+".Name"));
							queryFields.Add(new DataStoreQueryField(queryId, field.Name+".Id"));
						} else if (field.VirtualDataStoreQueryName != null)
							queryFields.Add(new DataStoreQueryField(field.VirtualDataStoreQueryName, field.Name));
					}

					// Remove duplicate entries, since Storage may crash otherwise
					queryFields = queryFields.GroupBy(queryField => queryField.Alias ?? queryField.Name).Select(x => x.First()).ToList();

					elementInstance = pageInstance.DataSource.GetElement(elementId, queryFields);
					if (elementInstance == null)
						throw new Exception($"Element with id: {elementId} could not be found inside DataSource {singlePageEntity.DataSource.Name}");
					
					// load blueprint info if needed
					deepZoomInfo = pageInstance.DataSource.Type == DataSourceType.Blueprint && elementInstance.FileInfo != null ?
									   _databaseContext.DeepZoomImages.FirstOrDefault(image => image.FileId == elementInstance.FileInfo.Id)?.ToDto() : null;
					
					// if we have a blueprint now, try to load the available blueprint annotation types
					if (deepZoomInfo != null && pageInstance.DataSource.AnnotationSourceId != null)
					{
						blueprintSourceId = pageInstance.DataSourceId.ToString();
						annotationSource = BuildAnnotationSourceDto(_databaseContext, pageInstance.DataSource);
					}

					// if we're inside an Annotation source, loading the blueprint info is a bit more challenging
					if (pageInstance.DataSource.Type == DataSourceType.Annotation)
					{
						var blueprintDataSource = _databaseContext.DataSources
							.Include(dataSource => dataSource.AnnotationKeyField)
							.FirstOrDefault(dataSource => dataSource.Type == DataSourceType.Blueprint &&
														  dataSource.AnnotationSourceId == pageInstance.DataSourceId);
						
						if (blueprintDataSource?.AnnotationKeyField != null)
						{
							blueprintSourceId = blueprintDataSource.Id.ToString();
							var blueprintId = (string?)elementInstance.Values[blueprintDataSource.AnnotationKeyField.Name];
							if (blueprintId != null)
							{
								var blueprintElement = blueprintDataSource.GetElement(blueprintId);
								if (blueprintElement?.FileInfo != null)
									deepZoomInfo = _databaseContext.DeepZoomImages.FirstOrDefault(image => image.FileId == blueprintElement.FileInfo.Id)?.ToDto();
								
								// if we have a blueprint now, try to load the available blueprint annotation types
								if (deepZoomInfo != null)
								{
									annotationSource = BuildAnnotationSourceDto(_databaseContext, blueprintDataSource);
									
									// currently we need the workflow info only for annotation single pages (because of the pin preview)
									workflowInfos = MultiPageController.GetWorkflowInfos(_stringLocalizerFactory, singlePageEntity.DataSource, elementInstance);
								}
							}
						}
					}
					
					// try to start a new blueprint rendering
					if (pageInstance.DataSource.Type == DataSourceType.Blueprint && elementInstance.FileInfo != null && (deepZoomInfo == null || deepZoomInfo.AllowRendering()))
					{
						_deepZoomHelperService.GetAndCacheDeepZoomImageAsync(_databaseContext, elementInstance.FileInfo.Id, pageInstance.DataSource);
						deepZoomInfo = new CachedDeepZoomDto { State = CachedDeepZoomState.Caching };
					}
					
					foreach (var field in lookupFields)
					{
						var displayFieldName = field.Name + "." + field.LookupDisplayField!.Name;
						elementInstance.Values[field.Name] = new Dictionary<string, object>
						{
							{ "id", elementInstance.Values[field.Name]! },
							{ "name", elementInstance.Values[displayFieldName]! }
						};
						elementInstance.Values.Remove(displayFieldName);
					}

					foreach (var field in virtualFields.Where(field => field.Type == DataType.Guid))
					{
						elementInstance.Values[field.Name] = new Dictionary<string, object>
						{
							{ "id", elementInstance.Values[field.Name + ".Id"] ?? "" },
							{ "name", elementInstance.Values[field.Name + ".Name"] ?? "" }
						};
						elementInstance.Values.Remove(field.Name+".Id");
						elementInstance.Values.Remove(field.Name+".Name");
					}

					// prepare values for frontend serialization -> date(time) field values are formatted as strings 
					elementInstance.PrepareValuesForResponse(pageInstance.DataSource.Fields);
				}
				else
				{
					singlePageEntity = _databaseContext.SingleDataPages
						.Include(page => page.Views)
						.Include(page => page.DataSource)
							.ThenInclude(dataSource => dataSource.Fields)
								.ThenInclude(field => field.LookupDisplayField)
						.Include(page => page.DataSource.DataStore)
						.Include(page => page.HeaderElements)
						.AsSplitQuery()
						.FirstOrDefault(page => page.Id == pageInstance.Id);
				}

				if (singlePageEntity == null)
					throw new ElementNotFoundException($"SingleDataPage with Id: {pageInstance.Id} could not be found");
				
				return RenderPartialFromDirectory(new SingleDataModel
				{
					Page = singlePageEntity.ToDto(),
					Element = elementInstance,
					DeepZoomInfo = deepZoomInfo,
					BlueprintSourceId = blueprintSourceId,
					AnnotationSource = annotationSource,
					WorkflowInfos = workflowInfos,
					HeaderElements = singlePageEntity.HeaderElements.Select(headerElement => headerElement.ToDto()).ToList(),
					CurrentViewId = pageViewSlug != null ? singlePageEntity.Views.First(view => view.Slug == pageViewSlug).Id : null,
					PageViewSlug = pageViewSlug
				}, "~/Features/SinglePage/Views", "/Visualization.cshtml");
			case PageType.MultiData:
				var multiPageEntity = _databaseContext.MultiDataPages
					.Include(entity => entity.DataSource)
						.ThenInclude(dataSource => dataSource.DataStore)
					.Include(entity => entity.Views)
					.Include(entity => entity.FilterFields)
						.ThenInclude(filterField => filterField.Field)
					.Include(page => page.CreatePage)
					.Include(page => page.DetailPage)
					.Include(page => page.DefaultSorting)
						.ThenInclude(sorting => sorting.Field)
					.AsSplitQuery()
					.FirstOrDefault(page => page.Id == pageInstance.Id);
				
				// Maximized embedded page? add filters
				if (gridViewPageInstance != null)
				{
					gridViewPageInstance.Filters = _databaseContext.GridViewPageFilters
						.AsNoTracking()
						.IgnoreAutoIncludes()
						.Include(filter => filter.FilterField)
						.Where(filter => filter.PageId == gridViewPageInstance.Id).ToList();
				}

				if (Request.GetDisplayUrl().EndsWith("Create"))
					ViewData["targetAction"] = "Create";

				return RenderPartialFromDirectory(new MultiDataModel
				{
					Page = multiPageEntity!.ToDto(),
					ParentPage = gridViewPageInstance != null ? mainPageInstance.ToDto() : null,
					ParentElementId = elementId,
					GridViewPage = gridViewPageInstance?.ToDto()
				}, "~/Features/MultiPage/Views", "/Visualization.cshtml");
		}

		throw new Exception($"Pages of type \"{pageInstance.Type}\" are currently not supported");
	}

	/// <summary>
	/// Renders a configured page view
	/// </summary>
	/// <param name="pageViewId">readable identifier for a specific page</param>
	/// <param name="pageViewType">the page view type</param>
	/// <param name="embedded">is this page view embedded?</param>
	/// <param name="embeddedPageId">Id of the embedded page wrapper</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Public/PageViews/{pageViewId:guid}")]
	public IActionResult RenderPageView(Guid pageViewId, [FromQuery(Name = "type")] PageViewType? pageViewType,
										[FromQuery(Name = "embedded")] bool? embedded, [FromQuery(Name = "embeddedPageId")] Guid? embeddedPageId)
	{
		// load minimal view info and decide if we may need to go any further (or if the client already has the correct version)
		var pageViewEntity = _databaseContext.PageViews.Find(pageViewId);
		if (pageViewEntity == null)
			throw new ElementNotFoundException($"PageView with id: {pageViewId} could not be found");

		if (IsPartialRequest())
		{
			var eTag = VersionReader.GetSemanticVersion() + ":" + pageViewEntity.Revision + ":" + CultureInfo.CurrentCulture;
			if (embedded == true && embeddedPageId is not null)
			{
				var gridViewPageEntity = _databaseContext.GridViewPages.Find(embeddedPageId.Value);
				if (gridViewPageEntity != null)
				{
					var combinedRevision = GuidHelpers.CreateFromName(GuidHelpers.UrlNamespace, pageViewEntity.Revision + ":" + gridViewPageEntity.Revision);
					eTag = combinedRevision + ":" + CultureInfo.CurrentCulture;
				}
			}
				
			var requestTag = Request.Headers.IfNoneMatch;
			if (eTag == requestTag)
				return StatusCode(304);

			Response.Headers.CacheControl = "max-age=0";
			Response.Headers.ETag = eTag;
		}

		// without type an additional select to the base entity page view is needed to extract the type
		pageViewType ??= pageViewEntity.Type;

		switch (pageViewType)
		{
			case PageViewType.List:
				var listViewDto = _databaseContext.ListViews
					.Include(listView => listView.Columns)
						.ThenInclude(column => column.Field)
						.ThenInclude(field => field!.VirtualDataField)
					.Include(listView => listView.Columns)
						.ThenInclude(column => column.Field)
						.ThenInclude(field => field!.LookupDisplayField)
					.AsSplitQuery()
					.FirstOrDefault(listView => listView.Id == pageViewId)?.ToDto();

				if (listViewDto == null)
					throw new ElementNotFoundException($"ListView with id: {pageViewId} could not be found");

				// include page as multiDataPage
				listViewDto.Page = _databaseContext.MultiDataPages
					.Include(page => page.DataSource)
					.Include(page => page.DetailPage)
					.FirstOrDefault(entity => entity.Id == listViewDto.PageId)?.ToDto();
				
				// include workflows
				listViewDto.Page!.DataSource!.Workflows = _databaseContext.Workflows
					.Where(workflow => workflow.DataSourceId == listViewDto.Page.DataSourceId)
					.OrderBy(workflow => workflow.Slot)
					.Include(workflow => workflow.Nodes)
					.AsSplitQuery()
					.ToDtoList();

				// add config of embedded page wrapper
				GridViewPageDto? gridViewPageDto = embeddedPageId != null
													   ? _databaseContext.GridViewPages
														   .Include(page => page.ReferenceField)
														   .Include(page => page.KeyField)
														   .AsNoTracking()
														   .FirstOrDefault(page => page.Id == embeddedPageId)?.ToDto()
													   : null;

				if (gridViewPageDto != null)
					gridViewPageDto.Filters = _databaseContext.GridViewPageFilters
						.Include(filter => filter.FilterField)
						.AsNoTracking()
						.Where(filter => filter.PageId == embeddedPageId).ToDtoList<GridViewPageFilterDto>(); 
				
				return RenderPartialFromDirectory(
					new ListViewModel { View = listViewDto, Embedded = embedded != null && embedded.Value, GridViewPage = gridViewPageDto },
					"~/Features/MultiPage/Views", "/_ListView.cshtml");
			case PageViewType.Gallery:
				var galleryViewDto = _databaseContext.GalleryViews
					.Include(view => view.TitleField)
						.ThenInclude(field => field != null ? field.LookupDisplayField : null)
					.Include(view => view.SubtitleField)
						.ThenInclude(field => field != null ? field.LookupDisplayField : null)
					.AsSplitQuery()
					.FirstOrDefault(galleryView => galleryView.Id == pageViewId)?.ToDto();
				if (galleryViewDto == null)
					throw new ElementNotFoundException($"GalleryView with id: {pageViewId} could not be found");
				
				galleryViewDto.Page = _databaseContext.MultiDataPages
					.Include(page => page.DataSource)
					.FirstOrDefault(page => page.Id == galleryViewDto.PageId)?.ToDto();
				
				// include workflows
				galleryViewDto.Page!.DataSource!.Workflows = _databaseContext.Workflows
					.Where(workflow => workflow.DataSourceId == galleryViewDto.Page.DataSourceId)
					.OrderBy(workflow => workflow.Slot)
					.Include(workflow => workflow.Nodes)
					.AsSplitQuery()
					.ToDtoList();
				
				// add config of embedded page wrapper
				GridViewPageDto? gridViewPageGalleryDto = embeddedPageId != null
													   ? _databaseContext.GridViewPages
														   .AsNoTracking()
														   .IgnoreAutoIncludes()
														   .Include(page => page.ReferenceField)
														   .Include(page => page.KeyField)
														   .FirstOrDefault(page => page.Id == embeddedPageId)?.ToDto()
													   : null;

				if (gridViewPageGalleryDto != null)
					gridViewPageGalleryDto.Filters = _databaseContext.GridViewPageFilters
						.AsNoTracking()
						.IgnoreAutoIncludes()
						.Include(filter => filter.FilterField)
						.Where(filter => filter.PageId == embeddedPageId).ToDtoList<GridViewPageFilterDto>(); 
				
				return RenderPartialFromDirectory(new GalleryViewModel() { View = galleryViewDto, Embedded = embedded != null && embedded.Value, GridViewPage = gridViewPageGalleryDto}, "~/Features/MultiPage/Views", "/_GalleryView.cshtml");
			case PageViewType.Grid:
				// This was a massive single statement of .Include().ThenInclude() before...and that was really slow.  
				var gridViewDto = _databaseContext.GridViews
					.Include(view => view.Page)
						.ThenInclude(page => page.DataSource)
					.FirstOrDefault(view => view.Id == pageViewId)?.ToDto();

				if (gridViewDto == null)
					throw new ElementNotFoundException($"GridView with id: {pageViewId} could not be found");

				// Load grid view's sections
				var sections = _databaseContext.GridViewSections
					.Include(section => section.Fields)
					.Include(section => section.Texts)
					.Include(section => section.Pages)
					.AsSplitQuery()
					.Where(section => section.GridViewId == pageViewId);

				// Load section's dependencies (Child elements of fields & embedded pages) to avoid deep .Include().ThenInclude() chains
				foreach (var section in sections)
				{
					foreach (var field in section.Fields)
					{
						var dataField = _databaseContext.DataFields
							.Include(dataField => dataField.VirtualDataField)
							.Include(dataField => dataField.LookupDisplayField)
							.Include(dataField => dataField.Columns)
								.ThenInclude(column => column.DisplayField)
							.Include(dataField => dataField.Filters)
								.ThenInclude(filter => filter.FilterField)
							.AsSplitQuery()
							.FirstOrDefault(dataField => dataField.Id == field.DataFieldId);

						if (field.DataType == InputDataType.Annotation && dataField != null)
						{
							dataField.LookupSource = _databaseContext.DataSources
								.Include(dataSource => dataSource.DefaultDetailPage)
								.FirstOrDefault(dataSource => dataSource.Id == dataField.LookupSourceId);
						}

						field.DataField = dataField;
					}

					LoadEmbeddedPageConfig(section.Pages);
				}
				
				gridViewDto.Sections = sections.ToDtoList(nameof(MultiDataPageEntity.DetailPage));

				// Load embedded pages that don't belong to sections
				var pages = _databaseContext.GridViewPages
					.Where(page => page.GridViewId == pageViewId)
					.ToList();
				
				LoadEmbeddedPageConfig(pages);

				gridViewDto.Pages = pages.Select(page => page.ToDto([ nameof(MultiDataPageEntity.DetailPage) ])).ToList();

				var headerElements = _databaseContext.PageHeaderElements.Where(element => element.PageId == gridViewDto.PageId).ToDtoList();
				var allWorkflows = _databaseContext.Workflows
					.Include(workflow => workflow.Nodes
								 .OrderBy(node => node.Sorting)
							 )
					.Where(workflow => workflow.Enabled && workflow.DataSourceId == gridViewDto.Page!.DataSourceId)
					.ToDtoList();
				// Only select one workflow per slot, ignore the rest
				var distinctSlotWorkflows = allWorkflows
					.GroupBy(workflow => workflow.Slot)
					.Select(slotGroup => slotGroup.First())
					.OrderBy(workflow => workflow.Slot)
					.ToList();
				return RenderPartialFromDirectory(new GridViewModel { View = gridViewDto, HeaderElements = headerElements, Workflows = distinctSlotWorkflows }, "~/Features/SinglePage/Views",
												  "/_GridView.cshtml");
		}

		return BadRequest();

		void LoadEmbeddedPageConfig(ICollection<GridViewPageEntity> pages)
		{
			foreach (var page in pages)
			{
				page.EmbeddedPage = _databaseContext.MultiDataPages
					.Include(pageEntity => pageEntity.Views)
					.Include(pageEntity => pageEntity.DataSource)
					.Include(pageEntity => pageEntity.DetailPage)
					.Include(pageEntity => pageEntity.DefaultSorting)
						.ThenInclude(sorting => sorting.Field)
					.AsSplitQuery()
					.AsNoTracking()
					.FirstOrDefault(embeddedPage => embeddedPage.Id == page.EmbeddedPageId);

				page.KeyField = _databaseContext.DataFields
					.Include(keyField => keyField.LookupDisplayField)
					.FirstOrDefault(keyField => keyField.Id == page.KeyFieldId);

				page.ReferenceField = _databaseContext.DataFields
					.FirstOrDefault(referenceField => referenceField.Id == page.ReferenceFieldId);

				// This was part of an .ThenInclude() before and casting inside .ThenInclude() seems to be really slow
				if (page is { AllowCreate: true, EmbeddedPage: MultiDataPageEntity { CreatePageId: not null } multiDataPage })
				{
					((MultiDataPageEntity)page.EmbeddedPage!).CreatePage = _databaseContext.CreatePages
						.FirstOrDefault(createPage => createPage.Id == multiDataPage.CreatePageId);
				}
				
				// add config of embedded page wrapper
				page.Filters = _databaseContext.GridViewPageFilters
					.Include(filter => filter.FilterField)
					.AsNoTracking()
					.IgnoreAutoIncludes()
					.Where(filter => filter.PageId == page.Id).ToList();
			}
		}
	}

	/// <summary>
	/// Renders a configured grid view section as a form (direct edit of a specific section)
	/// </summary>
	/// <returns>rendered section</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Public/GridViewSections/{sectionId:guid}")]
	public IActionResult RenderGridViewSectionForm(Guid sectionId)
	{
		var sectionEntity = _databaseContext.GridViewSections
			.Include(section => section.Fields)
				.ThenInclude(field => field.DataField)
					.ThenInclude(dataField => dataField!.LookupDisplayField)
			.Include(section => section.Fields)
				.ThenInclude(field => field.DataField)
					.ThenInclude(dataField => dataField!.Columns)
						.ThenInclude(column => column.DisplayField)
			.Include(section => section.Fields)
				.ThenInclude(field => field.DataField)
					.ThenInclude(dataField => dataField!.Filters)
						.ThenInclude(filter => filter.FilterField)
			.Include(section => section.Texts)
			.AsSplitQuery()
			.FirstOrDefault(section => section.Id == sectionId);

		if (sectionEntity == null)
			throw new ElementNotFoundException($"GridViewSection with id: {sectionId} could not be found");

		if (IsPartialRequest())
		{
			var eTag = VersionReader.GetSemanticVersion() + ":" + sectionEntity.Revision + ":" + CultureInfo.CurrentCulture;
			var requestTag = Request.Headers.IfNoneMatch;
			if (eTag == requestTag)
				return StatusCode(304);

			Response.Headers.CacheControl = "max-age=0";
			Response.Headers.ETag = eTag;
		}

		return RenderPartialFromDirectory(new SectionModel(ViewType.Dialog) { Section = sectionEntity.ToDto() }, "~/Features/SinglePage/Views/GridViewElements",
										  "/_Section.cshtml");
	}

	/// <summary>
	/// prepare AnnotationSourceDto by selecting the available annotation type values
	/// </summary>
	/// <param name="dbContext">db context to use</param>
	/// <param name="blueprintSource">starting point is always our blueprint source</param>
	/// <returns></returns>
	/// <exception cref="Exception"></exception>
	public static AnnotationSourceDto? BuildAnnotationSourceDto(CoreDatabaseContext dbContext, DataSourceEntity blueprintSource)
	{
		var annotationKeyField = dbContext.DataFields.Find(blueprintSource.AnnotationKeyFieldId);
		
		var annotationSourceEntity = dbContext.DataSources
			.Include(dataSource => dataSource.AnnotationKeyField)
			.Include(dataSource => dataSource.AnnotationGroupByField)
			.Include(dataSource => dataSource.AnnotationDetailPage)
			.FirstOrDefault(dataSource => dataSource.Id == blueprintSource.AnnotationSourceId);

		if (annotationSourceEntity == null)
			return null;
		
		List<AnnotationSourceTypeDto> annotationTypes = [];
		if (annotationSourceEntity.AnnotationGroupByFieldId != null)
		{
			var annotationField = dbContext.DataFields
				.Include(dataField => dataField.LookupSource)
				.ThenInclude(lookupSource => lookupSource!.Fields)
				.Include(dataField => dataField.LookupDisplayField)
				.FirstOrDefault(dataField => dataField.Id == annotationSourceEntity.AnnotationGroupByFieldId && dataField.LookupSource != null);

			if (annotationField?.LookupDisplayField == null)
				throw new Exception("loading annotation types failed because element type field is set but has no proper display value");

			var annotationFields = new List<DataStoreQueryField> { new("Id"), new(annotationField.LookupDisplayField.Name) };
			if (annotationField.LookupSource!.Fields.FirstOrDefault(field => field.Name == "ElementTypeIcon") != null)
				annotationFields.Add(new DataStoreQueryField("ElementTypeIcon"));

			var query = new DataStoreQuery(annotationFields);
			annotationTypes = annotationField.LookupSource.GetElements(query).Select(element => new AnnotationSourceTypeDto()
			{
				Id = element.Id,
				Label = element.Values[annotationField.LookupDisplayField.Name]?.ToString() ?? "",
				Icon = element.Values.TryGetValue("ElementTypeIcon", out var value) ? value?.ToString() : null
			}).ToList();
		}
		
		return new AnnotationSourceDto
		{
			Id = annotationSourceEntity.Id,
			Name = annotationSourceEntity.Name,
			TypeFieldName = annotationSourceEntity.AnnotationGroupByField?.Name,
			KeyFieldName = annotationKeyField?.Name,
			Types = annotationTypes,
			CreatePageId = annotationSourceEntity.AnnotationCreatePageId,
			DetailPageSlug = annotationSourceEntity.AnnotationDetailPage?.Slug,
			AllowFile = annotationSourceEntity.AllowFile,
			AllowDiscard = annotationSourceEntity.Inactive
		};
	}
}