{"list": {"de": {"pageTitle": "Seiten", "newItem": "Neue Seite", "menuTitle": "Backend"}, "en": {"pageTitle": "Pages", "newItem": "New Page", "menuTitle": "Backend"}}, "detail": {"de": {"menuTitle": "Einstellungen", "sectionViews": "<PERSON><PERSON><PERSON><PERSON>", "defaultView": "Standard-View", "defaultViewDescription": "Die gewählte View wird standardmäßig beim Öffnen angezeigt", "defaultTab": "Standard-Tab", "defaultTabDescription": "Der gewählte Tab wird standardmäßig beim Öffnen angezeigt", "memorizeDefaultView": "Nutzerauswahl merken", "sectionTabs": "Tabs", "defaultTabs": "Automatisch erzeugte Standard-Tabs", "additionalTabs": "<PERSON><PERSON><PERSON>", "additionalTabsLegend": "Reihenfolge der Liste entspricht Anzeige-Reihenfolge", "breadcrumbLabel": "Datensatzbeschriftung", "breadcrumbLabelDescription": "Diese Information wird z.B. im Bread-Crumb angezeigt, um den einzelnen Datensatz zu identifzieren. <PERSON><PERSON><PERSON><PERSON> von ##feldname## können Feldinformationen dargestellt werden.", "description": "Beschreibung", "appPage": "App-Konfiguration", "appPageDescription": "Diese Seite ist für die Darstellung innerhalb der App vorgesehen"}, "en": {"menuTitle": "Settings", "sectionViews": "Views", "defaultView": "Default view", "defaultViewDescription": "The selected view is displayed by default when it is opened", "defaultTab": "Default tab", "defaultTabDescription": "The selected tab is displayed by default when it is opened", "memorizeDefaultView": "Remember user selection", "sectionTabs": "Tabs", "defaultTabs": "Automatically generated standard tabs", "additionalTabs": "Additional Tabs", "additionalTabsLegend": "Sequence of the list corresponds to the display sequence", "breadcrumbLabel": "Data set label", "breadcrumbLabelDescription": "This information is displayed in the bread crumb, for example, to identify the individual data record. Field information can be displayed using ##fieldname##.", "description": "Description", "appPage": "App Configuration", "appPageDescription": "This page is intended for display within the app"}}, "functions": {"de": {"sectionNewRecord": "<PERSON>euer <PERSON>atz", "sectionGeneralFunctions": "Allgemeine Funktionen", "createPage": "Eingabeformular", "createPageLegend": "Welche Seite soll als Eingabeformular genutzt werden?", "detailPage": "Detailansicht", "detailPageLegend": "Welche Seite soll zur Detailansicht genutzt werden?", "singleRecordBehaviour": "Darstellung eines einzelnen Eintrags", "singleRecordBehaviourDescription": "Enth<PERSON>lt beim Öffnen der Seite die Datenquelle nur einen einzelnen Eintrag, kann der Eintrag direkt geöffnet werden"}, "en": {"sectionNewRecord": "New Record", "sectionGeneralFunctions": "General functions", "createPage": "Input Form", "createPageLegend": "Which page should be used as the input form?", "detailPage": "Detail Page", "detailPageLegend": "Which page should be used for the detailed view?", "singleRecordBehaviour": "Display of a single entry", "singleRecordBehaviourDescription": "If the data source only contains a single entry when the page is opened, the entry can be opened directly"}}, "sorting": {"de": {"description": "In der Sortierung kann der Nutzer automatisch alle Felder auf/oder absteigend sortieren, die in der Standard-View sichtbar sind. Soll es möglich sein nach Feldern zu sortieren, die nicht angezeigt werden, können diese hier hinzugefügt werden. Das Sortierungsmenü ist für alle Views gleich.", "sectionDefault": "Voreinstellung beim Betreten", "sectionDefaultDescription": "<PERSON>er kannst du festlegen, nach welchen Feldern beim Betreten der Seite, die View sortiert sein soll.", "sectionStandard": "Standard", "sectionAdditionalFields": "Zusätzliche Sortierfelder (nicht in Standard)", "sectionAddFields": "<PERSON><PERSON>", "sortDirectionUpdateError": "Die Sortierrichtung konnte nicht aktualisiert werden"}, "en": {"description": "When sorting, the user can automatically sort all fields that are visible in the standard view in ascending or descending order. If it should be possible to sort by fields that are not displayed, these can be added here. The sorting menu is the same for all views.", "sectionPredefined": "Default setting when entering", "sectionPredefinedDescription": "Here you can specify the fields according to which the view should be sorted when you enter the page.", "sectionStandard": "Standard", "sectionAdditionalFields": "Additional sort fields (not in standard)", "sectionAddFields": "Add fields", "sortDirectionUpdateError": "Sorting direction could not be updated"}}, "filterFields": {"de": {"sectionFilterFields": "<PERSON><PERSON><PERSON><PERSON>", "sectionAddFields": "<PERSON><PERSON>"}, "en": {"sectionFilterFields": "Filterable fields", "sectionAddFields": "Add fields"}}, "menu": {"de": {"item/basicSettings": "Grundkonfiguration", "item/functions": "Funktionen", "item/accessControl": "Zugriffsverwaltung", "item/configChanges": "Konfigurationsänderungen", "item/filterAndSorting": "Filter und Sortierung", "item/sorting": "Sortierung", "item/filterField": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item/create/designer": "Designer", "item/multiData/actions": "Aktionen", "item/singleData/headerConfig": "Header <PERSON><PERSON>", "info/noBackend": "<PERSON><PERSON>", "noDataHeading": "<PERSON><PERSON> Backends vorhanden!", "noDataDescription": "Um eine Seite anlegen zu können müssen Sie ein Backend und wenigstens eine Datenquelle hinzufügen!", "noDataButton": "Backends"}, "en": {"item/basicSettings": "Basic Settings", "item/functions": "Functions", "item/accessControl": "Access Control", "item/configChanges": "Configuration Changes", "item/filterAndSorting": "Filter and Sorting", "item/sorting": "Sorting", "item/filterField": "Filter fields", "item/create/designer": "Designer", "item/multiData/actions": "Actions", "item/singleData/headerConfig": "Header configuration", "info/noBackend": "No data store", "noDataHeading": "No backends available!", "noDataDescription": "To be able to create a page, you must add a backend and at least one data source!", "noDataButton": "Data Stores"}}, "*": {"de": {"name": "Name", "dataStore": "Backend", "dataSource": "<PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "module": "<PERSON><PERSON><PERSON>", "responsible": "Verantwortlicher", "lastModifiedText": "Zuletzt bearbeitet am", "lastModifiedValue": "{0} durch {1}", "lastModified": "Zuletzt bearbeitet am", "lastModifiedBy": "Zuletzt bearbeitet durch"}, "en": {"name": "Name", "dataStore": "Backend", "dataSource": "Datasource", "type": "Type", "module": "<PERSON><PERSON><PERSON>", "responsible": "Responsible", "lastModifiedText": "Last Modified", "lastModifiedValue": "{0} by {1}", "lastModified": "Last modified", "lastModifiedBy": "Last modified by"}}}