@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.EditPanel
@model Levelbuild.Frontend.WebApp.Features.Page.ViewModels.PageForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("PageHeaderElement", "");
	var sortings = new List<QueryParamSortingDto>()
	{
		new() { OrderColumn = "Position", Direction = SortDirection.Asc }
	};
}
<script type="module" defer>
	// disable save button
	Page.buttonConfig.saveButton.disabled = true

	// reactivate save button when content changes
	Page.getContentChangeSignal().addEventListener("abort", () => {
		Page.buttonConfig.saveButton.disabled = false
	})
</script>
<div class="grid--centered vanishing-scrollbar static-scrollbar">
	<config-section label="@localizer["sectionHeaderContent"]">
		<enumeration-component id="page-header-element-list" class="section-span-all" sorting="@sortings">
			<list-component skeleton="@(Model.Page == null)">
				<list-column-component name="flag" type="ListColumnType.Flag" with-converter></list-column-component>
				<list-data-column-component name="value" label="@localizer["value"]" hide-label></list-data-column-component>
				<list-column-component name="delete" with-converter width="30"></list-column-component>
			</list-component>
		</enumeration-component>
		<script>
			document.querySelector('#page-header-element-list lvl-list-column[name="flag"]').converter = (rowData) => {
				const icon = document.createElement('i')
				icon.classList.add('fa', 'fa-'+rowData.icon)
				return icon
			}
			document.querySelector('#page-header-element-list lvl-list-column[name="delete"]').converter = () => `<i class="fa-light fa-trash clickable" style="color: var(--clr-signal-error)" onclick="removePageHeaderElement(event)"></i>`
		</script>
	</config-section>
</div>

<script>
	async function removePageHeaderElement(event) {
		event.stopPropagation()
		const columnEnumeration = document.getElementById('page-header-element-list')
		const columnList = columnEnumeration.querySelector('lvl-list')
		const listLine = event.target.parentElement.parentElement
		if (!listLine || listLine.tagName !== 'LVL-LIST-LINE'){
			console.error('List line not found. Column cannot be removed')
			return
		}

		const position = Number(listLine.dataset['position'])
		const currentRow = columnList.rows[position]
		const response = await fetch(`/Api/PageHeaderElements/${currentRow.id}`, { method: 'DELETE' })
		if (response.ok)
			columnEnumeration.reload()
	}
</script>

<vc:admin-list-page entity="PageHeaderElement" route-name="PageHeaderElements" localizer="@localizer" menu-entry="HeaderConfig"
                    parent-property-name="pageId" parent-property-value="@(Model.Page?.Id)" use-custom-list="true"></vc:admin-list-page>

<vc:create-panel entity="PageHeaderElement" route-name="PageHeaderElements" localizer="@localizer" menu-entry="HeaderConfig"
                 parent-property-name="pageId" skeleton="@(Model.Page == null)"></vc:create-panel>
                 
<vc:edit-panel entity="PageHeaderElement" route-name="PageHeaderElements" localizer="@localizer" menu-entry="HeaderConfig"
               heading="@localizer["list/editItem"]" display-property-name="label"  skeleton="@(Model.Page == null)"></vc:edit-panel>