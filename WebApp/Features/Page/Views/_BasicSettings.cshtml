@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.FrontendDtos.Page
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.Page.ViewModels
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@model Levelbuild.Frontend.WebApp.Features.Page.ViewModels.PageForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("Page", "detail");
	var typeLocalizer = LocalizerFactory.Create("PageType", "");
	var viewLocalizer = LocalizerFactory.Create("PageView", "");
}

<script type="module" defer>
	@if (Model.ViewType == ViewType.Create)
	{
		<text>
			const form = document.getElementById('page-form')
			const dataStoreInput = form.querySelector('[name="dataStoreId"]')
			
			@if (Model.DataSourceId == null)
			{
				<text>
					const url = `/Api/DataStores/${Page.getFormData().id}/DataSources`
					form.querySelector("#page-data-source-id").setAttribute('url', url)
				</text>
			}
		</text>
	}
</script>

@if (Model.ViewType == ViewType.Edit && Model.PageType != PageType.Create)
{
	<vc:admin-list-page entity="PageView" route-name="PageViews" localizer="@localizer" menu-entry="BasicSettings" parent-property-name="pageId" parent-property-value="@(Model.Page?.Id)" use-custom-list="true"></vc:admin-list-page>
	<vc:create-panel entity="PageView" route-name="PageViews" route-params="{type:'@(Model.PageType)'}" localizer="@viewLocalizer" menu-entry="BasicSettings" parent-property-name="pageId" skeleton="@(Model.Page == null)"></vc:create-panel>
	
	<script type="module" defer>
		const form = document.getElementById('page-form')
		const viewInput = form.querySelector('[name="defaultView"]')
		const pageId = "@(Model.Page != null ? Model.Page.Id : "")"
		const queryView = document.querySelector('#page-view-list')
		const viewList = queryView.querySelector('lvl-list')
		
		/**
		* Click on table row opens the detail view to edit the backend configuration
		* @@param rowContent {object} complete value map of the backend configuration 
		* @@param rowIndex {number}
		*/
		viewList.onRowClick = async (rowContent, _, newTab) => {
			const loadData = Page.getJSON(`/Api/PageViews/${rowContent.data.id}`)
			let listLocation = Page.getMainPageUrl()

			if (newTab) {
				const completeUrl = `${listLocation}/Views/${rowContent.data.slug}/@(Model.PageType == PageType.SingleData ? "Designer" : "BasicSettings")`
				Page.openNewTab(completeUrl, true)
				return
			}
			
			// load page and update page info
			@if (Model.PageType == PageType.SingleData)
			{
				<text>
					await Page.load(`${listLocation}/Views/${rowContent.data.slug}/Designer`, {type:rowContent.data.type})
					Page.setMainPage(`${listLocation}/Views/${rowContent.data.slug}`, 'Designer')
					Page.setInfo(Page.getMainPageUrl() + '/Designer', { title: `${rowContent.data.name} Config`})
				</text>
			}
			else
			{
				<text>
					await Page.load('/Admin/PageViews/Edit', {type:rowContent.data.type})
					Page.setMainPage(`${listLocation}/Views/${rowContent.data.slug}`, 'BasicSettings')
					Page.setInfo(Page.getMainPageUrl()+'/BasicSettings', { params: {}, title: `${rowContent.data.name} Config` })
				</text>
			}
			
			await Page.addBreadcrumb({ label: rowContent.data.name, url: Page.getMainPageUrl() })
			
			// load page info
			let json = await loadData
			const form = document.getElementById("page-view-form")
			Page.saveFormData(json.data)
			if (form != null)
				await Page.setFormData(form, json.data)
			Page.buttonConfig.showAdminButtons()
			
			// add source id in detail view
			document.getElementById('pageViewId').value = rowContent.data.id
			
			const infoSection = document.getElementById('page-view-menu')
			await Component.waitForComponentInitialization('lvl-side-nav')
			const dataStoreSlug = Page.getValueByPath(json.data, "page.dataSource.dataStore.slug")
			const dataSourceSlug = Page.getValueByPath(json.data, "page.dataSource.slug")
			
			// Backend
			infoSection.setInfoData(json.data, "info-dataStore-link-left", "page.dataSource.dataStore.name")
			infoSection.setInfoUrl("info-dataStore-link-right", `/Admin/DataStores/${dataStoreSlug}`)
			
			// DataSource
			infoSection.setInfoData(json.data, "info-dataSource-link-left", "page.dataSourceName")
			infoSection.setInfoUrl("info-dataSource-link-right", `/Admin/DataStores/${dataStoreSlug}/DataSources/${dataSourceSlug}`)
			
			// CreatedBy
			infoSection.setInfoData(json.data, "info-createdBy-left", "createdBy")
			infoSection.setInfoData(json.data, "info-createdBy-right", "created")
			
			// LastModifiedBy
			infoSection.setInfoData(json.data, "info-modifiedBy-left", "lastModifiedBy")
			infoSection.setInfoData(json.data, "info-modifiedBy-right", "lastModified")
			
			// disable skeleton on menu and fab
			if (form != null)
				form.skeleton = false
			const menu = document.getElementById("page-view-menu")
			if (menu != null)
				menu.skeleton = false
		}

		await Component.waitForComponentInitialization(viewInput)
		queryView.addEventListener('query-view:changed', () => {
			viewInput?.setOptions(viewList.items.map(item => ({ label: item.data.name, value: item.data.id })))
		})
		
		viewList.querySelector('lvl-list-column[name="flag"]').converter = (rowData) => `<i class="fa-light fa-${rowData.icon || 'table'}"></i>`
	</script>
}
<div class="grid--centered @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
	<form-component id="page-form" skeleton="@(Model is { ViewType: ViewType.Edit, Page: null })">
		<input type="hidden" name="dtoType" value="@Model.PageType"/>
		<config-section label="@localizer["sectionInfo"]">
			<input type="hidden" class="item__value" name="id" value="@Model.Page?.Id"/>
			<input type="hidden" class="item__value" name="dataStoreId" value="@Model.DataStoreId"/>
			@if (Model is { ViewType: ViewType.Create, DataSourceId: not null })
			{
				<input type="hidden" class="item__value" name="dataSourceId" value="@Model.DataSourceId"/>
			}
			@if (Model is { ViewType: ViewType.Create, DataSourceId: null })
			{
				<div class="form__item">
					<config-label target="page-data-source-id" label="@localizer["dataSource"]"></config-label>
					@{
						var dataSourceColumns = new List<AutocompleteColumnDefinition>
						{
							new("name", localizer["name"], DataType.String),
							new("comment", localizer["comment"], DataType.String)
						};
						<autocomplete-component type="InputDataType.Guid" id="page-data-source-id" Url="/Api/DataStores/@(Model.DataStoreId)/DataSources"
						                        columns="dataSourceColumns" name="dataSourceId" value="@(Model.Page?.DataSourceId.ToString() ?? Model.DataSourceId.ToString() ?? string.Empty)"
						                        display-value="@(Model.Page?.DataSource?.Name ?? Model.DataSourceName ?? string.Empty)" class="item__value"
						                        placeholder="@localizer["pleaseChoose"]" required="true" readonly="@(Model.ViewType != ViewType.Create || Model.DataSourceId != null)">
						</autocomplete-component>
					}
				</div>
			}
			<div class="form__item">
				@{
					var pageTypes = Enum.GetValues(typeof(PageType)).Cast<PageType>().Select(level => new AutocompleteOptionDefinition(level.ToString(), typeLocalizer[level.ToString()])).ToList();
				}
				<config-label target="page-type" label="@localizer["type"]"></config-label>
				<autocomplete-component type="InputDataType.String" id="page-type" options="pageTypes" name="type" value="@(Model.Page?.Type ?? PageType.SingleData)" class="item__value"
				                        placeholder="@localizer["pleaseChoose"]" required="true" readonly="@(Model.ViewType != ViewType.Create)"
				                        onchange="document.querySelector('#page-form input[name=dtoType]').setAttribute('value', this.value);">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="page-name" label="@localizer["name"]"></config-label>
				<input-component type="InputDataType.Translation" translation-prefix="/Pages/" id="page-name" name="name" value="@(Model.Page?.Name ?? string.Empty)" class="item__value" required="true"></input-component>
			</div>
			@if (Model.PageType == PageType.SingleData)
			{
				<div class="form__item">
					<config-label target="breadcrumb-label" label="@localizer["breadcrumbLabel"]" description="@localizer["breadcrumbLabelDescription"]"></config-label>
					<input-component type="InputDataType.String" required="true" id="breadcrumb-label" name="breadcrumbLabel" placeholder="##projectname## - ##projectnr##"
					                 value="@((Model.Page as SingleDataPageDto)?.BreadcrumbLabel ?? string.Empty)" class="item__value"></input-component>
				</div>
			}
			<div class="form__item">
				<config-label target="page-description" label="@localizer["description"]"></config-label>
				<textarea-component id="page-description" name="description" placeholder="@localizer["description"]" value="@(Model.Page?.Description ?? string.Empty)" class="item__value" rows="5"></textarea-component>
			</div>
			@if (Model.ViewType == ViewType.Create)
			{
				<div class="form__item">
					<config-label target="app-page" label="@localizer["appPage"]" description="@localizer["appPageDescription"]"></config-label>
					<toggle-component id="app-page" name="appPage"></toggle-component>
				</div>
			}
		</config-section>
		@if (Model.ViewType == ViewType.Edit)
		{
			@if (Model.PageType == PageType.Create)
			{
				<config-section label="@localizer["sectionButtons"]">
					<div class="form__item">
						<config-label target="save-button-label" label="@localizer["saveButtonLabel"]"></config-label>
						<input-component type="InputDataType.Translation" translation-prefix="/CreatePage/" id="save-button-label" name="saveButtonLabel"
						                 value="@((Model as CreateForm)?.CreatePage?.SaveButtonLabel ?? string.Empty)"
						                 placeholder="@localizer["saveButton"]" class="item__value"></input-component>
					</div>
				</config-section>
			}
			else
			{
				<config-section label="@(Model.PageType == PageType.SingleData ? localizer["sectionTabs"] : localizer["sectionViews"]) ">
					
					@if (Model.PageType == PageType.MultiData)
					{
						<div class="form__item">
							<config-label label="@(Model.PageType == PageType.SingleData ? localizer["defaultTab"] : localizer["defaultView"])"
							              description="@(Model.PageType == PageType.SingleData ? localizer["defaultTabDescription"] : localizer["defaultViewDescription"])"></config-label>
							<autocomplete-component type="InputDataType.Guid" name="defaultView" output-name="defaultViewId" value="@(Model.Page?.DefaultView?.Id)" display-value="@(Model.Page?.DefaultView?.Name)" class="item__value"
							                        placeholder="@localizer["pleaseChoose"]">
							</autocomplete-component>
						</div>
					
						<div class="form__item">
							<config-label label="@localizer["memorizeDefaultView"]"></config-label>
							<toggle-component name="memorizeDefaultView" value="@(Model.Page?.MemorizeDefaultView)" class="item__value" readonly="true"></toggle-component>
						</div>
					}
					@{
						var sortings = new List<QueryParamSortingDto>
						{
							new() { OrderColumn = "Position", Direction = SortDirection.Asc },
							new() { OrderColumn = "Created", Direction = SortDirection.Asc }
						};

						var payload = new Dictionary<string, object>
						{
							{ "dtoType", "##type##" }
						};
					}
					<enumeration-component id="page-view-list" class="section-span-all" sorting="@sortings" payload="@payload">
						<list-component skeleton="@(Model.Page == null)">
							<list-column-component name="flag" type="ListColumnType.Flag" with-converter></list-column-component>
							<list-data-column-component name="name" label="@viewLocalizer["name"]" hide-label></list-data-column-component>
							<list-data-column-component name="typeName" label="@viewLocalizer["type"]" max-width="120"></list-data-column-component>
							<list-data-column-component name="created" label="@viewLocalizer["created"]" type="DataColumnType.DateTime" max-width="120"></list-data-column-component>
							<list-data-column-component name="lastModified" label="@viewLocalizer["lastModified"]" type="DataColumnType.DateTime" max-width="120"></list-data-column-component>
							@if (Model.PageType == PageType.MultiData)
							{
								<list-data-column-component
									name="display" type="DataColumnType.Icon" icon="list-dropdown" icon-tooltip-yes="@viewLocalizer["hideInSelection"]" icon-tooltip-no="@viewLocalizer["displayInSelection"]"
									label="@viewLocalizer["basicSettings/display"]" hide-label max-width="50" live-editable="true"></list-data-column-component>
							}
						</list-component>
					</enumeration-component>
				</config-section>
			}
		}
	</form-component>
</div>