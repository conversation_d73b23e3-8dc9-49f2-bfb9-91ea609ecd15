using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.Page.SingleData;
using Levelbuild.Frontend.WebApp.Features.ListViewColumn.Controllers;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.Page.Controllers;
using Levelbuild.Frontend.WebApp.Features.PageHeaderElement.ViewModels;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.PageHeaderElement.Controllers;

/// <summary>
/// Controller for the configuration view of list view columns
/// </summary>
public class PageHeaderElementController : AdminController<PageHeaderElementDto>
{
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="versionReader">injected VersionReader</param>
	public PageHeaderElementController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : 
		base(logManager, logManager.GetLoggerForClass<ListViewColumnController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		
	}
	
	#region Views
	
	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/PageHeaderElements/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Pages/{pageSlug}/{menu=HeaderConfig}/Create")]
	public IActionResult Create(string? dataStoreSlug, string? pageSlug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(pageSlug))
			return CachedPartial() ?? RenderPartial(new PageHeaderElementForm());
		
		PageEntity? entity = null;
		var dataStoreEntity = DatabaseContext.DataStoreConfigs.FirstOrDefault(dataStoreEntity => dataStoreEntity.Slug == dataStoreSlug.ToLower());
		if (dataStoreEntity != null)
			entity = DatabaseContext.Pages.FirstOrDefault(pageEntity => pageEntity.DataStoreId == dataStoreEntity.Id &&  pageEntity.Slug == pageSlug.ToLower());
		
		if (entity == null)
			throw new ElementNotFoundException($"Page with slug: {pageSlug} could not be found for DataStore with slug: {dataStoreSlug}");
		
		return RenderPartial(
			new PageHeaderElementForm()
			{
				PageId = entity.Id
			},
			"~/Features/Page/Views/Detail.cshtml",
			AdminPageController.GetPageFormByType(DatabaseContext, entity.Type, entity.Id)
		);
	}

	/// <summary>
	/// Renders the detail (edit) view to edit a specific header element
	/// </summary>
	/// <param name="dataStoreSlug">readable identifier for a specific dataStore</param>
	/// <param name="pageSlug">readable identifier for a specific culture</param>
	/// <param name="headerElementId">uuid of the header element we want to edit</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/PageHeaderElements/Edit")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Pages/{pageSlug}/HeaderConfig/{headerElementId:guid}")]
	public IActionResult Detail(string? dataStoreSlug, string? pageSlug, Guid? headerElementId)
	{
		if (!headerElementId.HasValue)
			return CachedPartial() ?? RenderPartial(new PageHeaderElementForm(ViewType.Edit));
		
		Request.RouteValues.Add("menu", "HeaderConfig");
		PageHeaderElementEntity? configEntity = DatabaseContext.PageHeaderElements.Find(headerElementId);
		if (configEntity == null)
			throw new ElementNotFoundException($"Header element with id: {headerElementId} could not be found");
		
		return RenderPartial(
			new PageHeaderElementForm(ViewType.Edit)
			{
				PageId = configEntity.PageId,
				HeaderElement = configEntity.ToDto()
			},
			"~/Features/Page/Views/Detail.cshtml",
			AdminPageController.GetPageFormByType(DatabaseContext, PageType.SingleData, configEntity.PageId)
		);
	}
	
	#endregion
	
	#region Actions
	
	/// <inheritdoc />
	[HttpGet("/Api/PageHeaderElements/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		return HandleQueryRequest<PageHeaderElementEntity, PageHeaderElementDto>(DatabaseContext.PageHeaderElements, parameters);
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/PageHeaderElements/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<PageHeaderElementEntity, PageHeaderElementDto>(DatabaseContext.PageHeaderElements, id);
	}

	/// <inheritdoc />
	[HttpPost("/Api/PageHeaderElements/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] PageHeaderElementDto configurationDto)
	{
		// calculate position
		configurationDto.Position = await DatabaseContext.PageHeaderElements.CountAsync(element => element.PageId == configurationDto.PageId)+1;
		
		return await HandleCreateRequestAsync(DatabaseContext.PageHeaderElements, configurationDto);
	}
	
	/// <inheritdoc />
	[HttpPatch("/Api/PageHeaderElements/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] PageHeaderElementDto configurationDto)
	{
		var entity = await DatabaseContext.PageHeaderElements.FindAsync(id);
		var oldPosition = entity?.Position;
		var result = await HandleUpdateRequestAsync(DatabaseContext.PageHeaderElements, id, configurationDto);
		if (entity != null && result.Result is OkObjectResult && oldPosition != configurationDto.Position)
			ReorderElements(entity.PageId, entity.Id, oldPosition, configurationDto.Position);

		return result;
	}
	
	/// <inheritdoc />
	[HttpDelete("/Api/PageHeaderElements/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		var entity = DatabaseContext.PageHeaderElements.Find(id);
		var result = HandleDeleteRequest(DatabaseContext.PageHeaderElements, id);
		if (entity != null && result.Result is OkObjectResult)
		{
			ReorderElements(entity.PageId);
			var pageEntity = DatabaseContext.Pages.Find(entity.PageId);
			pageEntity?.Touch(true);
		}

		return result;
	}
	
	#endregion

	private void ReorderElements(Guid pageId, Guid? updatedElementId = null, int? oldPosition = null, int? newPosition = null)
	{
		var queryable = DatabaseContext.PageHeaderElements.Where(element => element.PageId == pageId).OrderBy(element => element.Position);
		
		// if a specific element position gets updated, this position comes first!
		if (updatedElementId != null)
		{
			var changedElementFirst = oldPosition > newPosition;
			queryable = queryable.ThenBy(column => column.Id == updatedElementId.Value ? changedElementFirst ? 0 : 2 : 1);
		}

		var elements = queryable.ToList();
		foreach (var (column, index) in elements.Select((item, index) => (item, index)))
		{
			column.Position = index+1;
		}
		DatabaseContext.SaveChanges();
	}
}