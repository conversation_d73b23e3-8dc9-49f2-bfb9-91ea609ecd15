using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.PageHeaderElement.ViewModels;

[ExcludeFromCodeCoverage]
public class PageHeaderElementForm
{
	public ViewType ViewType { get; init; }

	public PageHeaderElementDto? HeaderElement { get; init; }

	public Guid PageId { get; init; }

	public PageHeaderElementForm(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}