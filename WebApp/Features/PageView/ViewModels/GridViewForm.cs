using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ViewModels;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.PageView.ViewModels;

[ExcludeFromCodeCoverage]
public class GridViewForm : PageViewForm
{
	public GridViewDto? GridView => (GridViewDto?)PageView;
	
	public GridViewForm(ViewType viewType = ViewType.Create, GridViewDto? pageView = null) : base(viewType, pageView)
	{
		PageViewType = PageViewType.Grid;
		MenuItems = new List<MenuItem>
		{
			new("basicSettings", "BasicSettings", "screwdriver-wrench") { UrlParams = new() { { "appPage", pageView?.Page?.AppPage == true ? "true" : "false"} } },
			new("gridView/designer", "Designer", "brush") { UrlParams = new() { { "id", pageView?.Id != null ? pageView.Id : "" } } }
		};
		
		ViewDirectory = "GridView/";
	}
}