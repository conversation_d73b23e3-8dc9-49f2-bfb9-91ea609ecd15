using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ViewModels;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.PageView.ViewModels;

[ExcludeFromCodeCoverage]
public class ListViewForm : PageViewForm
{
	public ListViewForm(ViewType viewType = ViewType.Create, PageViewDto? pageView = null) : base(viewType, pageView)
	{
		PageViewType = PageViewType.List;
		MenuItems = new List<MenuItem>
		{
			new("basicSettings", "BasicSettings", "gears"),
			new("listView/columns", "ViewDesigner", "brush")
		};
 
		ViewDirectory = "ListView/";
	}
}