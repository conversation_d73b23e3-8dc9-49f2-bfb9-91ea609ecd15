@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@model Levelbuild.Frontend.WebApp.Features.PageView.ViewModels.PageViewForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("PageView", "detail");
	string currentMenu = ViewData["targetMenu"] == null ? "BasicSettings" : ViewData["targetMenu"]!.ToString()!;
	var menuRouteParams = $"{{type: '{Model.PageViewType}'}}";

	// mwa: needed in order for things like /Columns/Create and /Columns/columnSlug to render properly when the browser page gets refreshed!
	Context.GetRouteData().Values.Add("feature", "PageView");
}
@if (Program.IsDevelopment && !Program.IsRunningInContainer)
{
	<link vite-href="/css/features/single-data-page/page-designer.css" rel="stylesheet" type="text/css"/>
}
else
{
	<link href="@Url.Content("~/css/features/single-data-page/page-designer.css")" rel="stylesheet" type="text/css" asp-append-version="true"/>	
}
<script type="module" defer>
	@if (Model.PageView != null)
	{
		var pageLocalizer = LocalizerFactory.Create("Page", "list");
		<text>
    		Page.setMainPage(`/Admin/DataStores/@(Model.PageView.Page?.DataSource?.DataStore?.Slug)/Pages/@(Model.PageView.Page?.Slug)/Views/@(Model.PageView?.Slug)`, '@(currentMenu)')
    		Page.setBreadcrumbs([
    				{ label: '@pageLocalizer["pageTitle"]', url: '/Admin/DataStores/@(Model.PageView?.Page?.DataSource?.DataStore?.Slug)/Pages' },
    				{ label: '@(Model.PageView?.Page?.Name ?? "")', url: `/Admin/DataStores/@(Model.PageView?.Page?.DataSource?.DataStore?.Slug)/Pages/@(Model.PageView?.Page?.Slug)/BasicSettings` },
    				{ label: '@Model.PageView?.Name', url: `/Admin/DataStores/@(Model.PageView?.Page?.DataSource?.DataStore?.Slug)/Pages/@(Model.PageView?.Page?.Slug)/Views/@(Model.PageView?.Slug)` }
    		]) 
    	</text>
	}

	@if (Model.PageViewType.GetPageType() == PageType.MultiData)
	{
		<text>
			Page.buttonConfig.preview = true
			Page.buttonConfig.previewButton.addEventListener('click', () => { window.open(`/Public/Pages/${Page.getFormData()?.page?.slug}`, '_blank') }, { signal: Page.getPageChangeSignal() })
			Page.buttonConfig.previewButton.skeleton = false
		</text>
	}
</script>

<input type="hidden" id="pageViewId" value="@Model.PageView?.Id"/>
<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="PageView" title="@localizer["menuTitle"]" route-name="PageViews" route-params="@menuRouteParams"
               menu-items="@Model.MenuItems" menu-infos="@Model.MenuInfos" width="250" skeleton="@(Model.PageView == null)">
</vc:basic-menu>
<vc:admin-detail-page entity="PageView" route-name="PageViews" model="@Model" title="@Model.PageView?.Name" menu-item="@currentMenu"
                      show-default-buttons="@(Model.PageView != null)" view-name="@(Model.PageView?.Type + "View/_" + currentMenu)" use-custom-layout="true">
</vc:admin-detail-page>