using System.Diagnostics.CodeAnalysis;
using System.Net.Mime;
using System.Text.Json;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PublicApi;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.PublicApi;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Attributes;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Constants;
using Levelbuild.Frontend.WebApp.Features.PublicApi.OData;
using Levelbuild.Frontend.WebApp.Features.PublicApi.OData.Attributes;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Utils;
using Levelbuild.Frontend.WebApp.Shared.Services.Files;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;

/// <summary>
/// Public API controller for data access
/// </summary>
[SwaggerGroup("Data Access", 2)]
[Produces(MediaTypeNames.Application.Json)]
[Consumes(MediaTypeNames.Application.Json)]
[Route("PublicApi/DataSources/{dataSourceId:guid}/[controller]")]
public class ElementsController : PublicApiController
{
	private readonly IDeepZoomHelperService _deepZoomHelperService;
	private readonly IThumbnailHelperService _thumbnailHelperService;
	private readonly IExtendedStringLocalizerFactory _stringLocalizerFactory;
	

	/// <inheritdoc />
	public ElementsController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, IVersionReader versionReader, 
							  IExtendedStringLocalizerFactory stringLocalizerFactory, IDeepZoomHelperService deepZoomHelperService, IThumbnailHelperService thumbnailHelperService) : base(
		logManager.GetLoggerForClass<ElementsController>(), contextFactory, versionReader)
	{
		_deepZoomHelperService = deepZoomHelperService;
		_thumbnailHelperService = thumbnailHelperService;
		_stringLocalizerFactory = stringLocalizerFactory;
	}

	/// <summary>
	/// Returns a mutated list of elements as JSON.
	///
	/// OData params may be used here.
	/// </summary>
	/// <param name="dataSourceId"></param>
	/// <param name="queryOptions">The query options. Automagically parsed by the OData lib.</param>
	[ExcludeFromCodeCoverage]
	[SwaggerOperation("Query data using OData parameters")]
	[HttpGet]
	[EnableOdataQuery]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DataStoreQueryResultDto>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public ActionResult<PublicApiResponse> Query(Guid dataSourceId, ODataQueryOptions<DataStoreElement> queryOptions)
	{
		DataSourceEntity? dataSource = null;
		try
		{
			dataSource = DatabaseContext.DataSources
				.Include(source => source.Fields).ThenInclude(field => field.LookupSource)
				.Include(source => source.Fields).ThenInclude(field => field.LookupDisplayField)
				.FirstOrDefault(source => source.Id == dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			var dataStoreQuery = ODataToQueryTranslator.Translate(queryOptions, dataSource.Fields);
			dataStoreQuery.PrepareQueryForDataStore(dataSource.Fields);

			var additionalQueryFields = new List<DataStoreQueryField>();

			// Already some fields present? -> Parse lookup display-fields and virtual fields if present!
			if (dataStoreQuery.Fields?.Count > 0)
			{
				// Collect defect fields first and remove them from query fields if necessary
				var defectQueryFields = dataSource.Fields.Where(
						field => field is { FieldType: DataFieldType.VirtualField, HasVirtualData: false } ||
								 (field.FieldType == DataFieldType.LookupField && field is { LookupDisplayField: null }))
					.ToList();

				foreach (var defectField in defectQueryFields)
				{
					var defectQueryField = dataStoreQuery.Fields.FirstOrDefault(
						field => field.Name.Equals(defectField.Name, StringComparison.CurrentCultureIgnoreCase) ||
								 field.Alias?.Contains(defectField.Name) == true && field.Alias.EndsWith(".Id", StringComparison.CurrentCultureIgnoreCase) ||
								 field.Alias?.Contains(defectField.Name) == true &&
								 field.Alias.EndsWith(".Display", StringComparison.CurrentCultureIgnoreCase));
					if (defectQueryField != null)
						dataStoreQuery.Fields.Remove(defectQueryField);
				}

				var queryFieldsClone = new List<DataStoreQueryField>(dataStoreQuery.Fields.Distinct());
				foreach (var queryField in dataStoreQuery.Fields)
				{
					// get name and check field
					var existingField = DataStoreQueryExtensions.GetFieldFromName(queryField.Name, dataSource.Fields);
					if (existingField == null)
						continue;

					switch (existingField.FieldType)
					{
						case DataFieldType.LookupField:
						{
							// .display or plain
							if (!queryField.Name.EndsWith(".Id", StringComparison.CurrentCultureIgnoreCase))
							{
								additionalQueryFields.Add(new DataStoreQueryField(existingField.Name + "." + existingField.LookupDisplayField!.Name,
																				  existingField.Name + ".Display"));
							}
							// .id or plain
							if (!queryField.Name.EndsWith(".Display", StringComparison.CurrentCultureIgnoreCase))
							{
								additionalQueryFields.Add(new DataStoreQueryField(existingField.Name + ".Id", existingField.Name + ".Id"));
							}
							// in any case remove the original query
							queryFieldsClone.Remove(queryField);
							continue;
						}

						case DataFieldType.VirtualField:
						{
							queryFieldsClone.Remove(queryField);

							// (stacking)virtual on data field
							if (existingField.Type != DataType.Guid)
							{
								queryFieldsClone.Add(new DataStoreQueryField(existingField.VirtualDataStoreQueryName!, existingField.Name));
								continue;
							}

							// (stacking)virtual on lookup field
							// .display or plain
							if (!queryField.Name.EndsWith(".Id", StringComparison.CurrentCultureIgnoreCase))
							{
								additionalQueryFields.Add(new DataStoreQueryField(existingField.VirtualDataStoreQueryName!,
																				  existingField.Name + ".Display"));
							}

							// .id or plain
							if (!queryField.Name.EndsWith(".Display", StringComparison.CurrentCultureIgnoreCase))
							{
								// if the virtual field references a FK, we compare against the FK field, not the target definitions display field (so we remove the last part of the query)
								var queryName = existingField.VirtualDataStoreQueryName!;
								var queryParts = queryName.Split('.').ToList();
								queryParts.RemoveAt(queryParts.Count - 1);
								additionalQueryFields.Add(new DataStoreQueryField(string.Join('.', queryParts),
																				  existingField.Name + ".Id"));
							}

							continue;
						}
					}
				}

				// remove duplicates
				additionalQueryFields = additionalQueryFields.Distinct().ToList();

				queryFieldsClone.AddRange(additionalQueryFields);

				// Replace query fields
				dataStoreQuery.WithFields(queryFieldsClone);
			}
			else
			{
				var lookupOrVirtualFieldsPresent = dataSource.Fields
					.Any(field => field.FieldType is DataFieldType.LookupField or DataFieldType.VirtualField);

				// No fields present, but lookup fields? -> Load all!
				if (lookupOrVirtualFieldsPresent)
				{
					additionalQueryFields = dataSource.SelectLookupAndVirtualFields();

					var dataFields = dataSource.Fields
						.Where(field => field.FieldType == DataFieldType.DataField || field.FieldType == DataFieldType.LookupField)
						.Select(field => new DataStoreQueryField(field.Name))
						.ToList();

					additionalQueryFields.AddRange(dataFields);

					dataStoreQuery.AddFields(additionalQueryFields);
				}
			}

			var resultSet = dataSource.GetElements(dataStoreQuery);

			foreach (var result in resultSet)
			{
				result.ParseCompositeValuesForResponse(additionalQueryFields);

				result.PrepareValuesForResponse(dataSource.Fields, additionalQueryFields);
			}

			if (dataStoreQuery.CountAll)
				return GetOkResponse(new DataStoreQueryResultDto(resultSet.ToList(), resultSet.CountTotal));

			return GetOkResponse(new DataStoreQueryResultDto(resultSet.ToList()));
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Elements list of data source '{dataSource?.Name ?? string.Empty}' could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Returns the count of filtered elements.
	///
	/// OData $filter param may be used here.
	/// </summary>
	/// <param name="dataSourceId"></param>
	/// <param name="queryOptions">The query options. Automagically parsed by the OData lib.</param>
	[ExcludeFromCodeCoverage]
	[SwaggerOperation("Count elements using optional OData filters")]
	[HttpGet("$count")]
	[EnableOdataQuery(ODataParameterConstants.Filter)]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<int>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public ActionResult<PublicApiResponse> Count(Guid dataSourceId, ODataQueryOptions<DataStoreElement> queryOptions)
	{
		DataSourceEntity? dataSource = null;
		try
		{
			dataSource = DatabaseContext.DataSources
				.Include(source => source.Fields).ThenInclude(field => field.LookupSource)
				.Include(source => source.Fields).ThenInclude(field => field.LookupDisplayField)
				.Include(source => source.Fields).ThenInclude(field => field.VirtualLookupField)
				.FirstOrDefault(source => source.Id == dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			var dataStoreQuery = ODataToQueryTranslator
				.Translate(queryOptions, dataSource.Fields, ODataParameterConstants.Filter, ODataParameterConstants.Apply)
				.WithCountAll();
			dataStoreQuery.PrepareQueryForDataStore(dataSource.Fields);

			var result = dataSource.GetElements(dataStoreQuery).CountTotal;
			return GetOkResponse(result);
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Element count of data source '{dataSource?.Name ?? string.Empty}' could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Returns a specific element as JSON.
	/// </summary>
	/// <param name="dataSourceId"></param>
	/// <param name="elementId"></param>
	[SwaggerOperation("Get a single element")]
	[HttpGet("{elementId}")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DataStoreElement>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public ActionResult<PublicApiResponse> Get(Guid dataSourceId, string elementId)
	{
		DataSourceEntity? dataSource = null;
		try
		{
			dataSource = DatabaseContext.DataSources
				.Include(source => source.Fields).ThenInclude(field => field.LookupDisplayField)
				.Include(source => source.Fields).ThenInclude(field => field.VirtualLookupField)
				.FirstOrDefault(source => source.Id == dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			// Select lookup/virtual fields
			var additionalQueryFields = dataSource.SelectLookupAndVirtualFields();

			DataStoreElement? storeElement = dataSource.GetElement(elementId, additionalQueryFields);

			if (storeElement == null)
				return GetNotFoundResponse($"Element with id: {elementId} could not be found.");
			
			storeElement.ParseCompositeValuesForResponse(additionalQueryFields);

			storeElement.PrepareValuesForResponse(dataSource.Fields, additionalQueryFields);

			return GetOkResponse(storeElement);
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Element of data source '{dataSource?.Name ?? string.Empty}' could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Creates an element and returns it.
	/// </summary>
	/// <param name="dataSourceId"></param>
	/// <param name="elementData"></param>
	[SwaggerOperation("Create a single element")]
	[HttpPost]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DataStoreElement>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	public ActionResult<PublicApiResponse> Create(Guid dataSourceId, DataStoreElementData elementData)
	{
		DataSourceEntity? dataSource = null;
		try
		{
			dataSource = DatabaseContext.DataSources
				.Include(source => source.Fields).ThenInclude(field => field.LookupDisplayField)
				.Include(source => source.Fields).ThenInclude(field => field.VirtualLookupField)
				.FirstOrDefault(source => source.Id == dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			// TODO - Use real origin
			var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.PublicApi, -1, "");

			elementData.PrepareValuesForDataStore(dataSource.Fields);

			var lookupDisplayQueryFields = dataSource.Fields.Where(field => field is { FieldType: DataFieldType.LookupField, LookupDisplayFieldId: not null })
				.Select(field => new DataStoreQueryField(field.Name + "." + field.LookupDisplayField!.Name, field.Name + ".Display"))
				.ToList();

			var result = dataSource.CreateElement(elementData, origin, lookupFields: lookupDisplayQueryFields).ElementData!;

			foreach (var queryField in lookupDisplayQueryFields)
			{
				var lookupName = queryField.Name.Split(".")[0];
				var value = result.Values[queryField.Alias!]!;
				result.Values[lookupName] = result.Values[lookupName] != null
												? new Dictionary<string, object> { { "Id", result.Values[lookupName]! }, { "DisplayValue", value } }
												: null;
				result.Values.Remove(queryField.Alias!);
			}

			result.PrepareValuesForResponse(dataSource.Fields, lookupDisplayQueryFields);
			
			//cache deep zoom image version of file if necessary
			if (dataSource.Type == DataSourceType.Blueprint && result.FileInfo?.Id != null)
				_deepZoomHelperService.GetAndCacheDeepZoomImageAsync(DatabaseContext, result.FileInfo.Id, dataSource);

			return GetOkResponse(result);
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Element could not be created in data source '{dataSource?.Name ?? string.Empty}'");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Creates multiple elements and returns their elementIds.
	/// </summary>
	/// <param name="dataSourceId"></param>
	/// <param name="elementData"></param>
	[SwaggerOperation("Create multiple elements")]
	[HttpPost("Batch")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<IList<string>>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	public ActionResult<PublicApiResponse> CreateBatch(Guid dataSourceId, IList<DataStoreElementData> elementData)
	{
		DataSourceEntity? dataSource = null;
		try
		{
			dataSource = DatabaseContext.DataSources
				.Include(source => source.Fields).ThenInclude(field => field.LookupDisplayField)
				.Include(source => source.Fields).ThenInclude(field => field.VirtualLookupField)
				.FirstOrDefault(source => source.Id == dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			// TODO - Use real origin
			var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.PublicApi, -1, "");

			foreach (var entry in elementData)
			{
				entry.PrepareValuesForDataStore(dataSource.Fields);
			}

			var result = dataSource.CreateElements(elementData, origin);
			return GetOkResponse(result.Select(successInfo => successInfo.ElementId).ToArray());
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Element batch could not be created in data source '{dataSource?.Name ?? string.Empty}'");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Updates a specific and returns it.
	/// </summary>
	/// <param name="dataSourceId"></param>
	/// <param name="elementId"></param>
	/// <param name="elementData"></param>
	[SwaggerOperation("Update a single element")]
	[HttpPatch("{elementId}")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DataStoreElement>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	public async Task<ActionResult<PublicApiResponse>> Update(Guid dataSourceId, String elementId, DataStoreElementData elementData)
	{
		DataSourceEntity? dataSource = null;
		try
		{
			if (elementData.ElementId != null && elementData.ElementId != elementId)
				return GetBadRequestResponse("Element data seems to belong to another element than requested.");

			dataSource = DatabaseContext.DataSources
				.Include(source => source.Fields).ThenInclude(field => field.LookupDisplayField)
				.Include(source => source.Fields).ThenInclude(field => field.VirtualLookupField)
				.FirstOrDefault(source => source.Id == dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			var oldFileId = (await dataSource.GetElementAsync(elementId))?.FileInfo?.Id;
			
			// TODO - Use real origin
			var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.PublicApi, -1, "");

			elementData.PrepareValuesForDataStore(dataSource.Fields);

			// Values present? -> Update the element!
			if (elementData.Values.Count > 0)
			{
				DataStoreFileInfo? fileInfo = null;
				try
				{
					// Update the file before updating the element
					if (elementData.FileUploadId != null)
						fileInfo = (await dataSource.UpdateFileAsync(elementId, elementData.FileUploadId, origin)).ElementData!.FileInfo;
				}
				catch (Exception e)
				{
					Logger.Error(e, $"Temp file with fileUploadId '{elementData.FileUploadId}' could not be found.");
					return GetBadRequestResponse($"Temp file with fileUploadId '{elementData.FileUploadId}' could not be found.");
				}


				var lookupDisplayQueryFields = dataSource.Fields
					.Where(field => field is { FieldType: DataFieldType.LookupField, LookupDisplayFieldId: not null })
					.Select(field => new DataStoreQueryField(field.Name + "." + field.LookupDisplayField!.Name, field.Name + ".Display"))
					.ToList();

				var updateElementData = dataSource.UpdateElement(new(elementId, elementData.Values), origin, lookupDisplayQueryFields).ElementData!;
				var result = new DataStoreElement
				{
					Id = updateElementData.Id,
					Values = updateElementData.Values,
					Groups = updateElementData.Groups,
					FileInfo = fileInfo ?? updateElementData.FileInfo,
				};

				foreach (var queryField in lookupDisplayQueryFields)
				{
					var lookupName = queryField.Name.Split(".")[0];
					var value = result.Values[queryField.Alias!]!;
					result.Values[lookupName] = result.Values[lookupName] != null
													? new Dictionary<string, object> { { "Id", result.Values[lookupName]! }, { "DisplayValue", value } }
													: null;
					result.Values.Remove(queryField.Alias!);
				}

				result.PrepareValuesForResponse(dataSource.Fields, lookupDisplayQueryFields);
				
				if (oldFileId != null && oldFileId != result.FileInfo?.Id)
				{
					_thumbnailHelperService.DeleteThumbnailsAsync(DatabaseContext, oldFileId);
					_deepZoomHelperService.DeleteDeepZoomImagesAsync(DatabaseContext, oldFileId);
				}

				return GetOkResponse(result);
			}

			// No Values, but file? -> Just update the file!
			if (elementData.FileUploadId != null)
			{
				var result = (await dataSource.UpdateFileAsync(elementId, elementData.FileUploadId, origin)).ElementData!;

				result.PrepareValuesForResponse(dataSource.Fields);
				
				if (oldFileId != null && oldFileId != result.FileInfo?.Id)
				{
					_thumbnailHelperService.DeleteThumbnailsAsync(DatabaseContext, oldFileId);
					_deepZoomHelperService.DeleteDeepZoomImagesAsync(DatabaseContext, oldFileId);
				}

				return GetOkResponse(result);
			}

			// Nothing? -> Return error message!
			return GetBadRequestResponse("No data to update was provided.");
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Element could not be updated in data source '{dataSource?.Name ?? string.Empty}'");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Deletes a specific element.
	/// </summary>
	/// <param name="dataSourceId"></param>
	/// <param name="elementId"></param>
	[SwaggerOperation("Delete a single element")]
	[HttpDelete("{elementId}")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	public async Task<ActionResult<PublicApiResponse>> Delete(Guid dataSourceId, String elementId)
	{
		DataSourceEntity? dataSource = null;
		try
		{
			dataSource = await DatabaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");


			var fileId = (await dataSource.GetElementAsync(elementId))?.FileInfo?.Id;

			// TODO - Use real origin
			var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.PublicApi, -1, "");
			var successInfo = await dataSource.DeleteElementAsync(elementId, origin);
			
			if (fileId != null)
			{
				_thumbnailHelperService.DeleteThumbnailsAsync(DatabaseContext, fileId);
				_deepZoomHelperService.DeleteDeepZoomImagesAsync(DatabaseContext, fileId);
			}

			if (successInfo.ElementId == elementId)
				return GetOkResponse();

			return GetBadRequestResponse("Something went wrong. :-(");
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Element could not be deleted in data source '{dataSource?.Name ?? string.Empty}'");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Execute an action on a specific record
	/// </summary>
	/// <param name="dataSourceId">ID of the data source configuration</param>
	/// <param name="elementId">ID of a data record for executing the action</param>
	/// <param name="actionDto">Dto which contains the action</param>
	[HttpPatch("{elementId}/Action")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	public async Task<ActionResult<PublicApiResponse>> ExecuteElementAction(Guid dataSourceId, string elementId, [FromBody] ElementActionDto actionDto)
	{
		try
		{
			var dataSource = await DatabaseContext.DataSources
								 .FirstOrDefaultAsync(entity => entity.Id == dataSourceId);
			if (dataSource == null)
				throw new ElementNotFoundException($"DataSource configuration with id: {dataSourceId} could not be found");
			
			await dataSource.ExecuteElementAction(actionDto, elementId);
			
			return GetOkResponse();
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "Element could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Execute an action on a specific record
	/// </summary>
	/// <param name="dataSourceId">ID of the data source configuration</param>
	/// <param name="actionDto">Dto which contains the action</param>
	[HttpPatch("Action")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	public async Task<ActionResult<PublicApiResponse>> ExecuteElementsAction(Guid dataSourceId, [FromBody] ElementActionDto actionDto)
	{
		try
		{
			var dataSource = await DatabaseContext.DataSources
								 .FirstOrDefaultAsync(entity => entity.Id == dataSourceId);
			if (dataSource == null)
				throw new ElementNotFoundException($"DataSource configuration with id: {dataSourceId} could not be found");

			if (actionDto.Elements != null)
			{
				var tasks = actionDto.Elements.Select(elementGuid => dataSource.ExecuteElementAction(actionDto, elementGuid.ToString()));
				await Task.WhenAll(tasks);
			}

			return GetOkResponse();
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "Element could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Gets the workflow status on a specific record
	/// </summary>
	/// <param name="dataSourceId">ID of the data source configuration</param>
	/// <param name="elementId">ID of a data record</param>
	/// <param name="workflowId">ID of a workflow</param>
	[SwaggerOperation("Gets the workflow status on a specific record")]
	[HttpGet("{elementId}/Workflow/{workflowId}/Status")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<WorkflowNodeDto>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	public async Task<ActionResult<PublicApiResponse>> GetCurrentWorkflowState(Guid dataSourceId, string elementId, Guid workflowId)
	{
		try
		{
			var workflow = DatabaseContext.Workflows
				.Include(workflow => workflow.DataSource)
				.Include(workflow => workflow.StatusField)
				.Include(workflow => workflow.Nodes)
				.FirstOrDefault(workflow => workflow.Id == workflowId);
			if (workflow == null)
				return GetNotFoundResponse($"Views with id {workflowId} not found.");

			var element = await workflow.DataSource.GetElementAsync(elementId);
			if (element == null)
				return GetNotFoundResponse($"Element with id: {elementId} could not be found.");
			
			Guid statusValue;
			try
			{
				statusValue = Guid.Parse((element.Values[workflow.StatusField!.Name] as string)!);
			}
			catch (Exception e)
			{
				if (e is FormatException or ArgumentNullException)
					statusValue = workflow.Nodes.First(node => node.State == WorkflowNodeState.Start).Id;
				else
					throw;
			}
			
			var node = workflow.Nodes.FirstOrDefault(node => node.Id == statusValue);
			if(node == null)
				return GetNotFoundResponse($"Node with id {statusValue} not found.");
			
			var json = JsonSerializer.SerializeToElement(node.ToDto(), SerializerOptions);
			return GetOkResponse(json);
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "Current workflow state could not be retrieved");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Change the workflow status on a specific record
	/// </summary>
	/// <param name="dataSourceId">ID of the data source configuration</param>
	/// <param name="elementId">ID of a data record</param>
	/// <param name="workflowId">ID of a workflow</param>
	/// <param name="nodeId">ID of a workflow node that should be set</param>
	[SwaggerOperation("Change the workflow status on a specific record")]
	[HttpPatch("{elementId}/Workflow/{workflowId}/Status/{nodeId}")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DataStoreElement>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	public async Task<ActionResult<PublicApiResponse>> ChangeWorkflowStatus(Guid dataSourceId, string elementId, Guid workflowId, Guid nodeId)
	{
		try
		{
			var workflow = await DatabaseContext.Workflows
				.Include(workflow => workflow.DataSource)
				.Include(workflow => workflow.StatusField)
				.Include(workflow => workflow.Nodes)
				.FirstOrDefaultAsync(workflow => workflow.Id == workflowId);
			if (workflow == null)
				return GetNotFoundResponse($"Workflow with id {workflowId} not found.");
			
			var node = workflow.Nodes.FirstOrDefault(node => node.Id == nodeId);
			if(node == null)
				return GetNotFoundResponse($"Workflow node with id {nodeId} not found.");

			var updateValues = new Dictionary<string, object?>
			{
				{ workflow.StatusField!.Name, node.Id }
			};
			var elementData = new DataStoreElementData(elementId, updateValues);
			
			// TODO - Use real origin
			var successInfo = workflow.DataSource.UpdateElement(elementData, new DataStoreOperationOrigin(DataStoreOperationOriginType.UserWorkflow, -1, ""));
			if (successInfo.ElementId == null)
				throw new DataStoreOperationException($"Something went wrong during update of element with id: {elementId}.");
			
			// load element fresh (because we need the virtual fields and those can't be accessed through the update method currently)
			var element = ElementUtils.GetElementWithVirtualFields(_stringLocalizerFactory, DatabaseContext, _deepZoomHelperService, workflow.DataSourceId, elementId);
			if (element == null)
				return GetNotFoundResponse($"Element with id: {elementId} could not be found in DataSource with id {workflow.DataSourceId}");
			
			return GetOkResponse(element);
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "Element could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}
}