using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Net.Mime;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.CachedDeepZoom;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Thumbnail;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.PublicApi;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Attributes;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.Viewer.Email.Dtos;
using Levelbuild.Frontend.WebApp.Features.Viewer.Email.Services;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Services.Files;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;

/// <summary>
/// Public API controller for data access
/// </summary>
[ExcludeFromCodeCoverage]
[SwaggerGroup("Data Access", 2)]
[Route("PublicApi/DataSources/{dataSourceId:guid}/[controller]")]
public class FilesController : PublicApiController
{
	private readonly UserManager _userManager;

	private readonly IThumbnailHelperService _thumbnailHelperService;

	private readonly IDeepZoomHelperService _deepZoomHelperService;

	private readonly IRedisAccessService _redisAccessService;

	private readonly IEmailReader _emailReader;
	
	private readonly IServiceScopeFactory _scopes;

	//Todo: @TWE
	private readonly ActivitySource _activitySource;
	
	/// <inheritdoc />
	public FilesController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, IThumbnailHelperService thumbnailHelperService,
						   IDeepZoomHelperService deepZoomHelperService, IVersionReader versionReader,
						   IRedisAccessService redisAccessService, UserManager userManager, IEmailReader emailReader, ActivitySource activitySource, IServiceScopeFactory scopes) : base(
		logManager.GetLoggerForClass<FilesController>(), contextFactory, versionReader)
	{
		_userManager = userManager;

		_thumbnailHelperService = thumbnailHelperService;
		_deepZoomHelperService = deepZoomHelperService;

		_redisAccessService = redisAccessService;

		_emailReader = emailReader;

		_scopes = scopes;
		
		_activitySource = activitySource;
	}

	/// <summary>
	/// Returns a specific file.
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">id of the storage file</param>
	[SwaggerOperation("Get a file by its ID")]
	[HttpGet("{fileId}")]
	[Produces(MediaTypeNames.Application.Octet, MediaTypeNames.Application.Json)]
	[Consumes(MediaTypeNames.Application.Json)]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(FileStreamResult))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status404NotFound, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public ActionResult<PublicApiResponse> Get(Guid dataSourceId, string fileId)
	{
		try
		{
			var dataSource = DatabaseContext.DataSources.Find(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			var fileStream = dataSource.GetFile(fileId);

			if (fileStream == null)
				return GetNotFoundResponse($"File with id: {fileId} could not be found.");

			return File(fileStream, MediaTypeNames.Application.Octet, fileStream.Name);
		}
		catch (Exception e)
		{
			Logger.Error(e, "File could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// loads the mail data of a mail file from the given data source by its ID
	/// </summary>
	/// <param name="dataSourceId">ID of a DataSourceEntity</param>
	/// <param name="fileId">ID of a file within this data source</param>
	[SwaggerOperation("Get a files mail data by its ID")]
	[HttpGet("{fileId}/MailData")]
	[Produces(MediaTypeNames.Application.Json)]
	[Consumes(MediaTypeNames.Application.Json)]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<EmailDto>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status404NotFound, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public ActionResult<PublicApiResponse> GetMailData(Guid dataSourceId, string fileId)
	{
		try
		{
			var dataSource = DatabaseContext.DataSources.Find(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			using var fileStream = dataSource.GetFile(fileId);

			if (fileStream == null)
				return GetNotFoundResponse($"File with id: {fileId} could not be found.");

			var fileExtension = fileStream.Name.Split('.').Last().ToLower();
			switch (fileExtension)
			{
				case "eml":
				case "msg":
					var mailData = _emailReader.ParseMail(fileStream);
					fileStream.Dispose();
					return mailData != null ? GetOkResponse(mailData) : GetNotFoundResponse($"File with id: {fileId} could not be parsed.");
				default:
					return GetBadRequestResponse("File is not an email format.");
			}
		}
		catch (Exception e)
		{
			Logger.Error(e, "File could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Uploads a single file to get a temporary ID used to append the file to an element.
	/// </summary>
	/// <param name="dataSourceId">id the datasource</param>
	/// <param name="file">file to upload</param>
	/// <param name="fileDate">Optional.</param>
	[SwaggerOperation("Upload a single file to get a temporary ID used to append the file to an element")]
	[HttpPost]
	[RequestSizeLimit(10_000_000_000)]
	[Produces(MediaTypeNames.Application.Json)]
	[Consumes(MediaTypeNames.Multipart.FormData, MediaTypeNames.Image.Jpeg)]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<string>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public ActionResult<PublicApiResponse> Create(Guid dataSourceId, [Required] IFormFile file, [FromForm] DateTime? fileDate = null)
	{
		try
		{
			var dataSource = DatabaseContext.DataSources.Find(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
			
			using var fileStream = new DataStoreFileStream(file.FileName, fileDate ?? DateTime.Now.ToUniversalTime(), file.OpenReadStream(), file.Length);
			
			return GetOkResponse(dataSource.UploadFile(fileStream));
		}
		catch (Exception e)
		{
			Logger.Error(e, "File could not be uploaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Remove a file from a specified element.
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="elementId">id of the corresponding element</param>
	[SwaggerOperation("Remove a file from a specified element")]
	[HttpDelete("{elementId}")]
	[Produces(MediaTypeNames.Application.Octet, MediaTypeNames.Application.Json)]
	[Consumes(MediaTypeNames.Application.Json)]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(FileStreamResult))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status404NotFound, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public ActionResult<PublicApiResponse> Delete(Guid dataSourceId, string elementId)
	{
		try
		{
			var dataSource = DatabaseContext.DataSources.Find(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			// TODO - Use real origin
			var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.PublicApi, -1, "");

			dataSource.RemoveFile(elementId, origin);
			return GetOkResponse();
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"Element with id: {elementId} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "File could not be deleted");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Returns a thumbnail of a specific file.
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">id of the storage file</param>
	/// <param name="width">desired width (optional)</param>
	/// <param name="height">desired height (optional)</param>
	/// <param name="crop">should the thumbnail be cropped out of the file?(optional)</param>
	[SwaggerOperation("Get a thumbnail for a file by its ID")]
	[HttpGet("{fileId}/Thumbnail")]
	[Produces(MediaTypeNames.Image.Png, MediaTypeNames.Application.Octet, MediaTypeNames.Application.Json)]
	[Consumes(MediaTypeNames.Application.Json)]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(FileStreamResult))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	[ResponseCache(Duration = 31536000, Location = ResponseCacheLocation.Client)]
	public async Task<ActionResult<PublicApiResponse>> GetThumbnail(Guid dataSourceId, string fileId, [FromQuery] int width = 100,
																	[FromQuery] int height = 100, [FromQuery] bool crop = true)
	{
		try
		{
			var dataSource = await DatabaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			//check redis if the user has accessed this file before
			var user = await _userManager.GetCurrentUserAsync();
			if (await _redisAccessService.StringGetAsync(RedisConstants.UserThumbnailCachingKey + user?.Id) != fileId)
			{
				try
				{
					if(!await dataSource.IsGetFileAllowedAsync(fileId))
						return GetNotFoundResponse($"File with id: {fileId} could not be found.");
				}
				catch (Exception)
				{
					return GetNotFoundResponse($"File with id: {fileId} could not be found.");
				}
				
				//save access to redis
				if (user != null)
					await _redisAccessService.StringSetAsync(RedisConstants.UserThumbnailCachingKey + user.Id, fileId, TimeSpan.FromDays(30));
			}

			var thumbnailStream = await _thumbnailHelperService.GetThumbnailAsync(DatabaseContext, fileId, dataSource, width, height, crop);
			
			if(thumbnailStream == null)
				return GetBadRequestResponse("Thumbnail could not be fetched.");
			
			var thumbnailStreamCopy = new MemoryStream();
			await thumbnailStream.CopyToAsync(thumbnailStreamCopy);
			thumbnailStream.Seek(0, SeekOrigin.Begin);

			_ = Task.Run(async () =>
			{
				try
				{
					using var scope = _scopes.CreateScope();
					await using var parallelContext = scope.ServiceProvider.GetRequiredService<CoreDatabaseContext>();
					thumbnailStreamCopy.Seek(0, SeekOrigin.Begin);
					await _thumbnailHelperService.CacheThumbnailAsync(parallelContext, thumbnailStreamCopy, fileId, width, height, crop);
				}
				catch (Exception e)
				{
					Logger.Error(e, "Could not cache thumbnail");
				}
				finally
				{
					await thumbnailStreamCopy.DisposeAsync();
				}
			});

			var thumbnail = File(thumbnailStream, MediaTypeNames.Application.Octet, "thumbnail");

			return thumbnail;
		}
		catch (Exception e)
		{
			Logger.Error(e, "Thumbnail could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Fetches and saves deep zoom image version of a file
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">id of the storage file</param>
	/// <param name="dpi">dpi if file is pdf</param>
	[SwaggerOperation("Cache a deep zoom image of a file")]
	[HttpPost("{fileId}/DeepZoom")]
	[Produces(MediaTypeNames.Application.Json)]
	[Consumes(MediaTypeNames.Application.Json)]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<CachedDeepZoomDto>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public async Task<ActionResult<PublicApiResponse>> SaveDeepZoom(Guid dataSourceId, string fileId, [FromQuery] int dpi = 300)
	{
		try
		{
			var dataSource = await DatabaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
			
			try
			{
				if(!await dataSource.IsGetFileAllowedAsync(fileId))
					return GetNotFoundResponse($"File with id: {fileId} could not be found.");
			}
			catch (Exception)
			{
				return GetNotFoundResponse($"File with id: {fileId} could not be found.");
			}

			var cachedDeepZoomEntity = await _deepZoomHelperService.GetAndCacheDeepZoomImageAsync(DatabaseContext, fileId, dataSource, dpi);

			if (cachedDeepZoomEntity == null)
				return BadRequest("Deep Zoom Image could not be generated!");

			if (cachedDeepZoomEntity.State != CachedDeepZoomState.Ready)
				return BadRequest($"Deep Zoom Image could not be generated! {cachedDeepZoomEntity.ErrorMessage}");

			return GetOkResponse(cachedDeepZoomEntity.ToDto());
		}
		catch (Exception e)
		{
			Logger.Error(e, "Deep Zoom Image could not be generated");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Fetches the metadata of a deep zoom image
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">id of the storage file</param>
	/// <param name="dpi">dpi if file is pdf</param>
	[SwaggerOperation("Get the metadata of a deep zoom image")]
	[HttpGet("{fileId}/DeepZoom")]
	[Produces(MediaTypeNames.Application.Json)]
	[Consumes(MediaTypeNames.Application.Json)]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<CachedDeepZoomDto>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public async Task<ActionResult<PublicApiResponse>> GetDeepZoomEntity(Guid dataSourceId, string fileId, [FromQuery] int dpi = 300)
	{
		try
		{
			var dataSource = await DatabaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			//check redis if the user has accessed this file before
			var user = await _userManager.GetCurrentUserAsync();
			if (await _redisAccessService.StringGetAsync(RedisConstants.UserDeepZoomCachingKey + user.Id) != fileId)
			{
				try
				{
					if(!await dataSource.IsGetFileAllowedAsync(fileId))
						return GetNotFoundResponse($"File with id: {fileId} could not be found.");
				}
				catch (Exception)
				{
					return GetNotFoundResponse($"File with id: {fileId} could not be found.");
				}
				
				//save access to redis
				if (user != null)
					await _redisAccessService.StringSetAsync(RedisConstants.UserDeepZoomCachingKey + user.Id, fileId, TimeSpan.FromDays(30));
			}

			var deepZoomImageId = $"{fileId}_{dpi}";

			var cachedDeepZoomEntity = await DatabaseContext.DeepZoomImages.FirstOrDefaultAsync(entity => entity.CachedFileId == deepZoomImageId);

			if (cachedDeepZoomEntity == null)
				return GetNotFoundResponse($"DeepZoomImage with id: {deepZoomImageId} could not be found.");

			return GetOkResponse(cachedDeepZoomEntity.ToDto());
		}
		catch (Exception e)
		{
			Logger.Error(e, "Deep Zoom Image could not be generated");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Fetches a specific .webp file of a cached deep zoom image
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">id of the storage file</param>
	/// <param name="subFileFolder">level of the tile file</param>
	/// <param name="subFileName">tile name</param>
	/// <param name="dpi">dpi if file is pdf</param>
	[SwaggerOperation("Get a deep zoom image of a file")]
	[HttpGet("{fileId}/DeepZoom/SubFiles/{subFileFolder}/{subFileName}")]
	[Produces(MediaTypeNames.Image.Webp, MediaTypeNames.Application.Json)]
	[Consumes(MediaTypeNames.Application.Json)]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(FileStreamResult))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public async Task<ActionResult<PublicApiResponse>> GetDeepZoom(Guid dataSourceId, string fileId, string subFileFolder, string subFileName,
																   [FromQuery] int dpi = 300)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();
		
		Stream? cachedDeepZoomFile = null;
		try
		{
			using var activityFindDatasource = _activitySource.StartActivity("Find DataSource by Id");
			var dataSource = await DatabaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
			activityFindDatasource?.Stop();
			
			//check redis if the user has accessed this file before
			using var activityCheckRedisForUser = _activitySource.StartActivity("Check Redis for User");
			
			using var activityGetUserFromDb = _activitySource.StartActivity("Get User from DB");
			var user = await _userManager.GetCurrentUserAsync();
			activityGetUserFromDb?.Stop();

			using var activityFindUserInRedis = _activitySource.StartActivity("Find User from Redis");
			var foundInRedis = await _redisAccessService.StringGetAsync(RedisConstants.UserDeepZoomCachingKey + user.Id) != fileId;
			activityFindUserInRedis?.Stop();
			
			if (foundInRedis)
			{
					using var activityGetElementFromDb = _activitySource.StartActivity("Get Element from DB");
					
					try
					{
						if(!await dataSource.IsGetFileAllowedAsync(fileId))
							return GetNotFoundResponse($"File with id: {fileId} could not be found.");
					}
					catch (Exception)
					{
						return GetNotFoundResponse($"File with id: {fileId} could not be found.");
					}

					activityGetElementFromDb?.Stop();
					
					//save access to redis
					using var activitySetUserInRedis = _activitySource.StartActivity("Set User from Redis");
					
					if (user != null)
						await _redisAccessService.StringSetAsync(RedisConstants.UserDeepZoomCachingKey + user.Id, fileId, TimeSpan.FromDays(30));
					else
						activitySetUserInRedis?.SetStatus(ActivityStatusCode.Error);

					activitySetUserInRedis?.Stop();
			}
			activityCheckRedisForUser?.Stop();

			using var activityGetDeepZoomImage = _activitySource.StartActivity("Get Deep Zoom Image");
			cachedDeepZoomFile = await _deepZoomHelperService.GetDeepZoomImageFileAsync(DatabaseContext, fileId, subFileFolder, subFileName, dpi);

			if (cachedDeepZoomFile != null)
				return File(cachedDeepZoomFile, MediaTypeNames.Image.Webp, "cachedDeepZoomImage");

			activityGetDeepZoomImage?.SetStatus(ActivityStatusCode.Error);
			activityGetDeepZoomImage?.Stop();
			
			return GetNotFoundResponse($"Deep zoom image for fileId {fileId} could not be found.");
		}
		catch (Exception e)
		{
			if(cachedDeepZoomFile != null)
				await cachedDeepZoomFile.DisposeAsync();
			
			Logger.Error(e, "Deep zoom could not be loaded");
			activity?.SetStatus(ActivityStatusCode.Error, "Deep zoom could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}
}