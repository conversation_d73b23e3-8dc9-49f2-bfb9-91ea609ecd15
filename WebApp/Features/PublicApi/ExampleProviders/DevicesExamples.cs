using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Device;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;
using Swashbuckle.AspNetCore.Filters;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.ExampleProviders;

/// <summary>
/// Swagger example data for <see cref="DevicesController.Get"/>
/// </summary>
public class DevicesExamples : IExamplesProvider<PublicApiResponse<DeviceDto>>
{
	/// <inheritdoc />
	public PublicApiResponse<DeviceDto> GetExamples()
	{
		// TODO
		var dto = new DeviceDto();
		
		return PublicApiResponseExample.GetExample(dto);
	}
}

/// <summary>
/// Swagger example data for <see cref="DevicesController.List"/>
/// </summary>
public class DevicesListExample : IExamplesProvider<PublicApiResponse<IList<DeviceDto>>>
{
	/// <inheritdoc />
	public PublicApiResponse<IList<DeviceDto>> GetExamples()
	{
		// TODO
		var dtoList = new List<DeviceDto>
		{
			new()
		};
		
		return PublicApiResponseExample.GetExample<IList<DeviceDto>>(dtoList);
	}
}