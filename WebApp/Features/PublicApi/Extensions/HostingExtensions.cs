using System.Reflection;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Attributes;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Filters;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.OData;
using Microsoft.AspNetCore.OData.Routing.Conventions;
using Microsoft.OData.ModelBuilder;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.Filters;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Extensions;

/// <summary>
/// Hosting extensions for Public API related services.
/// </summary>
public static class HostingExtensions
{
	/// <summary>
	/// Configures and adds Swagger Gen to the WebApp
	/// </summary>
	/// <param name="builder"></param>
	/// <returns>The <see cref="WebApplicationBuilder"/> instance for chaining.</returns>
	public static WebApplicationBuilder AddSwaggerGen(this WebApplicationBuilder builder)
	{
		var config = builder.Configuration.GetSection("Zitadel");
		
		builder.Services.AddEndpointsApiExplorer();
		builder.Services.AddSwaggerGen(options =>
		{
			options.SwaggerDoc("v1", new OpenApiInfo
			{
				Title = "levelbuild Public API", 
				Version = "v1"
			});
			
			// Enable optional Annotations (i.e. SwaggerOperation)
			options.EnableAnnotations();
			
			// Include XML Comments (Summaries)
			options.IncludeXmlComments(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "WebApp.xml"));
			
			// Enable custom endpoint grouping on controller level by using the SwaggerGroupAttribute
			options.TagActionsBy(api =>
			{
				if (api.ActionDescriptor is ControllerActionDescriptor controllerActionDescriptor)
				{
					var swaggerTag = controllerActionDescriptor.ControllerTypeInfo
						.GetCustomAttributes(typeof(SwaggerGroupAttribute), true)
						.Cast<SwaggerGroupAttribute>().FirstOrDefault();
					
					if(swaggerTag == null)
						return [controllerActionDescriptor.ControllerName];
					
					return [swaggerTag.GroupName];
				}
				
				throw new InvalidOperationException($"Unable to determine tag for endpoint '{api.ActionDescriptor.DisplayName}'.");
			});
			
			// Apply custom sorting, based on SwaggerGroupAttribute's Order property
			options.OrderActionsBy(api =>
			{
				if (api.ActionDescriptor is ControllerActionDescriptor controllerActionDescriptor)
				{
					var swaggerTag = controllerActionDescriptor.ControllerTypeInfo
						.GetCustomAttributes(typeof(SwaggerGroupAttribute), true)
						.Cast<SwaggerGroupAttribute>().FirstOrDefault();
					
					if(swaggerTag == null)
						return controllerActionDescriptor.ControllerName;
					
					return $"{swaggerTag.Order:D10}_{controllerActionDescriptor.ControllerName}";
				}
				
				throw new InvalidOperationException($"Unable to determine tag for endpoint '{api.ActionDescriptor.DisplayName}'.");
			});
			
			// Add Filters
			options.OperationFilter<ODataOperationFilter>();
			options.SchemaFilter<PublicApiSchemaFilter>();
			options.DocumentFilter<PublicApiSchemaFilter>();
			
			// Add Filters that enable usage of SwaggerResponseExample attribute
			options.ExampleFilters();
			
			// Auth stuff
			options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
			{
				In = ParameterLocation.Header,
				Description = "Please enter token",
				Name = "Authorization",
				Type = SecuritySchemeType.Http,
				BearerFormat = "Opaque",
				Scheme = "bearer"
			});
			options.AddSecurityDefinition(
				"oauth2",
				new OpenApiSecurityScheme
				{
					Type = SecuritySchemeType.OAuth2,
					Flows = new OpenApiOAuthFlows
					{
						AuthorizationCode = new OpenApiOAuthFlow
						{
							AuthorizationUrl = new($"{config["Url"]}/oauth/v2/authorize"),
							TokenUrl = new($"{config["Url"]}/oauth/v2/token"),
							Scopes = new Dictionary<string, string>
							{
								{ "openid", "" },
								{ "profile", "" },
								{ "email", "" },
								{ "offline_access", "" },
								{ "urn:zitadel:iam:user:resourceowner", "" },
								{ "urn:zitadel:iam:org:project:id:zitadel:aud", "" }
							}
						}
					}
				}
			);
			
			options.AddSecurityRequirement(new OpenApiSecurityRequirement
			{
				{
					new OpenApiSecurityScheme
					{
						Reference = new OpenApiReference
						{
							Type = ReferenceType.SecurityScheme,
							Id = "Bearer"
						},
					},
					[]
				},
				{
					new OpenApiSecurityScheme
					{
						Reference = new OpenApiReference
						{
							Type = ReferenceType.SecurityScheme,
							Id = "oauth2"
						},
					},
					[]
				}
			});
		});
		
		// Add current assembly for examples
		builder.Services.AddSwaggerExamplesFromAssemblies(Assembly.GetEntryAssembly());

		return builder;
	}
	
	/// <summary>
	/// Configures and adds OAuth support to the WebApp
	/// </summary>
	/// <param name="builder"></param>
	/// <returns>The <see cref="WebApplicationBuilder"/> instance for chaining.</returns>
	public static WebApplicationBuilder InitOdata(this WebApplicationBuilder builder)
	{
		ODataModelBuilder modelBuilder = new ODataConventionModelBuilder();
		modelBuilder.EntityType<PublicApiResponse>();
		modelBuilder.EntityType<PublicApiResponse<DataStoreElement>>();
		modelBuilder.EntityType<PublicApiResponse<IQueryable<DataStoreElement>>>();
		
		//modelBuilder.EntitySet<PublicApiResponse<DataStoreElement>>("DataStoreElement");
		
		builder.Services.AddControllers().AddOData(options =>
		{
			// Remove Metadata from ApiDoc
			options.Conventions.Remove(options.Conventions.OfType<MetadataRoutingConvention>().First());
			options
				.Select()
				.Filter()
				.OrderBy()
				.Count()
				.SetMaxTop(null)
				.AddRouteComponents("odata", modelBuilder.GetEdmModel());
		});

		return builder;
	}

	/// <summary>
	/// Configures and adds Swagger UI to the WebApp
	/// </summary>
	/// <param name="app"></param>
	/// <returns>The <see cref="WebApplication"/> instance for chaining.</returns>
	public static WebApplication AddSwagger(this WebApplication app)
	{
		var config = app.Configuration.GetSection("Zitadel");
		
		app.UseSwagger();
		app.UseSwaggerUI(options =>
		{
			options.DocumentTitle = "levelbuild Public API";
			options.RoutePrefix = "apidoc";
			options.SwaggerEndpoint("../swagger/v1/swagger.json", "Public API");
			options.OAuthClientId(config["ClientId"]);
			options.OAuthClientSecret(config["ClientSecret"]);
		});
		app.MapSwagger().AllowAnonymous();

		return app;
	}
}