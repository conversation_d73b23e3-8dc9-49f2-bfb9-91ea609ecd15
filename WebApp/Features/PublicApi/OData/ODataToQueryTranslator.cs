using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Constants;
using Levelbuild.Frontend.WebApp.Shared.Utils;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.OData.UriParser;
using Microsoft.OData.UriParser.Aggregation;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.OData;

/// <summary>
/// Translator class to extract DataStoreQueries from OData query params.
/// </summary>
public class ODataToQueryTranslator
{
	private readonly DataStoreQuery _query;
	private readonly ICollection<DataFieldEntity> _dataFields;
	private QueryFilterGroup? _queryFilter;
	
	private readonly string? _rawSelect;
	private readonly FilterClause? _filterClause;
	private readonly ApplyClause? _applyClause;
	private readonly OrderByClause? _orderByClause;
	private readonly SearchClause? _searchClause;
	
	private readonly long? _limit;
	private readonly long? _offset;
	private readonly bool? _count;
	
	#region Init
	
	private ODataToQueryTranslator(ODataQueryOptions oDataQueryOptions, ICollection<DataFieldEntity> fields, bool ignoreSelect = false)
	{
		_rawSelect = oDataQueryOptions.SelectExpand?.RawSelect;
		_filterClause = oDataQueryOptions.Filter?.FilterClause;
		_applyClause = oDataQueryOptions.Apply?.ApplyClause;
		_orderByClause = oDataQueryOptions.OrderBy?.OrderByClause;
		_searchClause = oDataQueryOptions.Search?.SearchClause;
		_limit = oDataQueryOptions.Top?.Value;
		_offset = oDataQueryOptions.Skip?.Value;
		_count = oDataQueryOptions.Count?.Value;
		
		_query = new DataStoreQuery(ignoreSelect ? new List<DataStoreQueryField>() : GetSelectedFields());
		_dataFields = fields;
	}
	
	private ODataToQueryTranslator(
		string? rawSelect,
		FilterClause? filterClause,
		ApplyClause? applyClause,
		OrderByClause? orderByClause,
		SearchClause? searchClause,
		long? top,
		long? skip,
		bool? count,
		bool ignoreSelect = false
	)
	{
		_rawSelect = rawSelect;
		_filterClause = filterClause;
		_applyClause = applyClause;
		_orderByClause = orderByClause;
		_searchClause = searchClause;
		_limit = top;
		_offset = skip;
		_count = count;
		
		_query = new DataStoreQuery(ignoreSelect ? new List<DataStoreQueryField>() : GetSelectedFields());
		_dataFields = new List<DataFieldEntity>();
	}

	/// <summary>
	/// Translates <see cref="ODataQueryOptions"/> to a <see cref="DataStoreQuery"/>.
	/// </summary>
	/// <param name="oDataQueryOptions">OData query options to translate.</param>
	/// <param name="fields">A collection of fields of the data source that gets queried.</param>
	/// <param name="whitelist">Optional whitelist (if not all params are supported by a certain endpoint).</param>
	/// <returns>The resulting DataStoreQuery.</returns>
	public static DataStoreQuery Translate(ODataQueryOptions oDataQueryOptions, ICollection<DataFieldEntity> fields, params string[] whitelist)
	{
		var ignoreSelect = whitelist.Length > 0 && !whitelist.Contains(ODataParameterConstants.Select);
		
		var translator = new ODataToQueryTranslator(oDataQueryOptions, fields, ignoreSelect);
		
		return whitelist.Length > 0 ? translator.TranslateWhitelisted(whitelist) : translator.TranslateAll();
	}

	/// <summary>
	/// Translates the given OData query parts to a <see cref="DataStoreQuery"/>.
	/// 
	/// Mostly for testing purposes.
	/// </summary>
	/// <param name="rawSelect"></param>
	/// <param name="filterClause"></param>
	/// <param name="applyClause"></param>
	/// <param name="orderByClause"></param>
	/// <param name="searchClause"></param>
	/// <param name="top"></param>
	/// <param name="skip"></param>
	/// <param name="count"></param>
	/// <param name="whitelist">Optional whitelist (if not all params are supported by a certain endpoint).</param>
	/// <returns>The resulting DataStoreQuery.</returns>
	public static DataStoreQuery Translate(
		string? rawSelect,
		FilterClause? filterClause,
		ApplyClause? applyClause,
		OrderByClause? orderByClause,
		SearchClause? searchClause,
		long? top,
		long? skip,
		bool? count,
		params string[] whitelist
	)
	{
		var ignoreSelect = whitelist.Length > 0 && !whitelist.Contains(ODataParameterConstants.Select);
		
		var translator = new ODataToQueryTranslator(rawSelect, filterClause, applyClause, orderByClause, searchClause, top, skip, count, ignoreSelect);
		
		return whitelist.Length > 0 ? translator.TranslateWhitelisted(whitelist) : translator.TranslateAll();
	}
	
	private List<DataStoreQueryField> GetSelectedFields()
	{
		var fields = new List<DataStoreQueryField>();
		
		if (string.IsNullOrEmpty(_rawSelect))
			return fields;
		
		if (_rawSelect == "*")
			return fields;
		
		var fieldNames = _rawSelect?.Split(",");
		
		if(fieldNames != null)
			fields.AddRange(fieldNames.Select(fieldName => new DataStoreQueryField(fieldName.Replace("/", "."))));

		return fields;
	}
	
	#endregion
	
	#region Translator Methods
	
	private DataStoreQuery TranslateAll()
	{
		TranslateFilter();
		TranslateGroupBy();
		TranslateOrderBy();
		TranslateSearch();
		ApplyPaging();
		ApplyCount();

		if(_queryFilter != null)
			_query.WithFilter(_queryFilter);
		
		return _query;
	}
	
	private DataStoreQuery TranslateWhitelisted(string[] whitelist)
	{
		if (whitelist.Contains(ODataParameterConstants.Filter))
			TranslateFilter();
		
		if (whitelist.Contains(ODataParameterConstants.Apply))
			TranslateGroupBy();
		
		if (whitelist.Contains(ODataParameterConstants.OrderBy))
			TranslateOrderBy();

		if (whitelist.Contains(ODataParameterConstants.Search))
			TranslateSearch();
		
		if (whitelist.Contains(ODataParameterConstants.Skip) && whitelist.Contains(ODataParameterConstants.Top))
			ApplyPaging();
		
		if (whitelist.Contains(ODataParameterConstants.Count))
			ApplyCount();
		
		if(_queryFilter != null)
			_query.WithFilter(_queryFilter);
		
		return _query;
	}
	
	#region Filter
	
	/// <summary>
	///	Translates the filter AST provided by the OData lib.
	/// See: https://learn.microsoft.com/en-us/archive/blogs/alexj/parsing-filter-and-orderby-using-the-odatauriparser
	/// </summary>
	private void TranslateFilter()
	{
		if (_filterClause == null)
			return;
		
		_queryFilter ??= new QueryFilterGroup();
		ParseGroupNode(_filterClause.Expression, ref _queryFilter);
	}
	
	private void ParseGroupNode(SingleValueNode node, ref QueryFilterGroup filterGroup, BinaryOperatorKind? prevOperatorKind = null)
	{
		// Binary operators ('Field eq Value')
		if (node is BinaryOperatorNode binaryNode)
		{
			switch (binaryNode.OperatorKind)
			{
				case BinaryOperatorKind.And:
				case BinaryOperatorKind.Or:
					var left = binaryNode.Left is ConvertNode leftConvertNode ? leftConvertNode.Source : binaryNode.Left;
					var right = binaryNode.Right is ConvertNode rightConvertNode ? rightConvertNode.Source : binaryNode.Right;
					
					if (binaryNode.OperatorKind == prevOperatorKind)
					{
						ParseGroupNode(left, ref filterGroup, binaryNode.OperatorKind);
						ParseGroupNode(right, ref filterGroup, binaryNode.OperatorKind);
					}
					else
					{
						var childGroup = new QueryFilterGroup()
						{
							LinkType = binaryNode.OperatorKind == BinaryOperatorKind.And ? QueryFilterLinkType.And : QueryFilterLinkType.Or
						};
						
						ParseGroupNode(left, ref childGroup, binaryNode.OperatorKind);
						ParseGroupNode(right, ref childGroup, binaryNode.OperatorKind);
						filterGroup.AddFilterGroup(childGroup);
					}
					
					break;
				default:
					filterGroup.AddFilter(ParseFilterNode(binaryNode)!);
					break;
			}
		}
		// Function calls ('contains(Field,Value)')
		else if (node is SingleValueFunctionCallNode functionNode)
		{
			switch (functionNode.Name.ToLower())
			{
				case "contains":
					var paramNodes = functionNode.Parameters.ToArray();
					
					var left = paramNodes[0];
					var leftValue = left switch
					{
						SingleValueOpenPropertyAccessNode accessNode => accessNode.Name,
						SingleValuePropertyAccessNode accessNode => accessNode.Property.Name,
						ConvertNode { Source: SingleValuePropertyAccessNode } convertNode => 
							((SingleValuePropertyAccessNode)convertNode.Source).Property.Name,
						_ => ((SingleValueOpenPropertyAccessNode)((ConvertNode)left).Source).Name
					};
					
					var field = new QueryFilterField(leftValue, _query.DataSourceName);
					
					var right = paramNodes[1];
					var rightValue = right is ConvertNode rightConvertNode ? ((ConstantNode)rightConvertNode.Source).Value : ((ConstantNode)right).Value;
					
					filterGroup.AddFilter(new LikeFilter(field, $"%{rightValue}%"));
					break;
				default:
					throw new NotSupportedException($"Function '{functionNode.Name}' is not yet supported.");
			}
		}
	}
	
	private QueryFilter? ParseFilterNode(BinaryOperatorNode node)
	{
		var leftValue = node.Left switch
		{
			SingleValueOpenPropertyAccessNode accessNode => accessNode.Name,
			SingleValuePropertyAccessNode accessNode => accessNode.Property.Name,
			ConvertNode { Source: SingleValuePropertyAccessNode } convertNode => 
				((SingleValuePropertyAccessNode)convertNode.Source).Property.Name,
			_ => ((SingleValueOpenPropertyAccessNode)((ConvertNode)node.Left).Source).Name
		};
		
		var field = new QueryFilterField(leftValue, _query.DataSourceName);
		
		var rightValue = node.Right is ConvertNode right ?
							 ((ConstantNode)right.Source).Value :
							 ((ConstantNode)node.Right).Value;

		if (rightValue != null && _dataFields.Any(dataField => string.Equals(dataField.Name, leftValue, StringComparison.InvariantCultureIgnoreCase)))
		{
			var dataField = _dataFields.Single(dataField => string.Equals(dataField.Name, leftValue, StringComparison.InvariantCultureIgnoreCase));
			rightValue = DataStoreUtils.ParseRequestElementDataValue(rightValue, dataField.Type);
		}
		
		switch (node.OperatorKind)
		{
			case BinaryOperatorKind.Equal:
				if (rightValue == null)
					return new IsNullFilter(field);
				
				return new EqualsFilter(field, rightValue);
			case BinaryOperatorKind.NotEqual:
				if (rightValue == null)
					return new NotNullFilter(field);
				
				return new NotEqualsFilter(field, rightValue);
			case BinaryOperatorKind.GreaterThan:
				return new GreaterThanFilter(field, rightValue!);
			case BinaryOperatorKind.GreaterThanOrEqual:
				return new GreaterThanEqualsFilter(field, rightValue!);
			case BinaryOperatorKind.LessThan:
				return new LessThanFilter(field, rightValue!);
			case BinaryOperatorKind.LessThanOrEqual:
				return new LessThanEqualsFilter(field, rightValue!);
			case BinaryOperatorKind.Or:
			case BinaryOperatorKind.And:
			case BinaryOperatorKind.Add:
			case BinaryOperatorKind.Subtract:
			case BinaryOperatorKind.Multiply:
			case BinaryOperatorKind.Divide:
			case BinaryOperatorKind.Modulo:
			case BinaryOperatorKind.Has:
			default:
				return null;
		}
	}
	
	#endregion

	#region Search

	private void TranslateSearch()
	{
		if(_searchClause == null)
			return;
		
		_queryFilter ??= new QueryFilterGroup();
		ParseSearchNode(_searchClause.Expression, ref _queryFilter);
	}
	
	private void ParseSearchNode(SingleValueNode node, ref QueryFilterGroup filterGroup, BinaryOperatorKind? prevOperatorKind = null)
	{
		// Binary operators (AND/OR linked search terms)
		if (node is BinaryOperatorNode binaryNode)
		{
			switch (binaryNode.OperatorKind)
			{
				case BinaryOperatorKind.And:
				case BinaryOperatorKind.Or:
					var left = binaryNode.Left;
					var right = binaryNode.Right;
					
					if (binaryNode.OperatorKind == prevOperatorKind)
					{
						ParseSearchNode(left, ref filterGroup, binaryNode.OperatorKind);
						ParseSearchNode(right, ref filterGroup, binaryNode.OperatorKind);
					}
					else
					{
						var childGroup = new QueryFilterGroup()
						{
							LinkType = binaryNode.OperatorKind == BinaryOperatorKind.And ? QueryFilterLinkType.And : QueryFilterLinkType.Or
						};
						
						ParseSearchNode(left, ref childGroup, binaryNode.OperatorKind);
						ParseSearchNode(right, ref childGroup, binaryNode.OperatorKind);
						filterGroup.AddFilterGroup(childGroup);
					}
					
					break;
				default:
					throw new NotSupportedException($"Search operator '{binaryNode.OperatorKind}' is not yet supported.");
			}
		}
		// Single search terms
		else if (node is SearchTermNode searchTermNode)
		{
			filterGroup.AddFilter(new FulltextSearchFilter(searchTermNode.Text));
		}
	}

	#endregion
	
	private void TranslateGroupBy()
	{
		if (_applyClause == null)
			return;
		
		var groupings = new List<string>();
		var newFields = new List<DataStoreQueryField>();
		foreach (var transformation in _applyClause.Transformations)
		{
			if (transformation is not GroupByTransformationNode node) 
				continue;
			
			var groupingProperties = node.GroupingProperties;
			foreach (var groupingProperty in groupingProperties)
			{
				var name = GetGroupingPropertyName(groupingProperty);
				groupings.Add(name);
				newFields.Add(new DataStoreQueryField(name));
			}
		}
		
		_query
			.WithFields(newFields)
			.WithGroupBy(groupings);
	}
	
	private void TranslateOrderBy()
	{
		if (_orderByClause == null)
			return;
		
		var currentClause = _orderByClause;
		var sortings = new List<DataStoreElementSort>();
		while (currentClause != null)
		{
			currentClause = ParseOrderBy(currentClause);
			var direction = currentClause.Direction == OrderByDirection.Descending ? DataStoreElementSortDirection.Desc : DataStoreElementSortDirection.Asc;
			
			string? fieldName;
			if (currentClause.Expression is SingleValueOpenPropertyAccessNode node)
				fieldName = node.Name;
			else
				fieldName = ((SingleValuePropertyAccessNode)currentClause.Expression).Property.Name;
			
			sortings.Add(new DataStoreElementSort(fieldName, direction));
			
			currentClause = currentClause.ThenBy;
		}
		
		_query.WithOrderBy(sortings);
	}
	
	private void ApplyPaging()
	{
		var hasLimit = _limit is > 0;
		var hasOffset = _offset is > 0;
		
		var limit = hasLimit ? (int)Math.Min(_limit!.Value, Int32.MaxValue) : 10000;
		var offset = hasOffset ? (int)Math.Min(_offset!.Value, Int32.MaxValue) : 0;
		
		_query.WithPaging(limit, offset);
	}
	
	private void ApplyCount()
	{
		if (_count.HasValue && _count!.Value)
			_query.WithCountAll();
	}
	
	private OrderByClause ParseOrderBy(OrderByClause orderByClause)
	{
		if (orderByClause.Expression is not SingleValueOpenPropertyAccessNode castClauseExpression) 
			return orderByClause;
		
		if (castClauseExpression.Source is not SingleValueOpenPropertyAccessNode node) 
			return orderByClause;
		
		var name = node.Name + "." + castClauseExpression.Name;
		var source = node.Source;
		var expression = new SingleValueOpenPropertyAccessNode(source, name);
		orderByClause = new OrderByClause(orderByClause.ThenBy, expression, orderByClause.Direction, orderByClause.RangeVariable);
		if (expression.Source is SingleValueOpenPropertyAccessNode)
			return ParseOrderBy(orderByClause);
		
		return orderByClause;
	}
	
	private static string GetGroupingPropertyName(GroupByPropertyNode groupingNode)
	{
		var expression = groupingNode.Expression;
		var name = string.Empty;
		
		while (expression != null)
		{
			if (expression is SingleValuePropertyAccessNode propertyAccessNode)
			{
				name = string.IsNullOrEmpty(name) ?
						   propertyAccessNode.Property.Name :
						   $"{propertyAccessNode.Property.Name}.{name}";
				
				expression = propertyAccessNode.Source;
				continue;
			}
			
			if (expression is SingleValueOpenPropertyAccessNode openPropertyAccessNode)
			{
				name = string.IsNullOrEmpty(name) ?
						   openPropertyAccessNode.Name :
						   $"{openPropertyAccessNode.Name}.{name}";
				
				expression = openPropertyAccessNode.Source;
				continue;
			}

			expression = null;
		}
		
		return name;
	}
	
	#endregion
}