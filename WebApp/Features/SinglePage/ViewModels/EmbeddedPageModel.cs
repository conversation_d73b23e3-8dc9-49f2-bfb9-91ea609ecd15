using Levelbuild.Core.FrontendDtos.PageView;

namespace Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels;

/// <summary>
/// Frontend model for embedded multi data pages 
/// </summary>
public class EmbeddedPageModel
{
	/// <summary>
	/// Dto which holds the Page config
	/// </summary>
	public required GridViewPageDto Page { get; init; }
	
	/// <summary>
	/// Is used to differentiate between editable form and text preview
	/// </summary>
	public bool? TextView { get; init; }
	
	/// <summary>
	/// Constructor 
	/// </summary>
	/// <param name="textView">readonly multi page?</param>
	public EmbeddedPageModel(bool textView = false)
	{
		TextView = textView;
	}
}