using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.FrontendDtos.Annotations;
using Levelbuild.Core.FrontendDtos.CachedDeepZoom;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Core.FrontendDtos.Shared;

#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels;

/// <summary>
/// Dto for multi data page 
/// </summary>
public class SingleDataModel
{
	/// <summary>
	/// Dto of the page configuration
	/// </summary>
	public required SingleDataPageDto Page { get; init; }
	
	public DataStoreElement? Element { get; init; }
	
	public CachedDeepZoomDto? DeepZoomInfo { get; init; }
	
	public string? BlueprintSourceId { get; init; }
	
	public AnnotationSourceDto? AnnotationSource { get; init; }
	
	public IList<WorkflowInfoDto>? WorkflowInfos { get; init; }
	
	public Guid? CurrentViewId { get; init; }
	
	public string? PageViewSlug { get; init; }

	public IList<PageHeaderElementDto> HeaderElements { get; init; } = [];
}