@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.FrontendDtos.Page
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.MultiPage.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels.EmbeddedPageModel;
@inject IExtendedStringLocalizerFactory LocalizerFactory
@{
	var createPageLocalizer = LocalizerFactory.Create("CreatePage", "");
	var multiPageLocalizer = LocalizerFactory.Create("MultiPage", "");
}

@if (Model.Page.EmbeddedPage?.Views == null)
{
	// do not render any embedded page which has no configured views
	return;
}

@{
	var embeddedPageToken = $"embedded-page-{Model.Page.Id}";
	var embeddedPage = (MultiDataPageDto)Model.Page.EmbeddedPage;
}
<!--suppress CssUnusedSymbol -->
<style>
	.embedded_section {
		--content-padding: var(--size-spacing-s) var(--size-spacing-m) var(--size-spacing-m);
	}
	
	.embedded_section__page {
		position:  relative;
		min-height: 111px;
	}

	.embedded_section__loading {
		position:  absolute;
		inset: 0;
		background-color: var(--clr-background-lvl-0);
		z-index: 3;
		opacity: 0;
		pointer-events: none;
		transition: opacity var(--animation-time-medium) ease;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: var(--size-spacing-m);
		color: var(--clr-text-tertiary);
		font-size: var(--size-text-m);
		
		& > i {
			font-size: var(--size-text-l);
		}
	}
	
	.embedded_section__page.loading .embedded_section__loading {
		opacity: 1;
		pointer-events: auto;
	}
	
	.page-view__wrapper {
		height: calc(100% - 4rem);
		
		& .page-view {
			height: 100%;
		}
	}
	
	.embedded_section__page .dropzone-preview {
		container-name: dropzone;
		container-type: size;
		
		padding: 0;
		border-radius: 4px;
		top: var(--dropzone-top, 0);
		right: 0;
		bottom: var(--dropzone-bottom, 0);
		left: 0;

		&  > div {
			position : absolute;
			inset: 0;
			width:  auto;
			height:  auto;
			border-width: 3px;
			font-size: 1.5rem;
			margin: 3.5rem;
			padding: 3rem;
			
			& > i {
				font-size: var(--size-text-xxl);
			}
		}
	}

	@@container dropzone (width < 500px) or (height < 200px) {
		.embedded_section__page .dropzone-preview > div {
			margin: 2.5rem;
			padding: 2rem;
			font-size: 1.4rem;

			& > i {
				font-size: var(--size-text-xl);
			}
		}
	}

	@@container dropzone (height < 150px) {
		.embedded_section__page .dropzone-preview > div {
			margin: 1.6rem;
			padding: 1.6rem;
		}
	}

	@@container dropzone (height < 120px) {
		.embedded_section__page .dropzone-preview > div {
			& > i {
				display:  none;
			}
		}
	}

	@@container dropzone (height < 80px) {
		.embedded_section__page .dropzone-preview > div {
			border-width: 0;
			margin: 0;
		}
	}

	@@container dropzone (height < 50px) {
		.embedded_section__page .dropzone-preview > div {
			font-size: 1.2rem;
		}
	}

	.embedded_section__page > lvl-query-view-action-bar,
	.embedded_section__page > .page-view__wrapper {
		transition: opacity 150ms ease-in;
	}
	
	.embedded_section__page.dz-drag-hover > lvl-query-view-action-bar,
	.embedded_section__page.dz-drag-hover > .page-view__wrapper {
		opacity:  0.25;
	}
</style>
<script type="module" defer>
	const section = document.getElementById('@embeddedPageToken')
	const column = section.closest('.page-column')

	section.querySelector('[data-action=create]')?.addEventListener('click', handleCreateButtonClick)
	section.querySelector('lvl-multi-data-view').onCreateClick = handleCreateButtonClick

	function collectDefaultValues() {
		const parentElementId = Page.getFormData()['@(Model.Page.ReferenceType == GridViewPageReferenceType.ForeignElement ? Model.Page.ReferenceField?.Name ?? "Id" : "Id")']
		let defaultValues = { '@Model.Page.KeyField?.Name' : parentElementId }
		@foreach (var filters in Model.Page.Filters.GroupBy(filter => filter.FilterFieldId))
		{
			@if (filters.Count() == 1 && filters.First().Operator == CompareOperator.Equals)
			{
				var filter = filters.First();
				@:defaultValues['@filter.FilterFieldName'] = Page.replacePlaceholders('@filter.CompareValue')
			}
		}
		return defaultValues
	}
	
	function handleCreateButtonClick() {
		FileManager.showCreatePage({
			target: section.querySelector('.embedded_section__page'),
			dataSourceId: '@Model.Page.EmbeddedPage?.DataSourceId',
			createPageId: '@embeddedPage.CreatePageId',
			defaultValues: collectDefaultValues(),
			embedded: true,
			allowFile: @(Model.Page is { AllowCreate: true, EmbeddedPage.DataSource.AllowFile: true } ? "true" : "false"),
			saveButtonLabel: '@(createPageLocalizer[!String.IsNullOrEmpty(embeddedPage.CreatePage?.SaveButtonLabel) ? embeddedPage.CreatePage.SaveButtonLabel : "saveButton"])'
		}).then((success) => {
			if (success)
				section.querySelector('.embedded_section__page lvl-multi-data-view')?.reload()
		})
	}

	section.querySelector('[data-action=maximize]')?.addEventListener('click', async event => {
		const maximizedMultiPageUrl = `${Page.getMainPageUrl()}/EmbeddedPages/@(Model.Page.Id)` 
		if (event.ctrlKey) {
			Page.openNewTab(maximizedMultiPageUrl, true)
			return
		}
		// define callbacks for resolve (and cancel)
		const callback = () => {
			section.querySelector('[data-action=maximize]').click()
		}

		// define event and put callbacks in detail
		const pageLeaveEvent = new CustomEvent('initiate-page-leave', {
			cancelable: true,
			detail: {
				resolveCallback: callback
			}
		})

		// dispatch the page-leave-event and stop if it gets canceled
		if(!window.dispatchEvent(pageLeaveEvent))
			return

		await Page.load(maximizedMultiPageUrl)
	})

	@if (Model.Page is { AllowCreate: true, EmbeddedPage.DataSource.AllowFile: true })
	{
		<text>
			const dropzoneContainer = section.querySelector('.embedded_section__page')
			FileManager.initDropzone({
				target: dropzoneContainer,
				dataSourceId: '@Model.Page.EmbeddedPage?.DataSourceId',
				createPageId: '@embeddedPage.CreatePageId',
				defaultValues: collectDefaultValues,
				embedded: true
			})
			
			const calculateDropzonePosition = () => {		
				const columnRect = column.getBoundingClientRect()
				const sectionRect = section.getBoundingClientRect()
				if (columnRect.y > sectionRect.y)
					section.style.setProperty("--dropzone-top", (columnRect.y - sectionRect.y - 1)+"px")
				else
					section.style.removeProperty("--dropzone-top")
				if (sectionRect.bottom > columnRect.bottom)
					section.style.setProperty("--dropzone-bottom", (sectionRect.bottom - columnRect.bottom - 1)+"px")
				else
					section.style.removeProperty("--dropzone-bottom")
			}
			
			section.querySelector('.embedded_section__page').addEventListener('dragenter', _ => {
				calculateDropzonePosition()
				column.addEventListener('scroll', calculateDropzonePosition)
			})
			section.querySelector('.embedded_section__page').addEventListener('dragleave', _ => {
				column.removeEventListener('scroll', calculateDropzonePosition)
			})
		</text>
	}
</script>
<section-component id="@embeddedPageToken" class="embedded_section" ignore-overflow calc-height
                   heading="@(Model.Page.TitleTranslated)" allow-collapse="@(Model.Page.AllowMinimize.HasValue && Model.Page.AllowMinimize.Value)"
                   collapsed="@(Model.Page.StartMinimized.HasValue && Model.Page.StartMinimized.Value)" max-height="@(Model.Page.MaxHeight is > 0 and < 190 ? 190 : Model.Page.MaxHeight)"
                   sticky="@(Model.Page.SectionId == null)">
	@if (Model.Page.AllowCreate == true && (Model.Page.EmbeddedPage as MultiDataPageDto)?.CreatePageId != null)
	{
		<button-component slot="action" data-action="create" size="FontSize.Medium" icon="circle-plus" icon-style="IconStyle.Solid" color="ColorState.Active"></button-component>
	}
	@if (Model.Page.AllowMaximize.HasValue && Model.Page.AllowMaximize.Value)
	{
		<button-component slot="action" data-action="maximize" size="FontSize.Medium" icon="arrows-maximize" color="ColorState.Active"></button-component>
	}
	<div class="embedded_section__page loading" data-multi-view-container>
		<div class="embedded_section__loading">
			<i class="fa-duotone fa-spinner-third fa-spin"></i>
			<span>@multiPageLocalizer["loadingContents"]</span>
		</div>
		<script type="module">
			// catch loading of embedded page data is done (to hide the loading overlay)
			const embeddedSection = document.querySelector('#@embeddedPageToken .embedded_section__page')
			embeddedSection.addEventListener("query-view:loading-done", _ => {
				embeddedSection.classList.remove("loading")
			})
		</script>
		@await Html.PartialAsync("~/Features/MultiPage/Views/_PageView.cshtml", new MultiDataModel()
		{
			Page = (MultiDataPageDto)Model.Page.EmbeddedPage!,
			GridViewPage = Model.Page,
			Embedded = true
		})
	</div>
</section-component>