using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Core.FrontendDtos.User;
using Levelbuild.Core.ZitadelApiInterface;
using Levelbuild.Core.ZitadelApiInterface.Constants;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.UserCustomerMapping;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.User.ViewModels;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.User.Controllers;

/// <summary>
/// Controller for the configuration view of users
/// </summary>
public class AdminUserController : AdminController<UserDto>
{
	private readonly IZitadelApiClientFactory _apiClientFactory;
	
	/// <inheritdoc />
	public AdminUserController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, IZitadelApiClientFactory apiClientFactory,
						  UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) :
		base(logManager, logManager.GetLoggerForClass<AdminUserController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		_apiClientFactory = apiClientFactory;
	}
	
	#region Views
	
	/// <summary>
	/// Renders the base list view
	/// </summary>
	/// <returns>rendered list view</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Users/")]
	public IActionResult List()
	{
		return CachedPartial() ?? RenderPartial(new UserList());
	}
	
	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Users/<USER>")]
	public IActionResult Create()
	{
		var customers = new List<CustomerDto>();
		customers.AddRange(DatabaseContext.Customers.ToList()
							   .Select(customer => new CustomerDto { Id = customer.Id, DisplayName = customer.DisplayName }));
		return CachedPartial() ?? RenderPartial(new UserForm { Customers = customers }, "List", new UserList());
	}
	
	/// <summary>
	/// Renders the detail view with help of the user dto
	/// </summary>
	/// <param name="slug">readable identifier for a specific user</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Users/<USER>")]
	[HttpGet("/Admin/Users/<USER>")]
	public IActionResult Detail(string slug)
	{
		var customers = new List<CustomerDto>();
		customers.AddRange(DatabaseContext.Customers.ToList()
							   .Select(customer => new CustomerDto { Id = customer.Id, DisplayName = customer.DisplayName }));
		
		if (string.IsNullOrEmpty(slug))
		{
			return CachedPartial() ?? RenderPartial(new UserForm(ViewType.Edit) { User = null, Customers = customers });
		}
		
		UserEntity? user = null;
		try
		{
			user = DatabaseContext.Users.FirstOrDefault(userEntity => userEntity.Slug == slug.ToLower());
			if (user == null)
				return new NotFoundObjectResult($"User configuration with slug: {slug} could not be found");
			if (user.MainCustomer == null)
				return new NotFoundObjectResult($"Main Customer of User with: {slug} could not be found");
			
			var zitadelUser = GetUserFromZitadel(user.RemoteId, user.MainCustomer!.RemoteId);
			
			if (zitadelUser != null)
				CheckAndUpdateUserInfo(user, zitadelUser);
			else
				Logger.Warning("User '{DisplayName}' not found in Zitadel", user.DisplayName);
		}
		catch (Exception e)
		{
			Logger.Error(e, "User configuration could not be loaded");
		}
		
		return RenderPartial(new UserForm(ViewType.Edit)
		{
			User = user!.ToDto(),
			Customers = customers
		});
	}
	
	#endregion
	
	#region Actions
	
	/// <inheritdoc />
	[HttpGet("/Api/Users/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		var query = DatabaseContext.Users.Include(user => user.MainCustomer)
			.Include(user => user.CustomerMapping
						 .Where(mapping => mapping.CustomerId == mapping.User!.MainCustomerId));
		return HandleQueryRequest<UserEntity, UserDto>(query, parameters);
	}
	
	/// <summary>
	/// Returns a mutated list of machine users as JSON.
	/// </summary>
	/// <param name="parameters">an object to query a Subset of entities</param>
	[HttpGet("/Api/MachineUsers/")]
	public ActionResult<FrontendResponse> QueryMachineUsers(QueryParamsDto parameters)
	{
		var query = DatabaseContext.Users.Include(user => user.MainCustomer).Where(user => user.IsMachineUser);
		return HandleQueryRequest<UserEntity, UserDto>(query, parameters);
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/Users/<USER>")]
	[HttpGet("/Api/MachineUsers/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		var query = DatabaseContext.Users
			.Include(user => user.MainCustomer);
		
		var result = HandleGetRequest<UserEntity, UserDto>(query, id);
		
		// if we get a valid OkResult we need to check if firstName and lastName are set and up to date 
		if (result.Result?.GetType() == typeof(OkObjectResult))
			return CheckAndUpdateUserInfoInFrontendResponse((OkObjectResult)result.Result, id);
		
		return result;
	}
	
	/// <inheritdoc />
	[HttpPost("/Api/Users/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] UserDto userDto)
	{
		var client = _apiClientFactory.GetManagementClient();
		
		if (string.IsNullOrEmpty(userDto.Username))
			return GetBadRequestResponse("Property name is not valid.");
		
		try
		{
			if (!IsUniquePropertyValue(DatabaseContext.Users, "DisplayName", userDto.Username))
				throw new Exception($"User with name: {userDto.Username} already exists.");

			var tryParse = Guid.TryParse(userDto.MainCustomerId, out var guid);
			if (!tryParse)
			{
				const string message = "Invalid customer Id!";
				
				Logger.Warning(message);
				return GetBadRequestResponse(message);
			}
			
			var customerInstance = DatabaseContext.Customers.SingleOrDefault(customer => customer.Id == guid);
			if (customerInstance == null)
			{
				const string message = "Customer not found.";
				
				Logger.Warning(message);
				return GetNotFoundResponse(message);
			}

			var remoteId = userDto.IsMachineUser == true ? client.AddMachineUser(userDto, customerInstance.RemoteId) : client.AddHumanUser(userDto, customerInstance.RemoteId);
			
			client.AddUserGrant(remoteId, customerInstance.RemoteId, [AuthRole.User]);
			
			var userEntity = UserEntity.FromDto(userDto, remoteId);
			
			await DatabaseContext.Users.AddAsync(userEntity);
			await DatabaseContext.SaveChangesAsync();
			
			// Needs to be set after creating the entity, since we need the Id for encryption
			if (userEntity.IsMachineUser)
			{
				// TODO: Make expiration date configurable
				var expirationDate = DateTime.Now.AddYears(1).ToUniversalTime();
				var token = client.CreatePersonalAccessToken(remoteId, customerInstance.RemoteId, expirationDate);
				userEntity.PersonalAccessToken = token;
				userEntity.PersonalAccessTokenExpirationDate = expirationDate;
			}
			
			await DatabaseContext.UserCustomerMappings.AddAsync(new UserCustomerMappingEntity()
			{
				UserId = userEntity.Id,
				CustomerId = customerInstance.Id
			});
			
			await DatabaseContext.SaveChangesAsync();
			
			return GetOkResponse(userEntity.ToDto());
		}
		catch (Exception e)
		{
			Logger.Error(e, "User could not be created");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <inheritdoc />
	[HttpPatch("/Api/Users/<USER>")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] UserDto userDto)
	{
		try
		{
			if (!string.IsNullOrEmpty(userDto.Username) && !IsUniquePropertyValue(DatabaseContext.Users, "DisplayName", userDto.Username, id))
				throw new Exception($"User with name: {userDto.Username} already exists.");
			
			var userInstance = DatabaseContext.Users.Include(user => user.MainCustomer).SingleOrDefault(user => user.Id == id);
			if (userInstance == null)
				return GetNotFoundResponse($"User with id: {userDto.Id} could not be found");
			if (userInstance.MainCustomer == null)
				return new NotFoundObjectResult($"Main Customer of User with id: {userDto.Id} could not be found");
			
			var client = _apiClientFactory.GetManagementClient();
			// User info changed?
			client.UpdateHumanUser(userInstance.RemoteId, userInstance.MainCustomer!.RemoteId, userDto);
			userInstance.UpdatePartial(userDto);
			await DatabaseContext.SaveChangesAsync();
			return GetOkResponse(userInstance.ToDto());
		}
		catch (Exception e)
		{
			Logger.Error(e, "User could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Deactivates the given user.
	/// </summary>
	/// <param name="id">id of a specific user</param>
	/// <returns>OK Response for successful update and BAD Response when an errors occurred</returns>
	[HttpPost("/Api/Users/<USER>/Deactivate")]
	public ActionResult<FrontendResponse> DeactivateUser(Guid id)
	{
		return ToggleActive(id, false);
	}
	
	/// <summary>
	/// Deactivates the given user.
	/// </summary>
	/// <param name="id">id of a specific user</param>
	/// <returns>OK Response for successful update and BAD Response when an errors occurred</returns>
	[HttpPost("/Api/Users/<USER>/Activate")]
	public ActionResult<FrontendResponse> ActivateUser(Guid id)
	{
		return ToggleActive(id, true);
	}
	
	/// <inheritdoc />
	[HttpDelete("/Api/Users/<USER>")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		try
		{
			var userInstance = DatabaseContext.Users.Include(user => user.MainCustomer).FirstOrDefault(user => user.Id == id);
			if (userInstance == null)
				return GetNotFoundResponse($"User configuration with id: {id} could not be found");
			if (userInstance.MainCustomer == null)
				return new NotFoundObjectResult($"Main Customer of User with id: {id} could not be found");
			
			var client = _apiClientFactory.GetManagementClient();
			client.DeleteUser(userInstance.RemoteId, userInstance.MainCustomer!.RemoteId);
			
			return HandleDeleteRequest(DatabaseContext.Users, id);
		}
		catch (Exception e)
		{
			Logger.Error(e, "User could not be deleted");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Creates a new PAT for a machine user and returns it.
	/// </summary>
	/// <returns>OK Response for successful request and BAD Response when an errors occurred.</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Api/Users/<USER>/RefreshPersonalAccessToken")]
	public ActionResult<FrontendResponse> ZitadelPersonalAccessTokenRefresh(Guid id)
	{
		try
		{
			var userInstance = DatabaseContext.Users
				.Include(user => user.MainCustomer)
				.FirstOrDefault(user => user.Id == id);
			
			if (userInstance == null)
				return GetNotFoundResponse($"User with id: '{id}' could not be found.");
			
			if (!userInstance.IsMachineUser)
				return GetBadRequestResponse($"User with id: {id} is not a machine user.");
			
			var client = _apiClientFactory.GetManagementClient();
			// TODO: Make expiration date configurable
			var expirationDate = DateTime.Now.AddYears(1).ToUniversalTime();
			var token = client.CreatePersonalAccessToken(userInstance.RemoteId, userInstance.MainCustomer!.RemoteId, expirationDate);
			userInstance.PersonalAccessToken = token;
			userInstance.PersonalAccessTokenExpirationDate = expirationDate;
			
			DatabaseContext.SaveChanges();
			
			return GetOkResponse(ServerResponsePayload.FromDictionary(new Dictionary<string, object>()
			{
				{ "token", token },
				{ "expirationDate", expirationDate }
			}));
		}
		catch (Exception e)
		{
			Logger.Error(e, $"PAT could not be refreshed.");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Delivers a json with a list of all users matching the parameters 
	/// </summary>
	/// <param name="parameters">query parameter</param>
	/// <returns></returns>
	[HttpGet("/Api/Users/<USER>")]
	public ActionResult<FrontendResponse> Modules(QueryParamsDto parameters)
	{
		var query = DatabaseContext.Users;
		return HandleAutocompleteRequest(query, parameters,
										 nameof(UserEntity.Id),
										 nameof(UserEntity.DisplayName),
										 new PropertyPathList<UserEntity>()
										 {
											 nameof(UserEntity.DisplayName),
											 nameof(UserEntity.Slug)
										 });
	}
	
	#endregion
	
	private Zitadel.User.V1.User? GetUserFromZitadel(string zitadelUserId, string zitadelCustomerId)
	{
		try
		{
			return _apiClientFactory.GetManagementClient().GetUser(zitadelUserId, zitadelCustomerId);
		}
		catch (Exception ex)
		{
			Logger.Error(ex, "Error trying to fetch user from Zitadel");
			return null;
		}
	}
	
	private void CheckAndUpdateUserInfo(UserEntity userInstance, Zitadel.User.V1.User zitadelUser)
	{
		// TODO: Separate update logic needed here?
		if (userInstance.IsMachineUser)
			return;
		
		userInstance.FirstName = zitadelUser.Human.Profile.FirstName;
		userInstance.LastName = zitadelUser.Human.Profile.LastName;
		
		DatabaseContext.SaveChanges();
	}
	
	private ActionResult<FrontendResponse> CheckAndUpdateUserInfoInFrontendResponse(OkObjectResult result, Guid id)
	{
		var userDto = (result.Value as FrontendResponse<UserDto>)?.Data;
		
		// TODO: Separate update logic needed here?
		if (userDto?.IsMachineUser == true)
			return Ok(result.Value);
		
		if (userDto == null)
			return new NotFoundObjectResult($"User with id: {id} could not be found");
		var userInstance = DatabaseContext.Users.Include(user => user.MainCustomer).FirstOrDefault(user => user.Id == userDto.Id)!;
		if (userInstance.MainCustomer == null)
			return new NotFoundObjectResult($"Main Customer of User with id: {userDto.Id} could not be found");
		
		var zitadelUser = GetUserFromZitadel(userInstance.RemoteId, userInstance.MainCustomer!.RemoteId);
		
		if (zitadelUser == null)
		{
			Logger.Warning("User '{DisplayName}' not found in Zitadel", userInstance.DisplayName);
			return Ok(result.Value);
		}
		
		if (userInstance.FirstName == zitadelUser.Human.Profile.FirstName && userInstance.LastName == zitadelUser.Human.Profile.LastName)
			return Ok(result.Value);
		
		CheckAndUpdateUserInfo(userInstance, zitadelUser);
		
		var query = DatabaseContext.Users.Include(user => user.MainCustomer);
		return HandleGetRequest<UserEntity, UserDto>(query, id);
	}
	
	private ActionResult<FrontendResponse> ToggleActive(Guid id, bool activate)
	{
		try
		{
			var userInstance = DatabaseContext.Users.Include(user => user.MainCustomer).FirstOrDefault(user => user.Id == id);
			if (userInstance == null)
				return GetNotFoundResponse($"User configuration with id: {id} could not be found");
			if (userInstance.MainCustomer == null)
				return GetNotFoundResponse($"Main Customer of User with id: {id} could not be found");
			if (userInstance.Enabled == activate)
				return GetBadRequestResponse($"User is already { (activate ? "active" : "disabled") }.");

			var client = _apiClientFactory.GetManagementClient();
			if(userInstance.Enabled)
				client.DeactivateUser(userInstance.RemoteId, userInstance.MainCustomer!.RemoteId);
			else
				client.ReactivateUser(userInstance.RemoteId, userInstance.MainCustomer!.RemoteId);
			userInstance.Enabled = !userInstance.Enabled;
			DatabaseContext.SaveChanges();
			
			return GetOkResponse(userInstance.ToDto());
		}
		catch (Exception e)
		{
			const string message = "User active state could not be changed";
			Logger.Error(e, message);
			return GetBadRequestResponse(message);
		}
	}
}