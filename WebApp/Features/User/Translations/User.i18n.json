{"list": {"de": {"pageTitle": "<PERSON><PERSON><PERSON>", "menuTitle": "Benutzerarten", "newItem": "<PERSON><PERSON><PERSON>"}, "en": {"pageTitle": "Users", "menuTitle": "User types", "newItem": "New User"}}, "menu": {"de": {"item/userList": "Menschliche Nutzer", "item/machineUserList": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "en": {"item/userList": "Human Users", "item/machineUserList": "Machine Users"}}, "*": {"de": {"displayName": "Anzeigename", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "email": "Email", "mainCustomerName": "Kundenkontext", "profile": "Profil", "isMachineUser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userCompositeName": "<PERSON><PERSON><PERSON>", "isAdmin": "Adminrechte", "isAdminTrueTooltip": "Adminrechte", "isAdminFalseTooltip": "keine <PERSON>", "initialPassword": "Initial Passwort", "zitadelPasswordValidation": "Passwort muss mindestens 8 Zeichen lang sein und mindestens einen Kleinbuchstaben, einen <PERSON>buchstaben, eine <PERSON> und ein Sonderzeichen enthalten.", "personalAccessToken": "Zugangstoken", "personalAccessTokenExpirationDate": "Zugangstoken-Ablaufdatum", "delete": "Löschen", "deleting": "Element wird entfernt ...", "deactivate": "Deaktivieren", "reactivate": "Reaktivieren", "deletionWarningHeading": "Benutzer löschen?", "deletionWarning": "Sind <PERSON> sicher, dass Si<PERSON> diesen Benutzer löschen möchten?", "abort": "Abbrechen", "ok": "Löschen", "patRefresh/success": "Zugangstoken wurde erfolgreich erneuert", "patRefresh/error": "Fehler beim Erneuern des Zugangstokens", "impersonation/error": "Identität konnte nicht übernommen werden", "deactivate/notification/error": "Fehler beim Deaktivieren des Nutzers", "deactivate/notification/complete": "Deaktivieren erfolgreich", "reactivate/notification/error": "Fehler beim Reaktivieren des Nutzers", "reactivate/notification/complete": "Reaktivieren erfolgreich"}, "en": {"displayName": "Display name", "firstName": "First name", "lastName": "Last name", "email": "Email", "mainCustomerName": "Customer context", "profile": "Profile", "isMachineUser": "Machine user", "userCompositeName": "User", "isAdmin": "Admin rights", "isAdminTrueTooltip": "Admin rights", "isAdminFalseTooltip": "no Admin rights", "initialPassword": "Initial password", "zitadelPasswordValidation": "Password needs to be at least 8 characters long and must contain a lowercase and an uppercase latter, a number and a special character.", "personalAccessToken": "Access token", "personalAccessTokenExpirationDate": "Access token expiry date", "delete": "Delete", "deleting": "Removing element ...", "deactivate": "Deactivate", "reactivate": "Reactivate", "deletionWarningHeading": "Delete user?", "deletionWarning": "Are you sure you want to delete this user?", "abort": "Abort", "ok": "Delete", "patRefresh/success": "Access token has been successfully renewed", "patRefresh/error": "Error when renewing the access token", "impersonation/error": "User could not be impersonated", "deactivate/notification/error": "Error deactivating user", "deactivate/notification/complete": "Deactivation successful", "reactivate/notification/error": "Error reactivating user", "reactivate/notification/complete": "Reactivation successful"}}}