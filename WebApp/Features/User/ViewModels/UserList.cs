using System.Diagnostics.CodeAnalysis;
using Levelbuild.Frontend.WebApp.Shared.ViewModels;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.User.ViewModels;

// @TWO: getters are never used?
[ExcludeFromCodeCoverage]
public class UserList
{
	public IList<MenuItem> MenuItems { get; set; }
	
	public IList<MenuInfo> MenuInfos { get; set; }
	
	public UserList()
	{
		MenuItems = new List<MenuItem>
		{
			new("userList", "UserList", "user"),
			new("machineUserList", "MachineUserList", "robot")
		};
		MenuInfos = new List<MenuInfo>();
	}
}