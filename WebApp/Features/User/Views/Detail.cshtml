@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminDetailPage
@model Levelbuild.Frontend.WebApp.Features.User.ViewModels.UserForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("User", "");
}
<script type="module" defer>
	@if (Model.User != null)
	{
		@:Page.setMainPage(`/Admin/Users/<USER>
		@:Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: '/Admin/Users' }, { label: '@Model.User.Username', url: `/Admin/Users/<USER>
	}

	if (Page.getFormData().enabled === true || '@(Model.User?.Enabled == true)' === 'True') {
		Page.buttonConfig.deactivateButton.addEventListener('click', async () =>
		{
			const id = Page.getFormData().id
			const toaster = document.getElementById('toaster')
			const response = await fetch(`/Api/Users/<USER>/Deactivate`, { method: 'POST' })
			if (!response.ok) {
				toaster.notifySimple({ heading: '@localizer["deactivate/notification/error"]', type: 'error' })
				return
			}
			history.back()
		}, { signal: Page.getPageChangeSignal() })
		Page.buttonConfig.deactivateButton.skeleton = false
		Page.buttonConfig.deactivate = true
	}

	if (Page.getFormData().enabled === false || '@(Model.User?.Enabled == false)' === 'True') {
		Page.buttonConfig.reactivateButton.addEventListener('click', async () => {
			const id = Page.getFormData().id
			const toaster = document.getElementById('toaster')
			const response = await fetch(`/Api/Users/<USER>/Activate`, { method: 'POST' })
			if (!response.ok) {
				toaster.notifySimple({ heading: '@localizer["reactivate/notification/error"]', type: 'error' })
				return
			}
			history.back()
		}, { signal: Page.getPageChangeSignal() })
		Page.buttonConfig.reactivateButton.skeleton = false
		Page.buttonConfig.reactivate = true
	}
</script>
<vc:admin-detail-page entity="User" route-name="Users" model="@Model" title="@Model.User?.Username" show-default-buttons="@(Model.User != null)" show-deletion-warning-toast="true" deletion-warning-toast-heading="@localizer["deletionWarningHeading"]" deletion-warning-toast-message="@localizer["deletionWarning"]"></vc:admin-detail-page>