@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.User.Services
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@inject IExtendedStringLocalizerFactory LocalizerFactory
@inject UserManager UserManager
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("User", "");
	
	List<BaseColumnComponentTagHelper> columns =
	[
		new ListColumnComponentTagHelper
		{
			Type = ListColumnType.Flag,
			Name = "isMachineUser",
			WithConverter = true
		},
		
		new ListDataColumnComponentTagHelper
		{
			Name = "username",
			MinWidth = 30,
			MinWidthUnit = DimensionUnit.Percent
		},

		new ListDataColumnComponentTagHelper
		{
			Name = "userCompositeName",
			Label = localizer["userCompositeName"]
		},

		new ListDataColumnComponentTagHelper
		{
			Name = "email",
			Label = localizer["email"]
		},

		new ListDataColumnComponentTagHelper
		{
			Name = "mainCustomerName",
			Label = localizer["mainCustomerName"]
		},
		
		new ListDataColumnComponentTagHelper
		{
			Name = "isMainCustomerAdmin",
			Type = DataColumnType.Icon,
			Icon = "hammer",
			Width = 36,
			LiveEditable = true,
			IconTooltipYes = localizer["isAdminTrueTooltip"],
			IconTooltipNo = localizer["isAdminFalseTooltip"]
		},
		
		new ListColumnComponentTagHelper
		{
			Type = ListColumnType.OptionMenu,
			Name = "options",
			WithConverter = true
		}
	];

	List<DropdownMenuItemComponentTagHelper> activeColumns =
	[
		new()
		{
			Action = "Deactivate",
			Label = localizer["deactivate"],
			IconLeft = "power-off",
			ClickFunction = "deactivateUser"
		},
		new()
		{
			Action = "Delete",
			Label = localizer["delete"],
			IconLeft = "trash",
			IconLeftColor = "var(--clr-signal-error)",
			ClickFunction = "deleteUser"
		}
	];
	
	List<DropdownMenuItemComponentTagHelper> inactiveColumns =
	[
		new()
		{
			Action = "Activate",
			Label = localizer["reactivate"],
			IconLeft = "power-off",
			ClickFunction = "activateUser"
		},
		new()
		{
			Action = "Delete",
			Label = localizer["delete"],
			IconLeft = "trash",
			IconLeftColor = "var(--clr-signal-error)",
			ClickFunction = "deleteUser"
		}
	];
	
	List<QueryParamSortingDto> sortings =
	[
		new() { OrderColumn = "DisplayName", Direction = SortDirection.Asc }
	];
}

<style>
	.panel {
		position: relative;
		height: 100%;
		width: var(--panel-width);
		border-right: 2px solid var(--clr-background-lvl-1);
		overflow: hidden;
		left: 0;
		transition: left var(--animation-time-medium) ease-in-out;
	}

	.panel[data-state="closed"] {
		left: calc(var(--small-panel-width) - var(--panel-width));

		& :is(span, i):not([data-action="toggle"]) {
			display: none;
		}
	}

	div.dialog-main-content {
		margin: var(--size-spacing-l);
	}
</style>

<script type="module" defer>
	Page.setTitle('@localizer["list/pageTitle"]')
	Page.setMainPage(`/Admin/Users`)
	Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: Page.getMainPageUrl() }])
	
	const renderUserIcon = (data) => {
		return data.isMachineUser === true ? `<i class="fa-light fa-robot"></i>` : `<i class="fa-light fa-user"></i>`
	}
	const renderDeleteIcon = () => {
		return  `<i class="fa-light fa-trash" style="color: var(--clr-signal-error)" data-action="delete" onclick="removeUser(event)"></i>` 
	}
	
	const renderOptionMenu = (data) => {
		let optionMenuHtml 
		if (data.enabled) {
			optionMenuHtml = `
				<lvl-menu class="filter-menu">
					@foreach (var option in activeColumns)
					{
						@: @Html.Raw(option.CreateTagHelperOutput())
					}
				</lvl-menu>`
		} else {
			optionMenuHtml = `
				<lvl-menu class="filter-menu">
					@foreach (var option in inactiveColumns)
					{
						@: @Html.Raw(option.CreateTagHelperOutput())
					}
				</lvl-menu>`
		}
		return optionMenuHtml
	}
	
	document.querySelector('lvl-list-column[name="isMachineUser"]').converter = renderUserIcon
	document.querySelector('lvl-list-column[name="options"]').converter = renderOptionMenu
</script>

<!-- Amazon filter panel on the left side -->
<aside class="page__filter panel" data-state="open">
	@* @TODO MVP: Replace Mockup *@
	@await Html.PartialAsync("_AmazonFilterMockup.cshtml")
</aside>
<vc:admin-list-page entity="User" route-name="Users" localizer="@localizer" sorting="@sortings" display-property-name="username" columns="@columns" active-column="enabled"
                    open-on-row-click="true" parent-property-name="mainCustomerId" parent-property-value="@(await UserManager.GetCurrentCustomerAsync()).Id">
</vc:admin-list-page>
<vc:create-panel entity="User" route-name="Users" localizer="@localizer"></vc:create-panel>
<lvl-dialog name="user-delete-dialog" heading="@localizer["deleteDialog/heading"]" icon="trash" modal="true" style="display:none;" custom-keydown-handler>
	<div class="dialog-main-content">
		<div>@localizer["deleteDialog/content"]</div>
	</div>
	<button-component slot="button-left" class="dialog-button--tertiary" type="ButtonType.Tertiary" label="@localizer["deleteDialog/no"]"></button-component>
	<button-component slot="button-right" class="dialog-button--primary" type="ButtonType.Primary" label="@localizer["deleteDialog/yes"]"></button-component>
</lvl-dialog>

<script>
	async function deleteUser(event) {
		const listLine = this.closest('lvl-list-line')
		const columnEnumeration = document.getElementById('user-list')
		const columnList = columnEnumeration.querySelector('lvl-list')
		if (!listLine || listLine.tagName !== 'LVL-LIST-LINE'){
			console.error('List line not found. Column cannot be removed')
			return
		}

		const position = Number(listLine.dataset['position'])
		const currentRow = columnList.rows[position]
		
		const callback = async (elementId) => {
			Overlay.showWait("@localizer["deleting"]")
			const response = await fetch(`/Api/Users/<USER>'DELETE' })
			if (response.ok)
				columnEnumeration.reload() // -> only reload listLine, when implemented
			Overlay.hideWait()
		}
		
		Page.showDeletionWarningToast('@localizer["deletionWarningHeading"]', '@localizer["deletionWarning"]', async () => await callback(currentRow.Id), '@localizer["ok"]', '@localizer["abort"]')
	}

	async function activateUser(event) {
		const listLine = event.target.closest('lvl-list-line')
		const enumeration = document.getElementById('user-list')

		const list = enumeration.querySelector('lvl-list')

		const position = Number(listLine.dataset['position'])
		const currentRow = list.rows[position]
		const response = await fetch(`/Api/Users/<USER>/Activate`, { method: 'POST' })
		if (response.ok)
			enumeration.reload() // -> only reload listLine, when implemented
	}

	async function deactivateUser(event) {
		const listLine = event.target.closest('lvl-list-line')
		const enumeration = document.getElementById('user-list')

		const list = enumeration.querySelector('lvl-list')

		const position = Number(listLine.dataset['position'])
		const currentRow = list.rows[position]
		const response = await fetch(`/Api/Users/<USER>/Deactivate`, { method: 'POST' })
		if (response.ok)
			enumeration.reload() // -> only reload listLine, when implemented
	}
</script>