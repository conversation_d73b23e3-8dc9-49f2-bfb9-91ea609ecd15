using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.UserCustomerMapping;
using Levelbuild.Core.ZitadelApiInterface;
using Levelbuild.Core.ZitadelApiInterface.Constants;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.UserCustomerMapping;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.UserCustomerMapping.ViewModels;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.UserCustomerMapping.Controllers;

/// <summary>
/// Controller for the configuration of user grants
/// </summary>
public class UserCustomerMappingController : AdminController<UserCustomerMappingDto>
{
	private readonly IZitadelApiClientFactory _zitadelApiClientFactory;
	
	/// <inheritdoc />
	public UserCustomerMappingController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory stringLocalizerFactory, IVersionReader versionReader, IZitadelApiClientFactory zitadelApiClientFactory) : 
		base(logManager, logManager.GetLoggerForClass<UserCustomerMappingController>(), contextFactory, userManager, stringLocalizerFactory, versionReader)
	{
		_zitadelApiClientFactory = zitadelApiClientFactory;
	}
	
	#region Views
	
	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <param name="userSlug">readable identifier for a specific parent user</param>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Users/<USER>/UserCustomerMappings/Create")]
	public IActionResult Create(string userSlug)
	{
		var user = DatabaseContext.Users.FirstOrDefault(user => user.Slug == userSlug);
		if(user == null)
			return new NotFoundObjectResult($"User with slug: {userSlug} could not be found");
		
		var mappedCustomerIds = DatabaseContext.UserCustomerMappings
			.Where(mapping => mapping.UserId == user.Id)
			.Select(mapping => mapping.CustomerId)
			.ToList();
		var unmappedCustomers = DatabaseContext.Customers
			.Where(customer => !mappedCustomerIds.Contains(customer.Id))
			.ToList();
		var dto = new UserCustomerMappingDto()
		{
			UserId = user.Id.ToString(),
			User = user.ToDto()
		};
		return CachedPartial() ?? RenderPartial(new UserCustomerMappingForm(dto, unmappedCustomers));
	}
	
	#endregion
	
	#region Actions
	
	/// <inheritdoc />
	[HttpGet("/Api/UserCustomerMappings")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		return HandleQueryRequest<UserCustomerMappingEntity, UserCustomerMappingDto>(
			DatabaseContext.UserCustomerMappings.Include(mapping => mapping.Customer), 
			parameters
		);
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/UserCustomerMappings/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<UserCustomerMappingEntity, UserCustomerMappingDto>(
			DatabaseContext.UserCustomerMappings.Include(mapping => mapping.Customer),
			id
		);
	}
	
	/// <inheritdoc />
	[HttpPost("/Api/UserCustomerMappings")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] UserCustomerMappingDto dto)
	{
		try
		{
			var userId = Guid.Parse(dto.UserId!);
			var user = await DatabaseContext.Users.FindAsync(userId);
			if (user == null)
				return GetNotFoundResponse($"User with id: '{dto.UserId}' could not be found.");
			
			if(user.IsMachineUser)
				return GetBadRequestResponse($"Cannot grant machine users access to multiple customers.");
			
			var customerId = Guid.Parse(dto.CustomerId!);
			var customer = await DatabaseContext.Customers.FindAsync(customerId);
			if (customer == null)
				return GetNotFoundResponse($"Customer with id: '{dto.CustomerId}' could not be found.");
			
			var existingEntry = await DatabaseContext.UserCustomerMappings
				.FirstOrDefaultAsync(mapping => mapping.UserId == userId && mapping.CustomerId == customerId);
			if (existingEntry != null)
				return GetBadRequestResponse($"Mapping for user \"{user.DisplayName}\" and customer \"{customer.DisplayName}\" already exists.");
			
			// TODO - Replace hard coded org name
			var adminRole = customer.DisplayName.Equals("Levelbuild", StringComparison.CurrentCultureIgnoreCase) ? AuthRole.Admin : AuthRole.OrgAdmin;
			var roles = dto.IsAdmin == true ? new[] { adminRole, AuthRole.User } : [AuthRole.User];
			
			var managementClient = _zitadelApiClientFactory.GetManagementClient();
			managementClient.AddUserGrant(user.RemoteId, customer.RemoteId, roles);
			
			if(dto.IsAdmin == true)
				managementClient.AddImpersonationRights(user.RemoteId, customer.RemoteId);
			
			return await HandleCreateRequestAsync(DatabaseContext.UserCustomerMappings, dto);
		}
		catch (Exception e)
		{
			Logger.Error(e, "User grant mapping for user: \'{DtoUserId}\' and customer: \'{DtoCustomerId}\' could not be created", dto.UserId, dto.CustomerId);
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <inheritdoc />
	[HttpPatch("/Api/UserCustomerMappings/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] UserCustomerMappingDto dto)
	{
		try
		{
			var mapping = await DatabaseContext.UserCustomerMappings
				.Include(mapping => mapping.Customer)
				.Include(mapping => mapping.User)
				.AsSplitQuery()
				.FirstOrDefaultAsync(mapping => mapping.Id == id);
			
			if (mapping == null)
				return GetNotFoundResponse($"Mapping with id: '{id}' could not be found.");
			
			// TODO - Replace hard coded org name
			var adminRole = mapping.Customer!.DisplayName.Equals("Levelbuild", StringComparison.CurrentCultureIgnoreCase) ? AuthRole.Admin : AuthRole.OrgAdmin;
			var roles = dto.IsAdmin == true ? new[] { adminRole, AuthRole.User } : [AuthRole.User];
			
			var managementClient = _zitadelApiClientFactory.GetManagementClient();
			managementClient.UpdateUserGrant(mapping.User!.RemoteId, mapping.Customer!.RemoteId, roles);
			
			if(dto.IsAdmin == true)
				managementClient.AddImpersonationRights(mapping.User!.RemoteId, mapping.Customer!.RemoteId);
			else
				managementClient.RemoveImpersonationRights(mapping.User!.RemoteId, mapping.Customer!.RemoteId);
			
			return await HandleUpdateRequestAsync(DatabaseContext.UserCustomerMappings, id, dto);
		}
		catch (Exception e)
		{
			Logger.Error(e, "User grant mapping with id: \'{Id}\' could not be updated", id);
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <inheritdoc />
	[HttpDelete("/Api/UserCustomerMappings/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		try
		{
			var mapping = DatabaseContext.UserCustomerMappings
				.Include(mapping => mapping.Customer)
				.Include(mapping => mapping.User)
				.FirstOrDefault(mapping => mapping.Id == id);
			
			if (mapping == null)
				return GetNotFoundResponse($"Mapping with id: '{id}' could not be found.");
			
			if(mapping.User!.IsMachineUser)
				return GetBadRequestResponse($"Cannot delete grants of machine users.");
			
			var managementClient = _zitadelApiClientFactory.GetManagementClient();
			managementClient.RemoveUserGrant(mapping.User!.RemoteId, mapping.Customer!.RemoteId);
			managementClient.RemoveUserFromOrganisation(mapping.User!.RemoteId, mapping.Customer!.RemoteId);
			
			return HandleDeleteRequest(DatabaseContext.UserCustomerMappings, id);
		}
		catch (Exception e)
		{
			Logger.Error(e, "User grant mapping with id: \'{Id}\' could not be deleted", id);
			return GetBadRequestResponse(e.Message);
		}
	}
	
	#endregion
}