@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.UserCustomerMapping.ViewModels.UserCustomerMappingForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
var localizer = LocalizerFactory.Create("UserCustomerMapping", "");
}

<div class="grid--centered @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
	<form-component id="user-customer-mapping-form">
		<div class="form__item">
			<input type="hidden" class="item__value" name="userId" value="@Model.Mapping.User?.Id"/>
		</div>
		<config-section label="@localizer["sectionInfo"]">
			<div class="form__item">
				<input type="hidden" class="item__value" name="id" value="@Model.Mapping.User?.Username"/>
			</div>
			@{
			List<AutocompleteOptionDefinition> customerOptions = Model.Customers.Select(customer => new AutocompleteOptionDefinition(customer.Id, customer.DisplayName)).ToList();
			}
			<div class="form__item">
				<config-label target="customer" label="@localizer["customer"]"></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="customer"
					options="customerOptions"
					name="customerId"
					value="@(Model.Mapping.Customer?.Id)"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="true"
					readonly="@(Model.ViewType == ViewType.Edit)">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="is-admin" label="@localizer["isAdmin"]"></config-label>
				<toggle-component id="is-admin" name="isAdmin" class="item__value" value="@(Model.Mapping.IsAdmin == true)"></toggle-component>
			</div>
		</config-section>
	</form-component>
</div>