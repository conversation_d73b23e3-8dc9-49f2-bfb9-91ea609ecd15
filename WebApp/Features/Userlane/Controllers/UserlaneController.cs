using System.Text.Json;
using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.Userlane;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Module.ViewModels;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels;
using Levelbuild.Frontend.WebApp.Features.UserlaneStep.ViewModels;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.Userlane.Controllers;

/// <inheritdoc />

public class UserlaneController : AdminController<UserlaneDto>
{
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="localizerFactory"></param>
	/// <param name="versionReader">injected VersionReader</param>
	/// <param name="contextFactory"></param>
	/// <param name="userManager"></param>
	[ExcludeFromCodeCoverage]
	public UserlaneController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
							   IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : base(
		logManager, logManager.GetLoggerForClass<UserlaneController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
	}

	#region Views
	
	/// <summary>
	/// Displays the userlanes list
	/// </summary>
	/// <param name="type">Optional filter by userlane type</param>
	/// <param name="menu">Current menu selection</param>
	/// <returns></returns>
	[HttpGet("/Admin/Userlanes/{menu?}")]
	public IActionResult List(string? type = null, string? menu = null)
	{
		var userlanesList = new UserlaneList
		{
			Userlanes = DatabaseContext.Userlanes.AsQueryable().OrderBy(userlane => userlane.Name).ToDtoList().ToList(),
		};

		return RenderPartial(userlanesList);
	}

		/// <summary>
	/// Renders the step and actions editor for a Userlane
	/// </summary>
	/// <param name="userlaneId">The Userlane ID</param>
	/// <returns>The step and actions editor view</returns>
	[HttpGet("/Admin/Userlane/{userlaneId}/StepAndActionsEditor")]
	public IActionResult StepAndActionsEditor(Guid userlaneId)
	{
		var userlane = DatabaseContext.Userlanes.Find(userlaneId);
		if (userlane == null)
		{
			return BadRequest($"Userlane with ID {userlaneId} not found.");
		}
		
		Console.WriteLine(userlaneId);

		List<AutocompleteOptionDefinition> dropDownOptions;
		List<GridViewFieldEntity> dataFields;
		
		if (userlane.PageId != null && userlane.PageId != "custom")
		{
			var page = DatabaseContext.Pages.First(x=> x.Id == Guid.Parse(userlane.PageId));
			
			switch (page.Type)
			{
				case PageType.SingleData:
				case PageType.Create:
				case PageType.MultiData:
					// First, we collect the pages associated with the Multidata page
					var pages = DatabaseContext.MultiDataPages.First(x => x.Id == page.Id);

					// We collect the Views of the pages 
					var pageViews = DatabaseContext.PageViews.Where(pv => pv.PageId == pages.CreatePageId || pv.PageId == pages.DetailPageId || pv.PageId == Guid.Parse(userlane.PageId) ).Select(pv => pv.Id).ToList();

					// We collect the GridView Section using the page ID
					var gridViewSection = DatabaseContext.GridViewSections.Where(grid => pageViews.Contains(grid.GridViewId)).Select(g => g.Id).ToList();

					// Use the grid view Section ID to collect the data
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false)
						.Where(pag => gridViewSection.Contains(pag.SectionId))
						.AsQueryable().ToList();
					
					break;
				default:
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
					break;
			}
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}
		else
		{
			dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}
		
		return RenderPartial(new UserlaneStepForm()
		{
			UserlaneId = userlaneId,
			DataFields = dropDownOptions
		}, "StepAndActionsEditor");
	}

	/// <summary>
	/// Unified userlanes view that handles both Tests and Tours based on menu parameter
	/// </summary>
	/// <param name="dataStoreSlug">Data store identifier</param>
	/// <param name="moduleSlug">Module identifier</param>
	/// <param name="menu">Menu selection (Tests or Tours)</param>
	/// <returns></returns>
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Modules/{moduleSlug}/Userlanes/{menu?}")]
	public IActionResult ModuleUserlanes(string dataStoreSlug, string moduleSlug, string? menu = "Tests")
	{
		// Get the module entity to pass context to the view
		var module = DatabaseContext.Modules
			.Include(m => m.DataStore)
			.FirstOrDefault(m => m.Slug == moduleSlug.ToLower() && m.DataStore.Slug == dataStoreSlug.ToLower());
		
		if (module == null)
			return NotFound($"Module {moduleSlug} not found in DataStore {dataStoreSlug}");

		var viewModel = new ModuleForm(ViewType.Edit)
		{
			Module = module.ToDto()
		};

		// Set the target menu in ViewData for the view to use
		ViewData["targetMenu"] = menu;

		// Determine which view to render based on menu parameter
		var viewPath = menu?.ToLower() switch
		{
			"tours" => "~/Features/Userlane/Views/_Tours.cshtml",
			_ => "~/Features/Userlane/Views/_Tests.cshtml" // Default to Tests
		};

		return RenderPartial(viewModel, viewPath);
	}

	/// <summary>
	/// Create a new userlane
	/// </summary>
	/// <returns></returns>
	[HttpGet("/Admin/Userlane/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Modules/{moduleSlug}/Userlane/Create")]
	public IActionResult Create(string? dataStoreSlug = null, string? moduleSlug = null)
	{
		var pages = DatabaseContext.Pages.Where(p=>p.Type == PageType.MultiData).ToDtoList().ToList();
		var clientContexts = DatabaseContext.DataStoreContexts.Include(c => c.Customer).ToDtoList().ToList();
	
		// Get module ID if dataStoreSlug and moduleSlug are provided
		Guid? moduleId = null;
		if (!string.IsNullOrEmpty(dataStoreSlug) && !string.IsNullOrEmpty(moduleSlug))
		{
			var module = DatabaseContext.Modules
				.FirstOrDefault(m => m.Slug == moduleSlug.ToLower() && 
									m.DataStore.Slug == dataStoreSlug.ToLower());
			if (module != null)
			{
				moduleId = module.Id;
			}
		}
	
		var userlanesForm = new UserlaneForm
		{
			Pages = pages,
			ClientContexts = clientContexts,
			Userlane = moduleId.HasValue ? new UserlaneDto { ModuleId = moduleId.Value } : null
		};
		return CachedPartial() ?? RenderPartial(userlanesForm);
	}


	/// <param name="id"></param>
	/// <param name="menu"></param>
	/// <returns></returns>
	[HttpGet("/Admin/Userlane/{id:guid?}/")]
	[HttpGet("/Admin/Userlane/{id:guid}/{menu?}")]
	[HttpGet("/Admin/Userlane/Edit/{menu?}")]
	[HttpGet("/Admin/Userlane/Edit/{id:guid}/{menu?}")]
	public IActionResult Detail(Guid id, string? menu)
	{
		var pages = DatabaseContext.Pages.Where(p=>p.Type == PageType.MultiData).ToDtoList().ToList();
		var clientContexts = DatabaseContext.DataStoreContexts.Include(c => c.Customer).ToDtoList().ToList();

		if (id == Guid.Empty)
		{
			// Pass the user dictionary to ViewBag or add it to the model
			var model = new UserlaneForm(ViewType.Edit)
			{
				Pages = pages,
				ClientContexts = clientContexts
			};
			return RenderPartial(model);
		}

		var userlaneEntity = DatabaseContext.Userlanes.FirstOrDefault(x => x.Id == id);
		if (userlaneEntity == null)
		{
			return NotFound($"Userlane with ID {id} not found.");
		}

		var form = new UserlaneForm(ViewType.Edit)
		{
			Userlane = userlaneEntity.ToDto(),
			Pages = pages,
			ClientContexts = clientContexts
		};
		return RenderPartial(form);

	}

	#endregion

	#region Actions

	/// <summary>
	/// Query userlanes with optional module filtering
	/// </summary>
	[HttpGet("/Api/Userlane")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		var query = DatabaseContext.Userlanes.AsQueryable();
		
		// If moduleId is provided in query parameters, filter by module
		if (parameters.Filters == null) return HandleQueryRequest<UserlaneEntity, UserlaneDto>(query, parameters);
		var moduleFilter = parameters.Filters.FirstOrDefault(f => f.FilterColumn == "ModuleId");
		if (moduleFilter != null && Guid.TryParse(moduleFilter.CompareValue?.ToString(), out var moduleId))
		{
			query = query.Where(u => u.ModuleId == moduleId);
		}

		return HandleQueryRequest<UserlaneEntity, UserlaneDto>(query, parameters);
	}

	/// <inheritdoc />
	[HttpGet("/Api/Userlane/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		// Retrieve the specific Userlane based on the provided id
		var query = DatabaseContext.Userlanes;
		return HandleGetRequest<UserlaneEntity, UserlaneDto>(query, id);
	}

	/// <inheritdoc />
	[HttpPost("/Api/Userlane")]
	public override Task<ActionResult<FrontendResponse>> Create([FromBody] UserlaneDto userlanesDto)
	{
		var result = HandleCreateRequestAsync(DatabaseContext.Userlanes, userlanesDto);
		return result;
	}
	
	/// <summary>
	/// Get the results of the userlanes test tour
	/// </summary>
	/// <returns></returns>
	[HttpPost("/Api/Userlane/Results")]
	public async Task<ActionResult<FrontendResponse>> Results()
	{
		using var reader = new StreamReader(HttpContext.Request.Body);
		var body = await reader.ReadToEndAsync();
		
		// Deserialize the JSON into ResultsDto
		var dto = JsonSerializer.Deserialize<ResultsDto>(body, new JsonSerializerOptions
		{
			PropertyNameCaseInsensitive = true
		});

		if (dto == null)
		{
			throw new ArgumentNullException(nameof(dto), "Request data could not be deserialized.");
		}

		if (dto.UserlaneId == null) return GetNotFoundResponse("Error saving results");

		var currentUser = UserManager.GetCurrentUserAsync();
		if (currentUser == null)
		{
			throw new UnauthorizedAccessException("Current user is not authenticated.");
		}

		var userlaneResultBatchEntity = new UserlaneResultBatchEntity()
		{
			UserlaneId = Guid.Parse(dto.UserlaneId),
			UserId = currentUser.Result.Id,
			UserName = currentUser.Result.DisplayName,
			Runtime = dto.Runtime,
			StartTime = dto.StartTime,
			EndTime = dto.EndTime,
			Status = dto.Status,
			CreatedDateTime = DateTime.Now
		};

		DatabaseContext.UserlaneResultBatches.Add(userlaneResultBatchEntity);
		await DatabaseContext.SaveChangesAsync();
		var batchId = userlaneResultBatchEntity.Id;
		var userlaneResultEntities = new List<UserlaneResultEntity>();

		foreach (var result in dto.Results)
		{
			if (string.IsNullOrEmpty(result.UserlaneStepActionId) || !Guid.TryParse(result.UserlaneStepActionId, out var stepActionId))
				continue;

			var userlaneStepActionEntity = await DatabaseContext.UserlaneStepActions.FirstAsync(x => x.Id == stepActionId);

			var userlaneStepsEntity = await DatabaseContext.UserlaneSteps.FirstAsync(x => x.Id == userlaneStepActionEntity.UserlaneStepId);

			var userlaneResultEntity = new UserlaneResultEntity
			{
				BatchId = batchId,
				UserlaneStepActionId = userlaneStepActionEntity.Id,
				UserlaneStepId = userlaneStepsEntity.Id,
				UserlaneId = userlaneStepsEntity.UserlaneId,
				Title = result.Title,
				StartTime = result.StartTime ?? DateTime.Now,
				EndTime = result.EndTime ?? DateTime.Now,
				Duration = result.Duration,
				Complete = result.Complete,
				Found = result.Found,
				Result = result.Result
			};
			userlaneResultEntities.Add(userlaneResultEntity);
		}

		DatabaseContext.UserlaneResults.AddRange(userlaneResultEntities);
		await DatabaseContext.SaveChangesAsync();
		var json = JsonSerializer.SerializeToElement(batchId, HeaderSerializer);
		return GetOkResponse(ServerResponsePayload.FromJson(json));

	}

	/// <inheritdoc />
	[HttpPatch("/Api/Userlane/{id:guid}")]
	public override Task<ActionResult<FrontendResponse>> Update(Guid id,[FromBody] UserlaneDto dto)
	{
		return HandleUpdateRequestAsync(DatabaseContext.Userlanes, id, dto);
	}

	/// <inheritdoc />
	[HttpDelete("/Api/Userlane/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		return HandleDeleteRequest(DatabaseContext.Userlanes, id);
	}

	/// <summary>
	/// Retrieves userlane tour data for frontend automation including steps and actions
	/// </summary>
	/// <param name="userlaneId">The unique identifier of the userlane to run</param>
	/// <returns>An ActionResult containing the complete tour data structure</returns>
	[HttpGet("/Api/Userlane/RunTour/{userlaneId:guid}")]
	public ActionResult<FrontendResponse> RunTour(Guid userlaneId)
	{
	    try
	    {
	        // Fetch the userlane based on the provided id
	        var userlane = GetUserlaneById(userlaneId);
	        if (userlane == null)
	        {
	            return GetNotFoundResponse("Userlane not found.");
	        }

			// Fetch the steps associated with the userlane
	        var userlaneSteps = GetUserlaneSteps(userlaneId);

			if (userlaneSteps.Count == 0)
	        {
	            return GetNotFoundResponse("No steps found for this userlane.");
	        }

	        // Fetch all actions for the steps in this userlane
	        var stepIds = userlaneSteps.Select(s => s.Id).ToList();
	        var userlaneStepActions = GetUserlaneStepActionsByStepIds(stepIds);

	        // Prepare the final structure
	        var tourData = new
	        {
	            id = userlane.Id.ToString(),
	            name = userlane.Name,
	            url = userlane.StartPoint,
	            steps = userlaneSteps.Select(step => new
	            {
	                id = step.Id.ToString(),
	                title = step.Title,
	                description = step.Description,
	                element = step.TargetElement,
	                speed = step.Delay,
	                onHighlightStarted = userlaneStepActions
	                    .Where(action => action.UserlaneStepId == step.Id)
	                    .OrderBy(action => action.Order)
	                    .Select(action => new
	                    {
	                        id = action.Id.ToString(),
	                        actionType = action.ActionType,
	                        target = action.Target,
	                        targetValue = action.TargetValue,
	                        speed = action.Delay
	                    })
	                    .ToList()
	            }).ToList()
	        };

	        var json = JsonSerializer.SerializeToElement(tourData, HeaderSerializer);
	        return GetOkResponse(ServerResponsePayload.FromJson(json));
	    }
	    catch (Exception e)
	    {
	        const string message = "An error occurred while retrieving userlane tour data.";
	        Logger.Error(e, "{Message}", message);
	        return GetBadRequestResponse(message);
	    }
	}

	/// <summary>
	/// Retrieves data for a specific page based on its unique identifier.
	/// </summary>
	/// <param name="pageId">The unique identifier of the page to retrieve.</param>
	/// <returns>An ActionResult containing the serialized page data in a FrontendResponse.</returns>
	[HttpGet("/Api/Userlane/GetPage/{pageId}")]
	public ActionResult<FrontendResponse> PageId(Guid pageId)
	{
        var page = DatabaseContext.Pages.FirstOrDefault(x=>x.Id == pageId);
		var json = JsonSerializer.SerializeToElement(page, HeaderSerializer);
		return GetOkResponse(ServerResponsePayload.FromJson(json));
	}

	// Helper method to get the userlane by id
	private UserlaneDto? GetUserlaneById(Guid userlaneId)
	{
		var userlane = DatabaseContext.Userlanes.FirstOrDefault(ul => ul.Id == userlaneId);
		return userlane?.ToDto();
	}

	// Helper method to get the userlane steps for a specific userlane
	private List<UserlaneStepDto> GetUserlaneSteps(Guid userlaneId)
	{
		return (List<UserlaneStepDto>)DatabaseContext.UserlaneSteps
			.Where(uls => uls.UserlaneId == userlaneId)
			.OrderBy(uls=> uls.Order)
			.ToDtoList();
	}

	// Helper method to get actions filtered by step IDs
	private List<UserlaneStepActionDto> GetUserlaneStepActionsByStepIds(List<Guid?> stepIds)
	{
		return (List<UserlaneStepActionDto>)DatabaseContext.UserlaneStepActions
			.Where(action => stepIds.Contains(action.UserlaneStepId))
			.ToDtoList();
	}

	/// <summary>
	/// DTO for receiving step results from frontend
	/// </summary>
	private class StepResultDto
	{
		/// <summary>
		///
		/// </summary>
		public required string Title { get; init; }
		/// <summary>
		///
		/// </summary>
		public string? UserlaneStepActionId { get; init; } = null;
		/// <summary>
		///
		/// </summary>
		public DateTime? StartTime { get; init; } = null;
		/// <summary>
		///
		/// </summary>
		public DateTime? EndTime { get; init; } = null;
		/// <summary>
		///
		/// </summary>
		public required int Duration { get; set; }
		/// <summary>
		///
		/// </summary>
		public required bool Complete { get; set; }
		/// <summary>
		///
		/// </summary>
		public required bool Found { get; set; }
		/// <summary>
		///
		/// </summary>
		public required string Result { get; set; }
	}

	/// <summary>
	/// DTO for receiving complete results data from frontend
	/// </summary>
	private class ResultsDto
	{
		public required string? UserlaneId { get; init; }
		public required DateTime StartTime { get; init; }
		public required DateTime EndTime { get; init; }
		public required int Runtime { get; init; }
		public required string Status { get; init; }
		public required List<StepResultDto> Results { get; init; }
	}

	#endregion
}