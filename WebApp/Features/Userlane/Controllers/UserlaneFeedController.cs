using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Userlane;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.Userlane.Controllers;

/// <summary>
/// Controller for managing Userlane Feed operations
/// </summary>
[ExcludeFromCodeCoverage]
public class UserlaneFeedController : AdminController<UserlaneFeedDto>
{
    /// <summary>
    /// Constructor.
    /// </summary>
    /// <param name="logManager"></param>
    /// <param name="localizerFactory"></param>
    /// <param name="versionReader">injected VersionReader</param>
    /// <param name="contextFactory"></param>
    /// <param name="userManager"></param>
    [ExcludeFromCodeCoverage]
    public UserlaneFeedController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, 
                                  UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, 
                                  IVersionReader versionReader) : base(
        logManager, logManager.GetLoggerForClass<UserlaneFeedController>(), contextFactory, userManager, 
        localizerFactory, versionReader)
    {
    }

    #region Views

    /// <summary>
    /// Displays the feed view for a specific Userlane
    /// </summary>
    /// <param name="userlaneId">The Userlane ID to show feed for</param>
    /// <param name="dateFilter">Optional date filter (today, yesterday, week, month)</param>
    /// <param name="activityType">Optional activity type filter (created, updated, deleted, run)</param>
    /// <returns></returns>
    [HttpGet("/Admin/Userlane/{userlaneId:guid}/Feed")]
    public IActionResult Feed(Guid userlaneId, string? dateFilter = null, string? activityType = null)
    {
        // Get the userlane to pass context to the view
        var userlane = DatabaseContext.Userlanes.FirstOrDefault(u => u.Id == userlaneId);
        if (userlane == null)
        {
            return NotFound($"Userlane with ID {userlaneId} not found");
        }

        // Build query for feed items
        var feedQuery = DatabaseContext.UserlaneFeedItems.AsQueryable()
            .Where(f => f.UserlaneId == userlaneId)
            .Include(f => f.User)
            .Include(f => f.Userlane);

        // Apply date filter
        if (!string.IsNullOrEmpty(dateFilter))
        {
            var now = DateTime.Now;
            switch (dateFilter.ToLower())
            {
                case "today":
                    feedQuery = feedQuery.Where(f => f.CreatedDateTime.Date == now.Date);
                    break;
                case "yesterday":
                    var yesterday = now.AddDays(-1).Date;
                    feedQuery = feedQuery.Where(f => f.CreatedDateTime.Date == yesterday);
                    break;
                case "week":
                    var weekAgo = now.AddDays(-7);
                    feedQuery = feedQuery.Where(f => f.CreatedDateTime >= weekAgo);
                    break;
                case "month":
                    var monthAgo = now.AddMonths(-1);
                    feedQuery = feedQuery.Where(f => f.CreatedDateTime >= monthAgo);
                    break;
            }
        }

        // Apply activity type filter
        if (!string.IsNullOrEmpty(activityType))
        {
            feedQuery = feedQuery.Where(f => f.ActivityType.ToLower() == activityType.ToLower());
        }

        // Get feed items ordered by date (newest first)
        var feedItems = feedQuery
            .OrderByDescending(f => f.CreatedDateTime)
            .Take(100) // Limit to prevent performance issues
            .ToDtoList()
            .ToList();

        var feedViewModel = new UserlaneFeedList
        {
            UserlaneId = userlaneId,
            UserlaneName = userlane.Name ?? "Unknown",
            FeedItems = feedItems,
            DateFilter = dateFilter,
            ActivityTypeFilter = activityType
        };

        return RenderPartial(feedViewModel, "~/Features/Userlane/Views/_Feed.cshtml", 
            new UserlaneForm { Userlane = userlane.ToDto() });
    }

    /// <summary>
    /// Get feed item details for the sidebar
    /// </summary>
    /// <param name="feedItemId">The feed item ID</param>
    /// <returns></returns>
    [HttpGet("/Admin/Userlane/Feed/{feedItemId:guid}/Details")]
    public IActionResult FeedItemDetails(Guid feedItemId)
    {
        var feedItem = DatabaseContext.UserlaneFeedItems
            .Include(f => f.User)
            .Include(f => f.Userlane)
            .FirstOrDefault(f => f.Id == feedItemId);

        if (feedItem == null)
        {
            return NotFound($"Feed item with ID {feedItemId} not found");
        }

        var feedItemDto = feedItem.ToDto();
        return Json(feedItemDto);
    }

    #endregion

    #region Actions

    /// <summary>
    /// Query feed items with filtering support
    /// </summary>
    [HttpGet("/Api/UserlaneFeed")]
    public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
    {
        var query = DatabaseContext.UserlaneFeedItems
            .Include(f => f.User)
            .Include(f => f.Userlane)
            .AsQueryable();

        // Apply Userlane filter if provided
        if (parameters.Filters != null)
        {
            var userlaneFilter = parameters.Filters.FirstOrDefault(f => f.FilterColumn == "UserlaneId");
            if (userlaneFilter != null && Guid.TryParse(userlaneFilter.CompareValue?.ToString(), out var userlaneId))
            {
                query = query.Where(f => f.UserlaneId == userlaneId);
            }

            var activityTypeFilter = parameters.Filters.FirstOrDefault(f => f.FilterColumn == "ActivityType");
            if (activityTypeFilter != null && !string.IsNullOrEmpty(activityTypeFilter.CompareValue?.ToString()))
            {
                query = query.Where(f => f.ActivityType == activityTypeFilter.CompareValue.ToString());
            }
        }

        return HandleQueryRequest<UserlaneFeedEntity, UserlaneFeedDto>(query, parameters);
    }

    /// <inheritdoc />
    [HttpGet("/Api/UserlaneFeed/{id:guid}")]
    public override ActionResult<FrontendResponse> Get(Guid id)
    {
        var query = DatabaseContext.UserlaneFeedItems
            .Include(f => f.User)
            .Include(f => f.Userlane);
        return HandleGetRequest<UserlaneFeedEntity, UserlaneFeedDto>(query, id);
    }

    /// <inheritdoc />
    [HttpPost("/Api/UserlaneFeed")]
    public override Task<ActionResult<FrontendResponse>> Create([FromBody] UserlaneFeedDto feedDto)
    {
        return HandleCreateRequestAsync(DatabaseContext.UserlaneFeedItems, feedDto);
    }

    /// <inheritdoc />
    [HttpPatch("/Api/UserlaneFeed/{id:guid}")]
    public override Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] UserlaneFeedDto dto)
    {
        return HandleUpdateRequestAsync(DatabaseContext.UserlaneFeedItems, id, dto);
    }

    /// <inheritdoc />
    [HttpDelete("/Api/UserlaneFeed/{id:guid}")]
    public override ActionResult<FrontendResponse> Delete(Guid id)
    {
        return HandleDeleteRequest(DatabaseContext.UserlaneFeedItems, id);
    }

    /// <summary>
    /// Create a feed entry for Userlane activity
    /// </summary>
    /// <param name="userlaneId">The Userlane ID</param>
    /// <param name="activityType">Type of activity (created, updated, deleted, run)</param>
    /// <param name="status">Status of the activity (successful, failed, etc.)</param>
    /// <param name="description">Description of the activity</param>
    /// <param name="metadata">Additional metadata as JSON</param>
    /// <returns></returns>
    [HttpPost("/Api/UserlaneFeed/CreateActivity")]
    public async Task<ActionResult<FrontendResponse>> CreateActivity(
        [FromBody] CreateActivityRequest request)
    {
        var currentUser = await UserManager.GetCurrentUserAsync();
        if (currentUser == null)
        {
            return Unauthorized("User not authenticated");
        }

        var feedDto = new UserlaneFeedDto
        {
            UserlaneId = request.UserlaneId,
            UserId = currentUser.Id,
            ActivityType = request.ActivityType,
            Status = request.Status,
            Description = request.Description,
            Metadata = request.Metadata
        };

        return await HandleCreateRequestAsync(DatabaseContext.UserlaneFeedItems, feedDto);
    }

    #endregion

    #region Helper Classes

    /// <summary>
    /// Request model for creating activity feed entries
    /// </summary>
    public class CreateActivityRequest
    {
        public Guid UserlaneId { get; set; }
        public string ActivityType { get; set; } = string.Empty;
        public string? Status { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? Metadata { get; set; }
    }

    #endregion
}
