using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Userlane;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.Userlane.Controllers;

/// <inheritdoc />
public class UserlaneResultController : AdminController<UserlaneResultDto>
{
	/// <inheritdoc />
	public UserlaneResultController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
									IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : base(
		logManager, logManager.GetLoggerForClass<UserlaneResultController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="id"></param>
	/// <returns></returns>
	/// <exception cref="NotImplementedException"></exception>
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		throw new NotImplementedException();
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dto"></param>
	/// <returns></returns>
	/// <exception cref="NotImplementedException"></exception>
	public override Task<ActionResult<FrontendResponse>> Create(UserlaneResultDto dto)
	{
		throw new NotImplementedException();
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="id"></param>
	/// <param name="dto"></param>
	/// <returns></returns>
	/// <exception cref="NotImplementedException"></exception>
	public override Task<ActionResult<FrontendResponse>> Update(Guid id, UserlaneResultDto dto)
	{
		throw new NotImplementedException();
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="id"></param>
	/// <returns></returns>
	/// <exception cref="NotImplementedException"></exception>
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		throw new NotImplementedException();
	}
	
	/// <inheritdoc />
	[HttpGet("/Api/UserlaneResult")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
			var query = DatabaseContext.UserlaneResults;
			return HandleQueryRequest<UserlaneResultEntity, UserlaneResultDto>(query, parameters);
	}
}