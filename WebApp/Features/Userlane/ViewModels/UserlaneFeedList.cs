using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Userlane;

#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels;

/// <summary>
/// ViewModel for the Userlane Feed list view
/// </summary>
[ExcludeFromCodeCoverage]
public class UserlaneFeedList
{
    /// <summary>
    /// The ID of the Userlane this feed belongs to
    /// </summary>
    public Guid UserlaneId { get; init; }

    /// <summary>
    /// The name of the Userlane this feed belongs to
    /// </summary>
    public string UserlaneName { get; init; } = string.Empty;

    /// <summary>
    /// List of feed items to display
    /// </summary>
    public List<UserlaneFeedDto> FeedItems { get; init; } = [];

    /// <summary>
    /// Current date filter applied (today, yesterday, week, month)
    /// </summary>
    public string? DateFilter { get; init; }

    /// <summary>
    /// Current activity type filter applied (created, updated, deleted, run)
    /// </summary>
    public string? ActivityTypeFilter { get; init; }

    /// <summary>
    /// Available activity types for filtering
    /// </summary>
    public List<string> AvailableActivityTypes { get; } = new()
    {
        "created",
        "updated", 
        "deleted",
        "run"
    };

    /// <summary>
    /// Available date filters
    /// </summary>
    public List<string> AvailableDateFilters { get; } = new()
    {
        "today",
        "yesterday",
        "week",
        "month"
    };

    /// <summary>
    /// Group feed items by date for display
    /// </summary>
    public Dictionary<string, List<UserlaneFeedDto>> GroupedFeedItems
    {
        get
        {
            var grouped = new Dictionary<string, List<UserlaneFeedDto>>();
            var now = DateTime.Now;

            foreach (var item in FeedItems.OrderByDescending(f => f.ActivityTimestamp))
            {
                var itemDate = item.ActivityTimestamp;
                string dateKey;

                if (itemDate.Date == now.Date)
                {
                    dateKey = "Today";
                }
                else if (itemDate.Date == now.AddDays(-1).Date)
                {
                    dateKey = "Yesterday";
                }
                else
                {
                    dateKey = itemDate.ToString("dd.MM.yyyy");
                }

                if (!grouped.ContainsKey(dateKey))
                {
                    grouped[dateKey] = new List<UserlaneFeedDto>();
                }

                grouped[dateKey].Add(item);
            }

            return grouped;
        }
    }

    /// <summary>
    /// Get the display text for an activity status
    /// </summary>
    /// <param name="status">The status string</param>
    /// <returns>Display text for the status</returns>
    public static string GetStatusDisplayText(string? status)
    {
        return status?.ToLower() switch
        {
            "successful" => "Successful",
            "failed" => "Failed",
            "deleted" => "Deleted",
            "pending" => "Pending",
            "running" => "Running",
            _ => string.Empty
        };
    }

    /// <summary>
    /// Get the CSS class for an activity status
    /// </summary>
    /// <param name="status">The status string</param>
    /// <returns>CSS class for the status</returns>
    public static string GetStatusCssClass(string? status)
    {
        return status?.ToLower() switch
        {
            "successful" => "successful",
            "failed" => "failed",
            "deleted" => "deleted",
            "pending" => "pending",
            "running" => "running",
            _ => string.Empty
        };
    }

    /// <summary>
    /// Get the avatar color class based on user or activity type
    /// </summary>
    /// <param name="activityType">The activity type</param>
    /// <param name="userId">The user ID</param>
    /// <returns>CSS class for avatar color</returns>
    public static string GetAvatarColorClass(string activityType, Guid? userId = null)
    {
        return activityType.ToLower() switch
        {
            "created" => "blue",
            "updated" => "blue", 
            "deleted" => "red",
            "run" => "green",
            _ => (userId.HasValue ? userId.Value.GetHashCode() : 0) % 3 switch
            {
                0 => "blue",
                1 => "green",
                _ => "red"
            }
        };
    }

    /// <summary>
    /// Get user initials for avatar display
    /// </summary>
    /// <param name="userName">The user's display name</param>
    /// <returns>User initials (up to 2 characters)</returns>
    public static string GetUserInitials(string? userName)
    {
        if (string.IsNullOrWhiteSpace(userName))
            return "??";

        var parts = userName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        else if (parts.Length == 1 && parts[0].Length >= 2)
        {
            return parts[0].Substring(0, 2).ToUpper();
        }
        else if (parts.Length == 1)
        {
            return parts[0][0].ToString().ToUpper();
        }

        return "??";
    }

    /// <summary>
    /// Format the activity description with Userlane name
    /// </summary>
    /// <param name="description">The base description</param>
    /// <param name="userlaneName">The Userlane name to substitute</param>
    /// <returns>Formatted description</returns>
    public static string FormatDescription(string description, string userlaneName)
    {
        return description.Replace("@Model.Userlane?.Name", $"\"{userlaneName}\"");
    }
}
