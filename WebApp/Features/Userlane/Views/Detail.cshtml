@model Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels.UserlaneForm
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("Userlane", "");
	
	IList<MenuItem> menuItems = new List<MenuItem>()
	{
		new("basic", "DetailUserlane", "screwdriver-wrench"),
		new("stepsAndActionEditor", "StepAndActionsEditor", "stairs"),
		new("feed", "Feed", "rss")
	};

	List<MenuInfo> menuInfos =
	[
		
	];
	
	var currentMenu = ViewData["targetMenu"] == null ? "DetailUserlane" : ViewData["targetMenu"]!.ToString()!;
}

<script type="module" defer>
	@if (Model.Userlane != null)
	{
		<text>
			Page.setMainPage(`/Admin/Userlane/@(Model.Userlane.Id)`,'@(currentMenu)')
			Page.setBreadcrumbs([
				{ label: '@(localizer["Userlanes"])', url: '/Admin/Userlanes' },
				{ label: '@(Model.Userlane.Name)', url: `/Admin/Userlane/@(Model.Userlane.Id)` }
			],true)

		</text>
	}
</script>

<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="Userlane" route-name="Userlane" menu-items="@menuItems" menu-infos="@menuInfos" skeleton="@(Model.Userlane == null)" width="250"></vc:basic-menu>
<vc:admin-detail-page entity="Userlane" route-name="Userlane" model="@Model" title="@Model.Userlane?.Name" menu-item="@currentMenu" show-default-buttons="@(Model.Userlane != null)"></vc:admin-detail-page>