@model Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels.UserlaneList
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("Userlanes", "");
	IList<MenuItem> menuItems = new List<MenuItem>()
	{
		new("tests", "Tests", "vial"),
		new("guidedAndIntroduction", "GuidedAndIntroduction", "book-open"),
	};
	var currentMenu = ViewData["targetMenu"] == null ? "Tests" : ViewData["targetMenu"]!.ToString()!;
}


<script type="module" defer>
	Page.setMainPage('/Admin/Userlanes/@currentMenu')
	Page.setBreadcrumbs([{ label: '@localizer["Userlanes"]', url: Page.getMainPageUrl() }])
</script>

<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="Userlanes" route-name="Userlanes" menu-items="@menuItems" view-type="@ViewType.List"></vc:basic-menu>

<div class="page-content">
	@* @await Html.PartialAsync("_Tests.cshtml", Model) *@
</div>
