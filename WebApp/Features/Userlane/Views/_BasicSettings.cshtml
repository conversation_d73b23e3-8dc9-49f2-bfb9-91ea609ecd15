@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels.UserlaneForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("Userlane", "");
	var typeLocalizer = LocalizerFactory.Create("UserlaneType", "");
	var roleLocalizer = LocalizerFactory.Create("AuthRole", "");
	var typeOptions = Model.UserlaneType.Select(type => new AutocompleteOptionDefinition(type.ToString(), typeLocalizer[type.ToString()])).ToList();
	var pageOptions = Model.Pages.Select(page => new AutocompleteOptionDefinition(page.Id!, typeLocalizer[page.Name!])).ToList();
	var testerRoleOptions = Model.TesterRoles.Select(role => new AutocompleteOptionDefinition(role, roleLocalizer[role])).ToList();
	var clientContextOptions = Model.ClientContexts.Select(context => new AutocompleteOptionDefinition(context.Id!, context.CustomerName)).ToList();
}

<script type="module" defer>
	// enable save button
	Page.buttonConfig.saveButton.disabled = false
	// enable delete button on this panel
	Page.buttonConfig.deleteButton.disabled = false
</script>

<div class="grid--centered">
	<form-component id="userlane-form" skeleton="@(Model is { ViewType: ViewType.Edit, Userlane: null })">
		<div class="form__item">
			<input type="hidden" class="item__value" name="id" value="@Model.Userlane?.Id"/>
			<input type="hidden" class="item__value" name="moduleId" value="@Model.Userlane?.ModuleId"/>
		</div>
		<config-section label="Information">
			
			<div class="form__item">
				<config-label target="name" label="@localizer["name"]"></config-label>
				<input-component name="name" required="true" id="name" placeholder="@localizer["userlanesName"]" value="@Model.Userlane?.Name"></input-component>
			</div>

			<div class="form__item">
				<config-label target="page" label="@localizer["startPage"]" description=""></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="pageId"
					options="pageOptions"
					name="pageId"
					value="@Model.Userlane?.PageId"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="true">
				</autocomplete-component>
			</div>
			
			<div hidden class="form__item" id="startPointField">
				<config-label target="startPoint" label="@localizer["startPoint"]"></config-label>
				<input-component
					class="item__value"
					required="true"
					name="startPoint"
					id="startPoint"
					placeholder="@localizer["startPoint"]"
					value="@Model.Userlane?.StartPoint">
				</input-component>
			</div>
		
			<div class="form__item">
				<config-label target="testerRole" label="@localizer["testerRole"]"></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="testerRole"
					options="testerRoleOptions"
					name="testerRole"
					value="@Model.Userlane?.TesterRole"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="true">
				</autocomplete-component>
			</div>

			<div class="form__item">
				<config-label target="clientContext" label="@localizer["clientContext"]"></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="clientContext"
					options="clientContextOptions"
					name="clientContext"
					value="@Model.Userlane?.ClientContext"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]">
				</autocomplete-component>
			</div>

			<div class="form__item">
				<config-label target="speed" label="@localizer["speed"]"></config-label>
				<input-component class="item__value" type="InputDataType.Integer" min="0" name="speed" id="speed" placeholder="@localizer["Speed"]" value="@Model.Userlane?.Speed"></input-component>
			</div>

			<div class="form__item">
				<autocomplete-component
					type="InputDataType.String"
					id="type"
					options="typeOptions"
					name="type"
					value="@(Model.Userlane?.Type.ToString() ?? "Test")"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="true">
				</autocomplete-component>
			</div>
		</config-section>
	</form-component>
</div>
<script>
	
	function handlePageCheck() {
		const pageElement = document.getElementById('pageId');
		const startPoint = document.getElementById('startPoint');
		const startPointField = document.getElementById('startPointField');
		const typeField = document.getElementById('type');

		// Keep fields hidden
		startPointField.classList.add('hide');
		typeField.classList.add('hide');

		if (pageElement.value && pageElement.value !== "") {
			let pageId = pageElement.value;
			startPoint.setAttribute('readonly', "true");
			fetch("/Api/Userlane/GetPage/"+pageId).then(response => {
				if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
				return response.json();
			}).then(data => {
				if(data.data){
					startPoint.value = "/Public/Pages/" + data.data.slug;
				}else{
					console.warn("Data not found");
				}
			}).catch(error => {
				console.error("Error fetching page data:", error);
			});
		} else {
			startPoint.value = "";
			startPoint.removeAttribute('readonly');
		}
	}
	
	document.getElementById('pageId').addEventListener('change', () => {
		handlePageCheck();
	});
	
	handlePageCheck();

</script>

