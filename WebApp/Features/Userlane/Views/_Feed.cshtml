@using Levelbuild.Core.EntityInterface
@model Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels.UserlaneForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@{
    var localizer = LocalizerFactory.Create("Userlane", "");
}

<div class="activity-feed">
    <div class="feed-main" id="feedContainer">
        <!-- Feed content will be loaded here via JavaScript -->
        <div class="feed-loading">
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading activity feed...</p>
            </div>
        </div>
    </div>

    <div class="feed-sidebar">
        <div class="sidebar-content">
            <div class="sidebar-icon">
                <i class="fas fa-search fa-3x"></i>
            </div>
            <h4>Further information</h4>
            <p>Please select an entry on the left for which you would like to see further information.</p>
        </div>
    </div>
</div>

<!-- Report Modal -->
<div id="reportModal" class="report-modal" style="display: none;">
    <div class="modal-backdrop" onclick="closeReportModal()"></div>
    <div class="modal-container">
        <div class="modal-header">
            <div class="modal-title">
                <i class="fas fa-chart-bar"></i>
                <span>Userlane Test Results</span>
            </div>
            <button class="modal-close" onclick="closeReportModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-content">
            <div class="results-section">
                <h3>Results</h3>
                
                <div class="results-table">
                    <div class="table-header">
                        <div class="col-actions">Actions</div>
                        <div class="col-test">Test Condition</div>
                        <div class="col-prev">Previous Duration</div>
                        <div class="col-present">Present Duration</div>
                    </div>
                    
                    <div class="table-body">
                        <!-- Action 1 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>1. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">40 secs</div>
                            <div class="col-present">30 secs</div>
                        </div>
                        
                        <!-- Test Condition 1 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to Field
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Test Condition 2 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to ma...
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Test Condition 3 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to Field
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Action 2 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>2. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Failed Test Condition -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to ma...
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Action 3 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>3. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">15 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Action 4 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>4. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Failed Test Condition for Action 4 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to Field
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Action 5 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>5. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button class="btn-cancel" onclick="closeReportModal()">Cancel</button>
            <button class="btn-download">Download Results</button>
            <button class="btn-try-again">Try again</button>
        </div>
    </div>
</div>

<style>
.activity-feed {
    display: flex;
    min-height: 100vh;
    width: 100%;
    background: #f8f9fa;
    margin: 0;
    padding: 0;
}

.feed-main {
    flex: 1;
    width: 50%;
    padding: 1.5rem;
    overflow-y: auto;
    background: white;
    border-right: 1px solid #e9ecef;
}

.feed-date-group {
    margin-bottom: 2rem;
}

.feed-date-label {
    width: 120px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    flex-shrink: 0;
    margin-top: 0.5rem;
}

.feed-date-label.empty {
    visibility: hidden;
}

.timeline-content {
    display: flex;
    gap: 1rem;
    flex: 1;
}

.feed-date {
    font-size: 1rem;
}

.feed-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 0;
    position: relative;
}

.timeline-connector {
    position: relative;
    width: 60px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-shrink: 0;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: -1.5rem;
    width: 2px;
    background: #e9ecef;
    transform: translateX(-50%);
}

.feed-item:last-child .timeline-line {
    display: none;
}

.feed-avatar {
    position: relative;
    z-index: 2;
    margin-top: 0.5rem;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    color: white;
}

.avatar-circle.blue {
    background-color: #0066cc;
}

.avatar-circle.red {
    background-color: #dc3545;
}

.avatar-circle.green {
    background-color: #28a745;
}

.feed-content {
    flex: 1;
    padding-top: 0.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f3f4;
}

.feed-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.feed-user {
    font-weight: 600;
    color: #495057;
}

.feed-time {
    color: #6c757d;
}

.feed-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.feed-status.successful {
    background-color: #d4edda;
    color: #155724;
}

.feed-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

.feed-status.deleted {
    background-color: #f8d7da;
    color: #721c24;
}

.feed-action-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: auto;
}

.feed-action-btn:hover {
    color: #495057;
}

.feed-description {
    color: #495057;
    margin-bottom: 0.75rem;
}

.feed-actions {
    margin-top: 0.75rem;
}

.feed-separator {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.feed-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
    z-index: 1;
}

.feed-separator span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

.feed-sidebar {
    flex: 1;
    width: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-content {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.sidebar-icon {
    opacity: 0.3;
    margin-bottom: 1rem;
}

.sidebar-content h4 {
    color: #495057;
    margin-bottom: 1rem;
}

.sidebar-content p {
    font-size: 0.875rem;
    line-height: 1.5;
}

.btn-outline-primary {
    border: 1px solid #0066cc;
    color: #0066cc;
    background: transparent;
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    text-decoration: none;
    display: inline-block;
}

.btn-outline-primary:hover {
    background: #0066cc;
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.8125rem;
}

/* Report Modal Styles */
.report-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-container {
    position: relative;
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.modal-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
}

.modal-close:hover {
    color: #495057;
}

.modal-content {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.results-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
    display: inline-block;
}

.results-table {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr;
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
}

.table-header > div {
    padding: 0.75rem 1rem;
    border-right: 1px solid #e9ecef;
}

.table-header > div:last-child {
    border-right: none;
}

.table-body {
    background: white;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
}

.table-row:last-child {
    border-bottom: none;
}

.table-row.test-condition {
    background: #f8f9fa;
}

.table-row > div {
    padding: 0.75rem 1rem;
    border-right: 1px solid #e9ecef;
}

.table-row > div:last-child {
    border-right: none;
}

.col-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-indicator {
    width: 4px;
    height: 20px;
    border-radius: 2px;
}

.action-indicator.success {
    background: #28a745;
}

.action-indicator.failed {
    background: #dc3545;
}

.col-test {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
}

.col-test i {
    color: #28a745;
    font-size: 0.875rem;
}

.col-prev, .col-present {
    font-size: 0.875rem;
    color: #495057;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.btn-cancel {
    background: none;
    border: 1px solid #6c757d;
    color: #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-cancel:hover {
    background: #6c757d;
    color: white;
}

.btn-download {
    background: none;
    border: 1px solid #007bff;
    color: #007bff;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-download:hover {
    background: #007bff;
    color: white;
}

.btn-try-again {
    background: #007bff;
    border: 1px solid #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-try-again:hover {
    background: #0056b3;
}

/* Empty state styles */
.feed-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.empty-state {
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    opacity: 0.3;
    margin-bottom: 1rem;
}

.empty-state h4 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Feed details styles */
.feed-details {
    padding: 1rem;
}

.feed-details h4 {
    color: #495057;
    margin-bottom: 1rem;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
    display: inline-block;
}

.detail-item {
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
}

.detail-item strong {
    color: #495057;
    display: inline-block;
    min-width: 80px;
}

.detail-item pre {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    white-space: pre-wrap;
    word-break: break-word;
}

/* Loading state styles */
.feed-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #6c757d;
}

.feed-loading i {
    margin-bottom: 1rem;
}

.feed-loading p {
    font-size: 0.875rem;
}
</style>

<script>
function openReportModal() {
    document.getElementById('reportModal').style.display = 'flex';
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function closeReportModal() {
    document.getElementById('reportModal').style.display = 'none';
    document.body.style.overflow = 'auto'; // Restore scrolling
}

// Close modal when pressing Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeReportModal();
    }
});

// Load feed data when the feed tab is shown
function loadFeedData() {
    const userlaneId = '@Model.Userlane?.Id';
    if (!userlaneId || userlaneId === '') {
        showFeedError('No Userlane ID available');
        return;
    }

    fetch(`/Api/UserlaneFeed?filters[0].FilterColumn=UserlaneId&filters[0].CompareValue=${userlaneId}&pageSize=100&sortColumn=ActivityTimestamp&sortDirection=desc`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Handle both empty array and null/undefined data
                const feedItems = data.data || [];
                renderFeedData(feedItems);
            } else {
                showFeedError('Failed to load feed data');
            }
        })
        .catch(error => {
            console.error('Error loading feed data:', error);
            showFeedError('Error loading feed data');
        });
}

// Render feed data in the UI
function renderFeedData(feedItems) {
    const container = document.getElementById('feedContainer');

    if (!feedItems || feedItems.length === 0) {
        container.innerHTML = `
            <div class="feed-empty">
                <div class="empty-state">
                    <i class="fas fa-rss fa-3x"></i>
                    <h4>No Feed Data</h4>
                    <p>There are no activities recorded for this Userlane yet.</p>
                </div>
            </div>
        `;
        return;
    }

    // Group items by date
    const groupedItems = groupFeedItemsByDate(feedItems);
    let html = '';

    for (const [dateKey, items] of Object.entries(groupedItems)) {
        html += `<div class="feed-date-group">`;

        items.forEach((item, index) => {
            const isFirstInGroup = index === 0;
            const userInitials = getUserInitials(item.userName);
            const avatarColor = getAvatarColor(item.activityType, item.userId);

            html += `
                <div class="feed-item">
                    <div class="feed-date-label ${isFirstInGroup ? '' : 'empty'}">
                        ${isFirstInGroup ? `<span class="feed-date">${dateKey}</span><i class="fas fa-chevron-down"></i>` : ''}
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-connector">
                            <div class="timeline-line"></div>
                            <div class="feed-avatar">
                                <div class="avatar-circle ${avatarColor}">
                                    ${userInitials === '??' ? '<i class="fas fa-user"></i>' : userInitials}
                                </div>
                            </div>
                        </div>
                        <div class="feed-content">
                            <div class="feed-meta">
                                <span class="feed-user">${item.userName || 'Unknown User'}</span>
                                <span class="feed-time">${formatTimestamp(item.activityTimestamp)}</span>
                                ${item.status ? `<span class="feed-status ${getStatusClass(item.status)}">${getStatusDisplayText(item.status)}</span>` : ''}
                                <button class="feed-action-btn" onclick="showFeedDetails('${item.id}')">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="feed-description">
                                ${formatDescription(item.description, '@Model.Userlane?.Name')}
                            </div>
                            ${item.activityType.toLowerCase() === 'run' && item.metadata ? `
                                <div class="feed-actions">
                                    <button class="btn btn-outline-primary btn-sm" onclick="openReportModal('${item.id}')">Open report</button>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        html += `</div>`;
    }

    container.innerHTML = html;
}

// Helper functions
function groupFeedItemsByDate(items) {
    const grouped = {};
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

    items.forEach(item => {
        if (!item.activityTimestamp) return;

        const itemDate = new Date(item.activityTimestamp);
        if (isNaN(itemDate.getTime())) return; // Skip invalid dates

        const itemDateOnly = new Date(itemDate.getFullYear(), itemDate.getMonth(), itemDate.getDate());
        let dateKey;

        if (itemDateOnly.getTime() === today.getTime()) {
            dateKey = 'Today';
        } else if (itemDateOnly.getTime() === yesterday.getTime()) {
            dateKey = 'Yesterday';
        } else {
            // Format as DD.MM.YYYY
            const day = itemDate.getDate().toString().padStart(2, '0');
            const month = (itemDate.getMonth() + 1).toString().padStart(2, '0');
            const year = itemDate.getFullYear();
            dateKey = `${day}.${month}.${year}`;
        }

        if (!grouped[dateKey]) {
            grouped[dateKey] = [];
        }
        grouped[dateKey].push(item);
    });

    // Sort groups by date (most recent first)
    const sortedGrouped = {};
    const sortedKeys = Object.keys(grouped).sort((a, b) => {
        if (a === 'Today') return -1;
        if (b === 'Today') return 1;
        if (a === 'Yesterday') return -1;
        if (b === 'Yesterday') return 1;

        // Parse DD.MM.YYYY format for comparison
        const parseDate = (dateStr) => {
            const [day, month, year] = dateStr.split('.').map(Number);
            return new Date(year, month - 1, day);
        };

        return parseDate(b) - parseDate(a);
    });

    sortedKeys.forEach(key => {
        sortedGrouped[key] = grouped[key];
    });

    return sortedGrouped;
}

function getUserInitials(userName) {
    if (!userName || typeof userName !== 'string') return '??';

    // Remove extra whitespace and split by spaces
    const parts = userName.trim().split(/\s+/).filter(part => part.length > 0);

    if (parts.length >= 2) {
        // First and last name initials
        return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
    } else if (parts.length === 1 && parts[0].length >= 2) {
        // First two characters of single name
        return parts[0].substring(0, 2).toUpperCase();
    } else if (parts.length === 1 && parts[0].length === 1) {
        // Single character, duplicate it
        return (parts[0][0] + parts[0][0]).toUpperCase();
    }

    return '??';
}

function getAvatarColor(activityType, userId) {
    switch (activityType.toLowerCase()) {
        case 'created': return 'blue';
        case 'updated': return 'blue';
        case 'deleted': return 'red';
        case 'run': return 'green';
        default:
            const hash = userId ? userId.split('').reduce((a, b) => { a = ((a << 5) - a) + b.charCodeAt(0); return a & a; }, 0) : 0;
            return ['blue', 'green', 'red'][Math.abs(hash) % 3];
    }
}

function getStatusClass(status) {
    switch (status.toLowerCase()) {
        case 'successful': return 'successful';
        case 'failed': return 'failed';
        case 'deleted': return 'deleted';
        case 'pending': return 'pending';
        case 'running': return 'running';
        default: return '';
    }
}

function getStatusDisplayText(status) {
    switch (status.toLowerCase()) {
        case 'successful': return 'Successful';
        case 'failed': return 'Failed';
        case 'deleted': return 'Deleted';
        case 'pending': return 'Pending';
        case 'running': return 'Running';
        default: return status;
    }
}

function formatTimestamp(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return 'Invalid Date';

    // Format as DD.MM.YYYY • HH:MM to match the expected format
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}.${month}.${year} • ${hours}:${minutes}`;
}

function formatDescription(description, userlaneName) {
    if (!description) return '';

    // Replace various placeholder patterns that might be in the description
    let formatted = description
        .replace('@Model.Userlane?.Name', `"${userlaneName || 'Unknown Userlane'}"`)
        .replace('{{userlaneName}}', `"${userlaneName || 'Unknown Userlane'}"`)
        .replace('{userlaneName}', `"${userlaneName || 'Unknown Userlane'}"`);

    return formatted;
}

function showFeedError(message) {
    const container = document.getElementById('feedContainer');
    container.innerHTML = `
        <div class="feed-empty">
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle fa-3x"></i>
                <h4>Error</h4>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="loadFeedData()">Retry</button>
            </div>
        </div>
    `;
}

// Show feed item details in sidebar
function showFeedDetails(feedItemId) {
    fetch(`/Api/UserlaneFeed/${feedItemId}`)
        .then(response => response.json())
        .then(result => {
            if (result.success && result.data) {
                const data = result.data;
                const sidebar = document.querySelector('.sidebar-content');
                sidebar.innerHTML = `
                    <div class="feed-details">
                        <h4>Activity Details</h4>
                        <div class="detail-item">
                            <strong>User:</strong> ${data.userName || 'Unknown User'}
                        </div>
                        ${data.userEmail ? `<div class="detail-item"><strong>Email:</strong> ${data.userEmail}</div>` : ''}
                        <div class="detail-item">
                            <strong>Activity Type:</strong> ${data.activityType || 'Unknown'}
                        </div>
                        <div class="detail-item">
                            <strong>Timestamp:</strong> ${formatTimestamp(data.activityTimestamp)}
                        </div>
                        ${data.status ? `<div class="detail-item"><strong>Status:</strong> <span class="feed-status ${getStatusClass(data.status)}">${getStatusDisplayText(data.status)}</span></div>` : ''}
                        <div class="detail-item">
                            <strong>Description:</strong> ${formatDescription(data.description, '@Model.Userlane?.Name')}
                        </div>
                        ${data.userlaneName ? `<div class="detail-item"><strong>Userlane:</strong> "${data.userlaneName}"</div>` : ''}
                        ${data.metadata ? `
                            <div class="detail-item">
                                <strong>Additional Information:</strong>
                                <pre>${formatMetadata(data.metadata)}</pre>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <strong>ID:</strong> <code>${data.id}</code>
                        </div>
                    </div>
                `;
            } else {
                showSidebarError('Could not load feed item details');
            }
        })
        .catch(error => {
            console.error('Error loading feed details:', error);
            showSidebarError('Error loading feed item details');
        });
}

function showSidebarError(message) {
    const sidebar = document.querySelector('.sidebar-content');
    sidebar.innerHTML = `
        <div class="sidebar-icon">
            <i class="fas fa-exclamation-triangle fa-3x"></i>
        </div>
        <h4>Error</h4>
        <p>${message}</p>
    `;
}

// Open report modal with specific feed item data
function openReportModal(feedItemId) {
    // If feedItemId is provided, we could load specific report data
    // For now, just open the existing modal
    openReportModal();
}

// Load feed data when the page loads or when the feed tab is activated
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the feed tab
    const currentUrl = window.location.href;
    if (currentUrl.includes('/feed') || currentUrl.includes('menu=feed')) {
        loadFeedData();
    }
});

// Also load when the feed menu item is clicked
document.addEventListener('click', function(e) {
    if (e.target.closest('[data-menu="feed"]')) {
        setTimeout(loadFeedData, 100); // Small delay to ensure the tab content is visible
    }
});
</script>