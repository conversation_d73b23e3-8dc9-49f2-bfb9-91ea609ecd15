@using Levelbuild.Core.EntityInterface
@model Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels.UserlaneForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@{
    var localizer = LocalizerFactory.Create("Userlane", "");
}

<div class="activity-feed">
    <div class="feed-main">
        <!-- Today Section -->
        <div class="feed-date-group">
            <div class="feed-item">
                <div class="feed-date-label">
                    <span class="feed-date">Today</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-connector">
                        <div class="timeline-line"></div>
                        <div class="feed-avatar">
                            <div class="avatar-circle blue">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                    </div>
                    <div class="feed-content">
                        <div class="feed-meta">
                            <span class="feed-user"><PERSON><PERSON>, <PERSON></span>
                            <span class="feed-time">10.02.2025 • 09:15</span>
                            <button class="feed-action-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="feed-description">
                            "@Model.Userlane?.Name" was updated.
                        </div>
                    </div>
                </div>
            </div>

            <div class="feed-item">
                <div class="feed-date-label empty"></div>
                <div class="timeline-content">
                    <div class="timeline-connector">
                        <div class="timeline-line"></div>
                        <div class="feed-avatar">
                            <div class="avatar-circle red">
                                DG
                            </div>
                        </div>
                    </div>
                    <div class="feed-content">
                        <div class="feed-meta">
                            <span class="feed-user">Miller, Andrew</span>
                            <span class="feed-time">10.02.2025 • 09:15</span>
                            <span class="feed-status deleted">Deleted</span>
                            <button class="feed-action-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="feed-description">
                            "@Model.Userlane?.Name" was deleted
                        </div>
                    </div>
                </div>
            </div>

            <div class="feed-separator">
                <span>Already read</span>
            </div>
        </div>

        <!-- Yesterday Section -->
        <div class="feed-date-group">
            <div class="feed-item">
                <div class="feed-date-label">
                    <span class="feed-date">Yesterday</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-connector">
                        <div class="timeline-line"></div>
                        <div class="feed-avatar">
                            <div class="avatar-circle green">
                                TG
                            </div>
                        </div>
                    </div>
                    <div class="feed-content">
                        <div class="feed-meta">
                            <span class="feed-user">Kendrick, Anna</span>
                            <span class="feed-time">10.02.2025 • 09:15</span>
                            <span class="feed-status successful">Successful</span>
                            <button class="feed-action-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="feed-description">
                            "@Model.Userlane?.Name" was run
                        </div>
                        <div class="feed-actions">
                            <button class="btn btn-outline-primary btn-sm" onclick="openReportModal()">Open report</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="feed-item">
                <div class="feed-date-label empty"></div>
                <div class="timeline-content">
                    <div class="timeline-connector">
                        <div class="timeline-line"></div>
                        <div class="feed-avatar">
                            <div class="avatar-circle blue">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                    </div>
                    <div class="feed-content">
                        <div class="feed-meta">
                            <span class="feed-user">Davison, William</span>
                            <span class="feed-time">10.02.2025 • 09:15</span>
                            <button class="feed-action-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="feed-description">
                            "@Model.Userlane?.Name" was created.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Specific Date Section -->
        <div class="feed-date-group">
            <div class="feed-item">
                <div class="feed-date-label">
                    <span class="feed-date">09.02.2025</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-connector">
                        <div class="timeline-line"></div>
                        <div class="feed-avatar">
                            <div class="avatar-circle red">
                                DG
                            </div>
                        </div>
                    </div>
                    <div class="feed-content">
                        <div class="feed-meta">
                            <span class="feed-user">Mathews, Samuel</span>
                            <span class="feed-time">10.02.2025 • 09:15</span>
                            <span class="feed-status failed">Failed</span>
                            <button class="feed-action-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="feed-description">
                            "@Model.Userlane?.Name" was run
                        </div>
                        <div class="feed-actions">
                            <button class="btn btn-outline-primary btn-sm" onclick="openReportModal()">Open report</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="feed-sidebar">
        <div class="sidebar-content">
            <div class="sidebar-icon">
                <i class="fas fa-search fa-3x"></i>
            </div>
            <h4>Further information</h4>
            <p>Please select an entry on the left for which you would like to see further information.</p>
        </div>
    </div>
</div>

<!-- Report Modal -->
<div id="reportModal" class="report-modal" style="display: none;">
    <div class="modal-backdrop" onclick="closeReportModal()"></div>
    <div class="modal-container">
        <div class="modal-header">
            <div class="modal-title">
                <i class="fas fa-chart-bar"></i>
                <span>Userlane Test Results</span>
            </div>
            <button class="modal-close" onclick="closeReportModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-content">
            <div class="results-section">
                <h3>Results</h3>
                
                <div class="results-table">
                    <div class="table-header">
                        <div class="col-actions">Actions</div>
                        <div class="col-test">Test Condition</div>
                        <div class="col-prev">Previous Duration</div>
                        <div class="col-present">Present Duration</div>
                    </div>
                    
                    <div class="table-body">
                        <!-- Action 1 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>1. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">40 secs</div>
                            <div class="col-present">30 secs</div>
                        </div>
                        
                        <!-- Test Condition 1 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to Field
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Test Condition 2 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to ma...
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Test Condition 3 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to Field
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Action 2 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>2. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Failed Test Condition -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to ma...
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Action 3 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>3. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">15 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Action 4 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>4. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Failed Test Condition for Action 4 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to Field
                            </div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Action 5 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>5. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">10 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button class="btn-cancel" onclick="closeReportModal()">Cancel</button>
            <button class="btn-download">Download Results</button>
            <button class="btn-try-again">Try again</button>
        </div>
    </div>
</div>

<style>
.activity-feed {
    display: flex;
    min-height: 100vh;
    width: 100%;
    background: #f8f9fa;
    margin: 0;
    padding: 0;
}

.feed-main {
    flex: 1;
    width: 50%;
    padding: 1.5rem;
    overflow-y: auto;
    background: white;
    border-right: 1px solid #e9ecef;
}

.feed-date-group {
    margin-bottom: 2rem;
}

.feed-date-label {
    width: 120px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    flex-shrink: 0;
    margin-top: 0.5rem;
}

.feed-date-label.empty {
    visibility: hidden;
}

.timeline-content {
    display: flex;
    gap: 1rem;
    flex: 1;
}

.feed-date {
    font-size: 1rem;
}

.feed-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 0;
    position: relative;
}

.timeline-connector {
    position: relative;
    width: 60px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-shrink: 0;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: -1.5rem;
    width: 2px;
    background: #e9ecef;
    transform: translateX(-50%);
}

.feed-item:last-child .timeline-line {
    display: none;
}

.feed-avatar {
    position: relative;
    z-index: 2;
    margin-top: 0.5rem;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    color: white;
}

.avatar-circle.blue {
    background-color: #0066cc;
}

.avatar-circle.red {
    background-color: #dc3545;
}

.avatar-circle.green {
    background-color: #28a745;
}

.feed-content {
    flex: 1;
    padding-top: 0.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f3f4;
}

.feed-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.feed-user {
    font-weight: 600;
    color: #495057;
}

.feed-time {
    color: #6c757d;
}

.feed-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.feed-status.successful {
    background-color: #d4edda;
    color: #155724;
}

.feed-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

.feed-status.deleted {
    background-color: #f8d7da;
    color: #721c24;
}

.feed-action-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: auto;
}

.feed-action-btn:hover {
    color: #495057;
}

.feed-description {
    color: #495057;
    margin-bottom: 0.75rem;
}

.feed-actions {
    margin-top: 0.75rem;
}

.feed-separator {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.feed-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
    z-index: 1;
}

.feed-separator span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

.feed-sidebar {
    flex: 1;
    width: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-content {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.sidebar-icon {
    opacity: 0.3;
    margin-bottom: 1rem;
}

.sidebar-content h4 {
    color: #495057;
    margin-bottom: 1rem;
}

.sidebar-content p {
    font-size: 0.875rem;
    line-height: 1.5;
}

.btn-outline-primary {
    border: 1px solid #0066cc;
    color: #0066cc;
    background: transparent;
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    text-decoration: none;
    display: inline-block;
}

.btn-outline-primary:hover {
    background: #0066cc;
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.8125rem;
}

/* Report Modal Styles */
.report-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-container {
    position: relative;
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.modal-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
}

.modal-close:hover {
    color: #495057;
}

.modal-content {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.results-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
    display: inline-block;
}

.results-table {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr;
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
}

.table-header > div {
    padding: 0.75rem 1rem;
    border-right: 1px solid #e9ecef;
}

.table-header > div:last-child {
    border-right: none;
}

.table-body {
    background: white;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
}

.table-row:last-child {
    border-bottom: none;
}

.table-row.test-condition {
    background: #f8f9fa;
}

.table-row > div {
    padding: 0.75rem 1rem;
    border-right: 1px solid #e9ecef;
}

.table-row > div:last-child {
    border-right: none;
}

.col-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-indicator {
    width: 4px;
    height: 20px;
    border-radius: 2px;
}

.action-indicator.success {
    background: #28a745;
}

.action-indicator.failed {
    background: #dc3545;
}

.col-test {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
}

.col-test i {
    color: #28a745;
    font-size: 0.875rem;
}

.col-prev, .col-present {
    font-size: 0.875rem;
    color: #495057;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.btn-cancel {
    background: none;
    border: 1px solid #6c757d;
    color: #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-cancel:hover {
    background: #6c757d;
    color: white;
}

.btn-download {
    background: none;
    border: 1px solid #007bff;
    color: #007bff;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-download:hover {
    background: #007bff;
    color: white;
}

.btn-try-again {
    background: #007bff;
    border: 1px solid #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-try-again:hover {
    background: #0056b3;
}
</style>

<script>
function openReportModal() {
    document.getElementById('reportModal').style.display = 'flex';
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function closeReportModal() {
    document.getElementById('reportModal').style.display = 'none';
    document.body.style.overflow = 'auto'; // Restore scrolling
}

// Close modal when pressing Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeReportModal();
    }
});
</script>