@model Levelbuild.Frontend.WebApp.Features.Module.ViewModels.ModuleForm
@using Humanizer
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("Userlanes", "");

	LocalizerFactory.Create("UserlanesType", "");

	var filtering = new List<QueryParamFilterDto>()
	{
		new()
		{
			FilterColumn = "Type",
			Operator = QueryParamFilterOperator.Equals,
			CompareValue = UserlaneType.Test
		}
	};
	var currentMenu = ViewData["targetMenu"] == null ? "Tests" : ViewData["targetMenu"]!.ToString()!;

	// Filter sections for the filter panel (focused on test lanes)
	IList<FilterPanelSectionComponentTagHelper> sections =
	[
		new() { Name = "name", Label = localizer["name"], MultiValue = true },
		new() { Name = "startPoint", Label = localizer["startPoint"], MultiValue = true },
	];

	List<BaseColumnComponentTagHelper> columns =
	[
		new ListDataColumnComponentTagHelper { Name = "name" },
		new ListDataColumnComponentTagHelper { Name = "startPoint", Label = localizer["startPoint"] },
	];
}

<script type="module" defer>
	@if (Model.Module != null)
	{ 
		<text>
			Page.setMainPage('/Admin/DataStores/@(Model.Module.DataStore?.Slug)/Modules/@(Model.Module.Slug)', '@(currentMenu)')
			Page.setBreadcrumbs([
				{ label: '@localizer["modules"]', url: `/Admin/DataStores/@(Model.Module.DataStore?.Slug)/Modules` },
				{ label: '@(Model.Module.Name)', url: `/Admin/DataStores/@(Model.Module.DataStore?.Slug)/Modules/@(Model.Module.Slug)` },
				{ label: '@localizer["Userlanes"]', url: Page.getMainPageUrl() }
			])
		</text>
	}
</script>
<script type="module" defer>
	async function runMarkedUserlanes() {
		const toaster = document.getElementById('toaster')
		// Show wait overlay
		await Overlay.showWait('@localizer["PleaseWait"]')
		
		let selectedUserlanes = [];
		let userlaneList = document.getElementById('userlane-list')
		if(!userlaneList){
			await Overlay.hideWait()
			toaster.notify({ heading: '@localizer["NoUserlaneTableFound"]', type: 'error' })
			return
		}else{
			selectedUserlanes = userlaneList.querySelector('lvl-list').selectedItems
		}
		
		if (selectedUserlanes.length < 1) {
			await Overlay.hideWait()
			toaster.notify({ heading: '@localizer["NoUserlanesSelected"]', type: 'error' })
			return
		}
		
		let testInformation = []
		
		for (let i = 0; i < selectedUserlanes.length; i++) {
			let userlane = selectedUserlanes[i]
			let userlaneData = await Page.getJSON(`/Api/Userlane/RunTour/${userlane.id}`)
			
			if (!userlaneData.data) {
				await Overlay.hideWait()
				toaster.notify({ heading: '@localizer["FailedToLoadUserlaneData"]', type: 'error' })
				return
			}
			
			testInformation.push({
				id: userlane.id,
				name: userlaneData.data.name,
				url: userlaneData.data.url,
				steps: userlaneData.data.steps,
				completed: false
			})
		}
		await Overlay.hideWait()
		Userlane.runSelectedTests(testInformation)
	}
</script>

<!-- Filter panel for test lanes -->
<vc:filter-panel entity="Userlane" route-name="Userlane" sections="@sections" in-admin-config="true"></vc:filter-panel>

<div class="list-view">
	
	<enumeration-component id="userlane-list" url="/Api/Userlane" filters="@filtering" limit="50" with-sorting with-navigation>
		
		<div>
			<lvl-button onclick="runMarkedUserlanes()" label="Run" tooltip="Run selected tests" icon="play"></lvl-button>
			<lvl-button label="Duplicate" icon="copy"></lvl-button>
			<lvl-button label="Delete" icon="delete" style="color: #dc3545;"></lvl-button>
		</div>
		
		<list-component identity-column="id">
			<lvl-list-column type="flag" with-converter></lvl-list-column>
			<lvl-list-column type="select"></lvl-list-column>
			<lvl-list-data-column name="name" label="Userlanes name"></lvl-list-data-column>
			<lvl-list-data-column name="status" type="boolean" label="Status" width="50" text-align="center"></lvl-list-data-column>
			<lvl-list-data-column name="testerRole" label="Tester Role"></lvl-list-data-column>
			<lvl-list-data-column name="latestRuntime" label="Latest Runtime" text-align="right"></lvl-list-data-column>
			<lvl-list-data-column name="lastExecuted" label="Last Executed"></lvl-list-data-column>
			<lvl-list-data-column name="runUserlane" label="Run Userlane" icon="circle-play"></lvl-list-data-column>
			<lvl-list-column type="option-menu">
				<lvl-menu>
					<lvl-menu-item onclick="runMarkedUserlanes()">Run</lvl-menu-item>
					<lvl-menu-item>Duplicate</lvl-menu-item>
					<lvl-menu-item>Delete</lvl-menu-item>
				</lvl-menu>
			</lvl-list-column>
		</list-component>
	</enumeration-component>
</div>
<vc:admin-list-page entity="Userlane" route-name="Userlane" filter-by-menu="true" route-params="{type:'@((int)UserlaneType.Test)'}" parent-property-name="moduleId" parent-property-value="@Model.Module?.Id" localizer="@localizer" columns="@columns" use-custom-list="true" open-on-row-click="true"></vc:admin-list-page>
<vc:create-panel entity="Userlane" route-name="Userlane" parent-property-name="moduleId" parent-property-value="@Model.Module?.Id" localizer="@localizer"></vc:create-panel>

@{
	var kebabEntity = "Userlane".Kebaberize();
}
<script type="module" defer>
	
	let selectedUserlanes = [];
	const enumeration = document.getElementById('@(kebabEntity)-list');
	const list = enumeration?.querySelector('lvl-list');
	
	// Set up row number converter for flag column
	const flagColumn = list?.querySelector('lvl-list-column[type="flag"]');
	if (flagColumn) {
		flagColumn.converter = (data, index) => index + 1;
	}

	function navigateToNewPage(rowContent) {
		Page.setMainPage(`/Admin/Userlane/${rowContent.data.id}`)
		Page.setInfo(`/Admin/Userlane/${rowContent.data.id}`)
		Page.load(`/Admin/Userlane/${rowContent.data.id}`,{},{getRootNode:document.querySelector('main')})
	}

	/**
	 * Click on table row opens the detail view to edit the entity configuration
	 * @@param rowContent {object} complete value map of the entity configuration
	 */
	list.onRowClick = async (rowContent) => {
		if (selectedUserlanes.length < 1 && !rowContent.selected && list.selectedItems.length < 1){
			navigateToNewPage(rowContent)
			return;
		}
		selectedUserlanes = list.selectedItems
	}


</script>
