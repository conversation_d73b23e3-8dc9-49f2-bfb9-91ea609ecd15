@model Levelbuild.Frontend.WebApp.Features.Module.ViewModels.ModuleForm
@using Humanizer
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("Userlanes", "");
	
	var localizerStoreType = LocalizerFactory.Create("UserlanesType", "");
	
	var filtering = new List<QueryParamFilterDto>()
	{
		new()
		{
			FilterColumn = "Type",
			Operator = QueryParamFilterOperator.NotEquals,
			CompareValue = UserlaneType.Test
		}
	};
	
	// Add module filter only if module context is available
	if (Model.Module?.Id != null)
	{
		filtering.Add(new()
		{
			FilterColumn = "ModuleId",
			Operator = QueryParamFilterOperator.Equals,
			CompareValue = Model.Module.Id
		});
	}
	

	var currentMenu = ViewData["targetMenu"] == null ? "Tours" : ViewData["targetMenu"]!.ToString()!;

	var typeValues = Enum.GetValues(typeof(UserlaneType))
		.Cast<UserlaneType>()
		.Where(type => type != UserlaneType.Test) // Exclude Test type from this view
		.Select(type => new DataColumnValueDefinition((int)type, localizerStoreType[type.GetString()]))
		.ToList();

	// Filter sections for the filter panel (excludes Test type)
	IList<FilterPanelSectionComponentTagHelper> sections =
	[
		new() { Name = "name", Label = localizer["name"], MultiValue = true },
		new() { Name = "startPoint", Label = localizer["startPoint"], MultiValue = true },
		new() { Name = "speed", Label = localizer["speed"], MultiValue = true },
		new() { Name = "type", Label = localizer["type"], ValuePreview = true, MultiValue = true, Type = InputDataType.Enum, PossibleValues = typeValues.Select(tv => new FilterFieldQueryItemResultDto { Value = tv.Value, Label = tv.Label }).ToList()},
	];

	List<BaseColumnComponentTagHelper> columns =
	[
		new ListDataColumnComponentTagHelper { Name = "name" },
		new ListDataColumnComponentTagHelper { Name = "startPoint", Label = localizer["startPoint"] },
		new ListDataColumnComponentTagHelper { Name = "speed", Label = localizer["speed"] },
		new ListDataColumnComponentTagHelper { Name = "type", Label = localizer["type"], Values = typeValues},
	];
}

<script type="module" defer>
	@if (Model.Module != null)
	{ 
		<text>
			Page.setMainPage('/Admin/DataStores/@(Model.Module.DataStore?.Slug)/Modules/@(Model.Module.Slug)', '@(currentMenu)')
			Page.setBreadcrumbs([
				{ label: '@(Model.Module.DataStore?.Name)', url: `/Admin/DataStores/@(Model.Module.DataStore?.Slug)` },
				{ label: '@(Model.Module.Name)', url: `/Admin/DataStores/@(Model.Module.DataStore?.Slug)/Modules/@(Model.Module.Slug)` },
				{ label: '@localizer["Userlanes"]', url: Page.getMainPageUrl() }
			])
		</text>
	}
</script>


<!-- Filter panel -->
<vc:filter-panel entity="Userlane" route-name="Userlane" sections="@sections" in-admin-config="true"></vc:filter-panel>

<!-- Userlane list (excludes tests) -->
<div class="list-view">
	<enumeration-component id="userlane-list" url="/Api/Userlane" filters="@filtering">
		<list-component identity-column="id">
			<list-data-column-component name="name"></list-data-column-component>
			<list-data-column-component name="startPoint" label="@localizer["startPoint"]"></list-data-column-component>
			<list-data-column-component name="speed" label="@localizer["speed"]"></list-data-column-component>
			<list-data-column-component name="type" label="@localizer["type"]" values="@typeValues"></list-data-column-component>
		</list-component>
	</enumeration-component>
</div>
<vc:create-panel entity="Userlane" route-name="Userlane" localizer="@localizer"></vc:create-panel>

<script type="module" defer>
	// Set up create panel with module context
	const createPanel = document.querySelector('vc-create-panel[entity="Userlane"]');
	if (createPanel) {
		createPanel.addEventListener('panel:opened', () => {
			// Find the form in the create panel
			const form = document.querySelector('#create-panel form, #create-panel lvl-form');
			if (form) {
				const moduleInput = form.querySelector('input[name="moduleId"]');
				if (moduleInput && '@Model.Module?.Id') {
					moduleInput.value = '@Model.Module?.Id';
					moduleInput.dispatchEvent(new Event('change'));
					console.log('Set moduleId to:', '@Model.Module?.Id');
				}
			}
		});
	}
</script>

@{
	var kebabEntity = "Userlane".Kebaberize();
}

<script type="module" defer>

	const enumeration = document.getElementById('@(kebabEntity)-list');
	const list = enumeration?.children[0];

	/**
	 * Click on table row opens the detail view to edit the entity configuration
	 * @@param rowContent {object} complete value map of the entity configuration
	 */
	list.onRowClick = async (rowContent) => {
		Page.setMainPage(`/Admin/Userlane/${rowContent.data.id}`)
		Page.setInfo(`/Admin/Userlane/${rowContent.data.id}`)
		Page.load(`/Admin/Userlane/${rowContent.data.id}`,{},{getRootNode:document.querySelector('main')})
	}

</script>
