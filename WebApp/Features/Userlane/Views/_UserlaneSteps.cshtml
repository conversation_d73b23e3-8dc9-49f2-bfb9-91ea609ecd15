@model Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels.UserlaneForm
@using Humanizer
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("UserlaneStep", "");
	var sortings = new List<QueryParamSortingDto>()
	{
		new()
		{
			OrderColumn = "Order",
			Direction = SortDirection.Asc
		}
	};
}
<script type="module" defer>
	// disable save button
	Page.buttonConfig.saveButton.disabled = true
	// disable delete button on this panel
	Page.buttonConfig.deleteButton.disabled = true
</script>
<style>
	lvl-data-list {
		width: clamp(300px, 80%, 1400px);
		background-color: var(--clr-background-lvl-0);
		padding: 0 var(--size-spacing-m);
		border-radius: var(--size-radius-m);
		box-shadow: 0 0 0.2rem 0 var(--clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--clr-shadow-weak);
	}
</style>
<div class="list-view">
	@* ReSharper disable CSharpWarnings::CS0618 *@
	<enumeration-component id="userlane-steps-list" sorting="@sortings">
		<list-component identity-column="id">
			<list-data-column-component name="order" width="300"></list-data-column-component>
			<list-data-column-component name="title" label="@localizer["title"]"></list-data-column-component>
			<list-data-column-component name="delay" label="@localizer["delay"]" live-editable="false"></list-data-column-component>
		</list-component>
	</enumeration-component>
</div>

<vc:admin-list-page entity="UserlaneSteps" route-name="UserlaneSteps" localizer="@localizer" menu-entry="UserlaneSteps" parent-property-name="userlaneId" use-custom-list="true"></vc:admin-list-page>
<fab-component data-action="add"></fab-component>
<slide-out-component id="create-panel" class="side-panel" position="Alignment.Right" modal anchor heading="@localizer["newStep"]" width="370" open="@(ViewData["targetAction"]?.ToString() == "Create")">
	<div class="content vanishing-scrollbar static-scrollbar">
		@if (ViewData["targetAction"]?.ToString() == "Create")
		{
			@await Html.PartialAsync(ViewData["targetAction"] as string, ViewData["targetViewModel"])
		}
	</div>
	<button-component slot="button-left" data-action="cancel" label="@localizer["abortButton"]" type="ButtonType.Secondary" color="ColorState.Info"></button-component>
	<button-component slot="button-right" data-action="save" label="@localizer["saveButton"]" type="ButtonType.Primary"></button-component>
</slide-out-component>

@{
	var kebabEntity = "UserlaneSteps".Kebaberize();
}

<script type="module" defer>
	
	const enumeration = document.getElementById('@(kebabEntity)-list')
	const list = enumeration?.querySelector('& > *')
	
	@if (ViewData["targetAction"]?.ToString() == "Create")
	{
		@:Page.setPanelInfo(`${Page.getMainPageUrl()}/UserlaneSteps/Create`, {}, '@localizer["list/newItem"]')
	}
	Page.registerCreatePanel(document.getElementById('create-panel'));

	/**
	 * opens a dialog to create a new step
	 * @@param mouseEvent {MouseEvent} button click event
	 */
	document.querySelector('lvl-fab[data-action=add]').addEventListener("click", async () =>
	{
		await Page.showCreatePanel('UserlaneSteps/Create',  {type: Page.getFormData().type}, '@localizer["newStep"]')
		Page.setPanelInfo(`${Page.getMainPageUrl()}/UserlaneSteps/Create`, {type: Page.getFormData().type}, '@localizer["newStep"]');
		@* // TODO: remove as soon as all forms are lvl - form *@
		const form = Page.createSlideOut.querySelector('lvl-form')
		const parentInput = form.querySelector('input[name="userlaneId"]')
		if (parentInput && Page.getFormData()?.id) {
			parentInput.value = Page.getFormData().id
			parentInput.dispatchEvent(new Event('change'));
		}
	})

	const handleCreateSaveButtonClick = async () => {
		const result = await Form.storeData(Page.createSlideOut.querySelector('form, lvl-form'), '/Api/UserlaneSteps', 'POST')
		if (!result)
			return
		Page.createSlideOut.open = false
		enumeration?.reload()
	}

	const createSaveButton = Page.createSlideOut.querySelector('[data-action="save"]')
	createSaveButton?.addEventListener('click', handleCreateSaveButtonClick, { signal: Page.getPageChangeSignal() })
	Page.createSlideOut.setAttribute('initDone', '')

	/**
	 * Click on table row opens the detail view to edit the entity configuration
	 * @@param rowContent {object} complete value map of the entity configuration
	 * @@param rowIndex {number}
	 */
	list.onRowClick = async (rowContent, index) => {
		Page.setMainPage(`/Admin/Userlane/Step/${rowContent.data.id}`)
		Page.setInfo(`/Admin/Userlane/Step/${rowContent.data.id}`)
		Page.load(`/Admin/Userlane/Step/${rowContent.data.id}`,{},{getRootNode:document.querySelector('main')})
	}


</script>
