@page

@{
	Layout = null;
	var jsonData = ViewBag.JsonData as string;
	var accessToken = ViewBag.AccessToken;
	
	var user = await ViewBag.UserManager.VerifyWopiTokenAsync(accessToken);
	var currentUser = await ViewBag.UserManager.GetCurrentUserAsync();

	// Conditionally emit the script only if conditions are met
	if (currentUser != null)
	{
		<text>
			<script>
                window.receivedJsonData = @Html.Raw(Json.Serialize(jsonData ?? ""));
            </script>
		</text>
	}
}