using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.UserlaneStep;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.UserlaneStep.ViewModels;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.UserlaneStep.Controllers;

/// <inheritdoc />
public class UserlaneStepController : AdminController<UserlaneStepDto>
{
	private readonly ILogger _logger;

	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="localizerFactory"></param>
	/// <param name="versionReader">injected VersionReader</param>
	/// <param name="contextFactory"></param>
	/// <param name="userManager"></param>
	[ExcludeFromCodeCoverage]
	public UserlaneStepController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
								   IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : base(
		logManager, logManager.GetLoggerForClass<UserlaneStepController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
	}

	#region Views
	
	/// <summary>
	/// Get the Userlane step form
	/// </summary>
	/// <returns></returns>
	[HttpGet("/Admin/UserlaneSteps/Create")]
	[HttpGet("/Admin/Userlane/{userlaneId:guid}/UserlaneSteps/Create")]
	public IActionResult Create(Guid userlaneId)
	{
		List<AutocompleteOptionDefinition> dropDownOptions;
		var userlane = DatabaseContext.Userlanes.Find(userlaneId);
		
		Console.WriteLine(userlaneId);
		
		List<GridViewFieldEntity> dataFields;
		if (userlane?.PageId != null && userlane.PageId != "custom")
		{
			var page = DatabaseContext.Pages.First(x=> x.Id == Guid.Parse(userlane.PageId));
			
			switch (page.Type)
			{

				case PageType.SingleData:
				case PageType.Create:
				case PageType.MultiData:
					// First, we collect the pages associated with the Multidata page
					var pages = DatabaseContext.MultiDataPages.First(x => x.Id == page.Id);

					// We collect the Views of the pages 
					var pageViews = DatabaseContext.PageViews.Where(pv => pv.PageId == pages.CreatePageId || pv.PageId == pages.DetailPageId || pv.PageId == Guid.Parse(userlane.PageId) ).Select(pv => pv.Id).ToList();

					// We collect the GridView Section using the page ID
					var gridViewSection = DatabaseContext.GridViewSections.Where(grid => pageViews.Contains(grid.GridViewId)).Select(g => g.Id).ToList();
					
					

					// Use the grid view Section ID to collect the data
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false)
						.Where(pag => gridViewSection.Contains(pag.SectionId))
						.AsQueryable().ToList();
					
					break;
				default:
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
					break;
			}
			 dropDownOptions = dataFields.Select(dataField =>
													  new AutocompleteOptionDefinition(
														  dataField.Id,
														   (dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													  )
			).ToList();
		}
		else
		{
			dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}
		
		
		return RenderPartial(new UserlaneStepForm()
								  {
									  UserlaneStep = new UserlaneStepDto
									  {
										  UserlaneId = userlaneId,
										  Order = 0
									  }, 
									  DataFields = dropDownOptions
								  }, 
										"Create", new UserlaneStepForm()
										{
											UserlaneId = userlaneId , DataFields = dropDownOptions
										});
	}
	


	/// Userlane Step edit
	[HttpGet("/Admin/UserlaneStep/Edit/{menu?}")]
	[HttpGet("/Admin/Userlane/Step/{userlaneStepId?}/{menu?}")]
	public IActionResult Details(Guid? userlaneStepId, string? menu)
	{
		var dropDownOptions = new List<AutocompleteOptionDefinition>();
		
		if (userlaneStepId != null)
		{ 
			// Get the step details first
			var userlaneStep = DatabaseContext.UserlaneSteps.Find(userlaneStepId);
			if(userlaneStep == null)
			{
				return NotFound($"Userlane Step with ID {userlaneStepId} not found.");
			}
			
			//Get the userlane using the step
			var userlane = DatabaseContext.Userlanes.Find(userlaneStep.UserlaneId);
			
			List<GridViewFieldEntity> dataFields;
		
			// Logic required to find the data fields
			if (userlane?.PageId != null && userlane.PageId != "custom")
			{
				var page = DatabaseContext.Pages.First(x=> x.Id == Guid.Parse(userlane.PageId));
				
				switch (page.Type)
				{
				
					case PageType.SingleData:
					case PageType.Create:
					case PageType.MultiData :
						// Since the Multi data page can be used to get the form and single data pages,we get all the fields
						// and then show the page in the select
						dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
						break;
					default:
						dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
						break;
				}
				dropDownOptions = dataFields.Select(dataField =>
														new AutocompleteOptionDefinition(
															dataField.Id,
															(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
														)
				).ToList();
			}else
			{
				dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
				dropDownOptions = dataFields.Select(dataField =>
														new AutocompleteOptionDefinition(
															dataField.Id,
															(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
														)
				).ToList();
			}
			
			var userlaneStepsEntity = DatabaseContext.UserlaneSteps.First(x => x.Id == userlaneStepId).ToDto();
			return RenderPartial(new UserlaneStepForm()
			{
				UserlaneStep = userlaneStepsEntity, 
				UserlaneId = userlaneStepsEntity.UserlaneId,
				DataFields = dropDownOptions
			});
		}
	
		return RenderPartial(new UserlaneStepForm(){DataFields = dropDownOptions});
	}
	
	#endregion

	 
	#region Actions
	
	/// <inheritdoc />
	[HttpGet("/Api/UserlaneSteps")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		var query = DatabaseContext.UserlaneSteps;
		return HandleQueryRequest<UserlaneStepEntity, UserlaneStepDto>(query, parameters);
	}

	/// <inheritdoc />
	[HttpGet("/Api/UserlaneStep/{userlaneStepId:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid userlaneStepId)
	{
		// Retrieve the specific UserlaneStep based on the provided id
		var query = DatabaseContext.UserlaneSteps;
		return HandleGetRequest<UserlaneStepEntity, UserlaneStepDto>(query, userlaneStepId);
	}

	/// <inheritdoc />
	[HttpPost("/Api/UserlaneSteps")]
	public override Task<ActionResult<FrontendResponse>> Create([FromBody] UserlaneStepDto userlanesStepsDto)
	{
		var result = HandleCreateRequestAsync(DatabaseContext.UserlaneSteps, userlanesStepsDto);
		return result;
	}

	/// <inheritdoc />
	[HttpPatch("/Api/UserlaneStep/{userlaneStepId:guid}")]
	public override Task<ActionResult<FrontendResponse>> Update(Guid userlaneStepId,[FromBody] UserlaneStepDto dto)
	{
		return HandleUpdateRequestAsync(DatabaseContext.UserlaneSteps, userlaneStepId, dto);
	}
	
	/// <inheritdoc />
	[HttpDelete("/Api/UserlaneStep/{userlaneStepId:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid userlaneStepId)
	{
		return HandleDeleteRequest(DatabaseContext.UserlaneSteps, userlaneStepId);
	}
	#endregion
}
