@model Levelbuild.Frontend.WebApp.Features.UserlaneStep.ViewModels.UserlaneStepForm
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("Userlanes", "");
	
	IList<MenuItem> menuItems = new List<MenuItem>()
	{
		new("basic", "BasicSettings", "screwdriver-wrench"),
		new("action", "Actions", "person-digging"),
	};
	
	var currentMenu = ViewData["targetMenu"] == null ? "BasicSettings" : ViewData["targetMenu"]?.ToString()!;
}

<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="UserlaneStep" route-name="UserlaneStep" menu-items="@menuItems" skeleton="@(Model.UserlaneStep == null)" ></vc:basic-menu>
<vc:admin-detail-page entity="UserlaneStep" route-name="UserlaneStep" model="@Model" title="@Model.UserlaneStep?.Title" menu-item="@currentMenu" show-default-buttons="@(Model.UserlaneStep != null)"></vc:admin-detail-page>

<script type="module" defer>
	@if (Model.UserlaneStep != null)
	{ 
		<text>
			Page.setMainPage(`/Admin/Userlane/Step/@(Model.UserlaneStep.Id)`, '@(currentMenu)')
			Page.setBreadcrumbs([
				{ label: '@(localizer["Userlanes"])', url: '/Admin/Userlanes' },
				{ label: '@(localizer["UserlaneDetails"])', url: `/Admin/Userlane/@(Model.UserlaneStep.UserlaneId)/UserlaneSteps` },
				{ label: '@(Model.UserlaneStep.Title)', url: `/Admin/Userlane/Step/@(Model.UserlaneStep.Id)` }
			],true)
	
		</text> 
	}
</script>
