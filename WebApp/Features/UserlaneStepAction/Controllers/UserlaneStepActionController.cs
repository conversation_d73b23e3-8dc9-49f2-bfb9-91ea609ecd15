using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.UserlaneStepAction;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.UserlaneStepAction.ViewModels;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;

namespace Levelbuild.Frontend.WebApp.Features.UserlaneStepAction.Controllers;

/// <inheritdoc />
public class UserlaneStepActionController : AdminController<UserlaneStepActionDto>
{
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="localizerFactory"></param>
	/// <param name="versionReader">injected VersionReader</param>
	/// <param name="contextFactory"></param>
	/// <param name="userManager"></param>
	public UserlaneStepActionController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
										 IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : base(
		logManager, logManager.GetLoggerForClass<UserlaneStepActionController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
	}
	
	#region Views

	/// <summary>
	/// Renders the list view with help of the list dto
	/// </summary>
	/// <returns>rendered list view</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Userlane/Step/{userlaneStepId}/userlaneStepActions")]
	public IActionResult List(Guid userlaneStepId)
	{
		return CachedPartial() ?? RenderPartial(new UserlaneStepActionList { UserlaneStepId = userlaneStepId });
	}

	/// <summary>
	/// Renders the view for the "CreateAction" endpoint.
	/// </summary>
	/// <remarks>
	/// This action is mapped to two routes:
	/// - "/Admin/UserlaneStepActions/Create"
	/// - "/Admin/Userlanes/{id}/Create"
	/// </remarks>
	/// <returns>A partial view rendering the required data.</returns>
	[HttpGet("/Admin/Userlane/Step/{userlaneStepId}/userlaneStepAction/Create")]
	public IActionResult Create(Guid userlaneStepId)
	{
		List<GridViewFieldEntity> dataFields;
		List<AutocompleteOptionDefinition> dropDownOptions;
		
		
		// Find the UserlaneStep first and check if it exists
		var userlaneStep = DatabaseContext.UserlaneSteps.Find(userlaneStepId);
		if (userlaneStep == null)
		{
			return BadRequest($"UserlaneStep with ID {userlaneStepId} not found.");
		}
		
		var userlane = DatabaseContext.Userlanes.Find(userlaneStep.UserlaneId);
		if (userlane?.PageId != null && userlane.PageId != "custom")
		{
			var page = DatabaseContext.Pages.First(x=> x.Id == Guid.Parse(userlane.PageId));
			switch (page.Type)
			{

				case PageType.SingleData:
				case PageType.Create:
				case PageType.MultiData:
					// First, we collect the pages associated with the Multidata page
					var pages = DatabaseContext.MultiDataPages.First(x => x.Id == page.Id);

					// We collect the Views of the pages 
					var pageViews = DatabaseContext.PageViews.Where(pv => pv.PageId == pages.CreatePageId || pv.PageId == pages.DetailPageId).Select(pv => pv.Id).ToList();

					// We collect the GridView Section using the page ID
					var gridViewSection = DatabaseContext.GridViewSections.Where(grid => pageViews.Contains(grid.GridViewId)).Select(g => g.Id).ToList();

					// Use the grid view Section ID to collect the data
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false)
						.Where(pag => gridViewSection.Contains(pag.SectionId))
						.AsQueryable().ToList();
					
					break;
				default:
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
					break;
			}
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}else
		{
			dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}
		
		return RenderPartial(new UserlaneStepActionForm() { UserlaneStepAction = new UserlaneStepActionDto
							 {
								 UserlaneStepId = userlaneStepId
							 },DataFields = dropDownOptions}, 
								  "Create", new UserlaneStepActionForm { UserlaneStepId = userlaneStepId ,DataFields = dropDownOptions});
	}

	/// <summary>
	/// Renders the view for the "Details" endpoint.
	/// </summary>
	/// <param name="userlaneStepActionId"></param>
	/// <param name="menu"></param>
	/// <returns></returns>
	[HttpGet("/Admin/Userlane/Step/Action/{userlaneStepActionId}/{menu?}")]
	public IActionResult Details(Guid userlaneStepActionId, string? menu)
	{
		List<GridViewFieldEntity> dataFields;
		var userlaneStepAction = DatabaseContext.UserlaneStepActions.Find(userlaneStepActionId);
		if (userlaneStepAction == null)
		{
			return BadRequest($"UserlaneStepAction with ID {userlaneStepActionId} not found.");
		}
		var userlaneStepId = userlaneStepAction.UserlaneStepId;
		List<AutocompleteOptionDefinition> dropDownOptions;
		
		var userlaneStep = DatabaseContext.UserlaneSteps.Find(userlaneStepId);
		if (userlaneStep == null)
		{
			return BadRequest($"UserlaneStep with ID {userlaneStepId} not found.");
		}
		
		var userlane = DatabaseContext.Userlanes.Find(userlaneStep.UserlaneId);
		if (userlane?.PageId != null && userlane.PageId != "custom")
		{
			var page = DatabaseContext.Pages.First(x=> x.Id == Guid.Parse(userlane.PageId));
			
			switch (page.Type)
			{

				case PageType.SingleData:
				case PageType.Create:
				case PageType.MultiData:
					// First, we collect the pages associated with the Multidata page
					var pages = DatabaseContext.MultiDataPages.First(x => x.Id == page.Id);

					// We collect the Views of the pages 
					var pageViews = DatabaseContext.PageViews.Where(pv => pv.PageId == pages.CreatePageId || pv.PageId == pages.DetailPageId).Select(pv => pv.Id).ToList();

					// We collect the GridView Section using the page ID
					var gridViewSection = DatabaseContext.GridViewSections.Where(grid => pageViews.Contains(grid.GridViewId)).Select(g => g.Id).ToList();

					// Use the grid view Section ID to collect the data
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false)
						.Where(pag => gridViewSection.Contains(pag.SectionId))
						.AsQueryable().ToList();
					
					break;
				default:
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
					break;
			}
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}
		else {
			dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}

		if (userlaneStepActionId == Guid.Empty) return RenderPartial(new UserlaneStepActionForm(ViewType.Edit));
		{
			var userlaneStepActionEntity = DatabaseContext.UserlaneStepActions.First(x => x.Id == userlaneStepActionId).ToDto();
			ViewData["targetMenu"] = menu ?? "BasicSettings";
			return RenderPartial(new UserlaneStepActionForm(ViewType.Edit)
			{
				UserlaneStepAction = userlaneStepActionEntity,
				UserlaneStepId = userlaneStepActionEntity.UserlaneStepId,
				DataFields = dropDownOptions
			});
		}
	}
	
	#endregion
	
	#region Actions

	/// <inheritdoc />
	[HttpGet("/Api/UserlaneStepAction")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		var query = DatabaseContext.UserlaneStepActions;
		return HandleQueryRequest<UserlaneStepActionEntity, UserlaneStepActionDto>(query, parameters);
	}

	/// <inheritdoc />
	[HttpGet("/Api/UserlaneStepAction/{userlaneStepActionId}")]
	public override ActionResult<FrontendResponse> Get(Guid userlaneStepActionId)
	{
		return HandleGetRequest<UserlaneStepActionEntity, UserlaneStepActionDto>(DatabaseContext.UserlaneStepActions, userlaneStepActionId);
	}

	/// <inheritdoc />
	[HttpPost("/Api/UserlaneStepAction")]
	public override Task<ActionResult<FrontendResponse>> Create([FromBody] UserlaneStepActionDto userlaneStepActionDto)
	{
		Console.WriteLine($"Create UserlaneStepAction request parameters: {System.Text.Json.JsonSerializer.Serialize(userlaneStepActionDto)}");
		
		return HandleCreateRequestAsync(DatabaseContext.UserlaneStepActions, userlaneStepActionDto);
	}


	/// <summary>
	/// 
	/// </summary>
	/// <param name="userlaneStepActionId"></param>
	/// <param name="dto"></param>
	/// <returns></returns>
	[HttpPatch("/Api/UserlaneStepAction/{userlaneStepActionId:guid}")]
	public override Task<ActionResult<FrontendResponse>> Update(Guid userlaneStepActionId, UserlaneStepActionDto dto)
	{
		return HandleUpdateRequestAsync(DatabaseContext.UserlaneStepActions, userlaneStepActionId, dto);
	}

	/// <inheritdoc />
	[HttpDelete("/Api/UserlaneStepAction/{userlaneStepActionId:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid userlaneStepActionId)
	{
		return HandleDeleteRequest(DatabaseContext.UserlaneStepActions, userlaneStepActionId);
	}
	
	#endregion
}