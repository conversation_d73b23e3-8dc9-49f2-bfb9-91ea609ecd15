@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@inject IExtendedStringLocalizerFactory LocalizerFactory
@model Levelbuild.Frontend.WebApp.Features.UserlaneStepAction.ViewModels.UserlaneStepActionForm
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@{
	var localizer = LocalizerFactory.Create("UserlaneStepAction", "");
	var typeOptions = Model.UserlaneStepActionType.Select(type => new AutocompleteOptionDefinition(type.ToString(), localizer[type.ToString()])).ToList();
	var targetOptions = Model.DataFields.Select(dataField =>
	{
		// Split the Label string by spaces
		var labelParts = (dataField.Label?.ToString()!).Split(' ');

		// Localize each part and join them with a space
		var localizedLabel = string.Join(" : ", labelParts.Select(part => localizer[part]));

		// Return the AutocompleteOptionDefinition with localized parts
		return new AutocompleteOptionDefinition(
			dataField.Value!, 
			localizedLabel
		);
	}).ToList();
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-save", localizer["topBarSaveButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-delete", localizer["topBarDeleteButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-preview", localizer["topBarPreviewButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-edit", localizer["topBarEditButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-sync", localizer["topBarSyncButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-sync-all", localizer["topSyncAllSaveButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-import", localizer["topBarImportButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-impersonate", localizer["topBarImpersonateButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-deactivate", localizer["topBarDeactivateButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-reactivate", localizer["topBarReactivateButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("dialog-cancel-button", localizer["dialogCancelButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("dialog-save-button", localizer["dialogSaveButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("mask-edit-button", localizer["maskEditButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("createRecord", localizer["createButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("saveRecord", localizer["saveButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("cancelRecord", localizer["cancelButton"]));
}

<div class="grid--centered">
	<form-component id="userlanes-form" skeleton="@(Model is { ViewType: ViewType.Edit, UserlaneStepAction: null })">
		<div class="form__item">
			<input type="hidden" class="item__value" id="id" name="id" value="@Model.UserlaneStepAction?.Id"/>
			<input type="hidden" class="item__value" id="userlaneStepId" name="userlaneStepId" value="@Model.UserlaneStepId"/>
		</div>
		
		<!-- Page Accessed Information -->
		<info-component type="InfoType.Info" label="Page Accessed: Page_name"></info-component>
		
		<!-- Action Type - Primary field -->
		<config-section label="Action Configuration">
			<div class="form__item">
				<config-label target="actionType" label="Action Type"></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="actionType"
					options="typeOptions"
					name="actionType"
					value = "@((object?)Model.UserlaneStepAction?.ActionType ?? "")"
					class="item__value"
					placeholder="Select action type..."
					required="true"
					help-text="Select how the action will be executed (e.g., Click, Hover, Load).">
				</autocomplete-component>
			</div>

			<!-- Hidden fields for Order and Delay with default values -->
			<div class="form__item" style="display: none;">
				<input-component type="InputDataType.Integer" min="0" step="1" name="order" id="order" value="@(Model.UserlaneStepAction?.Order ?? 1)" class="item__value"></input-component>
			</div>

			<div class="form__item" style="display: none;">
				<input-component type="InputDataType.Integer" min="0" step="1" name="delay" id="delay" value="@(Model.UserlaneStepAction?.Delay ?? 0)" class="item__value"></input-component>
			</div>

			<!-- Dynamic fields that show based on action type -->
			<div class="form__item" id="targetFormSection">
				<config-label target="target" label="@localizer["Target"]"></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="target"
					options="targetOptions"
					name="target"
					value = "@(Model.UserlaneStepAction?.Target ?? "")"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="false">
				</autocomplete-component>
			</div>

			<div class="form__item" id="triggerFromSection">
				<config-label target="trigger" label="@localizer["Trigger"]"></config-label>
				<input-component type="InputDataType.String" name="trigger" id="trigger" placeholder="@localizer["Trigger"]" value="@Model.UserlaneStepAction?.Trigger" class="item__value"></input-component>
			</div>
			
			<div class="form__item" id="targetValueFormSection">
				<config-label target="targetValue" label="@localizer["TargetValue"]"></config-label>
				<input-component type="InputDataType.String" name="targetValue" id="targetValue" placeholder="@localizer["TargetValue"]" value="@Model.UserlaneStepAction?.TargetValue" class="item__value"></input-component>
			</div>
		</config-section>
	</form-component>
</div>

<script>
	hasChanged = false;

	checkValues = () => {
		const actionType = document.getElementById('actionType').value;

		if (!hasChanged) {
			// Handle visibility on an initial load
			switch (actionType) {
				case 'Click':
					document.getElementById('triggerFromSection').style.display = "none";
					document.getElementById('targetValueFormSection').style.display = "none";
					document.getElementById('targetFormSection').style.display = "";
					break;
				case 'SetValue':
					document.getElementById('triggerFromSection').style.display = "none";
					document.getElementById('targetValueFormSection').style.display = "";
					document.getElementById('targetFormSection').style.display = "";
					break;
				case 'OpenListItem':
					document.getElementById('triggerFromSection').style.display = "none";
					document.getElementById('targetFormSection').style.display = "none";
					document.getElementById('targetValueFormSection').style.display = "";
					break;
				default:
					document.getElementById('triggerFromSection').style.display = "";
					document.getElementById('targetValueFormSection').style.display = "";
			}
			return;
		}

		// From this point, user has changed something, so clear fields as needed
		switch (actionType) {
			case 'Click':
				document.getElementById('triggerFromSection').style.display = "none";
				document.getElementById('trigger').value = null;
				document.getElementById('targetValueFormSection').style.display = "none";
				document.getElementById('targetValue').value = null;
				document.getElementById('targetFormSection').style.display = "";
				document.getElementById('target').value = null;
				break;
			case 'SetValue':
				document.getElementById('triggerFromSection').style.display = "none";
				document.getElementById('trigger').value = null;
				document.getElementById('targetValueFormSection').style.display = "";
				document.getElementById('targetFormSection').style.display = "";
				break;
			case 'OpenListItem':
				document.getElementById('triggerFromSection').style.display = "none";
				document.getElementById('trigger').value = null;
				document.getElementById('targetFormSection').style.display = "none";
				document.getElementById('target').value = null;
				document.getElementById('targetValueFormSection').style.display = "";
				break;
			default:
				document.getElementById('triggerFromSection').style.display = "";
				document.getElementById('targetValueFormSection').style.display = "";
		}
	};

	// Add a listener and set flag
	document.getElementById('actionType').addEventListener('change', () => {
		hasChanged = true;
		checkValues();
	});

	// Initial call on a page load
	checkValues();
</script>
