using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.UserlaneStepTestCondition;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.UserlaneStepTestCondition.ViewModels;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;

namespace Levelbuild.Frontend.WebApp.Features.UserlaneStepTestCondition.Controllers;

/// <inheritdoc />
public class UserlaneStepTestConditionController : AdminController<UserlaneStepTestConditionDto>
{
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="localizerFactory"></param>
	/// <param name="versionReader">injected VersionReader</param>
	/// <param name="contextFactory"></param>
	/// <param name="userManager"></param>
	public UserlaneStepTestConditionController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
										 IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : base(
		logManager, logManager.GetLoggerForClass<UserlaneStepTestConditionController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
	}
	
	#region Views

	/// <summary>
	/// Renders the list view with help of the list dto
	/// </summary>
	/// <returns>rendered list view</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Userlane/Step/{userlaneStepId}/userlaneStepTestConditions")]
	public IActionResult List(Guid userlaneStepId)
	{
		return CachedPartial() ?? RenderPartial(new UserlaneStepTestConditionList { UserlaneStepId = userlaneStepId });
	}

	/// <summary>
	/// Renders the view for the "CreateTestCondition" endpoint.
	/// </summary>
	/// <remarks>
	/// This action is mapped to the route:
	/// - "/Admin/Userlane/Step/{userlaneStepId}/userlaneStepTestCondition/Create"
	/// </remarks>
	/// <returns>A partial view rendering the required data.</returns>
	[HttpGet("/Admin/Userlane/Step/{userlaneStepId}/userlaneStepTestCondition/Create")]
	public IActionResult Create(Guid userlaneStepId)
	{
		List<GridViewFieldEntity> dataFields;
		List<AutocompleteOptionDefinition> dropDownOptions;
		
		// Find the UserlaneStep first and check if it exists
		var userlaneStep = DatabaseContext.UserlaneSteps.Find(userlaneStepId);
		if (userlaneStep == null)
		{
			return BadRequest($"UserlaneStep with ID {userlaneStepId} not found.");
		}
		
		var userlane = DatabaseContext.Userlanes.Find(userlaneStep.UserlaneId);
		if (userlane?.PageId != null && userlane.PageId != "custom")
		{
			var page = DatabaseContext.Pages.First(x=> x.Id == Guid.Parse(userlane.PageId));
			switch (page.Type)
			{

				case PageType.SingleData:
				case PageType.Create:
				case PageType.MultiData:
					// First, we collect the pages associated with the Multidata page
					var pages = DatabaseContext.MultiDataPages.First(x => x.Id == page.Id);

					// We collect the Views of the pages 
					var pageViews = DatabaseContext.PageViews.Where(pv => pv.PageId == pages.CreatePageId || pv.PageId == pages.DetailPageId).Select(pv => pv.Id).ToList();

					// We collect the GridView Section using the page ID
					var gridViewSection = DatabaseContext.GridViewSections.Where(grid => pageViews.Contains(grid.GridViewId)).Select(g => g.Id).ToList();

					// Use the grid view Section ID to collect the data
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false)
						.Where(pag => gridViewSection.Contains(pag.SectionId))
						.AsQueryable().ToList();
					
					break;
				default:
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
					break;
			}
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}else
		{
			dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}
		
		return RenderPartial(new UserlaneStepTestConditionForm() { UserlaneStepTestCondition = new UserlaneStepTestConditionDto
							 {
								 UserlaneStepId = userlaneStepId
							 },DataFields = dropDownOptions}, 
								  "Create", new UserlaneStepTestConditionForm { UserlaneStepId = userlaneStepId ,DataFields = dropDownOptions});
	}

	/// <summary>
	/// Returns just the basic settings partial for use in panels
	/// </summary>
	[HttpGet("/Admin/Userlane/Step/{userlaneStepId}/userlaneStepTestCondition/CreatePartial")]
	public IActionResult CreatePartial(Guid userlaneStepId)
	{
		List<GridViewFieldEntity> dataFields;
		List<AutocompleteOptionDefinition> dropDownOptions;
		
		// Find the UserlaneStep first and check if it exists
		var userlaneStep = DatabaseContext.UserlaneSteps.Find(userlaneStepId);
		if (userlaneStep == null)
		{
			return BadRequest($"UserlaneStep with ID {userlaneStepId} not found.");
		}
		
		var userlane = DatabaseContext.Userlanes.Find(userlaneStep.UserlaneId);
		if (userlane?.PageId != null && userlane.PageId != "custom")
		{
			var page = DatabaseContext.Pages.First(x=> x.Id == Guid.Parse(userlane.PageId));
			switch (page.Type)
			{

				case PageType.SingleData:
				case PageType.Create:
				case PageType.MultiData:
					// First, we collect the pages associated with the Multidata page
					var pages = DatabaseContext.MultiDataPages.First(x => x.Id == page.Id);

					// We collect the Views of the pages 
					var pageViews = DatabaseContext.PageViews.Where(pv => pv.PageId == pages.CreatePageId || pv.PageId == pages.DetailPageId).Select(pv => pv.Id).ToList();

					// We collect the GridView Section using the page ID
					var gridViewSection = DatabaseContext.GridViewSections.Where(grid => pageViews.Contains(grid.GridViewId)).Select(g => g.Id).ToList();

					// Use the grid view Section ID to collect the data
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false)
						.Where(pag => gridViewSection.Contains(pag.SectionId))
						.AsQueryable().ToList();
					
					break;
				default:
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
					break;
			}
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}else
		{
			dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}
		
		var model = new UserlaneStepTestConditionForm() { 
			UserlaneStepTestCondition = new UserlaneStepTestConditionDto
			{
				UserlaneStepId = userlaneStepId
			},
			DataFields = dropDownOptions,
			UserlaneStepId = userlaneStepId
		};
		
		return PartialView("_BasicSettings", model);
	}

	/// <summary>
	/// Renders the view for the "Details" endpoint.
	/// </summary>
	/// <param name="userlaneStepTestConditionId"></param>
	/// <param name="menu"></param>
	/// <returns></returns>
	[HttpGet("/Admin/Userlane/Step/TestCondition/{userlaneStepTestConditionId}/{menu?}")]
	public IActionResult Details(Guid userlaneStepTestConditionId, string? menu)
	{
		List<GridViewFieldEntity> dataFields;
		var userlaneStepTestCondition = DatabaseContext.UserlaneStepTestConditions.Find(userlaneStepTestConditionId);
		if (userlaneStepTestCondition == null)
		{
			return BadRequest($"UserlaneStepTestCondition with ID {userlaneStepTestConditionId} not found.");
		}
		var userlaneStepId = userlaneStepTestCondition.UserlaneStepId;
		List<AutocompleteOptionDefinition> dropDownOptions;
		
		var userlaneStep = DatabaseContext.UserlaneSteps.Find(userlaneStepId);
		if (userlaneStep == null)
		{
			return BadRequest($"UserlaneStep with ID {userlaneStepId} not found.");
		}
		
		var userlane = DatabaseContext.Userlanes.Find(userlaneStep.UserlaneId);
		if (userlane?.PageId != null && userlane.PageId != "custom")
		{
			var page = DatabaseContext.Pages.First(x=> x.Id == Guid.Parse(userlane.PageId));
			
			switch (page.Type)
			{

				case PageType.SingleData:
				case PageType.Create:
				case PageType.MultiData:
					// First, we collect the pages associated with the Multidata page
					var pages = DatabaseContext.MultiDataPages.First(x => x.Id == page.Id);

					// We collect the Views of the pages 
					var pageViews = DatabaseContext.PageViews.Where(pv => pv.PageId == pages.CreatePageId || pv.PageId == pages.DetailPageId).Select(pv => pv.Id).ToList();

					// We collect the GridView Section using the page ID
					var gridViewSection = DatabaseContext.GridViewSections.Where(grid => pageViews.Contains(grid.GridViewId)).Select(g => g.Id).ToList();

					// Use the grid view Section ID to collect the data
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false)
						.Where(pag => gridViewSection.Contains(pag.SectionId))
						.AsQueryable().ToList();
					
					break;
				default:
					dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
					break;
			}
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}
		else {
			dataFields = DatabaseContext.GridViewFields.Include(entity => entity.Section).Where(x => x.Readonly == false).AsQueryable().ToList();
			dropDownOptions = dataFields.Select(dataField =>
													new AutocompleteOptionDefinition(
														dataField.Id,
														(dataField.Section.Title ?? "").ToString()+" "+dataField.Label
													)
			).ToList();
		}

		if (userlaneStepTestConditionId == Guid.Empty) return RenderPartial(new UserlaneStepTestConditionForm(ViewType.Edit));
		{
			var userlaneStepTestConditionEntity = DatabaseContext.UserlaneStepTestConditions.First(x => x.Id == userlaneStepTestConditionId).ToDto();
			ViewData["targetMenu"] = menu ?? "BasicSettings";
			return RenderPartial(new UserlaneStepTestConditionForm(ViewType.Edit)
			{
				UserlaneStepTestCondition = userlaneStepTestConditionEntity,
				UserlaneStepId = userlaneStepTestConditionEntity.UserlaneStepId,
				DataFields = dropDownOptions
			});
		}
	}
	
	#endregion
	
	#region Actions

	/// <inheritdoc />
	[HttpGet("/Api/UserlaneStepTestCondition")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		var query = DatabaseContext.UserlaneStepTestConditions;
		return HandleQueryRequest<UserlaneStepTestConditionEntity, UserlaneStepTestConditionDto>(query, parameters);
	}

	/// <inheritdoc />
	[HttpGet("/Api/UserlaneStepTestCondition/{userlaneStepTestConditionId}")]
	public override ActionResult<FrontendResponse> Get(Guid userlaneStepTestConditionId)
	{
		return HandleGetRequest<UserlaneStepTestConditionEntity, UserlaneStepTestConditionDto>(DatabaseContext.UserlaneStepTestConditions, userlaneStepTestConditionId);
	}

	/// <inheritdoc />
	[HttpPost("/Api/UserlaneStepTestCondition")]
	public override Task<ActionResult<FrontendResponse>> Create([FromBody] UserlaneStepTestConditionDto userlaneStepTestConditionDto)
	{
		return HandleCreateRequestAsync(DatabaseContext.UserlaneStepTestConditions, userlaneStepTestConditionDto);
	}


	/// <summary>
	/// 
	/// </summary>
	/// <param name="userlaneStepTestConditionId"></param>
	/// <param name="dto"></param>
	/// <returns></returns>
	[HttpPatch("/Api/UserlaneStepTestCondition/{userlaneStepTestConditionId:guid}")]
	public override Task<ActionResult<FrontendResponse>> Update(Guid userlaneStepTestConditionId, UserlaneStepTestConditionDto dto)
	{
		return HandleUpdateRequestAsync(DatabaseContext.UserlaneStepTestConditions, userlaneStepTestConditionId, dto);
	}

	/// <inheritdoc />
	[HttpDelete("/Api/UserlaneStepTestCondition/{userlaneStepTestConditionId:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid userlaneStepTestConditionId)
	{
		return HandleDeleteRequest(DatabaseContext.UserlaneStepTestConditions, userlaneStepTestConditionId);
	}
	
	#endregion
}