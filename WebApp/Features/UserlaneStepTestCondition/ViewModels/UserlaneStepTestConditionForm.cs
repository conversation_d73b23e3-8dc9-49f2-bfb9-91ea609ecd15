using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;

#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.UserlaneStepTestCondition.ViewModels;

[ExcludeFromCodeCoverage]
public class UserlaneStepTestConditionForm(ViewType viewType = ViewType.Create)
{
	public ViewType ViewType { get; init; } = viewType;

	public UserlaneStepTestConditionDto? UserlaneStepTestCondition { get; init; }
	
	public List<UserlaneStepTestConditionType> UserlaneStepTestConditionType { get; } = Enum.GetValues(typeof(UserlaneStepTestConditionType))
		.Cast<UserlaneStepTestConditionType>()
		.ToList();
		
	public List<CompareOperator> CompareOperators { get; } = Enum.GetValues(typeof(CompareOperator))
		.Cast<CompareOperator>()
		.ToList();
	
	public Guid? UserlaneStepId { get; set; }
	
	public List<AutocompleteOptionDefinition> DataFields { get; set; } = [];
}