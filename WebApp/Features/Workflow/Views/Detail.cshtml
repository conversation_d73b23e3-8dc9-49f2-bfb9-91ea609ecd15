@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminDetailPage
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@model Levelbuild.Frontend.WebApp.Features.Workflow.ViewModels.WorkflowForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("Workflow", "");
	List<MenuItem> menuItems =
	[
		new("basic", "BasicSettings", "screwdriver-wrench"),
		new("nodes", "WorkflowNodes", "puzzle-piece"),
	];

	List<MenuInfo> menuInfos =
	[
		MenuInfo.GetLinkInstance("info-dataStore-link", "dataStore", Model.Workflow?.DataSource?.DataStore?.Name ?? "", $"/Admin/DataStores/{Model.Workflow?.DataSource?.DataStore?.Slug}"),
		MenuInfo.GetLinkInstance("info-dataSource-link", "dataSource", Model.Workflow?.DataSource?.Name ?? "", $"/Admin/DataSources/{Model.Workflow?.DataSource?.Slug}"),
		/*MenuInfo.GetTextInstance("info-createdBy", "createdBy", Model.Workflow?.DataSource?.CreatedBy??"", InputDataType.String,Model.Workflow?.DataSource?.Created?.ToString("o"), InputDataType.DateTime),
		MenuInfo.GetTextInstance("info-lastModifiedBy", "lastModifiedBy", Model.Workflow?.DataSource?.LastModifiedBy??"", InputDataType.String,Model.Workflow?.DataSource?.LastModified?.ToString("o"), InputDataType.DateTime)*/
	];

	string currentMenu = ViewData["targetMenu"] == null ? "BasicSettings" : ViewData["targetMenu"]!.ToString()!;
}
<script type="module" defer>
	@if (Model.Workflow != null)
	{
		<text>
			Page.setMainPage(`/Admin/DataStores/@(Model.Workflow?.DataSource?.DataStore?.Slug)/DataSources/@(Model.Workflow?.DataSource?.Slug)/Workflows/@(Model.Workflow?.Slug)`, '@(currentMenu)')
			Page.setBreadcrumbs([
				{ label: '@localizer["list/pageTitle"]', url: `/Admin/DataStores/@(Model.Workflow?.DataSource!.DataStore?.Slug)/DataSources` },
				{ label: '@(Model.Workflow?.DataSource?.Name)', url: `/Admin/DataStores/@(Model.Workflow?.DataSource!.DataStore?.Slug)/DataSources/@(Model.Workflow?.DataSource?.Slug)` },
				{ label: '@(Model.Workflow?.Name)', url: `/Admin/DataStores/@(Model.Workflow?.DataSource!.DataStore?.Slug)/DataSources/@(Model.Workflow?.DataSource?.Slug)/Workflows/@(Model.Workflow?.Slug)` }
			], true)
		</text>
	}
</script>
<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="Workflow" route-name="Workflows" menu-items="@menuItems" menu-infos="@menuInfos" skeleton="@(Model.Workflow == null)" width="250"></vc:basic-menu>
<vc:admin-detail-page model="@Model" entity="Workflow" route-name="Workflows" title="@(Model.Workflow?.Name)" menu-item="@currentMenu" show-default-buttons="@(Model.Workflow != null)"></vc:admin-detail-page>
