@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.Workflow.ViewModels.WorkflowForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("Workflow", "");
	var slotLocalizer = LocalizerFactory.Create("WorkflowSlot", "");
}
<div class="grid--centered vanishing-scrollbar static-scrollbar">
	<form-component id="workflow-form" class="form" skeleton="@(Model is { Workflow: null })">
		<config-section label="@localizer["sectionInfo"]">
			<div class="form__item">
				<input type="hidden" class="item__value" id="workflow-id" name="id" value="@(Model.Workflow?.Id)"/>
			</div>
			<div class="form__item">
				<input type="hidden" class="item__value" name="dataSourceId" value="@(Model.Workflow?.DataSourceId)"/>
			</div>
			<div class="form__item">
				<config-label target="workflow-name" label="@localizer["name"]"></config-label>
				<input-component id="workflow-name" name="name" class="item__value" value="@(Model.Workflow?.Name)" type="InputDataType.Translation" translation-prefix="/Workflow/@(Model.Workflow != null ? Model.Workflow.Id.ToString() : "##id##")" required></input-component>
			</div>
			<div class="form__item">
				<config-label target="workflow-comment" label="@localizer["comment"]"></config-label>
				<textarea-component id="workflow-comment" name="comment" class="item__value" placeholder="@localizer["commentPlaceholder"]" value="@(Model.Workflow?.Comment)"></textarea-component>
			</div>
			<div class="form__item">
				<config-label target="workflow-slot" label="@localizer["slot"]"></config-label>
				<button-group-component class="item__value" id="workflow-slot" name="slot" value="@(Model.Workflow?.Slot ?? WorkflowSlot.First)">
					<button-component label="@slotLocalizer[WorkflowSlot.First.ToString()]" value="@(Model.Slots[0])"></button-component>
					<button-component label="@slotLocalizer[WorkflowSlot.Second.ToString()]" value="@(Model.Slots[1])"></button-component>
					<button-component label="@slotLocalizer[WorkflowSlot.Third.ToString()]" value="@(Model.Slots[2])"></button-component>
				</button-group-component>
			</div>
			@{
				List<AutocompleteOptionDefinition> fieldOptions = Model.Workflow?.DataSource?.Fields?.Select(field => new AutocompleteOptionDefinition(field.Id, field.Name!)).ToList()!;
			}
			<div class="form__item">
				<config-label target="workflow-status-field" label="@localizer["statusField"]" description="@localizer["statusFieldDescription"]"></config-label>
				<autocomplete-component
					id="workflow-status-field"
					name="statusField"
					output-name="statusFieldId"
					class="item__value"
					readonly="true"
					placeholder="@localizer["pleaseChoose"]"
					options="fieldOptions"
					value="@(Model.Workflow?.StatusFieldId)">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="workflow-recipients-field" label="@localizer["recipientsField"]" description="@localizer["recipientsFieldDescription"]"></config-label>
				<autocomplete-component
					id="workflow-recipients-field"
					name="recipientsField"
					output-name="recipientsFieldId"
					class="item__value"
					readonly="true"
					placeholder="@localizer["pleaseChoose"]"
					options="fieldOptions"
					value="@(Model.Workflow?.RecipientsFieldId)">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="workflow-enabled" label="@localizer["enabled"]"></config-label>
				<toggle-component id="workflow-enabled" name="enabled" class="item__value" value="true"></toggle-component>
			</div>
		</config-section>
	</form-component>
</div>