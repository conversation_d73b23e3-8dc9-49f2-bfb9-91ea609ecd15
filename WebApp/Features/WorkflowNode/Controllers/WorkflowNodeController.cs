using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Workflow;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Features.Workflow.ViewModels;
using Levelbuild.Frontend.WebApp.Features.WorkflowNode.ViewModels;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.WorkflowNode.Controllers;

/// <summary>
/// Controller for WorkflowNode configuration
/// </summary>
public class WorkflowNodeController : AdminController<WorkflowNodeDto>
{
	/// <inheritdoc />
	public WorkflowNodeController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory stringLocalizerFactory, IVersionReader versionReader) : base(logManager, logManager.GetLoggerForClass<WorkflowNodeController>(), contextFactory, userManager, stringLocalizerFactory, versionReader)
	{
	}

	#region Views

	/// <summary>
	/// Renders the detail view with help of the workflow node dto
	/// </summary>
	/// <param name="dataStoreSlug">readable identifier for a specific data store</param>
	/// <param name="dataSourceSlug">readable identifier for a specific data source</param>
	/// <param name="workflowSlug">readable identifier for a specific workflow</param>
	/// <param name="slug">readable identifier for a specific workflow</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/WorkflowNodes/Edit/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/DataSources/{dataSourceSlug?}/Workflows/{workflowSlug?}/WorkflowNodes/{slug}/{menu?}")]
	public IActionResult Detail(string? dataStoreSlug, string? dataSourceSlug, string? workflowSlug, string? slug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(dataSourceSlug) || string.IsNullOrEmpty(workflowSlug) || string.IsNullOrEmpty(slug))
		{
			return CachedPartial() ?? RenderPartial(new WorkflowNodeForm(ViewType.Edit));
		}
		
		var dataStore = DatabaseContext.DataStoreConfigs
			.FirstOrDefault(dataStore => dataStore.Slug == dataStoreSlug);
		if (dataStore == null)
			throw new ElementNotFoundException($"DataStore configuration with slug '{dataStoreSlug}' could not be found");
		
		var dataSource = DatabaseContext.DataSources
			.FirstOrDefault(dataSource => dataSource.DataStoreId == dataStore.Id && 
										  dataSource.Slug == dataSourceSlug);
		if (dataSource == null)
			throw new ElementNotFoundException($"DataSource configuration with slug '{dataSourceSlug}' could not be found");
		
		var workflow = DatabaseContext.Workflows
			.FirstOrDefault(workflow => workflow.DataSourceId == dataSource.Id && 
										workflow.Slug == workflowSlug);
		if (workflow == null)
			throw new ElementNotFoundException($"Views with slug '{workflowSlug}' could not be found in data source {dataSource.Name}");

		var node = DatabaseContext.WorkflowNodes
			.FirstOrDefault(node => node.WorkflowId == workflow.Id && 
									node.Slug == slug);
		if (node == null)
			throw new ElementNotFoundException($"Views node with slug '{slug}' could not be found in workflow {workflow.Name}");
		
		Request.RouteValues.Add("menu", "WorkflowNodes");
		return RenderPartial(new WorkflowNodeForm(ViewType.Edit)
		{
			WorkflowId = workflow.Id,
			WorkflowNode = node.ToDto(),
			Customers = DatabaseContext.Customers.Where(customer => customer.Enabled).ToDtoList()
		},
		"~/Features/Workflow/Views/Detail.cshtml",
		new WorkflowForm(ViewType.Edit)
		{
			Workflow = workflow.ToDto()
		}
		);
	}

	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/WorkflowNodes/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/DataSources/{dataSourceSlug?}/Workflows/{workflowSlug?}/WorkflowNodes/Create")]
	public IActionResult Create(string? dataStoreSlug, string? dataSourceSlug, string? workflowSlug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(dataSourceSlug) || string.IsNullOrEmpty(workflowSlug))
		{
			return CachedPartial() ?? RenderPartial(new WorkflowNodeForm()
			{
				WorkflowNode = new WorkflowNodeDto(),
				Customers = DatabaseContext.Customers.Where(customer => customer.Enabled).ToDtoList()
			});
		}

		var dataStore = DatabaseContext.DataStoreConfigs
			.FirstOrDefault(dataStore => dataStore.Slug == dataStoreSlug);
		if (dataStore == null)
			throw new ElementNotFoundException($"DataStore configuration with slug '{dataStoreSlug}' could not be found");
		
		var dataSource = DatabaseContext.DataSources
			.FirstOrDefault(dataSource => dataSource.DataStoreId == dataStore.Id && 
										  dataSource.Slug == dataSourceSlug);
		if (dataSource == null)
			throw new ElementNotFoundException($"DataSource configuration with slug '{dataSourceSlug}' could not be found");
		
		var workflow = DatabaseContext.Workflows
			.FirstOrDefault(workflow => workflow.DataSourceId == dataSource.Id && 
										workflow.Slug == workflowSlug);
		if (workflow == null)
			throw new ElementNotFoundException($"Views with slug '{workflowSlug}' could not be found in data source {dataSource.Name}");

		Request.RouteValues.Add("menu", "WorkflowNodes");
		return CachedPartial() ?? RenderPartial(new WorkflowNodeForm()
		{
			WorkflowId = workflow.Id,
			WorkflowNode = new WorkflowNodeDto() { Workflow = workflow.ToDto() }, 
			Customers = DatabaseContext.Customers.Where(customer => customer.Enabled).ToDtoList()
		},
		"~/Features/Workflow/Views/Detail.cshtml",
		new WorkflowForm(ViewType.Edit)
		{
			Workflow = workflow.ToDto()
		});
	}

	#endregion
	
	#region Endpoints
	
	/// <inheritdoc />
	[HttpGet("/Api/WorkflowNodes")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		return HandleQueryRequest<WorkflowNodeEntity, WorkflowNodeDto>(DatabaseContext.WorkflowNodes, parameters);
	}

	/// <inheritdoc />
	[HttpGet("/Api/WorkflowNodes/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<WorkflowNodeEntity, WorkflowNodeDto>(DatabaseContext.WorkflowNodes, id);
	}

	/// <inheritdoc />
	[HttpPost("/Api/WorkflowNodes")]
	public override async Task<ActionResult<FrontendResponse>> Create(WorkflowNodeDto dto)
	{
		if (dto.State == WorkflowNodeState.Start)
			return GetBadRequestResponse("Creating another start node is not allowed!");
		
		var existingNode = await DatabaseContext.WorkflowNodes.FirstOrDefaultAsync(node => node.WorkflowId == dto.WorkflowId && EF.Functions.Like(node.Name, dto.Name));
		if (existingNode != null)
			return GetBadRequestResponse($"Node with name '{dto.Name}' already exists in this workflow!");
		
		// Sorting = 0? -> Assume that automatic sorting is desired
		if (dto.Sorting == 0)
		{
			var highestSortingValue = DatabaseContext.WorkflowNodes
				.Where(node => node.WorkflowId == dto.WorkflowId)
				.OrderByDescending(node => node.Sorting)
				.Select(node => node.Sorting)
				.First();
			dto.Sorting = ++highestSortingValue;
		}
		
		return await HandleCreateRequestAsync(DatabaseContext.WorkflowNodes, dto, CreateAction);
		
		void CreateAction(WorkflowNodeEntity entity)
		{
			var workflow = DatabaseContext.Workflows
				.Include(workflow => workflow.DataSource)
				.First(workflow => workflow.Id == entity.WorkflowId);
			
			workflow.DataSource.Touch();
		}
	}

	/// <inheritdoc />
	[HttpPatch("/Api/WorkflowNodes/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, WorkflowNodeDto dto)
	{
		var existingNode = await DatabaseContext.WorkflowNodes.FindAsync(id);
		if (existingNode == null)
			return GetNotFoundResponse($"{nameof(WorkflowNodeEntity)}#{id} does not exist");
		
		if(existingNode.State == WorkflowNodeState.Start && dto.State != WorkflowNodeState.Start)
			return GetBadRequestResponse("Changing the state of the start node is not allowed!");
		
		if (dto.State == WorkflowNodeState.Start && existingNode.State != WorkflowNodeState.Start)
			return GetBadRequestResponse("Creating another start node is not allowed!");
		
		return await HandleUpdateRequestAsync(DatabaseContext.WorkflowNodes, id, dto, UpdateAction);
		
		void UpdateAction(WorkflowNodeEntity entity)
		{
			var workflow = DatabaseContext.Workflows
				.Include(workflow => workflow.DataSource)
				.First(workflow => workflow.Id == entity.WorkflowId);
			
			workflow.DataSource.Touch();
		}
	}

	/// <inheritdoc />
	[HttpDelete("/Api/WorkflowNodes/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		var node = DatabaseContext.WorkflowNodes.Find(id);
		if (node?.State == WorkflowNodeState.Start)
			return GetBadRequestResponse("Deleting the start node is not allowed!");
		
		return HandleDeleteRequest(DatabaseContext.WorkflowNodes, id, DeleteAction);
		
		void DeleteAction(WorkflowNodeEntity entity)
		{
			var workflow = DatabaseContext.Workflows
				.Include(workflow => workflow.DataSource)
				.First(workflow => workflow.Id == entity.WorkflowId);
			
			workflow.DataSource.Touch();
		}
	}
	
	#endregion
}