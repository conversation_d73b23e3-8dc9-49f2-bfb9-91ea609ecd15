@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.WorkflowNode.ViewModels.WorkflowNodeForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("WorkflowNode", "");
	var stateLocalizer = LocalizerFactory.Create("WorkflowNodeState", "");
}
<script type="module" defer>
	const form = document.getElementById('workflow-node-@(Model.ViewType == ViewType.Edit ? "edit" : "create")-form')
	const nodeList = document.getElementById('workflow-node-list')
	const deleteButton = document.getElementById('workflow-node-delete-button')
	deleteButton?.addEventListener('click', () => Form.handleDeleteButtonClick(form, '/Api/WorkflowNodes/', nodeList, true), { signal: Page.getPageChangeSignal() })
</script>
<form-component id="workflow-node-@(Model.ViewType == ViewType.Edit ? "edit" : "create")-form" class="form" skeleton="@(Model is { ViewType: ViewType.Edit, WorkflowNode: null })">
	<config-section label="@localizer["sectionInfo"]">
		<div class="form__item">
			<input type="hidden" class="item__value" id="workflow-node-id" name="id" value="@(Model.WorkflowNode?.Id)"/>
		</div>
		<div class="form__item">
			<input type="hidden" class="item__value" id="workflow-id" name="workflowId" value="@(Model.WorkflowNode?.WorkflowId)"/>
		</div>
		<div class="form__item">
			<config-label target="workflow-node-name" label="@localizer["name"]"></config-label>
			<input-component id="workflow-node-name" name="name" class="item__value" value="@(Model.WorkflowNode?.Name)" type="InputDataType.Translation" translation-prefix="/Workflow/@(Model.WorkflowId != null ? Model.WorkflowId.ToString() : "##workflowId##")/" required></input-component>
		</div>
		@*<div class="form__item">
			<config-label target="workflow-node-icon" label="@localizer["icon"]"></config-label>
			<input-component id="workflow-node-icon" name="icon" type="InputDataType.Icon" class="item__value" value="@(Model.WorkflowNode?.Icon)"></input-component>
		</div>*@
		<div class="form__item">
			<config-label target="workflow-node-state" label="@localizer["state"]"></config-label>
			<button-group-component class="item__value" id="workflow-node-state" name="state" value="@(Model.WorkflowNode?.State ?? WorkflowNodeState.InProgress)" readonly="@(Model.WorkflowNode?.State == WorkflowNodeState.Start)">
				<button-component tooltip="@stateLocalizer[WorkflowNodeState.Start + "Tooltip"]" label="@stateLocalizer[nameof(WorkflowNodeState.Start)]" value="@(Model.States[0])" disabled="true"></button-component>
				<button-component tooltip="@stateLocalizer[WorkflowNodeState.InProgress + "Tooltip"]" label="@stateLocalizer[nameof(WorkflowNodeState.InProgress)]" value="@(Model.States[1])"></button-component>
				<button-component tooltip="@stateLocalizer[WorkflowNodeState.Positive + "Tooltip"]" label="@stateLocalizer[nameof(WorkflowNodeState.Positive)]" value="@(Model.States[2])"></button-component>
				<button-component tooltip="@stateLocalizer[WorkflowNodeState.Negative + "Tooltip"]" label="@stateLocalizer[nameof(WorkflowNodeState.Negative)]" value="@(Model.States[3])"></button-component>
			</button-group-component>
		</div>
		<div class="form__item">
			<config-label target="workflow-node-sorting" label="@localizer["sorting"]"></config-label>
			<input-component id="workflow-node-sorting" name="sorting" type="InputDataType.Integer" class="item__value" value="@(Model.WorkflowNode?.Sorting)" required></input-component>
		</div>
		@{
			List<AutocompleteOptionDefinition> customerOptions = Model.Customers?.Select(field => new AutocompleteOptionDefinition(field.Id, field.DisplayName)).ToList() ?? new List<AutocompleteOptionDefinition>();
		}
		<div class="form__item">
			<config-label target="workflow-customer" label="@localizer["customer"]" description="@localizer["customerDescription"]"></config-label>
			<autocomplete-component
				id="workflow-customer"
				name="customer"
				output-name="customerId"
				class="item__value"
				placeholder="@localizer["pleaseChoose"]"
				options="customerOptions"
				value="@(Model.WorkflowNode?.CustomerId)">
			</autocomplete-component>
		</div>
	</config-section>
</form-component>
@if (Model.WorkflowNode?.State != WorkflowNodeState.Start && Model.ViewType == ViewType.Edit)
{
	<button-component id="workflow-node-delete-button" data-action="delete" label="@localizer["deleteButton"]" icon="trash" type="ButtonType.Secondary" color="ColorState.Error"></button-component>
}
@if (Model.WorkflowNode == null)
{
	<script type="module" defer>
		// Set state selection to readonly if node is the start node!
		const editForm = document.getElementById('workflow-node-@(Model.ViewType == ViewType.Edit ? "edit" : "create")-form')
		const stateButtonGroup = editForm.querySelector('#workflow-node-state')
		const state = stateButtonGroup.value
		if (state.toLowerCase() === "start") {
			stateButtonGroup.setAttribute('readonly', '')
			editForm.parentElement.querySelector('#workflow-node-delete-button').classList.add('hide')
		}
	</script>
}
