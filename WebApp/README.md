# Readme

This readme explains how Typescript files can be used, where they are located and how to work with them in `DEV`.

# Frontend

The pure frontend area can be found in the subfolder: `Client` and is divided as follows:
```ts
- `Client`
	- `public`				// folder for all css, assets and 3rd party libs which are not in node_modules
	- `src`					// Home of all ts files
		- `features`		// single script files which are only useful for a specific issue
		- `scripts`			// commands, events and intitialisations which have to be executed immediately after web app is opened in browser
		- `services`		// singleton class instances which are set on the window object. Instances containing functions which are usefull for different pages and features
	- `main.ts`				// Index File which is the entry point and linked in <head>
```

### features
All functions within **features** can be used via normal import statement in .cshtml-Files: 
<br>``import { FUNC } from '@AssetService.SolvePath("/PATH/FILENAME.ts")'``
<br>The root/base of the passed path is the feature folder `/Client/src/features`. Unfortunately, these functions and the path are not highlighted by Rider.

**Note:** The *IAssetService* has to be injected.

### scripts
Its script code you want to execute at the start. Nothing special.

### services
Every **service** should be a class which is exported and put to the window object.
```ts
class RandomService {
	// ... your functions ...
	rand(){ 
		// ... some code ...
	}
}

declare global {
	interface Window {
		Component: ComponentService
	}
}

// create singleton
const Random = new RandomService()

// pass it to the window object
window.Random = Random
export default Random
```

It can be used in all .cshtml files with `window.Random` or just `Random` to call their functions: `Random.rand()`.
To use it in other ts files, you can import them.

## How to install
Install Node.js and npm package manager like in the readme of the WebComponents described (See: [Readme.md](WebAppComponents/README.md))
and run: WebApp:http.  






