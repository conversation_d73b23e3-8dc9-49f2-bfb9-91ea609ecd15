using Microsoft.AspNetCore.Mvc.Routing;

namespace Levelbuild.Frontend.WebApp.Shared.Attributes;

/// <summary>
/// Source: https://www.c-sharpcorner.com/article/expanding-razor-view-location-and-sub-areas-in-asp-net-core/
/// 
/// Similar to: https://github.com/aspnet/Mvc/blob/dev/src/Microsoft.AspNetCore.Mvc.Core/AreaAttribute.cs  
/// </summary>  
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false, Inherited = true)]  
public class FeatureAttribute : RouteValueAttribute  
{  
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="featureName"></param>
	/// <exception cref="ArgumentException"></exception>
	public FeatureAttribute(string featureName) : base("feature", featureName)  
	{  
		if (string.IsNullOrEmpty(featureName))  
		{  
			throw new ArgumentException("Feature name cannot be null or empty", nameof(featureName));  
		}  
	}  
}  