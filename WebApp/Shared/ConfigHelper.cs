using System.Text.Json;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Levelbuild.Frontend.WebApp.Shared;

/// <summary>
/// static helper class which hosts some static configuration objects which are used in multiple places and shouldn't be instantiated over and over again
/// </summary>
public static class ConfigHelper
{
	/// <summary>
	/// JsonSerializerOptions for KebabCaseLower formating
	/// </summary>
	public static readonly JsonSerializerOptions DefaultJsonOptions = new()
	{
		PropertyNamingPolicy = JsonNamingPolicy.KebabCaseLower
	};

	/// <summary>
	/// JsonSerializerOptions for CamelCase formating
	/// </summary>
	public static readonly JsonSerializerOptions JsonOptionsCamel = new()
	{
		PropertyNamingPolicy = JsonNamingPolicy.CamelCase
	};

	/// <summary>
	///	JsonSerializerSettings for CamelCase formating
	/// </summary>
	public static JsonSerializerSettings JsonSettingsCamel = new()
	{
		ContractResolver = new CamelCasePropertyNamesContractResolver()
	};
}