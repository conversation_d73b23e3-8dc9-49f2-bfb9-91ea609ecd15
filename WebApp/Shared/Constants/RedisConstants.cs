namespace Levelbuild.Frontend.WebApp.Shared.Constants;

/// <summary>
/// Class containing constants belonging to Redis.
/// </summary>
public static class RedisConstants
{
	/// <summary>
	/// Service key for master redis connection.
	/// </summary>
	public const string RedisWriteConnection = "WriteConnection";
	
	/// <summary>
	/// Service key for read-only redis connection (to Replicas).
	/// </summary>
	public const string RedisReadConnection = "ReadConnection";
	
	/// <summary>
	/// The default database to use from WebApp.
	///
	/// Set to 1 to avoid multiple db instances when using multiple WebApp instances.
	/// </summary>
	public const int WebAppDatabase = 1;
	
	/// <summary>
	/// The redis key holding user impersonation info.
	/// </summary>
	public const string UserImpersonationKey = "auth_impersonation";
	
	/// <summary>
	/// The redis key holding the info if the user accessed the thumbnail before.
	/// </summary>
	public const string UserThumbnailCachingKey = "auth_thumbnail";
	
	/// <summary>
	/// The redis key holding the info if the user accessed the deep zoom image before.
	/// </summary>
	public const string UserDeepZoomCachingKey = "auth_deepZoom";
	
	/// <summary>
	/// The redis key holding the date when the FileCachingServiceCleanup last executed.
	/// </summary>
	public const string LastCleanupDateKey = "cleanup_date_queue";
}
