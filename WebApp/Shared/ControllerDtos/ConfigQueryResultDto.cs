using Levelbuild.Core.FrontendDtos.Shared;

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Default result dto of lists in admin ui
/// </summary>
/// <typeparam name="TEntityDto"></typeparam>
public class ConfigQueryResultDto<TEntityDto> : IResponseObject where TEntityDto : EntityDto
{
	/// <summary>
	/// List of records
	/// </summary>
	public IList<TEntityDto> Rows { get; init; } = [];

	/// <summary>
	/// Maximum number of records without limit and offset
	/// </summary>
	public int CountTotal { get; init; }
}