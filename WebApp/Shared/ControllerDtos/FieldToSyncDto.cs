using Levelbuild.Core.FrontendDtos.Shared;

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Response Object containing data of a field for syncing purposes
/// </summary>
public class FieldToSyncDto  : IResponseObject
{
	/// <summary>
	/// ID of the field
	/// </summary>
	public Guid Id { get; set; }
	
	/// <summary>
	/// Name of the field
	/// </summary>
	public string? Name { get; set; }
	
	/// <summary>
	/// Type of the field
	/// </summary>
	public string? Type { get; set; }
}