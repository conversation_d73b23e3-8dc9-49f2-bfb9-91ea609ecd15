using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Frontend.WebApp.Features.Viewer.Email.Dtos;

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// DTO for all File related data
/// </summary>
public class FileMetaDataDto : IResponseObject
{
	/// <summary>
	/// id of the File
	/// </summary>
	public string? FileId { get; set; }
	
	/// <summary>
	/// Meta Data for Emails
	/// </summary>
	public EmailDto? MailData { get; set; }
}