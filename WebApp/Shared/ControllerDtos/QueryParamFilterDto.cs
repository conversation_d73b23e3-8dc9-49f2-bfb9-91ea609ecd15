using System.Text.Json;
using System.Text.Json.Serialization;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.Extensions;

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Filter Dto which is coming from Frontend Http Requests
/// </summary>
public class QueryParamFilterDto : IResponseObject
{
	/// <summary>
	/// Field to filter by 
	/// </summary>
	public string FilterColumn { get; init; } = "";

	/// <summary>
	/// How to compare Value and Field? Most likely with equals =
	/// </summary>
	public QueryParamFilterOperator Operator { get; init; }

	/// <summary>
	/// The Value which is compared with the filter field
	/// </summary>
	[JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
	public object? CompareValue { get; set; }

	/// <summary>
	/// Human-readable representation for the compare value
	/// </summary>
	[JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
	public string? CompareLabel { get; init; }

	/// <summary>
	/// List of compare values for multi data
	/// </summary>
	[JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
	public QueryDataItemDto[]? CompareList { get; init; }

	/// <summary>
	/// String output for using in logs
	/// </summary>
	/// <returns></returns>
	public override string ToString()
	{
		return $"{FilterColumn} {Operator.ToString()} {CompareValue}";
	}

	/// <summary>
	/// Transforms this class instance in a QueryFilter instance. Used to pass it to the datastore
	/// </summary>
	/// <returns></returns>
	public QueryFilterGroup AddToQueryFilterGroup(QueryFilterGroup queryFilterGroup, DataType? fieldType = null)
	{
		var filterField = new QueryFilterField(FilterColumn);
		var value = (CompareValue is JsonElement element ? element.GetRealValue() : CompareValue) ?? string.Empty;

		// build new filter for date(times) fields with compare values of dateSpanType
		if (fieldType is DataType.DateTime or DataType.Date && value is string && Enum.TryParse(value.ToString(), true, out DateSpanType dateSpanType))
			return queryFilterGroup.AddFilterGroup(dateSpanType.GetQueryFilterGroup(filterField));

		switch (Operator)
		{
			case QueryParamFilterOperator.Equals:
				queryFilterGroup.AddFilter(new EqualsFilter(filterField, value));
				break;
			case QueryParamFilterOperator.NotEquals:
				queryFilterGroup.AddFilter(new NotEqualsFilter(filterField, value));
				break;
			case QueryParamFilterOperator.Like:
				queryFilterGroup.AddFilter(new LikeFilter(filterField, value));
				break;
			case QueryParamFilterOperator.NotLike:
				queryFilterGroup.AddFilter(new NotLikeFilter(filterField, value));
				break;
			case QueryParamFilterOperator.GreaterThan:
				queryFilterGroup.AddFilter(new GreaterThanFilter(filterField, value));
				break;
			case QueryParamFilterOperator.GreaterThanEquals:
				queryFilterGroup.AddFilter(new GreaterThanEqualsFilter(filterField, value));
				break;
			case QueryParamFilterOperator.LessThan:
				queryFilterGroup.AddFilter(new LessThanFilter(filterField, value));
				break;
			case QueryParamFilterOperator.LessThanEquals:
				queryFilterGroup.AddFilter(new LessThanEqualsFilter(filterField, value));
				break;
			case QueryParamFilterOperator.In:
				queryFilterGroup.AddFilter(new InFilter(filterField, value));
				break;
			case QueryParamFilterOperator.NotIn:
				queryFilterGroup.AddFilter(new NotInFilter(filterField, value));
				break;
			case QueryParamFilterOperator.IsNull:
				if (fieldType is DataType.String or DataType.Text)
				{
					queryFilterGroup.AddFilterGroup(new QueryFilterGroup([
						new IsNullFilter(filterField),
						new EqualsFilter(filterField, string.Empty)
					], QueryFilterLinkType.Or));
				}
				else
				{
					queryFilterGroup.AddFilter(new IsNullFilter(filterField));
				}

				break;
			case QueryParamFilterOperator.IsNotNull:
				if (fieldType is DataType.String or DataType.Text)
				{
					queryFilterGroup.AddFilterGroup(new QueryFilterGroup([
						new NotNullFilter(filterField),
						new NotEqualsFilter(filterField, string.Empty)
					]));
				}
				else
				{
					queryFilterGroup.AddFilter(new NotNullFilter(filterField));
				}

				break;
			case QueryParamFilterOperator.Contains:
				queryFilterGroup.AddFilter(new InFilter(CompareList == null ? value : GetCompareItemsAsList(), filterField));
				break;
			case QueryParamFilterOperator.NotContains:
				queryFilterGroup.AddFilter(new InFilter(CompareList == null ? value : GetCompareItemsAsList(), filterField));
				break;
			case QueryParamFilterOperator.FulltextSearch:
				if (!string.IsNullOrEmpty(value.ToString()))
					queryFilterGroup.AddFilter(new FulltextSearchFilter(value.ToString()!));
				break;
			case QueryParamFilterOperator.Favorite:
				queryFilterGroup.AddFilter(new FavouriteFilter(true));
				break;
			case QueryParamFilterOperator.Inactive:
				filterField = new QueryFilterField("SysInactiveDate");
				queryFilterGroup.AddFilterGroup(new QueryFilterGroup([
					new IsNullFilter(filterField),
					new NotNullFilter(filterField),
				], QueryFilterLinkType.Or));
				break;
		}

		return queryFilterGroup;
	}

	private List<object> GetCompareItemsAsList()
	{
		return CompareList == null ? [] : CompareList.Where(item => item.Value != null).Select(item => item.Value!).ToList();
	}

	/// <summary>
	/// converts a DataFieldFilterDto to a QueryParamFilterDto
	/// </summary>
	/// <param name="dataFieldFilter">DataFieldFilterDto to convert</param>
	/// <returns></returns>
	public static QueryParamFilterDto FromDataFieldFilter(DataFieldFilterDto dataFieldFilter)
	{
		return new QueryParamFilterDto
		{
			FilterColumn = dataFieldFilter.FilterFieldName ?? "",
			Operator = ParseFilterOperator(dataFieldFilter.Operator),
			CompareValue = dataFieldFilter.CompareValue
		};
	}

	private static QueryParamFilterOperator ParseFilterOperator(CompareOperator? compareOperator)
	{
		if (compareOperator is null)
			return QueryParamFilterOperator.Equals;

		Enum.TryParse(Enum.GetName(typeof(CompareOperator), compareOperator), true, out QueryParamFilterOperator filterOperator);
		return filterOperator;
	}
}