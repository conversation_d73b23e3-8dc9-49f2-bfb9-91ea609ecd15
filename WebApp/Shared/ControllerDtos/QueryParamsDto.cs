using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Dto to select specific DataStoreConfig items
/// </summary>
public class QueryParamsDto : IResponseObject
{
	/// <summary>
	/// Fields that needs to retrieve
	/// </summary>
	[FromJsonQuery(Name = "fields")]
	public IList<string>? Fields { get; set; }
	
	/// <summary>
	/// Maximum number of data records to be retrieved from the server
	/// </summary>
	[FromQuery(Name = "limit")]
	public int Limit { get; set; }

	/// <summary>
	/// Dictates the number of data records to skip from the beginning of the returned data before presenting the results
	/// </summary>
	[FromQuery(Name = "offset")]
	public int Offset { get; set; }

	/// <summary>
	/// Search term
	/// </summary>
	[FromQuery(Name = "term")]
	public string? Term { get; set; }
	
	/// <summary>
	/// Group by one column
	/// </summary>
	[FromQuery(Name = "groupBy")]
	public string? GroupBy { get; set; }

	/// <summary>
	/// Should the backend filter by favorite only records?
	/// </summary>
	public bool FavoritesOnly { get; set; } = false;

	/// <summary>
	/// Should the backend also include inactive records?
	/// </summary>
	public bool IncludeInactives { get; set; } = false;

	/// <summary>
	/// Ordering by columns
	/// </summary>
	[FromJsonQuery(Name = "sortings")]
	public IList<QueryParamSortingDto>? Sortings { get; set; }

	/// <summary>
	/// A list of 'where' conditions
	/// </summary>
	[FromJsonQuery(Name = "filters")]
	public IList<QueryParamFilterDto>? Filters { get; set; }
	
	/// <summary>
	/// Adds file infos per record if a file exists
	/// </summary>
	[FromQuery(Name = "includeFileInfo")]
	public bool IncludeFileInfo { get; set; } 
}