using Levelbuild.Core.FrontendDtos.Shared;

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Response Object containing data of a field for syncing purposes
/// </summary>
public class SourceToSyncDto  : IResponseObject
{
	/// <summary>
	/// ID of the source
	/// </summary>
	public Guid Id { get; set; }
	
	/// <summary>
	/// Name of the source
	/// </summary>
	public string? Name { get; set; }
}