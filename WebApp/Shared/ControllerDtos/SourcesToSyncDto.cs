using Levelbuild.Core.FrontendDtos.Shared;
// ReSharper disable AutoPropertyCanBeMadeGetOnly.Global

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Response Object containing multiple DataSource Ids and their names
/// </summary>
public class SourcesToSyncDto  : IResponseObject
{
	/// <summary>
	/// Dictionary with the key representing Ids of sources and the value the name
	/// </summary>
	public List<SourceToSyncDto> Sources { get; set; } = new();
}