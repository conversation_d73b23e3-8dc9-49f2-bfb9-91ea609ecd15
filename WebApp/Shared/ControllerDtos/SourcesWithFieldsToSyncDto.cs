using Levelbuild.Core.FrontendDtos.Shared;

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Response Object containing multiple DataSource Ids and their fields that should be synced
/// </summary>
public class SourcesWithFieldsToSyncDto : IResponseObject
{
	/// <summary>
	/// List of sources with source id, source name and the fields to sync
	/// </summary>
	public List<SourceWithFieldsToSync> Sources { get; set; } = new();

	/// <summary>
	/// struct of sources with source id, source name and the fields to sync
	/// </summary>
	public struct SourceWithFieldsToSync
	{
		/// <summary>
		/// Id of the source
		/// </summary>
		public Guid SourceId { get; set; }

		/// <summary>
		/// Name of the source
		/// </summary>
		public string SourceName { get; set; }

		/// <summary>
		/// Fields to sync of the source
		/// </summary>
		public List<FieldToSyncDto> Fields { get; set; }
	}
}