using Humanizer;
using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Shared.Enums;

/// <summary>
/// Frontend css states to map a state to a specific color
/// </summary>
public enum ColorState
{
	Error,

	Warning,

	Success,
	
	Info,

	Active,

	Inactive,
}

public static class ColorStateExtension
{
	public static string GetColorStateAsString(this ColorState enumValue)
	{
		return enumValue.GetDisplayName().Kebaberize();
	}

	public static string GetString(this ColorState enumValue)
	{
		return EnumUtils<ColorState>.GetTranslatableString(enumValue);
	}
}