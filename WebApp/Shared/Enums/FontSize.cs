using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Shared.Enums;

public enum FontSize
{
	Small,

	Medium,

	Large
}

public static class FontSizeExtensions
{
	public static string GetFontSizeAsString(this FontSize enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}

	public static string GetString(this FontSize enumValue)
	{
		return EnumUtils<FontSize>.GetTranslatableString(enumValue);
	}
}