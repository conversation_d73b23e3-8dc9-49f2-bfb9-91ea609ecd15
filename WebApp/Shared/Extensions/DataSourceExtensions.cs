using System.Text.Json;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.DataSource;

namespace Levelbuild.Frontend.WebApp.Shared.Extensions;

/// <summary>
/// Extension methods for <see cref="DataSourceEntity"/>.
/// </summary>
public static class DataSourceExtensions
{
	#region Lookup Fields
	
	/// <summary>
	/// Prepares additional lookup fields.
	/// </summary>
	/// <param name="dataSource"></param>
	/// <exception cref="ArgumentException"></exception>
	public static List<DataStoreQueryField> SelectLookupAndVirtualFields(this DataSourceEntity dataSource)
	{
		var result = new List<DataStoreQueryField>();
		
		// Select all valid lookup/virtual fields
		var virtualFields = dataSource.Fields.Where(field => field is { FieldType: DataFieldType.VirtualField, HasVirtualData: true });
		foreach (var virtualField in virtualFields)
		{
			if (virtualField.Type == DataType.Guid)
			{
				var queryName = virtualField.VirtualDataStoreQueryName!;
				var queryParts = queryName.Split('.');
				queryParts[^1] = "Id";
				var queryId = string.Join('.', queryParts);
			
				result.Add(new DataStoreQueryField(queryName, virtualField.Name + ".Display"));
				result.Add(new DataStoreQueryField(queryId, virtualField.Name));
			} else if (virtualField.VirtualDataStoreQueryName != null)
				result.Add(new DataStoreQueryField(virtualField.VirtualDataStoreQueryName, virtualField.Name));
		}
		
		var lookupDisplayFields = dataSource.Fields.Where(field => field.FieldType == DataFieldType.LookupField && field is { LookupDisplayField: not null })
			.Select(field => new DataStoreQueryField(field.Name + "." + field.LookupDisplayField!.Name, field.Name + ".Display"))
			.ToList();
		result.AddRange(lookupDisplayFields);
		
		return result;
	}

	/// <summary>
	/// Execute an action on a specific element
	/// </summary>
	/// <param name="dataSource"></param>
	/// <param name="actionDto">Contains the executable action</param>
	/// <param name="elementGuid"></param>
	public static async Task ExecuteElementAction(this DataSourceEntity dataSource, ElementActionDto actionDto, string elementGuid)
	{
		actionDto.Value = actionDto.Value is JsonElement jsonValue ? jsonValue.GetRealValue() : actionDto.Value;
		switch (actionDto.Type)
		{
			case ElementActionType.Favorite:
				if (actionDto.Value != null && (bool)actionDto.Value)
					await dataSource.AddFavouriteAsync(elementGuid);
				else
					await dataSource.RemoveFavouriteAsync(elementGuid);
				break;
			case ElementActionType.Inactive:
				if (actionDto.Value != null && (bool)actionDto.Value)
					await dataSource.SetInactiveAsync(elementGuid);
				else
					await dataSource.SetActiveAsync(elementGuid);
				break;
		}
	}
	
	#endregion
}