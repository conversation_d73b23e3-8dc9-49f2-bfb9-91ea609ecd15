using System.Linq.Expressions;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Entities.Interfaces;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Microsoft.EntityFrameworkCore;
using SharpCompress;

namespace Levelbuild.Frontend.WebApp.Shared.Extensions;

/// <summary>
/// Addon for Queryable Class which adds some useful methods to find, mutate and sort list items like entries of List oder DBSet.
/// </summary>
public static class QueryableExtensions
{
	/// <summary>
	/// Adds filtering to Queryable with the help of a filter configuration list 
	/// </summary>
	/// <param name="query"></param>
	/// <param name="filters">List of conditions to filter within the IQueryable</param>
	/// <param name="searchColumns">All fields that are taken into account in fulltext search filters</param>
	/// <param name="term">Optional additional search term (used to search over all termFilterColumns)</param>
	/// <param name="termFilterColumns">column names use for applying the term-search</param>
	/// <typeparam name="TEntity">Type for the list item. Mostly used with DbSet's</typeparam>
	/// <returns>A Queryable object to use it for additional LINQ calls like ToList() or Count()</returns>
	public static IQueryable<TEntity> WithFiltering<TEntity>(this IQueryable<TEntity> query,
																	   IList<QueryParamFilterDto>? filters,
																	   PropertyPathList<TEntity>? searchColumns = null,
																	   string? term = null,
																	   PropertyPathList<TEntity>? termFilterColumns = null)
		where TEntity : class
	{
		// do we have at least one filter to apply?
		if (filters is { Count: > 0 })
		{
			FilterTree<TEntity> filterTree = new();

			// add dynamic filter
			filters.ForEach(filter => filterTree.AddFilter(filter.FilterColumn, filter.Operator, filter.CompareValue, searchColumns));

			// apply filterTree
			if (filterTree.HasFilter())
			{
				var whereExpression = filterTree.GetExpression();
				if (whereExpression != null)
					query = query.Where((whereExpression as Expression<Func<TEntity, bool>>)!);
			}
		}

		// append term search expression if possible
		if (!string.IsNullOrEmpty(term) && termFilterColumns?.Count > 0)
			query = query.Where(EntityContainsValue(termFilterColumns, term));

		return query;
	}

	/// <summary>
	/// Adds sorting to Queryable with the help of a sorting configuration list 
	/// </summary>
	/// <param name="query"></param>
	/// <param name="sortings">List which contains the sorting configuration [{ColumnName, Direction}, ...]</param>
	/// <typeparam name="TEntity">Type for the list item. Mostly used with DbSet's</typeparam>
	/// <returns>A Queryable object to use it for additional LINQ calls like ToList() or Count()</returns>
	public static IQueryable<TEntity> WithSorting<TEntity>(this IQueryable<TEntity> query, IList<QueryParamSortingDto>? sortings = null)
	{
		sortings ??= [];
		
		// Add 'ID' as last ordering to get always the same results
		const string defaultColumn = "Id";
		if (typeof(TEntity).GetProperty(defaultColumn) != null)
		{
			sortings.Add(new QueryParamSortingDto() { OrderColumn = defaultColumn, Direction = SortDirection.Asc });
		}

		IOrderedQueryable<TEntity>? orderedQuery = null;
		sortings.ForEach(sorting =>
		{
			if (string.IsNullOrWhiteSpace(sorting.OrderColumn))
				throw new ArgumentException("The filter column name cannot be null or empty.", nameof(sorting.OrderColumn));

			var parameter = Expression.Parameter(typeof(TEntity), "x");
			var property = ExpressionExtension.CreateExpressionByPropertyName<TEntity>(sorting.OrderColumn, parameter);
			var keySelector = Expression.Lambda(property, parameter);

			var methodName = sorting.Direction == SortDirection.Desc
								 ? orderedQuery == null ? "OrderByDescending" : "ThenByDescending"
								 : orderedQuery == null
									 ? "OrderBy"
									 : "ThenBy";

			orderedQuery = ApplyOrdering(orderedQuery ?? query, methodName, keySelector);
		});

		return orderedQuery ?? query;
	}

	private static IOrderedQueryable<TEntity> ApplyOrdering<TEntity>(IQueryable<TEntity> query, string methodName, LambdaExpression keySelector)
	{
		var method = typeof(Queryable)
			.GetMethods()
			.First(m => m.Name == methodName && m.GetParameters().Length == 2)
			.MakeGenericMethod(typeof(TEntity), keySelector.Body.Type);

		return (IOrderedQueryable<TEntity>)method.Invoke(null, [query, keySelector])!;
	}

	/// <summary>
	/// Group Queryable list by a property name
	/// </summary>
	/// <param name="query"></param>
	/// <param name="propertyName">Name of the property to be grouped by</param>
	/// <typeparam name="TEntity"></typeparam>
	/// <typeparam name="TKey"></typeparam>
	/// <returns></returns>
	/// <exception cref="ArgumentException"></exception>
	public static IQueryable<IGrouping<TKey, TEntity>> GroupByProperty<TKey, TEntity>(
		this IQueryable<TEntity> query,
		string propertyName)
	{
		if (string.IsNullOrWhiteSpace(propertyName))
			throw new ArgumentException("Property name cannot be null or empty.", nameof(propertyName));

		var parameter = Expression.Parameter(typeof(TEntity), "entity");
		var property = ExpressionExtension.CreateExpressionByPropertyName<TEntity>(propertyName, parameter);
		
		var convertedProperty = Expression.Convert(property, typeof(TKey));
		var keySelector = Expression.Lambda<Func<TEntity, TKey>>(convertedProperty, parameter);

		return query.GroupBy(keySelector);
	}

	/// <summary>
	/// Adds select parameters to Queryable
	/// </summary>
	/// <param name="query"></param>
	/// <param name="parameters">Select parameters like filtering, sorting, limit, offset to query a list </param>
	/// <param name="searchColumns">All fields that are taken into account in fulltext search filters</param>
	/// <param name="termFilterColumns">column names use for applying the term-search (if parameters.Term is set)</param>
	/// <typeparam name="TEntity">Type for the list item. Mostly used with DbSet's</typeparam>
	/// <returns>A Queryable object to use it for additional LINQ calls like ToList() or Count()</returns>
	/// <exception cref="ArgumentNullException">will be thrown if parameters are null</exception>
	public static IQueryable<TEntity> WithQueryParams<TEntity>(
		this IQueryable<TEntity> query,
		QueryParamsDto parameters,
		PropertyPathList<TEntity>? searchColumns = null,
		PropertyPathList<TEntity>? termFilterColumns = null)
		where TEntity : class
	{
		ArgumentNullException.ThrowIfNull(parameters);

		// add filtering
		query = query.WithFiltering(parameters.Filters, searchColumns, parameters.Term, termFilterColumns);

		// add sorting
		query = query.WithSorting(parameters.Sortings);

		// with offset
		if (parameters.Offset > 0)
			query = query.Skip(parameters.Offset);

		// with limit
		if (parameters.Limit > 0)
			query = query.Take(parameters.Limit);

		return query;
	}

	/// <summary>
	/// Mutate a list of db entities to a list of DTO's  
	/// </summary>
	/// <param name="queryable">prepared queryable (which may have already been enhanced with things like .Include())</param>
	/// <param name="excludedProperties">An array of properties of the entity that are excluded</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <returns></returns>
	public static IList<TEntity> ToDtoList<TEntity>(this IQueryable<IConvertibleEntity<TEntity>> queryable, params string[] excludedProperties)
	{
		return queryable.Select(entity => entity.ToDto(excludedProperties, null)!).ToList();
	}
	
	/// <inheritdoc cref="ToDtoList{TEntity}"/>
	public static async Task<IList<TEntity>> ToDtoListAsync<TEntity>(this IQueryable<IConvertibleEntity<TEntity>> queryable, params string[] excludedProperties)
	{
		return await queryable.Select(entity => entity.ToDto(excludedProperties, null)!).ToListAsync();
	}

	private class FilterTree<TEntity> where TEntity : class
	{
		private readonly Dictionary<string, FilterTreeItem> _dictionary = new();

		public void AddFilter(string filterColumn, QueryParamFilterOperator filterOperator, object? compareValue, PropertyPathList<TEntity>? searchColumns = null)
		{
			_dictionary.TryAdd(filterColumn, new FilterTreeItem());

			var filterTreeItem = _dictionary[filterColumn];
			var whereExpression = ExpressionExtension.BuildFilterExpression(filterColumn, filterOperator, compareValue, searchColumns);
			filterTreeItem.AddToGroup(whereExpression, filterOperator.GetLinkType());
		}

		public Expression? GetExpression()
		{
			if (_dictionary.Count == 0)
				return null;

			Expression? expression = null;
			_dictionary.ForEach(item =>
			{
				var andExpression = item.Value.GetExpression();
				expression = expression != null ? ExpressionExtension.AndAlso<TEntity>(expression, andExpression) : andExpression;
			});
			return expression;
		}

		public bool HasFilter()
		{
			return _dictionary.Count > 0;
		}

		private class FilterTreeItem
		{
			private readonly List<Expression> _andGroup = new();
			private readonly List<Expression> _orGroup = new();

			public void AddToGroup(Expression filterExpression, QueryParamFilterLinkType linkType)
			{
				switch (linkType)
				{
					case QueryParamFilterLinkType.And:
						_andGroup.Add(filterExpression);
						break;
					case QueryParamFilterLinkType.Or:
						_orGroup.Add(filterExpression);
						break;
					default:
						throw new ArgumentOutOfRangeException(nameof(linkType), linkType, null);
				}
			}

			public Expression GetExpression()
			{
				Expression? expression = null;
				if (_orGroup.Count > 0)
					expression = _orGroup.Aggregate(ExpressionExtension.OrElse<TEntity>);

				if (_andGroup.Count <= 0)
					return expression ?? (() => true);

				var andExpression = _andGroup.Aggregate(ExpressionExtension.AndAlso<TEntity>);
				return expression != null ? ExpressionExtension.OrElse<TEntity>(expression, andExpression) : andExpression;
			}
		}
	}

	private static Expression<Func<TEntity, bool>> EntityContainsValue<TEntity>(PropertyPathList<TEntity> searchFields, string searchValue)
		where TEntity : class
	{
		var like = typeof(DbFunctionsExtensions).GetMethod("Like", new[] { typeof(DbFunctions), typeof(string), typeof(string) })!;
		var iLike = typeof(NpgsqlDbFunctionsExtensions).GetMethod("ILike", new[] { typeof(DbFunctions), typeof(string), typeof(string) })!;
		var compareMethod = GlobalValues.Instance.DatabaseProvider == DatabaseProvider.Postgres ? iLike : like;

		//var searchConstant = Expression.Constant(searchValue);
		var searchConstant = Expression.Constant("%" + searchValue + "%", typeof(string));
		var entityExpression = Expression.Parameter(typeof(TEntity));

		// remove duplicates
		searchFields = searchFields
			.Distinct()
			.ToPropertyNameList<TEntity>();

		Expression? combinedExpression = null;
		foreach (var fieldPath in searchFields)
		{
			var propertyField = fieldPath.GetAsMemberExpression(entityExpression);

			var expression = Expression.Call(compareMethod, Expression.Constant(null, typeof(DbFunctions)), propertyField, searchConstant);
			combinedExpression = combinedExpression == null ? expression : Expression.OrElse(combinedExpression, expression);
		}

		return Expression.Lambda<Func<TEntity, bool>>(combinedExpression!, entityExpression);
	}
}