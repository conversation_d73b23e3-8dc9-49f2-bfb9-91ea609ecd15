using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Frontend.WebApp.Shared.Extensions;

/// <summary>
/// An extension class for additional custom functionality for the string class
/// </summary>
public static class StringExtensions
{
	#region Conversions

	/// <summary>
	/// convert first character of string to lower case
	/// </summary>
	/// <param name="input">string input</param>
	/// <returns></returns>
	public static string ToLowerFirstChar(this string input)
	{
		if (string.IsNullOrEmpty(input))
			return input;

		return char.ToLower(input[0]) + input.Substring(1);
	}

	#endregion
	
	#region Parser
	
	/// <summary>
	/// Tries to parse a given string as an aggregate function.
	/// </summary>
	/// <param name="functionName"></param>
	/// <param name="function"></param>
	/// <returns></returns>
	public static bool TryParseAggregateFunction(this string functionName, out DataStoreColumnAggregationFunction? function)
	{
		functionName = functionName.ToLower();
		switch (functionName)
		{
			case {} when functionName.StartsWith("avg(") && functionName.EndsWith(")"):
				function = DataStoreColumnAggregationFunction.Avg;
				return true;
			case {} when functionName.StartsWith("sum(") && functionName.EndsWith(")"):
				function = DataStoreColumnAggregationFunction.Sum;
				return true;
			case {} when functionName.StartsWith("count(") && functionName.EndsWith(")"):
				function = DataStoreColumnAggregationFunction.Count;
				return true;
			case {} when functionName.StartsWith("max(") && functionName.EndsWith(")"):
				function = DataStoreColumnAggregationFunction.Max;
				return true;
			case {} when functionName.StartsWith("min(") && functionName.EndsWith(")"):
				function = DataStoreColumnAggregationFunction.Min;
				return true;
			default:
				function = null;
				return false;
		}
	}
	
	#endregion
}