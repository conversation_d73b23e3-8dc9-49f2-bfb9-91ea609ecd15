using Levelbuild.Core.EntityInterface.Constants;

namespace Levelbuild.Frontend.WebApp.Shared;

/// <summary>
/// Stores global values.
/// </summary>
public class GlobalValues
{
	#region Singleton

	private static GlobalValues? _instance;

	/// <summary>
	/// Singleton instance.
	/// </summary>
	public static GlobalValues Instance
	{
		get { return _instance ??= new GlobalValues(); }
	}

	#endregion

	#region Values

	private DatabaseProvider? _databaseProvider;
	
	/// <summary>
	/// The application's DatabaseProvider
	/// </summary>
	public DatabaseProvider? DatabaseProvider
	{
		get => _databaseProvider;
		set
		{
			// Only allowed to set once!
			if (_databaseProvider == null)
				_databaseProvider = value;
		}
	}

	#endregion

	private GlobalValues() { }
}