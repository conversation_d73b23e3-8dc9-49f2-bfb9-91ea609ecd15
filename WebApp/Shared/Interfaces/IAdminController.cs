using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Frontend.WebApp.Shared.Interfaces;

/// <summary>
/// Interface for the base class for all controllers that must only be available to admin users.
/// </summary>
// ReS<PERSON>per disable once TypeParameterCanBeVariant
public interface IAdminController<TDto>
{
	/// <summary>
	/// Returns a mutated list of entities as JSON.
	/// </summary>
	/// <param name="parameters">an object to query a Subset of entities</param>
	public ActionResult<FrontendResponse> Query(QueryParamsDto parameters);
	
	/// <summary>
	/// Returns a specific entity as JSON
	/// </summary>
	/// <param name="id">readable identifier for a specific entity</param>
	public ActionResult<FrontendResponse> Get(Guid id);
	
	/// <summary>
	/// Creates a new entity in the database.
	/// </summary>
	/// <param name="dto">DTO for the new entity</param>
	/// <returns>OK Response for successful creating and BAD Response when an errors occurred</returns>
	public Task<ActionResult<FrontendResponse>> Create([FromBody] TDto dto);
	
	/// <summary>
	/// Updates an entity in the database.
	/// </summary>
	/// <param name="id">identifier for a specific entity</param>
	/// <param name="dto">DTO for the updated version of an entity</param>
	/// <returns>OK Response for successful updating and BAD Response when an errors occurred</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	public Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] TDto dto);
	
	/// <summary>
	/// Deletes an entity in the database.
	/// </summary>
	/// <param name="id">identifier for a specific entity</param>
	/// <returns>OK Response for successful deleting and BAD Response when an errors occurred</returns>
	public ActionResult<FrontendResponse> Delete(Guid id);
}