using System.Globalization;
using System.Text.Encodings.Web;
using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Entities.Features.User;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Microsoft.Extensions.Localization;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using ILogger = Serilog.ILogger;

namespace Levelbuild.Frontend.WebApp.Shared.Localization;

/// <summary>
/// Localizer Instance used to translate one specific view
/// @TODO Fix Car Bug without setting Cache logic non-static -> see git blame
/// </summary>
public class StringLocalizer : IStringLocalizer
{
	private readonly string _contextPath;
	private readonly string[] _contextPathParts;
	private readonly Guid? _customerId;
	private TextEncoder? _encoder;
	private readonly StringLocalizerCache _cache;
	private readonly UserPreferencesEntity? _currentUserPreferences;
	private readonly bool _ignoreWarnings;
	
	private readonly CultureInfo _defaultCulture = new("en");
	private readonly ILogger _logger = Log.ForContext<StringLocalizer>();

	/// <summary>
	/// Instantiate new StringLocalizer (called from StringLocalizerFactory)
	/// </summary>
	/// <param name="contextPath">context for which the localizer is instantiated (normally ControllerName/ViewName)</param>
	/// <param name="cache"></param>
	/// <param name="userManager"></param>
	/// <param name="ignoreWarnings">optional, stops this localizer from throwing warnings</param>
	public StringLocalizer(string contextPath, StringLocalizerCache cache, UserManager userManager, bool ignoreWarnings = false)
	{
		_contextPath = contextPath;
		_contextPathParts = _contextPath.Split("/").Where(part => !part.IsNullOrEmpty()).ToArray();
		_cache = cache;
		_ignoreWarnings = ignoreWarnings;
		
		_currentUserPreferences = userManager.GetCurrentUserPreferencesAsync().ResultWithUnwrappedExceptions();
		_customerId = userManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().CurrentCustomerId;
	}

	/// <summary>
	/// get a single translation by code
	/// </summary>
	/// <param name="key">key to translate</param>
	public LocalizedString this[string key]
	{
		get
		{
			if (key.IsNullOrEmpty())
				return new LocalizedString("", "", resourceNotFound: true);
			
			var translated = Translate(key);
			if (translated.IsNullOrEmpty())
				HandleMissingTranslation(key);

			var formatted = translated ?? "[" + key + "]";
			return new LocalizedString(key, _encoder != null ? _encoder.Encode(formatted) : formatted, resourceNotFound: translated == null);
		}
	}

	/// <summary>
	/// get a single translation and pass additional arguments to format the string properly
	/// </summary>
	/// <param name="key"></param>
	/// <param name="arguments"></param>
	public LocalizedString this[string key, params object[] arguments]
	{
		get
		{
			if (key.IsNullOrEmpty())
				return new LocalizedString("", "", resourceNotFound: true);
			
			var translated = Translate(key);
			if (translated.IsNullOrEmpty())
				HandleMissingTranslation(key);

			var formatted = string.Format(translated ?? "[" + key + "]", arguments);
			return new LocalizedString(key, _encoder != null ? _encoder.Encode(formatted) : formatted, resourceNotFound: translated == null);
		}
	}

	/// <summary>
	/// Gets all translations for the current culture and contextPath
	/// </summary>
	/// <param name="includeAncestorCultures">should the list contain translations of the base culture if the current culture is no base culture (de-DE -> de)</param>
	/// <returns>List of all available Translations</returns>
	public IEnumerable<LocalizedString> GetAllStrings(bool includeAncestorCultures)
	{
		return GetAllStrings(includeAncestorCultures, false);
	}

	/// <summary>
	/// Gets all translations for the current culture and contextPath
	/// </summary>
	/// <param name="includeAncestorCultures">should the list contain translations of the base culture if the current culture is no base culture (de-DE -> de)</param>
	/// <param name="includeBaseTranslations"></param>
	/// <returns></returns>
	public IEnumerable<LocalizedString> GetAllStrings(bool includeAncestorCultures, bool includeBaseTranslations)
	{
		var culture = CultureInfo.CurrentCulture;
		var translations = GetAllByCulture(culture, includeBaseTranslations);

		if (!culture.IsNeutralCulture && includeAncestorCultures)
		{
			var parentCulture = culture.Parent;
			var baseTranslations = GetAllByCulture(parentCulture, includeBaseTranslations);
			translations = baseTranslations.Concat(translations);
		}

		return translations;
	}

	/// <summary>
	/// Sets the text encoder which should be used while creating the LocalizedStrings
	/// </summary>
	/// <param name="encoder"></param>
	public void SetEncoder(TextEncoder? encoder)
	{
		_encoder = encoder;
	}

	/// <summary>
	/// Removes the cached values for the specified culture. Can be used immediately after adding new values to the database.
	/// </summary>
	/// <param name="cultures">The cultures that should be removed from the cache.</param>
	public void ClearCache(params CultureInfo[] cultures)
	{
		_cache.Clear();
	}

	// get all translations for a given culture
	private IEnumerable<LocalizedString> GetAllByCulture(CultureInfo culture, bool includeBaseTranslations = false)
	{
		// update cache if necessary
		_cache.Update(culture);

		// do we have any translations for this culture?
		if (!_cache.ContainsKey(culture))
			return Enumerable.Empty<LocalizedString>();

		var all = _cache[culture].Where(x =>
												   _contextPath.IsNullOrEmpty() ? !x.Key.StartsWith("/") :
												   includeBaseTranslations ? x.Key.StartsWith(_contextPath) || !x.Key.StartsWith("/") :
												   x.Key.StartsWith(_contextPath)
		);
		return all.Select(x => new LocalizedString(x.Key, _encoder != null ? _encoder.Encode(x.Value) : x.Value, resourceNotFound: false));
	}

	// translate a single key
	private string? Translate(string key)
	{
		if(_currentUserPreferences == null)
			return Translate(key, CultureInfo.CurrentCulture);
		
		switch (_currentUserPreferences.CultureOptionType)
		{
			case CultureOptionType.Device:
				return Translate(key, CultureInfo.CurrentCulture);
			case CultureOptionType.Fixed:
				return Translate(key, _currentUserPreferences.CurrentCulture!.ToInfo());
			default:
				throw new NotSupportedException($"Unsupported {nameof(CultureOptionType)}: '{_currentUserPreferences.CultureOptionType}'");
		}
	}

	private string? Translate(string key, CultureInfo culture)
	{
		// Step 1: specific culture + specific path
		var translated = GetValue(culture, _contextPath + key);

		// Step 2: neutral culture + specific path
		if (translated == null && !culture.IsNeutralCulture)
			translated = GetValue(culture.Parent, _contextPath + key);

		if (translated != null || _contextPath == "")
			return translated;

		// Step 3: specific culture + value with base path (if path can be split)
		if (_contextPathParts.Length > 1)
		{
			var basePath = "/" + string.Join("/", _contextPathParts[..^1]) + "/";
			translated = GetValue(culture, basePath + key);

			// Step 4: neutral culture + value with base path
			if (translated == null && !culture.IsNeutralCulture)
				translated = GetValue(culture.Parent, basePath + key);

			if (translated != null)
				return translated;
		}

		// Step 5: specific culture + base value without path
		translated = GetValue(culture, key);

		// Step 6: neutral culture + base value without path
		if (translated == null && !culture.IsNeutralCulture)
			translated = GetValue(culture.Parent, key);

		if (translated != null)
			return translated;

		// Step 7: fallback to default Culture?
		var baseCulture = culture.IsNeutralCulture ? culture : culture.Parent;
		if (!baseCulture.Equals(_defaultCulture))
			translated = GetValue(_defaultCulture, _contextPath + key) ?? GetValue(_defaultCulture, key);

		return translated;
	}

	// get a single value from the cache
	private string? GetValue(CultureInfo culture, string key)
	{
		// update cache if necessary
		_cache.Update(culture);

		if (!_cache.ContainsKey(culture))
			return null;

		if (_customerId != null && _cache[culture].ContainsKey(_customerId + ":" + key))
			return _cache[culture][_customerId + ":" + key];
			
		return _cache[culture].ContainsKey(key) ? _cache[culture][key] : null;
	}

	// log a warning everytime a translation is missing while we're in dev mode
	private void HandleMissingTranslation(string key)
	{
		if (!Program.IsDevelopmentOrStaging)
			return;
		if (_ignoreWarnings)
			return;

		_logger.Warning("Missing translation for {Key} in context {ContextPath} with culture {CurrentCulture}",
						key, _contextPath, CultureInfo.CurrentCulture);
	}
}