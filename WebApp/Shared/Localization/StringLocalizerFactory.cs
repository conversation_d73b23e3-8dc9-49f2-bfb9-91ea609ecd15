using Levelbuild.Core.EntityInterface;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Microsoft.Extensions.Localization;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Frontend.WebApp.Shared.Localization;

/// <summary>
/// Used to create new StringLocalizer instances
/// </summary>
public class StringLocalizerFactory : IExtendedStringLocalizerFactory
{
	private readonly StringLocalizerCache _cache;
	private readonly UserManager _userManager;

	/// <summary>
	/// inject TranslationService
	/// </summary>
	/// <param name="cache"></param>
	/// <param name="userManager"></param>
	public StringLocalizerFactory(StringLocalizerCache cache, UserManager userManager)
	{
		_cache = cache;
		_userManager = userManager;
	}

	/// <summary>
	/// Create a new StringLocalizer instance based on a controller type
	/// </summary>
	/// <param name="resourceType">Type of the Controller we are currently working with</param>
	/// <returns></returns>
	public IStringLocalizer Create(Type resourceType)
	{
		return new StringLocalizer(resourceType.Name, _cache, _userManager);
	}

	/// <summary>
	/// Create a new StringLocalizer instance based on controller name and view name
	/// </summary>
	/// <param name="baseName">usually controller name</param>
	/// <param name="location">usually view name</param>
	/// <returns></returns>
	public IStringLocalizer Create(string baseName, string location)
	{
		return Create(baseName, location, false);
	}

	/// <summary>
	/// Create a new StringLocalizer instance based on controller name and view name
	/// </summary>
	/// <param name="baseName">usually controller name</param>
	/// <param name="location">usually view name</param>
	/// <param name="ignoreWarnings">should this localizer throw warnings, when translations are missing</param>
	/// <returns></returns>
	public IStringLocalizer Create(string baseName, string location, bool ignoreWarnings)
	{
		var contextPath = location.IsNullOrEmpty() ? $"/{baseName}/" : $"/{baseName}/{location}/";
		return new StringLocalizer(contextPath, _cache, _userManager, ignoreWarnings);
	}
}