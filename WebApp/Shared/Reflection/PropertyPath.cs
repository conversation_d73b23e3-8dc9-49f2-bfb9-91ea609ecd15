using System.Linq.Expressions;
using System.Reflection;
using Humanizer;

namespace Levelbuild.Frontend.WebApp.Shared.Reflection;

/// <summary>
/// Represents a complete path to a member property, that me be nested inside a descendant object.
/// </summary>
public class PropertyPath : List<PropertyInfo>
{
	private readonly string _stringPath;
	
	#region Constructor & Build Methods
	
	private PropertyPath(string stringPath)
	{
		_stringPath = stringPath;
	}
	
	/// <summary>
	/// Builds the path based on the string representation of said path.
	/// </summary>
	/// <param name="path">The string representation. i.e. "PageEntity.DataSource.Name"</param>
	/// <param name="type">The type of the base object.</param>
	/// <returns></returns>
	internal static PropertyPath BuildFromString(string path, Type type)
	{
		var propertyPath = new PropertyPath(path);
		
		foreach (var part in path.Split('.'))
		{
			var propertyInfo = type.GetProperty(part.Pascalize());
			
			if(propertyInfo == null)
				break;
			
			propertyPath.Add(propertyInfo);
			
			type = propertyInfo.PropertyType;
		}
		
		return propertyPath;
	}
	
	/// <summary>
	/// Builds the path based on an expression that defines it.
	/// </summary>
	/// <param name="expression"></param>
	/// <typeparam name="T"></typeparam>
	/// <typeparam name="TProperty"></typeparam>
	/// <returns></returns>
	/// <exception cref="InvalidOperationException"></exception>
	internal static PropertyPath BuildFromExpression<T, TProperty>(Expression<Func<T, TProperty>> expression)
	{
		var path = new List<string>();
		var body = expression.Body;
		var finished = false;
		while (!finished)
		{
			switch (body)
			{
				case MemberExpression memberExpression:
					path.Add(memberExpression.Member.Name);
					body = memberExpression.Expression;
					break;
				case ParameterExpression:
					finished = true;
					break;
				default:
					throw new InvalidOperationException($"Failed to generate path for expression type {body?.GetType()}.");
			}
		}
		
		path.Reverse();
		return BuildFromString(string.Join(".", path), typeof(T));
	}
	
	#endregion
	
	#region Methods
	
	/// <summary>
	/// Returns a <see cref="MemberExpression"/> that represents the entire path.
	/// </summary>
	/// <param name="entityExpression">The parameter expression of the base object.</param>
	/// <returns></returns>
	public MemberExpression GetAsMemberExpression(ParameterExpression entityExpression)
	{
		MemberExpression? memberExpression = null;
		foreach (var node in this)
		{
			if (memberExpression == null)
			{
				memberExpression = Expression.Property(entityExpression, node);
				continue;
			}
			
			memberExpression = Expression.Property(memberExpression, node);
		}
		
		return memberExpression!;
	}
	
	/// <summary>
	/// Returns the member property's value of the given source object.
	/// </summary>
	/// <param name="source"></param>
	/// <returns></returns>
	public object? GetPropertyValueFromPath(object source)
	{
		var currentObject = source;
		foreach (var node in this)
		{
			currentObject = node.GetValue(currentObject);
			
			if(currentObject == null)
				break;
		}
		
		return currentObject;
	}
	
	/// <summary>
	/// Returns the path's string representation.
	/// </summary>
	/// <returns></returns>
	public override string ToString()
	{
		return _stringPath;
	}
	
	#endregion
}