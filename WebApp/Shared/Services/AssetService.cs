using Microsoft.IdentityModel.Tokens;
using Vite.AspNetCore;

namespace Levelbuild.Frontend.WebApp.Shared.Services;

/// <inheritdoc />
public class AssetService : IAssetService
{
	private readonly IViteManifest _manifest;

	private readonly int _vitePort = 5173;
	
	private readonly bool _viteWithHttps;

	private readonly string _viteHost = "localhost";

	private readonly bool _runInContainer;
	
	private const string BasePath = "/src/features";


	/// <summary>
	/// Default ctor injecting vite manifest and current environment
	/// </summary>
	/// <param name="manifest">manifest file which includes the file mapping</param>
	/// <param name="configuration">app settings json</param>
	public AssetService(IViteManifest manifest, IConfiguration configuration)
	{
		_manifest = manifest;
		
		_runInContainer = Program.IsRunningInContainer;
		if (!Program.IsDevelopment || _runInContainer) 
			return;
		
		var serverSection = configuration.GetSection("Vite").GetSection("Server");
		_vitePort = serverSection.GetValue<int>("Port");
		_viteWithHttps = serverSection.GetValue<bool>("Https");
		
		var host = serverSection.GetValue<string>("Host");
		_viteHost = string.IsNullOrEmpty(host) ? "localhost" : host;
	}

	/// <inheritdoc />
	public string SolvePath(string path)
	{
		path = BasePath + path;
		if(Program.IsDevelopment && !_runInContainer)
			return $"{(_viteWithHttps ? "https" : "http")}://{_viteHost}:{_vitePort}{path}";

		var manifestKey = path.Substring(1);
		if (_manifest[manifestKey] != null) 
			return "/" + _manifest[manifestKey]!.File;
		
		Console.Out.WriteLine("No Manifest Mapping found for this path in /wwwroot/assets.manifest.json");
		return string.Empty;
	}
}