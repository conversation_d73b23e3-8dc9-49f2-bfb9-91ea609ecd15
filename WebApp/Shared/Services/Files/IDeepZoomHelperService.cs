using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DeepZoom;

namespace Levelbuild.Frontend.WebApp.Shared.Services.Files;

/// <summary>
/// Helper service to generate and cache a deep zoom image of a file
/// </summary>
public interface IDeepZoomHelperService
{
	/// <summary>
	/// Helper method to generate and cache a deep zoom image of a file
	/// </summary>
	public Task<CachedDeepZoomEntity?> GetAndCacheDeepZoomImageAsync(CoreDatabaseContext databaseContext, string fileId, DataSourceEntity dataSource, int dpi = 300);

	/// <summary>
	/// Helper method to fetch a specific file of a cached deep zoom image
	/// </summary>
	public Task<Stream?> GetDeepZoomImageFileAsync(CoreDatabaseContext databaseContext, string fileId, string subFileFolder, string subFileName, int dpi = 300);

	/// <summary>
	/// Helper method to delete all cached deep zoom images belonging to a file
	/// </summary>
	public Task DeleteDeepZoomImagesAsync(CoreDatabaseContext databaseContext, string fileId, CancellationToken cancellationToken = default);

	/// <summary>
	/// Helper method to delete all cached deep zoom images of the passed deep zoom Entities
	/// </summary>
	public Task DeleteDeepZoomImagesAsync(CoreDatabaseContext databaseContext, IList<CachedDeepZoomEntity> cachedDeepZoomEntities, CancellationToken cancellationToken = default);
}