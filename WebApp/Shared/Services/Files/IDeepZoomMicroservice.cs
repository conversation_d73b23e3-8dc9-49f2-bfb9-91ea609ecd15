namespace Levelbuild.Frontend.WebApp.Shared.Services;

/// <summary>
/// Service to call upon the deep zoom microservice
/// </summary>
public interface IDeepZoomMicroservice
{
	/// <summary>
	/// Sends a request to the microservice and returns the response
	/// </summary>
	public Task<HttpResponseMessage> GetDeepZoomFile(Stream file, string fileName = "file", int? tileSize = 256, CancellationToken cancellationToken = default);
}