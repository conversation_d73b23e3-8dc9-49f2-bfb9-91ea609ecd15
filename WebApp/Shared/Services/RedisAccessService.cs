using Levelbuild.Frontend.WebApp.Shared.Constants;
using StackExchange.Redis;

namespace Levelbuild.Frontend.WebApp.Shared.Services;

/// <summary>
/// Service to call upon redis
/// </summary>
public class RedisAccessService : IRedisAccessService
{
	private readonly IDatabase _masterDatabase;
	private readonly IDatabase _readOnlyDatabase;

	/// <summary>
	/// inject the redis access service
	/// </summary>
	/// <param name="redisMasterConnection"></param>
	/// <param name="redisReadOnlyConnection"></param>
	public RedisAccessService([FromKeyedServices(RedisConstants.RedisWriteConnection)] IConnectionMultiplexer redisMasterConnection,
							  [FromKeyedServices(RedisConstants.RedisReadConnection)]
							  IConnectionMultiplexer redisReadOnlyConnection)
	{
		_masterDatabase = redisMasterConnection.GetDatabase(RedisConstants.WebAppDatabase);
		_readOnlyDatabase = redisReadOnlyConnection.GetDatabase(RedisConstants.WebAppDatabase);
	}

	/// <summary>
	/// Checks if Hash exists.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="value"></param>
	/// <param name="flags"></param>
	public async Task<bool> HashExistsAsync(RedisKey key, RedisValue value, CommandFlags flags = CommandFlags.None)
	{
		return await _readOnlyDatabase.HashExistsAsync(key, value, flags);
	}

	/// <summary>
	/// Trys to fetch value for hash.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="hashField"></param>
	/// <param name="flags"></param>
	public async Task<RedisValue> HashGetAsync(RedisKey key, RedisValue hashField, CommandFlags flags = CommandFlags.None)
	{
		return await _readOnlyDatabase.HashGetAsync(key, hashField, flags);
	}

	/// <summary>
	/// Writes new entry to redis.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="hashField"></param>
	/// <param name="value"></param>
	/// <param name="when"></param>
	/// <param name="flags"></param>
	public async Task<bool> HashSetAsync(RedisKey key, RedisValue hashField, RedisValue value, When when = When.Always, CommandFlags flags = CommandFlags.None)
	{
		return await _masterDatabase.HashSetAsync(key, hashField, value, when, flags);
	}

	/// <summary>
	/// Deletes entry from redis.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="hashField"></param>
	/// <param name="flags"></param>
	public async Task<bool> HashDeleteAsync(RedisKey key, RedisValue hashField, CommandFlags flags = CommandFlags.None)
	{
		return await _masterDatabase.HashDeleteAsync(key, hashField, flags);
	}

	/// <summary>
	/// Saves string to redis.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="value"></param>
	/// <param name="expiry"></param>
	/// <param name="keepTtl"></param>
	/// <param name="when"></param>
	/// <param name="flags"></param>
	public async Task<bool> StringSetAsync(RedisKey key, RedisValue value, TimeSpan? expiry = null, bool keepTtl = false, When when = When.Always,
										   CommandFlags flags = CommandFlags.None)
	{
		return await _masterDatabase.StringSetAsync(key, value, expiry, keepTtl, when, flags);
	}

	/// <summary>
	/// Gets string from redis.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="flags"></param>
	public async Task<RedisValue> StringGetAsync(RedisKey key, CommandFlags flags = CommandFlags.None)
	{
		return await _readOnlyDatabase.StringGetAsync(key, flags);
	}
	
	/// <summary>
	/// Pushes a value to a queue to redis and returns the queue length.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="flags"></param>
	/// <param name="value"></param>
	public async Task<long> QueuePushAsync(RedisKey key, RedisValue value, CommandFlags flags = CommandFlags.None)
	{
		return await _masterDatabase.ListLeftPushAsync(key, new []{value}, flags);
	}
	
	/// <summary>
	/// Fetches the first value in the queue. Returns null if there is no queue or value.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="flags"></param>
	public async Task<RedisValue> QueuePeekAsync(RedisKey key, CommandFlags flags = CommandFlags.None)
	{
		var values = await _readOnlyDatabase.ListRangeAsync(key, -1, -1, flags);
		return values.Length > 0 ? values[0] : RedisValue.Null;
	}
	
	/// <summary>
	/// Fetches the last value in the queue and removes it. Returns null if there is no queue or value.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="flags"></param>
	public async Task<RedisValue> QueueRemoveNewestAsync(RedisKey key, CommandFlags flags = CommandFlags.None)
	{
		return await _masterDatabase.ListLeftPopAsync(key, flags);
	}

	/// <summary>
	/// Removes all elements but the newest fom the queue.
	/// </summary>
	/// <param name="key"></param>
	/// <param name="flags"></param>
	public async Task QueueTrimToNewestAsync(RedisKey key, CommandFlags flags = CommandFlags.None)
	{
		await _masterDatabase.ListTrimAsync(key, -1, -1, flags);
	}
}