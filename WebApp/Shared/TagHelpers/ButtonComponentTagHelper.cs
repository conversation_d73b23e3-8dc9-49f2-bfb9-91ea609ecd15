using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-button
/// </summary>
public class ButtonComponentTagHelper : TagHelper
{
	/// <summary>
	/// Text content of the Button
	/// </summary>
	public string? Label { get; set; }
	
	/// <summary>
	/// Defines the appearance of the button
	/// </summary>
	public ButtonType? Type { get; set; }
	
	/// <summary>
	/// Height of the button
	/// </summary>
	public FontSize? Size { get; set; }
	
	/// <summary>
	/// A Min Width of the Button (Buttons with label default to 6.4rem, without label will fit to content)
	/// string should include unit like px, rem or %
	/// </summary>
	public string? MinWidth { get; set; }
	
	/// <summary>
	/// Non Primary buttons get a signal color
	/// </summary>
	public ColorState? Color { get; set; }

	/// <summary>
	/// Hover over button shows the tooltip
	/// </summary>
	public string? Tooltip { get; set; }

	/// <summary>
	/// Icon which is located to the left of the button
	/// </summary>
	public string? Icon { get; set; }
	
	/// <summary>
	/// Icon which is located to the right of the button
	/// </summary>
	public string? TrailingIcon { get; set; }

	/// <summary>
	/// Optional icon style (default = light)
	/// </summary>
	public IconStyle? IconStyle { get; set; }

	/// <summary>
	/// Optional value which is only used, if the button is part of a button-group
	/// </summary>
	public string? Value { get; set; }

	/// <summary>
	/// Disable the button
	/// </summary>
	public bool Disabled { get; set; } = false;
	
	/// <summary>
	/// Hide the button
	/// </summary>
	public bool Hidden { get; set; } = false;

	/// <summary>
	/// Button displaying loading animation
	/// </summary>
	public bool Skeleton { get; set; } = false;
	
	/// <summary>
	/// Button will be round (makes only sense for icons)
	/// </summary>
	public bool Rounded { get; set; } = false;
	
	/// <summary>
	/// Put the label under the icon
	/// </summary>
	public bool Stacked { get; set; } = false;
	
	/// <summary>
	/// Adds a colored point in the top right corner of the button
	/// </summary>
	public bool WithBadge { get; set; } = false;
	
	/// <summary>
	/// Background color of the badge
	/// </summary>
	public string? BadgeColor { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-button";

		if (Label != null)
			output.Attributes.SetAttribute("label", Label);
		if (Type != null)
			output.Attributes.SetAttribute("type", Type.Value.GetTypeAsString());
		if (Size != null)
			output.Attributes.SetAttribute("size", Size.Value.GetFontSizeAsString());
		if (Color != null)
			output.Attributes.SetAttribute("color", Color.Value.GetColorStateAsString());
		if (Tooltip != null)
			output.Attributes.SetAttribute("tooltip", Tooltip);
		if (Value != null)
			output.Attributes.SetAttribute("value", Value);
		if (!string.IsNullOrEmpty(MinWidth))
			output.Attributes.SetAttribute("min-width", MinWidth);
		if (!string.IsNullOrEmpty(Icon))
			output.Attributes.SetAttribute("icon", Icon);
		if (!string.IsNullOrEmpty(TrailingIcon))
			output.Attributes.SetAttribute("trailing-icon", TrailingIcon);
		if (IconStyle != null)
			output.Attributes.SetAttribute("icon-style", IconStyle.ToString()!.ToLower());
		if (Disabled)
			output.Attributes.SetAttribute("disabled", "");
		if (Hidden)
			output.Attributes.SetAttribute("hidden", "");
		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");
		if (Rounded)
			output.Attributes.SetAttribute("rounded", "");
		if (Stacked)
			output.Attributes.SetAttribute("stacked", "");
		if (WithBadge)
			output.Attributes.SetAttribute("with-badge", "");
		if (!string.IsNullOrEmpty(BadgeColor))
			output.Attributes.SetAttribute("badge-color", BadgeColor);

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}