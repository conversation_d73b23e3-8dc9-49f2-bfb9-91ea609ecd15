using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-data-list
/// deprecated
/// </summary>
[Obsolete("Use TableComponent instead")]
public class DataListComponentTagHelper : DataQueryTagHelper
{
	/// <summary>
	/// In which column is the icon for the display at the beginning of the line
	/// </summary>
	public string? LabelColumn { get; set; }

	/// <summary>
	/// Rows can be displayed next to each other if space is available
	/// </summary>
	public bool Responsive { get; set; } = false;

	/// <summary>
	/// Button box with grouping labels to jump to
	/// </summary>
	public bool Searchbar { get; set; } = false;

	/// <summary>
	/// Records are grouped by a category (e.g. string column = grouped by the first letter)
	/// </summary>
	public string? GroupBy { get; set; }

	/// <summary>
	/// Mark item as loading
	/// </summary>
	public bool Skeleton { get; set; } = false;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-data-list";

		if (Responsive)
			output.Attributes.SetAttribute("responsive", "");

		if (Searchbar)
			output.Attributes.SetAttribute("searchbar", "");

		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");

		if (!string.IsNullOrEmpty(LabelColumn))
			output.Attributes.SetAttribute("label-column", LabelColumn);

		if (!string.IsNullOrEmpty(GroupBy))
			output.Attributes.SetAttribute("group-by", GroupBy);

		ProcessQueryOutput(output, context);
	}
}