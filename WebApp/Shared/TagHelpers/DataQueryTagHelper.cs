using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-data-query
/// </summary>
public class DataQueryTagHelper : TagHelper
{
	/// <summary>
	/// Width of the enumeration wrapper with units
	/// </summary>
	public string? Width { get; set; }

	/// <summary>
	/// Height of the enumeration wrapper with units
	/// </summary>
	public string? Height { get; set; }

	/// <summary>
	/// Base URL to fetch data from server
	/// </summary>
	public string? Url { get; set; }

	/// <summary>
	/// Used to update single data
	/// </summary>
	public string? IdentityColumn { get; set; }

	/// <summary>
	/// Maximum count of displayed rows ('Paging' has to be enabled)
	/// </summary>
	public int? Limit { get; set; }

	/// <summary>
	/// Used for paging ('Paging' has to be enabled)
	/// </summary>
	public int? Offset { get; set; }

	/// <summary>
	/// Should the data records be displayed in full or via the page navigation? 
	/// </summary>
	public bool Paging { get; set; } = false;

	/// <summary>
	/// This list will be displayed in the enumeration view as alternative to work without data fetching
	/// </summary>
	public List<Dictionary<string, object>>? Rows { get; set; }

	/// <summary>
	/// A list of filters to limit the entries
	/// </summary>
	public List<QueryParamFilterDto>? Filters { get; set; }

	/// <summary>
	/// Multiple sorting of specific columns
	/// </summary>
	public List<QueryParamSortingDto>? Sortings { get; set; }

	/// <summary>
	/// One Checkbox per row to select it for additional actions
	/// </summary>
	public bool Selectable { get; set; } = false;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	protected void ProcessQueryOutput(TagHelperOutput output, TagHelperContext context)
	{
		if (Url != null)
			output.Attributes.SetAttribute("url", Url);
		if (IdentityColumn != null)
			output.Attributes.SetAttribute("identity-column", IdentityColumn);
		if (Limit != null)
			output.Attributes.SetAttribute("limit", Limit);
		if (Offset != null)
			output.Attributes.SetAttribute("offset", Offset);
		if (Paging)
			output.Attributes.SetAttribute("paging", "");
		if (Sortings?.Count > 0)
		{
			var sortingString = JsonSerializer.Serialize(Sortings, ConfigHelper.JsonOptionsCamel);
			output.Attributes.SetAttribute("sortings", sortingString);
		}

		if (Filters?.Count > 0)
		{
			var filterString = JsonSerializer.Serialize(Filters, ConfigHelper.JsonOptionsCamel);
			output.Attributes.SetAttribute("filters", filterString);
		}

		if (Rows?.Count > 0)
		{
			var rowString = JsonConvert.SerializeObject(Rows);
			output.Attributes.SetAttribute("rows", rowString);
		}

		if (Selectable)
			output.Attributes.SetAttribute("selectable", "");

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}

		// Set Width and Height as style
		var styleList = new List<string>();
		if (!string.IsNullOrEmpty(Width))
		{
			styleList.Add($"width: {Width};");
		}

		if (!string.IsNullOrEmpty(Height))
		{
			styleList.Add($"height: {Height};");
		}

		if (styleList.Count > 0)
		{
			output.Attributes.SetAttribute("style", string.Join(" ", styleList));
		}
	}
}