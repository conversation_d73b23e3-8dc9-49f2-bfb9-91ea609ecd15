using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
using SharpCompress;

// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-menu-item
/// </summary>
public class DropdownMenuItemComponentTagHelper : TagHelper
{
	/// <summary>
	/// Displayed label of the menu item
	/// </summary>
	public string? Label { get; set; }
	
	/// <summary>
	/// Identifier of the menu item
	/// </summary>
	public string? Action { get; set; }

	/// <summary>
	/// Should there be an icon on the left or at least the space reserved for one? Use property with empty string for the later
	/// </summary>
	public string? IconLeft { get; set; }

	/// <summary>
	/// Left icon color (default = normal text color)
	/// </summary>
	public string? IconLeftColor { get; set; }

	/// <summary>
	/// Should there be an icon on the right or at least the space reserved for one? Use property with empty string for the later
	/// </summary>
	public string? IconRight { get; set; }

	/// <summary>
	/// Right icon color (default = normal text color)
	/// </summary>
	public string? IconRightColor { get; set; }

	/// <summary>
	/// Text which fills the legend at the right of the item
	/// </summary>
	public string? Legend { get; set; }
	
	/// <summary>
	/// Name of the function the gets called, when the option is clicked.
	/// </summary>
	public string? ClickFunction { get; set; }

	/// <summary>
	/// Marks the item as active (bold font)
	/// </summary>
	public bool Selected { get; set; } = false;

	/// <summary>
	/// Reserves space for the check icon even if it is not currently checked
	/// </summary>
	public bool Checkable { get; set; } = false;

	/// <summary>
	/// Shortcut which automatically sets the left icon to check and the left icon color to the default checked color
	/// </summary>
	public bool Checked { get; set; } = false;

	/// <summary>
	/// Reserves space for the sort icon even if it is not currently sorted
	/// </summary>
	public bool Sortable { get; set; } = false;

	/// <summary>
	/// Should there be a sorting indicator and if yes, which direction should it point to?
	/// </summary>
	public DataStoreElementSortDirection? Sorting { get; set; }

	/// <summary>
	/// Optional custom left padding (currently needed for search inputs only)
	/// </summary>
	public string? PaddingLeft { get; set; }
	
	public Dictionary<string, string>? ExtraData { get; set; } = new();

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-menu-item";

		if (Label != null)
			output.Attributes.SetAttribute("label", Label);
		if (Action != null)
			output.Attributes.SetAttribute("data-action", Action);
		if (IconLeft != null)
			output.Attributes.SetAttribute("icon-left", IconLeft);
		if (IconLeftColor != null)
			output.Attributes.SetAttribute("icon-left-color", IconLeftColor);
		if (IconRight != null)
			output.Attributes.SetAttribute("icon-right", IconRight);
		if (IconRightColor != null)
			output.Attributes.SetAttribute("icon-right-color", IconRightColor);
		if (Legend != null)
			output.Attributes.SetAttribute("legend", Legend);
		if (ClickFunction != null)
			output.Attributes.SetAttribute("onclick", $"{ClickFunction}.call(this, event)");
		if (Selected)
			output.Attributes.SetAttribute("selected", "");
		if (Checkable)
			output.Attributes.SetAttribute("checkable", "");
		if (Checked)
			output.Attributes.SetAttribute("checked", "");
		if (Sortable)
			output.Attributes.SetAttribute("sortable", "");
		if (Sorting != null)
			output.Attributes.SetAttribute("sorting", Sorting.Value == DataStoreElementSortDirection.Asc ? "asc" : "desc");
		if (PaddingLeft != null)
			output.Attributes.SetAttribute("padding-left", PaddingLeft);
		ExtraData?.ForEach( extraData =>
		{
			output.Attributes.SetAttribute($"data-{extraData.Key}", extraData.Value.ToString());
		});

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}

		if (Label != null) 
			output.Content.SetContent(Label);
	}
}