// ReSharper disable UnusedAutoPropertyAccessor.Global
namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;

/// <summary>
/// Static value definition for a select list item
/// </summary>
public class SelectListItemDefinition
{
	/// <summary>
	/// Human-readable string representation for the value.
	/// </summary>
	public string Label { get; init; }

	/// <summary>
	/// Value for a list item
	/// </summary>
	public string Value { get; init; }
	
	/// <summary>
	/// Icon for a list item 
	/// </summary>
	public string? Icon { get; init; }

	/// <summary>
	/// Default constructor
	/// </summary>
	/// <param name="value"></param>
	/// <param name="label"></param>
	/// <param name="icon"></param>
	public SelectListItemDefinition(string label, string value, string? icon = null)
	{
		Label = label;
		Value = value;
		Icon = icon;
	}
}