using Humanizer;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;

/// <summary>
/// Extensions for the TagHelper Class
/// </summary>
public static class TagHelperExtension
{
	/// <summary>
	/// returns all attributes currently associated with the tagHelper
	/// </summary>
	/// <param name="tagHelper"></param>
	/// <param name="context"></param>
	/// <returns></returns>
	public static Dictionary<string, string> GetAttributeList(this TagHelper tagHelper, TagHelperContext context)
	{
		var attributeObjects = context.AllAttributes.ToList();
		var props = tagHelper.GetType().GetProperties().Select(p => p.Name.Kebaberize());
		attributeObjects.RemoveAll(a => props.Contains(a.Name.ToLower()));
		return attributeObjects.ToDictionary(attr => attr.Name, attr => attr.Value.ToString() ?? "");
	}

	/// <summary>
	/// Creates default TageHelperContext and TagHelperOutput for use in Tests
	/// </summary>
	/// <param name="tagHelper">the tag helper</param>
	/// <returns></returns>
	public static string CreateTagHelperOutput(this TagHelper tagHelper)
	{
		var tagHelperContext = new TagHelperContext(
			new TagHelperAttributeList(),
			new Dictionary<object, object>(),
			Guid.NewGuid().ToString("N"));
		var output = new TagHelperOutput(
			"custom",
			new TagHelperAttributeList(),
			(_, _) =>
			{
				var tagHelperContent = new DefaultTagHelperContent();
				return Task.FromResult<TagHelperContent>(tagHelperContent);
			});

		tagHelper.Process(tagHelperContext, output);
		return $"<{output.TagName} { string.Join(" ", output.Attributes.Select(attribute => BuildAttribute(attribute.Name, attribute.Value)))}>{output.Content.GetContent()}</{output.TagName}>";

		string BuildAttribute(string name, object? value) => string.IsNullOrEmpty(value?.ToString()) ? $"{name}" : $"{name}=\"{value.ToString()!.Replace("\"", "&quot;")}\"";
	}
}