using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// Small TagHelper to render <lvl-form/>
/// </summary>
[ExcludeFromCodeCoverage]
public class FormComponentTagHelper : TagHelper
{
	/// <summary>
	/// Unique id of the form
	/// </summary>
	public string? Id { get; set; }

	/// <summary>
	/// should the form be displayed as a skeleton?
	/// </summary>
	public bool Skeleton { get; set; }
	
	/// <summary>
	/// should the form manage enabling/disabling the save button?
	/// </summary>
	public bool HandleSaveButton { get; set; }
	
	/// <summary>
	/// Url to made patches of the content
	/// </summary>
	public string? ApiUrl { get; set; }
	
	/// <summary>
	/// Disable all inputs
	/// </summary>
	public bool Disabled { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-form";
		output.Attributes.SetAttribute("class", "form");
		output.Attributes.SetAttribute("id", Id);
		if (Skeleton)
			output.Attributes.Add("skeleton", "");
		if (!string.IsNullOrEmpty(ApiUrl))
			output.Attributes.Add("api-url", ApiUrl);
		if (HandleSaveButton)
			output.Attributes.Add("handle-save-button", "");
		if (Disabled)
			output.Attributes.Add("disabled", "");
	}
}