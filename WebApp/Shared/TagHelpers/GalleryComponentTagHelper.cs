using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Newtonsoft.Json;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-list
/// </summary>
public class GalleryComponentTagHelper : TagHelper
{
	/// <summary>
	/// This list will be displayed in the enumeration view as alternative to work without data fetching
	/// </summary>
	public List<Dictionary<string, object>>? Rows { get; set; }

	/// <summary>
	/// Used to update single data
	/// </summary>
	public string? IdentityColumn { get; set; }

	/// <summary>
	/// Renders the whole component as skeleton
	/// </summary>
	public bool Skeleton { get; set; } = false;

	/// <summary>
	/// Different compact style for using the table within a form
	/// </summary>
	public bool Embedded { get; set; } = false;

	/// <summary>
	/// Display a create-card
	/// </summary>
	public bool AllowCreate { get; set; } = false;

	/// <summary>
	/// Allow to click on a card
	/// </summary>
	public bool Clickable { get; set; } = false;

	/// <summary>
	/// Allow to select on a card
	/// </summary>
	public bool Selectable { get; set; } = false;

	/// <summary>
	/// May the user favour his records?
	/// </summary>
	public bool AllowFavorite { get; set; } = false;

	/// <summary>
	/// May the user discard records?
	/// </summary>
	public bool AllowInactive { get; set; } = false;

	/// <summary>
	/// Display the current workflows as icons
	/// </summary>
	public bool DisplayWorkflow { get; set; } = false;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-gallery";

		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");
		if (IdentityColumn != null)
			output.Attributes.SetAttribute("identity-column", IdentityColumn);
		if (Rows?.Count > 0)
		{
			var rowString = JsonConvert.SerializeObject(Rows);
			output.Attributes.SetAttribute("rows", rowString);
		}
		if (Embedded)
			output.Attributes.SetAttribute("embedded", "");
		if (AllowCreate)
			output.Attributes.SetAttribute("allow-create", "");
		if (Clickable)
			output.Attributes.SetAttribute("clickable", "");
		if (Selectable)
			output.Attributes.SetAttribute("selectable", "");
		if (AllowFavorite)
			output.Attributes.SetAttribute("allow-favorite", "");
		if (AllowInactive)
			output.Attributes.SetAttribute("allow-inactive", "");
		if (DisplayWorkflow)
			output.Attributes.SetAttribute("display-workflow", "");

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}