using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// Tag helper for InfoElement
/// </summary>
[ExcludeFromCodeCoverage]
public class InfoElementTagHelper : TagHelper
{
	/// <summary>
	/// label of the info element
	/// </summary>
	public string? Label { get; set; }
	
	/// <summary>
	/// more detailed description
	/// </summary>
	public string? Text { get; set; }
	
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		var labelElement = string.IsNullOrEmpty(Label) ? "" : $"<label>{Label}</label>";
		
		output.TagName = "div";
		output.Attributes.SetAttribute("class", "info__item");
		output.Content.AppendHtml($"{labelElement}<span>{Text}</span>");
	}
}