using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.OpenApi.Extensions;

// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-input
/// </summary>
public class InputComponentTagHelper : TagHelper
{
	/// <summary>
	/// Unique name of the field
	/// </summary>
	public string? Name { get; set; }

	/// <summary>
	/// Label displayed above the input
	/// </summary>
	public string? Label { get; set; }

	/// <summary>
	/// Value which gets parsed to the configured data type
	/// </summary>
	public string? Value { get; set; }
	
	/// <summary>
	/// Default Value displayed on load
	/// </summary>
	public string? Default { get; set; }

	/// <summary>
	/// Optional unit symbol
	/// </summary>
	public string? Sign { get; set; }

	/// <summary>
	/// Data type of the field
	/// </summary>
	public InputDataType? Type { get; set; }

	/// <summary>
	/// Should text inside the input be aligned to the left or the right
	/// </summary>
	public Alignment? TextAlign { get; set; }
	
	/// <summary>
	/// Should the value be matched against a set regex?
	/// </summary>
	public Validator? Validator { get; set; }
	
	/// <summary>
	/// Should the value be matched against a custom regex?
	/// </summary>
	public string? ValidationRegex { get; set; }

	/// <summary>
	/// Is this a mandatory field?
	/// </summary>
	public bool Required { get; set; } = false;

	/// <summary>
	/// If set to true it is not possible to change the input value
	/// </summary>
	public bool Readonly { get; set; } = false;
	
	/// <summary>
	/// Is this a multiValue field?
	/// </summary>
	public bool MultiValue { get; set; } = false;

	/// <summary>
	/// if set to true, the input will not display extra features, like error messages or (error) tooltips
	/// </summary>
	public bool Compact { get; set; } = false;

	/// <summary>
	/// Placeholder displayed if the field is empty
	/// </summary>
	public string? Placeholder { get; set; }

	/// <summary>
	/// Tooltip displayed when hovering the help icon next to the toggle switch
	/// </summary>
	public string? Tooltip { get; set; }

	/// <summary>
	/// Makes it possible to set an error from outside the control (for example via rule action)
	/// </summary>
	public string? Error { get; set; }
	
	/// <summary>
	/// Prefix/Root for the translation
	/// </summary>
	public string? TranslationPrefix { get; set; }

	/// <summary>
	/// Compare operator (available options vary based on the field type)
	/// </summary>
	public CompareOperator? CompareOperator { get; set; }

	/// <summary>
	/// Are negative operators allowed?
	/// </summary>
	public bool NegativeOperators { get; set; } = false;
	
	/// <summary>
	/// Is the input part of the page designer and displayed without being connected to a DataField
	/// </summary>
	public bool Disconnected { get; set; } = false;
	
	/// <summary>
	/// Is used to distinguish between (Date)time and (Date)timeFixed
	/// </summary>
	public bool IgnoreTimezone { get; set; } = false;
	
	/// <summary>
	/// indicates whether it is a virtual field and on which lookup field it is based
	/// </summary>
	public string? LookupField { get; set; }
	
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-input";

		if (!string.IsNullOrEmpty(Value))
			output.Attributes.SetAttribute("value", Value);
		
		if (!string.IsNullOrEmpty(Default))
			output.Attributes.SetAttribute("default", Default);

		if (Name != null)
			output.Attributes.SetAttribute("name", Name);
		if (Label != null)
			output.Attributes.SetAttribute("label", Label);
		if (Type != null)
			output.Attributes.SetAttribute("type", Type.Value.GetTypeAsString());
		if (!string.IsNullOrEmpty(Sign))
			output.Attributes.SetAttribute("sign", Sign);
		if (TextAlign != null)
			output.Attributes.SetAttribute("text-align", TextAlign.Value.GetAlignmentAsString());
		if (Validator != null || ValidationRegex != null)
		{
			var regex = Validator != null ? Validator.Value.GetPattern() : ValidationRegex;
			output.Attributes.SetAttribute("validator", regex);	
		}
		if (!string.IsNullOrEmpty(Placeholder))
			output.Attributes.SetAttribute("placeholder", Placeholder);
		if (!string.IsNullOrEmpty(Tooltip))
			output.Attributes.SetAttribute("tooltip", Tooltip);
		if (TranslationPrefix != null)
			output.Attributes.SetAttribute("translation-prefix", TranslationPrefix);
		if (Error != null)
			output.Attributes.SetAttribute("error", Error);
		if (Required)
			output.Attributes.SetAttribute("required", "");
		if (Readonly)
			output.Attributes.SetAttribute("readonly", "");
		if (MultiValue)
			output.Attributes.SetAttribute("multi-value", "");
		if (Compact)
			output.Attributes.SetAttribute("compact", "");
		if (Disconnected)
			output.Attributes.SetAttribute("disconnected", "");
		if (NegativeOperators)
			output.Attributes.SetAttribute("negative-operators", "");
		if (IgnoreTimezone)
			output.Attributes.SetAttribute("ignore-timezone", "");
		if (!string.IsNullOrEmpty(LookupField))
			output.Attributes.SetAttribute("lookup-field", LookupField);
		if (CompareOperator != null)
		{
			if (Type != null && (Type.Value.IsNumeric() && CompareOperator.Value.IsNumeric() || Type.Value.IsText() && CompareOperator.Value.IsText()) &&
				(NegativeOperators || !CompareOperator.Value.IsNegative()))
			{
				output.Attributes.SetAttribute("compare-operator", CompareOperator.Value.GetDisplayName().ToLower());
			}
			else
			{
				output.Attributes.SetAttribute("compare-operator", Core.SharedDtos.Enums.CompareOperator.Equals.GetDisplayName().ToLower());
			}
		}

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}