using System.Text;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-list-column
/// </summary>
public class ListColumnComponentTagHelper : BaseColumnComponentTagHelper
{
	/// <summary>
	/// Used for predefined special columns
	/// </summary>
	public ListColumnType? Type { get; set; }

	/// <summary>
	/// List of options for dropdowns in menu component
	/// </summary>
	public List<DropdownMenuItemComponentTagHelper>? Options { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-list-column";
		
		if (Type != null)
			output.Attributes.SetAttribute("type", Type.Value.GetListColumnTypeAsString());
		ProcessBaseOutput(output, context);
		
		if (Options == null || Options.Count == 0)
			return;
		
		// build the options for the dropdown
		var stringBuilder = new StringBuilder();
		stringBuilder.Append("<lvl-menu>");
		foreach (var option in Options)
		{
			stringBuilder.Append(option.CreateTagHelperOutput());
		}
		stringBuilder.Append("</lvl-menu>");
		output.Content.AppendHtml(stringBuilder.ToString());
	}
}