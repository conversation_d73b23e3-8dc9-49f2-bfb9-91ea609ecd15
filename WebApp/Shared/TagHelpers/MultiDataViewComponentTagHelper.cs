using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-multi-data-view
/// </summary>
public class MultiDataViewComponentTagHelper : QueryViewTagHelper
{
	/// <summary>
	/// Different compact style for using the table within a form
	/// </summary>
	public bool Embedded { get; set; } = false;
	
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-multi-data-view";
		
		if (Embedded)
			output.Attributes.SetAttribute("embedded", "");

		ProcessQueryOutput(context, output);
	}
}