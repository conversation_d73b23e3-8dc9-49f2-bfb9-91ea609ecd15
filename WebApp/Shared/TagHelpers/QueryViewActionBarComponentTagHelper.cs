using System.Text.Json;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-query-view-action-bar
/// </summary>
public class QueryViewActionBarComponentTagHelper : TagHelper
{
	/// <summary>
	/// disables all actions
	/// </summary>
	public bool Disabled { get; set; } = false;
	
	/// <summary>
	/// adds a select all button
	/// </summary>
	public bool WithSelectAll { get; set; } = false;
	
	/// <summary>
	/// adds a favorite action button
	/// </summary>
	public bool WithFavoriteAction { get; set; } = false;
	
	/// <summary>
	/// adds a inactive action button
	/// </summary>
	public bool WithInactiveAction { get; set; } = false;
	
	/// <summary>
	/// adds a search option
	/// </summary>
	public bool WithSearch { get; set; } = false;
	
	/// <summary>
	/// adds a dropdown to show and hide columns
	/// </summary>
	public bool WithColumns { get; set; } = false;
	
	/// <summary>
	/// adds an option to switch between different display types, likes table, grid and others
	/// </summary>
	public bool WithDisplayType { get; set; } = false;
	
	/// <summary>
	/// adds a create button
	/// </summary>
	public bool WithCreate { get; set; } = false;
	
	/// <summary>
	/// Display as embedded bar?
	/// </summary>
	public bool Embedded { get; set; } = false;
	
	/// <summary>
	/// Initial view which is selected in the dropdown
	/// </summary>
	public string? DefaultView { get; set; }
	
	/// <summary>
	/// List of values which are passed to the column as possible static options 
	/// </summary>
	public IList<SelectListItemDefinition>? Views { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-query-view-action-bar";
		
		if (Disabled)
			output.Attributes.SetAttribute("disabled", "");
		if (WithSelectAll)
			output.Attributes.SetAttribute("with-select-all", "");
		if (WithFavoriteAction)
			output.Attributes.SetAttribute("with-favorite-action", "");
		if (WithInactiveAction)
			output.Attributes.SetAttribute("with-inactive-action", "");
		if (WithSearch)
			output.Attributes.SetAttribute("with-search", "");
		if (WithColumns)
			output.Attributes.SetAttribute("with-columns", "");
		if (WithDisplayType)
			output.Attributes.SetAttribute("with-display-type", "");
		if (WithCreate)
			output.Attributes.SetAttribute("with-create", "");
		if (Embedded)
			output.Attributes.SetAttribute("embedded", "");
		if (!string.IsNullOrEmpty(DefaultView))
			output.Attributes.SetAttribute("default-view", DefaultView);
		
		if (Views is { Count: > 0 })
		{
			var filterString = JsonSerializer.Serialize(Views, ConfigHelper.JsonOptionsCamel);
			output.Attributes.SetAttribute("views", filterString);
		}

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}