using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-rich-text
/// </summary>
public class RichTextComponentTagHelper : TagHelper
{
	/// <summary>
	/// Unique name of the field
	/// </summary>
	public string? Name { get; set; }
	
	/// <summary>
	/// Label displayed above the input
	/// </summary>
	public string? Label { get; set; }
	
	/// <summary>
	/// Value which gets parsed to the configured data type
	/// </summary>
	public string? Value { get; set; }
	
	/// <summary>
	/// Default Value displayed on load
	/// </summary>
	public string? Default { get; set; }

	/// <summary>
	/// Which height should the rich-text be?
	/// </summary>
	public Size? MinimumSize { get; set; }
	
	/// <summary>
	/// Is this a mandatory field?
	/// </summary>
	public bool Required { get; set; } = false;
	
	/// <summary>
	/// If set to true, it is not possible to change the input value
	/// </summary>
	public bool Readonly { get; set; } = false;
	
	/// <summary>
	/// Placeholder displayed if field is empty
	/// </summary>
	public string? Placeholder { get; set; }
	
	/// <summary>
	/// indicates whether it is a virtual field and on which lookup field it is based
	/// </summary>
	public string? LookupField { get; set; }
	
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-rich-text";
		
		if (!string.IsNullOrEmpty(Value))
			output.Attributes.SetAttribute("value", Value);
		if (!string.IsNullOrEmpty(Default))
			output.Attributes.SetAttribute("default", Default);
		
		if (Name != null)
			output.Attributes.SetAttribute("name", Name);
		if (Label != null)
			output.Attributes.SetAttribute("label", Label);
		if (MinimumSize != null)
			output.Attributes.SetAttribute("size", MinimumSize.Value.GetSizeAsString());
		if (Placeholder != null)
			output.Attributes.SetAttribute("placeholder", Placeholder);
		if (Required)
			output.Attributes.SetAttribute("required", "");
		if (Readonly)
			output.Attributes.SetAttribute("readonly", "");
		if (!string.IsNullOrEmpty(LookupField))
			output.Attributes.SetAttribute("lookup-field", LookupField);
		
		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}