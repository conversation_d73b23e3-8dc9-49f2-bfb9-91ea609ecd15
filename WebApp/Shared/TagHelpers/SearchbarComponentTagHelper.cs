using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-search
/// </summary>
public class SearchbarComponentTagHelper : TagHelper
{
	/// <summary>
	/// If set to true, it is not possible to change the input value
	/// </summary>
	public bool Disabled { get; set; } = false;
	
	/// <summary>
	/// Adds an icon for sorting purposes
	/// </summary>
	public bool WithSorting { get; set; } = false;
	
	/// <summary>
	/// Adds CTRL+F functionality
	/// </summary>
	public bool MainBar { get; set; } = false;
	
	/// <summary>
	/// Display searchbar as pill?
	/// </summary>
	public bool Rounded { get; set; } = false;
	
	/// <summary>
	/// Height of the button
	/// </summary>
	public FontSize? Size { get; set; }
	
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-search";
		
		if (Size.HasValue)
			output.Attributes.SetAttribute("size", Size.Value.GetFontSizeAsString());
		if (WithSorting)
			output.Attributes.SetAttribute("with-sorting", "");
		if (MainBar)
			output.Attributes.SetAttribute("main-bar", "");
		if (Rounded)
			output.Attributes.SetAttribute("rounded", "");
		if (Disabled)
			output.Attributes.SetAttribute("disabled", "");
		
		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}