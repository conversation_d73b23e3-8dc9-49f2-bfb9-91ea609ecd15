using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-slide-out
/// </summary>
public class SlideOutComponentTagHelper : TagHelper
{
	/// <summary>
	/// Optional headline of the slide out
	/// </summary>
	public string? Heading { get; set; }
	
	/// <summary>
	/// Icon which would be displayed left of the headline
	/// </summary>
	public string? Icon { get; set; }
	
	/// <summary>
	/// Should the slide out open on the left or the right of the window
	/// </summary>
	public Alignment? Position { get; set; }
	
	/// <summary>
	/// Should the slide out be anchored to the buttons' container?
	/// </summary>
	public bool Anchor { get; set; } = false;
	
	/// <summary>
	/// If set to true, the slide out becomes modal. This means, that it is not possible to interact with the rest of the application, without closing the slide out. 
	/// </summary>
	public bool Modal { get; set; } = false;
	
	/// <summary>
	/// Is the slide opened?
	/// </summary>
	public bool Open { get; set; } = false;
	
	/// <summary>
	/// Optional fixed width of the slide out.
	/// </summary>
	public double? Width { get; set; }
	
	/// <summary>
	/// Navigation Buttons for switching between records
	/// </summary>
	public bool Navigation { get; set; }
	
	/// <summary>
	/// Current Record/Page which is used for navigation buttons
	/// </summary>
	public int? PageNumber { get; set; } = 0;
	
	/// <summary>
	/// Maximum Record/Page which is used for navigation buttons
	/// </summary>
	public int? PageCount { get; set; } = 0;
	
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-slide-out";
		
		if (Heading != null)
			output.Attributes.SetAttribute("heading", Heading);
		if (Icon != null)
			output.Attributes.SetAttribute("icon", Icon);
		if (Width != null)
			output.Attributes.SetAttribute("width", Width);
		if (Position != null)
			output.Attributes.SetAttribute("position", ((Alignment)Position).GetAlignmentAsString());
		if (Anchor)
			output.Attributes.SetAttribute("anchor", "");
		if (Modal)
			output.Attributes.SetAttribute("modal", "");
		if (Open)
			output.Attributes.SetAttribute("open", "");
		if (Navigation)
			output.Attributes.SetAttribute("navigation", "");
		if (PageNumber != null)
			output.Attributes.SetAttribute("page-number", PageNumber);
		if (PageCount != null)
			output.Attributes.SetAttribute("page-count", PageCount);
		
		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}