using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Newtonsoft.Json;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-list
/// </summary>
public class TableComponentTagHelper : TagHelper
{
	/// <summary>
	/// This list will be displayed in the enumeration view as alternative to work without data fetching
	/// </summary>
	public List<Dictionary<string, object>>? Rows { get; set; }
	
	/// <summary>
	/// Width of the enumeration wrapper with units
	/// </summary>
	public string? Width { get; set; }

	/// <summary>
	/// Height of the enumeration wrapper with units
	/// </summary>
	public string? Height { get; set; }

	/// <summary>
	/// Used to update single data
	/// </summary>
	public string? IdentityColumn { get; set; }

	/// <summary>
	/// Renders the whole component as skeleton
	/// </summary>
	public bool Skeleton { get; set; } = false;
	
	/// <summary>
	/// Different compact style for using the table within a form
	/// </summary>
	public bool Embedded { get; set; } = false;
	
	/// <summary>
	/// Hides the header of the table
	/// </summary>
	public bool NoHeader { get; set; } = false;
	
	/// <summary>
	/// Allow to click on a row
	/// </summary>
	public bool Clickable { get; set; } = false;
	
	/// <summary>
	/// May the user favour his records?
	/// </summary>
	public bool AllowFavorite { get; set; } = false;
	
	/// <summary>
	/// May the user discard records?
	/// </summary>
	public bool AllowInactive { get; set; } = false;
	
	/// <summary>
	/// Display in expert mode?
	/// </summary>
	public bool ExpertMode { get; set; } = false;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-table";
		
		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");
		if (IdentityColumn != null)
			output.Attributes.SetAttribute("identity-column", IdentityColumn);
		if (Rows?.Count > 0)
		{
			var rowString = JsonConvert.SerializeObject(Rows);
			output.Attributes.SetAttribute("rows", rowString);
		}
		if (Embedded)
			output.Attributes.SetAttribute("embedded", "");
		if (NoHeader)
			output.Attributes.SetAttribute("no-header", "");
		if (Clickable)
			output.Attributes.SetAttribute("clickable", "");
		if (AllowFavorite)
			output.Attributes.SetAttribute("allow-favorite", "");
		if (AllowInactive)
			output.Attributes.SetAttribute("allow-inactive", "");
		if (ExpertMode)
			output.Attributes.SetAttribute("expert-mode", "");
		
		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}

		// Set Width and Height as style
		var styleList = new List<string>();
		if (!string.IsNullOrEmpty(Width))
		{
			styleList.Add($"width: {Width};");
		}

		if (!string.IsNullOrEmpty(Height))
		{
			styleList.Add($"height: {Height};");
		}

		if (styleList.Count > 0)
		{
			output.Attributes.SetAttribute("style", string.Join(" ", styleList));
		}
	}
}