using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-tooltip
/// </summary>
public class TooltipComponentTagHelper : TagHelper
{
	/// <summary>
	/// This name must be set in the component that calls the dropdown as 'data-tooltip='
	/// </summary>
	public string? Name { get; set; }

	/// <summary>
	/// Default placement position if there is enough space
	/// </summary>
	public PopupPlacement? Placement { get; set; }

	/// <summary>
	/// Offset if tooltip is placed on top / below the trigger element. Combined X/Y-Value.
	/// </summary>
	public int? BlockOffset { get; set; }

	/// <summary>
	/// Offset if tooltip is placed on top / below the trigger element. X-Value.
	/// </summary>
	public int? BlockOffsetX { get; set; }

	/// <summary>
	/// Offset if tooltip is placed on top / below the trigger element. Y-Value.
	/// </summary>
	public int? BlockOffsetY { get; set; }

	/// <summary>
	/// Offset if tooltip is placed left / right of the trigger element. Combined X/Y-Value
	/// </summary>
	public int? InlineOffset { get; set; }

	/// <summary>
	/// Offset if tooltip is placed left / right of the trigger element. X-Value
	/// </summary>
	public int? InlineOffsetX { get; set; }

	/// <summary>
	/// Offset if tooltip is placed left / right of the trigger element. Y-Value
	/// </summary>
	public int? InlineOffsetY { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-tooltip";

		if (Name != null)
			output.Attributes.SetAttribute("name", Name);
		if (Placement != null)
			output.Attributes.SetAttribute("placement", Placement.Value.GetPlacementAsString());
		if (BlockOffset != null)
			output.Attributes.SetAttribute("offset-block", BlockOffset);
		if (BlockOffsetX != null)
			output.Attributes.SetAttribute("offset-block-x", BlockOffsetX);
		if (BlockOffsetY != null)
			output.Attributes.SetAttribute("offset-block-y", BlockOffsetY);
		if (InlineOffset != null)
			output.Attributes.SetAttribute("offset-inline", InlineOffset);
		if (InlineOffsetX != null)
			output.Attributes.SetAttribute("offset-inline-x", InlineOffsetX);
		if (InlineOffsetY != null)
			output.Attributes.SetAttribute("offset-inline-y", InlineOffsetY);

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}