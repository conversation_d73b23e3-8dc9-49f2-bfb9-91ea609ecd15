using System.Text;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// Tag helper for workflow item component
/// </summary>
public class WorkflowItemTagHelper : TagHelper
{
	/// <summary>
	/// The workflow's id
	/// </summary>
	public string? WorkflowId { get; set; }
	
	/// <summary>
	/// The workflow's name
	/// </summary>
	public string? WorkflowName { get; set; }

	/// <summary>
	/// The label
	/// </summary>
	public string? Label { get; set; }

	/// <summary>
	/// The currently displayed state
	/// </summary>
	public WorkflowNodeState? State { get; set; }

	/// <summary>
	/// Available workflow nodes
	/// </summary>
	public ICollection<WorkflowNodeDto>? Nodes { get; set; }

	/// <summary>
	/// The currently displayed icon
	/// </summary>
	public string? Icon { get; set; }
	
	/// <summary>
	/// The icon's color
	///
	/// Will default to predefined state colors if empty
	/// </summary>
	public string? IconColor { get; set; }

	/// <summary>
	/// The item's color
	///
	/// Will default to predefined state colors if empty
	/// </summary>
	public string? ItemColor { get; set; }

	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-workflow-item";

		if(!string.IsNullOrWhiteSpace(WorkflowId))
			output.Attributes.Add("workflow-id", WorkflowId);
		
		if (!string.IsNullOrEmpty(WorkflowName))
			output.Attributes.SetAttribute("workflow-name", WorkflowName);

		if (!string.IsNullOrEmpty(Label))
			output.Attributes.SetAttribute("label", Label);

		if (State != null)
			output.Attributes.SetAttribute("state-type", State.Value.GetTypeAsString());

		if (!string.IsNullOrEmpty(Icon))
			output.Attributes.SetAttribute("icon", Icon);

		if (!string.IsNullOrEmpty(IconColor))
			output.Attributes.SetAttribute("icon-color", IconColor);

		if (!string.IsNullOrEmpty(ItemColor))
			output.Attributes.SetAttribute("item-color", ItemColor);

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}

		if (Nodes == null || Nodes?.Count == 0)
			return;
		
		var menuItems = Nodes!.Select(node => new DropdownMenuItemComponentTagHelper
		{
			IconLeft = node.Icon,
			Label = node.NameTranslated,
			ClickFunction = "changeWorkflowStatus",
			ExtraData = new Dictionary<string, string>
			{
				{ "id", node.Id?.ToString() ?? "" },
				{ "name", node.NameTranslated! },
				{ "state", node.State.ToString()! },
				{ "icon", node.Icon! },
			}
		}).ToList();

		var stringBuilder = new StringBuilder();
		menuItems.ForEach(menuItem => stringBuilder.Append(menuItem.CreateTagHelperOutput()));
		output.Content.AppendHtml(stringBuilder.ToString());
	}
}