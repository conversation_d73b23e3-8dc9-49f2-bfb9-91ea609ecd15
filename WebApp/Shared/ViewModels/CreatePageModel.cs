using System.Diagnostics.CodeAnalysis;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Shared.ViewModels;

[ExcludeFromCodeCoverage]
public class CreatePageModel
{
	public string? CreatePageId { get; init; }
	
	public string? SaveButtonLabel { get; init; }
	
	public bool AllowFile { get; init; }
	
	public bool RenderCreatePage { get; init; }
	
	public Guid? DataSourceId { get; init; }
	
	public string? KeyField { get; init; }
	
	public string? ParentElementId { get; init; }

	public CreatePageModel(string? createPageId = null, string? saveButtonLabel = null, bool? allowFile = false, bool? renderCreatePage = false, Guid? dataSourceId = null, string? keyField = null, string? parentElementId = null)
	{
		CreatePageId = createPageId;
		SaveButtonLabel = saveButtonLabel;
		AllowFile = allowFile == true;
		RenderCreatePage = renderCreatePage == true;
		DataSourceId = dataSourceId;
		KeyField = keyField;
		ParentElementId = parentElementId;
	}
}