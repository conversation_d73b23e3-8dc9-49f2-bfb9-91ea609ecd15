using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminDetailPage;

/// <summary>
/// Component for the configuration detail template
/// </summary>
public class AdminDetailPageViewComponent : ViewComponent
{
	/// <summary>
	/// Default method to render a view component
	/// </summary>
	/// <param name="entity">Name of the WebAppEntity in PascalCase</param>
	/// <param name="model">ViewModel passed from the controller</param>
	/// <param name="routeName">Entity name which is used in url routes</param>
	/// <param name="title">Representative string for title and labeling</param>
	/// <param name="menuItem">Do you start in a specific menu item view?</param>
	/// <param name="viewName">name (and optionally path) to the sub-page to render</param>
	/// <param name="showDefaultButtons">Displays the default buttons in the header</param>
	/// <param name="useCustomLayout">default centered layout or custom design?</param>
	/// <param name="showDeletionWarningToast">Should the user receive a warning after clicking delete?</param>
	/// <param name="deletionWarningToastHeading">The heading of the deletion warning</param>
	/// <param name="deletionWarningToastMessage">The message of the deletion warning</param>
	/// <returns></returns>
	public IViewComponentResult Invoke(string entity, object model, string routeName, string title, string? menuItem = null, bool? showDefaultButtons = false,
									   string? viewName = null, bool? useCustomLayout = false, bool? showDeletionWarningToast = false, string? deletionWarningToastHeading = "", string? deletionWarningToastMessage = "")
	{
		if (!string.IsNullOrEmpty(viewName) && viewName.EndsWith("/"))
			viewName = viewName.Substring(0, viewName.Length - 1);
				
		AdminDetailPageModel componentModel = new()
		{
			EntityName = entity,
			ViewModel = model,
			ViewName = viewName,
			RouteName = routeName,
			Title = title,
			MenuItem = menuItem,
			ShowDefaultButtons = showDefaultButtons == true,
			UseCustomLayout = useCustomLayout == true,
			ShowDeletionWarningToast = showDeletionWarningToast == true,
			DeletionWarningToastHeading = deletionWarningToastHeading ?? string.Empty,
			DeletionWarningToastMessage = deletionWarningToastMessage ?? string.Empty
		};

		return View(componentModel);
	}
}

/// <summary>
/// Model for the configuration detail template
/// </summary>
public class AdminDetailPageModel
{
	/// <summary>
	/// Name of the WebAppEntity in PascalCase
	/// </summary>
	public required string EntityName { get; init; }

	/// <summary>
	/// Entity name which is used in url routes
	/// </summary>
	public required string RouteName { get; init; }

	/// <summary>
	/// Representative string for title and labeling
	/// </summary>
	public required string Title { get; init; }

	/// <summary>
	/// Sometimes we need to set the view path explicitly
	/// </summary>
	public string? ViewName { get; init; }

	/// <summary>
	/// ViewModel passed from the controller
	/// </summary>
	public required object ViewModel { get; init; }

	/// <summary>
	/// Do you start in a specific menu item view?
	/// </summary>
	public string? MenuItem { get; init; }

	/// <summary>
	/// Displays the default buttons in the header
	/// </summary>
	public bool ShowDefaultButtons { get; init; }
	
	/// <summary>
	/// Default centered layout or custom design?
	/// </summary>
	public bool UseCustomLayout { get; set; }
	
	/// <summary>
	/// Should the user receive a warning after clicking delete?
	/// </summary>
	public bool ShowDeletionWarningToast { get; init; }
	
	/// <summary>
	/// The heading of the deletion warning
	/// </summary>
	public required string DeletionWarningToastHeading { get; init; }
	
	/// <summary>
	/// The message of the deletion warning
	/// </summary>
	public required string DeletionWarningToastMessage { get; init; }
}