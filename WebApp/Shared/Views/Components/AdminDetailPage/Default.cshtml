@using System.Text.Json
@using Humanizer
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared
@using Levelbuild.Frontend.WebApp.Shared.Utils
@model Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminDetailPage.AdminDetailPageModel
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var scriptLocalizer = LocalizerFactory.Create("ScriptMessages", "");
	var apiUrl = $"/Api/{Model.RouteName}";
	var kebabEntity = Model.EntityName.Kebaberize();
}
<!-- Script above the html is on purpose because inner views have their own scripts and so we get the right execution order -->
<!--suppress JSDuplicatedDeclaration -->
<script type="module" defer>
	@if (PageUtils.TryGetPropertyValue(Model.ViewModel, Model.EntityName, out var entityDto))
	{
		<text>
			Page.setTitle('@Model.Title')
			let formData = @(Html.Raw(JsonSerializer.Serialize(entityDto, ConfigHelper.JsonOptionsCamel)))
			// if there are config options in a subtree than we want to flatten them
			Page.saveFormData({...formData, ...formData.options})
		</text>
	}
	
	async function handleSaveButtonClick() {
		const form = document.querySelector('#@(kebabEntity)-form, #@(kebabEntity)-detail > :not(.side-panel) lvl-form, #@(kebabEntity)-detail > :not(.side-panel) form')
		const elementId = Page.getFormData().id
		const result = await Form.storeData(form, `@(apiUrl)/${elementId}`, 'PATCH')
		if (!result)
			return
		
		// Go back to the list view if there is no other menu item to deal with
		@if (Model.MenuItem == null)
		{
			@: history.back()
		}
	}
	
	async function handleDeleteButtonClick() {
		const callback = async () => {
			Overlay.showWait("@scriptLocalizer["deleting"]")
			const elementId = Page.getFormData().id
			const result = await fetch(`@(apiUrl)/${elementId}`, { method: 'DELETE' })
			Overlay.hideWait()

			if (!result)
				return

			history.back()
		}

		@if (Model.ShowDeletionWarningToast) {
			@:Page.showDeletionWarningToast('@Model.DeletionWarningToastHeading', '@Model.DeletionWarningToastMessage', async () => await callback(), '@scriptLocalizer["deleteToastOk"]', '@scriptLocalizer["deleteToastAbort"]')
		}
		else
		{
			@:callback()
		}
	}
	
	Page.buttonConfig.saveButton.addEventListener('click', handleSaveButtonClick, { signal: Page.getPageChangeSignal() })
	Page.buttonConfig.saveButton.skeleton = false
	
	Page.buttonConfig.deleteButton.addEventListener('click', handleDeleteButtonClick, { signal: Page.getPageChangeSignal() })
	Page.buttonConfig.deleteButton.skeleton = false

	// show buttons AFTER events were appended (because Cypress waits for buttons to be visible before clicking)
	@if (Model.ShowDefaultButtons)
	{
		@:Page.buttonConfig.showAdminButtons()
	}
	
	let form = Page.getForm()
	if (form != null)
		form.apiUrl = `@(apiUrl)`;
</script>
<div id="@(kebabEntity)-detail" class="page-content">
	@await Html.PartialAsync(Model.ViewName ?? $"~/Features/{Model.EntityName}/Views/_{Model.MenuItem ?? "DetailForm"}.cshtml", Model.ViewModel)
</div>