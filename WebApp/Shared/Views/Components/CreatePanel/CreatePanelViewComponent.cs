using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel;

/// <summary>
/// Component for the create-panel template
/// </summary>
public class CreatePanelViewComponent : ViewComponent
{
	/// <summary>
	/// Default method to render a view component
	/// </summary>
	/// <param name="entity">Name of the WebAppEntity in PascalCase</param>
	/// <param name="routeName">Entity name which is used in url routes</param>
	/// <param name="localizer">Used to translate labels</param>
	/// <param name="parentPropertyName">Is the list entity a children of another Entity? Used for parent filtering</param>
	/// <param name="menuEntry">Is the access of the create-panel part of a menu view? Then what is the name of the menu</param>
	/// <param name="skeleton">should the fab button be rendered with active skeleton flag?</param>
	/// <param name="listId">ID of the list to update afterwards</param>
	/// <param name="routeParams">Optional Get Params for the URL</param>
	/// <param name="additionalParentRouteParams">Optional Get Params for the URL that come from the parent</param>
	/// <returns></returns>
	public IViewComponentResult Invoke(string entity, string routeName, IStringLocalizer localizer, string? parentPropertyName = null, string? menuEntry = null,
									   bool skeleton = false, string? listId = null, string? routeParams = null, Dictionary<string, string>? additionalParentRouteParams = null)
	{
		CreatePanelModel model = new()
		{
			EntityName = entity,
			RouteName = routeName,
			RouteParams = routeParams,
			Localizer = localizer,
			ParentPropertyName = parentPropertyName,
			MenuEntry = menuEntry,
			Skeleton = skeleton,
			ListId = listId,
			AdditionalParentRouteParams = additionalParentRouteParams
		};
		
		return View(model);
	}
}

/// <summary>
/// Model for the create-panel template
/// </summary>
public class CreatePanelModel
{
	/// <summary>
	/// Name of the WebAppEntity in PascalCase
	/// </summary>
	public required string EntityName { get; init; }

	/// <summary>
	/// Entity name which is used in url routes
	/// </summary>
	public required string RouteName { get; init; }
	
	/// <summary>
	/// Used to translate labels
	/// </summary>
	public required IStringLocalizer Localizer { get; init; }

	/// <summary>
	/// Is the list entity a children of another Entity? Used for parent filtering
	/// </summary>
	public string? ParentPropertyName { get; init; }
	
	/// <summary>
	/// Is the access of the create-panel part of a menu view? Then what is the name of the menu 
	/// </summary>
	public string? MenuEntry { get; init; }

	/// <summary>
	/// should the fab button be rendered with active skeleton flag?
	/// </summary>
	public bool Skeleton { get; init; }
	
	/// <summary>
	/// Optional custom list id (default is "{EntityName kebaberized}-list")
	/// </summary>
	public string? ListId { get; init; }
	
	/// <summary>
	/// Optional Get Params for the URL
	/// </summary>
	public string? RouteParams { get; init; }
	
	/// <summary>
	/// Optional Get Params for the URL that come from the parent
	/// </summary>
	public Dictionary<string, string>? AdditionalParentRouteParams { get; init; }
	
	/// <summary>
	/// Content handles scrolling
	/// </summary>
	public bool IgnoreOverflow { get; init; }
}