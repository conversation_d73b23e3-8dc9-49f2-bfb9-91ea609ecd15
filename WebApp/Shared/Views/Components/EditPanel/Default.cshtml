@using Humanizer
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Microsoft.IdentityModel.Tokens
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Microsoft.AspNetCore.Html
@model Levelbuild.Frontend.WebApp.Shared.Views.Components.EditPanel.EditPanelModel
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var apiUrl = $"/Api/{Model.RouteName}";
	var adminDefaultUrl = $"/Admin/{Model.RouteName}";
	var kebabEntity = Model.EntityName.Kebaberize();
	var scriptLocalizer = LocalizerFactory.Create("ScriptMessages", "");
}
<slide-out-component id="edit-panel" class="side-panel" position="@(Alignment.Right)" heading="@Model.Heading" modal anchor icon="pen-to-square" navigation
					 width="@Model.Width" open="@(ViewData["targetAction"]?.ToString() == "Detail")">
	<div class="content @(Model.IgnoreOverflow ? "" : "vanishing-scrollbar static-scrollbar")">
		@if (ViewData["targetAction"]?.ToString() == "Detail")
		{
			@await Html.PartialAsync(ViewData["targetAction"] as string, ViewData["targetViewModel"])
		}
	</div>
	<button-component slot="button-left" class="dialog-button" data-action="reset" label="@Model.Localizer["resetButton"]" type="ButtonType.Tertiary" color="ColorState.Info" skeleton="@(Model.Skeleton)"></button-component>
	<button-component slot="button-right" data-action="cancel" label="@Model.Localizer["abortButton"]" type="ButtonType.Secondary" color="ColorState.Info" skeleton="@(Model.Skeleton)"></button-component>
	<button-component slot="button-right" data-action="save" label="@Model.Localizer["saveButton"]" type="ButtonType.Primary" skeleton="@(Model.Skeleton)"></button-component>
</slide-out-component>
<script type="module" defer>
	const listElement = document.querySelector('lvl-data-list#@(kebabEntity)-list, #@(kebabEntity)-list > *')
	if (!listElement)
		throw new Error('Datalist with id: @(kebabEntity)-list was not found.')
	
	Page.registerEditPanel(document.getElementById('edit-panel'))
	
	/**
	* Click on table row opens the detail view to edit the entity configuration
	* @@param rowContent {object} complete value map of the entity configuration 
	* @@param rowIndex {number}
	*/
	listElement.onRowClick = async (rowContent, index) => {
		const displayValue = rowContent.data['@(Model.DisplayPropertyName ?? "name")']
		const slugValue = rowContent.data.slug ?? rowContent.data.id

		// are there any additional params we need to submit?
		let pageParams = {}
		@if (!Model.RouteParams.IsNullOrEmpty())
		{
			<text>
				pageParams = @(new HtmlString(Model.RouteParams))
				for(const key of Object.keys(pageParams)) {
					pageParams[key] = rowContent.data[pageParams[key]]
				}
			</text>
		}
		
		// enable skeleton loading animation
		Page.editSlideOut.skeleton = true
		
		// start to load data
		const loadData = Page.getJSON(`@(apiUrl)/${rowContent.data.id}`)
		
		// load page (if needed) and update page info
		if (!Page.editSlideOut.open)
			await Page.showEditPanel('@(adminDefaultUrl)/Edit', pageParams, displayValue)
		Page.setPanelInfo(`${Page.getMainPageUrl()}@(Model.MenuEntry != null ? "/" + Model.MenuEntry : "")/${slugValue}`, {}, displayValue )
			
		// load user info
		let form = Page.editSlideOut.querySelector('lvl-form, form')
		let json = await loadData
		await Page.setFormData(form, json.data)
		
		// set field name as panel heading (only if there is no static heading!)
		@if (Model.Heading.IsNullOrEmpty() || !Model.DisplayPropertyName.IsNullOrEmpty())
		{
			@:Page.editSlideOut.setAttribute('heading', json.data['@(Model.DisplayPropertyName ?? "name")'])
		}
		
		// set navigation
		Page.editSlideOut.setAttribute('page-number', index + 1)
		Page.editSlideOut.setAttribute('page-count', listElement.count)
		
		// disable skeleton
		Page.editSlideOut.skeleton = false
	}
	
	Page.editSlideOut.addEventListener('move-previous:click', () => {
		listElement.previous()
	})
	
	Page.editSlideOut.addEventListener('move-next:click', () => {
		listElement.next()
	})
	
	// button click handler
	const handleSaveButtonClick = async () => {
		Overlay.showWait("@scriptLocalizer["elementIsSaved"]")
		const form = Page.editSlideOut.querySelector('form, lvl-form')
		const result = await Form.storeData(form, `@(apiUrl)/${form.querySelector('.item__value[name="id"]').value}`, 'PATCH')
		Overlay.hideWait()
		if (!result)
			return
		Page.editSlideOut.open = false
		listElement.reload()
	}
	
	const saveButton = Page.editSlideOut.querySelector('[data-action="save"]')
	saveButton?.addEventListener('click', handleSaveButtonClick, { signal: Page.getPageChangeSignal() })
	Page.editSlideOut.setAttribute('initDone', '')
</script>