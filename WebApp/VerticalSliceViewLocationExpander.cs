using System.Diagnostics.CodeAnalysis;
using JetBrains.Annotations;
using Microsoft.AspNetCore.Mvc.Razor;

[assembly: AspMvcViewLocationFormat("~/Features/{1}/Views/{0}.cshtml")]
[assembly: AspMvcViewLocationFormat("~/Shared/Views/{0}.cshtml")]
[assembly: AspMvcPartialViewLocationFormat("~/Features/{1}/Views/{0}.cshtml")]
[assembly: AspMvcPartialViewLocationFormat("~/Shared/Views/{0}.cshtml")]

namespace Levelbuild.Frontend.WebApp;

/// <summary>
/// Expands the ASP.NET view locator to enable us to place views inside of Feature directories.
/// </summary>
[ExcludeFromCodeCoverage]
public class VerticalSliceViewLocationExpander : IViewLocationExpander
{
	private const string Feature = "feature";
	private const string Admin = "Admin";
	private const string Public = "Public";

	/// <inheritdoc />
	public void PopulateValues(ViewLocationExpanderContext context)
	{
		context.ActionContext.ActionDescriptor.RouteValues.TryGetValue(Feature, out var feature);

		context.Values[Feature] = feature ?? string.Empty;
	}
	
	/// <inheritdoc />
	public IEnumerable<string> ExpandViewLocations(ViewLocationExpanderContext context, IEnumerable<string> viewLocations)
	{
		var locations = new List<string>
		{
			"~/Features/{1}/Views/{0}.cshtml",
			"~/Shared/Views/{0}.cshtml",
		};

		// also try to find the view in the normal view directory without the specific path
		if (context.ViewName.Contains("/"))
		{
			var viewName = context.ViewName.Split("/").Last();
			locations.Add($"~/Features/{{1}}/Views/{viewName}.cshtml");
		}
		
		// mind controllers that are divided into admin & public (i.e. Page)
		var controllerName = RazorViewEngine.GetNormalizedRouteValue(context.ActionContext, "controller")!;
		if (controllerName.Contains(Admin))
		{
			var featureName = controllerName.Replace(Admin, "");
			locations.Add($"~/Features/{featureName}/Views/{{0}}.cshtml");
			locations.Add($"~/Features/{featureName}/Views/{{1}}/{{0}}.cshtml");
		}
		else if (controllerName.Contains(Public))
		{
			var featureName = controllerName.Replace(Public, "");
			locations.Add($"~/Features/{featureName}/Views/{{0}}.cshtml");
			locations.Add($"~/Features/{featureName}/Views/{{1}}/{{0}}.cshtml");
		}
		
		if (context.ActionContext.RouteData.Values.ContainsKey(Feature))
		{
			var featureName = RazorViewEngine.GetNormalizedRouteValue(context.ActionContext, Feature);

			locations.Add($"~/Features/{featureName}/Views/{{0}}.cshtml");
			locations.Add($"~/Features/{featureName}/Views/{{1}}/{{0}}.cshtml");
		}

		return locations.Union(viewLocations);
	}
}