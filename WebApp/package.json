{"name": "level-studio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc && vite build", "dev": "vite dev"}, "devDependencies": {"@types/dropzone": "^5.7.9", "level-components": "file:../WebAppComponents/ClientApp", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.20.2"}, "dependencies": {"dropzone": "^6.0.0-beta.2", "glob": "^11.0.0", "uuid": "^11.0.5"}}