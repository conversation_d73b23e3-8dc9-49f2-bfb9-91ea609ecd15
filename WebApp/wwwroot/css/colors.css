/* ! DO NOT USE UNCATEGORIZED COLORS - SEE CATEGORIZED COLORS BELOW */
:root {
	/* brand */
	--clr-levelbuild-teal: #00BFCA;
	--clr-levelbuild-purple: #B04FE1;
	--clr-levelbuild-blue: #0097FD;
	--clr-levelbuild-charcoal: #444444;
	--clr-mainka-red: #E53012;
	--clr-bickhardtbau-yellow: #FFD600;
	--clr-jaegergruppe-blue: #2B68AE;
	--clr-spitzke-blue: #053CC4;
	--clr-demmelhuber-gold: #A18C61;

	/* black to white */
	--clr-black: black;
	--clr-black-950: #080A0C;
	--clr-black-900: #141B1F;
	--clr-black-800: #202A31;
	--clr-black-700: #354550;
	--clr-black-600: #455A68;
	--clr-black-500: #5E7A8D;
	--clr-black-400: #94A9B7;
	--clr-black-300: #AFBFCA;
	--clr-black-200: #CED8DE;
	--clr-black-100: #EDF0F3;
	--clr-black-50: #FAFAFA;
	--clr-white: white;

	/* blue */
	--clr-blue-950: #001E33;
	--clr-blue-900: #002E4D;
	--clr-blue-800: #003D66;
	--clr-blue-700: #005B99;
	--clr-blue-600: #007ACC;
	--clr-blue-500: #0097FD;
	--clr-blue-400: #33ADFF;
	--clr-blue-300: #66C1FF;
	--clr-blue-200: #99D6FF;
	--clr-blue-100: #CCEAFF;
	--clr-blue-50: #E5F5FF;

	/* orange */
	--clr-orange-950: #431407;
	--clr-orange-900: #7C2D12;
	--clr-orange-800: #9A3412;
	--clr-orange-700: #C2410C;
	--clr-orange-600: #EA580C;
	--clr-orange-500: #F97316;
	--clr-orange-400: #FB923C;
	--clr-orange-300: #FDBA74;
	--clr-orange-200: #FED7AA;
	--clr-orange-100: #FFEDD5;
	--clr-orange-50: #FFF7ED;

	/* yellow */
	--clr-yellow-950: #22202A;
	--clr-yellow-900: #713F12;
	--clr-yellow-800: #854D0E;
	--clr-yellow-700: #A16207;
	--clr-yellow-600: #CA8A04;
	--clr-yellow-500: #EAB308;
	--clr-yellow-400: #FACC15;
	--clr-yellow-300: #FDE047;
	--clr-yellow-200: #F2EBAA;
	--clr-yellow-100: #FEF9C3;
	--clr-yellow-50: #FFFAE8;

	/* green */
	--clr-green-950: #203D14;
	--clr-green-900: #305C1F;
	--clr-green-800: #407A29;
	--clr-green-700: #509933;
	--clr-green-600: #60B83D;
	--clr-green-500: #76C757;
	--clr-green-400: #8FD175;
	--clr-green-300: #A2D98C;
	--clr-green-200: #C1E6B3;
	--clr-green-100: #DAF0D1;
	--clr-green-50: #F3FAF0;

	/* red */
	--clr-red-950: #250E0E;
	--clr-red-900: #7F1D1D;
	--clr-red-800: #991B1B;
	--clr-red-700: #B91C1C;
	--clr-red-600: #DC2626;
	--clr-red-500: #DE5858;
	--clr-red-400: #F87171;
	--clr-red-300: #FCABCB;
	--clr-red-200: #F8CBCB;
	--clr-red-100: #FEE2E2;
	--clr-red-50: #FCE9E9;
	
	/* teal */
	--clr-teal-950: #030B18;
	--clr-teal-900: #022F3C;
	--clr-teal-800: #02535F;
	--clr-teal-700: #017783;
	--clr-teal-600: #01A6B1;
	--clr-teal-500: #00BFCA;
	--clr-teal-400: #2CC9D3;
	--clr-teal-300: #60D5DF;
	--clr-teal-200: #8FDFE9;
	--clr-teal-100: #BFEAF4;
	--clr-teal-50: #EEFFFF;
	
	/* purple */
	--clr-purple-950: #120718;
	--clr-purple-900: #321540;
	--clr-purple-800: #512468;
	--clr-purple-700: #713291;
	--clr-purple-600: #9041B9;
	--clr-purple-500: #B04FE1;
	--clr-purple-400: #BF70E7;
	--clr-purple-300: #CE99EC;
	--clr-purple-200: #DCB1F2;
	--clr-purple-100: #EBD1F7;
	--clr-purple-50: #FAF2FD;
	
	/* transparent */
	--clr-transparent-black-10: hsla(0, 0%, 0%, 0.1);
	--clr-transparent-black-15: hsla(0, 0%, 0%, 0.15);
	--clr-transparent-black-25: hsla(0, 0%, 0%, 0.25);
	--clr-transparent-black-35: hsla(0, 0%, 0%, 0.35);
	--clr-transparent-black-50: hsla(0, 0%, 0%, 0.50);
	--clr-transparent-black-75: hsla(0, 0%, 0%, 0.75);
	--clr-transparent-white-10: hsla(0, 100%, 100%, 0.1);
	--clr-transparent-white-15: hsla(0, 100%, 100%, 0.15);
	--clr-transparent-white-25: hsla(0, 100%, 100%, 0.25);
	--clr-transparent-white-35: hsla(0, 100%, 100%, 0.35);
	--clr-transparent-white-50: hsla(0, 100%, 100%, 0.50);
	--clr-transparent-white-75: hsla(0, 100%, 100%, 0.75);
}

/* Light mode */
:root {
	--color-scheme: light;
	
	/* text */
	--clr-text-primary-positiv: var(--clr-black-800);
	--clr-text-primary-negativ: var(--clr-white);
	--clr-text-secondary: var(--clr-black-500);
	--clr-text-tertiary: var(--clr-black-400);
	--clr-text-overlay: var(--clr-white);

	/* signal */
	--clr-signal-error: var(--clr-red-500);
	--clr-signal-error-medium: var(--clr-red-200);
	--clr-signal-error-light: var(--clr-red-50);
	--clr-signal-error-hover: var(--clr-red-600);
	--clr-signal-warning: var(--clr-orange-600);
	--clr-signal-warning-medium: var(--clr-orange-200);
	--clr-signal-warning-light: var(--clr-orange-50);
	--clr-signal-success: var(--clr-green-500);
	--clr-signal-success-medium: var(--clr-green-200);
	--clr-signal-success-light: var(--clr-green-50);
	--clr-signal-success-hover: var(--clr-green-600);
	--clr-signal-info: var(--clr-blue-600);
	--clr-signal-info-medium: var(--clr-blue-200);
	--clr-signal-info-light: var(--clr-blue-50);
	--clr-signal-favorite: var(--clr-yellow-500);
	--clr-signal-favorite-hover: var(--clr-yellow-400);

	/* state */
	--clr-state-active: var(--clr-blue-600);
	--clr-state-active-hover: var(--clr-blue-700);
	--clr-state-primary: var(--clr-blue-600);
	--clr-state-primary-hover: var(--clr-blue-700);
	--clr-state-hover: var(--clr-transparent-black-10);
	--clr-state-selected: var(--clr-transparent-black-15);
	--clr-state-enabled: var(--clr-green-500);
	--clr-state-focus: var(--clr-blue-200);
	--clr-state-readonly: var(--clr-black-100);
	--clr-state-inactive: var(--clr-black-200);
	--clr-state-inactive-hover: var(--clr-black-300);

	/* feed */
	--clr-feed-system: var(--clr-black-200);
	--clr-feed-neutral: var(--clr-blue-100);
	--clr-feed-completed: var(--clr-green-100);
	--clr-feed-attention: var(--clr-orange-300);
	--clr-feed-warning: var(--clr-red-400);

	/* background */
	--clr-background-lvl-0: var(--clr-white);
	--clr-background-lvl-1: var(--clr-black-100);
	--clr-background-lvl-2: var(--clr-black-200);
	--clr-background-lvl-3: var(--clr-black-300);
	--clr-background-lvl-4: var(--clr-black-400);
	--clr-background-lvl-0-tooltip: var(--clr-black-700);
	--clr-background-header: #EBE7E0; /* unfinished in figma */
	--clr-background-header-dark: #D7CEC1;
	--clr-background-viewer: #E3E6E8;
	--clr-background-dialog: var(--clr-transparent-black-50);

	/* border */
	--clr-border-white: var(--clr-white);
	--clr-border-weak: var(--clr-black-100);
	--clr-border-medium: var(--clr-black-300);
	--clr-border-strong: var(--clr-black-500);
	--clr-input-border-active: var(--clr-blue-500);
	
	/* shadow */
	--clr-shadow-weak: var(--clr-transparent-black-15);
	--clr-shadow-medium: var(--clr-transparent-black-25);
	--clr-shadow-strong: var(--clr-transparent-black-35);
	
	/* transparent */
	--clr-transparent-25: var(--clr-transparent-white-25);
	--clr-transparent-50: var(--clr-transparent-white-50);
	--clr-transparent-75: var(--clr-transparent-white-75);

	/* hover */
	--clr-hover-blue: hsl(204, 100%, 40%, 0.1);
	--clr-hover-red: hsl(0, 67%, 61%, 0.1);
	--clr-hover-green: hsl(142, 76%, 36%, 0.1);
	--clr-hover-grey: hsl(0, 0%, 0%, 0.1);
}

/* Dark mode */
:root[data-scheme=dark] {
	--color-scheme: dark;
	
	/* text */
	--clr-text-primary-positiv: var(--clr-black-300);
	--clr-text-primary-negativ: var(--clr-black-900);
	--clr-text-secondary: var(--clr-black-500);
	--clr-text-tertiary: var(--clr-black-700);

	/* signal */
	--clr-signal-error: var(--clr-red-700);
	--clr-signal-error-medium: var(--clr-red-900);
	--clr-signal-error-light: var(--clr-red-950);
	--clr-signal-error-hover: var(--clr-red-300);
	--clr-signal-warning: var(--clr-orange-600);
	--clr-signal-warning-medium: var(--clr-orange-900);
	--clr-signal-warning-light: var(--clr-orange-950);
	--clr-signal-success: var(--clr-green-700);
	--clr-signal-success-medium: var(--clr-green-900);
	--clr-signal-success-light: var(--clr-green-950);
	--clr-signal-success-hover: var(--clr-green-300);
	--clr-signal-info: var(--clr-blue-600);
	--clr-signal-info-medium: var(--clr-blue-900);
	--clr-signal-info-light: var(--clr-blue-950);
	--clr-signal-favorite: var(--clr-yellow-400);
	--clr-signal-favorite-hover: var(--clr-yellow-500);

	/* state */
	--clr-state-active: var(--clr-blue-600);
	--clr-state-active-hover: var(--clr-blue-500);
	--clr-state-primary: var(--clr-blue-600);
	--clr-state-primary-hover: var(--clr-blue-500);
	--clr-state-hover: var(--clr-transparent-white-10);
	--clr-state-selected: var(--clr-transparent-white-15);
	--clr-state-enabled: var(--clr-green-700);
	--clr-state-focus: var(--clr-blue-900);
	--clr-state-readonly: var(--clr-black-800);
	--clr-state-inactive: var(--clr-black-700);
	--clr-state-inactive-hover: var(--clr-black-600);

	/* feed */
	--clr-feed-system: var(--clr-black-600);
	--clr-feed-neutral: var(--clr-blue-800);
	--clr-feed-completed: var(--clr-green-800);
	--clr-feed-attention: var(--clr-orange-700);
	--clr-feed-warning: var(--clr-red-600);

	/* background */
	--clr-background-lvl-0: var(--clr-black-900);
	--clr-background-lvl-1: var(--clr-black-800);
	--clr-background-lvl-2: var(--clr-black-700);
	--clr-background-lvl-3: var(--clr-black-600);
	--clr-background-lvl-4: var(--clr-black-500);
	--clr-background-lvl-0-tooltip: var(--clr-black-400);
	--clr-background-header: #3D3629; /* unfinished in figma */
	--clr-background-header-dark: #5C523D;
	--clr-background-viewer: #E3E6E8;
	--clr-background-dialog: var(--clr-transparent-black-50);

	/* border */
	--clr-border-weak: var(--clr-black-700);
	--clr-border-medium: var(--clr-black-600);
	--clr-border-strong: var(--clr-black-400);
	--clr-input-border-active: var(--clr-blue-500);

	/* shadow */
	--clr-shadow-weak: var(--clr-transparent-white-15);
	--clr-shadow-medium: var(--clr-transparent-white-25);
	--clr-shadow-strong: var(--clr-transparent-white-35);

	/* transparent */
	--clr-transparent-25: var(--clr-transparent-black-25);
	--clr-transparent-50: var(--clr-transparent-black-50);
	--clr-transparent-75: var(--clr-transparent-black-75);

	/* hover */
	--clr-hover-blue: hsl(204, 100%, 40%, 0.1);
	--clr-hover-red: hsl(0, 67%, 61%, 0.1);
	--clr-hover-green: hsl(142, 76%, 36%, 0.1);
	--clr-hover-grey: hsl(0, 100%, 100%, 0.1);
}
