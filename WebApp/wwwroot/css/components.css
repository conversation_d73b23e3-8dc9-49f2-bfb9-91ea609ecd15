:not(:defined) {
	visibility: hidden;
}

:is(lvl-dropdown, lvl-popup, lvl-dialog):not(:defined) {
	display: none;
}

lvl-slide-out:not(:defined) {
	display: none;
}

lvl-fab {
	position: fixed;
	bottom: 3rem;
	right: 3rem;
}

lvl-side-nav {
	height: 100%;
}

lvl-breadcrumb:not(:defined) {
	width: 100%;
}

lvl-slide-out .content:not(:has(lvl-tab-bar)) {
	padding: var(--size-spacing-l) var(--size-spacing-xs) var(--size-spacing-l) var(--size-spacing-l);
}