@import url("colors.css");
@import url("components.css");
@import url("syncDialog.css");
@import url("layouts/config/list.css");
@import url("layouts/config/detail.css");
@import url("layouts/config/form.css");
@import url("layouts/config/skeleton.css");

/* Roboto font faces */
@import url("../fonts/Roboto/css/roboto.css");


*, *:before, *:after {
	box-sizing: border-box;
}

html {
	font-size: 62.5%;
}

:root {
	--size-text-xxs: 0.6rem;
	--size-text-xs: 0.8rem;
	--size-text-s: 1rem;
	--size-text-m: 1.2rem;
	--size-text-l: 1.6rem;
	--size-text-xl: 2.4rem;
	--size-text-xxl: 3.2rem;
	--size-text-xxxl: 4.8rem;

	--size-radius-s: 0.2rem;
	--size-radius-m: 0.4rem;
	--size-radius-l: 0.8rem;
	--size-radius-xl: 1.6rem;
	--size-radius-circle: 50%;
	--size-radius-pill: 100rem;

	--size-spacing-xxs: 0.1rem;
	--size-spacing-xs: 0.2rem;
	--size-spacing-s: 0.4rem;
	--size-spacing-m: 0.8rem;
	--size-spacing-l: 1.6rem;
	--size-spacing-xl: 2.4rem;
	--size-spacing-xxl: 3.2rem;
	--size-spacing-xxxl: 4rem;
}

:root {
	--header-height: 3.2rem;
	--scrollbar-width: 1.6rem;
	--scrollbar-radius: 0.8rem;
	--clr-corporate-text: var(--clr-text-primary-positiv);
	--clr-corporate-background: var(--clr-background-lvl-2);
}

/* animation times */
:root {
	--animation-time-fast: var(--custom-animation-time-fast, 75ms);
	--animation-time-medium: var(--custom-animation-time-medium, 150ms);
	--animation-time-slow: var(--custom-animation-time-slow, 300ms);
}

body {
	margin: 0;
	padding: 0;
	display: grid;
	grid-template-rows: auto auto 1fr;
	height: 100vh;
	background-color: var(--clr-background-lvl-1);
	color: var(--clr-text-primary-positiv);
	font-family: Roboto, system-ui, Helvetica, sans-serif;
	font-size: 1.2rem;
	overflow: hidden;
}

main {
	overflow: auto;
	height: 100%;
}

header {
	display: flex;
	align-items: center;
	gap: var(--size-spacing-s);
	padding: 0 var(--size-spacing-l);
	height: var(--header-height);

	color: var(--clr-corporate-text);
	background-color: var(--clr-corporate-background);
}

#user-control {
	flex-grow: 1;
	display: flex;
	justify-content: right;
	align-items: center;
	gap: var(--size-spacing-m);
}

.separator--vertical {
	height: 1em;
	border-left: 1px solid var(--clr-border-medium);
}

.separator--horizontal {
	width: 100%;
	border-top: 2px solid var(--clr-border-weak);
}

.profile__header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

#second-header {
	display: grid;
	grid-template-columns: 1fr auto 1fr;
	justify-items: center;
	align-items: center;
	gap: var(--size-spacing-m);
	height: 4.8rem;
	padding: 0 var(--size-spacing-l);
	background-color: var(--clr-background-lvl-1);
	overflow: hidden;
	
	& >:first-child {
		justify-self: start;
	}

	& >:last-child {
		justify-self: end;
	}
	
	& .header__part {
		display: flex;
		gap: var(--size-spacing-m);
		align-items: center;
	}
	
	& #global-search {
		width: 40rem;
	}

	& #navigation-buttons {
		display: flex;
	}
}

.side-panel[anchor] {
	margin-top: var(--header-height);
	height: calc(100vh - var(--header-height));
}

input {
	color-scheme: var(--color-scheme);
}

label {
	display: inline-block;
	color: var(--clr-text-primary-positiv);
}

legend {
	color: var(--clr-text-secondary);
	font-size: var(--size-text-s);
}

button, .button {
	cursor: pointer;
}

dialog {
	color: currentColor;
}

.hide, .hidden {
	display: none !important;
}

.flex--centered {
	display: flex;
	justify-content: center;
	align-items: center;
}

.grid--centered {
	display: grid;
	justify-items: center;
	align-content: start;
	overflow-y: auto;
	flex-grow: 1;
	padding: var(--size-spacing-m) 0 var(--size-spacing-l);
	gap: var(--size-spacing-l);
}

.grid--centered::-webkit-scrollbar-thumb {
	background-color: var(--clr-background-lvl-2);
	border: 5px solid transparent;
	border-radius: var(--scrollbar-radius);
	background-clip: padding-box;
}

.grid--centered::-webkit-scrollbar-thumb:hover {
	background-color: var(--clr-background-lvl-3);
}

.grid--centered::-webkit-scrollbar {
	width: var(--scrollbar-width);
}

.grid--centered::-webkit-scrollbar-corner {
	background: transparent;
}

.grid--centered::-webkit-scrollbar-track {
	background-color: transparent;
}

@property --scrollbar-color {
	syntax: "<color>";
	inherits: true;
	initial-value: transparent;
}

.vanishing-scrollbar {
	transition: --scrollbar-color var(--animation-time-slow);
}

.vanishing-scrollbar:hover,
.vanishing-scrollbar:focus {
	--scrollbar-color: var(--cp-clr-background-lvl-2);
}

.vanishing-scrollbar::-webkit-scrollbar-thumb {
	background-color: var(--scrollbar-color);
	border: var(--size-spacing-s) solid transparent;
	border-radius: var(--size-radius-pill);
	background-clip: padding-box;
}

.vanishing-scrollbar::-webkit-scrollbar-thumb:hover {
	--scrollbar-color: var(--cp-clr-background-lvl-3);
}

.vanishing-scrollbar::-webkit-scrollbar {
	width: 1.5rem;
}

.vanishing-scrollbar::-webkit-resizer {
	background-color: var(--cp-clr-background-lvl-0);
}

.static-scrollbar {
	overflow-y: scroll;
	overscroll-behavior: contain;
	scrollbar-gutter: stable;
}

.grid--with-2 {
	display: grid;
	grid-template-columns:repeat(2, 1fr);
}

.grid--with-3 {
	display: grid;
	grid-template-columns:repeat(3, 1fr);
}

.grid-column--full {
	grid-column: 1 / -1;
}

.grid-column--last {
	grid-column: -2 / -1;
}

.grid--gap-s {
	gap: var(--size-spacing-s)
}

.grid--gap-m {
	gap: var(--size-spacing-m)
}

.config-div {
	width: clamp(50%, 860px, 90%);
	color: var(--clr-text-secondary);
	font-size: var(--size-text-s);
}

.inline--active {
	color: var(--clr-state-active);
}

.dropdown-dialog {
	display: grid;
	width: 300px;
	gap: var(--size-spacing-l);
	padding: var(--size-spacing-l);
	background-color: var(--clr-background-lvl-0);
	
	& .heading {
		color: var(--clr-text-secondary);
	}
	
	& .separator--horizontal {
		border-color: var(--clr-border-weak);
	}
}

.info__item {
	display: flex;
	flex-direction: column;
	
	& label {
		font-size: var(--size-text-s);
		color: var(--clr-text-secondary);
	}
}

.html__wrapper {
	& p {
		margin: 0;
		line-height: 150%;
		white-space: normal;
	}

	& h1 {
		font-size: var(--size-text-xl);
	}

	& h2 {
		font-size: var(--size-text-l);
	}

	& h3 {
		font-size: var(--size-text-m);
	}

	& h1, h2, h3 {
		font-weight: bold;
	}

	& mark {
		border-radius: var(--size-radius-m);
	}

	& ol, ul {
		margin: 0;
		padding-left: var(--size-spacing-xl);
	}

	& ul > li {
		list-style-type: disc;
	}

	& ol > li {
		list-style-type: decimal;
	}

	& a {
		cursor: pointer;
	}

	& .is-empty:first-child::before {
		color: var(--cp-clr-text-tertiary);
		content: attr(data-placeholder);
		float: left;
		height: 0;
		pointer-events: none;
	}
}

.notification-box {
	display: flex;
	height: 4rem;
	align-items: center;
	border: 1px solid var(--color);
	border-radius: var(--size-radius-m);
	background-color: var(--background-color);
	padding: 0 var(--size-spacing-m);
	gap: var(--size-spacing-m);
	
	& i {
		color: var(--color);
	}
	
	&[data-signal="error"] {
		--color: var(--clr-signal-error);
		--background-color: var(--clr-signal-error-light);
	}

	&[data-signal="success"] {
		--color: var(--clr-signal-success);
		--background-color: var(--clr-signal-success-light);
	}

	&[data-signal="warning"] {
		--color: var(--clr-signal-warning);
		--background-color: var(--clr-signal-warning-light);
	}

	&[data-signal="info"] {
		--color: var(--clr-signal-info);
		--background-color: var(--clr-signal-info-light);
	}
}

[data-tooltip*="-mvp"] {
	cursor: no-drop;
}