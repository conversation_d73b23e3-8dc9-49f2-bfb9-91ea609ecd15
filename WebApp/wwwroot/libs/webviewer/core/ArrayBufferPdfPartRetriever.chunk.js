/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[2],{619:function(ya,ua,n){n.r(ua);ya=n(53);n=n(537);var na=function(){function ma(oa){this.buffer=oa;this.fileSize=null===oa||void 0===oa?void 0:oa.byteLength}ma.prototype.getFileData=function(oa){oa(new Uint8Array(this.buffer))};ma.prototype.getFile=function(){return Promise.resolve(null)};return ma}();Object(ya.a)(na);Object(n.a)(na);Object(n.b)(na);ua["default"]=na}}]);}).call(this || window)
