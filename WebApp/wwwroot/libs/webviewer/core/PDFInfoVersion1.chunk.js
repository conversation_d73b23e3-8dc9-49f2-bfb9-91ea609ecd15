/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[9],{628:function(ya,ua,n){function na(ca){ca.jb();ca.advance();var ha=ca.current.textContent;ca.Ob();return ha}function ma(ca){var ha=[];for(ca.jb();ca.advance();){var pa=ca.tb();"field"===pa?ha.push(String(ca.ka("name"))):Object(f.i)("unrecognised field list element: ".concat(pa))}ca.Ob();return ha}function oa(ca,ha){return ha?"false"!==ca:"true"===ca}function ka(ca,ha){var pa=ca.tb();switch(pa){case "javascript":return{name:"JavaScript",
javascript:ca.current.textContent};case "uri":return{name:"URI",uri:ca.ka("uri")};case "goto":pa=null;ca.jb();if(ca.advance()){var la=ca.ka("fit");pa={page:ca.ka("page"),fit:la};if("0"===pa.page)Object(f.i)("null page encountered in dest");else switch(ha=ha(Number(pa.page)),la){case "Fit":case "FitB":break;case "FitH":case "FitBH":pa.top=ha.Ba({x:0,y:ca.ka("top")||0}).y;break;case "FitV":case "FitBV":pa.left=ha.Ba({x:ca.ka("left")||0,y:0}).x;break;case "FitR":la=ha.Ba({x:ca.ka("left")||0,y:ca.ka("top")||
0});ha=ha.Ba({x:ca.ka("right")||0,y:ca.ka("bottom")||0});ha=new w.d(la.x,la.y,ha.x,ha.y);pa.top=ha.y1;pa.left=ha.x1;pa.bottom=ha.y2;pa.right=ha.x2;break;case "XYZ":la=ha.Ba({x:ca.ka("left")||0,y:ca.ka("top")||0});pa.top=la.y;pa.left=la.x;pa.zoom=ca.ka("zoom")||0;break;default:Object(f.i)("unknown dest fit: ".concat(la))}pa={name:"GoTo",dest:pa}}else Object(f.i)("missing dest in GoTo action");ca.Ob();return pa;case "submit-form":pa={name:"SubmitForm",url:ca.ka("url"),format:ca.ka("format"),method:ca.ka("method")||
"POST",exclude:oa(ca.ka("exclude"),!1)};ha=ca.ka("flags");pa.flags=ha?ha.split(" "):[];for(ca.jb();ca.advance();)switch(ha=ca.tb(),ha){case "fields":pa.fields=ma(ca);break;default:Object(f.i)("unrecognised submit-form child: ".concat(ha))}ca.Ob();return pa;case "reset-form":pa={name:"ResetForm",exclude:oa(ca.ka("exclude"),!1)};for(ca.jb();ca.advance();)switch(ha=ca.tb(),ha){case "fields":pa.fields=ma(ca);break;default:Object(f.i)("unrecognised reset-form child: ".concat(ha))}ca.Ob();return pa;case "hide":pa=
{name:"Hide",hide:oa(ca.ka("hide"),!0)};for(ca.jb();ca.advance();)switch(ha=ca.tb(),ha){case "fields":pa.fields=ma(ca);break;default:Object(f.i)("unrecognised hide child: ".concat(ha))}ca.Ob();return pa;case "named":return{name:"Named",action:ca.ka("name")};default:Object(f.i)("Encountered unexpected action type: ".concat(pa))}return null}function ia(ca,ha,pa){var la={};for(ca.jb();ca.advance();){var ja=ca.tb();switch(ja){case "action":ja=ca.ka("trigger");if(ha?-1!==ha.indexOf(ja):1){la[ja]=[];for(ca.jb();ca.advance();){var qa=
ka(ca,pa);Object(h.isNull)(qa)||la[ja].push(qa)}ca.Ob()}else Object(f.i)("encountered unexpected trigger on field: ".concat(ja));break;default:Object(f.i)("encountered unknown action child: ".concat(ja))}}ca.Ob();return la}function fa(ca){return new z.a(ca.ka("r")||0,ca.ka("g")||0,ca.ka("b")||0,ca.ka("a")||1)}function x(ca,ha){var pa=ca.ka("name"),la=ca.ka("type")||"Type1",ja=ca.ka("size"),qa=ha.Ba({x:0,y:0});ja=ha.Ba({x:Number(ja),y:0});ha=qa.x-ja.x;qa=qa.y-ja.y;pa={name:pa,type:la,size:Math.sqrt(ha*
ha+qa*qa)||0,strokeColor:[0,0,0],fillColor:[0,0,0]};for(ca.jb();ca.advance();)switch(la=ca.tb(),la){case "stroke-color":pa.strokeColor=fa(ca);break;case "fill-color":pa.fillColor=fa(ca);break;default:Object(f.i)("unrecognised font child: ".concat(la))}ca.Ob();return pa}function y(ca){var ha=[];for(ca.jb();ca.advance();){var pa=ca.tb();switch(pa){case "option":pa=ha;var la=pa.push;var ja=ca;ja={value:ja.ka("value"),displayValue:ja.ka("display-value")||void 0};la.call(pa,ja);break;default:Object(f.i)("unrecognised options child: ".concat(pa))}}ca.Ob();
return ha}function r(ca,ha){var pa=ca.ka("name"),la={type:ca.ka("type"),quadding:ca.ka("quadding")||"Left-justified",maxLen:ca.ka("max-len")||-1},ja=ca.ka("flags");Object(h.isString)(ja)&&(la.flags=ja.split(" "));for(ca.jb();ca.advance();)switch(ja=ca.tb(),ja){case "actions":la.actions=ia(ca,["C","F","K","V"],function(){return ha});break;case "default-value":la.defaultValue=na(ca);break;case "font":la.font=x(ca,ha);break;case "options":la.options=y(ca);break;default:Object(f.i)("unknown field child: ".concat(ja))}ca.Ob();
return new window.da.Annotations.la.ya(pa,la)}function e(ca,ha){switch(ca.type){case "Tx":try{if(Object(ea.c)(ca.actions))return new b.a.DatePickerWidgetAnnotation(ca,ha)}catch(pa){Object(f.i)(pa)}return new b.a.TextWidgetAnnotation(ca,ha);case "Ch":return ca.flags.get(ba.WidgetFlags.COMBO)?new b.a.ChoiceWidgetAnnotation(ca,ha):new b.a.ListWidgetAnnotation(ca,ha);case "Btn":return ca.flags.get(ba.WidgetFlags.PUSH_BUTTON)?new b.a.PushButtonWidgetAnnotation(ca,ha):ca.flags.get(ba.WidgetFlags.RADIO)?
new b.a.RadioButtonWidgetAnnotation(ca,ha):new b.a.CheckButtonWidgetAnnotation(ca,ha);case "Sig":return new b.a.SignatureWidgetAnnotation(ca,ha);default:Object(f.i)("Unrecognised field type: ".concat(ca.type))}return null}function a(ca,ha,pa,la){var ja=[],qa={};ca.jb();var ra=[],sa={},ta=[];Object(aa.a)(function(){if(ca.advance()){var va=ca.tb();switch(va){case "calculation-order":ra="calculation-order"===ca.tb()?ma(ca):[];break;case "document-actions":sa=ia(ca,["Init","Open"],ha);break;case "pages":va=
[];for(ca.jb();ca.advance();){var Ba=ca.tb();switch(Ba){case "page":Ba=va;var Aa=Ba.push,za=ca,Ga=ha,Da={number:za.ka("number")};for(za.jb();za.advance();){var Ja=za.tb();switch(Ja){case "actions":Da.actions=ia(za,["O","C"],Ga);break;default:Object(f.i)("unrecognised page child: ".concat(Ja))}}za.Ob();Aa.call(Ba,Da);break;default:Object(f.i)("unrecognised page child: ".concat(Ba))}}ca.Ob();ta=va;break;case "field":Ba=r(ca,ha(1));qa[Ba.name]=Ba;break;case "widget":va={border:{style:"Solid",width:1},
backgroundColor:[],fieldName:ca.ka("field"),page:ca.ka("page"),index:ca.ka("index")||0,rotation:ca.ka("rotation")||0,flags:[],isImporting:!0};(Ba=ca.ka("appearance"))&&(va.appearance=Ba);(Ba=ca.ka("flags"))&&(va.flags=Ba.split(" "));for(ca.jb();ca.advance();)switch(Ba=ca.tb(),Ba){case "rect":Aa=ca;za=ha(Number(va.page));Ba=za.Ba({x:Aa.ka("x1")||0,y:Aa.ka("y1")||0});Aa=za.Ba({x:Aa.ka("x2")||0,y:Aa.ka("y2")||0});Ba=new w.d(Ba.x,Ba.y,Aa.x,Aa.y);Ba.normalize();va.rect={x1:Ba.x1,y1:Ba.y1,x2:Ba.x2,y2:Ba.y2};
break;case "border":Ba=ca;Aa={style:Ba.ka("style")||"Solid",width:Ba.ka("width")||1,color:[0,0,0]};for(Ba.jb();Ba.advance();)switch(za=Ba.tb(),za){case "color":Aa.color=fa(Ba);break;default:Object(f.i)("unrecognised border child: ".concat(za))}Ba.Ob();va.border=Aa;break;case "background-color":va.backgroundColor=fa(ca);break;case "actions":va.actions=ia(ca,"E X D U Fo Bl PO PC PV PI".split(" "),ha);break;case "appearances":Ba=ca;Aa=Object(ea.b)(va,"appearances");for(Ba.jb();Ba.advance();)if(za=Ba.tb(),
"appearance"===za){za=Ba.ka("name");Ga=Object(ea.b)(Aa,za);za=Ba;for(za.jb();za.advance();)switch(Da=za.tb(),Da){case "Normal":Object(ea.b)(Ga,"Normal").data=za.current.textContent;break;default:Object(f.i)("unexpected appearance state: ",Da)}za.Ob()}else Object(f.i)("unexpected appearances child: ".concat(za));Ba.Ob();break;case "extra":Ba=ca;Aa=ha;za={};for(Ba.jb();Ba.advance();)switch(Ga=Ba.tb(),Ga){case "font":za.font=x(Ba,Aa(1));break;default:Object(f.i)("unrecognised extra child: ".concat(Ga))}Ba.Ob();
Ba=za;Ba.font&&(va.font=Ba.font);break;case "captions":Aa=ca;Ba={};(za=Aa.ka("Normal"))&&(Ba.Normal=za);(za=Aa.ka("Rollover"))&&(Ba.Rollover=za);(Aa=Aa.ka("Down"))&&(Ba.Down=Aa);va.captions=Ba;break;default:Object(f.i)("unrecognised widget child: ".concat(Ba))}ca.Ob();(Ba=qa[va.fieldName])?(va=e(Ba,va),ja.push(va)):Object(f.i)("ignoring widget with no corresponding field data: ".concat(va.fieldName));break;default:Object(f.i)("Unknown element encountered in PDFInfo: ".concat(va))}return!0}return!1},
function(){ca.Ob();pa({calculationOrder:ra,widgets:ja,fields:qa,documentActions:sa,pages:ta,custom:[]})},la)}n.r(ua);n.d(ua,"parse",function(){return a});var f=n(2),h=n(1);n.n(h);var b=n(158),w=n(4),z=n(11),aa=n(26),ea=n(136),ba=n(17)}}]);}).call(this || window)
