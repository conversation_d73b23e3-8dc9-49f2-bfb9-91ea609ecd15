{"version": 3, "sources": ["webpack:///./src/ui/src/components/PageManipulationOverlay/PageManipulationControls/PageManipulationControls.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/PageManipulationControls/PageManipulationControlsContainer.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/PageManipulationControls/index.js"], "names": ["PageManipulationControls", "props", "t", "useTranslation", "deletePages", "extractPages", "insertPages", "replacePages", "DataElementWrapper", "dataElement", "className", "onClick", "<PERSON><PERSON>", "title", "img", "role", "onClickAnnouncement", "propTypes", "pageNumbers", "PropTypes", "arrayOf", "number", "warn", "bool", "PageManipulationControlsContainer", "dispatch", "useDispatch", "isPageDeletionConfirmationModalEnabled", "useSelector", "state", "selectors", "pageDeletionConfirmationModalEnabled", "openInsertPageModal", "actions", "closeElement", "DataElements", "PAGE_MANIPULATION_OVERLAY", "openElement", "noPagesSelectedWarning", "isMobile", "replace"], "mappings": "qOA0EeA,MArEf,SAAkCC,GAChC,IAAQC,EAAMC,cAAND,EACAE,EAAyDH,EAAzDG,YAAaC,EAA4CJ,EAA5CI,aAAcC,EAA8BL,EAA9BK,YAAaC,EAAiBN,EAAjBM,aAEhD,OACE,oCACE,kBAACC,EAAA,EAAkB,CACjBC,YAAY,yBACZC,UAAU,QAETR,EAAE,4BAEL,kBAACM,EAAA,EAAkB,CACjBE,UAAU,MACVD,YAAY,aACZE,QAASL,GAET,kBAACM,EAAA,EAAM,CACLC,MAAM,oBACNC,IAAI,6BACJC,KAAK,SACLC,oBAAmB,UAAKd,EAAE,qBAAoB,YAAIA,EAAE,gBAAe,YAAIA,EAAE,oBAE3E,yBAAKQ,UAAU,SAASR,EAAE,mBAE5B,kBAACM,EAAA,EAAkB,CACjBE,UAAU,MACVD,YAAY,cACZE,QAASJ,GAET,kBAACK,EAAA,EAAM,CACLC,MAAM,qBACNC,IAAI,wBACJC,KAAK,SACLC,oBAAmB,UAAKd,EAAE,sBAAqB,YAAIA,EAAE,gBAAe,YAAIA,EAAE,oBAE5E,yBAAKQ,UAAU,SAASR,EAAE,oBAE5B,kBAACM,EAAA,EAAkB,CACjBE,UAAU,MACVD,YAAY,cACZE,QAASN,GAET,kBAACO,EAAA,EAAM,CACLC,MAAM,qBACNC,IAAI,iCACJC,KAAK,SACLC,oBAAmB,UAAKd,EAAE,sBAAqB,YAAIA,EAAE,gBAAe,YAAIA,EAAE,oBAE5E,yBAAKQ,UAAU,SAASR,EAAE,oBAE5B,kBAACM,EAAA,EAAkB,CACjBC,YAAY,aACZC,UAAU,MACVC,QAASP,GAET,kBAACQ,EAAA,EAAM,CACLC,MAAM,+BACNC,IAAI,mBACJC,KAAK,SACLC,oBAAmB,UAAKd,EAAE,iBAAgB,YAAIA,EAAE,gBAAe,YAAIA,EAAE,oBAEvE,yBAAKQ,UAAU,SAASR,EAAE,qB,8kCCzDlC,IAAMe,EAAY,CAChBC,YAAaC,IAAUC,QAAQD,IAAUE,QACzCC,KAAMH,IAAUI,MAGlB,SAASC,EAAkCvB,GACzC,IAAMwB,EAAWC,cACTR,EAAsBjB,EAAtBiB,YAAaI,EAASrB,EAATqB,KACdK,EAEL,EAF+CC,aAAY,SAACC,GAAK,MAAK,CACtEC,IAAUC,qCAAqCF,OAC/C,GAF2C,GAIvCG,EAAsB,WAC1BP,EAASQ,IAAQC,aAAaC,IAAaC,4BAC3CX,EAASQ,IAAQI,YAAY,qBAqC/B,OACE,kBAAC,EAAwB,CACvB/B,YApCa,WACXgB,GACDgB,YAAuBpB,EAAaO,IAAaO,IAElDA,IAEFO,eAAcd,EAASQ,IAAQC,aAAaC,IAAaC,6BA+BvDhC,YAZa,WACXkB,GACDgB,YAAuBpB,EAAaO,IAAarB,YAAYc,EAAaO,EAAUE,GAErFvB,YAAYc,EAAaO,EAAUE,GAErCY,eAAcd,EAASQ,IAAQC,aAAaC,IAAaC,6BAOvD/B,aArBc,WACZiB,GACDgB,YAAuBpB,EAAaO,IAAapB,YAAaa,EAAaO,GAE5EpB,YAAaa,EAAaO,GAE5Bc,eAAcd,EAASQ,IAAQC,aAAaC,IAAaC,6BAgBvD7B,aA9Bc,WACZe,GACDgB,YAAuBpB,EAAaO,IAAae,YAAQf,GAE1De,YAAQf,GAEVc,eAAcd,EAASQ,IAAQC,aAAaC,IAAaC,+BA6B7DZ,EAAkCP,UAAYA,EAE/BO,QCvEAA", "file": "chunks/chunk.10.js", "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport Button from 'components/Button';\n\nfunction PageManipulationControls(props) {\n  const { t } = useTranslation();\n  const { deletePages, extractPages, insertPages, replacePages } = props;\n\n  return (\n    <>\n      <DataElementWrapper\n        dataElement=\"pageManipulationHeader\"\n        className=\"type\"\n      >\n        {t('action.pageManipulation')}\n      </DataElementWrapper>\n      <DataElementWrapper\n        className=\"row\"\n        dataElement=\"insertPage\"\n        onClick={insertPages}\n      >\n        <Button\n          title=\"action.insertPage\"\n          img=\"icon-page-insertion-insert\"\n          role=\"option\"\n          onClickAnnouncement={`${t('action.insertPage')} ${t('action.modal')} ${t('action.isOpen')}`}\n        />\n        <div className=\"title\">{t('action.insert')}</div>\n      </DataElementWrapper>\n      <DataElementWrapper\n        className=\"row\"\n        dataElement=\"replacePage\"\n        onClick={replacePages}\n      >\n        <Button\n          title=\"action.replacePage\"\n          img=\"icon-page-replacement\"\n          role=\"option\"\n          onClickAnnouncement={`${t('action.replacePage')} ${t('action.modal')} ${t('action.isOpen')}`}\n        />\n        <div className=\"title\">{t('action.replace')}</div>\n      </DataElementWrapper>\n      <DataElementWrapper\n        className=\"row\"\n        dataElement=\"extractPage\"\n        onClick={extractPages}\n      >\n        <Button\n          title=\"action.extractPage\"\n          img=\"icon-page-manipulation-extract\"\n          role=\"option\"\n          onClickAnnouncement={`${t('action.extractPage')} ${t('action.modal')} ${t('action.isOpen')}`}\n        />\n        <div className=\"title\">{t('action.extract')}</div>\n      </DataElementWrapper>\n      <DataElementWrapper\n        dataElement=\"deletePage\"\n        className=\"row\"\n        onClick={deletePages}\n      >\n        <Button\n          title=\"option.thumbnailPanel.delete\"\n          img=\"icon-delete-line\"\n          role=\"option\"\n          onClickAnnouncement={`${t('action.delete')} ${t('action.modal')} ${t('action.isOpen')}`}\n        />\n        <div className=\"title\">{t('action.delete')}</div>\n      </DataElementWrapper>\n    </>\n  );\n}\n\n\nexport default PageManipulationControls;\n", "import React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport PageManipulationControls from './PageManipulationControls';\nimport { deletePages, extractPages, noPagesSelectedWarning, replace } from 'helpers/pageManipulationFunctions';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport { isMobile } from 'helpers/device';\nimport DataElements from 'constants/dataElement';\n\nconst propTypes = {\n  pageNumbers: PropTypes.arrayOf(PropTypes.number),\n  warn: PropTypes.bool,\n};\n\nfunction PageManipulationControlsContainer(props) {\n  const dispatch = useDispatch();\n  const { pageNumbers, warn } = props;\n  const [isPageDeletionConfirmationModalEnabled] = useSelector((state) => [\n    selectors.pageDeletionConfirmationModalEnabled(state),\n  ]);\n\n  const openInsertPageModal = () => {\n    dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n    dispatch(actions.openElement('insertPageModal'));\n  };\n\n  const onInsert = () => {\n    if (warn) {\n      !noPagesSelectedWarning(pageNumbers, dispatch) && openInsertPageModal();\n    } else {\n      openInsertPageModal();\n    }\n    isMobile() && dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n  };\n\n  const onReplace = () => {\n    if (warn) {\n      !noPagesSelectedWarning(pageNumbers, dispatch) && replace(dispatch);\n    } else {\n      replace(dispatch);\n    }\n    isMobile() && dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n  };\n  const onExtract = () => {\n    if (warn) {\n      !noPagesSelectedWarning(pageNumbers, dispatch) && extractPages(pageNumbers, dispatch);\n    } else {\n      extractPages(pageNumbers, dispatch);\n    }\n    isMobile() && dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n  };\n  const onDelete = () => {\n    if (warn) {\n      !noPagesSelectedWarning(pageNumbers, dispatch) && deletePages(pageNumbers, dispatch, isPageDeletionConfirmationModalEnabled);\n    } else {\n      deletePages(pageNumbers, dispatch, isPageDeletionConfirmationModalEnabled);\n    }\n    isMobile() && dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n  };\n\n  return (\n    <PageManipulationControls\n      insertPages={onInsert}\n      deletePages={onDelete}\n      extractPages={onExtract}\n      replacePages={onReplace}\n    />\n  );\n}\n\nPageManipulationControlsContainer.propTypes = propTypes;\n\nexport default PageManipulationControlsContainer;", "import PageManipulationControlsContainer from './PageManipulationControlsContainer';\n\nexport default PageManipulationControlsContainer;"], "sourceRoot": ""}