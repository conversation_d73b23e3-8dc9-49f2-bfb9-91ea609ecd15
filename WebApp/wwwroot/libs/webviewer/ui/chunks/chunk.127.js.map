{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/de-at.js"], "names": ["module", "exports", "e", "t", "default", "n", "i", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "a", "Array", "isArray", "replace", "r", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "ordinal", "weekStart", "formats", "LTS", "LT", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,EAAE,oBAAoBC,EAAE,CAAC,cAAc,gBAAgBC,GAAG,aAAaC,EAAE,CAAC,cAAc,gBAAgBC,GAAG,aAAaC,EAAE,CAAC,UAAU,aAAaC,GAAG,CAAC,UAAU,YAAYC,EAAE,CAAC,YAAY,eAAeC,GAAG,CAAC,YAAY,cAAcC,EAAE,CAAC,WAAW,cAAcC,GAAG,CAAC,WAAW,cAAc,SAASC,EAAEhB,EAAEG,EAAEF,GAAG,IAAIe,EAAEZ,EAAEH,GAAG,OAAOgB,MAAMC,QAAQF,KAAKA,EAAEA,EAAEb,EAAE,EAAE,IAAIa,EAAEG,QAAQ,KAAKnB,GAAG,IAAIoB,EAAE,CAACC,KAAK,QAAQC,SAAS,8DAA8DC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,qFAAqFH,MAAM,KAAKI,YAAY,6DAA6DJ,MAAM,KAAKK,QAAQ,SAAS5B,GAAG,OAAOA,EAAE,KAAK6B,UAAU,EAAEC,QAAQ,CAACC,IAAI,WAAWC,GAAG,QAAQC,EAAE,aAAaC,GAAG,eAAeC,IAAI,qBAAqBC,KAAK,4BAA4BC,aAAa,CAACC,OAAO,QAAQC,KAAK,SAASlC,EAAEW,EAAEV,EAAEU,EAAET,GAAGS,EAAER,EAAEQ,EAAEP,GAAGO,EAAEN,EAAEM,EAAEL,GAAGK,EAAEJ,EAAEI,EAAEH,GAAGG,EAAEF,EAAEE,EAAED,GAAGC,IAAI,OAAOf,EAAEC,QAAQsC,OAAOpB,EAAE,MAAK,GAAIA,EAAj0CjB,CAAE,EAAQ", "file": "chunks/chunk.127.js", "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_de_at=n(e.dayjs)}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e),i={s:\"ein paar Sekunden\",m:[\"eine Minute\",\"einer Minute\"],mm:\"%d Minuten\",h:[\"eine Stunde\",\"einer Stunde\"],hh:\"%d Stunden\",d:[\"ein Tag\",\"einem Tag\"],dd:[\"%d Tage\",\"%d Tagen\"],M:[\"ein Monat\",\"einem Monat\"],MM:[\"%d <PERSON><PERSON>\",\"%d <PERSON><PERSON>\"],y:[\"ein Jahr\",\"einem Jahr\"],yy:[\"%d Jahre\",\"%d Jahren\"]};function a(e,n,t){var a=i[t];return Array.isArray(a)&&(a=a[n?0:1]),a.replace(\"%d\",e)}var r={name:\"de-at\",weekdays:\"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag\".split(\"_\"),weekdaysShort:\"So._Mo._Di._Mi._Do._Fr._Sa.\".split(\"_\"),weekdaysMin:\"So_Mo_Di_Mi_Do_Fr_Sa\".split(\"_\"),months:\"Jänner_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember\".split(\"_\"),monthsShort:\"Jän._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,formats:{LTS:\"HH:mm:ss\",LT:\"HH:mm\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY HH:mm\",LLLL:\"dddd, D. MMMM YYYY HH:mm\"},relativeTime:{future:\"in %s\",past:\"vor %s\",s:a,m:a,mm:a,h:a,hh:a,d:a,dd:a,M:a,MM:a,y:a,yy:a}};return t.default.locale(r,null,!0),r}));"], "sourceRoot": ""}