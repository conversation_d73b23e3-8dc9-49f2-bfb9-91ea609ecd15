{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/lb.js"], "names": ["module", "exports", "e", "t", "default", "_", "n", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,mEAAmEC,MAAM,KAAKC,OAAO,uFAAuFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,8BAA8BH,MAAM,KAAKI,YAAY,+DAA+DJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,cAAcC,IAAI,iBAAiBC,EAAE,aAAaC,GAAG,eAAeC,IAAI,2BAA2BC,KAAK,mCAAmC,OAAOnB,EAAEC,QAAQmB,OAAOjB,EAAE,MAAK,GAAIA,EAA93BD,CAAE,EAAQ", "file": "chunks/chunk.181.js", "sourcesContent": ["!function(e,_){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=_(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],_):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_lb=_(e.dayjs)}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),n={name:\"lb\",weekdays:\"Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg\".split(\"_\"),months:\"Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember\".split(\"_\"),weekStart:1,weekdaysShort:\"So._Mé._Dë._Më._Do._Fr._Sa.\".split(\"_\"),monthsShort:\"Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.\".split(\"_\"),weekdaysMin:\"So_Mé_Dë_Më_Do_Fr_Sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"H:mm [Auer]\",LTS:\"H:mm:ss [Auer]\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY H:mm [Auer]\",LLLL:\"dddd, D. MMMM YYYY H:mm [Auer]\"}};return t.default.locale(n,null,!0),n}));"], "sourceRoot": ""}