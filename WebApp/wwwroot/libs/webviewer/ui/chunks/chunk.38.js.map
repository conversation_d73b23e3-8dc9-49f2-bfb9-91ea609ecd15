{"version": 3, "sources": ["webpack:///./src/ui/src/components/SettingsModal/GeneralTab.scss?ded9", "webpack:///./src/ui/src/components/SettingsModal/GeneralTab.scss", "webpack:///./src/ui/src/components/SettingsModal/EditKeyboardShortcutModal.scss?14c1", "webpack:///./src/ui/src/components/SettingsModal/EditKeyboardShortcutModal.scss", "webpack:///./src/ui/src/components/SettingsModal/KeyboardShortcutTab.scss?62ae", "webpack:///./src/ui/src/components/SettingsModal/KeyboardShortcutTab.scss", "webpack:///./src/ui/src/components/SettingsModal/AdvancedTab.scss?49f2", "webpack:///./src/ui/src/components/SettingsModal/AdvancedTab.scss", "webpack:///./src/ui/src/components/SettingsModal/SettingsModal.scss?c158", "webpack:///./src/ui/src/components/SettingsModal/SettingsModal.scss", "webpack:///./src/ui/src/components/SettingsModal/SearchWrapper.js", "webpack:///./src/ui/src/components/SettingsModal/GeneralTab.js", "webpack:///./src/ui/src/components/SettingsModal/EditKeyboardShortcutModal.js", "webpack:///./src/ui/src/components/SettingsModal/KeyboardShortcutTab.js", "webpack:///./src/ui/src/components/SettingsModal/AdvancedTab.js", "webpack:///./src/ui/src/components/SettingsModal/SettingsModal.js", "webpack:///./src/ui/src/components/SettingsModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "SearchContext", "createContext", "SearchWrapper", "children", "keywords", "searchTerm", "useContext", "trim", "some", "keyword", "toLowerCase", "includes", "GeneralTab", "useSelector", "state", "selectors", "getCurrentLanguage", "getActiveTheme", "currentLanguage", "activeTheme", "t", "useTranslation", "dispatch", "useDispatch", "store", "useStore", "isLightMode", "Theme", "LIGHT", "setTheme", "theme", "actions", "setActiveTheme", "DataElementWrapper", "className", "dataElement", "DataElements", "SETTINGS_LANGUAGE_SECTION", "id", "Dropdown", "labelledById", "SETTINGS_LANGUAGE_DROPDOWN", "items", "Languages", "currentSelectionKey", "<PERSON><PERSON><PERSON>", "item", "getDisplayValue", "onClickItem", "value", "setLanguage", "maxHeight", "width", "getCustomItemStyle", "textAlign", "isIE", "SETTINGS_THEME_SECTION", "Icon", "glyph", "Choice", "radio", "checked", "onChange", "label", "name", "DARK", "EditKeyboardShortcutModal", "currentShortcut", "finishEditing", "getCommandStrings", "shortcutKeyMap", "getShortcutKeyMap", "useState", "currentCommand", "setCurrentCommand", "isRecording", "setIsRecording", "Set", "pressedKeys", "setPressedKeys", "activatedByKeyboard", "setActivatedByKeyboard", "useEffect", "keyDownHandler", "e", "preventDefault", "keyName", "getKeyString", "prevKeys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "formattedCommand", "Array", "from", "join", "edit<PERSON><PERSON><PERSON>", "getRootNode", "querySelector", "focus", "addEventListener", "hotkeys", "setScope", "removeEventListener", "defaultHotkeysScope", "onCloseHandler", "useFocusOnClose", "keyCode", "key", "hasConflict", "hotkeysManager", "ModalWrapper", "isOpen", "title", "onCloseClick", "classNames", "map", "<PERSON><PERSON>", "disabled", "onClick", "detail", "key<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setShortcutKey", "keyboardShortcuts", "Shortcuts", "ROTATE_CLOCKWISE", "ROTATE_COUNTER_CLOCKWISE", "COPY", "PASTE", "UNDO", "REDO", "OPEN_FILE", "SEARCH", "ZOOM_IN", "ZOOM_OUT", "SET_HEADER_FOCUS", "FIT_SCREEN_WIDTH", "PRINT", "BOOKMARK", "PREVIOUS_PAGE", "NEXT_PAGE", "UP", "DOWN", "SWITCH_PAN", "SELECT", "PAN", "ARROW", "CALLOUT", "ERASER", "FREEHAND", "IMAGE", "LINE", "STICKY_NOTE", "ELLIPSE", "RECTANGLE", "RUBBER_STAMP", "FREETEXT", "SIGNATURE", "SQUIGGLY", "HIGHLIGHT", "STRIKEOUT", "UNDERLINE", "CLOSE", "KeyboardShortcutTab", "undefined", "setCurrentShortcut", "command", "toUpperCase", "commands", "split", "isMac", "focusHandler", "useFocusHandler", "shortcut", "setIsElementHidden", "SETTINGS_MODAL", "editShortcut", "currentTarget", "getAttribute", "replace", "description", "str", "img", "aria<PERSON><PERSON><PERSON>", "createItem", "isChecked", "onToggled", "AdvancedTab", "shouldFadePageNavigationComponent", "isNoteSubmissionWithEnterEnabled", "isCommentThreadExpansionEnabled", "isNotesPanelRepliesCollapsingEnabled", "isNotesPanelTextCollapsingEnabled", "shouldClearSearchPanelOnClose", "pageDeletionConfirmationModalEnabled", "isThumbnailSelectingPages", "getCustomSettings", "isToolDefaultStyleUpdateFromAnnotationPopupEnabled", "customSettings", "updateState", "forceUpdate", "useCallback", "viewingItems", "enable", "disableFadePageNavigationComponent", "enableFadePageNavigationComponent", "touchEventManager", "useNativeScroll", "annotationsItems", "setToolDefaultStyleUpdateFromAnnotationPopupEnabled", "notesPanelItems", "setNoteSubmissionEnabledWithEnter", "setCommentThreadExpansion", "setNotesPanelRepliesCollapsing", "setNotesPanelTextCollapsing", "searchItems", "setClearSearchOnPanelClose", "pageManipulationItems", "disablePageDeletionConfirmationModal", "enablePageDeletionConfirmationModal", "setThumbnailSelectingPages", "sections", "getSectionKeywords", "sectionTitle", "sectionItems", "flat", "aria-label", "isSwitch", "target", "TABS_ID", "SettingsModal", "isDisabled", "isElementDisabled", "isElementOpen", "isSpreadsheetEditorMode", "isSpreadsheetEditorModeEnabled", "selectedTab", "getSelectedTab", "isGeneralTabDisabled", "SETTINGS_GENERAL_BUTTON", "isKeyboardTabDisabled", "SETTINGS_KEYBOARD_BUTTON", "isAdvancedTabDisabled", "SETTINGS_ADVANCED_BUTTON", "setSearchTerm", "tabs", "tabToEnable", "setSelectedTab", "closeModal", "closeElement", "Provider", "data-element", "<PERSON><PERSON><PERSON><PERSON>", "swipeToClose", "filter", "tab", "selected", "type", "handleTabClicked", "aria-selected", "aria-current"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,q7BAAs7B,M,qBCL/8B,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,ioOAAkoO,KAG3pO0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,8wFAA+wF,KAGxyF0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,4VAA6V,M,qBCLtX,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,gkSAAikS,KAG1lS0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,yQCRVC,G,0BAAgBC,2BAEhBC,EAAgB,SAAH,GAAoC,IAA9BC,EAAQ,EAARA,SAAQ,IAAEC,gBAAQ,IAAG,KAAE,EAC/CC,EAAaC,qBAAWN,GAAeO,OAE7C,OAASF,GAAcD,EAASI,MAAK,SAACC,GAAO,OAAKA,EAAQC,cAAcC,SAASN,EAAWK,kBAC1F,oCACGP,GAED,M,uiCCON,IA4FeS,EA5FI,WACjB,IAME,IAHEC,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,mBAAmBF,GAC7BC,IAAUE,eAAeH,OACzB,GALAI,EAAe,KACfC,EAAW,KAKNC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cACXC,EAAQC,cAQRC,EAAcP,IAAgBQ,IAAMC,MAEpCC,EAAW,SAACC,GAChBR,EAASS,IAAQC,eAAeF,KAGlC,OACE,oCACE,kBAAC,EAAa,CACZ1B,SAAU,CAACgB,EAAE,8BAEb,kBAACa,EAAA,EAAkB,CACjBC,UAAU,kBACVC,YAAaC,IAAaC,2BAE1B,yBAAKH,UAAU,iBAAgB,2BAAOI,GAAG,2BAA2BlB,EAAE,8BACtE,kBAACmB,EAAA,EAAQ,CACPD,GAAG,oBACHE,aAAa,0BACbL,YAAaC,IAAaK,2BAC1BC,MAAOC,IACPC,oBAAqB1B,EACrB2B,OAAQ,SAACC,GAAI,OAAKA,EAAK,IACvBC,gBAAiB,SAACD,GAAI,OAAKA,EAAK,IAChCE,YA9Ba,SAACC,GAClBA,IAAU/B,GACZgC,YAAY1B,EAAZ0B,CAAmBD,IA6BbE,UAAW,IACXC,MAAO,IACPC,mBAAoB,iBAAO,CAAEC,UAAW,OAAQF,MAAO,UACvDlB,UAAU,wBAIhB,kBAAC,EAAa,CACZ9B,SAAU,CAACgB,EAAE,yBAA0BA,EAAE,6BAA8BA,EAAE,+BAEvEmC,KACA,kBAACtB,EAAA,EAAkB,CACjBC,UAAU,kBACVC,YAAaC,IAAaoB,wBAE1B,yBAAKtB,UAAU,iBAAgB,+BAAQd,EAAE,2BACzC,yBAAKc,UAAU,iBACb,yBAAKA,UAAS,uBAAkBR,EAAc,eAAiB,KAC7D,kBAAC+B,EAAA,EAAI,CAACC,MAAM,yBAAyBxB,UAAU,oBAC/C,yBAAKA,UAAU,gBACb,kBAACyB,EAAA,EAAM,CACLC,OAAK,EACLC,QAASnC,EACToC,SAAU,kBAAMjC,EAASF,IAAMC,QAC/BmC,MAAO3C,EAAE,6BACT4C,KAAK,mBAIX,yBAAK9B,UAAS,uBAAmBR,EAA+B,GAAjB,iBAC7C,kBAAC+B,EAAA,EAAI,CAACC,MAAM,wBAAwBxB,UAAU,mBAC9C,yBAAKA,UAAU,gBACb,kBAACyB,EAAA,EAAM,CACLC,OAAK,EACLC,SAAUnC,EACVoC,SAAU,kBAAMjC,EAASF,IAAMsC,OAC/BF,MAAO3C,EAAE,4BACT4C,KAAK,wB,g+CCpFzB,IAyIeE,EAvImB,SAAH,GAA8D,IAAxDC,EAAe,EAAfA,gBAAiBC,EAAa,EAAbA,cAAeC,EAAiB,EAAjBA,kBAC5DjD,EAAqB,EAAhBC,cAAgB,GAApB,GACFiD,EAAiBzD,YAAYE,IAAUwD,mBAEW,IAAZC,mBAAS,IAAG,GAAjDC,EAAc,KAAEC,EAAiB,KACa,IAAfF,oBAAS,GAAM,GAA9CG,EAAW,KAAEC,EAAc,KACuB,IAAnBJ,mBAAS,IAAIK,KAAM,GAAlDC,EAAW,KAAEC,EAAc,KACmC,IAAfP,oBAAS,GAAM,GAA9DQ,EAAmB,KAAEC,EAAsB,KAElDC,qBAAU,WACRR,EAAkBJ,EAAeH,MAChC,CAACA,EAAiBG,IAErBY,qBAAU,WACR,GAAKP,EAAL,CAIA,IAAMQ,EAAiB,SAACC,GACtBA,EAAEC,iBACF,IAAMC,EAAUC,EAAaH,GAC7BL,GAAe,SAACS,GAAQ,OAAK,IAAIX,IAAI,GAAD,SAAKW,GAAQ,CAAEF,SAG/CG,EAAe,WACnB,GAAIX,EAAYY,KAAO,EAAG,CACxB,IAAMC,EAAmBC,MAAMC,KAAKf,GAAagB,KAAK,KAAKpF,cAC3DgE,EAAkBiB,GAMpB,GAJAZ,EAAe,IAAIF,KACnBD,GAAe,GAGXI,EAAqB,CACvB,IAAMe,EAAaC,cAAcC,cAAc,wDAC3CF,GACFA,EAAWG,UAUjB,OALA3H,OAAO4H,iBAAiB,UAAWhB,GACnC5G,OAAO4H,iBAAiB,QAASV,GAEjCW,IAAQC,SA9CqB,gBAgDtB,WACL9H,OAAO+H,oBAAoB,UAAWnB,GACtC5G,OAAO+H,oBAAoB,QAASb,GACpCW,IAAQC,SAASE,SAElB,CAAC5B,EAAaG,EAAaE,IAE9B,IAoBMwB,EAAiBC,YAAgBrC,GAQjCmB,EAAe,SAACH,GACpB,OAAQA,EAAEsB,SACR,KAAK,GACH,MAAO,QACT,KAAK,GACH,MAAO,OACT,KAAK,GACH,MAAO,MACT,KAAK,GACL,KAAK,GACH,MAAO,UACT,QACE,OAAOtB,EAAEuB,IAAIjG,gBAIbkG,EAAcC,IAAeD,YAAYzC,EAAiBM,GAMhE,OACE,yBAAKvC,UAAU,wCACb,kBAAC4E,EAAA,EAAY,CAACC,QAAQ,EAAMC,MAAM,sCAAsCC,aAAc7C,GACpF,yBAAKlC,UAAU,QACb,yBAAKA,UAAWgF,IAAW,CACzB,qBAAqB,EACrB,UAAavC,GAAeK,KAVNX,EAAkBI,GAAgB0C,KAAI,SAACR,EAAKvI,GAAC,OAC3E,0BAAMuI,IAAKvI,GAAIuI,OAWRC,GAAgB,yBAAK1E,UAAU,oBAAoBd,EAAE,2CAExD,yBAAKc,UAAU,YACf,yBAAKA,UAAU,UACb,kBAACkF,EAAA,EAAM,CACLlF,UAAU,oCACV6B,MAAO3C,EAAE,gCACTiG,SAAU1C,EACV2C,QAlEa,SAAClC,GAItB,GAHAL,EAAe,IAAIF,KAGfO,EAAEmC,OAAS,EACb3C,GAAe,GACfK,GAAuB,OAClB,CAELA,GAAuB,GAOvB1G,OAAO4H,iBAAiB,SALE,SAApBqB,IACJ5C,GAAe,GACfrG,OAAO+H,oBAAoB,QAASkB,QAsDhCrF,YAAY,wCAEd,kBAACiF,EAAA,EAAM,CACLlF,UAAU,uBACV6B,MAAO3C,EAAE,+BACTiG,SAAU1C,GAAeiC,EACzBU,QAnDU,WAClBlB,IAAQC,SAASE,KACjBM,IAAeY,eAAetD,EAAiBM,GAC/C+B,KAiDQrE,YAAY,2C,+iCChIxB,IAAMuF,EAAoB,CACxB,CAACC,IAAUC,iBAAkB,2CAC7B,CAACD,IAAUE,yBAA0B,kDACrC,CAACF,IAAUG,KAAM,4BACjB,CAACH,IAAUI,MAAO,6BAClB,CAACJ,IAAUK,KAAM,8BACjB,CAACL,IAAUM,KAAM,8BACjB,CAACN,IAAUO,UAAW,4BACtB,CAACP,IAAUQ,OAAQ,8BACnB,CAACR,IAAUS,QAAS,0BACpB,CAACT,IAAUU,SAAU,2BACrB,CAACV,IAAUW,iBAAkB,kCAC7B,CAACX,IAAUY,iBAAkB,kCAC7B,CAACZ,IAAUa,MAAO,yBAClB,CAACb,IAAUc,SAAU,qCACrB,CAACd,IAAUe,cAAe,oCAC1B,CAACf,IAAUgB,UAAW,gCACtB,CAAChB,IAAUiB,GAAI,2CACf,CAACjB,IAAUkB,KAAM,yCACjB,CAAClB,IAAUmB,WAAY,iCACvB,CAACnB,IAAUoB,OAAQ,wCACnB,CAACpB,IAAUqB,IAAK,6BAChB,CAACrB,IAAUsB,MAAO,yCAClB,CAACtB,IAAUuB,QAAS,2CACpB,CAACvB,IAAUwB,OAAQ,oCACnB,CAACxB,IAAUyB,SAAU,4CACrB,CAACzB,IAAU0B,MAAO,yCAClB,CAAC1B,IAAU2B,KAAM,wCACjB,CAAC3B,IAAU4B,YAAa,0CACxB,CAAC5B,IAAU6B,QAAS,2CACpB,CAAC7B,IAAU8B,UAAW,6CACtB,CAAC9B,IAAU+B,aAAc,+CACzB,CAAC/B,IAAUgC,SAAU,4CACrB,CAAChC,IAAUiC,UAAW,sCACtB,CAACjC,IAAUkC,SAAU,gDACrB,CAAClC,IAAUmC,UAAW,iDACtB,CAACnC,IAAUoC,UAAW,iDACtB,CAACpC,IAAUqC,UAAW,iDACtB,CAACrC,IAAUsC,MAAO,0BAkFLC,EA/Ea,WAC1B,IAAO9I,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAEX+C,EAAiBzD,YAAYE,IAAUwD,mBAEoB,IAAnBC,wBAAS2F,GAAU,GAA1DhG,EAAe,KAAEiG,EAAkB,KAEpC/F,EAAoB,SAACgG,GACzB,IAAKA,EACH,MAAO,GAGT,IADAA,EAAUA,EAAQC,eACN3J,SAAS,aAAc,CACjC,IAAM4J,EAAWF,EAAQG,MAAM,MAC/BH,EAAUI,IAAQF,EAAS,GAAKA,EAAS,GAE3C,OAAOF,EAAQG,MAAM,UAajBE,EAAeC,aAAgB,SAACvF,IAVjB,SAACwF,GACpBtJ,EAASS,IAAQ8I,mBAAmBzI,IAAa0I,gBAAgB,IACjEV,EAAmBQ,GAUnBG,CADiB3F,EAAE4F,cAAcC,aAAa,gBAAgBC,QAAQ,eAAgB,QAIxF,OACE,oCACE,yBAAKhJ,UAAU,yBACb,yBAAKA,UAAU,iCAAiCd,EAAE,4BAClD,yBAAKc,UAAU,qCAAqCd,EAAE,gCACtD,yBAAKc,UAAU,gCAAgCd,EAAE,4BAEnD,yBAAKc,UAAU,0BACZwF,EAAkBP,KAAI,yBAAEkD,EAAO,KAAEc,EAAW,YAC3C,kBAAC,EAAa,CACZxE,IAAK0D,EACLjK,SAAU,CAACgB,EAAE+J,KAEb,yBAAKjJ,UAAU,uBACb,yBAAKA,UAAU,+BACZmC,EAAkBC,EAAe+F,IAAUlD,KAAI,SAACiE,EAAKhN,GAAC,OACrD,0BAAMuI,IAAKvI,GAAIgN,OAGnB,yBAAKlJ,UAAU,mCACZd,EAAE+J,IAEL,kBAAC/D,EAAA,EAAM,CACLjF,YAAW,sBAAiBkI,GAC5BgB,IAAI,uBACJrE,MAAO5F,EAAE,eACTkK,UAAS,UAAKlK,EAAE+J,GAAY,YAAI/J,EAAE,gBAClCkG,QAASoD,UAMlBvG,GACC,kBAAC,EAAyB,CACxBA,gBAAiBA,EACjBC,cA9Cc,WACpBgG,OAAmBD,GACnB7I,EAASS,IAAQ8I,mBAAmBzI,IAAa0I,gBAAgB,KA6C3DzG,kBAAmBA,M,gkCCrH7B,IAAMkH,EAAa,SAACxH,EAAOoH,EAAaK,EAAWC,GAAS,MAAM,CAAE1H,QAAOoH,cAAaK,YAAWC,cAuKpFC,EArKK,WAClB,IAsBE,IAXE7K,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAU4K,kCAAkC7K,GAC5CC,IAAU6K,iCAAiC9K,GAC3CC,IAAU8K,gCAAgC/K,GAC1CC,IAAU+K,qCAAqChL,GAC/CC,IAAUgL,kCAAkCjL,GAC5CC,IAAUiL,8BAA8BlL,GACxCC,IAAUkL,qCAAqCnL,GAC/CC,IAAUmL,0BAA0BpL,GACpCC,IAAUoL,kBAAkBrL,GAC5BC,IAAUqL,mDAAmDtL,OAC7D,IArBA6K,EAAiC,KACjCC,EAAgC,KAChCC,EAA+B,KAC/BC,EAAoC,KACpCC,EAAiC,KACjCC,EAA6B,KAC7BC,EAAoC,KACpCC,EAAyB,KACzBG,EAAc,KACdD,EAAkD,KAa7ChL,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAER+K,EAAyB,EAAV9H,qBAAU,GAAd,GACd+H,EAAcC,uBAAY,kBAAMF,EAAY,MAAK,IAEjDG,EAAe,CACnBlB,EACEnK,EAAE,sDACFA,EAAE,2DACDuK,GACD,SAACe,GACUpL,EAAToL,EAAkB3K,IAAQ4K,qCAAiD5K,IAAQ6K,wCAGvFrB,EACEnK,EAAE,0CACFA,EAAE,+CACDyL,IAAkBC,iBACnB,SAACJ,GACCG,IAAkBC,iBAAmBJ,MAKrCK,EAAmB,CACvBxB,EACEnK,EAAE,oEACFA,EAAE,yEACDgL,GACD,SAACM,GAAM,OAAKpL,EAASS,IAAQiL,qDAAqDN,QAIhFO,EAAkB,CACtB1B,EACEnK,EAAE,kDACFA,EAAE,uDACDwK,GACD,SAACc,GAAM,OAAKpL,EAASS,IAAQmL,mCAAmCR,OAElEnB,EACEnK,EAAE,kDACFA,EAAE,uDACDyK,GACD,SAACa,GAAM,OAAKpL,EAASS,IAAQoL,2BAA2BT,OAE1DnB,EACEnK,EAAE,wCACFA,EAAE,6CACD0K,GACD,SAACY,GAAM,OAAKpL,EAASS,IAAQqL,gCAAgCV,OAE/DnB,EACEnK,EAAE,uCACFA,EAAE,4CACD2K,GACD,SAACW,GAAM,OAAKpL,EAASS,IAAQsL,6BAA6BX,QAIxDY,EAAc,CAClB/B,EACEnK,EAAE,kDACFA,EAAE,uDACD4K,GACD,SAACU,GAAM,OAAKpL,EAASS,IAAQwL,4BAA4Bb,QAIvDc,EAAwB,CAC5BjC,EACEnK,EAAE,wDACFA,EAAE,6DACD6K,GACD,SAACS,GACUpL,EAAToL,EAAkB3K,IAAQ0L,uCAAmD1L,IAAQ2L,0CAGzFnC,EACEnK,EAAE,sCACFA,EAAE,2CACD8K,GACD,SAACQ,GAAM,OAAKpL,EAASS,IAAQ4L,4BAA4BjB,QAIvDkB,EAAW,CACf,CAACxM,EAAE,2BAA4BqL,GAC/B,CAACrL,EAAE,+BAAgC2L,GACnC,CAAC3L,EAAE,8BAA+B6L,GAClC,CAAC7L,EAAE,0BAA2BkM,GAC9B,CAAClM,EAAE,oCAAqCoM,GACxC,CAACpM,EAAE,iCAAkCiL,IAGjCwB,EAAqB,SAACC,EAAcC,GACxC,MAAO,CACLD,EACAC,EAAa5G,KAAI,SAACrE,GAAI,MAAK,CAACA,EAAKiB,MAAOjB,EAAKqI,gBAAc6C,QAC3DA,QAGJ,OACE,oCACGJ,EAASzG,KAAI,yBAAE2G,EAAY,KAAEC,EAAY,YAAQA,EAAajP,OAAS,EAAK,KAC3E,kBAAC,EAAa,CACZ6H,IAAKmH,EACL1N,SAAUyN,EAAmBC,EAAcC,IAE3C,yBAAK7L,UAAU,mBACb,yBAAKA,UAAU,iBAAgB,+BAAQ4L,IACtCC,EAAa5G,KAAI,SAACrE,GAAI,OACrB,kBAAC,EAAa,CACZ6D,IAAK7D,EAAKiB,MACV3D,SAAU,CAAC0N,EAAchL,EAAKiB,MAAOjB,EAAKqI,cAE1C,yBAAKjJ,UAAU,gBACb,yBAAKA,UAAU,qBACb,yBAAKA,UAAU,sBAAsBY,EAAKiB,OAC1C,6BAAMjB,EAAKqI,cAEb,kBAACxH,EAAA,EAAM,CACLsK,aAAYnL,EAAKiB,MACjBmK,UAAQ,EACRrK,QAAoC,mBAAnBf,EAAK0I,UAA4B1I,EAAK0I,YAAc1I,EAAK0I,UAC1E1H,SAAU,SAACsB,GACTtC,EAAK2I,UAAUrG,EAAE+I,OAAOtK,SACxB0I,mB,uiCCrJtB,IAAM6B,EAAUhM,IAAa0I,eA+HduD,EA7HO,WACpB,IAAMC,EAAazN,aAAY,SAACC,GAAK,OAAKC,IAAUwN,kBAAkBzN,EAAOsB,IAAa0I,mBACpF/D,EAASlG,aAAY,SAACC,GAAK,OAAKC,IAAUyN,cAAc1N,EAAOsB,IAAa0I,mBAC5E2D,EAA0B5N,YAAYE,IAAU2N,gCAChDC,EAAc9N,aAAY,SAACC,GAAK,OAAKC,IAAU6N,eAAe9N,EAAOsN,MACrES,EAAuBhO,aAAY,SAACC,GAAK,OAAKC,IAAUwN,kBAAkBzN,EAAOsB,IAAa0M,4BAC9FC,EAAwBlO,aAAY,SAACC,GAAK,OAAKC,IAAUwN,kBAAkBzN,EAAOsB,IAAa4M,6BAC/FC,EAAwBpO,aAAY,SAACC,GAAK,OAAKC,IAAUwN,kBAAkBzN,EAAOsB,IAAa8M,6BAC9F9N,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAC+B,IAAZiD,mBAAS,IAAG,GAAzCnE,EAAU,KAAE8O,EAAa,KAE1BC,EAAO,CACX,CAAChN,IAAa0M,wBAAyB1N,EAAE,4BACzC,CAACgB,IAAa4M,yBAA0B5N,EAAE,qCAC1C,CAACgB,IAAa8M,yBAA0B9N,EAAE,qCAG5C8D,qBAAU,WACR,GACGyJ,IAAgBvM,IAAa0M,yBAA2BD,GACxDF,IAAgBvM,IAAa4M,0BAA4BD,GACzDJ,IAAgBvM,IAAa8M,0BAA4BD,EAC1D,CACA,IAAII,EAAc,GACbR,EAEOE,EAEAE,IACVI,EAAcjN,IAAa8M,0BAF3BG,EAAcjN,IAAa4M,yBAF3BK,EAAcjN,IAAa0M,wBAM7BxN,EAASS,IAAQuN,eAAelB,EAASiB,OAE1C,CAACR,EAAsBE,EAAuBE,IAEjD,IAAMM,EAAa,WACjBjO,EAASS,IAAQyN,aAAapN,IAAa0I,kBAS7C,GAAIwD,EACF,OAAO,KAGT,IAAMpM,EAAYgF,IAAW,QAAS,gBAAiB,QAEvD,OACE,kBAAClH,EAAcyP,SAAQ,CAACxM,MAAO5C,GAC7B,yBAAK6B,UAAWA,EAAWwN,eAActN,IAAaiM,eACpD,kBAACvH,EAAA,EAAY,CACXE,MAAO5F,EAAE,4BACTuO,aAAcJ,EACdtI,aAAcsI,EACdxI,OAAQA,EACR6I,cAAY,GAEZ,yBAAK1N,UAAU,aACb,yBAAKA,UAAU,oBACf,yBAAKA,UAAU,UACb,yBAAKA,UAAU,yBACb,kBAACuB,EAAA,EAAI,CAACC,MAAM,uBACZ,2BACET,MAAO5C,EACPyD,SAAU,SAACsB,GAAC,OAAK+J,EAAc/J,EAAE+I,OAAOlL,QACxCgL,aAAY7M,EAAE,yCAIpB,yBAAKc,UAAU,YACf,yBAAKA,UAAU,QACb,yBAAKA,UAAU,2BACb,yBAAKA,UAAU,iBACZkN,EACES,QAAO,YAAW,IAATC,EAAS,OAAN,GACX,OAAIrB,GACKqB,IAAQ1N,IAAa4M,0BAA4Bc,IAAQ1N,IAAa8M,4BAIhF/H,KAAI,YAAkB,aAAhB2I,EAAG,KAAE9I,EAAK,KACT9E,EAAYgF,IAAW,eAAgB,CAC3C6I,SAAUD,IAAQnB,IAEpB,OACE,kBAAC1M,EAAA,EAAkB,CACjB+N,KAAK,SACL9N,UAAWA,EACXC,YAAa2N,EACbxI,QAAS,kBAtDR,SAACwI,GACpBA,IAAQnB,GACVrN,EAASS,IAAQuN,eAAelB,EAAS0B,IAoDNG,CAAiBH,IAChCnJ,IAAKmJ,EACLI,gBAAeJ,IAAQnB,EACvBwB,eAAcL,IAAQnB,EAAc,OAAS,MAE5C3H,QAOb,yBAAK9E,UAAWgF,IAAW,mBAAoB,CAAEgD,oBAAqByE,IAAgBvM,IAAa4M,4BAChGL,IAAgBvM,IAAa0M,yBAC5B,kBAAC,EAAU,MAEZH,IAAgBvM,IAAa4M,0BAC5B,kBAAC,EAAmB,MAErBL,IAAgBvM,IAAa8M,0BAC5B,kBAAC,EAAW,aClIfb", "file": "chunks/chunk.38.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./GeneralTab.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".language-dropdown.Dropdown{border:1px solid var(--gray-6)}.language-dropdown .Dropdown__items{left:0;width:336px}.theme-options{width:336px;height:160px;display:flex;justify-content:space-between}.theme-options .theme-option{width:160px;height:160px;display:flex;flex-direction:column}.theme-options .theme-option .Icon{width:160px;height:120px}.theme-options .theme-option .Icon.light-mode-icon{color:#fff}.theme-options .theme-option .Icon.dark-mode-icon{color:#000}.theme-options .theme-option .Icon svg{border:1px solid;border-color:var(--border);border-top-left-radius:4px;border-top-right-radius:4px}.theme-options .theme-option .theme-choice{height:100%;border:1px solid;border-color:var(--border);border-top:0;border-bottom-left-radius:4px;border-bottom-right-radius:4px;display:flex;padding-left:12px}.theme-options .theme-option.active-theme .Icon svg,.theme-options .theme-option.active-theme .theme-choice{border-color:var(--blue-5)}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./EditKeyboardShortcutModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.EditKeyboardShortcutModal.Modal{visibility:visible}.closed.EditKeyboardShortcutModal.Modal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.EditKeyboardShortcutModal.Modal .footer .modal-button.cancel:hover,.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.EditKeyboardShortcutModal.Modal .footer .modal-button.cancel,.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.EditKeyboardShortcutModal.Modal .footer .modal-button.cancel.disabled,.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.EditKeyboardShortcutModal.Modal .footer .modal-button.cancel.disabled span,.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.EditKeyboardShortcutModal.Modal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.EditKeyboardShortcutModal.Modal .modal-container .wrapper .modal-content{padding:10px}.EditKeyboardShortcutModal.Modal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.EditKeyboardShortcutModal.Modal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.EditKeyboardShortcutModal.Modal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm{margin-left:4px}.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .EditKeyboardShortcutModal.Modal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .EditKeyboardShortcutModal.Modal .footer .modal-button{padding:23px 8px}}.EditKeyboardShortcutModal.Modal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .EditKeyboardShortcutModal.Modal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .EditKeyboardShortcutModal.Modal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .EditKeyboardShortcutModal.Modal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .EditKeyboardShortcutModal.Modal .swipe-indicator{width:32px}}.EditKeyboardShortcutModal.Modal .modal-container{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);overflow-y:auto;max-height:100%}@media(max-height:500px){.App:not(.is-web-component) .EditKeyboardShortcutModal.Modal .modal-container,.EditKeyboardShortcutModal.Modal .App:not(.is-web-component) .modal-container{overflow:auto;max-height:100%}}@container (max-height: 500px){.App.is-web-component .EditKeyboardShortcutModal.Modal .modal-container,.EditKeyboardShortcutModal.Modal .App.is-web-component .modal-container{overflow:auto;max-height:100%}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .EditKeyboardShortcutModal.Modal .modal-container,.EditKeyboardShortcutModal.Modal .App:not(.is-in-desktop-only-mode):not(.is-web-component) .modal-container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .EditKeyboardShortcutModal.Modal .modal-container,.EditKeyboardShortcutModal.Modal .App.is-web-component:not(.is-in-desktop-only-mode) .modal-container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}.EditKeyboardShortcutModal.Modal .modal-container{display:flex;flex-direction:column;width:480px;height:320px}.EditKeyboardShortcutModal.Modal .modal-container .header{display:flex;flex-direction:row;justify-content:space-between;padding:16px;font-size:16px;font-weight:700;align-items:center}.EditKeyboardShortcutModal.Modal .modal-container .body{height:100%;display:flex;flex-direction:column;padding:16px;position:relative}.EditKeyboardShortcutModal.Modal .modal-container .body .press-key-note{font-weight:700}.EditKeyboardShortcutModal.Modal .modal-container .body .keyboard-shortcut{height:100%;color:var(--gray-7);display:flex;align-items:center;justify-content:center;background:var(--gray-2);border-radius:4px}.EditKeyboardShortcutModal.Modal .modal-container .body .keyboard-shortcut span:not(:last-child){margin-right:8px}.EditKeyboardShortcutModal.Modal .modal-container .body .keyboard-shortcut.recording{outline:var(--focus-visible-outline)}.EditKeyboardShortcutModal.Modal .modal-container .body .conflict-warning{position:absolute;bottom:50px;font-size:13px;color:var(--red);width:calc(100% - 32px);display:flex;justify-content:center}.EditKeyboardShortcutModal.Modal .modal-container .divider{height:1px;width:100%;background:var(--divider)}.EditKeyboardShortcutModal.Modal .modal-container .footer{padding:16px;margin-top:0;grid-gap:8px;gap:8px}.EditKeyboardShortcutModal.Modal .modal-container .footer .modal-button{height:32px;margin:0;font-weight:unset}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./KeyboardShortcutTab.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.shortcut-table-header{border:1px solid var(--gray-4);border-bottom:0;padding:12px 16px;display:flex;font-weight:700;position:relative}.shortcut-table-header .shortcut-table-header-command{width:220px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .shortcut-table-header .shortcut-table-header-command{width:180px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .shortcut-table-header .shortcut-table-header-command{width:180px}}.shortcut-table-header .shortcut-table-header-action{position:absolute;right:16px}.shortcut-table-content{border:1px solid var(--gray-4);padding:0 16px;display:flex;flex-direction:column}.shortcut-table-content .shortcut-table-item{display:flex;align-items:center;height:40px;position:relative}.shortcut-table-content .shortcut-table-item .shortcut-table-item-command{color:var(--gray-7);width:220px}.shortcut-table-content .shortcut-table-item .shortcut-table-item-command span:not(:last-child){margin-right:8px}.shortcut-table-content .shortcut-table-item .shortcut-table-item-description{width:380px}.shortcut-table-content .shortcut-table-item button{position:absolute;right:0;height:32px;width:32px}.shortcut-table-content .shortcut-table-item button .Icon{height:20px;width:20px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .shortcut-table-content .shortcut-table-item .shortcut-table-item-command{width:180px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .shortcut-table-content .shortcut-table-item .shortcut-table-item-description{width:340px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .shortcut-table-content .shortcut-table-item .shortcut-table-item-command{width:180px}.App.is-web-component:not(.is-in-desktop-only-mode) .shortcut-table-content .shortcut-table-item .shortcut-table-item-description{width:340px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./AdvancedTab.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".setting-item{border:1px solid var(--gray-4);padding:16px;display:flex;align-items:flex-start;justify-content:space-between}.setting-item:not(:last-child){border-bottom:0}.setting-item .setting-item-info{display:flex;flex-direction:column;margin-right:18px}.setting-item .setting-item-info .setting-item-label{font-weight:700;margin-bottom:10px}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./SettingsModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.SettingsModal{visibility:visible}.closed.SettingsModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SettingsModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.SettingsModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.SettingsModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.SettingsModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.SettingsModal .footer .modal-button.cancel:hover,.SettingsModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.SettingsModal .footer .modal-button.cancel,.SettingsModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.SettingsModal .footer .modal-button.cancel.disabled,.SettingsModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.SettingsModal .footer .modal-button.cancel.disabled span,.SettingsModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.SettingsModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.SettingsModal .modal-container .wrapper .modal-content{padding:10px}.SettingsModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.SettingsModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.SettingsModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.SettingsModal .footer .modal-button.confirm{margin-left:4px}.SettingsModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .footer .modal-button{padding:23px 8px}}.SettingsModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .swipe-indicator{width:32px}}.SettingsModal .container{display:flex;flex-direction:column;width:888px;height:445px}.SettingsModal .container .settings-search-input{display:flex;border:1px solid var(--border);align-items:center;height:32px;padding:6px;border-radius:4px;background:var(--component-background);width:100%}.SettingsModal .container .settings-search-input[focus-within]{outline:none;border:1px solid var(--blue-5)}.SettingsModal .container .settings-search-input:focus-within{outline:none;border:1px solid var(--blue-5)}.SettingsModal .container .settings-search-input .Icon{width:16px;height:16px}.SettingsModal .container .settings-search-input input{width:100%;padding-right:26px;height:20px;border:none;background:transparent;padding-left:8px}.SettingsModal .container .header{display:flex;flex-direction:column;padding:12px 16px;font-size:16px;font-weight:700;align-items:center}.SettingsModal .container .header .title{display:flex;justify-content:space-between;align-items:center;width:100%;height:32px;margin-bottom:8px}.SettingsModal .container .body{height:100%;display:flex;flex-direction:row;overflow-y:hidden}.SettingsModal .container .body .settings-tabs-container{width:168px;flex-shrink:0;border-right:1px solid var(--gray-4)}.SettingsModal .container .body .settings-tabs-container .settings-tabs button{border:none;background:none;width:100%}.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab{height:32px;margin-top:4px;display:flex;align-items:center;padding:8px;color:var(--gray-9);cursor:pointer}.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab:hover{border-right:2px solid var(--blue-6);color:var(--blue-6)}.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab.selected{background-color:var(--gray-2)!important;border-right:2px solid var(--blue-5);color:var(--blue-5);cursor:default}.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab.focus-visible,.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab:focus-visible{outline-offset:-2px;outline:var(--focus-visible-outline);border-radius:4px}.SettingsModal .container .body .settings-content{padding:16px;overflow-y:scroll;width:100%}.SettingsModal .container .body .settings-content .setting-section{margin-bottom:16px}.SettingsModal .container .body .settings-content .setting-section .setting-label{font-weight:700;margin-bottom:8px}.SettingsModal .container .body .settings-content .Dropdown.language-dropdown{height:32px}.SettingsModal .container .body .Button.custom-ui.icon-only:not([disabled]):hover{box-shadow:none;border:1px solid var(--blue-6)}.SettingsModal .container .divider{height:1px;width:100%;background:var(--divider)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container{height:100%}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body{flex-direction:column}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-tabs-container{width:100%;position:fixed}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-tabs-container .settings-tabs{display:flex}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab{flex:1;height:32px;margin-top:0;justify-content:center;white-space:nowrap;min-width:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab.selected{border-right:0;border-bottom:2px solid var(--blue-5)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-content{margin-top:32px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-content.KeyboardShortcutTab{overflow-x:scroll;width:640px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container{height:100%}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body{flex-direction:column}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-tabs-container{width:100%;position:fixed}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-tabs-container .settings-tabs{display:flex}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab{flex:1;height:32px;margin-top:0;justify-content:center;white-space:nowrap;min-width:0}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab.selected{border-right:0;border-bottom:2px solid var(--blue-5)}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-content{margin-top:32px}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-content.KeyboardShortcutTab{overflow-x:scroll;width:640px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { createContext, useContext } from 'react';\n\nexport const SearchContext = createContext();\n\nexport const SearchWrapper = ({ children, keywords = [] }) => {\n  const searchTerm = useContext(SearchContext).trim();\n\n  return (!searchTerm || keywords.some((keyword) => keyword.toLowerCase().includes(searchTerm.toLowerCase()))) ? (\n    <>\n      {children}\n    </>\n  ) : null;\n};\n", "import React from 'react';\nimport { useSelector, useDispatch, useStore } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport { useTranslation } from 'react-i18next';\nimport { isIE } from 'helpers/device';\nimport Languages from 'constants/languages';\nimport Theme from 'constants/theme';\nimport DataElements from 'constants/dataElement';\nimport setLanguage from '../../apis/setLanguage';\nimport Dropdown from 'components/Dropdown';\nimport Icon from 'components/Icon';\nimport Choice from 'components/Choice';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport { SearchWrapper } from './SearchWrapper';\n\nimport './GeneralTab.scss';\n\nconst GeneralTab = () => {\n  const [\n    currentLanguage,\n    activeTheme\n  ] = useSelector((state) => [\n    selectors.getCurrentLanguage(state),\n    selectors.getActiveTheme(state)\n  ]);\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const store = useStore();\n\n  const changeLanguage = (value) => {\n    if (value !== currentLanguage) {\n      setLanguage(store)(value);\n    }\n  };\n\n  const isLightMode = activeTheme === Theme.LIGHT;\n\n  const setTheme = (theme) => {\n    dispatch(actions.setActiveTheme(theme));\n  };\n\n  return (\n    <>\n      <SearchWrapper\n        keywords={[t('option.settings.language')]}\n      >\n        <DataElementWrapper\n          className=\"setting-section\"\n          dataElement={DataElements.SETTINGS_LANGUAGE_SECTION}\n        >\n          <div className=\"setting-label\"><label id=\"language-dropdown-label\">{t('option.settings.language')}</label></div>\n          <Dropdown\n            id=\"language-dropdown\"\n            labelledById='language-dropdown-label'\n            dataElement={DataElements.SETTINGS_LANGUAGE_DROPDOWN}\n            items={Languages}\n            currentSelectionKey={currentLanguage}\n            getKey={(item) => item[0]}\n            getDisplayValue={(item) => item[1]}\n            onClickItem={changeLanguage}\n            maxHeight={200}\n            width={336}\n            getCustomItemStyle={() => ({ textAlign: 'left', width: '326px' })}\n            className=\"language-dropdown\"\n          />\n        </DataElementWrapper>\n      </SearchWrapper>\n      <SearchWrapper\n        keywords={[t('option.settings.theme'), t('option.settings.lightMode'), t('option.settings.darkMode')]}\n      >\n        {!isIE && (\n          <DataElementWrapper\n            className=\"setting-section\"\n            dataElement={DataElements.SETTINGS_THEME_SECTION}\n          >\n            <div className=\"setting-label\"><label>{t('option.settings.theme')}</label></div>\n            <div className=\"theme-options\">\n              <div className={`theme-option ${isLightMode ? 'active-theme' : ''}`}>\n                <Icon glyph=\"icon-light-mode-option\" className=\"light-mode-icon\" />\n                <div className=\"theme-choice\">\n                  <Choice\n                    radio\n                    checked={isLightMode}\n                    onChange={() => setTheme(Theme.LIGHT)}\n                    label={t('option.settings.lightMode')}\n                    name=\"theme_choice\"\n                  />\n                </div>\n              </div>\n              <div className={`theme-option ${!isLightMode ? 'active-theme' : ''}`}>\n                <Icon glyph=\"icon-dark-mode-option\" className=\"dark-mode-icon\" />\n                <div className=\"theme-choice\">\n                  <Choice\n                    radio\n                    checked={!isLightMode}\n                    onChange={() => setTheme(Theme.DARK)}\n                    label={t('option.settings.darkMode')}\n                    name=\"theme_choice\"\n                  />\n                </div>\n              </div>\n            </div>\n          </DataElementWrapper>\n        )}\n      </SearchWrapper>\n    </>\n  );\n};\n\nexport default GeneralTab;\n", "import React, { useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport Button from 'components/Button';\nimport hotkeys from 'hotkeys-js';\nimport hotkeysManager, { defaultHotkeysScope } from 'helpers/hotkeysManager';\nimport ModalWrapper from '../ModalWrapper';\nimport getRootNode from 'helpers/getRootNode';\nimport classNames from 'classnames';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nimport './EditKeyboardShortcutModal.scss';\n\nconst editShortcutHotkeysScope = 'editShortcut';\n\nconst EditKeyboardShortcutModal = ({ currentShortcut, finishEditing, getCommandStrings }) => {\n  const [t] = useTranslation();\n  const shortcutKeyMap = useSelector(selectors.getShortcutKeyMap);\n\n  const [currentCommand, setCurrentCommand] = useState('');\n  const [isRecording, setIsRecording] = useState(false);\n  const [pressedKeys, setPressedKeys] = useState(new Set());\n  const [activatedByKeyboard, setActivatedByKeyboard] = useState(false);\n\n  useEffect(() => {\n    setCurrentCommand(shortcutKeyMap[currentShortcut]);\n  }, [currentShortcut, shortcutKeyMap]);\n\n  useEffect(() => {\n    if (!isRecording) {\n      return;\n    }\n\n    const keyDownHandler = (e) => {\n      e.preventDefault();\n      const keyName = getKeyString(e);\n      setPressedKeys((prevKeys) => new Set([...prevKeys, keyName]));\n    };\n\n    const keyUpHandler = () => {\n      if (pressedKeys.size > 0) {\n        const formattedCommand = Array.from(pressedKeys).join('+').toLowerCase();\n        setCurrentCommand(formattedCommand);\n      }\n      setPressedKeys(new Set());\n      setIsRecording(false);\n\n      // Only focus the button if it was activated via the keyboard\n      if (activatedByKeyboard) {\n        const editButton = getRootNode().querySelector('[data-element=\"EditKeyboardShortcutModalEditButton\"]');\n        if (editButton) {\n          editButton.focus();\n        }\n      }\n    };\n\n    window.addEventListener('keydown', keyDownHandler);\n    window.addEventListener('keyup', keyUpHandler);\n\n    hotkeys.setScope(editShortcutHotkeysScope);\n\n    return () => {\n      window.removeEventListener('keydown', keyDownHandler);\n      window.removeEventListener('keyup', keyUpHandler);\n      hotkeys.setScope(defaultHotkeysScope);\n    };\n  }, [isRecording, pressedKeys, activatedByKeyboard]);\n\n  const startRecording = (e) => {\n    setPressedKeys(new Set());\n\n    // If this was triggered by a mouse click (event.detail > 0), start recording immediately\n    if (e.detail > 0) {\n      setIsRecording(true);\n      setActivatedByKeyboard(false); // Activated by mouse, so no focus after recording\n    } else {\n      // Otherwise, it was a keyboard event, wait for key release before recording\n      setActivatedByKeyboard(true); // Activated by keyboard, so focus after recording\n\n      const keyReleaseHandler = () => {\n        setIsRecording(true);\n        window.removeEventListener('keyup', keyReleaseHandler);\n      };\n\n      window.addEventListener('keyup', keyReleaseHandler);\n    }\n  };\n\n  const onCloseHandler = useFocusOnClose(finishEditing);\n\n  const setShortcut = () => {\n    hotkeys.setScope(defaultHotkeysScope);\n    hotkeysManager.setShortcutKey(currentShortcut, currentCommand);\n    onCloseHandler();\n  };\n\n  const getKeyString = (e) => {\n    switch (e.keyCode) {\n      case 16:\n        return 'shift';\n      case 17:\n        return 'ctrl';\n      case 18:\n        return 'alt';\n      case 91:\n      case 93:\n        return 'command';\n      default:\n        return e.key.toLowerCase();\n    }\n  };\n\n  const hasConflict = hotkeysManager.hasConflict(currentShortcut, currentCommand);\n\n  const renderCurrentKeys = () => getCommandStrings(currentCommand).map((key, i) => (\n    <span key={i}>{key}</span>\n  ));\n\n  return (\n    <div className=\"Modal EditKeyboardShortcutModal open\">\n      <ModalWrapper isOpen={true} title='option.settings.editKeyboardShorcut' onCloseClick={finishEditing}>\n        <div className=\"body\">\n          <div className={classNames({\n            'keyboard-shortcut': true,\n            'recording': isRecording && activatedByKeyboard,\n          })}>{renderCurrentKeys()}</div>\n          {hasConflict && (<div className=\"conflict-warning\">{t('option.settings.shortcutAlreadyExists')}</div>)}\n        </div>\n        <div className=\"divider\"></div>\n        <div className=\"footer\">\n          <Button\n            className=\"modal-button secondary-btn-custom\"\n            label={t('option.settings.editShortcut')}\n            disabled={isRecording}\n            onClick={startRecording}\n            dataElement='EditKeyboardShortcutModalEditButton'\n          />\n          <Button\n            className=\"modal-button confirm\"\n            label={t('option.settings.setShortcut')}\n            disabled={isRecording || hasConflict}\n            onClick={setShortcut}\n            dataElement='EditKeyboardShortcutModalSetButton'\n          />\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default EditKeyboardShortcutModal;\n", "import React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport actions from 'actions';\nimport { useTranslation } from 'react-i18next';\nimport selectors from 'selectors';\nimport Button from 'components/Button';\nimport DataElements from 'constants/dataElement';\nimport { Shortcuts } from 'helpers/hotkeysManager';\nimport { isMac } from 'helpers/device';\nimport EditKeyboardShortcutModal from './EditKeyboardShortcutModal';\nimport { SearchWrapper } from './SearchWrapper';\nimport useFocusHandler from 'hooks/useFocusHandler';\n\nimport './KeyboardShortcutTab.scss';\n\nconst keyboardShortcuts = [\n  [Shortcuts.ROTATE_CLOCKWISE, 'option.settings.rotateDocumentClockwise'],\n  [Shortcuts.ROTATE_COUNTER_CLOCKWISE, 'option.settings.rotateDocumentCounterclockwise'],\n  [Shortcuts.COPY, 'option.settings.copyText'],\n  [Shortcuts.PASTE, 'option.settings.pasteText'],\n  [Shortcuts.UNDO, 'option.settings.undoChange'],\n  [Shortcuts.REDO, 'option.settings.redoChange'],\n  [Shortcuts.OPEN_FILE, 'option.settings.openFile'],\n  [Shortcuts.SEARCH, 'option.settings.openSearch'],\n  [Shortcuts.ZOOM_IN, 'option.settings.zoomIn'],\n  [Shortcuts.ZOOM_OUT, 'option.settings.zoomOut'],\n  [Shortcuts.SET_HEADER_FOCUS, 'option.settings.setHeaderFocus'],\n  [Shortcuts.FIT_SCREEN_WIDTH, 'option.settings.fitScreenWidth'],\n  [Shortcuts.PRINT, 'option.settings.print'],\n  [Shortcuts.BOOKMARK, 'option.settings.bookmarkOpenPanel'],\n  [Shortcuts.PREVIOUS_PAGE, 'option.settings.goToPreviousPage'],\n  [Shortcuts.NEXT_PAGE, 'option.settings.goToNextPage'],\n  [Shortcuts.UP, 'option.settings.goToPreviousPageArrowUp'],\n  [Shortcuts.DOWN, 'option.settings.goToNextPageArrowDown'],\n  [Shortcuts.SWITCH_PAN, 'option.settings.holdSwitchPan'],\n  [Shortcuts.SELECT, 'option.settings.selectAnnotationEdit'],\n  [Shortcuts.PAN, 'option.settings.selectPan'],\n  [Shortcuts.ARROW, 'option.settings.selectCreateArrowTool'],\n  [Shortcuts.CALLOUT, 'option.settings.selectCreateCalloutTool'],\n  [Shortcuts.ERASER, 'option.settings.selectEraserTool'],\n  [Shortcuts.FREEHAND, 'option.settings.selectCreateFreeHandTool'],\n  [Shortcuts.IMAGE, 'option.settings.selectCreateStampTool'],\n  [Shortcuts.LINE, 'option.settings.selectCreateLineTool'],\n  [Shortcuts.STICKY_NOTE, 'option.settings.selectCreateStickyTool'],\n  [Shortcuts.ELLIPSE, 'option.settings.selectCreateEllipseTool'],\n  [Shortcuts.RECTANGLE, 'option.settings.selectCreateRectangleTool'],\n  [Shortcuts.RUBBER_STAMP, 'option.settings.selectCreateRubberStampTool'],\n  [Shortcuts.FREETEXT, 'option.settings.selectCreateFreeTextTool'],\n  [Shortcuts.SIGNATURE, 'option.settings.openSignatureModal'],\n  [Shortcuts.SQUIGGLY, 'option.settings.selectCreateTextSquigglyTool'],\n  [Shortcuts.HIGHLIGHT, 'option.settings.selectCreateTextHighlightTool'],\n  [Shortcuts.STRIKEOUT, 'option.settings.selectCreateTextStrikeoutTool'],\n  [Shortcuts.UNDERLINE, 'option.settings.selectCreateTextUnderlineTool'],\n  [Shortcuts.CLOSE, 'option.settings.close'],\n];\n\nconst KeyboardShortcutTab = () => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const shortcutKeyMap = useSelector(selectors.getShortcutKeyMap);\n\n  const [currentShortcut, setCurrentShortcut] = useState(undefined);\n\n  const getCommandStrings = (command) => {\n    if (!command) {\n      return [];\n    }\n    command = command.toUpperCase();\n    if (command.includes(', COMMAND')) {\n      const commands = command.split(', ');\n      command = isMac ? commands[1] : commands[0];\n    }\n    return command.split(/(\\+)/g);\n  };\n\n  const editShortcut = (shortcut) => {\n    dispatch(actions.setIsElementHidden(DataElements.SETTINGS_MODAL, true));\n    setCurrentShortcut(shortcut);\n  };\n\n  const finishEditing = () => {\n    setCurrentShortcut(undefined);\n    dispatch(actions.setIsElementHidden(DataElements.SETTINGS_MODAL, false));\n  };\n\n  const focusHandler = useFocusHandler((e) => {\n    const shortcut = e.currentTarget.getAttribute('data-element').replace('edit-button-', '');\n    editShortcut(shortcut);\n  });\n\n  return (\n    <>\n      <div className=\"shortcut-table-header\">\n        <div className=\"shortcut-table-header-command\">{t('option.settings.command')}</div>\n        <div className=\"shortcut-table-header-description\">{t('option.settings.description')}</div>\n        <div className=\"shortcut-table-header-action\">{t('option.settings.action')}</div>\n      </div>\n      <div className=\"shortcut-table-content\">\n        {keyboardShortcuts.map(([command, description]) => (\n          <SearchWrapper\n            key={command}\n            keywords={[t(description)]}\n          >\n            <div className=\"shortcut-table-item\">\n              <div className=\"shortcut-table-item-command\">\n                {getCommandStrings(shortcutKeyMap[command]).map((str, i) => (\n                  <span key={i}>{str}</span>\n                ))}\n              </div>\n              <div className=\"shortcut-table-item-description\">\n                {t(description)}\n              </div>\n              <Button\n                dataElement={`edit-button-${command}`}\n                img=\"icon-edit-form-field\"\n                title={t('action.edit')}\n                ariaLabel={`${t(description)} ${t('action.edit')}`}\n                onClick={focusHandler}\n              />\n            </div>\n          </SearchWrapper>\n        ))}\n      </div>\n      {currentShortcut && (\n        <EditKeyboardShortcutModal\n          currentShortcut={currentShortcut}\n          finishEditing={finishEditing}\n          getCommandStrings={getCommandStrings}\n        />\n      )}\n    </>\n  );\n};\n\nexport default KeyboardShortcutTab;\n", "import React, { useState, useCallback } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport { useTranslation } from 'react-i18next';\nimport touchEventManager from 'helpers/TouchEventManager';\nimport Choice from 'components/Choice';\nimport { SearchWrapper } from './SearchWrapper';\n\nimport './AdvancedTab.scss';\n\nconst createItem = (label, description, isChecked, onToggled) => ({ label, description, isChecked, onToggled });\n\nconst AdvancedTab = () => {\n  const [\n    shouldFadePageNavigationComponent,\n    isNoteSubmissionWithEnterEnabled,\n    isCommentThreadExpansionEnabled,\n    isNotesPanelRepliesCollapsingEnabled,\n    isNotesPanelTextCollapsingEnabled,\n    shouldClearSearchPanelOnClose,\n    pageDeletionConfirmationModalEnabled,\n    isThumbnailSelectingPages,\n    customSettings,\n    isToolDefaultStyleUpdateFromAnnotationPopupEnabled\n  ] = useSelector((state) => [\n    selectors.shouldFadePageNavigationComponent(state),\n    selectors.isNoteSubmissionWithEnterEnabled(state),\n    selectors.isCommentThreadExpansionEnabled(state),\n    selectors.isNotesPanelRepliesCollapsingEnabled(state),\n    selectors.isNotesPanelTextCollapsingEnabled(state),\n    selectors.shouldClearSearchPanelOnClose(state),\n    selectors.pageDeletionConfirmationModalEnabled(state),\n    selectors.isThumbnailSelectingPages(state),\n    selectors.getCustomSettings(state),\n    selectors.isToolDefaultStyleUpdateFromAnnotationPopupEnabled(state)\n  ]);\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const [, updateState] = useState();\n  const forceUpdate = useCallback(() => updateState({}), []);\n\n  const viewingItems = [\n    createItem(\n      t('option.settings.disableFadePageNavigationComponent'),\n      t('option.settings.disableFadePageNavigationComponentDesc'),\n      !shouldFadePageNavigationComponent,\n      (enable) => {\n        enable ? dispatch(actions.disableFadePageNavigationComponent()) : dispatch(actions.enableFadePageNavigationComponent());\n      }\n    ),\n    createItem(\n      t('option.settings.disableNativeScrolling'),\n      t('option.settings.disableNativeScrollingDesc'),\n      !touchEventManager.useNativeScroll,\n      (enable) => {\n        touchEventManager.useNativeScroll = !enable;\n      }\n    )\n  ];\n\n  const annotationsItems = [\n    createItem(\n      t('option.settings.disableToolDefaultStyleUpdateFromAnnotationPopup'),\n      t('option.settings.disableToolDefaultStyleUpdateFromAnnotationPopupDesc'),\n      !isToolDefaultStyleUpdateFromAnnotationPopupEnabled,\n      (enable) => dispatch(actions.setToolDefaultStyleUpdateFromAnnotationPopupEnabled(!enable))\n    )\n  ];\n\n  const notesPanelItems = [\n    createItem(\n      t('option.settings.disableNoteSubmissionWithEnter'),\n      t('option.settings.disableNoteSubmissionWithEnterDesc'),\n      !isNoteSubmissionWithEnterEnabled,\n      (enable) => dispatch(actions.setNoteSubmissionEnabledWithEnter(!enable))\n    ),\n    createItem(\n      t('option.settings.disableAutoExpandCommentThread'),\n      t('option.settings.disableAutoExpandCommentThreadDesc'),\n      !isCommentThreadExpansionEnabled,\n      (enable) => dispatch(actions.setCommentThreadExpansion(!enable))\n    ),\n    createItem(\n      t('option.settings.disableReplyCollapse'),\n      t('option.settings.disableReplyCollapseDesc'),\n      !isNotesPanelRepliesCollapsingEnabled,\n      (enable) => dispatch(actions.setNotesPanelRepliesCollapsing(!enable))\n    ),\n    createItem(\n      t('option.settings.disableTextCollapse'),\n      t('option.settings.disableTextCollapseDesc'),\n      !isNotesPanelTextCollapsingEnabled,\n      (enable) => dispatch(actions.setNotesPanelTextCollapsing(!enable))\n    )\n  ];\n\n  const searchItems = [\n    createItem(\n      t('option.settings.disableClearSearchOnPanelClose'),\n      t('option.settings.disableClearSearchOnPanelCloseDesc'),\n      !shouldClearSearchPanelOnClose,\n      (enable) => dispatch(actions.setClearSearchOnPanelClose(!enable))\n    )\n  ];\n\n  const pageManipulationItems = [\n    createItem(\n      t('option.settings.disablePageDeletionConfirmationModal'),\n      t('option.settings.disablePageDeletionConfirmationModalDesc'),\n      !pageDeletionConfirmationModalEnabled,\n      (enable) => {\n        enable ? dispatch(actions.disablePageDeletionConfirmationModal()) : dispatch(actions.enablePageDeletionConfirmationModal());\n      }\n    ),\n    createItem(\n      t('option.settings.disableMultiselect'),\n      t('option.settings.disableMultiselectDesc'),\n      !isThumbnailSelectingPages,\n      (enable) => dispatch(actions.setThumbnailSelectingPages(!enable))\n    )\n  ];\n\n  const sections = [\n    [t('option.settings.viewing'), viewingItems],\n    [t('option.settings.annotations'), annotationsItems],\n    [t('option.settings.notesPanel'), notesPanelItems],\n    [t('option.settings.search'), searchItems],\n    [t('option.settings.pageManipulation'), pageManipulationItems],\n    [t('option.settings.miscellaneous'), customSettings]\n  ];\n\n  const getSectionKeywords = (sectionTitle, sectionItems) => {\n    return [\n      sectionTitle,\n      sectionItems.map((item) => [item.label, item.description]).flat()\n    ].flat();\n  };\n\n  return (\n    <>\n      {sections.map(([sectionTitle, sectionItems]) => ((sectionItems.length < 1) ? null : (\n        <SearchWrapper\n          key={sectionTitle}\n          keywords={getSectionKeywords(sectionTitle, sectionItems)}\n        >\n          <div className=\"setting-section\">\n            <div className=\"setting-label\"><label>{sectionTitle}</label></div>\n            {sectionItems.map((item) => (\n              <SearchWrapper\n                key={item.label}\n                keywords={[sectionTitle, item.label, item.description]}\n              >\n                <div className=\"setting-item\">\n                  <div className=\"setting-item-info\">\n                    <div className=\"setting-item-label\">{item.label}</div>\n                    <div>{item.description}</div>\n                  </div>\n                  <Choice\n                    aria-label={item.label}\n                    isSwitch\n                    checked={(typeof item.isChecked === 'function') ? item.isChecked() : item.isChecked}\n                    onChange={(e) => {\n                      item.onToggled(e.target.checked);\n                      forceUpdate();\n                    }}\n                  />\n                </div>\n              </SearchWrapper>\n            ))}\n          </div>\n        </SearchWrapper>\n      )))}\n    </>\n  );\n};\n\nexport default AdvancedTab;\n", "import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport selectors from 'selectors';\nimport classNames from 'classnames';\nimport actions from 'actions';\nimport DataElements from 'constants/dataElement';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport GeneralTab from './GeneralTab';\nimport KeyboardShortcutTab from './KeyboardShortcutTab';\nimport AdvancedTab from './AdvancedTab';\nimport { SearchContext } from './SearchWrapper';\nimport Icon from 'components/Icon';\nimport ModalWrapper from 'components/ModalWrapper';\nimport './SettingsModal.scss';\n\nconst TABS_ID = DataElements.SETTINGS_MODAL;\n\nconst SettingsModal = () => {\n  const isDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.SETTINGS_MODAL));\n  const isOpen = useSelector((state) => selectors.isElementOpen(state, DataElements.SETTINGS_MODAL));\n  const isSpreadsheetEditorMode = useSelector(selectors.isSpreadsheetEditorModeEnabled);\n  const selectedTab = useSelector((state) => selectors.getSelectedTab(state, TABS_ID));\n  const isGeneralTabDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.SETTINGS_GENERAL_BUTTON));\n  const isKeyboardTabDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.SETTINGS_KEYBOARD_BUTTON));\n  const isAdvancedTabDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.SETTINGS_ADVANCED_BUTTON));\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const [searchTerm, setSearchTerm] = useState('');\n\n  const tabs = [\n    [DataElements.SETTINGS_GENERAL_BUTTON, t('option.settings.general')],\n    [DataElements.SETTINGS_KEYBOARD_BUTTON, t('option.settings.keyboardShortcut')],\n    [DataElements.SETTINGS_ADVANCED_BUTTON, t('option.settings.advancedSetting')]\n  ];\n\n  useEffect(() => {\n    if (\n      (selectedTab === DataElements.SETTINGS_GENERAL_BUTTON && isGeneralTabDisabled) ||\n      (selectedTab === DataElements.SETTINGS_KEYBOARD_BUTTON && isKeyboardTabDisabled) ||\n      (selectedTab === DataElements.SETTINGS_ADVANCED_BUTTON && isAdvancedTabDisabled)\n    ) {\n      let tabToEnable = '';\n      if (!isGeneralTabDisabled) {\n        tabToEnable = DataElements.SETTINGS_GENERAL_BUTTON;\n      } else if (!isKeyboardTabDisabled) {\n        tabToEnable = DataElements.SETTINGS_KEYBOARD_BUTTON;\n      } else if (!isAdvancedTabDisabled) {\n        tabToEnable = DataElements.SETTINGS_ADVANCED_BUTTON;\n      }\n      dispatch(actions.setSelectedTab(TABS_ID, tabToEnable));\n    }\n  }, [isGeneralTabDisabled, isKeyboardTabDisabled, isAdvancedTabDisabled]);\n\n  const closeModal = () => {\n    dispatch(actions.closeElement(DataElements.SETTINGS_MODAL));\n  };\n\n  const handleTabClicked = (tab) => {\n    if (tab !== selectedTab) {\n      dispatch(actions.setSelectedTab(TABS_ID, tab));\n    }\n  };\n\n  if (isDisabled) {\n    return null;\n  }\n\n  const className = classNames('Modal', 'SettingsModal', 'open');\n\n  return (\n    <SearchContext.Provider value={searchTerm}>\n      <div className={className} data-element={DataElements.SettingsModal}>\n        <ModalWrapper\n          title={t('option.settings.settings')}\n          closeHandler={closeModal}\n          onCloseClick={closeModal}\n          isOpen={isOpen}\n          swipeToClose\n        >\n          <div className=\"container\">\n            <div className=\"swipe-indicator\" />\n            <div className=\"header\">\n              <div className=\"settings-search-input\">\n                <Icon glyph=\"icon-header-search\" />\n                <input\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  aria-label={t('message.searchSettingsPlaceholder')}\n                />\n              </div>\n            </div>\n            <div className=\"divider\" />\n            <div className=\"body\">\n              <div className=\"settings-tabs-container\">\n                <div className=\"settings-tabs\">\n                  {tabs\n                    .filter(([tab]) => {\n                      if (isSpreadsheetEditorMode) {\n                        return tab !== DataElements.SETTINGS_KEYBOARD_BUTTON && tab !== DataElements.SETTINGS_ADVANCED_BUTTON;\n                      }\n                      return true;\n                    })\n                    .map(([tab, title]) => {\n                      const className = classNames('settings-tab', {\n                        selected: tab === selectedTab\n                      });\n                      return (\n                        <DataElementWrapper\n                          type=\"button\"\n                          className={className}\n                          dataElement={tab}\n                          onClick={() => handleTabClicked(tab)}\n                          key={tab}\n                          aria-selected={tab === selectedTab}\n                          aria-current={tab === selectedTab ? 'page' : null}\n                        >\n                          {title}\n                        </DataElementWrapper>\n                      );\n                    })\n                  }\n                </div>\n              </div>\n              <div className={classNames('settings-content', { KeyboardShortcutTab: selectedTab === DataElements.SETTINGS_KEYBOARD_BUTTON })}>\n                {selectedTab === DataElements.SETTINGS_GENERAL_BUTTON && (\n                  <GeneralTab />\n                )}\n                {selectedTab === DataElements.SETTINGS_KEYBOARD_BUTTON && (\n                  <KeyboardShortcutTab />\n                )}\n                {selectedTab === DataElements.SETTINGS_ADVANCED_BUTTON && (\n                  <AdvancedTab />\n                )}\n              </div>\n            </div>\n          </div>\n        </ModalWrapper>\n      </div>\n    </SearchContext.Provider>\n  );\n};\n\nexport default SettingsModal;", "import SettingsModal from './SettingsModal';\n\nexport default SettingsModal;"], "sourceRoot": ""}