{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/outlineFlyoutHelper.js", "webpack:///./src/ui/src/hooks/useNestingLevel.js", "webpack:///./src/ui/src/helpers/createItemsForBookmarkOutlineFlyout.js", "webpack:///./src/ui/src/components/PanelListItem/PanelListItem.js", "webpack:///./src/ui/src/components/PanelListItem/PanelListItem.scss?23f7", "webpack:///./src/ui/src/components/PanelListItem/PanelListItem.scss"], "names": ["createFlyoutItem", "option", "icon", "label", "title", "dataElement", "toUpperCase", "slice", "menuTypes", "OPENFILE", "RENAME", "SETDEST", "DOWNLOAD", "DELETE", "OPENFORMFIELDPANEL", "MOVE_UP", "MOVE_DOWN", "MOVE_LEFT", "MOVE_RIGHT", "menuItems", "useNestingLevel", "elementRef", "nestingSelector", "useState", "level", "setLevel", "useEffect", "currentLevel", "pointer", "current", "parentElement", "closest", "createItemsForBookmarkOutlineFlyout", "type", "shouldHideDeleteButton", "handleOnClick", "hiddenRules", "includes", "map", "item", "hidden", "onClick", "PanelList<PERSON><PERSON><PERSON><PERSON>", "children", "length", "className", "child", "key", "propTypes", "PropTypes", "oneOfType", "arrayOf", "node", "PanelItemContent", "React", "memo", "iconGlyph", "labelHeader", "onDoubleClick", "useI18String", "textColor", "isActive", "Icon", "glyph", "<PERSON><PERSON>", "style", "color", "aria<PERSON><PERSON><PERSON>", "classNames", "displayName", "string", "isRequired", "func", "bool", "PanelListItem", "checkboxOptions", "contentMenuFlyoutOptions", "contextMenuMoreButtonOptions", "description", "enableMoreOptionsContextMenuFlyout", "expanded", "setIsExpandedHandler", "panelListItemRef", "useRef", "currentNestingLevel", "isExpanded", "setIsExpanded", "t", "useTranslation", "dispatch", "useDispatch", "currentFlyout", "flyoutSelector", "flyoutToggleElement", "moreOptionsDataElement", "showCheckBox", "disabled", "data-element", "ref", "Choice", "role", "id", "aria-label", "aria-checked", "checked", "onChange", "toggled", "visible", "img", "ariaExpanded", "ToggleElementButton", "toggleElement", "bookmarkOutlineFlyout", "items", "actions", "updateFlyout", "addFlyout", "setFlyoutToggleElement", "shape", "object", "api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals"], "mappings": "yKAAMA,EAAmB,SAACC,EAAQC,EAAMC,GAAK,MAAM,CACjDD,OACAC,QACAC,MAAOD,EACPF,SACAI,YAAa,GAAF,OAAKJ,EAAO,GAAGK,cAAiBL,EAAOM,MAAM,GAAE,YAG/CC,EAAY,CACvBC,SAAU,WACVC,OAAQ,SACRC,QAAS,iBACTC,SAAU,WACVC,OAAQ,SACRC,mBAAoB,qBACpBC,QAAS,SACTC,UAAW,WACXC,UAAW,WACXC,WAAY,aAGDC,EAAY,CACvBnB,EAAiBQ,EAAUM,mBAAoB,uBAAwB,eACvEd,EAAiBQ,EAAUC,SAAU,sBAAuB,sBAC5DT,EAAiBQ,EAAUE,OAAQ,oBAAqB,iBACxDV,EAAiBQ,EAAUG,QAAS,iBAAkB,yBACtDX,EAAiBQ,EAAUI,SAAU,gBAAiB,mBACtDZ,EAAiBQ,EAAUK,OAAQ,mBAAoB,iBACvDb,EAAiBQ,EAAUO,QAAS,oBAAqB,iBACzDf,EAAiBQ,EAAUQ,UAAW,sBAAuB,mBAC7DhB,EAAiBQ,EAAUS,UAAW,sBAAuB,mBAC7DjB,EAAiBQ,EAAUU,WAAY,uBAAwB,sB,2vCC7BjE,IAgBeE,EAhBS,SAACC,EAAYC,GACnC,IAAqC,IAAXC,mBAAS,GAAE,GAA9BC,EAAK,KAAEC,EAAQ,KAYtB,OAVAC,qBAAU,WAGR,IAFA,IAAIC,GAAgB,EAChBC,EAAUP,aAAU,EAAVA,EAAYQ,QACnBD,GACLD,IACAC,EAAUA,EAAQE,cAAcC,QAAQT,GAE1CG,EAASE,KACR,CAACN,IAEGG,G,2yCCWMQ,MA1Bf,SAA6Cb,EAAWc,EAAMC,EAAwBC,EAAe3B,GAAW,MACxG4B,GAAW,OACd5B,EAAUK,QAAS,kBAAMqB,KAAsB,IAC/C1B,EAAUI,UAAW,iBAAe,cAATqB,KAAoB,IAC/CzB,EAAUC,UAAW,iBAAe,cAATwB,KAAoB,IAC/CzB,EAAUG,SAAU,iBAAe,YAATsB,KAAkB,IAC5CzB,EAAUM,oBAAqB,iBAAM,CAAC,YAAa,YAAYuB,SAASJ,MAAK,IAC7EzB,EAAUS,WAAY,iBAAe,YAATgB,KAAkB,IAC9CzB,EAAUU,YAAa,iBAAe,YAATe,KAAkB,IAC/CzB,EAAUO,SAAU,iBAAe,YAATkB,GAA+B,cAATA,KAAoB,IACpEzB,EAAUQ,WAAY,iBAAe,YAATiB,GAA+B,cAATA,KAAoB,GAGzE,OAAOd,EAAUmB,KAAI,SAACC,GACpB,IAAQtC,EAAWsC,EAAXtC,OACFuC,IAASJ,EAAYnC,IAAUmC,EAAYnC,KAEjD,OAAO,EAAP,KACKsC,GAAI,IACPC,SACAnC,YAAa,GAAF,OAAK4B,GAAI,OAAGM,EAAKlC,aAC5BoC,QAAS,kBAAMN,EAAcI,EAAKtC,e,ujCCNxC,IAAMyC,EAAoB,SAAH,GAAqB,IAAfC,EAAQ,EAARA,SAC3B,OAAKA,GAAgC,IAApBA,EAASC,OAIxB,wBAAIC,UAAU,uBACXF,EAASL,KAAI,SAACQ,GAAK,OAClB,wBAAIC,IAAKD,aAAK,EAALA,EAAOC,KAAMD,OALnB,MAWXJ,EAAkBM,UAAY,CAC5BL,SAAUM,IAAUC,UAAU,CAC5BD,IAAUE,QAAQF,IAAUG,MAC5BH,IAAUG,QAId,IAAMC,EAAmBC,IAAMC,MAAK,gBAClCC,EAAS,EAATA,UACAC,EAAW,EAAXA,YACAC,EAAa,EAAbA,cACAjB,EAAO,EAAPA,QACAkB,EAAY,EAAZA,aACAC,EAAS,EAATA,UACAC,EAAQ,EAARA,SAAQ,OAER,oCACGL,GACC,yBAAKX,UAAU,6BACb,kBAACiB,EAAA,EAAI,CAACC,MAAOP,KAGjB,yBAAKX,UAAW,6BACd,yBAAKA,UAAU,2BACb,kBAACmB,EAAA,EAAM,CACLC,MAAO,CAAEC,MAAON,GAAa,WAC7BO,UAAWV,EACXtD,MAAOsD,EACPC,cAAeA,EACfjB,QAASA,EACTI,UAAWuB,IAAW,CACpB,YAAaP,IAEfF,aAAcA,UAOxBN,EAAiBgB,YAAc,mBAE/BhB,EAAiBL,UAAY,CAC3BQ,UAAWP,IAAUqB,OACrBb,YAAaR,IAAUqB,OAAOC,WAC9Bb,cAAeT,IAAUuB,KACzB/B,QAASQ,IAAUuB,KACnBb,aAAcV,IAAUwB,KACxBb,UAAWX,IAAUqB,OACrBT,SAAUZ,IAAUwB,MAGtB,IAAMC,EAAgB,SAAH,GAgBb,IAfJC,EAAe,EAAfA,gBACAhC,EAAQ,EAARA,SAAQ,IACRiC,gCAAwB,IAAG,KAAE,MAC7BC,oCAA4B,IAAG,KAAE,EACjCC,EAAW,EAAXA,YACAC,EAAkC,EAAlCA,mCACAvB,EAAS,EAATA,UACAC,EAAW,EAAXA,YAAW,IACXE,oBAAY,IAAG,GAAI,MACnBD,qBAAa,IAAG,eAAQ,MACxBjB,eAAO,IAAG,eAAQ,EAClBuC,EAAQ,EAARA,SACAC,EAAoB,EAApBA,qBACArB,EAAS,EAATA,UACAC,EAAQ,EAARA,SAEMqB,EAAmBC,mBACnBC,EAAsBhE,EAAgB8D,GACmB,IAA3B3D,mBAASyD,YAAkB,GAAxDK,EAAU,KAAEC,EAAa,KACxBC,EAAMC,cAAND,EACFE,EAAWC,cACjB,EAMId,EALF1C,8BAAsB,IAAG,GAAK,EAC9ByD,EAIEf,EAJFe,cACAC,EAGEhB,EAHFgB,eACA3D,EAEE2C,EAFF3C,KACAE,EACEyC,EADFzC,cAIA0D,EAEEhB,EAFFgB,oBACAC,EACEjB,EADFiB,uBAkBIC,EAAepB,IAAoBA,EAAgBqB,WAAY,EAOrE,OACE,yBAAKC,eAAa,gBAAgBpD,UAAU,kBAAkBqD,IAAKhB,GACjE,yBAAKrC,UAAWuB,IAAW,CACzB,mBAAmB,EACnB,mBAAoBU,EACpB,mBAAoBA,EACpB,uBAAwBtB,IAAcmB,EACtC,sBAAuBnB,GAAamB,EACpC,sBAAuBnB,GAAamB,KAEpC,yBAAK9B,UAAWuB,IAAW,iBAAD,OAAkBO,EAAkB,iBAAmB,IAAM,wBACpFoB,GACC,yBACE9B,MAAO,CAAE,kBAAmB,GAAF,QAAM,GAAKmB,EAAsB,EAAC,OAC5DvC,UAAU,YAEV,kBAACsD,EAAA,EAAM,CACLC,KAAK,WACLC,GAAI1B,aAAe,EAAfA,EAAiB0B,GACrBC,aAAY3B,aAAe,EAAfA,EAAiBR,UAC7BoC,eAAc5B,aAAe,EAAfA,EAAiB6B,QAC/BA,QAAS7B,aAAe,EAAfA,EAAiB6B,QAC1BC,SAAU9B,aAAe,EAAfA,EAAiB8B,YAKjC,yBACEhE,QAAS,WACP6C,GAAeD,GACXJ,GACFA,GAAsBI,IAG1BxC,UAAWuB,IAAW,CACpB,qBAAqB,EACrBsC,QAASrB,EACTsB,QAAShE,GAAYA,EAASC,OAAS,KAGzC,kBAACoB,EAAA,EAAM,CACL4C,IAAI,qBACJ/D,UAAU,oBACVgE,aAAcxB,EACdlB,UAAS,UAAkBoB,EAAbF,EAAe,kBAAuB,iBAAgB,YAAI5B,MAI5E,kBAACJ,EAAgB,CACfG,UAAWA,EACXC,YAAaA,EACbC,cAAeA,EACfjB,QAASA,EACTkB,aAAcA,EACdC,UAAWA,EACXC,SAAUA,IAEXkB,GACC,yBAAKlC,UAAU,2BACb,kBAACiE,EAAA,EAAmB,CAClBjE,UAAU,qBACVzC,MAAK,UAAKmF,EAAE,kCAAiC,YAAI9B,GACjDsD,cAAelB,EACfxF,YAAayF,EACbc,IAAI,iBACJZ,UAAU,EACVvD,QAxFO,WACnB,IAAMuE,EAAwB,CAC5B3G,YAAauF,EACb/C,UAAW,+BACXoE,MAAOjF,EAAoCb,IAAWc,EAAMC,EAAwBC,EAAe3B,MAKnGiF,EAHGE,EAGMuB,IAAQC,aAAavB,EAAgBoB,GAFrCE,IAAQE,UAAUJ,IAI7BvB,EAASyB,IAAQG,uBAAuBvB,IACxCL,EAASyB,IAAQH,cAAcnB,SAiF1Bd,GACC,yBAAKjC,UAAU,0BAA0BiC,IAG5CO,GAAc,kBAAC,EAAiB,KAAE1C,KAKzC+B,EAAc1B,UAAY,CACxB2B,gBAAiB1B,IAAUqE,MAAM,CAC/BjB,GAAIpD,IAAUqB,OACdkC,QAASvD,IAAUwB,KACnBgC,SAAUxD,IAAUuB,KACpBL,UAAWlB,IAAUqB,OACrB0B,SAAU/C,IAAUwB,OAEtBd,aAAcV,IAAUwB,KACxBjB,UAAWP,IAAUqB,OACrBb,YAAaR,IAAUqB,OAAOC,WAC9BO,YAAa7B,IAAUqB,OACvBS,mCAAoC9B,IAAUwB,KAC9C9B,SAAUM,IAAUG,KACpBM,cAAeT,IAAUuB,KACzB/B,QAASQ,IAAUuB,KACnBQ,SAAU/B,IAAUwB,KACpBb,UAAWX,IAAUqB,OACrBW,qBAAsBhC,IAAUuB,KAChCI,yBAA0B3B,IAAUqE,MAAM,CACxCpF,uBAAwBe,IAAUwB,KAClCkB,cAAe1C,IAAUsE,OACzB3B,eAAgB3C,IAAUqB,OAC1BrC,KAAMgB,IAAUqB,OAChBnC,cAAec,IAAUuB,OAE3BK,6BAA8B5B,IAAUqE,MAAM,CAC5CzB,oBAAqB5C,IAAUqB,OAC/BwB,uBAAwB7C,IAAUqB,SAEpCT,SAAUZ,IAAUwB,MAGPC,O,qBC1Pf,IAAI8C,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAczF,SACjByF,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAInB,EAAI,EAAGA,EAAIQ,EAAczF,OAAQiF,IAAK,CAC7C,MAAMoB,EAAeZ,EAAcR,GACnC,GAAU,IAANA,EACFoB,EAAaF,WAAWX,YAAYL,GACpCA,EAASmB,OAAS,WACZF,EAAgBpG,OAAS,GAC3BoG,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYrB,EAASqB,iBAIhC,CACL,MAAMD,EAAYpB,EAASsB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP3B,EAAIC,EAASK,GAI1BF,EAAO0B,QAAU7B,EAAQ8B,QAAU,I,sBClEzB3B,EAAO0B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAAClB,EAAOC,EAAI,guGAAmuG", "file": "chunks/chunk.4.js", "sourcesContent": ["const createFlyoutItem = (option, icon, label) => ({\n  icon,\n  label,\n  title: label,\n  option,\n  dataElement: `${option[0].toUpperCase() +  option.slice(1)}Button`,\n});\n\nexport const menuTypes = {\n  OPENFILE: 'openFile',\n  RENAME: 'rename',\n  SETDEST: 'setDestination',\n  DOWNLOAD: 'download',\n  DELETE: 'delete',\n  OPENFORMFIELDPANEL: 'openFormFieldPanel',\n  MOVE_UP: 'moveUp',\n  MOVE_DOWN: 'moveDown',\n  MOVE_LEFT: 'moveLeft',\n  MOVE_RIGHT: 'moveRight',\n};\n\nexport const menuItems = [\n  createFlyoutItem(menuTypes.OPENFORMFIELDPANEL, 'icon-edit-form-field', 'action.edit'),\n  createFlyoutItem(menuTypes.OPENFILE, 'icon-portfolio-file', 'portfolio.openFile'),\n  createFlyoutItem(menuTypes.RENAME, 'ic_edit_page_24px', 'action.rename'),\n  createFlyoutItem(menuTypes.SETDEST, 'icon-thumbtack', 'action.setDestination'),\n  createFlyoutItem(menuTypes.DOWNLOAD, 'icon-download', 'action.download'),\n  createFlyoutItem(menuTypes.DELETE, 'icon-delete-line', 'action.delete'),\n  createFlyoutItem(menuTypes.MOVE_UP, 'icon-page-move-up', 'action.moveUp'),\n  createFlyoutItem(menuTypes.MOVE_DOWN, 'icon-page-move-down', 'action.moveDown'),\n  createFlyoutItem(menuTypes.MOVE_LEFT, 'icon-page-move-left', 'action.moveLeft'),\n  createFlyoutItem(menuTypes.MOVE_RIGHT, 'icon-page-move-right', 'action.moveRight'),\n];", "import { useState, useEffect } from 'react';\n\nconst useNestingLevel = (elementRef, nestingSelector) => {\n  const [level, setLevel] = useState(1);\n\n  useEffect(() => {\n    let currentLevel = -1;\n    let pointer = elementRef?.current;\n    while (pointer) {\n      currentLevel++;\n      pointer = pointer.parentElement.closest(nestingSelector);\n    }\n    setLevel(currentLevel);\n  }, [elementRef]);\n\n  return level;\n};\n\nexport default useNestingLevel;", "function createItemsForBookmarkOutlineFlyout(menuItems, type, shouldHideDeleteButton, handleOnClick, menuTypes) {\n  const hiddenRules = {\n    [menuTypes.DELETE]: () => shouldHideDeleteButton,\n    [menuTypes.DOWNLOAD]: () => type !== 'portfolio',\n    [menuTypes.OPENFILE]: () => type !== 'portfolio',\n    [menuTypes.SETDEST]: () => type !== 'outline',\n    [menuTypes.OPENFORMFIELDPANEL]: () => ['portfolio', 'bookmark'].includes(type),\n    [menuTypes.MOVE_LEFT]: () => type !== 'outline',\n    [menuTypes.MOVE_RIGHT]: () => type !== 'outline',\n    [menuTypes.MOVE_UP]: () => type !== 'outline' && type !== 'portfolio',\n    [menuTypes.MOVE_DOWN]: () => type !== 'outline' && type !== 'portfolio',\n  };\n\n  return menuItems.map((item) => {\n    const { option } = item;\n    const hidden = hiddenRules[option] ? hiddenRules[option]() : false;\n\n    return {\n      ...item,\n      hidden,\n      dataElement: `${type}${item.dataElement}`,\n      onClick: () => handleOnClick(item.option),\n    };\n  });\n}\n\nexport default createItemsForBookmarkOutlineFlyout;", "import React, { useRef, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport classNames from 'classnames';\nimport { useTranslation } from 'react-i18next';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\nimport Button from '../Button';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport Icon from 'components/Icon';\nimport './PanelListItem.scss';\nimport useNestingLevel from 'src/hooks/useNestingLevel';\nimport createItemsForBookmarkOutlineFlyout from 'helpers/createItemsForBookmarkOutlineFlyout';\nimport { menuItems, menuTypes } from 'helpers/outlineFlyoutHelper';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions';\n\nconst PanelListChildren = ({ children }) => {\n  if (!children || children.length === 0) {\n    return null;\n  }\n  return (\n    <ul className=\"panel-list-children\">\n      {children.map((child) => (\n        <li key={child?.key}>{child}</li>\n      ))}\n    </ul>\n  );\n};\n\nPanelListChildren.propTypes = {\n  children: PropTypes.oneOfType([\n    PropTypes.arrayOf(PropTypes.node),\n    PropTypes.node\n  ])\n};\n\nconst PanelItemContent = React.memo(({\n  iconGlyph,\n  labelHeader,\n  onDoubleClick,\n  onClick,\n  useI18String,\n  textColor,\n  isActive,\n}) => (\n  <>\n    {iconGlyph && (\n      <div className=\"panel-list-icon-container\">\n        <Icon glyph={iconGlyph} />\n      </div>\n    )}\n    <div className={'panel-list-text-container'}>\n      <div className=\"panel-list-label-header\">\n        <Button\n          style={{ color: textColor || 'inherit' }}\n          ariaLabel={labelHeader}\n          label={labelHeader}\n          onDoubleClick={onDoubleClick}\n          onClick={onClick}\n          className={classNames({\n            'set-focus': isActive,\n          })}\n          useI18String={useI18String}\n        />\n      </div>\n    </div>\n  </>\n));\n\nPanelItemContent.displayName = 'PanelItemContent';\n\nPanelItemContent.propTypes = {\n  iconGlyph: PropTypes.string,\n  labelHeader: PropTypes.string.isRequired,\n  onDoubleClick: PropTypes.func,\n  onClick: PropTypes.func,\n  useI18String: PropTypes.bool,\n  textColor: PropTypes.string,\n  isActive: PropTypes.bool,\n};\n\nconst PanelListItem = ({\n  checkboxOptions,\n  children,\n  contentMenuFlyoutOptions = {},\n  contextMenuMoreButtonOptions = {},\n  description,\n  enableMoreOptionsContextMenuFlyout,\n  iconGlyph,\n  labelHeader,\n  useI18String = true,\n  onDoubleClick = () => {},\n  onClick = () => {},\n  expanded,\n  setIsExpandedHandler,\n  textColor,\n  isActive,\n}) => {\n  const panelListItemRef = useRef();\n  const currentNestingLevel = useNestingLevel(panelListItemRef);\n  const [isExpanded, setIsExpanded] = useState(expanded ?? false);\n  const { t } = useTranslation();\n  const dispatch = useDispatch();\n  const {\n    shouldHideDeleteButton = false,\n    currentFlyout,\n    flyoutSelector,\n    type,\n    handleOnClick,\n  } = contentMenuFlyoutOptions;\n\n  const {\n    flyoutToggleElement,\n    moreOptionsDataElement,\n  } = contextMenuMoreButtonOptions;\n\n  const updateFlyout = () => {\n    const bookmarkOutlineFlyout = {\n      dataElement: flyoutSelector,\n      className: 'MoreOptionsContextMenuFlyout',\n      items: createItemsForBookmarkOutlineFlyout(menuItems, type, shouldHideDeleteButton, handleOnClick, menuTypes),\n    };\n    if (!currentFlyout) {\n      dispatch(actions.addFlyout(bookmarkOutlineFlyout));\n    } else {\n      dispatch(actions.updateFlyout(flyoutSelector, bookmarkOutlineFlyout));\n    }\n    dispatch(actions.setFlyoutToggleElement(moreOptionsDataElement));\n    dispatch(actions.toggleElement(flyoutSelector));\n  };\n\n  const showCheckBox = checkboxOptions && !checkboxOptions.disabled || false;\n\n  /* component layout when all options are enabled:\n  checkbox|chevron| icon |    label header   |menu\n    empty | empty | empty| label description |empty\n  */\n\n  return (\n    <div data-element=\"panelListItem\" className=\"panel-list-item\" ref={panelListItemRef}>\n      <div className={classNames({\n        'panel-list-grid': true,\n        'grid-with-2-rows': description,\n        'grid-with-1-row': !description,\n        'grid-with-3-columns': !iconGlyph && !checkboxOptions,\n        'grid-with-4-columns': iconGlyph || checkboxOptions,\n        'grid-with-5-columns': iconGlyph && checkboxOptions,\n      })} >\n        <div className={classNames(`panel-list-row${checkboxOptions ? ' with-checkbox' : ''}`, 'focusable-container')}>\n          {showCheckBox && (\n            <div\n              style={{ '--checkbox-left': `${-32 * currentNestingLevel + 4}px` }}\n              className=\"checkbox\"\n            >\n              <Choice\n                role=\"checkbox\"\n                id={checkboxOptions?.id}\n                aria-label={checkboxOptions?.ariaLabel}\n                aria-checked={checkboxOptions?.checked}\n                checked={checkboxOptions?.checked}\n                onChange={checkboxOptions?.onChange}\n              />\n            </div>\n          )}\n\n          <div\n            onClick={() => {\n              setIsExpanded(!isExpanded);\n              if (setIsExpandedHandler) {\n                setIsExpandedHandler(!isExpanded);\n              }\n            }}\n            className={classNames({\n              'chevron-container': true,\n              toggled: isExpanded,\n              visible: children && children.length > 0,\n            })}\n          >\n            <Button\n              img=\"icon-chevron-right\"\n              className=\"panel-list-button\"\n              ariaExpanded={isExpanded}\n              ariaLabel={`${isExpanded ? t('action.collapse') : t('action.expand')} ${labelHeader}`}\n            />\n          </div>\n\n          <PanelItemContent\n            iconGlyph={iconGlyph}\n            labelHeader={labelHeader}\n            onDoubleClick={onDoubleClick}\n            onClick={onClick}\n            useI18String={useI18String}\n            textColor={textColor}\n            isActive={isActive}\n          />\n          {enableMoreOptionsContextMenuFlyout && (\n            <div className=\"panel-list-more-options\">\n              <ToggleElementButton\n                className=\"toggle-more-button\"\n                title={`${t('option.searchPanel.moreOptions')} ${labelHeader}`}\n                toggleElement={flyoutToggleElement}\n                dataElement={moreOptionsDataElement}\n                img=\"icon-tool-more\"\n                disabled={false}\n                onClick={updateFlyout}\n              />\n            </div>\n          )}\n        </div>\n        {description && (\n          <div className=\"panel-list-description\">{description}</div>\n        )}\n      </div>\n      {isExpanded && <PanelListChildren>{children}</PanelListChildren>}\n    </div>\n  );\n};\n\nPanelListItem.propTypes = {\n  checkboxOptions: PropTypes.shape({\n    id: PropTypes.string,\n    checked: PropTypes.bool,\n    onChange: PropTypes.func,\n    ariaLabel: PropTypes.string,\n    disabled: PropTypes.bool\n  }),\n  useI18String: PropTypes.bool,\n  iconGlyph: PropTypes.string,\n  labelHeader: PropTypes.string.isRequired,\n  description: PropTypes.string,\n  enableMoreOptionsContextMenuFlyout: PropTypes.bool,\n  children: PropTypes.node,\n  onDoubleClick: PropTypes.func,\n  onClick: PropTypes.func,\n  expanded: PropTypes.bool,\n  textColor: PropTypes.string,\n  setIsExpandedHandler: PropTypes.func,\n  contentMenuFlyoutOptions: PropTypes.shape({\n    shouldHideDeleteButton: PropTypes.bool,\n    currentFlyout: PropTypes.object,\n    flyoutSelector: PropTypes.string,\n    type: PropTypes.string,\n    handleOnClick: PropTypes.func,\n  }),\n  contextMenuMoreButtonOptions: PropTypes.shape({\n    flyoutToggleElement: PropTypes.string,\n    moreOptionsDataElement: PropTypes.string,\n  }),\n  isActive: PropTypes.bool,\n};\n\nexport default PanelListItem;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./PanelListItem.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".panel-list-item{width:100%;display:flex;flex-direction:column;position:relative;box-sizing:border-box;list-style-type:none}.panel-list-item ul{list-style-type:none;margin:0}.panel-list-item li::marker{content:\\\"\\\";margin:0}.panel-list-grid{display:grid;align-items:center;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;margin-top:8px;margin-bottom:8px}.panel-list-grid.grid-with-1-row{grid-template-rows:auto}.panel-list-grid.grid-with-2-rows{grid-template-rows:auto auto}.panel-list-grid.grid-with-3-columns{grid-template-columns:auto minmax(0,1fr) auto}.panel-list-grid.grid-with-4-columns{grid-template-columns:auto auto minmax(0,1fr) auto}.panel-list-grid.grid-with-5-columns{grid-template-columns:auto auto auto minmax(0,1fr) auto}.panel-list-grid:hover .panel-list-more-options,.panel-list-grid[focus-within] .panel-list-more-options{visibility:visible}.panel-list-grid:focus-within .panel-list-more-options,.panel-list-grid:hover .panel-list-more-options{visibility:visible}.panel-list-row{display:contents}.panel-list-row.with-checkbox{padding-left:32px}.panel-list-row .checkbox{margin:0;position:relative;left:0;left:var(--checkbox-left,0)}.panel-list-row .chevron-container{min-width:24px;transition:transform .1s ease;visibility:hidden}.panel-list-row .chevron-container.toggled{transform:rotate(90deg)}.panel-list-row .chevron-container.visible{visibility:visible}.panel-list-row .chevron-container:hover{cursor:pointer;border:none;border-radius:4px;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.panel-list-row .chevron-container .Button{width:24px;height:24px}.panel-list-row .chevron-container .Button .Icon{width:12px;height:12px}.panel-list-row .panel-list-icon-container .Icon{width:24px;height:24px}.panel-list-row .panel-list-text-container{grid-area:1/-3/auto/-2;display:flex;flex-direction:row;height:24px}.panel-list-row .panel-list-text-container .panel-list-label-header{align-content:center;margin:0;width:100%}.panel-list-row .panel-list-text-container .panel-list-label-header .set-focus{color:#2c73ab}.panel-list-row .panel-list-text-container .panel-list-label-header .Button{display:flex;width:auto;max-width:100%;height:100%;padding:2px 0 2px 4px;justify-content:start}.panel-list-row .panel-list-text-container .panel-list-label-header .Button:focus{color:#2c73ab}.panel-list-row .panel-list-text-container .panel-list-label-header .Button:hover{cursor:pointer;border:none;border-radius:4px;box-shadow:unset;color:var(--blue-6)}.panel-list-row .panel-list-text-container .panel-list-label-header .Button span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:inline-block;flex-grow:1}.panel-list-row .panel-list-more-options{grid-area:1/-2/auto/-1;display:flex;justify-content:flex-end;margin-left:2px;visibility:hidden}.panel-list-row .panel-list-more-options .Button{width:24px;height:24px;min-width:24px}.panel-list-row .panel-list-more-options .Button:focus{color:var(--blue-6)}.panel-list-row .panel-list-more-options .Button .Icon{width:12px;height:12px}.panel-list-description{grid-area:2/-3/auto/-2;display:flex;align-items:center;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;padding:2px 0 2px 4px;height:24px}.panel-list-children{padding-left:32px}\", \"\"]);\n\n// exports\n"], "sourceRoot": ""}