{"version": 3, "sources": ["webpack:///./src/ui/src/components/ThumbnailControlsMulti/ThumbnailControlsMulti.scss?a7fc", "webpack:///./src/ui/src/components/ThumbnailControls/ThumbnailControls.scss?2fb7", "webpack:///./src/ui/src/components/ThumbnailControls/ThumbnailControls.scss", "webpack:///./src/ui/src/components/Thumbnail/Thumbnail.scss?dd93", "webpack:///./src/ui/src/components/Thumbnail/Thumbnail.scss", "webpack:///./src/ui/src/components/ThumbnailControlsMulti/ThumbnailControlsMulti.scss", "webpack:///./src/ui/src/components/DocumentControls/DocumentControls.scss?8aa0", "webpack:///./src/ui/src/components/DocumentControls/DocumentControls.scss", "webpack:///./src/ui/src/components/ThumbnailsPanel/ThumbnailsPanel.scss?b775", "webpack:///./src/ui/src/components/ThumbnailsPanel/ThumbnailsPanel.scss", "webpack:///./src/ui/src/components/PageManipulationOverlayButton/PageManipulationOverlayButtonContainer.js", "webpack:///./src/ui/src/components/PageManipulationOverlayButton/index.js", "webpack:///./src/ui/src/components/ThumbnailControls/ThumbnailControls.js", "webpack:///./src/ui/src/components/ThumbnailControls/index.js", "webpack:///./src/ui/src/components/Thumbnail/Thumbnail.js", "webpack:///./src/ui/src/components/Thumbnail/ThumbnailRedux.js", "webpack:///./src/ui/src/components/Thumbnail/index.js", "webpack:///./src/ui/src/components/ThumbnailControlsMulti/MoveOperations/MoveOperations.js", "webpack:///./src/ui/src/components/ThumbnailControlsMulti/ManipulateOperations/ManipulateOperations.js", "webpack:///./src/ui/src/components/ThumbnailControlsMulti/RotateOperations/RotateOperations.js", "webpack:///./src/ui/src/components/ThumbnailControlsMulti/ThumbnailControlsMultiContainer.js", "webpack:///./src/ui/src/components/ThumbnailControlsMulti/index.js", "webpack:///./src/ui/src/components/DocumentControls/DocumentControls.js", "webpack:///./src/ui/src/constants/pageNumberPlaceholder.js", "webpack:///./src/ui/src/components/DocumentControls/index.js", "webpack:///./src/ui/src/helpers/pageManipulation.js", "webpack:///./src/ui/src/components/ThumbnailsPanel/ThumbnailsPanel.js", "webpack:///./src/ui/src/components/ThumbnailsPanel/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "PageManipulationOverlayButtonContainer", "props", "className", "pageIndex", "dispatch", "useDispatch", "selectedPageIndexes", "useSelector", "state", "selectors", "getSelectedThumbnailPageIndexes", "onClick", "indexOf", "actions", "setSelectedPageThumbnails", "ToggleElementButton", "dataElement", "DataElements", "PAGE_MANIPULATION_OVERLAY_BUTTON", "toggleElement", "PAGE_MANIPULATION", "img", "title", "propTypes", "index", "PropTypes", "number", "isRequired", "ThumbnailControls", "t", "useTranslation", "isElementDisabled", "isMoreOptionDisabled", "pageDeletionConfirmationModalEnabled", "isPageDeletionConfirmationModalEnabled", "selectedIndexes", "buttonsRef", "useRef", "buttonContainerRef", "getCurrentPage", "getThumbnailControlMenuItems", "getFeatureFlags", "shallowEqual", "currentPage", "pageThumbnailControlMenuItems", "featureFlags", "pageNumbers", "map", "isCurrentPageInTheSelection", "includes", "customizableUI", "core", "getDocument", "documentType", "type", "isXod", "workerTypes", "XOD", "isOffice", "OFFICE", "LEGACY_OFFICE", "BUTTONS_MAP", "<PERSON><PERSON>", "rotateClockwise", "rotateCounterClockwise", "deletePages", "onClickAnnouncement", "isCustomized", "occurredButtons", "buttons", "item", "key", "component", "React", "cloneElement", "useEffect", "current", "findFocusableElements", "element", "tabIndex", "isWebViewerServerDocument", "data-element", "style", "display", "classNames", "ref", "PageManipulationOverlayButton", "<PERSON><PERSON><PERSON><PERSON>", "forwardRef", "isSelected", "updateAnnotations", "shiftKeyThumbnailPivotIndex", "onFinishLoading", "onLoad", "onRemove", "onDragStart", "onDragOver", "isDraggable", "shouldShowControls", "thumbnailSize", "pageLabe<PERSON>", "isThumbnailMultiselectEnabled", "isReaderModeOrReadOnly", "isMobile", "canLoad", "onCancel", "isThumbnailSelectingPages", "thumbnailSelectionMode", "activeDocumentViewerKey", "panelSelector", "parentKeyListener", "thumbSize", "Number", "useState", "currentFocusIndex", "setCurrentFocusIndex", "thumbContainerRef", "buttonRefs", "buttonMultiSelectRefs", "width", "height", "dimensions", "setDimensions", "loaded", "setLoaded", "isContentEditingEnabled", "loadTimeout", "handleClickOutside", "event", "contains", "target", "preventDefaultTab", "addEventListener", "removeEventListener", "loadThumbnailAsync", "setTimeout", "thumbnail<PERSON><PERSON><PERSON>", "getRootNode", "querySelector", "pageNum", "viewerRotation", "getRotation", "doc", "getPageInfo", "id", "loadCanvas", "pageNumber", "drawComplete", "thumb", "childElement", "<PERSON><PERSON><PERSON><PERSON>", "ratio", "Math", "min", "abs", "cssTransform", "allowUseOfOptimizedThumbnail", "onPagesUpdated", "changes", "contentChanged", "moved", "added", "removed", "isPageAdded", "didPageChange", "some", "changedPage", "didPageMove", "Object", "keys", "movedPage", "parseInt", "isPageRemoved", "newPageCount", "getTotalPages", "onRotationUpdated", "clearTimeout", "useDidUpdate", "isActive", "pageLabel", "checkboxRotateClass", "rotation", "useImperativeHandle", "focusInput", "selectElement", "aria<PERSON>urrent", "focus", "deselectElement", "undefined", "handleEnterGrid", "e", "preventDefault", "elem", "handleKeyDown", "useCallback", "stopPropagation", "leaveFocusActions", "Tab", "Escape", "keyboardActions", "ArrowUp", "handleArrowKey", "ArrowDown", "ArrowLeft", "ArrowRight", "isMultiselectEnabled", "direction", "prevIndex", "newFocusIndex", "updateTabIndexes", "focusedElement", "active", "selected", "onKeyDown", "checkboxToggled", "multiSelectionKeyPressed", "ctrl<PERSON>ey", "metaKey", "shiftKeyPressed", "shift<PERSON>ey", "updatedSelectedPages", "setThumbnailSelectingPages", "shiftKeyPivot", "setShiftKeyThumbnailsPivotIndex", "currSelectMinIndex", "currSelectMaxIndex", "max", "Set", "Array", "from", "_", "thumbnailSelectionModes", "filter", "lastSelectedPageIndex", "closeElement", "setCurrentPage", "cursor", "background", "border", "draggable", "Choice", "checked", "aria-label", "displayName", "bool", "func", "array", "object", "string", "T<PERSON>bnailRedux", "getPageLabels", "isReaderMode", "isDocumentReadOnly", "getShiftKeyThumbnailPivotIndex", "getThumbnailSelectionMode", "getActiveDocumentViewerKey", "selectionModes", "MoveOperations", "moveToTop", "moveToBottom", "ManipulateOperations", "onInsert", "onReplace", "onExtractPages", "onDeletePages", "RotateOperations", "onRotateClockwise", "onRotateCounterClockwise", "ThumbnailControlsMultiContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parentElement", "store", "useStore", "isInMobile", "panelWidth", "getPanel<PERSON>idth", "getLeftPanelWidth", "deleteModalEnabled", "isDesktopOnlyMode", "isInDesktopOnlyMode", "items", "getMultiPageManipulationControlsItems", "displayFlyout", "setDisplayFlyout", "useMemo", "childProps", "noPagesSelectedWarning", "replace", "extractPages", "PAGE_MANIPULATION_OVERLAY", "openElement", "movePagesToTop", "movePagesToBottom", "getBoundingClientRect", "panel<PERSON><PERSON><PERSON><PERSON><PERSON>", "isPanelSmall", "isPanelLarge", "startIndex", "flyoutItems", "skip", "getPageRotationControls", "getPageAdditionalControls", "getPageManipulationControls", "getPageCustomControlsFlyout", "getFlyoutItemsFromType", "flyout", "PAGE_MANIPULATION_FLYOUT_MULTI_SELECT", "updateFlyout", "removeFlyout", "renderedItems", "lastDividerAdded", "actualIndex", "lastIndexToRender", "operations", "operation", "getPageString", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "pagesToPrint", "sortedPages", "sort", "a", "b", "slice", "DocumentControls", "isDisabled", "initialPagesString", "pageString", "setPageString", "previousPageString", "setPreviousPageString", "enableThumbnailSelectingPages", "htmlFor", "name", "onBlur", "selectedPagesString", "value", "pages", "getPageArrayFromString", "pageIndexes", "page", "updatedString", "onChange", "placeholder", "arrayOf", "toggleDocumentControl", "mergeDocument", "srcToMerge", "mergeToPage", "shouldFireEvent", "LOADING_MODAL", "Promise", "resolve", "reject", "then", "mergeResults", "fireEvent", "Events", "DOCUMENT_MERGED", "err", "ThumbnailsPanel", "parentDataElement", "isLeftPanelOpen", "isElementOpen", "totalPages", "isThumbnailMergingEnabled", "getIsThumbnailMergingEnabled", "isThumbnailReorderingEnabled", "getIsThumbnailReorderingEnabled", "isMultipleViewerMerging", "getIsMultipleViewerMerging", "isThumbnailControlDisabled", "isThumbnailSliderDisabled", "totalPagesFromSecondaryDocumentViewer", "isRightClickEnabled", "openingPageManipulationOverlayByRightClickEnabled", "listRef", "pendingThumbs", "thumbs", "afterMovePageNumber", "isOfficeEditor", "setIsOfficeEditor", "setCanLoad", "setHeight", "<PERSON><PERSON><PERSON><PERSON>", "draggingOverPageIndex", "setDraggingOverPageIndex", "isDraggingToPreviousPage", "setDraggingToPreviousPage", "numberOfColumns", "setNumberOfColumns", "isDragging", "setIsDragging", "focusedIndex", "setFocusedIndex", "thumbnailContainerRefs", "thumbnailRefs", "focusedThumbnailRef", "setThumbnailSize", "lastTimeTriggered", "setLastTimeTriggered", "globalIndex", "setGlobalIndex", "pageCount", "activeThumbRenders", "removeOutline", "currentIndex", "columnCount", "newIndex", "arrowActions", "keyActions", "Enter", "handleEnterKey", "handleTabOutKey", "outline", "handleThumbnailKeyDown", "handleTabKey", "handleEscapeKey", "thumbContainer", "pageWidth", "getPageWidth", "pageHeight", "round", "getThumbnailSize", "getPageHeight", "annotCanvas", "createElement", "role", "aria<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "maxHeight", "ctx", "getContext", "zoom", "getCompleteRotation", "multiplier", "Core", "getCanvasMultiplier", "setAnnotationCanvasTransform", "override<PERSON><PERSON><PERSON>", "overridePageRotation", "overridePageCanvas", "debounce", "drawAnnotations", "debouncedDraw", "onBeginRendering", "onFinishedRendering", "needsMoreRendering", "onDocumentLoaded", "getType", "onPageComplete", "updatedPagesIndexes", "isPageAddedBefore", "scrollToRow", "floor", "onAnnotationChanged", "annots", "indices", "annot", "PageNumber", "Listable", "onPageNumberUpdated", "onDragEnd", "scrollToRowHelper", "change", "time", "now", "Date", "getTime", "thumbnail", "pageX", "x", "pageY", "y", "bottom", "hoverAreaHeight", "scrollDown", "scrollUp", "getContextElementId", "host", "frameElement", "draggingSelectedPage", "pagesToMove", "THUMBNAIL_DRAGGED", "dataTransfer", "setData", "setDragImage", "Image", "dropEffect", "effectAllowed", "extractedDataPromise", "extractPagesWithAnnotations", "pagesExtracted", "onDrop", "externalPageWebViewerFrameId", "files", "insertTo", "isIE11", "getData", "viewerID", "mergingDocument", "currentPageIndex", "otherWebViewerIframe", "parent", "console", "warn", "contentWindow", "docToMerge", "filename", "file", "targetPageNumber", "pageNumbersToMove", "p", "movePages", "updatedPagesNumbers", "offset", "THUMBNAIL_DROPPED", "pageNumbersBeforeMove", "pagesNumbersAfterMove", "numberOfPagesMoved", "thumbIsLoaded", "thumbIsPending", "removeFromPendingThumbs", "getPendingThumbIndex", "splice", "cancelLoadThumbnail", "findIndex", "thumbStatus", "canvases", "c", "cancel", "renderThumbnails", "columnsOfThumbnails", "row", "allowPageOperationsUI", "fill", "columnIndex", "thumbIndex", "allowDragAndDrop", "showPlaceHolder", "Fragment", "aria-current", "onContextMenu", "setFlyoutPosition", "openElements", "onFocus", "handleFocus", "updateNumberOfColumns", "thumbnailHeight", "thumbnailAutoScrollAreaStyle", "onSliderChange", "property", "zoomValue", "hideTooltipShortcut", "Slide<PERSON>", "displayProperty", "step", "getDisplayValue", "onStyleChange", "shouldHideSliderTitle", "shouldHideSliderValue", "aria-valuemin", "aria-valuemax", "aria-valuenow", "bounds", "onResize", "measureRef", "rowHeight", "rowCount", "ceil", "<PERSON><PERSON><PERSON><PERSON>", "overscanRowCount", "scrollToIndex"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,qBClEnC,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,uwEAA0wE,KAGnyE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,i7DAAk7D,KAG38D0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,sBCVvBD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,mlEAAolE,KAG7mE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,yrGAA0rG,KAGntG0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,k7HAAm7H,KAG58H0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,4fCwBRC,IChCAA,EDKf,SAAgDC,GAC9C,IAAQC,EAAyBD,EAAzBC,UAAWC,EAAcF,EAAdE,UAEbC,EAAWC,cACXC,EAAsBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,gCAAgCF,MAQ7F,OACE,yBACEN,UAAWA,EACXS,QATyC,YACK,IAA5CL,EAAoBM,QAAQT,IAC9BC,EAASS,IAAQC,0BAA0B,CAACX,OAS5C,kBAACY,EAAA,EAAmB,CAClBC,YAAaC,IAAaC,iCAC1BC,cAAeF,IAAaG,kBAC5BC,IAAI,iBACJC,MAAM,wC,yjCEbd,IAAMC,EAAY,CAChBC,MAAOC,IAAUC,OAAOC,YAKpBC,EAAoB,SAAH,GAAkB,IAAZJ,EAAK,EAALA,MACnBK,EAAMC,cAAND,EACDE,EAAkG,EAA7ExB,aAAY,SAACC,GAAK,MAAK,CAACC,IAAUsB,kBAAkBvB,EAJ1D,wBAImF,GAAjF,GACjBwB,EAAmI,EAA3GzB,aAAY,SAACC,GAAK,MAAK,CAACC,IAAUsB,kBAAkBvB,EAAOS,IAAaC,sCAAmC,GAA/G,GAIzB,IAHgEX,aAAY,SAACC,GAAK,MAAK,CACvFC,IAAUwB,qCAAqCzB,GAC/CC,IAAUC,gCAAgCF,OAC1C,GAHK0B,EAAsC,KAAEC,EAAe,KAIxD/B,EAAWC,cACX+B,EAAaC,iBAAO,IACpBC,EAAqBD,iBAAO,MAUlB,IAJZ9B,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAU8B,eAAe/B,GACzBC,IAAU+B,6BAA6BhC,GACvCC,IAAUgC,gBAAgBjC,MACzBkC,KAAa,GAPdC,EAAW,KACXC,EAA6B,KAC7BC,EAAY,KAOVC,EAAcX,EAAgBrD,OAAS,EAAIqD,EAAgBY,KAAI,SAAC3E,GAAC,OAAKA,EAAI,KAAK,CAACoD,EAAQ,GAEtFwB,EAA8BF,EAAYG,SAASN,GACnDO,EAAiBL,EAAaK,eAE/BF,IACHF,EAAc,CAACH,IAGjB,IAAMlE,EAAW0E,IAAKC,cAChBC,EAAe5E,aAAQ,EAARA,EAAU6E,KACzBC,EAAQF,IAAiBG,IAAYC,IACrCC,EAAWL,IAAiBG,IAAYG,QAAUN,IAAiBG,IAAYI,cAE/EC,EAAc,CAClB,qBAAwB,kBAACC,EAAA,EAAM,CAC7B5D,UAAU,gBACVmB,IAAI,6DACJV,QAAS,kBAAMoD,YAAgBjB,IAC/BxB,MAAM,4CACNN,YAAY,yBAEd,4BAA+B,kBAAC8C,EAAA,EAAM,CACpCzC,IAAI,oEACJV,QAAS,kBAAMqD,YAAuBlB,IACtCxB,MAAM,mDACNN,YAAY,gCAEd,YAAe,kBAAC8C,EAAA,EAAM,CACpB5D,UAAU,gBACVmB,IAAI,mBACJV,QAAS,kBAAMsD,YAAYnB,EAAa1C,EAAU8B,IAClDZ,MAAM,+BACNN,YAAY,cACZkD,oBAAmB,UAAKrC,EAAE,iBAAgB,YAAIA,EAAE,gBAAe,YAAIA,EAAE,qBAGrEsC,GAAe,EACbC,EAAkB,GAClBC,EAAUzB,EAA8BG,KAAI,SAACuB,GACjD,IAAQtD,EAAgBsD,EAAhBtD,YACFuD,EAAMvD,EACRwD,EAAYX,EAAY7C,GAC5B,GAAIoD,EAAgBxD,QAAQI,IAAgB,EAC1C,OAAO,KAWT,GATAoD,EAAgB9E,KAAK0B,IAShBwD,EAAW,CACdL,GAAe,EACf,IAAQ9C,EAAwBiD,EAAxBjD,IAAKV,EAAmB2D,EAAnB3D,QAASW,EAAUgD,EAAVhD,MACtBkD,EAAY,kBAACV,EAAA,EAAM,CACjB5D,UAAS,UAAKc,EAAW,WACzBK,IAAKA,EACLV,QAAS,kBAAMA,EAAQgC,IACvBrB,MAAOA,EACPN,YAAaA,IAIjB,OAAOwD,EACHC,IAAMC,aAAaF,EAAW,CAC9BD,QAEA,QAYN,OATAI,qBAAU,WACRvC,EAAWwC,QAAUC,YAAsBvC,EAAmBsC,SAC1DxC,EAAWwC,QAAQ9F,OAAS,GAC9BsD,EAAWwC,QAAQxF,SAAQ,SAAC0F,GAC1BA,EAAQC,UAAY,OAGvB,CAAC3C,EAAWwC,QAASP,IAEpBtC,EACK,KACHwB,GAASG,GAAYjF,WAAUuG,4BAEjC,yBAAK9E,UAAU,4BAA4B+E,eA/GzB,mBAgHhBC,MAAO,CAAEC,QAAS,SAElB,kBAACrB,EAAA,EAAM,CACLzC,IAAI,oEACJV,QAAS,kBAAMqD,YAAuBlB,IACtCxB,MAAM,mDACNN,YAAY,gCAEd,kBAAC8C,EAAA,EAAM,CACLzC,IAAI,6DACJV,QAAS,kBAAMoD,YAAgBjB,IAC/BxB,MAAM,4CACNN,YAAY,0BAMlB,yBAAKd,UAAWkF,IAAW,CACzB,6BAA6B,EAC7B,iBAAkBjB,EAClB,aAAcjB,IAEhB+B,eAvIoB,mBAwIpBI,IAAK/C,GAEF+B,EAEErC,EAAwB,KAAO,kBAACsD,EAA6B,CAC5DpF,UAAW,eACXC,UAAWqB,MASrBI,EAAkBL,UAAYA,EAEfK,IC1KAA,ED0KAA,E,sxBE3Kf,8lGAAAxD,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,ygBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAgBA,IAEMmH,EAAYd,IAAMe,YAAW,SAACvF,EAAOoF,GACzC,IACE7D,EA2BEvB,EA3BFuB,MACAiE,EA0BExF,EA1BFwF,WACAC,EAyBEzF,EAzBFyF,kBACAC,EAwBE1F,EAxBF0F,4BACAC,EAuBE3F,EAvBF2F,gBACAC,EAsBE5F,EAtBF4F,OAAM,EAsBJ5F,EArBF6F,gBAAQ,IAAG,eAAS,EACpBC,EAoBE9F,EApBF8F,YACAC,EAmBE/F,EAnBF+F,WACAC,EAkBEhG,EAlBFgG,YACAC,EAiBEjG,EAjBFiG,mBACAC,EAgBElG,EAhBFkG,cACAxD,EAeE1C,EAfF0C,YAAW,EAeT1C,EAdFmG,kBAAU,IAAG,KAAE,EACf9F,EAaEL,EAbFK,oBACA+F,EAYEpG,EAZFoG,8BACAC,EAWErG,EAXFqG,uBACAlG,EAUEH,EAVFG,SACAS,EASEZ,EATFY,QACA0F,EAQEtG,EARFsG,SACAC,EAOEvG,EAPFuG,QACAC,EAMExG,EANFwG,SACAC,EAKEzG,EALFyG,0BACAC,EAIE1G,EAJF0G,uBACAC,EAGE3G,EAHF2G,wBACAC,EAEE5G,EAFF4G,cACAC,EACE7G,EADF6G,kBAEIC,EAAYZ,EAAgBa,OAAOb,GAAiB,IACI,IAAZc,oBAAU,GAAE,GAAvDC,EAAiB,KAAEC,EAAoB,KACxCC,EAAoB/E,iBAAO,MAC3BgF,EAAahF,iBAAO,IACpBiF,GAAwBjF,iBAAO,IACgD,KAAjD4E,mBAAS,CAAEM,MAAOR,EAAWS,OAAQT,IAAY,GAA9EU,GAAU,MAAEC,GAAa,MACxB7F,GAAMC,cAAND,EAEmC,KAAfoF,oBAAS,GAAM,GAApCU,GAAM,MAAEC,GAAS,MAElBC,GAA0BtH,YAAYE,IAAUoH,yBAElDC,GAAc,KAElBnD,qBAAU,WACR,IAAMoD,EAAqB,SAACC,GACtBZ,EAAkBxC,UAAYwC,EAAkBxC,QAAQqD,SAASD,EAAME,SACzEC,MAIJ,OADA1J,SAAS2J,iBAAiB,YAAaL,GAChC,WACLtJ,SAAS4J,oBAAoB,YAAaN,MAE3C,IAEH,IAAMO,GAAqB,WACzBR,GAAcS,YAAW,WACvB,IA5EN,EA0FsB,EAdVC,EAAqBC,cAAcC,cAAc,oBAAD,OAAqB7B,EAAa,sBAAcrF,IAEhGmH,EAAUnH,EAAQ,EAClBoH,EAAiBzF,IAAK0F,YAAYF,GAElCG,EAAM3F,IAAKC,YAAYwD,GAI7B,GAAIkC,GAAOA,EAAIC,YAAYJ,GAAU,CACnC,IAAMK,EAAKF,EAAIG,WAAW,CACxBC,WAAYP,EACZpB,MAAOR,EACPS,OAAQT,EACRoC,cA1FV,EA0FsB,UAAE,WAAOC,GAAK,8EAClBZ,EAAqBC,cAAcC,cAAc,oBAAD,OAAqB7B,EAAa,sBAAcrF,QAE9F6H,EAAeb,EAAmBE,cAAc,iBAEpDF,EAAmBc,YAAYD,GAGjCD,EAAMlJ,UAAY,aAEZqJ,EAAQC,KAAKC,IAAI1C,EAAYqC,EAAM7B,MAAOR,EAAYqC,EAAM5B,QAClE4B,EAAMlE,MAAMqC,MAAQ,GAAH,OAAM6B,EAAM7B,MAAQgC,EAAK,MAC1CH,EAAMlE,MAAMsC,OAAS,GAAH,OAAM4B,EAAM5B,OAAS+B,EAAK,MAC5C7B,GAAc,CAAEH,MAAOP,OAAOoC,EAAM7B,OAAQC,OAAQR,OAAOoC,EAAM5B,UAE7DgC,KAAKE,IAAId,KACLe,EAAe,UAAH,OAA8B,GAAjBf,EAAmB,6BAElDQ,EAAMlE,MAAiB,UAAIyE,EAC3BP,EAAMlE,MAAM,oBAFe,WAG3BkE,EAAMlE,MAAM,gBAAkByE,EAC9BP,EAAMlE,MAAM,uBAJe,WAK3BkE,EAAMlE,MAAM,kBAAoByE,EAChCP,EAAMlE,MAAM,yBANe,WAO3BkE,EAAMlE,MAAM,4BAPe,WAQ3BkE,EAAMlE,MAAM,qBAAuByE,EACnCP,EAAMlE,MAAM,gBAAkByE,EAC9BP,EAAMlE,MAAM,uBAVe,YAa7BsD,EAAmB7J,YAAYyK,IAG7B1D,GACFA,EAAkBlE,GAGpBoE,EAAgBpE,GAChBoG,IAAU,GAAM,0CAtCN,EA1FtB,8KAiIW,6CACDgC,8BAA8B,IAEhC/D,EAAOrE,EAAOgH,EAAoBQ,MApHb,KAyH3BrE,qBAAU,WACR,IAAMkF,EAAiB,SAACC,GACtB,IAAQC,EAA0CD,EAA1CC,eAAgBC,EAA0BF,EAA1BE,MAAOC,EAAmBH,EAAnBG,MAAOC,EAAYJ,EAAZI,QAEhCvH,EAAcnB,EAAQ,EAEtB2I,EAAcF,EAAMhH,SAASN,GAC7ByH,EAAgBL,EAAeM,MAAK,SAACC,GAAW,OAAK3H,IAAgB2H,KACrEC,EAAcC,OAAOC,KAAKT,GAAOK,MAAK,SAACK,GAAS,OAAK/H,IAAgBgI,SAASD,MAC9EE,EAAgBV,EAAQjH,SAASN,GACjCkI,EAAe1H,IAAK2H,gBAEtBZ,EAAQpL,OAAS,GAAK0C,EAAQ,EAAIqJ,IAIlCV,GAAeC,GAAiBG,GAAeK,IACjDtC,MAIEyC,EAAoB,WACxBnD,IAAU,GACVU,MAQF,OALAnF,IAAKiF,iBAAiB,eAAgByB,GACtC1G,IAAKiF,iBAAiB,kBAAmB2C,GACrCvE,GACF8B,KAEK,WACLnF,IAAKkF,oBAAoB,eAAgBwB,GACzC1G,IAAKkF,oBAAoB,kBAAmB0C,GAC5CC,aAAalD,IACbhC,EAAStE,MAEV,IAEHyJ,aAAa,WACPzE,GACF8B,KACA5C,EAAkBlE,IAElBiF,EAASjF,KAEV,CAACgF,EAASI,IAEb,IA+DMsE,GAAWvI,IAAgBnB,EAAQ,EACnC2J,GAAY/E,EAAW5E,GACzB4J,GAAsB,UACpBC,GAAWlI,IAAK0F,YAAYrH,EAAQ,MACpC6J,IAAyB,IAAbA,KAAmB5D,GAAWF,MAAQE,GAAWD,SAE1C,IAAb6D,IAA+B,IAAbA,KAAmB5D,GAAWF,MAAQE,GAAWD,UAD7E4D,GAAsB,WAIxBE,8BAAoBjG,GAAK,iBAAO,CAC9BkG,WAAY,WACN7E,GAA6BiB,IAC/B6D,GAAclE,GAAsB1C,QAAQ,IAC5CuC,EAAqB,IACZE,EAAWzC,SACpB2D,YAAW,WACTiD,GAAcnE,EAAWzC,QAAQ,IACjCuC,EAAqB,KACpB,QAKT,IAAMqE,GAAgB,SAAC1G,GACjBA,IACFA,EAAQ2G,YAAc,OACtB3G,EAAQ4G,UAINC,GAAkB,SAAC7G,GACnBA,IACFA,EAAQ2G,iBAAcG,IAIpBC,GAAkB,SAACC,GACvBA,EAAEC,iBACF5D,MAGIA,GAAoB,WACxBd,EAAWzC,QAAQxF,SAAQ,SAAC4M,GAC1BL,GAAgBK,OAIdC,GAAgBC,uBAAY,SAACJ,GACjCA,EAAEK,kBACFrF,EAAkBgF,GAClB,IAI8B,EAJxBM,EAAoB,CACxBC,IAAK,kBAAMR,GAAgBC,IAC3BQ,OAAQ,kBAAMT,GAAgBC,KAE5BM,EAAkBN,EAAEvH,OACE,QAAxB,EAAA6H,EAAkBN,EAAEvH,YAAI,OAAxB,OAAA6H,IAEF,IAAMG,EAAkB,CACtBC,QAAS,kBAAMC,GAAeX,GAAI,IAClCY,UAAW,kBAAMD,GAAeX,EAAG,IACnCa,UAAW,kBAAMF,GAAeX,GAAI,IACpCc,WAAY,kBAAMH,GAAeX,EAAG,KAElCS,EAAgBT,EAAEvH,OAASsI,IAC7BN,EAAgBT,EAAEvH,SAEnB,CAAC8C,EAAWzC,QAASsC,IAElBuF,GAAiB,SAACX,EAAGgB,GACzBhB,EAAEC,iBACgC,IAA9B1E,EAAWzC,QAAQ9F,QAIvBqI,GAAqB,SAAC4F,GACpB,IAAIC,EAAgBD,EAAYD,EAOhC,OANIE,EAAgB,EAClBA,EAAgB3F,EAAWzC,QAAQ9F,OAAS,EACnCkO,GAAiB3F,EAAWzC,QAAQ9F,SAC7CkO,EAAgB,GAElBC,GAAiB5F,EAAWzC,QAAQoI,IAC7BA,MAILC,GAAmB,SAACC,GACxB7F,EAAWzC,QAAQxF,SAAQ,SAAC4M,GAC1BA,IAASkB,EAAiB1B,GAAcQ,GAAQL,GAAgBK,OAGpErH,qBAAU,WACJyC,EAAkBxC,UACpByC,EAAWzC,QAAUC,YAAsBuC,EAAkBxC,YAE9D,CAACsB,EAAoBgF,GAAUvD,KAElChD,qBAAU,WACJyC,EAAkBxC,UACpB0C,GAAsB1C,QAAUC,YAAsBuC,EAAkBxC,YAEzE,CAAC8B,EAA2BiB,KAE/B,IAAMkF,GAAuBnG,GAA6BiB,GAE1D,OACE,4BACEzH,UAAWkF,IAAW,CACpBG,WAAW,EACX4H,OAAQjC,GACRkC,SAAU3H,GAAciB,IAE1BV,WAAY,SAAC8F,GAAC,OAAK9F,EAAW8F,EAAGtK,IACjCwH,GAAE,8BAAyBxH,GAC3B6D,IAAK+B,EACLiG,UAAW,SAACvB,GAAC,OAAKG,GAAcH,IAChCnL,QAnLgB,SAACmL,GACnB,IAAMwB,EAAkBxB,EAAE5D,OAAO5E,MAA0B,aAAlBwI,EAAE5D,OAAO5E,KAClD,GAAI+C,IAAkCC,EAAwB,CAC5D,IAAMiH,EAA2BzB,EAAE0B,SAAW1B,EAAE2B,QAC1CC,EAAkB5B,EAAE6B,SACtBC,EAAuB,EAAItN,GAE/B,GAAIoN,EAAiB,CACnBtN,EAASS,EAAQgN,4BAA2B,IAE5C,IAAIC,EAAgBnI,EACE,OAAlBmI,IACFA,EAAgBnL,EAAc,EAC9BvC,EAASS,EAAQkN,gCAAgCD,KAGnD,IAAME,EAAqBxE,KAAKC,IAAIqE,EAAetM,GAC7CyM,EAAqBzE,KAAK0E,IAAIJ,EAAetM,GACnDoM,EAAuB,EAAI,IAAIO,IAAI,EAAIC,MAAMC,KAC3C,CAAEvP,OAAQmP,EAAqBD,EAAqB,IACpD,SAACM,EAAGlQ,GAAC,OAAKA,EAAI4P,aAEPT,GAA4B7G,GACrCtG,EAASS,EAAQgN,4BAA2B,KAExCN,GAA4BD,GAAmB3G,IAA2B4H,IAAmC,aAE5E,IAA/BjO,EAAoBxB,QAAiB4H,EAE9BpG,EAAoB2C,SAASzB,GACtCoM,EAAuBtN,EAAoBkO,QAAO,SAACrO,GAAS,OAAKqB,IAAUrB,KAE3EyN,EAAqBtO,KAAKkC,GAJ1BoM,EAAqBtO,KAAKqD,EAAc,IAO5CvC,EAASS,EAAQkN,gCAAgCvM,KAEjDoM,EAAuB,CAACpM,GAG1B,IAAMiN,EAAwBb,EAAqBA,EAAqB9O,OAAS,IACzC4H,IAA8BgH,GAGpEtN,EAASS,EAAQkN,gCAAgCU,IAGnDrO,EAASS,EAAQC,0BAA0B8M,SAClCrH,KACTnG,EAASS,EAAQ6N,aAAa,cAKhCnG,YAAW,WAGJ+E,GAAmB3G,IAA2B4H,IAAmC,WACpFpL,IAAKwL,eAAenN,EAAQ,KAE7B,IAwHD0D,MAAO,CACLqC,MAAOR,EACP6H,OAAQ,UACRC,WAAY,OACZC,OAAQ,QAEV/J,UAAW,GAEX,yBACE7E,UAAU,YACVgF,MAAO,CACLsC,OAAQT,EACRQ,MAAOR,GAEThB,YAAa,SAAC+F,GAAC,OAAK/F,EAAY+F,EAAGtK,IACnCuN,UAAW9I,EACXlB,UAAW,GAEX,yBAAKiE,GAAE,mBAAcxH,GAAStB,UAAU,cACvCwG,GAA6BiB,IAC5B,kBAACqH,EAAA,EAAM,CACL9O,UAAS,mBAAckL,IACvB6D,QAAS3O,EAAoB2C,SAASzB,GACtC0N,aAAA,UAAerN,GAAE,eAAc,YAAIsJ,GAAS,YAAItJ,GAAE,6BAClDkD,UAAW,KAIjB,yBAAK7E,UAAU,cAAciL,KAC3BzE,GAA6BwE,IAAYhF,IAAuB2B,IAA2B,kBAAC,EAAiB,CAACrG,MAAOA,QAK7H+D,EAAU4J,YAAc,YACxB5J,EAAUhE,UAAY,CACpBC,MAAOC,IAAUC,OACjB+D,WAAYhE,IAAU2N,KACtB1J,kBAAmBjE,IAAU4N,KAC7B1J,4BAA6BlE,IAAUC,OACvCkE,gBAAiBnE,IAAU4N,KAC3BxJ,OAAQpE,IAAU4N,KAClBvJ,SAAUrE,IAAU4N,KACpBtJ,YAAatE,IAAU4N,KACvBrJ,WAAYvE,IAAU4N,KACtBpJ,YAAaxE,IAAU2N,KACvBlJ,mBAAoBzE,IAAU2N,KAC9BjJ,cAAe1E,IAAUC,OACzBiB,YAAalB,IAAUC,OACvB0E,WAAY3E,IAAU6N,MACtBhP,oBAAqBmB,IAAU6N,MAC/BjJ,8BAA+B5E,IAAU2N,KACzC9I,uBAAwB7E,IAAU2N,KAClChP,SAAUqB,IAAU4N,KACpBxO,QAASY,IAAU8N,OACnBhJ,SAAU9E,IAAU4N,KACpB7I,QAAS/E,IAAU2N,KACnB3I,SAAUhF,IAAU4N,KACpB3I,0BAA2BjF,IAAU2N,KACrCzI,uBAAwBlF,IAAU+N,OAClC5I,wBAAyBnF,IAAUC,OACnCmF,cAAepF,IAAU+N,OACzB1I,kBAAmBrF,IAAU4N,MAGhB9J,Q,kwCCtaf,IAAMkK,EAAiBhL,IAAMe,YAAW,SAACvF,EAAOoF,GAC9C,IA0BC,IAdG9E,aACF,SAACC,GAAK,MAAK,CACTC,IAAU8B,eAAe/B,GACzBC,IAAUiP,cAAclP,GACxBC,IAAUC,gCAAgCF,GAC1CC,IAAU4F,8BAA8B7F,GACxCC,IAAUkP,aAAanP,GACvBC,IAAUmP,mBAAmBpP,GAC7BC,IAAUoP,+BAA+BrP,GACzCC,IAAUiG,0BAA0BlG,GACpCC,IAAUqP,0BAA0BtP,GACpCC,IAAUsP,2BAA2BvP,MAEvCkC,KACD,IAzBCC,EAAW,KACXyD,EAAU,KACV9F,EAAmB,KACnB+F,EAA6B,KAC7BsJ,EAAY,KACZC,EAAkB,KAClBjK,EAA2B,KAC3Be,EAAyB,KACzBC,EAAsB,KACtBC,EAAuB,KACvBoJ,EAAc,MAiBV5P,EAAWC,cAEjB,OAAO,kBAAC,EAAS,KAAKJ,EAAK,CACzBoF,MACA1C,cACAyD,aACA9F,sBACA+F,gCACAC,uBAAwBqJ,GAAgBC,EACxCxP,WACAS,YACA0F,aACAZ,8BACAe,4BACAC,yBACAqJ,iBACApJ,gCAIJ6I,EAAeN,YAAc,iBACdM,ICzDAA,EDyDAA,E,yCEtDf,SAASQ,EAAe,GAA6B,IAA3BC,EAAS,EAATA,UAAWC,EAAY,EAAZA,aACnC,OACE,oCACE,kBAACrM,EAAA,EAAM,CACL5D,UAAW,eACXc,YAAY,YACZK,IAAI,oBACJV,QAASuP,EACT5O,MAAM,qBAER,kBAACwC,EAAA,EAAM,CACL5D,UAAW,eACXc,YAAY,eACZK,IAAI,sBACJV,QAASwP,EACT7O,MAAM,yBAMd2O,EAAe1O,UAAY,CACzB2O,UAAWzO,IAAU4N,KACrBc,aAAc1O,IAAU4N,MAGXY,QC1Bf,SAASG,EAAqB,GAAwD,IAAtDC,EAAQ,EAARA,SAAUC,EAAS,EAATA,UAAWC,EAAc,EAAdA,eAAgBC,EAAa,EAAbA,cACnE,OACE,oCACE,kBAAC1M,EAAA,EAAM,CACL5D,UAAW,eACXc,YAAY,0BACZK,IAAI,6BACJV,QAAS0P,EACT/O,MAAM,kBAER,kBAACwC,EAAA,EAAM,CACL5D,UAAW,eACXc,YAAY,2BACZK,IAAI,wBACJV,QAAS2P,EACThP,MAAM,mBAER,kBAACwC,EAAA,EAAM,CACL5D,UAAW,eACXc,YAAY,2BACZK,IAAI,iCACJV,QAAS4P,EACTjP,MAAM,mBAER,kBAACwC,EAAA,EAAM,CACL5D,UAAW,eACXc,YAAY,0BACZK,IAAI,mBACJV,QAAS6P,EACTlP,MAAM,mBAMd8O,EAAqB7O,UAAY,CAC/B8O,SAAU5O,IAAU4N,KACpBiB,UAAW7O,IAAU4N,KACrBkB,eAAgB9O,IAAU4N,KAC1BmB,cAAe/O,IAAU4N,MAGZe,SC1Cf,SAASK,GAAiB,GAAiD,IAA/CC,EAAiB,EAAjBA,kBAAmBC,EAAwB,EAAxBA,yBAC7C,OACE,oCACE,kBAAC7M,EAAA,EAAM,CACL5D,UAAW,eACXc,YAAY,0CACZK,IAAI,oEACJV,QAASgQ,EACTrP,MAAM,kCAER,kBAACwC,EAAA,EAAM,CACL5D,UAAW,eACXc,YAAY,mCACZK,IAAI,6DACJV,QAAS+P,EACTpP,MAAM,4BAMdmP,GAAiBlP,UAAY,CAC3BmP,kBAAmBjP,IAAU4N,KAC7BsB,yBAA0BlP,IAAU4N,MAGvBoB,U,wnDCMf,SAASG,GAAgC,GAAmB,IAkDtDC,EAlDqCC,EAAa,EAAbA,cACnCC,EAAQC,cACR5Q,EAAWC,cACXkG,EAAW0K,cACX3Q,EAAsBC,YAAYE,IAAUC,iCAC5CwQ,EAAa3Q,aAAY,SAACC,GAAK,OAAMsQ,GAAmC,cAAlBA,EAAqErQ,IAAU0Q,cAAc3Q,EAAOsQ,GAApErQ,IAAU2Q,kBAAkB5Q,MAClH6Q,EAAqB9Q,YAAYE,IAAUwB,sCAC3CqP,EAAoB/Q,YAAYE,IAAU8Q,qBAC1CC,EAAQjR,YAAYE,IAAUgR,sCAAuC/O,KAClB,KAAfuE,oBAAS,GAAM,GAAlDyK,EAAa,KAAEC,EAAgB,KAEhC7O,EAAc8O,mBAAQ,kBAC1BtR,EAAoByC,KAAI,SAACvB,GAAK,OAAKA,EAAQ,OAAI,CAAClB,IAO5CuR,EAAaD,mBAAQ,WASzB,MAAO,CACLtB,UATgB,WAAH,OAAUwB,YAAuBhP,EAAa1C,IAAa2R,YAAQ3R,IAUhFmQ,eATqB,WAAH,OAAUuB,YAAuBhP,EAAa1C,IAAa4R,YAAalP,EAAa1C,IAUvGoQ,cAToB,WAAH,OAAUsB,YAAuBhP,EAAa1C,IAAa6D,YAAYnB,EAAa1C,EAAUiR,IAU/GV,yBAR+B,WAAH,OAAUmB,YAAuBhP,EAAa1C,IAAa4D,YAAuBlB,IAS9G4N,kBAVwB,WAAH,OAAUoB,YAAuBhP,EAAa1C,IAAa2D,YAAgBjB,IAWhGuN,SATe,WAAH,OAAUyB,YAAuBhP,EAAa1C,KAV5DA,EAASS,IAAQ6N,aAAazN,IAAagR,iCAC3C7R,EAASS,IAAQqR,YAAY,sBAmB3BhC,UATgB,WAAH,OAAU4B,YAAuBhP,EAAa1C,IAAa+R,YAAerP,IAUvFqN,aATmB,WAAH,OAAU2B,YAAuBhP,EAAa1C,IAAagS,YAAkBtP,IAU7FA,iBAED,CAACA,EAAauO,IAETX,EAAgDmB,EAAhDnB,kBAAmBC,EAA6BkB,EAA7BlB,yBAErBlS,EAAW0E,IAAKC,cAChBC,EAAe5E,aAAQ,EAARA,EAAU6E,KACzBC,EAAQF,IAAiBG,IAAYC,IACrCC,EAAWL,IAAiBG,IAAYG,QAAUN,IAAiBG,IAAYI,cAKrF,IAAK0N,GAAqB/K,EACxB,IAEEsK,EADgBpI,cAAcC,cAAc,QAAQ2J,wBACxB9K,MAxDZ,GAyDhB,MAAOuE,GACP+E,GAAqBK,GAAcoB,KA1DnB,QA6DlBzB,GAAqBK,GAAcoB,KA7DjB,GA+DpB,IAAMC,EAAe1B,EAbG,IAclB2B,EAAe3B,EAbG,IAexBlM,qBAAU,WACR,IAcI8N,EADEC,EAAc,GAGlBD,EADEF,EACW,EACJC,EACI,EAEA,EAEf,IAAIhR,EAAQ,EACZgQ,EAAMpS,SAAQ,SAACkF,GACb,IAAIqO,GAAO,GACPnR,EAAQiR,GAAsC,IAAvBC,EAAY5T,QAA+B,aAAfwF,aAAI,EAAJA,EAAMhB,SAC3DqP,GAAO,GAELrO,GAAsB,YAAdA,EAAKhB,MACf9B,KAEDmR,GAAQD,EAAYpT,KAAI,MAAhBoT,EAAW,GA/BS,SAACpO,GAC9B,MAAyB,4BAArBA,EAAKtD,YACA4R,aAAwB7B,GAAO,GACR,0BAArBzM,EAAKtD,YACP6R,aAA0B9B,GAAO,GACV,0BAArBzM,EAAKtD,YACP8R,aAA4B/B,GAAO,GACnB,wBAAdzM,EAAKhB,KACPyP,aAA4BhC,EAAOzM,GACnB,YAAdA,EAAKhB,KACP,CAAC,gBADH,EAsBsB0P,CAAuB1O,QAEtD,IAAM2O,EAAS,CACbjS,YAAaC,IAAaiS,sCAC1BhT,UAAWe,IAAaiS,sCACxB1B,MAAOkB,GAELF,GACFpS,EAASS,IAAQ6N,aAAauE,EAAOjS,cAEnCiS,EAAOzB,MAAM1S,QACfsB,EAASS,IAAQsS,aAAaF,EAAOjS,YAAaiS,IAClDtB,GAAiB,KAEjBvR,EAASS,IAAQuS,aAAaH,EAAOjS,cACrC2Q,GAAiB,MAElB,CAACZ,EAAOyB,EAAcD,EAAcf,IAEvC,IAAM6B,EAAgBzB,mBAAQ,WAC5B,IAAI0B,GAAmB,EACnB9R,EAAQ,EACZ,OAAOgQ,EAAMzO,KAAI,SAACuB,EAAMiP,GACtB,IAAIC,EAQJ,OANEA,EADEjB,EACkB,EACXC,EACW,EAEA,EAEH,aAAflO,aAAI,EAAJA,EAAMhB,OAAuBgQ,EAM7B9R,EAAQgS,EACH,MAELlP,GAAsB,YAAdA,EAAKhB,MACf9B,IAEuB,4BAArB8C,EAAKtD,YACA,kBAAC,GAAgB,MAAK6Q,EAAU,CAAEtN,IAAI,6BACf,0BAArBD,EAAKtD,YACP,kBAAC,EAAc,MAAK6Q,EAAU,CAAEtN,IAAI,2BACb,0BAArBD,EAAKtD,YACP,kBAAC,GAAoB,MAAK6Q,EAAU,CAAEtN,IAAI,2BAC1B,wBAAdD,EAAKhB,KACTgB,EAAKmP,WAIHnP,EAAKmP,WAAW1Q,KAAI,SAAC2Q,GAAS,OACnC,kBAAC5P,EAAA,EAAM,CACLS,IAAKmP,EAAU1S,YACfd,UAAW,eACXc,YAAa0S,EAAU1S,YACvBK,IAAKqS,EAAUrS,IACfV,QAAS,kBAAM+S,EAAU/S,QAAQmC,IACjCxB,MAAOoS,EAAUpS,YAVnBE,IACO,MAaJ,OAjCDA,EAAQgS,IACVF,GAAmB,GAEd,yBAAK/O,IAAG,iBAAYgP,GAAerT,UAAU,kBAgCvD,CAACsR,EAAOK,EAAYU,EAAcC,IAErC,OAAIjP,GAASG,GAAYjF,WAAUuG,4BAE/B,yBAAK9E,UAAW,mCACd,kBAAC,GAAgB,CAACwQ,kBAAmBA,EACnCC,yBAA0BA,KAK1B,yBAAKzQ,UAAU,6BACpBmT,EACA3B,GAAiB,yBAAKxR,UAAU,iBAC/B,kBAACa,EAAA,EAAmB,CAClBC,YAAW,UAAKC,IAAaiS,sCAAqC,UAClE/R,cAAeF,IAAaiS,sCAC5B5R,MAAM,cACND,IAAI,mBAEN,yBAAKnB,UAAU,gBAKrB0Q,GAAgCrP,UAAY,CAC1CuP,cAAerP,IAAU+N,QAGZoB,ICnOAA,GDmOAA,G,2iCExNf,SAAS+C,GAAcC,EAAmBxN,GAKxC,IAJA,IAAIyN,EAAe,GACbC,EAAcF,EAAkBG,MAAK,SAACC,EAAGC,GAAC,OAAKD,EAAIC,KACrDlH,EAAY,KAEP3O,EAAI,EAAG0V,EAAYhV,OAASV,EAAGA,IAClC0V,EAAY1V,EAAI,KAAO0V,EAAY1V,GAAK,EAC1C2O,EAA0B,OAAdA,EAAqBA,EAAY+G,EAAY1V,GAClC,OAAd2O,GACT8G,EAAe,GAAH,OAAMA,GAAY,OAAGzN,EAAW2G,GAAU,YAAI3G,EAAW0N,EAAY1V,IAAG,MACpF2O,EAAY,MAEZ8G,EAAe,GAAH,OAAMA,GAAY,OAAGzN,EAAW0N,EAAY1V,IAAG,MAI/D,OAAOyV,EAAaK,MAAM,GAAI,GAGhC,IAAMC,GAAmB,SAAH,GAA8C,IAAxCjO,EAAkB,EAAlBA,mBAAoB4K,EAAa,EAAbA,cACvCjP,EAAqB,GAAhBC,cAAgB,GAApB,GACF1B,EAAWC,cAcf,KANEE,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,gCAAgCF,GAC1CC,IAAUsB,kBAAkBvB,EAAO,mBACnCC,IAAUiP,cAAclP,GACxBC,IAAUiG,0BAA0BlG,GACpCC,IAAUgC,gBAAgBjC,OAC1B,GAXAF,EAAmB,KACnB8T,EAAU,KACVhO,EAAU,KACVM,EAAyB,KACzB7D,EAAY,KASRwR,EAAqBV,GAAcrT,EAAqB8F,GAEE,KAA5Ba,mBAASoN,GAAmB,GAAzDC,EAAU,KAAEC,EAAa,KACgD,KAA5BtN,mBAASoN,GAAmB,GAAzEG,EAAkB,KAAEC,EAAqB,KAC1CvR,EAAiBL,EAAaK,eAEpCyB,qBAAU,WACR4P,EAAcZ,GAAcrT,EAAqB8F,MAChD,CAACmO,EAAejU,EAAqB4F,EAAoBE,IAE5D,IAoCMsO,EAAgC,WACpCtU,EAASS,IAAQgN,4BAA2B,KAG9C,OAAOuG,EAAa,KAClB,yBAAKlU,UAAW,4BAA6B+E,eAAc,mBACxDiB,EACC,yBAAKhG,UAAW,oBACd,yBAAKA,UAAW,YACfwG,GAA6B,kBAAC,GAAsB,CAACoK,cAAeA,IACpE5N,GACC,2BAAOhD,UAAW,wBAAyByU,QAAQ,oBACjD,8BACG9S,EAAE,0CAA0C,MAE/C,0BAAM3B,UAAU,2BACb2B,EAAE,mDAIT,yBAAK3B,UAAW,yBACd,2BACE0U,KAAK,mBACLC,OA3DG,SAAC/I,GACd,IAAMgJ,EAAsBhJ,EAAE5D,OAAO6M,MAAMhD,QAAQ,KAAM,IACnDiD,EAASF,EAA2BG,YAAuBH,EAAqB1O,GAAjD,GAC/B8O,EAAcF,EAAMjS,KAAI,SAACoS,GAAI,OAAKA,EAAO,KAE/C,GAAIH,EAAMlW,SAAWgW,EAAqB,CACxC1U,EAASS,IAAQC,0BAA0BoU,IAE3C,IAAME,EAAgBzB,GAAcrT,EAAqB8F,GAEzDmO,EAAca,GACdX,EAAsBW,QAEtBb,EAAcC,GAGZlU,EAAoBxB,OAAS,IAAM4H,GAGrC6B,YAAW,WACL+L,IAAeE,GACjBE,MAED,MAqCKW,SAjCa,SAACvJ,GACxByI,EAAczI,EAAE5D,OAAO6M,QAiCbA,MAAOT,EACPgB,YAAapS,EAAiB,GC1H7B,aD2HDgM,aAAYrN,EAAE,0CACd3B,UAAU,aACVoD,KAAK,SAEP,yBAAKpD,UAAW,0BACZwG,EAQA,kBAAC5C,EAAA,EAAM,CACLzC,IAAK,aACLC,MAAO,uCACPX,QAhDuB,WACrCP,EAASS,IAAQC,0BAA0B,CAACqC,IAAKZ,iBAAmB,KACpEnC,EAASS,IAAQgN,4BAA2B,KA+C9B7M,YAAa,0BAXf,kBAAC8C,EAAA,EAAM,CACLzC,IAAK,yBACLC,MAAO,wCACPX,QAAS+T,EACT1T,YAAa,wBAarB,OAKVmT,GAAiB5S,UAAY,CAC3B6S,WAAY3S,IAAU2N,KACtBhJ,WAAY3E,IAAU8T,QAAQ9T,IAAU+N,QACxCgG,sBAAuB/T,IAAU4N,KACjCnJ,mBAAoBzE,IAAU2N,MAGjB+E,IE5JAA,GF4JAA,G,sCGjJFsB,GAAgB,SAACC,EAAYC,GAAW,IAAEC,IAAkB,UAAH,+CAAO,OAAK,SAACxV,GAGjF,OAFAA,EAASS,IAAQqR,YAAYjR,IAAa4U,gBAEnC,IAAIC,SAAQ,SAACC,EAASC,GAC3B7S,IAAKsS,cAAcC,EAAYC,GAAaM,MAAK,SAACC,GAChD9V,EAASS,IAAQ6N,aAAazN,IAAa4U,gBAC3C1S,IAAKwL,eAAegH,GAEhBC,GACFO,aAAUC,KAAOC,gBAAiBH,GAGpCH,EAAQG,MACR,OAAO,SAACI,GACRN,EAAOM,GACPlW,EAASS,IAAQ6N,aAAazN,IAAa4U,wB,yxECHjD,IAivBeU,GAxuBS,SAAH,GAA6C,IAAvC1P,EAAa,EAAbA,cAAe2P,EAAiB,EAAjBA,kBAClCC,EAAkBlW,aAAY,SAACC,GAAK,OAAKC,IAAUiW,cAAclW,EAAO,gBACxE4T,EAAa7T,aAAY,SAACC,GAAK,OAAKC,IAAUsB,kBAAkBvB,EAAO,sBACvEmW,EAAapW,YAAYE,IAAUqK,eACnCnI,EAAcpC,YAAYE,IAAU8B,gBACpCjC,EAAsBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,gCAAgCF,KAAQkC,KAC/FkU,EAA4BrW,YAAYE,IAAUoW,8BAClDC,EAA+BvW,YAAYE,IAAUsW,iCACrDC,EAA0BzW,YAAYE,IAAUwW,4BAChDC,EAA6B3W,aAAY,SAACC,GAAK,OAAKC,IAAUsB,kBAAkBvB,EAAO,uBACvF2W,EAA4B5W,aAAY,SAACC,GAAK,OAAKC,IAAUsB,kBAAkBvB,EAAO,2BACtFmP,EAAepP,YAAYE,IAAUkP,cACrCC,EAAqBrP,YAAYE,IAAUmP,oBAC3CwH,EAAwC7W,aAAY,SAACC,GAAK,OAAKC,IAAUqK,cAActK,EAAO,MAC9FoG,EAA0BrG,YAAYE,IAAUsP,4BAChDsH,EAAsB9W,YAAYE,IAAU6W,mDAC5CzU,EAAetC,YAAYE,IAAUgC,gBAAiBC,KACtDmF,EAA0BtH,YAAYE,IAAUoH,yBAE/ChG,EAAqB,GAAhBC,cAAgB,GAApB,GAEFyV,EAAUlV,mBACVmV,EAAgBnV,iBAAO,IACvBoV,EAASpV,iBAAO,IAChBqV,EAAsBrV,iBAAO,MAEwB,KAAf4E,oBAAS,GAAM,GAApD0Q,EAAc,KAAEC,EAAiB,KACI,KAAd3Q,oBAAS,GAAK,GAArCT,EAAO,KAAEqR,EAAU,KACa,KAAX5Q,mBAAS,GAAE,GAAhCO,EAAM,KAAEsQ,EAAS,KACa,KAAX7Q,mBAAS,GAAE,GAA9BM,EAAK,KAAEwQ,EAAQ,KACkD,KAAd9Q,mBAAS,MAAK,GAAjE+Q,GAAqB,KAAEC,GAAwB,KACuB,MAAfhR,oBAAS,GAAM,GAAtEiR,GAAwB,MAAEC,GAAyB,MACD,MAAXlR,mBAAS,GAAE,GAAlDmR,GAAe,MAAEC,GAAkB,MACS,MAAfpR,oBAAS,GAAM,GAA5CqR,GAAU,MAAEC,GAAa,MACmB,MAAXtR,mBAAS,GAAE,GAA5CuR,GAAY,MAAEC,GAAe,MAC9BC,GAAyBrW,iBAAO,IAChCsW,GAAgBtW,iBAAO,IACvBuW,GAAsBvW,iBAAO,MAEoB,MAAb4E,mBAAS,KAAI,GAAhDd,GAAa,MAAE0S,GAAgB,MACuB,MAAX5R,mBAAS,GAAE,GAAtD6R,GAAiB,MAAEC,GAAoB,MACG,MAAX9R,mBAAS,GAAE,GAA1C+R,GAAW,MAAEC,GAAc,MAC5BC,GAAwC,IAA5BtS,EAAgCwQ,EAAwCT,EACpFzT,GAAiBL,aAAY,EAAZA,EAAcK,eAE/B9C,GAAWC,cAMb8Y,GAAqB,GAEzBxU,qBAAU,WACR,IAAMoD,EAAqB,SAACC,GACtB4Q,GAAoBhU,UAAYgU,GAAoBhU,QAAQqD,SAASD,EAAME,SAC7EkR,GAAcR,GAAoBhU,UAItC,OADAnG,SAAS2J,iBAAiB,YAAaL,GAChC,WACLtJ,SAAS4J,oBAAoB,YAAaN,MAE3C,IAEH,IAAMkE,GAAgBC,uBAAY,SAACJ,EAAGuN,EAAcC,GAElD,IAAIC,EADJzN,EAAEK,kBAEF,IAAMqN,EAAe,CACnB7M,UAAW,kBAAMF,GAAeX,EAAGuN,GAAe,IAClDzM,WAAY,kBAAMH,GAAeX,EAAGuN,EAAc,IAClD7M,QAAS,kBAAMC,GAAeX,EAAGuN,GAAeC,IAChD5M,UAAW,kBAAMD,GAAeX,EAAGuN,EAAcC,KAE7CG,EAAa,CACjBC,MAAO,kBAAMC,GAAe7N,EAAGuN,IAC/BhN,IAAK,kBAAMuN,GAAgBP,KAGzBG,EAAa1N,EAAEvH,OACjBgV,EAAWC,EAAa1N,EAAEvH,QAExBkV,EAAW3N,EAAEvH,MACfkV,EAAW3N,EAAEvH,YAGEqH,IAAb2N,GAA0BA,IAAaF,IACzCZ,GAAgBc,GAChB/N,GAAckN,GAAuB9T,QAAQ2U,IAC7C5N,GAAgB+M,GAAuB9T,QAAQyU,OAEhD,CAACV,GAAeD,KAEblN,GAAgB,SAAC1G,GACrBA,EAAQC,SAAW,EACnBD,EAAQ2G,YAAc,OACtB3G,EAAQI,MAAM2U,QAAU,+BACxB/U,EAAQ4G,QACRkN,GAAoBhU,QAAUE,GAG1B6G,GAAkB,SAAC7G,GACvBA,EAAQC,UAAY,EACpBD,EAAQ2G,iBAAcG,EACtBwN,GAActU,IAGVsU,GAAgB,SAACtU,GACrBA,EAAQI,MAAM2U,QAAU,QAGpBD,GAAkB,SAACpY,GACvB4X,GAAcV,GAAuB9T,QAAQpD,KAGzCiL,GAAiB,SAACX,EAAGuN,EAAcvM,GACvC,IAAIyM,EAAWF,EAAevM,EAC9B,OAAIyM,EAAW,GAAKA,GAAYL,GACvBG,EAEFE,GAGHI,GAAiB,SAAC7N,EAAGtK,GACzBsK,EAAEC,iBACFP,GAAckN,GAAuB9T,QAAQpD,IAC7C2B,IAAKwL,eAAenN,EAAQ,GAC5BmX,GAAc/T,QAAQpD,GAAO+J,cAGzBuO,GAAyB5N,uBAAY,SAACJ,EAAGtK,GAC7CsK,EAAEK,kBACF,IAIuB,EAJjBsN,EAAa,CACjBpN,IAAK,kBAAM0N,GAAajO,EAAGtK,IAC3B8K,OAAQ,kBAAM0N,GAAgBlO,EAAGtK,KAE/BiY,EAAW3N,EAAEvH,OACE,QAAjB,EAAAkV,EAAW3N,EAAEvH,YAAI,OAAjB,OAAAkV,MAED,CAACf,KAEEsB,GAAkB,SAAClO,EAAGtK,GAC1BsK,EAAEC,iBACFP,GAAckN,GAAuB9T,QAAQpD,KAGzCuY,GAAe,SAACjO,EAAGtK,GACvB,IACI+X,EAAW/X,GADCsK,EAAE6B,UAAY,EAAI,IAE9B4L,EAAW,GAAKA,GAAYL,MAC9BK,EAAW/X,GAEbiX,GAAgBc,GAChB/N,GAAckN,GAAuB9T,QAAQ2U,IACzCA,IAAa/X,GACfmK,GAAgB+M,GAAuB9T,QAAQpD,KAyB7CkE,GAAoB,SAACvF,GACzB,IAAM8Z,EAAiBxC,EAAO7S,SAAW6S,EAAO7S,QAAQzE,IAAcsX,EAAO7S,QAAQzE,GAAW2E,QAChG,GAAKmV,EAAL,CAIA,IAAM/Q,EAAa/I,EAAY,EACzB+Z,EAAY/W,IAAKgX,aAAajR,GAGpC,EA/BuB,SAACgR,EAAWE,GACnC,IAAI7S,EACAC,EACA+B,EAYJ,OAVI2Q,EAAYE,GACd7Q,EAAQ2Q,EAAY/T,GACpBoB,EAAQpB,GACRqB,EAASgC,KAAK6Q,MAAMD,EAAa7Q,KAEjCA,EAAQ6Q,EAAajU,GACrBoB,EAAQiC,KAAK6Q,MAAMH,EAAY3Q,GAC/B/B,EAASrB,IAGJ,CACLoB,QACAC,UAcwB8S,CAAiBJ,EAFxB/W,IAAKoX,cAAcrR,IAE9B3B,EAAK,EAALA,MAAOC,EAAM,EAANA,OAETgT,EAAcP,EAAevR,cAAc,sBAAwBjK,SAASgc,cAAc,UAChGD,EAAYta,UAAY,mBACxBsa,EAAYE,KAAO,MACnBF,EAAYG,UAAY,GAAH,OAAM9Y,EAAE,eAAc,YAAIqH,GAC/CsR,EAAYtV,MAAM0V,SAAW,GAAH,OAAMzU,GAAa,MAC7CqU,EAAYtV,MAAM2V,UAAY,GAAH,OAAM1U,GAAa,MAC9C,IAAM2U,EAAMN,EAAYO,WAAW,MAE/BC,EAAO,EACP3P,EAAWlI,IAAK8X,oBAAoB/R,GACpCmC,EAAW,IACbA,GAAY,GAEd,IAAM6P,EAAa3c,OAAO4c,KAAKC,sBAE3B/P,EAAW,GAAM,GACnBmP,EAAYjT,MAAQA,EACpBiT,EAAYhT,OAASA,EACrBwT,EAAOR,EAAYjT,MAAQ2S,EAC3Bc,GAAQE,IAERV,EAAYjT,MAAQC,EACpBgT,EAAYhT,OAASD,EAErByT,EAAOR,EAAYhT,OAAS0S,EAC5Bc,GAAQE,GAGVjB,EAAetb,YAAY6b,GAC3BrX,IAAKkY,6BAA6BP,EAAKE,EAAM3P,GAE7C,IAAIhN,EAAU,CACZ6K,aACAoS,eAAgBd,GAGZpR,EAAQ6Q,EAAevR,cAAc,eAE3C,GAAIU,EACF/K,EAAU,GAAH,MACFA,GAAO,IACVkd,qBAAsBlQ,EACtBmQ,mBAAoBpS,IAMnB+P,GAAmBjQ,KACtBiQ,GAAmBjQ,GAAcuS,IAAStY,IAAKuY,gBAAiB,OAGlEC,EADsBxC,GAAmBjQ,IAC3B7K,KAwHhB,GArHAsG,qBAAU,WACR,IAAMiX,EAAmB,WACvB/D,GAAW,IAGPgE,EAAsB,SAACC,GACtBA,GACHjE,GAAW,IAITkE,EAAmB,WAAM,MACS,kBAAhB,QAAlB,EAAA5Y,IAAKC,qBAAa,aAAlB,EAAoB4Y,WACtBpE,GAAkB,GAElBA,GAAkB,GAEpBuB,GAAqB,GACrB/Y,GAASS,IAAQC,0BAA0B,MAGvCmb,EAAiB,WACjBvE,EAAoB9S,UACtBzB,IAAKwL,eAAe+I,EAAoB9S,SACxC8S,EAAoB9S,QAAU,OAgBlC,OAZAzB,IAAKiF,iBAAiB,iBAAkBwT,GACxCzY,IAAKiF,iBAAiB,oBAAqByT,GAC3C1Y,IAAKiF,iBAAiB,iBAAkB2T,GACxC5Y,IAAKiF,iBAAiB,eAAgB6T,GAKlC9Y,IAAKC,eACP2Y,IAGK,WACL5Y,IAAKkF,oBAAoB,iBAAkBuT,GAC3CzY,IAAKkF,oBAAoB,oBAAqBwT,GAC9C1Y,IAAKkF,oBAAoB,iBAAkB0T,GAC3C5Y,IAAKkF,oBAAoB,eAAgB4T,MAE1C,IAEHtX,qBAAU,WACR,IAAMkF,EAAiB,SAACC,GACtB,GAAKA,EAAL,CAGA,IAAIoS,EAAsB9N,MAAMC,KAAK/N,GAEjCwJ,EAAQI,UACVgS,EAAsBA,EAAoB1N,QAAO,SAACrO,GAAS,OAAiD,IAA5C2J,EAAQI,QAAQtJ,QAAQT,EAAY,OAGlG2J,EAAQE,QACVkS,EAAsBA,EAAoBnZ,KAAI,SAAC5C,GAAS,OAAM2J,EAAQE,MAAM7J,EAAY,GAAK2J,EAAQE,MAAM7J,EAAY,GAAK,EAAIA,MAIlI,IAAMgc,EAAoBrS,EAAQG,OAAUH,EAAQG,MAAM,GAAK,GAAMiS,EAAoB,GACtD,IAA/BA,EAAoBpd,QAAgBqd,IACtCD,EAAsBpS,EAAQG,MAAMlH,KAAI,SAACmG,GAAU,OAAKA,EAAa,MAGvE9I,GAASS,IAAQC,0BAA0Bob,MAK7C,OAFA/Y,IAAKiF,iBAAiB,eAAgByB,GAE/B,kBAAM1G,IAAKkF,oBAAoB,eAAgBwB,MACrD,CAACvJ,IAEJqE,qBAAU,WAAM,MACC,QAAf,EAAA4S,EAAQ3S,eAAO,OAAf,EAAiBwX,YAAY5S,KAAK6S,OAAO1Z,EAAc,GAAKyV,KAC5D,IAAMkE,EAAsB,SAACC,GAC3B,IAAMC,EAAU,GAEhBD,EAAOnd,SAAQ,SAACqd,GACd,IAAMtc,EAAYsc,EAAMC,WAAa,GAChCD,EAAME,UAAYH,EAAQ5b,QAAQT,IAAc,IAGrDqc,EAAQld,KAAKa,GAEbuF,GAAkBvF,QAIhByc,EAAsB,SAAC1T,GAAe,MACpC/I,EAAY+I,EAAa,EAChB,QAAf,EAAAqO,EAAQ3S,eAAO,OAAf,EAAiBwX,YAAY5S,KAAK6S,MAAMlc,EAAYiY,MAOtD,OAJAjV,IAAKiF,iBAAiB,oBAAqBwU,GAC3CzZ,IAAKiF,iBAAiB,oBAAqBkU,GAC3CnZ,IAAKiF,iBAAiB,mBAAoBkU,GAEnC,WACLnZ,IAAKkF,oBAAoB,oBAAqBuU,GAC9CzZ,IAAKkF,oBAAoB,oBAAqBiU,GAC9CnZ,IAAKkF,oBAAoB,mBAAoBiU,MAE9C,CAACnW,GAAeiS,KAEnBzT,qBAAU,YACJgL,GAAgBC,KAClBxP,GAASS,IAAQC,0BAA0B,KAC3CV,GAASS,IAAQgN,4BAA2B,OAE7C,CAAC8B,EAAcC,IAGdwE,GAAcuD,IAAoBlB,IAAoB5P,IAAkB3D,GAC1E,OAAO,KAET,IAAM2Z,GAAY,WAChBtE,IAAc,GACdN,GAAyB,OAGrB6E,GAAoB,SAACtb,EAAOub,EAAQC,GACxC,IAC2E,EADrEC,GAAM,IAAIC,MAAOC,UACvB,OAAI3b,EAAQ0X,GAAY,GAAK1X,EAAQ,GAAKyb,EAAMnE,IAAqBkE,GACpD,QAAf,EAAAzF,EAAQ3S,eAAO,OAAf,EAAiBwX,YAAY5S,KAAK6S,OAAO7a,EAAQub,GAAU3E,KAC3DW,GAAqBkE,GACdzb,EAAQub,GAEVvb,GAGHwE,GAAa,SAAC8F,EAAGtK,GAIrB,GAFAsK,EAAEC,iBACFD,EAAEK,kBACG2K,GAAiCF,EAAtC,CAIA,IAAMwG,EAAYtR,EAAE5D,OAAOmK,wBAGzB8F,GAFEC,GAAkB,IAEQtM,EAAEuR,MAAQD,EAAUE,EAAIF,EAAU7V,MAAQ,KAE1CuE,EAAEyR,MAAQH,EAAUI,EAAIJ,EAAU5V,OAAS,IAGzEyQ,GAAyBzW,GACzB,IACA,EAD6CiH,cAAcC,cAAc,qCACd2J,wBAAnDmL,EAAC,EAADA,EAAGC,EAAM,EAANA,OAEP3R,EAAEyR,MAAQC,EAAIE,IAChBzE,GAAe6D,GAAkBtb,GAAQ,EAAG,MACnCsK,EAAEyR,MAAQE,EAASC,KAC5BzE,GAAe6D,GAAkBtb,EAAO,EAAG,QAIzCmc,GAAa,WACjB1E,GAAe6D,GAAkB9D,GAAa,EAAG,OAE7C4E,GAAW,WACf3E,GAAe6D,GAAkB9D,IAAc,EAAG,OAG9C6E,GAAsB,WAC1B,OAAItf,OAAOC,8BACFiK,cAAcqV,KAAK9U,GAEnBzK,OAAOwf,aAAa/U,IAIzBjD,GAAc,SAAC+F,EAAGtK,GACtByX,GAAezX,GACf+W,IAAc,GACd,IDtcgCzV,ECsc1Bkb,EAAuB1d,EAAoB+J,MAAK,SAACjM,GAAC,OAAKA,IAAMoD,KAC7Dyc,EAAcD,EAAuB1d,EAAoByC,KAAI,SAACvB,GAAK,OAAKA,EAAQ,KAAK,CAACA,EAAQ,GACpG2U,aAAUC,KAAO8H,mBAEjBpS,EAAEqS,aAAaC,QAAQ,OAAQ,IAE3BH,EAAYnf,OAAS,GAEvBgN,EAAEqS,aAAaE,aAAa,IAAIC,MAAS,EAAG,GAG1C1H,GAA6BI,IAC/BlL,EAAEqS,aAAaI,WAAa,OAC5BzS,EAAEqS,aAAaK,cAAgB,MAC/B1S,EAAEqS,aAAaC,QAlciB,6BAkcsBP,MDpdxB/a,ECqdVmb,EDndxB1f,OAAOkgB,qBAAuBC,aAA4B5b,GAC1DvE,OAAOogB,eAAiB7b,GCqdjBkb,GACH5d,GAASS,IAAQC,0BAA0B,CAACU,KAG9C2B,IAAKwL,eAAenN,EAAQ,IAGxBod,GAAS,SAAC9S,GACdA,EAAEC,iBACF,IAEI8S,EAFIC,EAAUhT,EAAEqS,aAAZW,MACFC,EAAW7G,GAA2BF,GAAwB,EAAIA,GAAwB,EAE3FgH,MAEHH,EAA+B/S,EAAEqS,aAAac,QApdd,+BAsdlC,ID9c2CC,EAAUvJ,EC8c/CwJ,EACHN,GAAgChB,OAA0BgB,GAAiCC,EAAMhgB,OAC9FsgB,EAAmBzc,EAAc,EAEvC,GAAIiU,GAA6BuI,EAC3BN,GAAgChB,OAA0BgB,EAC5Dze,IDpduC8e,ECodCL,EDpdSlJ,ECodqBoJ,EDpdL,SAAC3e,GACxE,OAAO,IAAI0V,SAAQ,SAACC,EAASC,GAC3B,IAAMqJ,EAAuB9gB,OAAO+gB,OAAO7gB,SAASiK,cAAc,IAAD,OAAKwW,IACjEG,IACHE,QAAQC,KAAK,8CACbxJ,KAGF,IAAIyJ,EAAgBJ,EAAqBI,cACrClhB,OAAOC,gCACTihB,EAAgBlhB,QAGlB,IAAMkgB,EAAuBgB,EAAchB,qBACtCA,IACHc,QAAQC,KAAK,4DACbxJ,KAGF5V,EAASS,IAAQqR,YAAYjR,IAAa4U,gBAC1C4I,EAAqBxI,MAAK,SAACyJ,GACzBtf,EAASqV,GAAciK,EAAY/J,GAAa,IAAQM,MAAK,YAAyB,IAAtB0J,EAAQ,EAARA,SAAU3K,EAAK,EAALA,MACxEmB,aAAUC,KAAOC,gBAAiB,CAAEsJ,WAAU3K,MAAOyK,EAAcd,iBACnEve,EAASS,IAAQ6N,aAAazN,IAAa4U,gBAC3CE,EAAQ,CAAE4J,WAAU3K,gBAEtB,OAAO,SAACsB,GACRlW,EAASS,IAAQ6N,aAAazN,IAAa4U,gBAC3CG,EAAOM,YCybIwI,EAAMhgB,QACfsP,MAAMC,KAAKyQ,GAAO1f,SAAQ,SAACwgB,GACzBxf,GAASqV,GAAcmK,EAAMb,YAG5B,GAAIjI,IAAiCqI,GACZ,OAA1BnH,GAAgC,CAClC,IAAM6H,EAAmB3H,GAA2BF,GAAwB,EAAIA,GAAwB,EAElG8H,EADuBxf,EAAoB+J,MAAK,SAACjM,GAAC,OAAKA,IAAMghB,KAClB9e,EAAoByC,KAAI,SAAC3E,GAAC,OAAKA,EAAI,KAAK,CAACuE,GAC1F+U,EAAoB9S,QAAUib,EAAmBC,EAAkBtR,QAAO,SAACuR,GAAC,OAAKA,EAAIF,KAAkB/gB,OACvGqE,IAAK6c,UAAUF,EAAmBD,GAElC,IADA,IAAMI,EAAsB,GACnBC,EAAS,EAAGA,EAASJ,EAAkBhhB,OAAQohB,IACtDD,EAAoB3gB,KAAKoY,EAAoB9S,QAAUsb,GAEzD/J,aAAUC,KAAO+J,kBAAmB,CAAEC,sBAAuBN,EAAmBO,sBAAuBJ,EAAqBK,mBAAoBL,EAAoBnhB,SAGxKmZ,GAAyB,MACzBM,IAAc,IAGV1S,GAAS,SAAC1F,EAAW2E,EAASkE,GAC7BuX,GAAcpgB,IAAeqgB,GAAergB,KAC/CsX,EAAO7S,QAAQzE,GAAa,CAC1B2E,UACA6C,QAAQ,GAGV6P,EAAc5S,QAAQtF,KAAK,CACzBa,YACA6I,SAKAyX,GAA0B,SAACtgB,GAC/B,IAAMqB,EAAQkf,GAAqBvgB,IACpB,IAAXqB,GACFgW,EAAc5S,QAAQ+b,OAAOnf,EAAO,IAIlC+e,GAAgB,SAACpgB,GAAS,aAA8B,QAA9B,EAAKsX,EAAO7S,QAAQzE,UAAU,aAAzB,EAA2BwH,QAE1D6Y,GAAiB,SAACrgB,GAAS,OAA0C,IAArCugB,GAAqBvgB,IAErDsG,GAAW,SAACtG,GAChB,IAAMqB,EAAQkf,GAAqBvgB,IACpB,IAAXqB,IACF2B,IAAKyd,oBAAoBpJ,EAAc5S,QAAQpD,GAAOwH,IACtDwO,EAAc5S,QAAQ+b,OAAOnf,EAAO,KAmBlCkf,GAAuB,SAACvgB,GAAS,OAAKqX,EAAc5S,QAAQic,WAAU,SAACC,GAAW,OAAKA,EAAY3gB,YAAcA,MAEjH2F,GAAW,SAAC3F,GAAc,QAC9BsG,GAAStG,GACT,IAAM4gB,EAAoC,QAA5B,EAAGtJ,EAAO7S,QAAQzE,UAAU,OAAS,QAAT,EAAzB,EAA2B2E,eAAO,WAAT,EAAzB,EAAoC3F,iBAAiB,UAClE4hB,WAAUjiB,QACZiiB,EAAS3hB,SAAQ,SAAC4hB,GAChBA,EAAExZ,OAAS,EACXwZ,EAAEzZ,MAAQ,KAIV4R,GAAmBhZ,IACrBgZ,GAAmBhZ,GAAW8gB,SAEhCxJ,EAAO7S,QAAQzE,GAAa,MASxB+gB,GAAmB,SAAH,GAA8B,IAAxB1f,EAAK,EAALA,MAAO+C,EAAG,EAAHA,IAAKW,EAAK,EAALA,MAChChF,EAAYkF,IAAW,CAC3B+b,oBAAqB/I,GAAkB,EACvCgJ,KAAK,IAEDC,IAA0B1R,GAAgBC,GAChD,OACE,yBAAK8K,KAAK,MAAMxL,aAAW,MAAMhP,UAAWA,EAAWqE,IAAKA,EAAKW,MAAOA,GACrE,IAAIkJ,MAAMgK,IAAiBkJ,OAAOve,KAAI,SAACuL,EAAGiT,GACzC,IAAMC,EAAahgB,EAAQ4W,GAAkBmJ,EACvCE,EAAmBJ,IAA0BzK,GAA6BE,GAC1E4K,EAAkBD,GAAoBzJ,KAA0BwJ,EAEtE,OAAOA,EAAatI,GAClB,kBAAC,IAAMyI,SAAQ,CAACpd,IAAKid,IACjBpJ,GAAkB,GAAoB,IAAfoJ,IAAqBE,GAAmBxJ,IAA4B,yBAAK3T,IAAG,uBAAkBid,GAActhB,UAAU,yBAC/I,wBACEmF,IAAK,SAAChG,GAAE,OAAMqZ,GAAuB9T,QAAQ4c,GAAcniB,GAC3DkF,IAAKid,EACL9G,KAAK,WACL3V,SAAUyT,KAAiBgJ,EAAa,GAAK,EAC7CI,eAAcpJ,KAAiBgJ,EAAa,YAAS5V,EACrDiR,UAAWA,GACX3c,UAAU,qBACVmN,UAAW,SAACvB,GAAC,OAAKG,GAAcH,EAAG0V,EAAYpJ,KAC/CyJ,cAAe,SAAC/V,GAAC,OAAKuL,IAhEPlX,EAgE8CqhB,GAhErDxZ,EAgEkD8D,GA/DhEC,iBACN5I,IAAKwL,eAAexO,EAAY,GAC3BG,EAAoB2C,SAAS9C,IAChCC,GAASS,IAAQC,0BAA0B,CAACX,UAG1CwP,GAAgBC,GAAsB/H,IAI1CzH,GAASS,IAAQihB,kBAAkB,CAAExE,EAAGtV,EAAMqV,MAAOG,EAAGxV,EAAMuV,SAC9Dnd,GAASS,IAAQkhB,aAAa,CAAC9gB,IAAaG,wBAZzB,IAAC4G,EAAO7H,GAiEf6hB,QAAS,SAAClW,GAAC,OAhCL,SAACA,EAAGtK,GAClBkX,GAAuB9T,SAAW8T,GAAuB9T,QAAQ3B,SAAS6I,EAAE5D,SAC9EsD,GAAckN,GAAuB9T,QAAQpD,IA8BnBygB,CAAYnW,EAAG0V,KAE/B,kBAAC,EAAS,CACRnc,IAAK,SAAChG,GAAE,OAAMsZ,GAAc/T,QAAQ4c,GAAcniB,GAClD4G,YAAawb,EACbhc,WAAYnF,EAAoB2C,SAASue,GACzChgB,MAAOggB,EACPhb,QAASA,EACTX,OAAQA,GACRY,SAAUA,GACVX,SAAUA,GACVC,YAAaA,GACbC,WAAYA,GACZJ,gBAAiB6a,GACjB/a,kBAAmBA,GACnBQ,mBAAoBmb,EACpBlb,cAAeA,GACfU,cAAeA,EACfC,kBAAmB,SAACgF,GAAC,OAAKgO,GAAuBhO,EAAG0V,OAGvDE,IAAoBxJ,IAA4B,yBAAK3T,IAAG,uBAAkBid,GAActhB,UAAU,0BAEnG,UAYNgiB,GAAwB,WAC5B7J,GAAmB7O,KAAKC,IArnBR,GAqnByBD,KAAK0E,IAAI,EAAG1E,KAAK6S,MAAM9U,EAAQpB,QAGpEgc,GAAkBjL,EAA6BlQ,OAAOb,IAAiB,GAAKa,OAAOb,IAAiB,GACpGD,KAAuByJ,GAAgBC,GAAsB/H,GAC7Dua,GAA+B,CACnC,OAAU,GAAF,OAznBY,GAynBQ,OAGxBC,GAAiB,SAACC,EAAUvN,GAChC,IAAIwN,EAjoBe,OAioBHvb,OAAO+N,GACnBwN,EAAY,MACdA,EAAY,KAGd1J,GAAiB0J,GACjBL,MAGF,OACE,kBAAC,IAAMP,SAAQ,MACXxK,GAA6B,yBAAKlS,eAAa,uBAAuB/E,UAAU,8BAChF,kBAAC4D,EAAA,EAAM,CACLzC,IAAI,sBACJC,MAAM,iBACNkhB,qBAAmB,EACnB7hB,QAAS,WACHwF,GAjpBQ,YAkpBV0S,GAAiB1S,GAlpBP,MAmpBV+b,OAGJlhB,YAAY,uBAEbkC,IACC,kBAACuf,GAAA,EAAM,CACLzhB,YAAa,uBACbshB,SAAU,OACVI,gBAAiB,OACjBjZ,IAAK,EACLyE,IAAK,EACLyU,KAAM,IACN5N,MAAO5O,GAAc,IACrByc,gBAAiB,kBAAMzc,IACvBkc,eAAgBA,GAChBQ,cAAeR,GACfS,uBAAuB,EACvBC,uBAAuB,KAGzB7f,IACA,2BACEwX,KAAK,SACLpX,KAAK,QACL4L,aAAW,wBACXzF,IA/qBW,MAgrBXyE,IA/qBW,OAgrBX6G,MAAO5O,GACP6c,gBAlrBW,MAmrBXC,gBAlrBW,OAmrBXC,gBAAe/c,GACfkP,SAAU,SAACvJ,GACT+M,GAAiB7R,OAAO8E,EAAE5D,OAAO6M,QACjCmN,MAEFS,KAvrBY,KAwrBZziB,UAAU,mBACV8I,GAAG,kBAGP,kBAAClF,EAAA,EAAM,CACLzC,IAAI,qBACJC,MAAM,gBACNkhB,qBAAmB,EACnB7hB,QAAS,WACHwF,GAAgBa,OAjsBR,MAisBkC,OAC5C6R,GAAiB1S,GAAgBa,OAlsBvB,OAmsBVkb,OAGJlhB,YAAY,uBAGhB,kBAAC,IAAO,CAACmiB,QAAM,EAACC,SA1FE,SAAH,GAAmB,IAAbD,EAAM,EAANA,OACvBrL,EAAUqL,EAAO3b,QACjBuQ,EAASoL,EAAO5b,OAChB8Q,GAAmB7O,KAAKC,IAjnBR,GAinByBD,KAAK0E,IAAI,EAAG1E,KAAK6S,MAAM8G,EAAO5b,MAAQpB,QAuFpC5B,IAAK4B,KAC3C,gBAAGkd,EAAU,EAAVA,WAAU,OACZ,yBAAKnjB,UAAS,gCAA2B2G,GAAiBmC,GAAG,mCAAmC/D,eAAa,kBAAkB2Z,OAAQA,GAAQvZ,IAAKge,GAClJ,yBAAKnjB,UAAU,oCACZoY,GACC,yBAAKpY,UAAU,0BAA0B8F,WAAY4X,GAAU1Y,MAAOkd,KAAuC,GAE/G,kBAAC,IAAI,CACH/c,IAAKkS,EACL/P,OAAQA,EACRD,MAAOA,EACP+b,UAAWnB,GAGXoB,SAAU/Z,KAAKga,KAAKtK,GAAYd,IAChCqL,YAAavC,GACbwC,iBAAkB,EAClBxjB,UAAW,iBAEXyjB,cAAena,KAAK6S,OAAO1Z,EAAc,GAAKyV,IAC9CsC,KAAK,OACLxL,aAAYrN,EAAE,6BACdkD,UAAW,IAEZuT,GACC,yBAAKpY,UAAU,0BAA0B8F,WAAY2X,GAAYzY,MAAK,SAAOkd,IAA4B,IAAE,OAAU,WAAmB,QAMlJ,kBAAC,GAAgB,CAAClc,mBAAoBA,GAAoB4K,cAAe0F,GAAqB3P,MCnwBrF0P", "file": "chunks/chunk.40.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ThumbnailControlsMulti.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ThumbnailControls.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.thumbnailControls-overlay{display:grid;text-align:center;z-index:2;margin-top:5px;grid-template-areas:\\\"rotate delete . more\\\";grid-template-columns:repeat(3,1fr)}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.thumbnailControls-overlay{display:flex}}.thumbnailControls-overlay .Button{height:32px;padding:0;width:32px}.thumbnailControls-overlay .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.thumbnailControls-overlay .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.thumbnailControls-overlay .Button.active{background:var(--view-header-button-active)}.thumbnailControls-overlay .Button.active .Icon{color:var(--selected-icon-color)}.thumbnailControls-overlay.modular-ui .Button:hover{border:1px solid var(--focus-border);background:var(--tools-button-hover)}.thumbnailControls-overlay .rotate-button{grid-area:rotate}.thumbnailControls-overlay .delete-button{grid-area:delete}.thumbnailControls-overlay .more-options{grid-area:more}.thumbnailControls-overlay.custom-buttons .Button{grid-area:auto}.thumbnailControls{display:flex;flex-direction:row;text-align:center;z-index:2;margin-top:5px}.thumbnailControls .Button{height:32px;padding:0;width:32px;margin:0 4px}.thumbnailControls .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.thumbnailControls .Button:hover{background:var(--view-header-button-hover);border-radius:4px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Thumbnail.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Thumbnail{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;cursor:pointer}.Thumbnail.active .container .page-image{border:2px solid var(--focus-border);box-shadow:none;box-sizing:content-box}.Thumbnail .container{position:relative;display:flex;justify-content:center;align-items:center;cursor:pointer}.Thumbnail .container .page-image{box-shadow:0 0 3px 0 var(--box-shadow)}.Thumbnail .container .annotation-image,.Thumbnail .container .page-image{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.Thumbnail .container .checkbox{position:absolute;border-radius:4px;z-index:4}.Thumbnail .container .default{top:3%;right:15%}.Thumbnail .container .rotated{top:15%;right:3%}.Thumbnail .page-label{margin-top:11px}.Thumbnail.selected .container .thumbnail:before{color:#fff;background:var(--focus-border);width:16px;height:16px;position:absolute;z-index:10}.Thumbnail.selected .container canvas{background:hsla(0,0%,100%,.6)}.Thumbnail.active .page-label{color:var(--focus-border)!important}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControlsContainer{margin-left:16px;margin-right:16px;margin-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControlsContainer{margin-left:16px;margin-right:16px;margin-bottom:16px}}.documentControlsContainer .PageControlContainer{display:flex;background-color:var(--gray-2);justify-content:center;align-content:center;border-radius:4px}.documentControlsContainer .PageControlContainer .dropdown-menu{position:relative}.documentControlsContainer .PageControlContainer .dropdown-menu .indicator{position:absolute;bottom:1px;right:1px;width:0;height:0;border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid #c4c4c4;transform:rotateY(0deg) rotate(315deg)}.documentControlsContainer .PageControlContainer button .Icon{height:21px;width:21px;color:var(--icon-color)}.documentControlsContainer .PageControlContainer .button-hover:hover{background:var(--view-header-button-hover);border-radius:4px}.documentControlsContainer .PageControlContainer .divider{height:20px;width:1px;background:var(--divider);margin:6px;display:block!important}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./DocumentControls.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.documentControls{display:flex;flex-direction:column}.documentControls .divider{height:1px;background:var(--divider);margin:16px 0 8px}.documentControls .documentControlsInput{display:flex;flex-direction:row;padding-bottom:16px;padding-top:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsInput{padding-bottom:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsInput{padding-bottom:0}}.documentControls .documentControlsInput.customizableUI{padding:8px 0}.documentControls .documentControlsInput .pagesInput{width:100%;height:30px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsInput .pagesInput{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsInput .pagesInput{font-size:13px}}.documentControls .documentControlsInput .pagesInput:focus{outline:none;border:1px solid var(--focus-border)}.documentControls .documentControlsInput .pagesInput::-moz-placeholder{color:var(--placeholder-text)}.documentControls .documentControlsInput .pagesInput::placeholder{color:var(--placeholder-text)}.documentControls .documentControlsInput .documentControlsButton{display:flex;flex-direction:row;padding-left:2px}.documentControls .documentControlsInput .documentControlsButton .Button{height:30px;padding:0;width:30px;margin:0 4px}.documentControls .documentControlsInput .documentControlsButton .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.documentControls .documentControlsInput .documentControlsButton .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.documentControls .documentControlsLabel{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsLabel{margin-top:8px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsLabel{margin-top:8px;font-size:13px}}.documentControls .documentControlsLabel .multiSelectExampleLabel{color:var(--faded-text);margin-left:2px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ThumbnailsPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.thumbnail-slider-container{display:flex;align-items:center;width:230px;margin:0 auto;height:40px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container{width:inherit;margin:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container{width:inherit;margin:16px}}.thumbnail-slider-container .thumbnail-slider{width:100%;height:20px;padding:0;color:transparent;background-color:transparent;border:0 transparent}.thumbnail-slider-container input[type=range]{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;accent-color:green;height:3px;background:#2980b9}.thumbnail-slider-container input[type=range]::-webkit-slider-runnable-track{height:5px;border-radius:6px}.thumbnail-slider-container input[type=range]::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;height:14px;width:14px;background-color:var(--slider-filled);border-radius:50%;border:2px solid var(--slider-filled);margin-top:-4px}.thumbnail-slider-container input[type=range]::-moz-range-track{height:5px;border-radius:4px}.thumbnail-slider-container Button{width:15px;height:15px;margin:2.5px;padding-top:6px}.thumbnail-slider-container Button:hover{background:var(--view-header-button-hover);border-radius:4px}.thumbnail-slider-container .slider{width:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container .slider{margin-top:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container .slider{margin-top:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container .slider .slider-element-container{width:auto;margin-left:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container .slider .slider-element-container{width:auto;margin-left:auto}}.ThumbnailsPanel{overflow:hidden!important;display:flex;height:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ThumbnailsPanel{width:inherit;margin:0 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ThumbnailsPanel{width:inherit;margin:0 16px}}.ThumbnailsPanel #virtualized-thumbnails-container{flex:1}.ThumbnailsPanel .row{display:flex;justify-content:space-around;align-items:center;flex-direction:column}.ThumbnailsPanel .thumbnailPlaceholder{width:150px;margin:2px;border:1px solid var(--focus-border)}.ThumbnailsPanel .columnsOfThumbnails.row{display:flex;justify-content:left;align-items:center;flex-direction:row}.ThumbnailsPanel .columnsOfThumbnails .cellThumbContainer{display:flex;flex-direction:row}.ThumbnailsPanel .columnsOfThumbnails .Thumbnail{display:inline-flex}.ThumbnailsPanel .columnsOfThumbnails .thumbnailPlaceholder{width:116px;min-width:116px;height:150px;margin-bottom:30px}.cellThumbContainer{border-radius:4px}.thumbnailAutoScrollArea{position:absolute;width:calc(100% - 55px);z-index:10;background:hsla(0,0%,100%,0)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport DataElements from 'constants/dataElement';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\n\nfunction PageManipulationOverlayButtonContainer(props) {\n  const { className, pageIndex } = props;\n\n  const dispatch = useDispatch();\n  const selectedPageIndexes = useSelector((state) => selectors.getSelectedThumbnailPageIndexes(state));\n\n  const onClickPageManipulationOverlayButton = () => {\n    if (selectedPageIndexes.indexOf(pageIndex) === -1) {\n      dispatch(actions.setSelectedPageThumbnails([pageIndex]));\n    }\n  };\n\n  return (\n    <div\n      className={className}\n      onClick={onClickPageManipulationOverlayButton}\n    >\n      <ToggleElementButton\n        dataElement={DataElements.PAGE_MANIPULATION_OVERLAY_BUTTON}\n        toggleElement={DataElements.PAGE_MANIPULATION}\n        img=\"icon-tool-more\"\n        title=\"option.thumbnailPanel.moreOptions\"\n      />\n    </div>\n  );\n}\n\nexport default PageManipulationOverlayButtonContainer;", "import PageManipulationOverlayButtonContainer from './PageManipulationOverlayButtonContainer';\n\nexport default PageManipulationOverlayButtonContainer;", "import React, { useRef, useEffect } from 'react';\nimport { useDispatch, shallowEqual, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport classNames from 'classnames';\nimport { deletePages, rotateClockwise, rotateCounterClockwise } from 'helpers/pageManipulationFunctions';\nimport Button from 'components/Button';\nimport selectors from 'selectors';\nimport './ThumbnailControls.scss';\nimport PageManipulationOverlayButton from 'components/PageManipulationOverlayButton';\nimport { workerTypes } from 'constants/types';\nimport core from 'src/core';\nimport DataElements from 'constants/dataElement';\nimport { useTranslation } from 'react-i18next';\nimport findFocusableElements from 'helpers/findFocusableElements';\n\nconst propTypes = {\n  index: PropTypes.number.isRequired,\n};\n\nconst dataElementName = 'thumbnailControl';\n\nconst ThumbnailControls = ({ index }) => {\n  const { t } = useTranslation();\n  const [isElementDisabled] = useSelector((state) => [selectors.isElementDisabled(state, dataElementName)]);\n  const [isMoreOptionDisabled] = useSelector((state) => [selectors.isElementDisabled(state, DataElements.PAGE_MANIPULATION_OVERLAY_BUTTON)]);\n  const [isPageDeletionConfirmationModalEnabled, selectedIndexes] = useSelector((state) => [\n    selectors.pageDeletionConfirmationModalEnabled(state),\n    selectors.getSelectedThumbnailPageIndexes(state),\n  ]);\n  const dispatch = useDispatch();\n  const buttonsRef = useRef([]);\n  const buttonContainerRef = useRef(null);\n\n  const [\n    currentPage,\n    pageThumbnailControlMenuItems,\n    featureFlags,\n  ] = useSelector((state) => [\n    selectors.getCurrentPage(state),\n    selectors.getThumbnailControlMenuItems(state),\n    selectors.getFeatureFlags(state),\n  ], shallowEqual);\n\n  let pageNumbers = selectedIndexes.length > 0 ? selectedIndexes.map((i) => i + 1) : [index + 1];\n\n  const isCurrentPageInTheSelection = pageNumbers.includes(currentPage);\n  const customizableUI = featureFlags.customizableUI;\n\n  if (!isCurrentPageInTheSelection) {\n    pageNumbers = [currentPage];\n  }\n\n  const document = core.getDocument();\n  const documentType = document?.type;\n  const isXod = documentType === workerTypes.XOD;\n  const isOffice = documentType === workerTypes.OFFICE || documentType === workerTypes.LEGACY_OFFICE;\n\n  const BUTTONS_MAP = {\n    'thumbRotateClockwise': <Button\n      className=\"rotate-button\"\n      img=\"icon-header-page-manipulation-page-rotation-clockwise-line\"\n      onClick={() => rotateClockwise(pageNumbers)}\n      title=\"option.thumbnailPanel.rotatePageClockwise\"\n      dataElement=\"thumbRotateClockwise\"\n    />,\n    'thumbRotateCounterClockwise': <Button\n      img=\"icon-header-page-manipulation-page-rotation-counterclockwise-line\"\n      onClick={() => rotateCounterClockwise(pageNumbers)}\n      title=\"option.thumbnailPanel.rotatePageCounterClockwise\"\n      dataElement=\"thumbRotateCounterClockwise\"\n    />,\n    'thumbDelete': <Button\n      className=\"delete-button\"\n      img=\"icon-delete-line\"\n      onClick={() => deletePages(pageNumbers, dispatch, isPageDeletionConfirmationModalEnabled)}\n      title=\"option.thumbnailPanel.delete\"\n      dataElement=\"thumbDelete\"\n      onClickAnnouncement={`${t('action.delete')} ${t('action.modal')} ${t('action.isOpen')}`}\n    />,\n  };\n  let isCustomized = false;\n  const occurredButtons = [];\n  const buttons = pageThumbnailControlMenuItems.map((item) => {\n    const { dataElement } = item;\n    const key = dataElement;\n    let component = BUTTONS_MAP[dataElement];\n    if (occurredButtons.indexOf(dataElement) > -1) {\n      return null;\n    }\n    occurredButtons.push(dataElement);\n\n    /* Example button object:\n    {\n      title: 'Alert me',\n      img: '/path-to-image',\n      onClick: (selectedPageNumbers) => alert(``),\n      dataElement: 'alertMeDataElement',\n    } */\n    if (!component) {\n      isCustomized = true;\n      const { img, onClick, title } = item;\n      component = <Button\n        className={`${dataElement}-button`}\n        img={img}\n        onClick={() => onClick(currentPage)}\n        title={title}\n        dataElement={dataElement}\n      />;\n    }\n\n    return component\n      ? React.cloneElement(component, {\n        key,\n      })\n      : null;\n  });\n\n  useEffect(() => {\n    buttonsRef.current = findFocusableElements(buttonContainerRef.current);\n    if (buttonsRef.current.length > 0) {\n      buttonsRef.current.forEach((element) => {\n        element.tabIndex = -1;\n      });\n    }\n  }, [buttonsRef.current, buttons]);\n\n  if (isElementDisabled) {\n    return null;\n  } if (isXod || isOffice || document?.isWebViewerServerDocument()) {\n    return (\n      <div className=\"thumbnailControls-overlay\" data-element={dataElementName}\n        style={{ display: 'flex' }}\n      >\n        <Button\n          img=\"icon-header-page-manipulation-page-rotation-counterclockwise-line\"\n          onClick={() => rotateCounterClockwise(pageNumbers)}\n          title=\"option.thumbnailPanel.rotatePageCounterClockwise\"\n          dataElement=\"thumbRotateCounterClockwise\"\n        />\n        <Button\n          img=\"icon-header-page-manipulation-page-rotation-clockwise-line\"\n          onClick={() => rotateClockwise(pageNumbers)}\n          title=\"option.thumbnailPanel.rotatePageClockwise\"\n          dataElement=\"thumbRotateClockwise\"\n        />\n      </div>\n    );\n  }\n  return (\n    <div className={classNames({\n      'thumbnailControls-overlay': true,\n      'custom-buttons': isCustomized,\n      'modular-ui': customizableUI,\n    })}\n    data-element={dataElementName}\n    ref={buttonContainerRef}\n    >\n      {buttons}\n      {\n        (isMoreOptionDisabled) ? null : <PageManipulationOverlayButton\n          className={'more-options'}\n          pageIndex={index}\n        />\n      }\n\n    </div>\n  );\n};\n\n\nThumbnailControls.propTypes = propTypes;\n\nexport default ThumbnailControls;", "import ThumbnailControls from './ThumbnailControls';\n\nexport default ThumbnailControls;\n", "import React, { useRef, useEffect, useState, useImperativeHandle, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport classNames from 'classnames';\nimport selectors from 'selectors';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport core from 'core';\nimport ThumbnailControls from 'components/ThumbnailControls';\nimport thumbnailSelectionModes from 'constants/thumbnailSelectionModes';\nimport { useTranslation } from 'react-i18next';\nimport PropTypes from 'prop-types';\n\nimport './Thumbnail.scss';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport getRootNode from 'helpers/getRootNode';\nimport findFocusableElements from 'helpers/findFocusableElements';\n\n// adds a delay in ms so thumbs that are only on the screen briefly are not loaded.\nconst THUMBNAIL_LOAD_DELAY = 50;\n\nconst Thumbnail = React.forwardRef((props, ref) => {\n  const {\n    index,\n    isSelected,\n    updateAnnotations,\n    shiftKeyThumbnailPivotIndex,\n    onFinishLoading,\n    onLoad,\n    onRemove = () => { },\n    onDragStart,\n    onDragOver,\n    isDraggable,\n    shouldShowControls,\n    thumbnailSize,\n    currentPage,\n    pageLabels = [],\n    selectedPageIndexes,\n    isThumbnailMultiselectEnabled,\n    isReaderModeOrReadOnly,\n    dispatch,\n    actions,\n    isMobile,\n    canLoad,\n    onCancel,\n    isThumbnailSelectingPages,\n    thumbnailSelectionMode,\n    activeDocumentViewerKey,\n    panelSelector,\n    parentKeyListener\n  } = props;\n  const thumbSize = thumbnailSize ? Number(thumbnailSize) : 150;\n  const [currentFocusIndex, setCurrentFocusIndex] = useState(-1);\n  const thumbContainerRef = useRef(null);\n  const buttonRefs = useRef([]);\n  const buttonMultiSelectRefs = useRef([]);\n  const [dimensions, setDimensions] = useState({ width: thumbSize, height: thumbSize });\n  const { t } = useTranslation();\n  // To ensure checkmark loads after thumbnail\n  const [loaded, setLoaded] = useState(false);\n\n  const isContentEditingEnabled = useSelector(selectors.isContentEditingEnabled);\n\n  let loadTimeout = null;\n\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (thumbContainerRef.current && !thumbContainerRef.current.contains(event.target)) {\n        preventDefaultTab();\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const loadThumbnailAsync = () => {\n    loadTimeout = setTimeout(() => {\n      const thumbnailContainer = getRootNode().querySelector(`.ThumbnailsPanel.${panelSelector} #pageThumb${index}`);\n\n      const pageNum = index + 1;\n      const viewerRotation = core.getRotation(pageNum);\n\n      const doc = core.getDocument(activeDocumentViewerKey);\n      // Possible race condition can happen where we try to render a thumbnail for a page that has\n      // been deleted. Prevent that by checking if pageInfo exists\n\n      if (doc && doc.getPageInfo(pageNum)) {\n        const id = doc.loadCanvas({\n          pageNumber: pageNum,\n          width: thumbSize,\n          height: thumbSize,\n          drawComplete: async (thumb) => {\n            const thumbnailContainer = getRootNode().querySelector(`.ThumbnailsPanel.${panelSelector} #pageThumb${index}`);\n            if (thumbnailContainer) {\n              const childElement = thumbnailContainer.querySelector('.page-image');\n              if (childElement) {\n                thumbnailContainer.removeChild(childElement);\n              }\n\n              thumb.className = 'page-image';\n\n              const ratio = Math.min(thumbSize / thumb.width, thumbSize / thumb.height);\n              thumb.style.width = `${thumb.width * ratio}px`;\n              thumb.style.height = `${thumb.height * ratio}px`;\n              setDimensions({ width: Number(thumb.width), height: Number(thumb.height) });\n\n              if (Math.abs(viewerRotation)) {\n                const cssTransform = `rotate(${viewerRotation * 90}deg) translate(-50%,-50%)`;\n                const cssTransformOrigin = 'top left';\n                thumb.style['transform'] = cssTransform;\n                thumb.style['transform-origin'] = cssTransformOrigin;\n                thumb.style['ms-transform'] = cssTransform;\n                thumb.style['ms-transform-origin'] = cssTransformOrigin;\n                thumb.style['-moz-transform'] = cssTransform;\n                thumb.style['-moz-transform-origin'] = cssTransformOrigin;\n                thumb.style['-webkit-transform-origin'] = cssTransformOrigin;\n                thumb.style['-webkit-transform'] = cssTransform;\n                thumb.style['-o-transform'] = cssTransform;\n                thumb.style['-o-transform-origin'] = cssTransformOrigin;\n              }\n\n              thumbnailContainer.appendChild(thumb);\n            }\n\n            if (updateAnnotations) {\n              updateAnnotations(index);\n            }\n\n            onFinishLoading(index);\n            setLoaded(true);\n          },\n          allowUseOfOptimizedThumbnail: true,\n        });\n        onLoad(index, thumbnailContainer, id);\n      }\n    }, THUMBNAIL_LOAD_DELAY);\n  };\n\n  useEffect(() => {\n    const onPagesUpdated = (changes) => {\n      const { contentChanged, moved, added, removed } = changes;\n\n      const currentPage = index + 1;\n\n      const isPageAdded = added.includes(currentPage);\n      const didPageChange = contentChanged.some((changedPage) => currentPage === changedPage);\n      const didPageMove = Object.keys(moved).some((movedPage) => currentPage === parseInt(movedPage));\n      const isPageRemoved = removed.includes(currentPage);\n      const newPageCount = core.getTotalPages();\n\n      if (removed.length > 0 && index + 1 > newPageCount) {\n        return;\n      }\n\n      if (isPageAdded || didPageChange || didPageMove || isPageRemoved) {\n        loadThumbnailAsync();\n      }\n    };\n\n    const onRotationUpdated = () => {\n      setLoaded(false);\n      loadThumbnailAsync();\n    };\n\n    core.addEventListener('pagesUpdated', onPagesUpdated);\n    core.addEventListener('rotationUpdated', onRotationUpdated);\n    if (canLoad) {\n      loadThumbnailAsync();\n    }\n    return () => {\n      core.removeEventListener('pagesUpdated', onPagesUpdated);\n      core.removeEventListener('rotationUpdated', onRotationUpdated);\n      clearTimeout(loadTimeout);\n      onRemove(index);\n    };\n  }, []);\n\n  useDidUpdate(() => {\n    if (canLoad) {\n      loadThumbnailAsync();\n      updateAnnotations(index);\n    } else {\n      onCancel(index);\n    }\n  }, [canLoad, activeDocumentViewerKey]);\n\n  const handleClick = (e) => {\n    const checkboxToggled = e.target.type && e.target.type === 'checkbox';\n    if (isThumbnailMultiselectEnabled && !isReaderModeOrReadOnly) {\n      const multiSelectionKeyPressed = e.ctrlKey || e.metaKey;\n      const shiftKeyPressed = e.shiftKey;\n      let updatedSelectedPages = [...selectedPageIndexes];\n\n      if (shiftKeyPressed) {\n        dispatch(actions.setThumbnailSelectingPages(true));\n        // Include current page as part of selection if we just started shift-selecting\n        let shiftKeyPivot = shiftKeyThumbnailPivotIndex;\n        if (shiftKeyPivot === null) {\n          shiftKeyPivot = currentPage - 1;\n          dispatch(actions.setShiftKeyThumbnailsPivotIndex(shiftKeyPivot));\n        }\n        // get the range of the selected index and update selected page\n        const currSelectMinIndex = Math.min(shiftKeyPivot, index);\n        const currSelectMaxIndex = Math.max(shiftKeyPivot, index);\n        updatedSelectedPages = [...new Set([...Array.from(\n          { length: currSelectMaxIndex - currSelectMinIndex + 1 },\n          (_, i) => i + currSelectMinIndex,\n        )])];\n      } else if (multiSelectionKeyPressed || isThumbnailSelectingPages) {\n        dispatch(actions.setThumbnailSelectingPages(true));\n        // Only select a page if multiSelectionKeyPressed or if checkbox is checked unless in 'thumbnail' mode\n        if (multiSelectionKeyPressed || checkboxToggled || thumbnailSelectionMode === thumbnailSelectionModes['THUMBNAIL']) {\n          // Include current page as part of selection if we just started multi-selecting\n          if (selectedPageIndexes.length === 0 && !isThumbnailSelectingPages) {\n            updatedSelectedPages.push(currentPage - 1);\n          } else if (selectedPageIndexes.includes(index)) {\n            updatedSelectedPages = selectedPageIndexes.filter((pageIndex) => index !== pageIndex);\n          } else {\n            updatedSelectedPages.push(index);\n          }\n        }\n        dispatch(actions.setShiftKeyThumbnailsPivotIndex(index));\n      } else {\n        updatedSelectedPages = [index];\n      }\n      // set shiftKeyPivot when press ctr key or single key\n      const lastSelectedPageIndex = updatedSelectedPages[updatedSelectedPages.length - 1];\n      const shouldUpdateShiftKeyPivotIndex = !isThumbnailSelectingPages && !shiftKeyPressed;\n\n      if (shouldUpdateShiftKeyPivotIndex) {\n        dispatch(actions.setShiftKeyThumbnailsPivotIndex(lastSelectedPageIndex));\n      }\n\n      dispatch(actions.setSelectedPageThumbnails(updatedSelectedPages));\n    } else if (isMobile()) {\n      dispatch(actions.closeElement('leftPanel'));\n    }\n\n    // due to the race condition, we need a settimeout here\n    // otherwise, sometimes the current page will not be visible in left panel\n    setTimeout(() => {\n      // If user clicks on checkbox, it should not automatically jump to that page,\n      // only if the user clicks on thumbnail then go to page and view it, unless in 'thumbnail' mode\n      if (!checkboxToggled || thumbnailSelectionMode === thumbnailSelectionModes['THUMBNAIL']) {\n        core.setCurrentPage(index + 1);\n      }\n    }, 0);\n  };\n\n  const isActive = currentPage === index + 1;\n  const pageLabel = pageLabels[index];\n  let checkboxRotateClass = 'default';\n  const rotation = core.getRotation(index + 1);\n  if ((!rotation || rotation === 2) && dimensions.width > dimensions.height) {\n    checkboxRotateClass = 'rotated';\n  } else if ((rotation === 1 || rotation === 3) && dimensions.width < dimensions.height) {\n    checkboxRotateClass = 'rotated';\n  }\n  useImperativeHandle(ref, () => ({\n    focusInput: () => {\n      if (isThumbnailSelectingPages && loaded) {\n        selectElement(buttonMultiSelectRefs.current[0]);\n        setCurrentFocusIndex(0);\n      } else if (buttonRefs.current) {\n        setTimeout(() => {\n          selectElement(buttonRefs.current[0]);\n          setCurrentFocusIndex(0);\n        }, 0);\n      }\n    }\n  }));\n\n  const selectElement = (element) => {\n    if (element) {\n      element.ariaCurrent = 'page';\n      element.focus();\n    }\n  };\n\n  const deselectElement = (element) => {\n    if (element) {\n      element.ariaCurrent = undefined;\n    }\n  };\n\n  const handleEnterGrid = (e) => {\n    e.preventDefault();\n    preventDefaultTab();\n  };\n\n  const preventDefaultTab = () => {\n    buttonRefs.current.forEach((elem) => {\n      deselectElement(elem);\n    });\n  };\n\n  const handleKeyDown = useCallback((e) => {\n    e.stopPropagation();\n    parentKeyListener(e);\n    const leaveFocusActions = {\n      Tab: () => handleEnterGrid(e),\n      Escape: () => handleEnterGrid(e)\n    };\n    if (leaveFocusActions[e.key]) {\n      leaveFocusActions[e.key]?.();\n    }\n    const keyboardActions = {\n      ArrowUp: () => handleArrowKey(e, -1),\n      ArrowDown: () => handleArrowKey(e, 1),\n      ArrowLeft: () => handleArrowKey(e, -1),\n      ArrowRight: () => handleArrowKey(e, 1),\n    };\n    if (keyboardActions[e.key] && !isMultiselectEnabled) {\n      keyboardActions[e.key]();\n    }\n  }, [buttonRefs.current, currentFocusIndex]);\n\n  const handleArrowKey = (e, direction) => {\n    e.preventDefault();\n    if (buttonRefs.current.length === 0) {\n      return;\n    }\n\n    setCurrentFocusIndex((prevIndex) => {\n      let newFocusIndex = prevIndex + direction;\n      if (newFocusIndex < 0) {\n        newFocusIndex = buttonRefs.current.length - 1;\n      } else if (newFocusIndex >= buttonRefs.current.length) {\n        newFocusIndex = 0;\n      }\n      updateTabIndexes(buttonRefs.current[newFocusIndex]);\n      return newFocusIndex;\n    });\n  };\n\n  const updateTabIndexes = (focusedElement) => {\n    buttonRefs.current.forEach((elem) => {\n      elem === focusedElement ? selectElement(elem) : deselectElement(elem);\n    });\n  };\n  useEffect(() => {\n    if (thumbContainerRef.current) {\n      buttonRefs.current = findFocusableElements(thumbContainerRef.current);\n    }\n  }, [shouldShowControls, isActive, loaded]);\n\n  useEffect(() => {\n    if (thumbContainerRef.current) {\n      buttonMultiSelectRefs.current = findFocusableElements(thumbContainerRef.current);\n    }\n  }, [isThumbnailSelectingPages, loaded]);\n\n  const isMultiselectEnabled = isThumbnailSelectingPages && loaded;\n\n  return (\n    <button\n      className={classNames({\n        Thumbnail: true,\n        active: isActive,\n        selected: isSelected && isThumbnailSelectingPages,\n      })}\n      onDragOver={(e) => onDragOver(e, index)}\n      id={`Thumbnail-container-${index}`}\n      ref={thumbContainerRef}\n      onKeyDown={(e) => handleKeyDown(e)}\n      onClick={handleClick}\n      style={{\n        width: thumbSize,\n        cursor: 'pointer',\n        background: 'none',\n        border: 'none'\n      }}\n      tabIndex={-1}\n    >\n      <div\n        className=\"container\"\n        style={{\n          height: thumbSize,\n          width: thumbSize,\n        }}\n        onDragStart={(e) => onDragStart(e, index)}\n        draggable={isDraggable}\n        tabIndex={-1}\n      >\n        <div id={`pageThumb${index}`} className=\"thumbnail\" />\n        {isThumbnailSelectingPages && loaded && (\n          <Choice\n            className={`checkbox ${checkboxRotateClass}`}\n            checked={selectedPageIndexes.includes(index)}\n            aria-label={`${t('action.page')} ${pageLabel} ${t('formField.types.checkbox')}`}\n            tabIndex={-1}\n          />\n        )}\n      </div>\n      <div className=\"page-label\">{pageLabel}</div>\n      {!isThumbnailSelectingPages && isActive && shouldShowControls && !isContentEditingEnabled && <ThumbnailControls index={index} />}\n    </button>\n  );\n});\n\nThumbnail.displayName = 'Thumbnail';\nThumbnail.propTypes = {\n  index: PropTypes.number,\n  isSelected: PropTypes.bool,\n  updateAnnotations: PropTypes.func,\n  shiftKeyThumbnailPivotIndex: PropTypes.number,\n  onFinishLoading: PropTypes.func,\n  onLoad: PropTypes.func,\n  onRemove: PropTypes.func,\n  onDragStart: PropTypes.func,\n  onDragOver: PropTypes.func,\n  isDraggable: PropTypes.bool,\n  shouldShowControls: PropTypes.bool,\n  thumbnailSize: PropTypes.number,\n  currentPage: PropTypes.number,\n  pageLabels: PropTypes.array,\n  selectedPageIndexes: PropTypes.array,\n  isThumbnailMultiselectEnabled: PropTypes.bool,\n  isReaderModeOrReadOnly: PropTypes.bool,\n  dispatch: PropTypes.func,\n  actions: PropTypes.object,\n  isMobile: PropTypes.func,\n  canLoad: PropTypes.bool,\n  onCancel: PropTypes.func,\n  isThumbnailSelectingPages: PropTypes.bool,\n  thumbnailSelectionMode: PropTypes.string,\n  activeDocumentViewerKey: PropTypes.number,\n  panelSelector: PropTypes.string,\n  parentKeyListener: PropTypes.func,\n};\n\nexport default Thumbnail;", "import React from 'react';\nimport { useSelector, shallowEqual, useDispatch } from 'react-redux';\n\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport { isMobile } from 'helpers/device';\n\nimport Thumbnail from './Thumbnail';\n\nconst ThumbnailRedux = React.forwardRef((props, ref) => {\n  const [\n    currentPage,\n    pageLabels,\n    selectedPageIndexes,\n    isThumbnailMultiselectEnabled,\n    isReaderMode,\n    isDocumentReadOnly,\n    shiftKeyThumbnailPivotIndex,\n    isThumbnailSelectingPages,\n    thumbnailSelectionMode,\n    activeDocumentViewerKey,\n    selectionModes\n  ] = useSelector(\n    (state) => [\n      selectors.getCurrentPage(state),\n      selectors.getPageLabels(state),\n      selectors.getSelectedThumbnailPageIndexes(state),\n      selectors.isThumbnailMultiselectEnabled(state),\n      selectors.isReaderMode(state),\n      selectors.isDocumentReadOnly(state),\n      selectors.getShiftKeyThumbnailPivotIndex(state),\n      selectors.isThumbnailSelectingPages(state),\n      selectors.getThumbnailSelectionMode(state),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual,\n  );\n\n  const dispatch = useDispatch();\n\n  return <Thumbnail {...props} {...{\n    ref,\n    currentPage,\n    pageLabels,\n    selectedPageIndexes,\n    isThumbnailMultiselectEnabled,\n    isReaderModeOrReadOnly: isReaderMode || isDocumentReadOnly,\n    dispatch,\n    actions,\n    isMobile,\n    shiftKeyThumbnailPivotIndex,\n    isThumbnailSelectingPages,\n    thumbnailSelectionMode,\n    selectionModes,\n    activeDocumentViewerKey,\n  }}\n  />;\n});\nThumbnailRedux.displayName = 'ThumbnailRedux';\nexport default ThumbnailRedux;\n", "import ThumbnailRedux from './ThumbnailRedux';\n\nexport default ThumbnailRedux;", "import React from 'react';\nimport Button from 'components/Button';\nimport '../ThumbnailControlsMulti.scss';\nimport PropTypes from 'prop-types';\n\nfunction MoveOperations({ moveToTop, moveToBottom }) {\n  return (\n    <>\n      <Button\n        className={'button-hover'}\n        dataElement=\"moveToTop\"\n        img=\"icon-page-move-up\"\n        onClick={moveToTop}\n        title=\"action.moveToTop\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"moveToBottom\"\n        img=\"icon-page-move-down\"\n        onClick={moveToBottom}\n        title=\"action.moveToBottom\"\n      />\n    </>\n  );\n}\n\nMoveOperations.propTypes = {\n  moveToTop: PropTypes.func,\n  moveToBottom: PropTypes.func,\n};\n\nexport default MoveOperations;\n", "import React from 'react';\nimport Button from 'components/Button';\nimport '../ThumbnailControlsMulti.scss';\nimport PropTypes from 'prop-types';\n\nfunction ManipulateOperations({ onInsert, onReplace, onExtractPages, onDeletePages }) {\n  return (\n    <>\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlInsert\"\n        img=\"icon-page-insertion-insert\"\n        onClick={onInsert}\n        title=\"action.insert\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlReplace\"\n        img=\"icon-page-replacement\"\n        onClick={onReplace}\n        title=\"action.replace\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlExtract\"\n        img=\"icon-page-manipulation-extract\"\n        onClick={onExtractPages}\n        title=\"action.extract\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlDelete\"\n        img=\"icon-delete-line\"\n        onClick={onDeletePages}\n        title=\"action.delete\"\n      />\n    </>\n  );\n}\n\nManipulateOperations.propTypes = {\n  onInsert: PropTypes.func,\n  onReplace: PropTypes.func,\n  onExtractPages: PropTypes.func,\n  onDeletePages: PropTypes.func,\n};\n\nexport default ManipulateOperations;\n", "import React from 'react';\nimport Button from 'components/Button';\nimport '../ThumbnailControlsMulti.scss';\nimport PropTypes from 'prop-types';\n\nfunction RotateOperations({ onRotateClockwise, onRotateCounterClockwise }) {\n  return (\n    <>\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlRotateCounterClockwise\"\n        img=\"icon-header-page-manipulation-page-rotation-counterclockwise-line\"\n        onClick={onRotateCounterClockwise}\n        title=\"action.rotateCounterClockwise\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlRotateClockwise\"\n        img=\"icon-header-page-manipulation-page-rotation-clockwise-line\"\n        onClick={onRotateClockwise}\n        title=\"action.rotateClockwise\"\n      />\n    </>\n  );\n}\n\nRotateOperations.propTypes = {\n  onRotateClockwise: PropTypes.func,\n  onRotateCounterClockwise: PropTypes.func,\n};\n\nexport default RotateOperations;\n", "import React, { useEffect, useState, useMemo } from 'react';\nimport { useDispatch, useSelector, useStore, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport {\n  deletePages,\n  extractPages,\n  noPagesSelectedWarning,\n  replace,\n  rotateClockwise,\n  rotateCounterClockwise,\n  movePagesToBottom,\n  movePagesToTop\n} from 'helpers/pageManipulationFunctions';\nimport { workerTypes } from 'constants/types';\nimport core from 'src/core';\nimport DataElements from 'constants/dataElement';\nimport { panelMinWidth } from 'constants/panel';\nimport { isMobile as isInMobile } from 'helpers/device';\nimport getRootNode from 'helpers/getRootNode';\nimport MoveOperations from './MoveOperations/MoveOperations';\nimport ManipulateOperations from './ManipulateOperations/ManipulateOperations';\nimport RotateOperations from './RotateOperations/RotateOperations';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\nimport {\n  getPageAdditionalControls,\n  getPageManipulationControls,\n  getPageRotationControls,\n  getPageCustomControlsFlyout\n} from 'helpers/pageManipulationFlyoutHelper';\nimport './ThumbnailControlsMulti.scss';\nimport PropTypes from 'prop-types';\nimport Button from 'components/Button';\n\n// Values come from the CSS\nconst WIDTH_MARGINS = 16 + 8 + 16 + 16 + 16 + 16;\n\nfunction ThumbnailControlsMultiContainer({ parentElement }) {\n  const store = useStore();\n  const dispatch = useDispatch();\n  const isMobile = isInMobile();\n  const selectedPageIndexes = useSelector(selectors.getSelectedThumbnailPageIndexes);\n  const panelWidth = useSelector((state) => !parentElement || parentElement === 'leftPanel' ? selectors.getLeftPanelWidth(state) : selectors.getPanelWidth(state, parentElement));\n  const deleteModalEnabled = useSelector(selectors.pageDeletionConfirmationModalEnabled);\n  const isDesktopOnlyMode = useSelector(selectors.isInDesktopOnlyMode);\n  const items = useSelector(selectors.getMultiPageManipulationControlsItems, shallowEqual);\n  const [displayFlyout, setDisplayFlyout] = useState(false);\n\n  const pageNumbers = useMemo(() =>\n    selectedPageIndexes.map((index) => index + 1), [selectedPageIndexes]);\n\n  const openInsertPageModal = () => {\n    dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n    dispatch(actions.openElement('insertPageModal'));\n  };\n\n  const childProps = useMemo(() => {\n    const onReplace = () => !noPagesSelectedWarning(pageNumbers, dispatch) && replace(dispatch);\n    const onExtractPages = () => !noPagesSelectedWarning(pageNumbers, dispatch) && extractPages(pageNumbers, dispatch);\n    const onDeletePages = () => !noPagesSelectedWarning(pageNumbers, dispatch) && deletePages(pageNumbers, dispatch, deleteModalEnabled);\n    const onRotateClockwise = () => !noPagesSelectedWarning(pageNumbers, dispatch) && rotateClockwise(pageNumbers);\n    const onRotateCounterClockwise = () => !noPagesSelectedWarning(pageNumbers, dispatch) && rotateCounterClockwise(pageNumbers);\n    const onInsert = () => !noPagesSelectedWarning(pageNumbers, dispatch) && openInsertPageModal();\n    const moveToTop = () => !noPagesSelectedWarning(pageNumbers, dispatch) && movePagesToTop(pageNumbers);\n    const moveToBottom = () => !noPagesSelectedWarning(pageNumbers, dispatch) && movePagesToBottom(pageNumbers);\n    return {\n      onReplace,\n      onExtractPages,\n      onDeletePages,\n      onRotateCounterClockwise,\n      onRotateClockwise,\n      onInsert,\n      moveToTop,\n      moveToBottom,\n      pageNumbers,\n    };\n  }, [pageNumbers, deleteModalEnabled]);\n\n  const { onRotateClockwise, onRotateCounterClockwise } = childProps;\n\n  const document = core.getDocument();\n  const documentType = document?.type;\n  const isXod = documentType === workerTypes.XOD;\n  const isOffice = documentType === workerTypes.OFFICE || documentType === workerTypes.LEGACY_OFFICE;\n\n  const smallBreakPoint = 190;\n  const largeBreakPoint = 290;\n  let widthMinusMargins;\n  if (!isDesktopOnlyMode && isMobile) {\n    try {\n      const appRect = getRootNode().querySelector('.App').getBoundingClientRect();\n      widthMinusMargins = appRect.width - WIDTH_MARGINS;\n    } catch (e) {\n      widthMinusMargins = (panelWidth || panelMinWidth) - WIDTH_MARGINS;\n    }\n  } else {\n    widthMinusMargins = (panelWidth || panelMinWidth) - WIDTH_MARGINS;\n  }\n  const isPanelSmall = widthMinusMargins < smallBreakPoint;\n  const isPanelLarge = widthMinusMargins > largeBreakPoint;\n\n  useEffect(() => {\n    const getFlyoutItemsFromType = (item) => {\n      if (item.dataElement === 'leftPanelPageTabsRotate') {\n        return getPageRotationControls(store, true);\n      } else if (item.dataElement === 'leftPanelPageTabsMove') {\n        return getPageAdditionalControls(store, true);\n      } else if (item.dataElement === 'leftPanelPageTabsMore') {\n        return getPageManipulationControls(store, true);\n      } else if (item.type === 'customPageOperation') {\n        return getPageCustomControlsFlyout(store, item);\n      } else if (item.type === 'divider') {\n        return ['divider'];\n      }\n    };\n    const flyoutItems = [];\n    let startIndex;\n    if (isPanelSmall) {\n      startIndex = 1;\n    } else if (isPanelLarge) {\n      startIndex = 3;\n    } else {\n      startIndex = 2;\n    }\n    let index = 0;\n    items.forEach((item) => {\n      let skip = false;\n      if (index < startIndex || (flyoutItems.length === 0 && item?.type === 'divider')) {\n        skip = true;\n      }\n      if (item && item.type !== 'divider') {\n        index++;\n      }\n      !skip && flyoutItems.push(...getFlyoutItemsFromType(item));\n    });\n    const flyout = {\n      dataElement: DataElements.PAGE_MANIPULATION_FLYOUT_MULTI_SELECT,\n      className: DataElements.PAGE_MANIPULATION_FLYOUT_MULTI_SELECT,\n      items: flyoutItems,\n    };\n    if (isPanelLarge) {\n      dispatch(actions.closeElement(flyout.dataElement));\n    }\n    if (flyout.items.length) {\n      dispatch(actions.updateFlyout(flyout.dataElement, flyout));\n      setDisplayFlyout(true);\n    } else {\n      dispatch(actions.removeFlyout(flyout.dataElement));\n      setDisplayFlyout(false);\n    }\n  }, [store, isPanelLarge, isPanelSmall, items]);\n\n  const renderedItems = useMemo(() => {\n    let lastDividerAdded = false;\n    let index = 0;\n    return items.map((item, actualIndex) => {\n      let lastIndexToRender;\n      if (isPanelSmall) {\n        lastIndexToRender = 0;\n      } else if (isPanelLarge) {\n        lastIndexToRender = 2;\n      } else {\n        lastIndexToRender = 1;\n      }\n      if (item?.type === 'divider' && !lastDividerAdded) {\n        if (index > lastIndexToRender) {\n          lastDividerAdded = true;\n        }\n        return <div key={`divider${actualIndex}`} className=\"divider\"/>;\n      }\n      if (index > lastIndexToRender) {\n        return null;\n      }\n      if (item && item.type !== 'divider') {\n        index++;\n      }\n      if (item.dataElement === 'leftPanelPageTabsRotate') {\n        return <RotateOperations {...childProps} key=\"leftPanelPageTabsRotate\"/>;\n      } else if (item.dataElement === 'leftPanelPageTabsMove') {\n        return <MoveOperations {...childProps} key=\"leftPanelPageTabsMove\"/>;\n      } else if (item.dataElement === 'leftPanelPageTabsMore') {\n        return <ManipulateOperations {...childProps} key=\"leftPanelPageTabsMore\"/>;\n      } else if (item.type === 'customPageOperation') {\n        if (!item.operations) {\n          index--;\n          return null;\n        }\n        return item.operations.map((operation) => (\n          <Button\n            key={operation.dataElement}\n            className={'button-hover'}\n            dataElement={operation.dataElement}\n            img={operation.img}\n            onClick={() => operation.onClick(pageNumbers)}\n            title={operation.title}\n          />\n        ));\n      }\n      return null;\n    });\n  }, [items, childProps, isPanelSmall, isPanelLarge]);\n\n  if (isXod || isOffice || document?.isWebViewerServerDocument()) {\n    return (\n      <div className={'PageControlContainer root small'}>\n        <RotateOperations onRotateClockwise={onRotateClockwise}\n          onRotateCounterClockwise={onRotateCounterClockwise}/>\n      </div>\n    );\n  }\n\n  return (<div className=\"PageControlContainer root\">\n    {renderedItems}\n    {displayFlyout && <div className=\"dropdown-menu\">\n      <ToggleElementButton\n        dataElement={`${DataElements.PAGE_MANIPULATION_FLYOUT_MULTI_SELECT}Button`}\n        toggleElement={DataElements.PAGE_MANIPULATION_FLYOUT_MULTI_SELECT}\n        title=\"action.more\"\n        img=\"icon-tool-more\"\n      />\n      <div className=\"indicator\"/>\n    </div>}\n  </div>);\n}\n\nThumbnailControlsMultiContainer.propTypes = {\n  parentElement: PropTypes.string,\n};\n\nexport default ThumbnailControlsMultiContainer;\n", "import ThumbnailControlsMultiContainer from './ThumbnailControlsMultiContainer';\n\nexport default ThumbnailControlsMultiContainer;", "import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport Button from 'components/Button';\nimport getPageArrayFromString from 'helpers/getPageArrayFromString';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport pageNumberPlaceholder from 'constants/pageNumberPlaceholder';\nimport core from 'src/core';\nimport { useTranslation } from 'react-i18next';\nimport ThumbnailControlsMulti from 'src/components/ThumbnailControlsMulti';\nimport './DocumentControls.scss';\n\nfunction getPageString(selectedPageArray, pageLabels) {\n  let pagesToPrint = '';\n  const sortedPages = selectedPageArray.sort((a, b) => a - b);\n  let prevIndex = null;\n\n  for (let i = 0; sortedPages.length > i; i++) {\n    if (sortedPages[i + 1] === sortedPages[i] + 1) {\n      prevIndex = prevIndex !== null ? prevIndex : sortedPages[i];\n    } else if (prevIndex !== null) {\n      pagesToPrint = `${pagesToPrint}${pageLabels[prevIndex]}-${pageLabels[sortedPages[i]]}, `;\n      prevIndex = null;\n    } else {\n      pagesToPrint = `${pagesToPrint}${pageLabels[sortedPages[i]]}, `;\n    }\n  }\n\n  return pagesToPrint.slice(0, -2);\n}\n\nconst DocumentControls = ({ shouldShowControls, parentElement }) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const [\n    selectedPageIndexes,\n    isDisabled,\n    pageLabels,\n    isThumbnailSelectingPages,\n    featureFlags,\n  ] = useSelector((state) => [\n    selectors.getSelectedThumbnailPageIndexes(state),\n    selectors.isElementDisabled(state, 'documentControl'),\n    selectors.getPageLabels(state),\n    selectors.isThumbnailSelectingPages(state),\n    selectors.getFeatureFlags(state),\n  ]);\n\n  const initialPagesString = getPageString(selectedPageIndexes, pageLabels);\n\n  const [pageString, setPageString] = useState(initialPagesString);\n  const [previousPageString, setPreviousPageString] = useState(initialPagesString);\n  const customizableUI = featureFlags.customizableUI;\n\n  useEffect(() => {\n    setPageString(getPageString(selectedPageIndexes, pageLabels));\n  }, [setPageString, selectedPageIndexes, shouldShowControls, pageLabels]);\n\n  const onBlur = (e) => {\n    const selectedPagesString = e.target.value.replace(/ /g, '');\n    const pages = !selectedPagesString ? [] : getPageArrayFromString(selectedPagesString, pageLabels);\n    const pageIndexes = pages.map((page) => page - 1);\n\n    if (pages.length || !selectedPagesString) {\n      dispatch(actions.setSelectedPageThumbnails(pageIndexes));\n\n      const updatedString = getPageString(selectedPageIndexes, pageLabels);\n\n      setPageString(updatedString);\n      setPreviousPageString(updatedString);\n    } else {\n      setPageString(previousPageString);\n    }\n\n    if (selectedPageIndexes.length > 0 && !isThumbnailSelectingPages) {\n      // set a short timeout due to race condition caused by onBlur and\n      // changing the documentControlsButton based on isThumbnailSelectingPages\n      setTimeout(() => {\n        if (pageString !== previousPageString) {\n          enableThumbnailSelectingPages();\n        }\n      }, 100);\n    }\n  };\n\n  const pageStringUpdate = (e) => {\n    setPageString(e.target.value);\n  };\n\n  const disableThumbnailSelectingPages = () => {\n    dispatch(actions.setSelectedPageThumbnails([core.getCurrentPage() - 1]));\n    dispatch(actions.setThumbnailSelectingPages(false));\n  };\n\n  const enableThumbnailSelectingPages = () => {\n    dispatch(actions.setThumbnailSelectingPages(true));\n  };\n\n  return isDisabled ? null : (\n    <div className={'documentControlsContainer'} data-element={'documentControl'}>\n      {shouldShowControls ? (\n        <div className={'documentControls'}>\n          <div className={'divider'}></div>\n          {isThumbnailSelectingPages && <ThumbnailControlsMulti parentElement={parentElement}/>}\n          {customizableUI &&\n            <label className={'documentControlsLabel'} htmlFor=\"pageNumbersInput\">\n              <span>\n                {t('option.thumbnailPanel.multiSelectPages')} -\n              </span>\n              <span className='multiSelectExampleLabel'>\n                {t('option.thumbnailPanel.multiSelectPagesExample')}\n              </span>\n            </label>\n          }\n          <div className={'documentControlsInput'}>\n            <input\n              name=\"pageNumbersInput\"\n              onBlur={onBlur}\n              onChange={pageStringUpdate}\n              value={pageString}\n              placeholder={customizableUI ? '' : pageNumberPlaceholder}\n              aria-label={t('option.thumbnailPanel.enterPageNumbers')}\n              className=\"pagesInput\"\n              type=\"text\"\n            />\n            <div className={'documentControlsButton'}>\n              {!isThumbnailSelectingPages ? (\n                <Button\n                  img={'icon-tool-select-pages'}\n                  title={'option.documentControls.selectTooltip'}\n                  onClick={enableThumbnailSelectingPages}\n                  dataElement={'thumbMultiSelect'}\n                />\n              ) : (\n                <Button\n                  img={'icon-close'}\n                  title={'option.documentControls.closeTooltip'}\n                  onClick={disableThumbnailSelectingPages}\n                  dataElement={'thumbCloseMultiSelect'}\n                />\n              )}\n            </div>\n          </div>\n        </div>\n      ) : null}\n    </div>\n  );\n};\n\nDocumentControls.propTypes = {\n  isDisabled: PropTypes.bool,\n  pageLabels: PropTypes.arrayOf(PropTypes.string),\n  toggleDocumentControl: PropTypes.func,\n  shouldShowControls: PropTypes.bool,\n};\n\nexport default DocumentControls;\n", "export default '1, 3, 5-10';", "import DocumentControls from './DocumentControls';\n\nexport default DocumentControls;", "import core from 'core';\nimport actions from 'actions';\nimport extractPagesWithAnnotations from './extractPagesWithAnnotations';\nimport fireEvent from './fireEvent';\nimport Events from 'constants/events';\nimport DataElements from 'src/constants/dataElement';\n\nexport const extractPagesToMerge = (pageNumbers) => {\n  // extract pages and put the data on the iFrame window element for another instance of WebViewer to access\n  window.extractedDataPromise = extractPagesWithAnnotations(pageNumbers);\n  window.pagesExtracted = pageNumbers;\n};\n\nexport const mergeDocument = (srcToMerge, mergeToPage, shouldFireEvent = true) => (dispatch) => {\n  dispatch(actions.openElement(DataElements.LOADING_MODAL));\n\n  return new Promise((resolve, reject) => {\n    core.mergeDocument(srcToMerge, mergeToPage).then((mergeResults) => {\n      dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n      core.setCurrentPage(mergeToPage);\n\n      if (shouldFireEvent) {\n        fireEvent(Events.DOCUMENT_MERGED, mergeResults);\n      }\n\n      resolve(mergeResults);\n    }).catch((err) => {\n      reject(err);\n      dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n    });\n  });\n};\n\nexport const mergeExternalWebViewerDocument = (viewerID, mergeToPage) => (dispatch) => {\n  return new Promise((resolve, reject) => {\n    const otherWebViewerIframe = window.parent.document.querySelector(`#${viewerID}`);\n    if (!otherWebViewerIframe) {\n      console.warn('Could not find other instance of WebViewer');\n      reject();\n    }\n\n    let contentWindow = otherWebViewerIframe.contentWindow;\n    if (window.isApryseWebViewerWebComponent) {\n      contentWindow = window;\n    }\n\n    const extractedDataPromise = contentWindow.extractedDataPromise;\n    if (!extractedDataPromise) {\n      console.warn('Could not retrieve data from other instance of WebViewer');\n      reject();\n    }\n\n    dispatch(actions.openElement(DataElements.LOADING_MODAL));\n    extractedDataPromise.then((docToMerge) => {\n      dispatch(mergeDocument(docToMerge, mergeToPage, false)).then(({ filename, pages }) => {\n        fireEvent(Events.DOCUMENT_MERGED, { filename, pages: contentWindow.pagesExtracted });\n        dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n        resolve({ filename, pages });\n      });\n    }).catch((err) => {\n      dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n      reject(err);\n    });\n  });\n};", "import debounce from 'lodash/debounce';\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { List } from 'react-virtualized';\nimport Measure from 'react-measure';\nimport classNames from 'classnames';\nimport { isIE11 } from 'helpers/device';\n\nimport Thumbnail from 'components/Thumbnail';\nimport DocumentControls from 'components/DocumentControls';\nimport Button from 'components/Button';\nimport Slider from 'components/Slider';\n\nimport core from 'core';\nimport { extractPagesToMerge, mergeDocument, mergeExternalWebViewerDocument } from 'helpers/pageManipulation';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport Events from 'constants/events';\nimport DataElements from 'constants/dataElement';\nimport fireEvent from 'helpers/fireEvent';\n\nimport './ThumbnailsPanel.scss';\nimport getRootNode from 'helpers/getRootNode';\nimport { useTranslation } from 'react-i18next';\n\nconst dataTransferWebViewerFrameKey = 'dataTransferWebViewerFrame';\n\nconst ZOOM_RANGE_MIN = '100';\nconst ZOOM_RANGE_MAX = '1000';\nconst ZOOM_RANGE_STEP = '50';\nconst MAX_COLUMNS = 16;\n\nconst hoverAreaHeight = 25;\n\nconst ThumbnailsPanel = ({ panelSelector, parentDataElement }) => {\n  const isLeftPanelOpen = useSelector((state) => selectors.isElementOpen(state, 'leftPanel'));\n  const isDisabled = useSelector((state) => selectors.isElementDisabled(state, 'thumbnailsPanel'));\n  const totalPages = useSelector(selectors.getTotalPages);\n  const currentPage = useSelector(selectors.getCurrentPage);\n  const selectedPageIndexes = useSelector((state) => selectors.getSelectedThumbnailPageIndexes(state), shallowEqual);\n  const isThumbnailMergingEnabled = useSelector(selectors.getIsThumbnailMergingEnabled);\n  const isThumbnailReorderingEnabled = useSelector(selectors.getIsThumbnailReorderingEnabled);\n  const isMultipleViewerMerging = useSelector(selectors.getIsMultipleViewerMerging);\n  const isThumbnailControlDisabled = useSelector((state) => selectors.isElementDisabled(state, 'thumbnailControl'));\n  const isThumbnailSliderDisabled = useSelector((state) => selectors.isElementDisabled(state, 'thumbnailsSizeSlider'));\n  const isReaderMode = useSelector(selectors.isReaderMode);\n  const isDocumentReadOnly = useSelector(selectors.isDocumentReadOnly);\n  const totalPagesFromSecondaryDocumentViewer = useSelector((state) => selectors.getTotalPages(state, 2));\n  const activeDocumentViewerKey = useSelector(selectors.getActiveDocumentViewerKey);\n  const isRightClickEnabled = useSelector(selectors.openingPageManipulationOverlayByRightClickEnabled);\n  const featureFlags = useSelector(selectors.getFeatureFlags, shallowEqual);\n  const isContentEditingEnabled = useSelector(selectors.isContentEditingEnabled);\n\n  const [t] = useTranslation();\n\n  const listRef = useRef();\n  const pendingThumbs = useRef([]);\n  const thumbs = useRef([]);\n  const afterMovePageNumber = useRef(null);\n\n  const [isOfficeEditor, setIsOfficeEditor] = useState(false);\n  const [canLoad, setCanLoad] = useState(true);\n  const [height, setHeight] = useState(0);\n  const [width, setWidth] = useState(0);\n  const [draggingOverPageIndex, setDraggingOverPageIndex] = useState(null);\n  const [isDraggingToPreviousPage, setDraggingToPreviousPage] = useState(false);\n  const [numberOfColumns, setNumberOfColumns] = useState(1);\n  const [isDragging, setIsDragging] = useState(false);\n  const [focusedIndex, setFocusedIndex] = useState(0);\n  const thumbnailContainerRefs = useRef([]);\n  const thumbnailRefs = useRef([]);\n  const focusedThumbnailRef = useRef(null);\n\n  const [thumbnailSize, setThumbnailSize] = useState(150);\n  const [lastTimeTriggered, setLastTimeTriggered] = useState(0);\n  const [globalIndex, setGlobalIndex] = useState(0);\n  const pageCount = activeDocumentViewerKey === 2 ? totalPagesFromSecondaryDocumentViewer : totalPages;\n  const customizableUI = featureFlags?.customizableUI;\n\n  const dispatch = useDispatch();\n\n  // If memory becomes an issue, change this to use pageNumbers.\n  // Instead of a debounced drawAnnotations function, perhaps use\n  // a function that first checks for the pageNumber in this map\n  // before calling drawAnnotations on a page.\n  let activeThumbRenders = {};\n\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (focusedThumbnailRef.current && !focusedThumbnailRef.current.contains(event.target)) {\n        removeOutline(focusedThumbnailRef.current);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleKeyDown = useCallback((e, currentIndex, columnCount) => {\n    e.stopPropagation();\n    let newIndex;\n    const arrowActions = {\n      ArrowLeft: () => handleArrowKey(e, currentIndex, -1),\n      ArrowRight: () => handleArrowKey(e, currentIndex, 1),\n      ArrowUp: () => handleArrowKey(e, currentIndex, -columnCount),\n      ArrowDown: () => handleArrowKey(e, currentIndex, columnCount),\n    };\n    const keyActions = {\n      Enter: () => handleEnterKey(e, currentIndex),\n      Tab: () => handleTabOutKey(currentIndex),\n    };\n\n    if (arrowActions[e.key]) {\n      newIndex = arrowActions[e.key]();\n    }\n    if (keyActions[e.key]) {\n      keyActions[e.key]();\n    }\n\n    if (newIndex !== undefined && newIndex !== currentIndex) {\n      setFocusedIndex(newIndex);\n      selectElement(thumbnailContainerRefs.current[newIndex]);\n      deselectElement(thumbnailContainerRefs.current[currentIndex]);\n    }\n  }, [thumbnailRefs, thumbnailContainerRefs]);\n\n  const selectElement = (element) => {\n    element.tabIndex = 0;\n    element.ariaCurrent = 'page';\n    element.style.outline = 'var(--focus-visible-outline)';\n    element.focus();\n    focusedThumbnailRef.current = element;\n  };\n\n  const deselectElement = (element) => {\n    element.tabIndex = -1;\n    element.ariaCurrent = undefined;\n    removeOutline(element);\n  };\n\n  const removeOutline = (element) => {\n    element.style.outline = 'none';\n  };\n\n  const handleTabOutKey = (index) => {\n    removeOutline(thumbnailContainerRefs.current[index]);\n  };\n\n  const handleArrowKey = (e, currentIndex, direction) => {\n    let newIndex = currentIndex + direction;\n    if (newIndex < 0 || newIndex >= pageCount) {\n      return currentIndex;\n    }\n    return newIndex;\n  };\n\n  const handleEnterKey = (e, index) => {\n    e.preventDefault();\n    selectElement(thumbnailContainerRefs.current[index]);\n    core.setCurrentPage(index + 1);\n    thumbnailRefs.current[index].focusInput();\n  };\n\n  const handleThumbnailKeyDown = useCallback((e, index) => {\n    e.stopPropagation();\n    const keyActions = {\n      Tab: () => handleTabKey(e, index),\n      Escape: () => handleEscapeKey(e, index)\n    };\n    if (keyActions[e.key]) {\n      keyActions[e.key]?.();\n    }\n  }, [thumbnailContainerRefs]);\n\n  const handleEscapeKey = (e, index) => {\n    e.preventDefault();\n    selectElement(thumbnailContainerRefs.current[index]);\n  };\n\n  const handleTabKey = (e, index) => {\n    let direction = e.shiftKey ? -1 : 1;\n    let newIndex = index + direction;\n    if (newIndex < 0 || newIndex >= pageCount) {\n      newIndex = index;\n    }\n    setFocusedIndex(newIndex);\n    selectElement(thumbnailContainerRefs.current[newIndex]);\n    if (newIndex !== index) {\n      deselectElement(thumbnailContainerRefs.current[index]);\n    }\n  };\n\n  const getThumbnailSize = (pageWidth, pageHeight) => {\n    let width;\n    let height;\n    let ratio;\n\n    if (pageWidth > pageHeight) {\n      ratio = pageWidth / thumbnailSize;\n      width = thumbnailSize;\n      height = Math.round(pageHeight / ratio);\n    } else {\n      ratio = pageHeight / thumbnailSize;\n      width = Math.round(pageWidth / ratio); // Chrome has trouble displaying borders of non integer width canvases.\n      height = thumbnailSize;\n    }\n\n    return {\n      width,\n      height,\n    };\n  };\n\n  const updateAnnotations = (pageIndex) => {\n    const thumbContainer = thumbs.current && thumbs.current[pageIndex] && thumbs.current[pageIndex].element;\n    if (!thumbContainer) {\n      return;\n    }\n\n    const pageNumber = pageIndex + 1;\n    const pageWidth = core.getPageWidth(pageNumber);\n    const pageHeight = core.getPageHeight(pageNumber);\n\n    const { width, height } = getThumbnailSize(pageWidth, pageHeight);\n\n    const annotCanvas = thumbContainer.querySelector('.annotation-image') || document.createElement('canvas');\n    annotCanvas.className = 'annotation-image';\n    annotCanvas.role = 'img';\n    annotCanvas.ariaLabel = `${t('action.page')} ${pageNumber}`;\n    annotCanvas.style.maxWidth = `${thumbnailSize}px`;\n    annotCanvas.style.maxHeight = `${thumbnailSize}px`;\n    const ctx = annotCanvas.getContext('2d');\n\n    let zoom = 1;\n    let rotation = core.getCompleteRotation(pageNumber);\n    if (rotation < 0) {\n      rotation += 4;\n    }\n    const multiplier = window.Core.getCanvasMultiplier();\n\n    if (rotation % 2 === 0) {\n      annotCanvas.width = width;\n      annotCanvas.height = height;\n      zoom = annotCanvas.width / pageWidth;\n      zoom /= multiplier;\n    } else {\n      annotCanvas.width = height;\n      annotCanvas.height = width;\n\n      zoom = annotCanvas.height / pageWidth;\n      zoom /= multiplier;\n    }\n\n    thumbContainer.appendChild(annotCanvas);\n    core.setAnnotationCanvasTransform(ctx, zoom, rotation);\n\n    let options = {\n      pageNumber,\n      overrideCanvas: annotCanvas,\n    };\n\n    const thumb = thumbContainer.querySelector('.page-image');\n\n    if (thumb) {\n      options = {\n        ...options,\n        overridePageRotation: rotation,\n        overridePageCanvas: thumb,\n      };\n    } else {\n      return;\n    }\n\n    if (!activeThumbRenders[pageNumber]) {\n      activeThumbRenders[pageNumber] = debounce(core.drawAnnotations, 112);\n    }\n    const debouncedDraw = activeThumbRenders[pageNumber];\n    debouncedDraw(options);\n  };\n\n  useEffect(() => {\n    const onBeginRendering = () => {\n      setCanLoad(false);\n    };\n\n    const onFinishedRendering = (needsMoreRendering) => {\n      if (!needsMoreRendering) {\n        setCanLoad(true);\n      }\n    };\n\n    const onDocumentLoaded = () => {\n      if (core.getDocument()?.getType() === 'officeEditor') {\n        setIsOfficeEditor(true);\n      } else {\n        setIsOfficeEditor(false);\n      }\n      activeThumbRenders = {};\n      dispatch(actions.setSelectedPageThumbnails([]));\n    };\n\n    const onPageComplete = () => {\n      if (afterMovePageNumber.current) {\n        core.setCurrentPage(afterMovePageNumber.current);\n        afterMovePageNumber.current = null;\n      }\n    };\n\n    core.addEventListener('beginRendering', onBeginRendering);\n    core.addEventListener('finishedRendering', onFinishedRendering);\n    core.addEventListener('documentLoaded', onDocumentLoaded);\n    core.addEventListener('pageComplete', onPageComplete);\n\n\n    // The document might have already been loaded before this component is mounted.\n    // If document is already loaded, call 'onDocumentLoaded()' manually to update the state properly.\n    if (core.getDocument()) {\n      onDocumentLoaded();\n    }\n\n    return () => {\n      core.removeEventListener('beginRendering', onBeginRendering);\n      core.removeEventListener('finishedRendering', onFinishedRendering);\n      core.removeEventListener('documentLoaded', onDocumentLoaded);\n      core.removeEventListener('pageComplete', onPageComplete);\n    };\n  }, []);\n\n  useEffect(() => {\n    const onPagesUpdated = (changes) => {\n      if (!changes) {\n        return;\n      }\n      let updatedPagesIndexes = Array.from(selectedPageIndexes);\n\n      if (changes.removed) {\n        updatedPagesIndexes = updatedPagesIndexes.filter((pageIndex) => changes.removed.indexOf(pageIndex + 1) === -1);\n      }\n\n      if (changes.moved) {\n        updatedPagesIndexes = updatedPagesIndexes.map((pageIndex) => (changes.moved[pageIndex + 1] ? changes.moved[pageIndex + 1] - 1 : pageIndex),\n        );\n      }\n\n      const isPageAddedBefore = changes.added && (changes.added[0] - 1) <= updatedPagesIndexes[0];\n      if (updatedPagesIndexes.length === 1 && isPageAddedBefore) {\n        updatedPagesIndexes = changes.added.map((pageNumber) => pageNumber - 1);\n      }\n\n      dispatch(actions.setSelectedPageThumbnails(updatedPagesIndexes));\n    };\n\n    core.addEventListener('pagesUpdated', onPagesUpdated);\n\n    return () => core.removeEventListener('pagesUpdated', onPagesUpdated);\n  }, [selectedPageIndexes]);\n\n  useEffect(() => {\n    listRef.current?.scrollToRow(Math.floor((currentPage - 1) / numberOfColumns));\n    const onAnnotationChanged = (annots) => {\n      const indices = [];\n\n      annots.forEach((annot) => {\n        const pageIndex = annot.PageNumber - 1;\n        if (!annot.Listable || indices.indexOf(pageIndex) > -1) {\n          return;\n        }\n        indices.push(pageIndex);\n\n        updateAnnotations(pageIndex);\n      });\n    };\n\n    const onPageNumberUpdated = (pageNumber) => {\n      const pageIndex = pageNumber - 1;\n      listRef.current?.scrollToRow(Math.floor(pageIndex / numberOfColumns));\n    };\n\n    core.addEventListener('pageNumberUpdated', onPageNumberUpdated);\n    core.addEventListener('annotationChanged', onAnnotationChanged);\n    core.addEventListener('annotationHidden', onAnnotationChanged);\n\n    return () => {\n      core.removeEventListener('pageNumberUpdated', onPageNumberUpdated);\n      core.removeEventListener('annotationChanged', onAnnotationChanged);\n      core.removeEventListener('annotationHidden', onAnnotationChanged);\n    };\n  }, [thumbnailSize, numberOfColumns]);\n\n  useEffect(() => {\n    if (isReaderMode || isDocumentReadOnly) {\n      dispatch(actions.setSelectedPageThumbnails([]));\n      dispatch(actions.setThumbnailSelectingPages(false));\n    }\n  }, [isReaderMode, isDocumentReadOnly]);\n\n  // if disabled, or is office editor or left panel is not open when we are not in customize mode, return\n  if (isDisabled || isOfficeEditor || (!isLeftPanelOpen && !panelSelector && !customizableUI)) {\n    return null;\n  }\n  const onDragEnd = () => {\n    setIsDragging(false);\n    setDraggingOverPageIndex(null);\n  };\n\n  const scrollToRowHelper = (index, change, time) => {\n    const now = new Date().getTime();\n    if (index < pageCount - 1 && index > 0 && now - lastTimeTriggered >= time) {\n      listRef.current?.scrollToRow(Math.floor((index + change) / numberOfColumns));\n      setLastTimeTriggered(now);\n      return index + change;\n    }\n    return index;\n  };\n\n  const onDragOver = (e, index) => {\n    // 'preventDefault' to prevent opening pdf dropped in and 'stopPropagation' to keep parent from opening pdf\n    e.preventDefault();\n    e.stopPropagation();\n    if (!isThumbnailReorderingEnabled && !isThumbnailMergingEnabled) {\n      return;\n    }\n\n    const thumbnail = e.target.getBoundingClientRect();\n    if (numberOfColumns > 1) {\n      // row with more than 1 thumbnail so user are dragging to the left and right\n      setDraggingToPreviousPage(!(e.pageX > thumbnail.x + thumbnail.width / 2));\n    } else {\n      setDraggingToPreviousPage(!(e.pageY > thumbnail.y + thumbnail.height / 2));\n    }\n\n    setDraggingOverPageIndex(index);\n    const virtualizedThumbnailContainerElement = getRootNode().querySelector('#virtualized-thumbnails-container');\n    const { y, bottom } = virtualizedThumbnailContainerElement.getBoundingClientRect();\n\n    if (e.pageY < y + hoverAreaHeight * 4) {\n      setGlobalIndex(scrollToRowHelper(index, -1, 400));\n    } else if (e.pageY > bottom - hoverAreaHeight * 4) {\n      setGlobalIndex(scrollToRowHelper(index, 1, 400));\n    }\n  };\n\n  const scrollDown = () => {\n    setGlobalIndex(scrollToRowHelper(globalIndex, 1, 200));\n  };\n  const scrollUp = () => {\n    setGlobalIndex(scrollToRowHelper(globalIndex, -1, 200));\n  };\n\n  const getContextElementId = () => {\n    if (window.isApryseWebViewerWebComponent) {\n      return getRootNode().host.id;\n    } else {\n      return window.frameElement.id;\n    }\n  };\n\n  const onDragStart = (e, index) => {\n    setGlobalIndex(index);\n    setIsDragging(true);\n    const draggingSelectedPage = selectedPageIndexes.some((i) => i === index);\n    const pagesToMove = draggingSelectedPage ? selectedPageIndexes.map((index) => index + 1) : [index + 1];\n    fireEvent(Events.THUMBNAIL_DRAGGED);\n    // need to set 'text' to empty for drag to work in FireFox and mobile\n    e.dataTransfer.setData('text', '');\n\n    if (pagesToMove.length > 1) {\n      // can't set to null so set to new instance of an image\n      e.dataTransfer.setDragImage(new Image(), 0, 0);\n    }\n\n    if (isThumbnailMergingEnabled && isMultipleViewerMerging) {\n      e.dataTransfer.dropEffect = 'move';\n      e.dataTransfer.effectAllowed = 'all';\n      e.dataTransfer.setData(dataTransferWebViewerFrameKey, getContextElementId());\n      extractPagesToMerge(pagesToMove);\n    }\n\n    if (!draggingSelectedPage) {\n      dispatch(actions.setSelectedPageThumbnails([index]));\n    }\n\n    core.setCurrentPage(index + 1);\n  };\n\n  const onDrop = (e) => {\n    e.preventDefault();\n    const { files } = e.dataTransfer;\n    const insertTo = isDraggingToPreviousPage ? draggingOverPageIndex + 1 : draggingOverPageIndex + 2;\n    let externalPageWebViewerFrameId;\n    if (!isIE11) {\n      // at this time of writing, IE11 does not really have support for getData\n      externalPageWebViewerFrameId = e.dataTransfer.getData(dataTransferWebViewerFrameKey);\n    }\n    const mergingDocument =\n      (externalPageWebViewerFrameId && getContextElementId() !== externalPageWebViewerFrameId) || files.length;\n    const currentPageIndex = currentPage - 1;\n\n    if (isThumbnailMergingEnabled && mergingDocument) {\n      if (externalPageWebViewerFrameId && getContextElementId() !== externalPageWebViewerFrameId) {\n        dispatch(mergeExternalWebViewerDocument(externalPageWebViewerFrameId, insertTo));\n      } else if (files.length) {\n        Array.from(files).forEach((file) => {\n          dispatch(mergeDocument(file, insertTo));\n        });\n      }\n    } else if (isThumbnailReorderingEnabled && !mergingDocument) {\n      if (draggingOverPageIndex !== null) {\n        const targetPageNumber = isDraggingToPreviousPage ? draggingOverPageIndex + 1 : draggingOverPageIndex + 2;\n        const draggingSelectedPage = selectedPageIndexes.some((i) => i === currentPageIndex);\n        const pageNumbersToMove = draggingSelectedPage ? selectedPageIndexes.map((i) => i + 1) : [currentPage];\n        afterMovePageNumber.current = targetPageNumber - pageNumbersToMove.filter((p) => p < targetPageNumber).length;\n        core.movePages(pageNumbersToMove, targetPageNumber);\n        const updatedPagesNumbers = [];\n        for (let offset = 0; offset < pageNumbersToMove.length; offset++) {\n          updatedPagesNumbers.push(afterMovePageNumber.current + offset);\n        }\n        fireEvent(Events.THUMBNAIL_DROPPED, { pageNumbersBeforeMove: pageNumbersToMove, pagesNumbersAfterMove: updatedPagesNumbers, numberOfPagesMoved: updatedPagesNumbers.length });\n      }\n    }\n    setDraggingOverPageIndex(null);\n    setIsDragging(false);\n  };\n\n  const onLoad = (pageIndex, element, id) => {\n    if (!thumbIsLoaded(pageIndex) && !thumbIsPending(pageIndex)) {\n      thumbs.current[pageIndex] = {\n        element,\n        loaded: false,\n      };\n\n      pendingThumbs.current.push({\n        pageIndex,\n        id,\n      });\n    }\n  };\n\n  const removeFromPendingThumbs = (pageIndex) => {\n    const index = getPendingThumbIndex(pageIndex);\n    if (index !== -1) {\n      pendingThumbs.current.splice(index, 1);\n    }\n  };\n\n  const thumbIsLoaded = (pageIndex) => thumbs.current[pageIndex]?.loaded;\n\n  const thumbIsPending = (pageIndex) => getPendingThumbIndex(pageIndex) !== -1;\n\n  const onCancel = (pageIndex) => {\n    const index = getPendingThumbIndex(pageIndex);\n    if (index !== -1) {\n      core.cancelLoadThumbnail(pendingThumbs.current[index].id);\n      pendingThumbs.current.splice(index, 1);\n    }\n  };\n\n  const onRightClick = (event, pageIndex) => {\n    event.preventDefault();\n    core.setCurrentPage(pageIndex + 1);\n    if (!selectedPageIndexes.includes(pageIndex)) {\n      dispatch(actions.setSelectedPageThumbnails([pageIndex]));\n    }\n\n    if (isReaderMode || isDocumentReadOnly || isContentEditingEnabled) {\n      return;\n    }\n\n    dispatch(actions.setFlyoutPosition({ x: event.pageX, y: event.pageY }));\n    dispatch(actions.openElements([DataElements.PAGE_MANIPULATION]));\n  };\n\n  const getPendingThumbIndex = (pageIndex) => pendingThumbs.current.findIndex((thumbStatus) => thumbStatus.pageIndex === pageIndex);\n\n  const onRemove = (pageIndex) => {\n    onCancel(pageIndex);\n    const canvases = thumbs.current[pageIndex]?.element?.querySelectorAll('canvas');\n    if (canvases?.length) {\n      canvases.forEach((c) => {\n        c.height = 0;\n        c.width = 0;\n      });\n    }\n\n    if (activeThumbRenders[pageIndex]) {\n      activeThumbRenders[pageIndex].cancel();\n    }\n    thumbs.current[pageIndex] = null;\n  };\n\n  const handleFocus = (e, index) => {\n    if (thumbnailContainerRefs.current && thumbnailContainerRefs.current.includes(e.target)) {\n      selectElement(thumbnailContainerRefs.current[index]);\n    }\n  };\n\n  const renderThumbnails = ({ index, key, style }) => {\n    const className = classNames({\n      columnsOfThumbnails: numberOfColumns > 1,\n      row: true,\n    });\n    const allowPageOperationsUI = !(isReaderMode || isDocumentReadOnly);\n    return (\n      <div role=\"row\" aria-label=\"row\" className={className} key={key} style={style}>\n        {new Array(numberOfColumns).fill().map((_, columnIndex) => {\n          const thumbIndex = index * numberOfColumns + columnIndex;\n          const allowDragAndDrop = allowPageOperationsUI && (isThumbnailMergingEnabled || isThumbnailReorderingEnabled);\n          const showPlaceHolder = allowDragAndDrop && draggingOverPageIndex === thumbIndex;\n\n          return thumbIndex < pageCount ? (\n            <React.Fragment key={thumbIndex}>\n              {(numberOfColumns > 1 || thumbIndex === 0) && showPlaceHolder && isDraggingToPreviousPage && <div key={`placeholder1-${thumbIndex}`} className=\"thumbnailPlaceholder\" />}\n              <td\n                ref={(el) => (thumbnailContainerRefs.current[thumbIndex] = el)}\n                key={thumbIndex}\n                role=\"gridcell\"\n                tabIndex={focusedIndex === thumbIndex ? 0 : -1}\n                aria-current={focusedIndex === thumbIndex ? 'page' : undefined}\n                onDragEnd={onDragEnd}\n                className=\"cellThumbContainer\"\n                onKeyDown={(e) => handleKeyDown(e, thumbIndex, numberOfColumns)}\n                onContextMenu={(e) => isRightClickEnabled && onRightClick(e, thumbIndex)}\n                onFocus={(e) => handleFocus(e, thumbIndex)}\n              >\n                <Thumbnail\n                  ref={(el) => (thumbnailRefs.current[thumbIndex] = el)}\n                  isDraggable={allowDragAndDrop}\n                  isSelected={selectedPageIndexes.includes(thumbIndex)}\n                  index={thumbIndex}\n                  canLoad={canLoad}\n                  onLoad={onLoad}\n                  onCancel={onCancel}\n                  onRemove={onRemove}\n                  onDragStart={onDragStart}\n                  onDragOver={onDragOver}\n                  onFinishLoading={removeFromPendingThumbs}\n                  updateAnnotations={updateAnnotations}\n                  shouldShowControls={allowPageOperationsUI}\n                  thumbnailSize={thumbnailSize}\n                  panelSelector={panelSelector}\n                  parentKeyListener={(e) => handleThumbnailKeyDown(e, thumbIndex)}\n                />\n              </td>\n              {showPlaceHolder && !isDraggingToPreviousPage && <div key={`placeholder2-${thumbIndex}`} className=\"thumbnailPlaceholder\" />}\n            </React.Fragment>\n          ) : null;\n        })}\n      </div>\n    );\n  };\n\n  const onPanelResize = ({ bounds }) => {\n    setHeight(bounds.height);\n    setWidth(bounds.width);\n    setNumberOfColumns(Math.min(MAX_COLUMNS, Math.max(1, Math.floor(bounds.width / thumbnailSize))));\n  };\n\n  const updateNumberOfColumns = () => {\n    setNumberOfColumns(Math.min(MAX_COLUMNS, Math.max(1, Math.floor(width / thumbnailSize))));\n  };\n\n  const thumbnailHeight = isThumbnailControlDisabled ? Number(thumbnailSize) + 50 : Number(thumbnailSize) + 80;\n  const shouldShowControls = !(isReaderMode || isDocumentReadOnly || isContentEditingEnabled);\n  const thumbnailAutoScrollAreaStyle = {\n    'height': `${hoverAreaHeight}px`,\n  };\n\n  const onSliderChange = (property, value) => {\n    let zoomValue = Number(value) * ZOOM_RANGE_MAX;\n    if (zoomValue < 100) {\n      zoomValue = 100;\n    }\n\n    setThumbnailSize(zoomValue);\n    updateNumberOfColumns();\n  };\n\n  return (\n    <React.Fragment>\n      {!isThumbnailSliderDisabled && <div data-element=\"thumbnailsSizeSlider\" className=\"thumbnail-slider-container\">\n        <Button\n          img=\"icon-zoom-thumb-out\"\n          title=\"action.zoomOut\"\n          hideTooltipShortcut\n          onClick={() => {\n            if (thumbnailSize - ZOOM_RANGE_STEP > ZOOM_RANGE_STEP) {\n              setThumbnailSize(thumbnailSize - ZOOM_RANGE_STEP);\n              updateNumberOfColumns();\n            }\n          }}\n          dataElement=\"zoomThumbOutButton\"\n        />\n        {customizableUI &&\n          <Slider\n            dataElement={'thumbnailsSizeSlider'}\n            property={'zoom'}\n            displayProperty={'zoom'}\n            min={0}\n            max={1}\n            step={0.01}\n            value={thumbnailSize/1000}\n            getDisplayValue={() => thumbnailSize}\n            onSliderChange={onSliderChange}\n            onStyleChange={onSliderChange}\n            shouldHideSliderTitle={true}\n            shouldHideSliderValue={true}\n          />\n        }\n        {!customizableUI &&\n          <input\n            role='slider'\n            type=\"range\"\n            aria-label='thumbnail size slider'\n            min={ZOOM_RANGE_MIN}\n            max={ZOOM_RANGE_MAX}\n            value={thumbnailSize}\n            aria-valuemin={ZOOM_RANGE_MIN}\n            aria-valuemax={ZOOM_RANGE_MAX}\n            aria-valuenow={thumbnailSize}\n            onChange={(e) => {\n              setThumbnailSize(Number(e.target.value));\n              updateNumberOfColumns();\n            }}\n            step={ZOOM_RANGE_STEP}\n            className=\"thumbnail-slider\"\n            id=\"thumbnailSize\"\n          />\n        }\n        <Button\n          img=\"icon-zoom-thumb-in\"\n          title=\"action.zoomIn\"\n          hideTooltipShortcut\n          onClick={() => {\n            if (thumbnailSize + Number(ZOOM_RANGE_STEP) < 1001) {\n              setThumbnailSize(thumbnailSize + Number(ZOOM_RANGE_STEP));\n              updateNumberOfColumns();\n            }\n          }}\n          dataElement=\"zoomThumbInButton\"\n        />\n      </div>}\n      <Measure bounds onResize={onPanelResize} key={thumbnailSize}>\n        {({ measureRef }) => (\n          <div className={`Panel ThumbnailsPanel ${panelSelector}`} id=\"virtualized-thumbnails-container\" data-element=\"thumbnailsPanel\" onDrop={onDrop} ref={measureRef}>\n            <div className=\"virtualized-thumbnails-container\">\n              {isDragging ?\n                <div className=\"thumbnailAutoScrollArea\" onDragOver={scrollUp} style={thumbnailAutoScrollAreaStyle}></div> : ''\n              }\n              <List\n                ref={listRef}\n                height={height}\n                width={width}\n                rowHeight={thumbnailHeight}\n                // Round it to a whole number because React-Virtualized list library doesn't round it for us and throws errors when rendering non whole number rows\n                // use ceiling rather than floor so that an extra row can be created in case the items can't be evenly distributed between rows\n                rowCount={Math.ceil(pageCount / numberOfColumns)}\n                rowRenderer={renderThumbnails}\n                overscanRowCount={3}\n                className={'thumbnailsList'}\n                // Ensure we show the current page in the thumbnails when we open the panel\n                scrollToIndex={Math.floor((currentPage - 1) / numberOfColumns)}\n                role='grid'\n                aria-label={t('component.thumbnailsPanel')}\n                tabIndex={-1}\n              />\n              {isDragging ?\n                <div className=\"thumbnailAutoScrollArea\" onDragOver={scrollDown} style={{ ...thumbnailAutoScrollAreaStyle, 'bottom': '70px' }}></div> : ''\n              }\n            </div>\n          </div>\n        )}\n      </Measure>\n      <DocumentControls shouldShowControls={shouldShowControls} parentElement={parentDataElement || panelSelector} />\n    </React.Fragment>\n  );\n};\n\nexport default ThumbnailsPanel;\n", "import ThumbnailsPanel from './ThumbnailsPanel';\n\nexport default ThumbnailsPanel;\n"], "sourceRoot": ""}