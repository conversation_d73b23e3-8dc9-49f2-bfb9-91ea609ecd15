{"version": 3, "sources": ["webpack:///./src/ui/src/components/Element/Element.scss?5aab", "webpack:///./src/ui/src/components/Element/Element.scss", "webpack:///./src/ui/src/components/Element/Element.js", "webpack:///./src/ui/src/components/Element/index.js", "webpack:///./src/ui/src/components/MathSymbolsPicker/MathSymbolsPicker.scss?6324", "webpack:///./src/ui/src/components/MathSymbolsPicker/MathSymbolsPicker.scss", "webpack:///./src/ui/src/components/RichTextPopup/RichTextPopup.scss?11c1", "webpack:///./src/ui/src/components/RichTextPopup/RichTextPopup.scss", "webpack:///./src/ui/src/helpers/getRichTextPopupPosition.js", "webpack:///./src/ui/src/components/MathSymbolsPicker/MathSymbolsPicker.js", "webpack:///./src/ui/src/components/MathSymbolsPicker/index.js", "webpack:///./src/ui/src/components/RichTextPopup/RichTextPopup.js", "webpack:///./src/ui/src/components/RichTextPopup/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "Element", "this", "props", "isDisabled", "className", "dataElement", "children", "data-element", "React", "PureComponent", "PropTypes", "bool", "string", "isRequired", "node", "connect", "state", "ownProps", "selectors", "isElementDisabled", "annotation", "popup", "current", "<PERSON><PERSON><PERSON><PERSON>", "getRootNode", "querySelector", "PageNumber", "Id", "scrollContainer", "core", "getScrollViewElement", "padding", "parseFloat", "StrokeThickness", "getZoom", "cBox", "getBoundingClientRect", "shadowTop", "shadowLeft", "shadowRect", "host", "top", "left", "cInfo", "topLeft", "x", "scrollLeft", "y", "scrollTop", "bottomRight", "right", "bottom", "pBox", "calcPopupLeft", "calcPopupTop", "symbols", "MathSymbolsPicker", "onClickHandler", "maxHeight", "style", "map", "symbol", "key", "onClick", "propTypes", "object", "editor", "RichTextPopup", "useSelector", "DataElements", "RICH_TEXT_POPUP", "isElementOpen", "getCustomColors", "isInDesktopOnlyMode", "STYLE_POPUP_TEXT_STYLE_CONTAINER", "STYLE_POPUP_COLORS_CONTAINER", "getFonts", "LEGACY_RICH_TEXT_POPUP", "shallowEqual", "isOpen", "isPaletteDisabled", "customColors", "isTextStylePickerOpen", "isColorPickerOpen", "fonts", "legacyPopup", "useState", "symbolsVisible", "setSymbolsVisible", "cssPosition", "setCssPosition", "draggablePosition", "setDraggablePosition", "format", "setFormat", "popupRef", "useRef", "editor<PERSON><PERSON>", "annotationRef", "propertiesRef", "dispatch", "useDispatch", "oldSelectionRef", "isAutoSizeFont", "setAutoSizeFont", "useEffect", "actions", "disableElements", "ANNOTATION_STYLE_POPUP", "closeElement", "ANNOTATION_POPUP", "enableElements", "handleSelectionChange", "range", "oldRange", "setSelection", "index", "getFormat", "handleTextChange", "isAutoSized", "position", "getRichTextPopupPosition", "getSelection", "addEventListener", "removeEventListener", "StrokeStyle", "err", "console", "error", "stylesTemp", "getRichTextStyle", "Font", "FontSize", "TextAlign", "TextVerticalAlign", "bold", "italic", "underline", "includes", "strikeout", "size", "font", "calculatedFontSize", "getCalculatedFontSize", "handleEditorBlur", "closeElements", "setPopupPosition", "useLayoutEffect", "setPosition", "debounce", "scrollViewElement", "getDocumentViewer", "color", "Core", "Annotations", "Color", "Array", "isArray", "lastSelectedColor", "TextColor", "handleTextFormatChange", "newSelection", "currentFormat", "applyFormat", "handleColorChange", "_", "toHexString", "formatKey", "value", "isFullPDFEnabled", "updated", "syncDraggablePosition", "e", "menuItems", "toggleMenuItem", "openElement", "openTextStyle", "openColors", "strike", "quillFont", "quillFontSize", "originalSize", "isMobile", "onDrag", "onStop", "enableUserSelectHack", "cancel", "onMouseDown", "type", "preventDefault", "classNames", "Popup", "open", "closed", "legacy", "ref", "<PERSON><PERSON>", "isActive", "img", "title", "HorizontalDivider", "paddingTop", "onTouchStart", "role", "i18next", "t", "Icon", "glyph", "TextStylePicker", "onPropertyChange", "property", "blur", "adjustFreeTextBoundingBox", "setTimeout", "getAnnotationManager", "getEditBoxManager", "focusBox", "onRichTextStyleChange", "propertyTranslation", "resizeAnnotation", "properties", "stateless", "isFreeText", "onFreeTextSizeToggle", "handleFreeTextAutoSizeToggle", "isFreeTextAutoSize", "isRichTextEditMode", "ColorPalette", "colorMapKey", "onStyleChange", "hasPadding", "ColorPalettePicker", "enableEdit", "deleteText", "insertText", "memo"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,GAAI,M,27DCCL,I,MAElB4B,EAAO,a,qRAAA,U,MAAA,6DAoBV,O,EApBU,G,EAAA,qBAQX,WACE,MAAyDC,KAAKC,MAAtDC,EAAU,EAAVA,WAAYC,EAAS,EAATA,UAAWC,EAAW,EAAXA,YAAaC,EAAQ,EAARA,SAE5C,OAAIH,EACK,KAIP,yBAAKC,UAAWA,EAAWG,eAAcF,GACtCC,Q,8EAGN,EApBU,CAASE,IAAMC,e,EAAtBT,E,EAAO,Y,EACQ,CACjBG,WAAYO,IAAUC,KACtBP,UAAWM,IAAUE,OAAOC,WAC5BR,YAAaK,IAAUE,OAAOC,WAC9BP,SAAUI,IAAUI,O,kGAkBxB,IAIeC,eAJS,SAACC,EAAOC,GAAQ,MAAM,CAC5Cd,WAAYe,IAAUC,kBAAkBH,EAAOC,EAASZ,gBAG3CU,CAAyBf,GCjCzBA,O,qBCFf,IAAIjC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,q+EAAs+E,KAG//E0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,48HAA68H,KAGt+H0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,2YCNR,WAACqB,EAAYC,GAC1B,GAAKA,GAAUA,EAAMC,QAArB,CAGA,IAAMC,EAAkBC,cAAcC,cAAc,uBAAD,OAC1BL,EAAWM,WAAU,iCAAyBN,EAAWO,GAAE,OAGpF,GAAKJ,EAAL,CAGA,IAAMK,EAAkBC,IAAKC,uBACvBC,EAAU,EAAIC,WAAWZ,EAAWa,iBAAmBJ,IAAKK,UAC5DC,EAAOZ,EAAgBa,wBACzBC,EAAY,EACZC,EAAa,EACjB,GAAI/D,OAAOC,8BAA+B,OAClC+D,EAA+B,QAArB,EAAGf,cAAcgB,YAAI,aAAlB,EAAoBJ,wBACvCC,EAAYE,EAAWE,IACvBH,EAAaC,EAAWG,KAE1B,IAAMC,EAAQ,CACZC,QAAS,CACPC,EAAGV,EAAKO,KAAOd,EAAgBkB,WAAaf,EAC5CgB,EAAGZ,EAAKM,IAAMb,EAAgBoB,UAAYjB,GAE5CkB,YAAa,CACXJ,EAAGV,EAAKe,MAAQtB,EAAgBkB,WAAaf,EAC7CgB,EAAGZ,EAAKgB,OAASvB,EAAgBoB,UAAYjB,IAG3CqB,EAAO/B,EAAMC,QAAQc,wBAC3B,MAAO,CACLM,KAAMW,YAAcV,EAAOS,GAAQd,EACnCG,IAAKa,YAAaX,EAAOS,GAAQf,M,UClC/BkB,G,cAAU,CACd,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,MClGaC,EDqGW,SAAH,GAAsC,IAAhCC,EAAc,EAAdA,eAAgBC,EAAS,EAATA,UAC3C,OACE,yBACEtD,UAAU,+BACVuD,MAAO,CAAED,UAAW,GAAF,OAAKA,EAAS,QAE/BH,EAAQK,KAAI,SAACC,EAAQzF,GAAC,OACrB,4BACE0F,IAAK1F,EACLgC,UAAU,iBACV2D,QAAS,WACPN,EAAeI,KAGhBA,Q,60EE1FX,IAAMG,EAAY,CAChB5C,WAAYV,IAAUuD,OACtBC,OAAQxD,IAAUuD,QAGdE,EAAgB,SAAH,GAA+B,MAAzB/C,EAAU,EAAVA,WAAY8C,EAAM,EAANA,OAwBlC,IAbGE,aACF,SAACpD,GAAK,MAAK,CACTE,IAAUC,kBAAkBH,EAAOqD,IAAaC,iBAChDpD,IAAUqD,cAAcvD,EAAOqD,IAAaC,iBAC5CpD,IAAUC,kBAAkBH,EAAO,gBACnCE,IAAUsD,gBAAgBxD,EAAO,gBACjCE,IAAUuD,oBAAoBzD,GAC9BE,IAAUqD,cAAcvD,EAAOqD,IAAaK,kCAC5CxD,IAAUqD,cAAcvD,EAAOqD,IAAaM,8BAC5CzD,IAAU0D,SAAS5D,IAClBE,IAAUC,kBAAkBH,EAAOqD,IAAaQ,2BAEnDC,KACD,GAtBC3E,EAAU,KACV4E,EAAM,KACNC,EAAiB,KACjBC,EAAY,KACZR,EAAmB,KACnBS,EAAqB,KACrBC,EAAiB,KACjBC,EAAK,KACLC,EAAW,KAe8C,IAAfC,oBAAS,GAAM,GAApDC,EAAc,KAAEC,EAAiB,KAC2B,IAA7BF,mBAAS,CAAE5C,KAAM,EAAGD,IAAK,IAAI,GAA5DgD,EAAW,KAAEC,EAAc,KACwC,IAAxBJ,mBAAS,CAAEzC,EAAG,EAAGE,EAAG,IAAI,GAAnE4C,EAAiB,KAAEC,EAAoB,KACN,IAAZN,mBAAS,IAAG,GAAjCO,EAAM,KAAEC,EAAS,KAClBC,EAAWC,iBAAO,MAClBC,EAAYD,iBAAO,MACnBE,GAAgBF,iBAAO,MACvBG,GAAgBH,iBAAO,IACvBI,GAAWC,cACXC,GAAkBN,mBAEuD,KAArCV,mBAASlE,EAAWmF,kBAAiB,GAAxEA,GAAc,MAAEC,GAAe,MAEtCC,qBAAU,WAIR,OAFAL,GAASM,IAAQC,gBAAgB,CAACtC,IAAauC,0BAC/CR,GAASM,IAAQG,aAAaxC,IAAayC,mBACpC,WACLV,GAASM,IAAQK,eAAe,CAAC1C,IAAauC,6BAE/C,IAEHH,qBAAU,WACR,IAAMO,EAAwB,SAACC,EAAOC,IACAD,GAASC,GAAYjB,EAAU3E,SAEjE2E,EAAU3E,QAAQ6F,aAAaD,EAASE,MAAOF,EAASpI,QAEtDmI,GAAShB,EAAU3E,SACrBwE,EAAUuB,GAAUJ,KAGlBK,EAAmB,WAAM,QAC7B,GAAyB,QAArB,EAAApB,GAAc5E,eAAO,OAArB,EAAuBiG,eAAiBxB,EAASzE,QAAS,CAC5D,IAAMkG,EAAWC,EACfvB,GAAc5E,QACdyE,GAEFL,EAAe8B,GAGjB1B,EAAUuB,GAA2B,QAAlB,EAACpB,EAAU3E,eAAO,aAAjB,EAAmBoG,kBAIzC,OAFA7F,IAAK8F,iBAAiB,yBAA0BX,GAChDnF,IAAK8F,iBAAiB,oBAAqBL,GACpC,WACLzF,IAAK+F,oBAAoB,yBAA0BZ,GACnDnF,IAAK+F,oBAAoB,oBAAqBN,MAE/C,IAEHb,qBAAU,WAAM,kBAGdb,EAAqB,CAAE/C,EAAG,EAAGE,EAAG,IAEhCkD,EAAU3E,QAAU4C,EACpBgC,GAAc5E,QAAUF,EACxB,IAAIyG,EAAc,QAClB,IACEA,EAAuC,SAAxBzG,EAAkB,MAAY,UACtCA,EAAkB,MAAC,YAAIA,EAAmB,QAC7CA,EAAkB,MACtB,MAAO0G,GACPC,QAAQC,MAAMF,GAEhB,IACMG,EADiB7G,EAAW8G,mBACA,GAClC/B,GAAc7E,QAAU,CACtB6G,KAAM/G,EAAW+G,KACjBC,SAAUhH,EAAWgH,SACrBC,UAAWjH,EAAWiH,UACtBC,kBAAmBlH,EAAWkH,kBAC9BC,KAA4C,QAAxC,EAAkC,UAAhCN,aAAU,EAAVA,EAAa,uBAAyB,SAC5CO,OAA+C,QAAzC,EAAiC,YAA/BP,aAAU,EAAVA,EAAa,sBAA0B,SAC/CQ,WAAWR,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCS,SAAS,gBAAgBT,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCS,SAAS,SAC/GC,UAAoE,QAA3D,EAAEV,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCS,SAAS,uBAAe,SACpEE,KAAMX,aAAU,EAAVA,EAAa,aACnBY,KAAMZ,aAAU,EAAVA,EAAa,eACnBJ,cACAiB,mBAAoB1H,EAAW2H,yBAGjCjD,EAAUuB,GAA2B,QAAlB,EAACpB,EAAU3E,eAAO,aAAjB,EAAmBoG,iBAEnCpB,GAAgBhF,UAClB2E,EAAU3E,QAAQ6F,aAAab,GAAgBhF,SAC/CgF,GAAgBhF,QAAU,QAE3B,CAACF,EAAY8C,IAEhBuC,qBAAU,WACR,IAAMuC,EAAmB,WACvB5C,GAASM,IAAQuC,cAAc,CAAC5E,IAAaC,mBAC7C2B,EAAU3E,QAAU,KACpB4E,GAAc5E,QAAU,MAG1B,OADAO,IAAK8F,iBAAiB,aAAcqB,GAC7B,WACLnH,IAAK+F,oBAAoB,aAAcoB,MAExC,CAAC5C,KAEJ,IAAM8C,GAAmB,WACvB,GAAInD,EAASzE,QAAS,CACpB,IAAMkG,EAAWC,EACfrG,EACA2E,GAEFL,EAAe8B,KAInB2B,2BAAgB,WACdD,OACC,CAAC9H,IAGJ+H,2BAAgB,WACd,IAAMC,EAAcC,KAAS,WACvBtD,EAASzE,SACX4H,OAED,KAEGI,EAAoBzH,IAAK0H,oBAAoBzH,uBAGnD,OAFAwH,WAAmB3B,iBAAiB,SAAUyB,GAEvC,kBAAME,aAAiB,EAAjBA,EAAmB1B,oBAAoB,SAAUwB,MAG7D,CAAChI,EAAYmE,IAEhB,IAAM8B,GAAY,SAACJ,GACjB,IAAKA,EACH,MAAO,GAGT,IAAMpB,EAASI,EAAU3E,QAAQ+F,UAAUJ,EAAMG,MAAOH,EAAMnI,QAE9D,GAA4B,iBAAjB+G,EAAO2D,MAChB3D,EAAO2D,MAAQ,IAAIjL,OAAOkL,KAAKC,YAAYC,MAAM9D,EAAO2D,YACnD,GAAII,MAAMC,QAAQhE,EAAO2D,OAAQ,CAEtC,IAAMM,EAAoB,IAAIvL,OAAOkL,KAAKC,YAAYC,MAAM9D,EAAO2D,MAAM3D,EAAO2D,MAAM1K,OAAS,IAC/F+G,EAAO2D,MAAQM,OACLjE,EAAO2D,QACjB3D,EAAO2D,MAAQtD,GAAc5E,QAAQyI,WAGvC,OAAOlE,GAGHmE,GAAyB,SAACnE,GAAM,OAAK,WACzC,MAAwBI,EAAU3E,QAAQoG,eAApCN,EAAK,EAALA,MAAOtI,EAAM,EAANA,OACb,GAAe,IAAXA,EAAc,CAChBwH,GAAgBhF,QAAU,CAAE8F,QAAOtI,UACnC,IAAMmL,EAAehE,EAAU3E,QAAQoG,eACvCN,EAAQ6C,EAAa7C,MACrBtI,EAASmL,EAAanL,OAGxB,IAAMoL,EAAgBjE,EAAU3E,QAAQ+F,UAAUD,EAAOtI,GACzDqL,GAAYtE,GAASqE,EAAcrE,MAO/BuE,GAAoB,SAACC,EAAGb,GAC5BW,GAAY,QAASX,EAAMc,gBAGvBH,GAAc,SAACI,EAAWC,GACJ,MAEnB,EAFW,SAAdD,EACe,QAAjB,EAAAtE,EAAU3E,eAAO,OAAjB,EAAmBuE,OAAO,sBAAuB2E,GAEhC,QAAjB,EAAAvE,EAAU3E,eAAO,OAAjB,EAAmBuE,OAAO0E,EAAWC,GAOvC,GAJkB,UAAdD,IACFC,EAAQ,IAAIjM,OAAOkL,KAAKC,YAAYC,MAAMa,IAGxC3I,IAAK4I,oBAAoC,SAAdF,EAAsB,CACnD,IAAMG,EAAU,EAAH,KACR7E,GAAM,IACT,uBAAwB2E,IAE1B,OAAO1E,EAAU4E,GAGnB5E,EAAU,EAAD,KACJD,GAAM,QACR0E,EAAYC,MAIXG,GAAwB,SAACC,EAAG,GAAa,IAAX/H,EAAC,EAADA,EAAGE,EAAC,EAADA,EACrC6C,EAAqB,CAAE/C,IAAGE,OActB8H,IAAS,OACZxG,IAAaK,iCAAmCQ,GAAqB,IACrEb,IAAaM,6BAA+BQ,GAAiB,GAE1D2F,GAAiB,SAACzK,GAIpB+F,GAHGyE,GAAUxK,GAGJqG,IAAQG,aAAaxG,GAFrBqG,IAAQqE,YAAY1K,KAK3B2K,GAAgB,WAAH,OAASF,GAAezG,IAAaK,mCAClDuG,GAAa,WAAH,OAASH,GAAezG,IAAaM,+BA+CrD,OARAwB,GAAc7E,QAAQiH,KAAO1C,EAAO0C,KACpCpC,GAAc7E,QAAQkH,OAAS3C,EAAO2C,OACtCrC,GAAc7E,QAAQmH,UAAY5C,EAAO4C,UACzCtC,GAAc7E,QAAQqH,UAAY9C,EAAOqF,OACzC/E,GAAc7E,QAAQ6J,UAAYtF,EAAO,yBAA2BA,EAAOgD,MAAQ1C,GAAc7E,QAAQ6G,KACzGhC,GAAc7E,QAAQ8J,cAAgBvF,EAAOwF,cAAgBlF,GAAc7E,QAAQ8G,SAG5EjI,GAAemL,gBAAe7G,EAAuB,KAC1D,kBAAC,IAAS,CACR+C,SAAU7B,EACV4F,OAAQZ,GACRa,OAAQb,GACRc,sBAAsB,EAEtBC,OAAO,oEAGPC,YAAa,SAACf,GACG,eAAXA,EAAEgB,MACJhB,EAAEiB,mBAIN,yBACEzL,UAAW0L,IAAW,CACpBC,OAAO,EACP5H,eAAe,EACf6H,KAAMjH,EACNkH,QAASlH,EACTmH,OAAQ7G,IAEV8G,IAAKpG,EACLxF,eAAc8D,IAAaC,gBAC3BX,MAAK,KAAO8B,IAGVJ,EAAe,oCACb,kBAACrF,EAAA,EAAO,CAACI,UAAU,0BAA0BC,YAAY,mBACvD,kBAAC+L,EAAA,EAAM,CACLC,SAAUxG,EAAO0C,KACjBlI,YAAY,qBACZ0D,QAASiG,GAAuB,QAChCsC,IAAI,iBACJC,MAAM,yBAER,kBAACH,EAAA,EAAM,CACLC,SAAUxG,EAAO2C,OACjBnI,YAAY,uBACZ0D,QAASiG,GAAuB,UAChCsC,IAAI,mBACJC,MAAM,2BAER,kBAACH,EAAA,EAAM,CACLC,SAAUxG,EAAO4C,UACjBpI,YAAY,0BACZ0D,QAASiG,GAAuB,aAChCsC,IAAI,qCACJC,MAAM,8BAER,kBAACH,EAAA,EAAM,CACLC,SAAUxG,EAAOqF,OACjB7K,YAAY,uBACZ0D,QAASiG,GAAuB,UAChCsC,IAAI,qCACJC,MAAM,8BAER,kBAACH,EAAA,EAAM,CACL/L,YAAY,oBACZ0D,QAxKa,WACzByB,GAAmBD,IAwKP+G,IAAI,gCACJC,MAAM,wBAGV,kBAACC,EAAA,EAAiB,CAAC7I,MAAO,CAAE8I,WAAY,MAEvC,oCACC,yBAAKrM,UAAU,mBAAmB2D,QAASiH,GAAe0B,aAAc1B,GAAe2B,KAAM,WAC3F,yBAAKvM,UAAU,cACZwM,IAAQC,EAAE,gCAEb,kBAACC,EAAA,EAAI,CAACC,MAAK,uBAAkB7H,EAAwB,KAAO,WAE7DA,GACC,yBAAK9E,UAAU,cACb,kBAAC4M,EAAA,EAAe,CACd5H,MAAOA,EACP6H,iBA5HO,SAACC,EAAU1C,GAClC,MAA0BvE,EAAU3E,QAAQoG,eAApCN,EAAK,EAALA,MAAOtI,EAAM,EAANA,OACTsC,EAAa8E,GAAc5E,QACjCF,EAAW8L,GAAY1C,EACvBvE,EAAU3E,QAAQ6L,OACD,aAAbD,GAAwC,SAAbA,GAC7BE,YAA0BhM,GAE5BiM,YAAW,WACT/G,GAAgBhF,QAAU,CAAE8F,QAAOtI,UACZ+C,IAAKyL,uBAAuBC,oBACpCC,SAASpM,KACvB,IAiHaqM,sBA9GY,SAACP,EAAU1C,GACvC,IAAMkD,EAAsB,CAC1B,cAAe,OACf,aAAc,SACd,UAAa,YACb,eAAgB,SAChB,cAAe,OACf,YAAa,QAEf,GAAiB,gBAAbR,GAA2C,cAAbA,EAA0B,CAC1D/C,GAAYuD,EAAoBR,GAAW1C,GAC3C,IAAMpJ,EAAa8E,GAAc5E,QACjC,GAAIF,EAAWmG,cACU1F,IAAKyL,uBAAuBC,oBACpCI,iBAAiBvM,QAGlC4I,GAAuB0D,EAAoBR,GAA3ClD,IA8Fc4D,WAAYzH,GAAc7E,QAC1BuM,WAAW,EACXC,YAAY,EACZC,qBAAsB,kBAAMC,YAA6B5M,EAAYoF,GAAiBD,KACtF0H,mBAAoB1H,GACpB2H,oBAAoB,KAI1B,yBAAK9N,UAAU,aACb4E,GACA,yBAAK5E,UAAU,mBAAmB2D,QAASkH,GAAYyB,aAAczB,GAAY0B,KAAM,WACrF,yBAAKvM,UAAU,cACZwM,IAAQC,EAAE,6BAEb,kBAACC,EAAA,EAAI,CAACC,MAAK,uBAAkB5H,EAAoB,KAAO,aAKhEH,IAAsBK,GAAeF,IACrC,oCACE,kBAACgJ,EAAA,EAAY,CACXC,YAAY,WACZ5E,MAAO3D,EAAO2D,MACd0D,SAAS,YACTmB,cAAejE,GACfkE,YAAU,IAEXrJ,EAAanG,OAAS,GACrB,kBAACyP,EAAA,EAAkB,CACjB/E,MAAO3D,EAAO2D,MACd0D,SAAS,YACTmB,cAAejE,GACfoE,YAAY,KAKnBjJ,GAAkB,kBAAC,EAAiB,CAAC9B,eA9LtB,SAACI,GACrB,MAA0BoC,EAAU3E,QAAQoG,eAApCN,EAAK,EAALA,MAAOtI,EAAM,EAANA,OAEXA,EAAS,GACXmH,EAAU3E,QAAQmN,WAAWrH,EAAOtI,GAGtCmH,EAAU3E,QAAQoN,WAAWtH,EAAOvD,GACpCoC,EAAU3E,QAAQ6F,aAAaC,EAAQ,IAsLkC1D,UA/XjD,SAqY5BS,EAAcH,UAAYA,EAEXxD,UAAMmO,KAAKxK,GCxcXA", "file": "chunks/chunk.46.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Element.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \"\", \"\"]);\n\n// exports\n", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { connect } from 'react-redux';\n\nimport selectors from 'selectors';\n\nimport './Element.scss';\n\nclass Element extends React.PureComponent {\n  static propTypes = {\n    isDisabled: PropTypes.bool,\n    className: PropTypes.string.isRequired,\n    dataElement: PropTypes.string.isRequired,\n    children: PropTypes.node,\n  };\n\n  render() {\n    const { isDisabled, className, dataElement, children } = this.props;\n\n    if (isDisabled) {\n      return null;\n    }\n\n    return (\n      <div className={className} data-element={dataElement}>\n        {children}\n      </div>\n    );\n  }\n}\n\nconst mapStateToProps = (state, ownProps) => ({\n  isDisabled: selectors.isElementDisabled(state, ownProps.dataElement),\n});\n\nexport default connect(mapStateToProps)(Element);", "import Element from './Element';\n\nexport default Element;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./MathSymbolsPicker.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.mathSymbolsContainer{display:flex;flex-wrap:wrap;overflow-y:auto;display:grid;grid-template-columns:repeat(7,1fr);margin-bottom:5px}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.mathSymbolsContainer{width:196px}}.mathSymbolsContainer.padding{padding:4px 20px 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .mathSymbolsContainer{max-width:450px;width:auto}}@media(max-width:640px)and (-ms-high-contrast:active),(max-width:640px)and (-ms-high-contrast:none){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .mathSymbolsContainer{width:308px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .mathSymbolsContainer{max-width:450px;width:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.App.is-web-component:not(.is-in-desktop-only-mode) .mathSymbolsContainer{width:308px}}}.mathSymbolsContainer .cell-container{padding:0;border:none;background-color:transparent;flex:1 0 14%;cursor:pointer;border-radius:4px;width:28px;height:28px;display:flex;align-items:center;justify-content:center}:host(:not([data-tabbing=true])) .mathSymbolsContainer .cell-container,html:not([data-tabbing=true]) .mathSymbolsContainer .cell-container{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .mathSymbolsContainer .cell-container{width:44px;height:44px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .mathSymbolsContainer .cell-container{width:44px;height:44px}}.mathSymbolsContainer .cell-container:hover{background:var(--popup-button-hover)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RichTextPopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.RichTextPopup{visibility:visible}.closed.RichTextPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.RichTextPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.RichTextPopup:empty{padding:0}.RichTextPopup .buttons{display:flex}.RichTextPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RichTextPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RichTextPopup .Button{width:42px;height:42px}}.RichTextPopup .Button:hover{background:var(--popup-button-hover)}.RichTextPopup .Button:hover:disabled{background:none}.RichTextPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RichTextPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RichTextPopup .Button .Icon{width:24px;height:24px}}.is-vertical.RichTextPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.RichTextPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.RichTextPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.RichTextPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.RichTextPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.RichTextPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.RichTextPopup{overflow:visible;flex-direction:column;background:var(--component-background);border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);width:220px;padding:6px 12px;align-items:flex-start}.RichTextPopup #FontSizeDropdown.Dropdown__wrapper{width:80px}.RichTextPopup #FontSizeDropdown.Dropdown__wrapper .Dropdown,.RichTextPopup #FontSizeDropdown.Dropdown__wrapper .Dropdown__items{width:80px!important}.RichTextPopup.legacy{padding:0;width:auto;align-items:center}.RichTextPopup.legacy .ColorPalette.padding{padding:4px 12px 8px}.RichTextPopup.legacy .colorPicker{padding:0 12px 8px}.RichTextPopup .rich-text-format-legacy{display:flex;width:100%;justify-content:center}.RichTextPopup .ColorPalette.padding{padding:0}.RichTextPopup .menu-items{width:100%;padding:6px 4px}.RichTextPopup .collapsible-menu{width:100%;display:flex;cursor:pointer;align-items:center;justify-content:space-between}.RichTextPopup .collapsible-menu .menu-title{padding-left:4px;padding-top:6px;padding-bottom:6px;font-weight:700;font-size:14px}.RichTextPopup .collapsible-menu .Icon{height:18px;width:18px}.RichTextPopup .divider{background-color:var(--divider);width:100%;height:1px;margin:6px 0}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import { calcPopupLeft, calcPopupTop } from 'helpers/getPopupPosition';\r\nimport core from 'core';\r\nimport getRootNode from 'helpers/getRootNode';\r\n\r\nexport default (annotation, popup) => {\r\n  if (!popup || !popup.current) {\r\n    return;\r\n  }\r\n  const editorContainer = getRootNode().querySelector(\r\n    `#pageWidgetContainer${annotation.PageNumber} [id=\"freetext-editor-${annotation.Id}\"]`\r\n  );\r\n\r\n  if (!editorContainer) {\r\n    return;\r\n  }\r\n  const scrollContainer = core.getScrollViewElement();\r\n  const padding = 2 * parseFloat(annotation.StrokeThickness) * core.getZoom();\r\n  const cBox = editorContainer.getBoundingClientRect();\r\n  let shadowTop = 0;\r\n  let shadowLeft = 0;\r\n  if (window.isApryseWebViewerWebComponent) {\r\n    const shadowRect = getRootNode().host?.getBoundingClientRect();\r\n    shadowTop = shadowRect.top;\r\n    shadowLeft = shadowRect.left;\r\n  }\r\n  const cInfo = {\r\n    topLeft: {\r\n      x: cBox.left + scrollContainer.scrollLeft - padding,\r\n      y: cBox.top + scrollContainer.scrollTop - padding\r\n    },\r\n    bottomRight: {\r\n      x: cBox.right + scrollContainer.scrollLeft + padding,\r\n      y: cBox.bottom + scrollContainer.scrollTop + padding\r\n    }\r\n  };\r\n  const pBox = popup.current.getBoundingClientRect();\r\n  return {\r\n    left: calcPopupLeft(cInfo, pBox) - shadowLeft,\r\n    top: calcPopupTop(cInfo, pBox) - shadowTop,\r\n  };\r\n};\r\n", "import React from 'react';\n\nimport './MathSymbolsPicker.scss';\n\nconst symbols = [\n  '\\u002B',\n  '\\u2212',\n  '\\u00D7',\n  '\\u00F7',\n  '\\u003D',\n  '\\u2260',\n  '\\u00B1',\n  '\\u00AC',\n  '\\u003C',\n  '\\u003E',\n  '\\u22DC',\n  '\\u22DD',\n  '\\u00B0',\n  '\\u00B9',\n  '\\u00B2',\n  '\\u00B3',\n  '\\u0192',\n  '\\u0025',\n  '\\u2030',\n  '\\u2031',\n  '\\u2200',\n  '\\u2201',\n  '\\u2202',\n  '\\u2203',\n  '\\u2204',\n  '\\u2205',\n  '\\u2206',\n  '\\u2207',\n  '\\u2208',\n  '\\u2209',\n  '\\u220A',\n  '\\u220B',\n  '\\u220C',\n  '\\u220D',\n  '\\u220E',\n  '\\u220F',\n  '\\u2210',\n  '\\u2211',\n  '\\u2213',\n  '\\u2214',\n  '\\u2215',\n  '\\u2216',\n  '\\u2217',\n  '\\u2218',\n  '\\u2219',\n  '\\u221A',\n  '\\u221B',\n  '\\u221C',\n  '\\u221D',\n  '\\u221E',\n  '\\u221F',\n  '\\u2220',\n  '\\u2221',\n  '\\u2222',\n  '\\u2223',\n  '\\u2224',\n  '\\u2225',\n  '\\u2226',\n  '\\u2227',\n  '\\u2228',\n  '\\u2229',\n  '\\u222A',\n  '\\u222B',\n  '\\u222C',\n  '\\u222D',\n  '\\u222E',\n  '\\u222F',\n  '\\u2230',\n  '\\u2231',\n  '\\u2232',\n  '\\u2233',\n  '\\u2234',\n  '\\u2235',\n  '\\u2236',\n  '\\u2237',\n  '\\u2238',\n  '\\u2239',\n  '\\u223A',\n  '\\u223B',\n  '\\u223C',\n  '\\u223D',\n  '\\u223E',\n  '\\u223F',\n  '\\u2240',\n  '\\u2241',\n  '\\u2242',\n  '\\u2243',\n  '\\u2244',\n  '\\u2245',\n  '\\u2246',\n  '\\u2247',\n  '\\u2248',\n  '\\u2249',\n  '\\u224A',\n  '\\u224B',\n  '\\u224C'\n];\n\nconst MathSymbolsPicker = ({ onClickHandler, maxHeight }) => {\n  return (\n    <div\n      className=\"mathSymbolsContainer padding\"\n      style={{ maxHeight: `${maxHeight}px` }}\n    >\n      {symbols.map((symbol, i) => (\n        <button\n          key={i}\n          className=\"cell-container\"\n          onClick={() => {\n            onClickHandler(symbol);\n          }}\n        >\n          {symbol}\n        </button>\n      ))}\n    </div>\n  );\n};\n\nexport default MathSymbolsPicker;", "import MathSymbolsPicker from './MathSymbolsPicker';\n\nexport default MathSymbolsPicker;", "import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';\r\nimport Draggable from 'react-draggable';\r\nimport classNames from 'classnames';\r\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\r\nimport debounce from 'lodash/debounce';\r\nimport PropTypes from 'prop-types';\r\nimport Element from 'components/Element';\r\nimport ColorPalette from 'components/ColorPalette';\r\nimport Button from 'components/Button';\r\nimport HorizontalDivider from 'components/HorizontalDivider';\r\nimport { isMobile } from 'helpers/device';\r\nimport core from 'core';\r\nimport getRichTextPopupPosition from 'helpers/getRichTextPopupPosition';\r\nimport adjustFreeTextBoundingBox from 'helpers/adjustFreeTextBoundingBox';\r\nimport MathSymbolsPicker from '../MathSymbolsPicker';\r\nimport ColorPalettePicker from 'components/ColorPalettePicker';\r\n\r\nimport actions from 'actions';\r\nimport selectors from 'selectors';\r\n\r\nimport './RichTextPopup.scss';\r\nimport DataElements from 'constants/dataElement';\r\nimport i18next from 'i18next';\r\nimport Icon from 'components/Icon';\r\nimport TextStylePicker from 'components/TextStylePicker';\r\nimport handleFreeTextAutoSizeToggle from 'src/helpers/handleFreeTextAutoSizeToggle';\r\n\r\nconst propTypes = {\r\n  annotation: PropTypes.object,\r\n  editor: PropTypes.object,\r\n};\r\n\r\nconst RichTextPopup = ({ annotation, editor }) => {\r\n  const [\r\n    isDisabled,\r\n    isOpen,\r\n    isPaletteDisabled,\r\n    customColors,\r\n    isInDesktopOnlyMode,\r\n    isTextStylePickerOpen,\r\n    isColorPickerOpen,\r\n    fonts,\r\n    legacyPopup,\r\n  ] = useSelector(\r\n    (state) => [\r\n      selectors.isElementDisabled(state, DataElements.RICH_TEXT_POPUP),\r\n      selectors.isElementOpen(state, DataElements.RICH_TEXT_POPUP),\r\n      selectors.isElementDisabled(state, 'colorPalette'),\r\n      selectors.getCustomColors(state, 'customColors'),\r\n      selectors.isInDesktopOnlyMode(state),\r\n      selectors.isElementOpen(state, DataElements.STYLE_POPUP_TEXT_STYLE_CONTAINER),\r\n      selectors.isElementOpen(state, DataElements.STYLE_POPUP_COLORS_CONTAINER),\r\n      selectors.getFonts(state),\r\n      !selectors.isElementDisabled(state, DataElements.LEGACY_RICH_TEXT_POPUP),\r\n    ],\r\n    shallowEqual,\r\n  );\r\n  const [symbolsVisible, setSymbolsVisible] = useState(false);\r\n  const [cssPosition, setCssPosition] = useState({ left: 0, top: 0 });\r\n  const [draggablePosition, setDraggablePosition] = useState({ x: 0, y: 0 });\r\n  const [format, setFormat] = useState({});\r\n  const popupRef = useRef(null);\r\n  const editorRef = useRef(null);\r\n  const annotationRef = useRef(null);\r\n  const propertiesRef = useRef({});\r\n  const dispatch = useDispatch();\r\n  const oldSelectionRef = useRef();\r\n  const symbolsAreaHeight = 150; // max height for the math symbols area\r\n  const [isAutoSizeFont, setAutoSizeFont] = useState(annotation.isAutoSizeFont());\r\n\r\n  useEffect(() => {\r\n    // Have to disable instead of closing because annotation popup will reopen itself\r\n    dispatch(actions.disableElements([DataElements.ANNOTATION_STYLE_POPUP]));\r\n    dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\r\n    return () => {\r\n      dispatch(actions.enableElements([DataElements.ANNOTATION_STYLE_POPUP]));\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const handleSelectionChange = (range, oldRange) => {\r\n      const shouldRestoreLostSelection = !range && oldRange && editorRef.current;\r\n      if (shouldRestoreLostSelection) {\r\n        editorRef.current.setSelection(oldRange.index, oldRange.length);\r\n      }\r\n      if (range && editorRef.current) {\r\n        setFormat(getFormat(range));\r\n      }\r\n    };\r\n    const handleTextChange = () => {\r\n      if (annotationRef.current?.isAutoSized() && popupRef.current) {\r\n        const position = getRichTextPopupPosition(\r\n          annotationRef.current,\r\n          popupRef,\r\n        );\r\n        setCssPosition(position);\r\n      }\r\n\r\n      setFormat(getFormat(editorRef.current?.getSelection()));\r\n    };\r\n    core.addEventListener('editorSelectionChanged', handleSelectionChange);\r\n    core.addEventListener('editorTextChanged', handleTextChange);\r\n    return () => {\r\n      core.removeEventListener('editorSelectionChanged', handleSelectionChange);\r\n      core.removeEventListener('editorTextChanged', handleTextChange);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // when the editor is focused, we want to reset any previous drag movements so that\r\n    // the popup will be positioned centered to the editor\r\n    setDraggablePosition({ x: 0, y: 0 });\r\n\r\n    editorRef.current = editor;\r\n    annotationRef.current = annotation;\r\n    let StrokeStyle = 'solid';\r\n    try {\r\n      StrokeStyle = (annotation['Style'] === 'dash')\r\n        ? `${annotation['Style']},${annotation['Dashes']}`\r\n        : annotation['Style'];\r\n    } catch (err) {\r\n      console.error(err);\r\n    }\r\n    const richTextStyles = annotation.getRichTextStyle();\r\n    const stylesTemp = richTextStyles[0];\r\n    propertiesRef.current = {\r\n      Font: annotation.Font,\r\n      FontSize: annotation.FontSize,\r\n      TextAlign: annotation.TextAlign,\r\n      TextVerticalAlign: annotation.TextVerticalAlign,\r\n      bold: stylesTemp?.['font-weight'] === 'bold' ?? false,\r\n      italic: stylesTemp?.['font-style'] === 'italic' ?? false,\r\n      underline: stylesTemp?.['text-decoration']?.includes('underline') || stylesTemp?.['text-decoration']?.includes('word'),\r\n      strikeout: stylesTemp?.['text-decoration']?.includes('line-through') ?? false,\r\n      size: stylesTemp?.['font-size'],\r\n      font: stylesTemp?.['font-family'],\r\n      StrokeStyle,\r\n      calculatedFontSize: annotation.getCalculatedFontSize()\r\n    };\r\n\r\n    setFormat(getFormat(editorRef.current?.getSelection()));\r\n\r\n    if (oldSelectionRef.current) {\r\n      editorRef.current.setSelection(oldSelectionRef.current);\r\n      oldSelectionRef.current = null;\r\n    }\r\n  }, [annotation, editor]);\r\n\r\n  useEffect(() => {\r\n    const handleEditorBlur = () => {\r\n      dispatch(actions.closeElements([DataElements.RICH_TEXT_POPUP]));\r\n      editorRef.current = null;\r\n      annotationRef.current = null;\r\n    };\r\n    core.addEventListener('editorBlur', handleEditorBlur);\r\n    return () => {\r\n      core.removeEventListener('editorBlur', handleEditorBlur);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  const setPopupPosition = () => {\r\n    if (popupRef.current) {\r\n      const position = getRichTextPopupPosition(\r\n        annotation,\r\n        popupRef,\r\n      );\r\n      setCssPosition(position);\r\n    }\r\n  };\r\n\r\n  useLayoutEffect(() => {\r\n    setPopupPosition();\r\n  }, [annotation]);\r\n\r\n  // useLayoutEffect so the popup can adjust position smoothly without flashing\r\n  useLayoutEffect(() => {\r\n    const setPosition = debounce(() => {\r\n      if (popupRef.current) {\r\n        setPopupPosition();\r\n      }\r\n    }, 100);\r\n\r\n    const scrollViewElement = core.getDocumentViewer().getScrollViewElement();\r\n    scrollViewElement?.addEventListener('scroll', setPosition);\r\n\r\n    return () => scrollViewElement?.removeEventListener('scroll', setPosition);\r\n\r\n    // The popup position should be updated when the math symbols are visible or hidden\r\n  }, [annotation, symbolsVisible]);\r\n\r\n  const getFormat = (range) => {\r\n    if (!range) {\r\n      return {};\r\n    }\r\n\r\n    const format = editorRef.current.getFormat(range.index, range.length);\r\n\r\n    if (typeof format.color === 'string') {\r\n      format.color = new window.Core.Annotations.Color(format.color);\r\n    } else if (Array.isArray(format.color)) {\r\n      // the selection contains multiple color, so we set the current color to the last selected color\r\n      const lastSelectedColor = new window.Core.Annotations.Color(format.color[format.color.length - 1]);\r\n      format.color = lastSelectedColor;\r\n    } else if (!format.color) {\r\n      format.color = annotationRef.current.TextColor;\r\n    }\r\n\r\n    return format;\r\n  };\r\n\r\n  const handleTextFormatChange = (format) => () => {\r\n    let { index, length } = editorRef.current.getSelection();\r\n    if (length === 0) {\r\n      oldSelectionRef.current = { index, length };\r\n      const newSelection = editorRef.current.getSelection();\r\n      index = newSelection.index;\r\n      length = newSelection.length;\r\n    }\r\n\r\n    const currentFormat = editorRef.current.getFormat(index, length);\r\n    applyFormat(format, !currentFormat[format]);\r\n  };\r\n\r\n  const handleSymbolsClick = () => {\r\n    setSymbolsVisible(!symbolsVisible);\r\n  };\r\n\r\n  const handleColorChange = (_, color) => {\r\n    applyFormat('color', color.toHexString());\r\n  };\r\n\r\n  const applyFormat = (formatKey, value) => {\r\n    if (formatKey === 'size') {\r\n      editorRef.current?.format('applyCustomFontSize', value);\r\n    } else {\r\n      editorRef.current?.format(formatKey, value);\r\n    }\r\n\r\n    if (formatKey === 'color') {\r\n      value = new window.Core.Annotations.Color(value);\r\n    }\r\n\r\n    if (core.isFullPDFEnabled() && formatKey === 'font') {\r\n      const updated = {\r\n        ...format,\r\n        'original-font-family': value\r\n      };\r\n      return setFormat(updated);\r\n    }\r\n    // format the entire editor doesn't trigger the editorTextChanged event, so we set the format state here\r\n    setFormat({\r\n      ...format,\r\n      [formatKey]: value\r\n    });\r\n  };\r\n\r\n  const syncDraggablePosition = (e, { x, y }) => {\r\n    setDraggablePosition({ x, y });\r\n  };\r\n\r\n  const insertSymbols = (symbol) => {\r\n    const { index, length } = editorRef.current.getSelection();\r\n    // if user selected some text, then we want to first delete the selected content\r\n    if (length > 0) {\r\n      editorRef.current.deleteText(index, length);\r\n    }\r\n    // insert symbol at the selected index\r\n    editorRef.current.insertText(index, symbol);\r\n    editorRef.current.setSelection(index + 1);\r\n  };\r\n\r\n  const menuItems = {\r\n    [DataElements.STYLE_POPUP_TEXT_STYLE_CONTAINER]: isTextStylePickerOpen,\r\n    [DataElements.STYLE_POPUP_COLORS_CONTAINER]: isColorPickerOpen,\r\n  };\r\n  const toggleMenuItem = (dataElement) => {\r\n    if (!menuItems[dataElement]) {\r\n      dispatch(actions.openElement(dataElement));\r\n    } else {\r\n      dispatch(actions.closeElement(dataElement));\r\n    }\r\n  };\r\n  const openTextStyle = () => toggleMenuItem(DataElements.STYLE_POPUP_TEXT_STYLE_CONTAINER);\r\n  const openColors = () => toggleMenuItem(DataElements.STYLE_POPUP_COLORS_CONTAINER);\r\n\r\n  const onPropertyChange = (property, value) => {\r\n    const { index, length } = editorRef.current.getSelection();\r\n    const annotation = annotationRef.current;\r\n    annotation[property] = value;\r\n    editorRef.current.blur();\r\n    if (property === 'FontSize' || property === 'Font') {\r\n      adjustFreeTextBoundingBox(annotation);\r\n    }\r\n    setTimeout(() => {\r\n      oldSelectionRef.current = { index, length };\r\n      const editBoxManager = core.getAnnotationManager().getEditBoxManager();\r\n      editBoxManager.focusBox(annotation);\r\n    }, 0);\r\n  };\r\n\r\n  const onRichTextStyleChange = (property, value) => {\r\n    const propertyTranslation = {\r\n      'font-weight': 'bold',\r\n      'font-style': 'italic',\r\n      'underline': 'underline',\r\n      'line-through': 'strike',\r\n      'font-family': 'font',\r\n      'font-size': 'size',\r\n    };\r\n    if (property === 'font-family' || property === 'font-size') {\r\n      applyFormat(propertyTranslation[property], value);\r\n      const annotation = annotationRef.current;\r\n      if (annotation.isAutoSized()) {\r\n        const editBoxManager = core.getAnnotationManager().getEditBoxManager();\r\n        editBoxManager.resizeAnnotation(annotation);\r\n      }\r\n    } else {\r\n      handleTextFormatChange(propertyTranslation[property])();\r\n    }\r\n  };\r\n\r\n\r\n  propertiesRef.current.bold = format.bold;\r\n  propertiesRef.current.italic = format.italic;\r\n  propertiesRef.current.underline = format.underline;\r\n  propertiesRef.current.strikeout = format.strike;\r\n  propertiesRef.current.quillFont = format['original-font-family'] || format.font || propertiesRef.current.Font;\r\n  propertiesRef.current.quillFontSize = format.originalSize || propertiesRef.current.FontSize;\r\n\r\n  // TODO for now don't show it in mobile\r\n  return isDisabled || (isMobile() && !isInDesktopOnlyMode) ? null : (\r\n    <Draggable\r\n      position={draggablePosition}\r\n      onDrag={syncDraggablePosition}\r\n      onStop={syncDraggablePosition}\r\n      enableUserSelectHack={false}\r\n      // don't allow drag when clicking on a button element or a color cell\r\n      cancel=\".Button, .cell, .mathSymbolsContainer, .Dropdown, .Dropdown__item\"\r\n      // prevent the blur event from being triggered when clicking on toolbar buttons\r\n      // otherwise we can't style the text since a blur event is triggered before a click event\r\n      onMouseDown={(e) => {\r\n        if (e.type !== 'touchstart') {\r\n          e.preventDefault();\r\n        }\r\n      }}\r\n    >\r\n      <div\r\n        className={classNames({\r\n          Popup: true,\r\n          RichTextPopup: true,\r\n          open: isOpen,\r\n          closed: !isOpen,\r\n          legacy: legacyPopup,\r\n        })}\r\n        ref={popupRef}\r\n        data-element={DataElements.RICH_TEXT_POPUP}\r\n        style={{ ...cssPosition }}\r\n      >\r\n        {\r\n          legacyPopup ? (<>\r\n            <Element className=\"rich-text-format-legacy\" dataElement=\"richTextFormats\">\r\n              <Button\r\n                isActive={format.bold}\r\n                dataElement=\"richTextBoldButton\"\r\n                onClick={handleTextFormatChange('bold')}\r\n                img=\"icon-text-bold\"\r\n                title=\"option.richText.bold\"\r\n              />\r\n              <Button\r\n                isActive={format.italic}\r\n                dataElement=\"richTextItalicButton\"\r\n                onClick={handleTextFormatChange('italic')}\r\n                img=\"icon-text-italic\"\r\n                title=\"option.richText.italic\"\r\n              />\r\n              <Button\r\n                isActive={format.underline}\r\n                dataElement=\"richTextUnderlineButton\"\r\n                onClick={handleTextFormatChange('underline')}\r\n                img=\"ic_annotation_underline_black_24px\"\r\n                title=\"option.richText.underline\"\r\n              />\r\n              <Button\r\n                isActive={format.strike}\r\n                dataElement=\"richTextStrikeButton\"\r\n                onClick={handleTextFormatChange('strike')}\r\n                img=\"ic_annotation_strikeout_black_24px\"\r\n                title=\"option.richText.strikeout\"\r\n              />\r\n              <Button\r\n                dataElement=\"mathSymbolsButton\"\r\n                onClick={handleSymbolsClick}\r\n                img=\"ic_thumbnails_grid_black_24px\"\r\n                title=\"option.mathSymbols\"\r\n              />\r\n            </Element>\r\n            <HorizontalDivider style={{ paddingTop: 0 }} />\r\n          </>) :\r\n            (<>\r\n              <div className=\"collapsible-menu\" onClick={openTextStyle} onTouchStart={openTextStyle} role={'toolbar'}>\r\n                <div className=\"menu-title\">\r\n                  {i18next.t('option.stylePopup.textStyle')}\r\n                </div>\r\n                <Icon glyph={`icon-chevron-${isTextStylePickerOpen ? 'up' : 'down'}`} />\r\n              </div>\r\n              {isTextStylePickerOpen && (\r\n                <div className=\"menu-items\">\r\n                  <TextStylePicker\r\n                    fonts={fonts}\r\n                    onPropertyChange={onPropertyChange}\r\n                    onRichTextStyleChange={onRichTextStyleChange}\r\n                    properties={propertiesRef.current}\r\n                    stateless={true}\r\n                    isFreeText={true}\r\n                    onFreeTextSizeToggle={() => handleFreeTextAutoSizeToggle(annotation, setAutoSizeFont, isAutoSizeFont)}\r\n                    isFreeTextAutoSize={isAutoSizeFont}\r\n                    isRichTextEditMode={true}\r\n                  />\r\n                </div>\r\n              )}\r\n              <div className=\"divider\" />\r\n              {!isPaletteDisabled &&\r\n                <div className=\"collapsible-menu\" onClick={openColors} onTouchStart={openColors} role={'toolbar'}>\r\n                  <div className=\"menu-title\">\r\n                    {i18next.t('option.stylePopup.colors')}\r\n                  </div>\r\n                  <Icon glyph={`icon-chevron-${isColorPickerOpen ? 'up' : 'down'}`} />\r\n                </div>\r\n              }\r\n            </>\r\n            )}\r\n        {!isPaletteDisabled && (legacyPopup || isColorPickerOpen) && (\r\n          <>\r\n            <ColorPalette\r\n              colorMapKey=\"freeText\"\r\n              color={format.color}\r\n              property=\"TextColor\"\r\n              onStyleChange={handleColorChange}\r\n              hasPadding\r\n            />\r\n            {customColors.length > 0 && (\r\n              <ColorPalettePicker\r\n                color={format.color}\r\n                property=\"TextColor\"\r\n                onStyleChange={handleColorChange}\r\n                enableEdit={false}\r\n              />\r\n            )}\r\n          </>\r\n        )}\r\n        {symbolsVisible && <MathSymbolsPicker onClickHandler={insertSymbols} maxHeight={symbolsAreaHeight} />}\r\n      </div>\r\n    </Draggable>\r\n  );\r\n};\r\n\r\nRichTextPopup.propTypes = propTypes;\r\n\r\nexport default React.memo(RichTextPopup);\r\n", "import RichTextPopup from './RichTextPopup';\r\n\r\nexport default RichTextPopup;"], "sourceRoot": ""}