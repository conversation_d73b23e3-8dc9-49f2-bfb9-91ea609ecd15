{"version": 3, "sources": ["webpack:///./src/ui/src/components/AlignmentPopup/AlignmentPopup.scss?3897", "webpack:///./src/ui/src/components/AlignmentPopup/AlignmentPopup.scss", "webpack:///./src/ui/src/components/AlignmentPopup/AlignmentPopup.js", "webpack:///./src/ui/src/components/AlignmentPopup/AlignmentConfig.js", "webpack:///./src/ui/src/components/AlignmentPopup/AlignmentPopupContainer.js", "webpack:///./src/ui/src/components/AlignmentPopup/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "alignmentConfig", "PropTypes", "array", "alignmentOnClick", "func", "backToMenuOnClick", "distributeConfig", "distributeOnClick", "isAnnotation", "bool", "AlignmentPopup", "t", "useTranslation", "renderButtonRow", "title", "config", "onClick", "className", "map", "ActionButton", "key", "img", "icon", "alignment", "data-testid", "classNames", "Popup", "AlignAnnotationPopup", "dataElement", "role", "type", "tabIndex", "onKeyDown", "annotationManager", "core", "getAnnotationManager", "Alignment", "StandardAlignmentTypes", "LEFT", "RIGHT", "TOP", "BOTTOM", "CenterAlignmentTypes", "CENTER_VERTICAL", "CENTER_HORIZONTAL", "DistributeAlignmentTypes", "DISTRIBUTE_VERTICAL", "DISTRIBUTE_HORIZONTAL", "annotation", "object", "AlignmentPopupContainer", "useSelector", "state", "selectors", "isElementOpen", "DataElements", "ANNOTATION_ALIGNMENT_POPUP", "getActiveDocumentViewerKey", "shallowEqual", "isOpen", "activeDocumentViewerKey", "useState", "left", "top", "position", "setPosition", "popupRef", "useRef", "dispatch", "useDispatch", "useLayoutEffect", "setPopupPosition", "current", "getAnnotationPopupPositionBasedOn", "AlignAnnotationPopupContainer", "open", "closed", "undefined", "DataElementWrapper", "style", "ref", "annotManager", "selectedAnnotations", "getSelectedAnnotations", "centerAnnotations", "alignAnnotations", "actions", "closeElement", "selectAnnotations", "distributeAnnotations"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,o+HAAq+H,KAG9/H0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,20CCFvB,IAAMC,EAAY,CAChBC,gBAAiBC,IAAUC,MAC3BC,iBAAkBF,IAAUG,KAC5BC,kBAAmBJ,IAAUG,KAC7BE,iBAAkBL,IAAUC,MAC5BK,kBAAmBN,IAAUG,KAC7BI,aAAcP,IAAUQ,MAGpBC,EAAiB,SAAH,GAOd,IANJV,EAAe,EAAfA,gBACAG,EAAgB,EAAhBA,iBACAE,EAAiB,EAAjBA,kBACAC,EAAgB,EAAhBA,iBACAC,EAAiB,EAAjBA,kBACAC,EAAY,EAAZA,aAEOG,EAAqB,EAAhBC,cAAgB,GAApB,GAEFC,EAAkB,SAACC,EAAOC,EAAQC,GACtC,OACE,yBAAKC,UAAU,wBACb,6BAAMN,EAAEG,IACR,yBAAKG,UAAU,cACZF,EAAOG,KAAI,SAACH,GAAM,OACjB,kBAACI,EAAA,EAAY,CACXC,IAAKL,EAAOD,MACZG,UAAU,mBACVH,MAAOH,EAAEI,EAAOD,OAChBO,IAAKN,EAAOO,KACZN,QAAS,WACPA,EAAQD,EAAOQ,oBAiC7B,OAAKf,EAKH,yBACEgB,cAAY,+BACZP,UAAWQ,IAAW,CACpBC,OAAO,EACPC,sBAAsB,EACtB,iBAAiB,KAlBrB,yBAAKV,UAAU,YAff,yBAAKA,UAAU,eACb,yBAAKA,UAAU,cACb,kBAACE,EAAA,EAAY,CACXF,UAAU,sBACVW,YAAY,mBACZd,MAAOH,EAAE,qBACTU,IAAI,6BACJL,QAASX,IAEX,yBAAKwB,KAAK,SAASC,KAAK,SAASC,SAAS,IAAIf,QAASX,EAAmB2B,UAAW3B,GAAoBM,EAAE,wBAQ7G,yBAAKM,UAAU,YACdJ,EAAgB,2BAA4Bb,EAAiBG,GAC7DU,EAAgB,4BAA6BP,EAAkBC,KAK3D,MAiBXG,EAAeX,UAAYA,EAEZW,Q,eC1FTuB,EAAoBC,IAAKC,uBAE/B,EAAqCF,EAAkBG,UAAUC,uBAAzDC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAAOC,EAAG,EAAHA,IAAKC,EAAM,EAANA,OAC1B,EAA+CR,EAAkBG,UAAUM,qBAAnEC,EAAe,EAAfA,gBAAiBC,EAAiB,EAAjBA,kBACzB,EAAuDX,EAAkBG,UAAUS,yBAA3EC,EAAmB,EAAnBA,oBAAqBC,EAAqB,EAArBA,sBAEvB/C,EAAkB,CACtB,CACEuB,UAAWe,EACXhB,KAAM,oBACNR,MAAO,4BAET,CACES,UAAWqB,EACXtB,KAAM,iCACNR,MAAO,wCAET,CACES,UAAWgB,EACXjB,KAAM,qBACNR,MAAO,6BAET,CACES,UAAWiB,EACXlB,KAAM,mBACNR,MAAO,2BAET,CACES,UAAWoB,EACXrB,KAAM,+BACNR,MAAO,sCAET,CACES,UAAWkB,EACXnB,KAAM,sBACNR,MAAO,+BAILR,EAAmB,CACvB,CACEiB,UAAWuB,EACXxB,KAAM,yBACNR,MAAO,qCAET,CACES,UAAWwB,EACXzB,KAAM,2BACNR,MAAO,wC,kwECnCX,IAAMf,EAAY,CAChBiD,WAAY/C,IAAUgD,QAGlBC,EAA0B,SAAH,GAEvB,IADJF,EAAU,EAAVA,WAWC,IANGG,aACF,SAACC,GAAK,MAAK,CACTC,IAAUC,cAAcF,EAAOG,IAAaC,4BAC5CH,IAAUI,2BAA2BL,MAEvCM,KACD,GARCC,EAAM,KACNC,EAAuB,KASoC,IAA7BC,mBAAS,CAAEC,KAAM,EAAGC,IAAK,IAAI,GAAtDC,EAAQ,KAAEC,EAAW,KACtBC,EAAWC,mBACXC,EAAWC,cA2BjBC,2BAAgB,YACVtB,GAAcW,IAChBY,MAED,CAACvB,EAAYW,EAAQC,IAExB,IAAMW,EAAmB,WACnBvB,GAAckB,EAASM,SACzBP,EAAYQ,YAAkCzB,EAAYkB,EAAUN,KAIlE3C,EAAYQ,IAAW,CAC3BC,OAAO,EACPgD,+BAA+B,EAC/BC,KAAMhB,EACNiB,QAASjB,IAGLnD,OAA8BqE,IAAf7B,EAErB,OACE,kBAAC8B,EAAA,EAAkB,CACjBlD,YAAa2B,IAAaC,2BAC1BvC,UAAWA,EACX8D,MAAK,KAAOf,GACZgB,IAAKd,GAEL,kBAAC,EAAc,CACblE,gBAAiBA,EACjBG,iBAvDmB,SAACoB,GACxB,IAAM0D,EAAe/C,IAAKC,uBACpB+C,EAAsBD,EAAaE,yBACvB,qBAAd5D,GAAkD,mBAAdA,EACtC0D,EAAa7C,UAAUgD,kBAAkBF,EAAqB3D,GAE9D0D,EAAa7C,UAAUiD,iBAAiBH,EAAqB3D,GAE/DgD,KAgDIlE,kBAtCoB,WACxB+D,EAASkB,IAAQC,aAAahC,IAAaC,6BAC3C,IAAMyB,EAAe/C,IAAKC,uBACpB+C,EAAsBD,EAAaE,yBACzCF,EAAaO,kBAAkBN,IAmC3B5E,iBAAkBA,EAClBC,kBA/CoB,SAACgB,GACzB,IAAM0D,EAAe/C,IAAKC,uBACpB+C,EAAsBD,EAAaE,yBACzCF,EAAa7C,UAAUqD,sBAAsBP,EAAqB3D,GAClEgD,KA4CI/D,aAAcA,MAMtB0C,EAAwBnD,UAAYA,EAErBmD,QCtGAA", "file": "chunks/chunk.56.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./AlignmentPopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.AlignAnnotationPopupContainer{visibility:visible}.closed.AlignAnnotationPopupContainer{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.AlignAnnotationPopupContainer{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.AlignAnnotationPopupContainer:empty{padding:0}.AlignAnnotationPopupContainer .buttons{display:flex}.AlignAnnotationPopupContainer .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AlignAnnotationPopupContainer .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AlignAnnotationPopupContainer .Button{width:42px;height:42px}}.AlignAnnotationPopupContainer .Button:hover{background:var(--popup-button-hover)}.AlignAnnotationPopupContainer .Button:hover:disabled{background:none}.AlignAnnotationPopupContainer .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AlignAnnotationPopupContainer .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AlignAnnotationPopupContainer .Button .Icon{width:24px;height:24px}}.is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button{width:100%;height:32px}}.is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.AlignAnnotationPopupContainer{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background)}.AlignAnnotationPopup.is-horizontal .contents{display:flex;grid-gap:16px;gap:16px;flex-direction:column;padding:16px}.AlignAnnotationPopup.is-horizontal .contents .back-to-menu-button{width:24px;height:24px}.AlignAnnotationPopup.is-horizontal .contents .back-to-menu-button .Icon{width:16px;height:16px}.AlignAnnotationPopup.is-horizontal .contents .divider{height:1px;width:100%;background:var(--divider)}.AlignAnnotationPopup.is-horizontal button{padding:0;margin:0;height:32px;width:32px}.AlignAnnotationPopup.is-horizontal button .Icon{width:24px;height:24px}.AlignAnnotationPopup.is-horizontal .button-row-container{display:flex;grid-gap:8px;gap:8px;flex-direction:column}.AlignAnnotationPopup.is-horizontal .button-row{display:flex;align-items:center;grid-gap:8px;gap:8px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport ActionButton from 'components/ActionButton';\n\nimport './AlignmentPopup.scss';\n\nconst propTypes = {\n  alignmentConfig: PropTypes.array,\n  alignmentOnClick: PropTypes.func,\n  backToMenuOnClick: PropTypes.func,\n  distributeConfig: PropTypes.array,\n  distributeOnClick: PropTypes.func,\n  isAnnotation: PropTypes.bool,\n};\n\nconst AlignmentPopup = ({\n  alignmentConfig,\n  alignmentOnClick,\n  backToMenuOnClick,\n  distributeConfig,\n  distributeOnClick,\n  isAnnotation\n}) => {\n  const [t] = useTranslation();\n\n  const renderButtonRow = (title, config, onClick) => {\n    return (\n      <div className='button-row-container'>\n        <div>{t(title)}</div>\n        <div className=\"button-row\">\n          {config.map((config) => (\n            <ActionButton\n              key={config.title}\n              className=\"main-menu-button\"\n              title={t(config.title)}\n              img={config.icon}\n              onClick={() => {\n                onClick(config.alignment);\n              }}\n            />\n          ))}\n        </div>\n      </div>\n    );\n  };\n\n  const renderTopSection = () => (\n    <div className=\"top-section\">\n      <div className=\"button-row\">\n        <ActionButton\n          className=\"back-to-menu-button\"\n          dataElement=\"backToMenuButton\"\n          title={t('action.backToMenu')}\n          img=\"ic_chevron_left_black_24px\"\n          onClick={backToMenuOnClick}\n        />\n        <div role=\"button\" type=\"button\" tabIndex=\"0\" onClick={backToMenuOnClick} onKeyDown={backToMenuOnClick}>{t('action.backToMenu')}</div>\n      </div>\n    </div>\n  );\n\n  const renderContents = () => (\n    <div className=\"contents\">\n      {renderTopSection()}\n      <div className=\"divider\" />\n      {renderButtonRow('alignmentPopup.alignment', alignmentConfig, alignmentOnClick)}\n      {renderButtonRow('alignmentPopup.distribute', distributeConfig, distributeOnClick)}\n    </div>\n  );\n\n  if (!isAnnotation) {\n    return null;\n  }\n\n  return (\n    <div\n      data-testid=\"alignment-annotation-element\"\n      className={classNames({\n        Popup: true,\n        AlignAnnotationPopup: true,\n        'is-horizontal': true\n      })}\n    >\n      {renderContents()}\n    </div>\n  );\n};\n\nAlignmentPopup.propTypes = propTypes;\n\nexport default AlignmentPopup;", "import core from 'core';\n\nconst annotationManager = core.getAnnotationManager();\n\nconst { LEFT, RIGHT, TOP, BOTTOM } = annotationManager.Alignment.StandardAlignmentTypes;\nconst { CENTER_VERTICAL, CENTER_HORIZONTAL } = annotationManager.Alignment.CenterAlignmentTypes;\nconst { DISTRIBUTE_VERTICAL, DISTRIBUTE_HORIZONTAL } = annotationManager.Alignment.DistributeAlignmentTypes;\n\nconst alignmentConfig = [\n  {\n    alignment: LEFT,\n    icon: 'ic-alignment-left',\n    title: 'alignmentPopup.alignLeft',\n  },\n  {\n    alignment: CENTER_HORIZONTAL,\n    icon: 'ic-alignment-center-horizontal',\n    title: 'alignmentPopup.alignHorizontalCenter',\n  },\n  {\n    alignment: RIGHT,\n    icon: 'ic-alignment-right',\n    title: 'alignmentPopup.alignRight',\n  },\n  {\n    alignment: TOP,\n    icon: 'ic-alignment-top',\n    title: 'alignmentPopup.alignTop',\n  },\n  {\n    alignment: CENTER_VERTICAL,\n    icon: 'ic-alignment-center-vertical',\n    title: 'alignmentPopup.alignVerticalCenter',\n  },\n  {\n    alignment: BOTTOM,\n    icon: 'ic-alignment-bottom',\n    title: 'alignmentPopup.alignBottom',\n  }\n];\n\nconst distributeConfig = [\n  {\n    alignment: DISTRIBUTE_VERTICAL,\n    icon: 'ic-distribute-vertical',\n    title: 'alignmentPopup.distributeVertical',\n  },\n  {\n    alignment: DISTRIBUTE_HORIZONTAL,\n    icon: 'ic-distribute-horizontal',\n    title: 'alignmentPopup.distributeHorizontal',\n  }\n];\n\nexport {\n  alignmentConfig,\n  distributeConfig\n};", "import actions from 'actions';\nimport classNames from 'classnames';\nimport core from 'core';\nimport React, { useState, useRef, useLayoutEffect } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport { getAnnotationPopupPositionBasedOn } from 'helpers/getPopupPosition';\nimport PropTypes from 'prop-types';\nimport selectors from 'selectors';\n\nimport AlignmentPopup from './AlignmentPopup';\nimport './AlignmentPopup.scss';\nimport DataElementWrapper from '../DataElementWrapper';\nimport DataElements from 'src/constants/dataElement';\nimport { alignmentConfig, distributeConfig } from './AlignmentConfig';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n};\n\nconst AlignmentPopupContainer = ({\n  annotation\n}) => {\n  const [\n    isOpen,\n    activeDocumentViewerKey\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, DataElements.ANNOTATION_ALIGNMENT_POPUP),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual,\n  );\n\n  const [position, setPosition] = useState({ left: 0, top: 0 });\n  const popupRef = useRef();\n  const dispatch = useDispatch();\n\n  const alignmentOnClick = (alignment) => {\n    const annotManager = core.getAnnotationManager();\n    const selectedAnnotations = annotManager.getSelectedAnnotations();\n    if (alignment === 'centerHorizontal' || alignment === 'centerVertical') {\n      annotManager.Alignment.centerAnnotations(selectedAnnotations, alignment);\n    } else {\n      annotManager.Alignment.alignAnnotations(selectedAnnotations, alignment);\n    }\n    setPopupPosition();\n  };\n\n  const distributeOnClick = (alignment) => {\n    const annotManager = core.getAnnotationManager();\n    const selectedAnnotations = annotManager.getSelectedAnnotations();\n    annotManager.Alignment.distributeAnnotations(selectedAnnotations, alignment);\n    setPopupPosition();\n  };\n\n  const backToMenuOnClick = () => {\n    dispatch(actions.closeElement(DataElements.ANNOTATION_ALIGNMENT_POPUP));\n    const annotManager = core.getAnnotationManager();\n    const selectedAnnotations = annotManager.getSelectedAnnotations();\n    annotManager.selectAnnotations(selectedAnnotations);\n  };\n\n  useLayoutEffect(() => {\n    if (annotation || isOpen) {\n      setPopupPosition();\n    }\n  }, [annotation, isOpen, activeDocumentViewerKey]);\n\n  const setPopupPosition = () => {\n    if (annotation && popupRef.current) {\n      setPosition(getAnnotationPopupPositionBasedOn(annotation, popupRef, activeDocumentViewerKey));\n    }\n  };\n\n  const className = classNames({\n    Popup: true,\n    AlignAnnotationPopupContainer: true,\n    open: isOpen,\n    closed: !isOpen,\n  });\n\n  const isAnnotation = annotation !== undefined;\n\n  return (\n    <DataElementWrapper\n      dataElement={DataElements.ANNOTATION_ALIGNMENT_POPUP}\n      className={className}\n      style={{ ...position }}\n      ref={popupRef}\n    >\n      <AlignmentPopup\n        alignmentConfig={alignmentConfig}\n        alignmentOnClick={alignmentOnClick}\n        backToMenuOnClick={backToMenuOnClick}\n        distributeConfig={distributeConfig}\n        distributeOnClick={distributeOnClick}\n        isAnnotation={isAnnotation}\n      />\n    </DataElementWrapper>\n  );\n};\n\nAlignmentPopupContainer.propTypes = propTypes;\n\nexport default AlignmentPopupContainer;\n", "import AlignmentPopupContainer from './AlignmentPopupContainer';\n\nexport default AlignmentPopupContainer;"], "sourceRoot": ""}