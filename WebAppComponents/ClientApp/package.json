{"name": "level-components", "author": "levelbuild AG", "private": true, "version": "0.0.1", "type": "module", "main": "dist/main.js", "types": "dist/main.d.ts", "module": "src/main.ts", "exports": {".": "./src/main.ts", "./helper": "./src/helper.ts", "./types": "./src/shared/types.ts", "./enums": "./src/enums/index.ts", "./individual": "./src/components/individual/index.ts", "./hybrid": "./src/components/hybrid/index.ts"}, "files": ["dist"], "scripts": {"build": "tsc --project ./tsconfig.build.json && vite build", "build-external": "tsc --project ./tsconfig.build.json && vite --config vite-external.config.ts build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-dev": "cypress open --component", "test:chrome": "cypress run --component --browser chrome", "test:chrome-beta": "cypress run --component --browser chrome:beta", "test:edge": "cypress run --component --browser edge", "test:firefox": "cypress run --component --browser firefox", "test:safari": "cypress run --component --browser webkit", "coverage:html": "nyc report --reporter=lcov & start ./coverage/lcov-report/index.html", "build-translations": "tsx ./scripts/deployTranslations.ts", "build-fontawesome": "tsx ./scripts/deployFontawesome.ts", "deploy-changelogs": "tsx ./scripts/deployChangelogs.ts", "build-fontawesome-library": "tsx ./scripts/deployFontawesomeLibrary.ts"}, "dependencies": {"@lit/context": "^1.1.5", "@open-wc/lit-helpers": "^0.7.0", "@pdftron/webviewer": "^11.4.0", "@tiptap/core": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "date-fns": "^4.1.0", "interactjs": "^1.10.27", "lit": "^3.3.0", "openseadragon": "^5.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@cypress/code-coverage": "^3.14.2", "@cypress/vite-dev-server": "^6.0.3", "@interactjs/types": "^1.10.27", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@storybook/addon-essentials": "^8.6.13", "@storybook/addon-links": "^8.6.13", "@storybook/addon-viewport": "^8.6.13", "@storybook/blocks": "^8.6.13", "@storybook/web-components": "^8.6.13", "@storybook/web-components-vite": "^8.6.13", "@types/js-yaml": "^4.0.9", "@types/node": "^22.15.18", "@types/openseadragon": "^4.1.0", "@types/uuid": "^10.0.0", "cypress": "^14.3.3", "cypress-lit": "^0.0.8", "cypress-real-events": "^1.14.0", "glob": "^11.0.2", "js-yaml": "^4.1.0", "mochawesome": "^7.1.3", "mochawesome-merge": "^5.0.0", "mochawesome-report-generator": "^6.2.0", "msw-storybook-addon": "^2.0.4", "playwright-webkit": "^1.52.0", "storybook": "^8.6.13", "tsx": "^4.19.4", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vite-plugin-istanbul": "^7.0.0"}, "optionalDependencies": {"@fortawesome/fontawesome-pro": "^6.5.1"}, "msw": {"workerDirectory": ["public"]}}