/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[1],{618:function(ya,ua,n){n.r(ua);var na=n(0),ma=n(358);ya=n(614);n=n(537);var oa=window,ka=function(ia){function fa(x,y){var r=ia.call(this,x,y)||this;r.url=x;r.range=y;r.request=new XMLHttpRequest;r.request.open("GET",r.url,!0);oa.Uint8Array&&(r.request.responseType="arraybuffer");r.request.setRequestHeader("X-Requested-With","XMLHttpRequest");r.status=ma.a.NOT_STARTED;return r}Object(na.c)(fa,ia);return fa}(ya.ByteRangeRequest);
ya=function(ia){function fa(x,y,r,e){x=ia.call(this,x,y,r,e)||this;x.hG=ka;return x}Object(na.c)(fa,ia);fa.prototype.jD=function(x,y){return"".concat(x,"/bytes=").concat(y.start,",").concat(y.stop?y.stop:"")};return fa}(ya["default"]);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
