{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/eu.js"], "names": ["module", "exports", "a", "t", "default", "e", "l", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "ll", "lll", "llll", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,sEAAsEC,MAAM,KAAKC,OAAO,+FAA+FD,MAAM,KAAKE,UAAU,EAAEC,cAAc,8BAA8BH,MAAM,KAAKI,YAAY,8DAA8DJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,0BAA0BC,IAAI,gCAAgCC,KAAK,sCAAsChB,EAAE,WAAWiB,GAAG,oBAAoBC,IAAI,0BAA0BC,KAAK,gCAAgCC,aAAa,CAACC,OAAO,WAAWC,KAAK,WAAWC,EAAE,iBAAiBC,EAAE,aAAaC,GAAG,YAAYC,EAAE,WAAWC,GAAG,UAAUC,EAAE,WAAWC,GAAG,UAAUC,EAAE,eAAeC,GAAG,cAAcC,EAAE,WAAWC,GAAG,YAAY,OAAOpC,EAAEC,QAAQoC,OAAOlC,EAAE,MAAK,GAAIA,EAAzsCD,CAAE,EAAQ", "file": "chunks/chunk.149.js", "sourcesContent": ["!function(a,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(a=\"undefined\"!=typeof globalThis?globalThis:a||self).dayjs_locale_eu=e(a.dayjs)}(this,(function(a){\"use strict\";function e(a){return a&&\"object\"==typeof a&&\"default\"in a?a:{default:a}}var t=e(a),l={name:\"eu\",weekdays:\"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata\".split(\"_\"),months:\"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua\".split(\"_\"),weekStart:1,weekdaysShort:\"ig._al._ar._az._og._ol._lr.\".split(\"_\"),monthsShort:\"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.\".split(\"_\"),weekdaysMin:\"ig_al_ar_az_og_ol_lr\".split(\"_\"),ordinal:function(a){return a},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"YYYY[ko] MMMM[ren] D[a]\",LLL:\"YYYY[ko] MMMM[ren] D[a] HH:mm\",LLLL:\"dddd, YYYY[ko] MMMM[ren] D[a] HH:mm\",l:\"YYYY-M-D\",ll:\"YYYY[ko] MMM D[a]\",lll:\"YYYY[ko] MMM D[a] HH:mm\",llll:\"ddd, YYYY[ko] MMM D[a] HH:mm\"},relativeTime:{future:\"%s barru\",past:\"duela %s\",s:\"segundo batzuk\",m:\"minutu bat\",mm:\"%d minutu\",h:\"ordu bat\",hh:\"%d ordu\",d:\"egun bat\",dd:\"%d egun\",M:\"hilabete bat\",MM:\"%d hilabete\",y:\"urte bat\",yy:\"%d urte\"}};return t.default.locale(l,null,!0),l}));"], "sourceRoot": ""}