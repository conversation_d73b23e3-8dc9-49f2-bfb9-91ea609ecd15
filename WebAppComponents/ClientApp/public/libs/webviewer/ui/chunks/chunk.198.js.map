{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/nl.js"], "names": ["module", "exports", "e", "n", "default", "a", "d", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "ordinal", "weekStart", "yearStart", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,6DAA6DC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,0FAA0FH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,QAAQ,SAASZ,GAAG,OAAOA,EAAE,KAAKa,UAAU,EAAEC,UAAU,EAAEC,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,UAAUC,KAAK,aAAaC,EAAE,oBAAoBC,EAAE,aAAaC,GAAG,aAAaC,EAAE,UAAUC,GAAG,SAASzB,EAAE,UAAU0B,GAAG,WAAWC,EAAE,YAAYC,GAAG,aAAaC,EAAE,WAAWC,GAAG,YAAY,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAAnjCD,CAAE,EAAQ", "file": "chunks/chunk.198.js", "sourcesContent": ["!function(e,a){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=a(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],a):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_nl=a(e.dayjs)}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=a(e),d={name:\"nl\",weekdays:\"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag\".split(\"_\"),weekdaysShort:\"zo._ma._di._wo._do._vr._za.\".split(\"_\"),weekdaysMin:\"zo_ma_di_wo_do_vr_za\".split(\"_\"),months:\"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december\".split(\"_\"),monthsShort:\"jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,yearStart:4,formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD-MM-YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"over %s\",past:\"%s geleden\",s:\"een paar seconden\",m:\"een minuut\",mm:\"%d minuten\",h:\"een uur\",hh:\"%d uur\",d:\"een dag\",dd:\"%d dagen\",M:\"een maand\",MM:\"%d maanden\",y:\"een jaar\",yy:\"%d jaar\"}};return n.default.locale(d,null,!0),d}));"], "sourceRoot": ""}