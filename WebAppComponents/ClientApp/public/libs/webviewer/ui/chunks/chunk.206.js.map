{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ro.js"], "names": ["module", "exports", "e", "t", "default", "i", "_", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "ordinal", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,kDAAkDC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,oGAAoGH,MAAM,KAAKI,YAAY,gEAAgEJ,MAAM,KAAKK,UAAU,EAAEC,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,cAAcC,IAAI,mBAAmBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,WAAWC,KAAK,UAAUC,EAAE,iBAAiBC,EAAE,WAAWC,GAAG,YAAYC,EAAE,QAAQC,GAAG,SAASC,EAAE,OAAOC,GAAG,UAAUC,EAAE,SAASC,GAAG,UAAUC,EAAE,QAAQC,GAAG,UAAUC,QAAQ,SAASlC,GAAG,OAAOA,IAAI,OAAOC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAArhCD,CAAE,EAAQ", "file": "chunks/chunk.206.js", "sourcesContent": ["!function(e,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],i):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_ro=i(e.dayjs)}(this,(function(e){\"use strict\";function i(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=i(e),_={name:\"ro\",weekdays:\"Duminică_Luni_Marți_Miercuri_Joi_Vineri_Sâmbătă\".split(\"_\"),weekdaysShort:\"Dum_Lun_Mar_Mie_Joi_Vin_Sâm\".split(\"_\"),weekdaysMin:\"Du_Lu_Ma_Mi_Jo_Vi_Sâ\".split(\"_\"),months:\"<PERSON>uarie_Februarie_Martie_Aprilie_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_August_Septembrie_Octombrie_Noiembrie_Decembrie\".split(\"_\"),monthsShort:\"Ian._Febr._Mart._Apr._Mai_Iun._Iul._Aug._Sept._Oct._Nov._Dec.\".split(\"_\"),weekStart:1,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY H:mm\",LLLL:\"dddd, D MMMM YYYY H:mm\"},relativeTime:{future:\"peste %s\",past:\"acum %s\",s:\"câteva secunde\",m:\"un minut\",mm:\"%d minute\",h:\"o oră\",hh:\"%d ore\",d:\"o zi\",dd:\"%d zile\",M:\"o lună\",MM:\"%d luni\",y:\"un an\",yy:\"%d ani\"},ordinal:function(e){return e}};return t.default.locale(_,null,!0),_}));"], "sourceRoot": ""}