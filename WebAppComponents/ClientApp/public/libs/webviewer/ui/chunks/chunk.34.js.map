{"version": 3, "sources": ["webpack:///./src/ui/src/constants/bookmarksOutlinesShared.scss?1d5f", "webpack:///./src/ui/src/constants/dnd.js", "webpack:///./src/ui/src/components/TextButton/TextButton.js", "webpack:///./src/ui/src/components/TextButton/index.js", "webpack:///./src/ui/src/components/PanelListItem/index.js", "webpack:///./src/ui/src/components/Outline/Context.js", "webpack:///./src/ui/src/constants/bookmarksOutlinesShared.scss", "webpack:///./src/ui/src/components/OutlineContent/OutlineContent.js", "webpack:///./src/ui/src/components/OutlineContent/index.js", "webpack:///./src/ui/src/components/TextButton/TextButton.scss?deeb", "webpack:///./src/ui/src/components/TextButton/TextButton.scss", "webpack:///./src/ui/src/components/OutlineContent/OutlineContent.scss?2e51", "webpack:///./src/ui/src/components/OutlineContent/OutlineContent.scss", "webpack:///./src/ui/src/components/Outline/Outline.scss?a897", "webpack:///./src/ui/src/components/Outline/Outline.scss", "webpack:///./src/ui/src/components/OutlinesPanel/OutlinesPanel.scss?71a9", "webpack:///./src/ui/src/components/OutlinesPanel/OutlinesPanel.scss", "webpack:///./src/ui/src/components/Outline/Outline.js", "webpack:///./src/ui/src/components/Outline/index.js", "webpack:///./src/ui/src/components/OutlinesPanel/OutlinesDragLayer.js", "webpack:///./src/ui/src/components/OutlinesPanel/OutlinesPanel.js", "webpack:///./src/ui/src/components/OutlinesPanel/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "ItemTypes", "OUTLINE", "PORTFOLIO", "DropLocation", "ON_TARGET_HORIZONTAL_MIDPOINT", "ABOVE_TARGET", "BELOW_TARGET", "INITIAL", "BUFFER_ROOM", "propTypes", "img", "PropTypes", "string", "label", "oneOfType", "number", "dataElement", "onClick", "func", "aria<PERSON><PERSON><PERSON>", "ariaControls", "role", "disabled", "bool", "TextButton", "props", "<PERSON><PERSON>", "className", "React", "memo", "PanelListItem", "OutlineContext", "createContext", "Outline", "lazy", "text", "isRequired", "outlinePath", "isAdding", "isExpanded", "setIsExpanded", "isOutlineRenaming", "setOutlineRenaming", "isOutlineChangingDest", "setOutlineChangingDest", "onCancel", "textColor", "children", "array", "setMultiSelected", "moveOutlineInward", "moveOutlineBeforeTarget", "moveOutlineAfterTarget", "OutlineContent", "useContext", "currentDestPage", "currentDestText", "editingOutlines", "setEditingOutlines", "isMultiSelectMode", "isOutlineEditable", "addNewOutline", "renameOutline", "updateOutlineDest", "selectedOutlines", "updateOutlines", "removeOutlines", "customizableUI", "useSelector", "state", "selectors", "getFeatureFlags", "shallowEqual", "t", "useTranslation", "useState", "isDefault", "setIsDefault", "outlineText", "setOutlineText", "isRenaming", "setIsRenaming", "inputRef", "useRef", "isSelected", "includes", "onAddOutline", "trim", "onRenameOutline", "onCancelOutline", "isRenameButtonDisabled", "useEffect", "current", "focus", "select", "editingOutlinesClone", "isOutlineEditing", "textStyle", "color", "handleOnClick", "val", "menuTypes", "RENAME", "SETDEST", "DELETE", "MOVE_UP", "MOVE_DOWN", "MOVE_LEFT", "MOVE_RIGHT", "core", "setToolMode", "outlineUtils", "moveOutlineUp", "moveOutlineDown", "moveOutlineOutward", "flyoutSelector", "DataElements", "BOOKMARK_OUTLINE_FLYOUT", "contentMenuFlyoutOptions", "shouldHideDeleteButton", "currentFlyout", "getFlyout", "type", "contextMenuMoreButtonOptions", "flyoutToggleElement", "moreOptionsDataElement", "checkboxOptions", "id", "checked", "onChange", "e", "target", "key", "labelHeader", "enableMoreOptionsContextMenuFlyout", "onDoubleClick", "expanded", "setIsExpandedHandler", "map", "outline", "getOutlineId", "renderContent", "style", "name", "ref", "placeholder", "aria-label", "value", "onKeyDown", "stopPropagation", "fontStyle", "isSubmitType", "object", "connectDragSource", "connectDragPreview", "connectDropTarget", "isDragging", "isDraggedUpwards", "isDraggedDownwards", "forwardRef", "outlines", "getOutlines", "setActiveOutlinePath", "activeOutlinePath", "isOutlineActive", "setAddingNewOutline", "isAddingNewOutline", "shouldAutoExpandOutlines", "<PERSON><PERSON><PERSON>", "isChangingDest", "setChangingDest", "undefined", "clearSingleClick", "setClearSingleClick", "dispatch", "useDispatch", "elementRef", "getEmptyImage", "captureDraggingState", "opacity", "useImperativeHandle", "getNode", "startsWith", "useLayoutEffect", "rgbObject", "rValue", "gValue", "bValue", "onSingleClick", "useCallback", "goToOutline", "isMobile", "actions", "closeElement", "isActive", "DataElementWrapper", "classNames", "tabIndex", "detail", "setTimeout", "clearTimeout", "getName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marginLeft", "getNestedLevel", "OutlineNested", "<PERSON><PERSON>arget", "hover", "dropTargetMonitor", "dropTargetContainer", "dragObject", "getItem", "dragOutline", "dragSourceNode", "dropOutline", "dropTargetNode", "contains", "dropLocation", "dragIndex", "index", "hoverIndex", "parent", "dropTargetBoundingRect", "getBoundingClientRect", "dropTargetVerticalMiddlePoint", "bottom", "top", "dropTargetClientY", "getClientOffset", "y", "isOver", "shallow", "classList", "add", "remove", "fireEvent", "Events", "DRAG_OUTLINE", "targetOutline", "draggedOutline", "drop", "DROP_OUTLINE", "connect", "dropTargetState", "drop<PERSON>ar<PERSON>", "DragSource", "beginDrag", "dragSourceMonitor", "dragSourceContainer", "sourceId", "canDrag", "isIE", "console", "warn", "isFullPDFEnabled", "dragSourceState", "dragSource", "dragPreview", "layerStyles", "position", "pointerEvents", "zIndex", "left", "width", "height", "getItemStyles", "initialOffset", "currentOffset", "display", "x", "transform", "WebkitTransform", "OutlinesDragLayer", "useDragLayer", "dragLayerState", "itemType", "getItemType", "item", "getInitialSourceClientOffset", "renderDragItem", "OutlinesPanel", "isTest", "isElementDisabled", "OUTLINE_PANEL", "getOutlineEditingEnabled", "getCurrentPage", "getPageLabels", "isDisabled", "outlineEditingEnabled", "currentPage", "pageLabe<PERSON>", "featureFlags", "defaultDestText", "areaDestinationText", "defaultDestCoord", "setCurrentDestText", "currentDestCoord", "setCurrentDestCoord", "setCurrentDestPage", "setOutlineEditable", "isAnyOutlineRenaming", "setAnyOutlineRenaming", "setMultiSelectMode", "setSelectedOutlines", "nextPathRef", "TOOL_NAME", "tool", "getTool", "panelRef", "isAnyEditing", "Object", "values", "some", "onSetDestination", "annotation", "getCustomData", "onOutlinesBookmarksChanged", "setOutlines", "onDocumentLoaded", "addEventListener", "removeEventListener", "handlePanelClick", "event", "getCurrentDestViewerCoord", "pageNum", "getDocumentViewer", "getDocument", "getViewerCoordinates", "outlineName", "slice", "doc", "pageRotation", "getPageRotation", "Core", "PageRotation", "E_90", "E_270", "tmp", "addRootOutline", "shouldEndAccessibleReadingOrderMode", "clearOutlineDestination", "defaultTool", "setOutlineDestination", "generalMoveOutlineAction", "moveDirection", "drag<PERSON>ath", "dropPath", "call", "then", "path", "moveOutlineInTarget", "newName", "setOutlineName", "outlinesToRemove", "sort", "reverse", "confirmation<PERSON><PERSON>ning", "message", "title", "confirmBtnText", "onConfirm", "fullIndex", "deleteOutline", "showWarningMessage", "data-element", "OUTLINE_MULTI_SELECT", "Provider", "DndProvider", "backend", "isMobileDevice", "TouchBackEnd", "HTML5Backend", "data", "itemContent", "find", "filter", "initialItemCount", "OUTLINE_ADD_NEW_BUTTON_CONTAINER", "OUTLINE_ADD_NEW_BUTTON"], "mappings": "iFAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,kCClEnC,sGAAO,IAAMC,EAAY,CACvBC,QAAS,UACTC,UAAW,aAGAC,EAAe,CAC1BC,8BAA+B,6BAC/BC,aAAc,cACdC,aAAc,cACdC,QAAS,WAGEC,EAAc,G,8ECNrBC,G,QAAY,CAChBC,IAAKC,IAAUC,OACfC,MAAOF,IAAUG,UAAU,CAACH,IAAUC,OAAQD,IAAUI,SACxDC,YAAaL,IAAUC,OACvBK,QAASN,IAAUO,KACnBC,UAAWR,IAAUC,OACrBQ,aAAcT,IAAUC,OACxBS,KAAMV,IAAUC,OAChBU,SAAUX,IAAUY,OAGhBC,EAAa,SAACC,GAClB,IACEf,EAQEe,EARFf,IACAM,EAOES,EAPFT,YACAC,EAMEQ,EANFR,QACAJ,EAKEY,EALFZ,MACAM,EAIEM,EAJFN,UACAC,EAGEK,EAHFL,aACAC,EAEEI,EAFFJ,KACAC,EACEG,EADFH,SAGF,OAAQ,kBAACI,EAAA,EAAM,CACbC,UAAU,aACVjB,IAAKA,EACLG,MAAOA,EACPG,YAAaA,EACbC,QAASA,EACTE,UAAWA,EACXC,aAAcA,EACdC,KAAMA,EACNC,SAAUA,KAIdE,EAAWf,UAAYA,EAERmB,UAAMC,KAAKL,GC1CXA,O,kCCFf,cAEeM,MAAa,G,kCCF5B,WAEMC,EAFN,OAEuBH,EAAMI,gBAEdD,O,sBCJL5D,EAAO2B,QAAU,EAAQ,GAAR,EAA+D,IAKlFR,KAAK,CAACnB,EAAOC,EAAI,wkPAAykP,M,yxBCJlmP,8lGAAAA,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,mnBAAAA,EAAA,EAAAA,EAAA,iBAAAA,IAAA,uBAAAA,GAAA,UAAAA,GAAA,GAAAA,EAAA,gtBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAeA,IAAM6D,EAAUC,gBAAK,kBAAM,6CAErBzB,EAAY,CAChB0B,KAAMxB,IAAUC,OAAOwB,WACvBC,YAAa1B,IAAUC,OACvB0B,SAAU3B,IAAUY,KACpBgB,WAAY5B,IAAUY,KACtBiB,cAAe7B,IAAUO,KACzBuB,kBAAmB9B,IAAUY,KAC7BmB,mBAAoB/B,IAAUO,KAC9ByB,sBAAuBhC,IAAUY,KACjCqB,uBAAwBjC,IAAUO,KAClC2B,SAAUlC,IAAUO,KACpB4B,UAAWnC,IAAUC,OACrBmC,SAAUpC,IAAUqC,MACpBC,iBAAkBtC,IAAUO,KAC5BgC,kBAAmBvC,IAAUO,KAC7BiC,wBAAyBxC,IAAUO,KACnCkC,uBAAwBzC,IAAUO,MAG9BmC,EAAiB,SAAH,GAiBd,IAhBJlB,EAAI,EAAJA,KACAE,EAAW,EAAXA,YACAC,EAAQ,EAARA,SACAC,EAAU,EAAVA,WACAC,EAAa,EAAbA,cACAC,EAAiB,EAAjBA,kBACAC,EAAkB,EAAlBA,mBACAC,EAAqB,EAArBA,sBACAC,EAAsB,EAAtBA,uBACAC,EAAQ,EAARA,SACAC,EAAS,EAATA,UACAC,EAAQ,EAARA,SACAE,EAAgB,EAAhBA,iBACAC,EAAiB,EAAjBA,kBACAC,EAAuB,EAAvBA,wBACAC,EAAsB,EAAtBA,uBAEA,EAaIE,qBAAWvB,KAZbwB,EAAe,EAAfA,gBACAC,EAAe,EAAfA,gBACAC,EAAe,EAAfA,gBACAC,EAAkB,EAAlBA,mBACAC,EAAiB,EAAjBA,kBACAC,EAAiB,EAAjBA,kBACAC,EAAa,EAAbA,cACAC,EAAa,EAAbA,cACAC,EAAiB,EAAjBA,kBACAC,EAAgB,EAAhBA,iBACAC,EAAc,EAAdA,eACAC,EAAc,EAAdA,eAIIC,EADeC,aAAY,SAACC,GAAK,OAAKC,IAAUC,gBAAgBF,KAAQG,KAC1CL,eAE7BM,EAAqB,EAAhBC,cAAgB,GAApB,GAGyC,IAAfC,oBAAS,GAAM,GAA1CC,EAAS,KAAEC,EAAY,KACsB,IAAdF,mBAASxC,GAAK,GAA7C2C,GAAW,KAAEC,GAAc,KACiB,KAAfJ,oBAAS,GAAM,GAA5CK,GAAU,MAAEC,GAAa,MAC1BC,GAAWC,mBAiBXC,IAAapB,aAAgB,EAAhBA,EAAkBqB,SAAShD,MAAgB,EAExDiD,GAAe,WACnBzB,EAAqC,KAAvBiB,GAAYS,OAAgB,GAAKT,KAG3CU,GAAkB,WACtB9C,GAAmB,GACnBuC,IAAc,GACdnB,EAAczB,EAAayC,KAGvBW,GAAkB,WACtBxB,IACIxB,IACFC,GAAmB,GACnBuC,IAAc,GACdF,GAAe5C,IAEbQ,GACFC,GAAuB,GAErBN,GACFO,KAIE6C,GAAyB,WAC7B,OAAQZ,IAAe3C,IAAS2C,IAGlCa,qBAAU,WACJb,KAAgB3C,GAClB4C,GAAe5C,KAEhB,CAACA,IAEJwD,qBAAU,YACJrD,GAAYG,KACdyC,GAASU,QAAQC,QACjBX,GAASU,QAAQE,UAGnBjB,GAAcvC,IAAaG,IAAsBE,KAChD,CAACF,EAAmBE,IAEvBgD,qBAAU,WACR,IAAMI,EAAuB,EAAH,GAAQtC,GAC5BuC,EAAmBvD,GAAqBE,EAC1CqD,EACFD,EAAqB1D,GAAgB2D,SAE9BD,EAAqB1D,GAE9BqB,EAAmB,EAAD,GAAMqC,MACvB,CAACtD,EAAmBE,IAEvB,IAAMsD,GAAY,CAChBC,MAAOpD,GAAa,QAGhBqD,GAAa,eA5JrB,EA4JqB,GA5JrB,EA4JqB,UAAG,WAAOC,GAAG,sEACtBA,EAAG,cACJC,IAAUC,OAAM,SAIhBD,IAAUE,QAAO,SAIjBF,IAAUG,OAAM,SAGhBH,IAAUI,QAAO,UAKjBJ,IAAUK,UAAS,UAKnBL,IAAUM,UAAS,UAKnBN,IAAUO,WAAU,mBAxBH,OADpBlE,GAAmB,GACnBuC,IAAc,GAAM,4BAIQ,OAD5BrC,GAAuB,GACvBiE,IAAKC,YA3FO,gCA2FgB,4BAGE,OAA9B5C,EAAe,CAAC7B,IAAc,8CAGxB0E,IAAaC,cAAc3E,GAAY,QAC5B,OAAjB4B,IAAiB,8CAIX8C,IAAaE,gBAAgB5E,GAAY,QAC9B,OAAjB4B,IAAiB,8CAIX8C,IAAaG,mBAAmB7E,GAAY,QACjC,OAAjB4B,IAAiB,8CAIX8C,IAAa7D,kBAAkBb,GAAY,QAChC,OAAjB4B,IAAiB,oGA1LzB,+KAgMG,gBApCkB,sCAsCbkD,GAAiBC,IAAaC,wBAI9BC,GAA2B,CAC/BC,wBAAwB,EACxBC,cALoBpD,aAAY,SAACC,GAAK,OAAKC,IAAUmD,UAAUpD,EAAO8C,OAMtEA,eAAgBA,GAChBO,KANW,UAOXvB,cAAeA,IAGXwB,GAA+B,CACnCC,oBAAqB,wBACrBC,uBAAwB,uBAAF,OAAyBxF,IAG3CyF,GAAkB,CACtBC,GAAE,2BAAqB1F,GACvB2F,QAAS5C,GACT6C,SAAU,SAACC,GACTjF,EAAiBZ,EAAa6F,EAAEC,OAAOH,UAEzC7G,UAAWgB,EACXb,UAAWqC,GAuBb,OACE,yBAAKhC,UAAU,8BACZW,GACC,yBAAKX,UAAU,0BACZ8C,EAAE,8BAGNO,IACC,yBAAKrD,UAAU,0BACZ8C,EAAE,2BAING,GACC,kBAAC9C,EAAA,EAAa,CACZsG,IAAK/F,EACLgG,YAAalG,EACbW,UAAWA,EACXwF,oCAAoC,EACpCC,cAvCc,WAChB3E,IACFlB,GAAmB,GACnBuC,IAAc,KAqCV6C,gBAAiBA,GACjBR,yBAA0BA,GAC1BK,6BAA8BA,GAC9Ba,SAAUjG,EACVkG,qBAAsBjG,GAErBO,EAAS2F,KAAI,SAACC,GACb,OAxCY,SAACA,GACrB,OACE,kBAAC1G,EAAO,CACNmG,IAAKrB,IAAa6B,aAAaD,GAC/BA,QAASA,EACT1F,iBAAkBA,EAClBC,kBAAmBA,EACnBC,wBAAyBA,EACzBC,uBAAwBA,IAgCbyF,CAAcF,OAK1BhG,GACC,yBACEhB,UAAU,qCACVmH,MAAO7C,IAEN9D,IAIHG,GAAYG,IACZ,2BACEiF,KAAK,OACLqB,KAAK,UACLC,IAAK9D,GACLvD,UAAU,yBACVsH,YAAa9E,EAAiB,GAAKM,EAAE,0BACrCyE,aAAYzE,EAAE,6BACd0E,MAAOrE,GACPsE,UAnNc,SAAClB,GACP,UAAVA,EAAEE,MACJF,EAAEmB,kBACE/G,GACFgD,KAEE7C,IAAsBiD,MACxBF,MAGU,WAAV0C,EAAEE,KACJ3C,MAyMIwC,SAAU,SAACC,GAAC,OAAKnD,GAAemD,EAAEC,OAAOgB,WAI3C7G,GAAY0C,IAAcrC,IAC1B,yBAAKhB,UAAU,uBACZ8C,EAAE,yBAAyB,KAAGA,EAAE,0BAA0B,IAAElB,EAAgB,IAC7E,0BAAMuF,MAAO,CAAEQ,UAAW,WAAY,KAAG9F,EAAgB,OAI3DlB,GAAYG,GAAqBE,IACjC,yBAAKhB,UAAU,qCACb,kBAACH,EAAA,EAAU,CACTG,UAAU,iCACVd,MAAO4D,EAAE,iBACTtD,UAAS,UAAKsD,EAAE,iBAAgB,YAAIA,EAAE,2BACtCxD,QAASwE,KAEVnD,GACC,kBAACZ,EAAA,EAAM,CACLC,UAAU,+BACVd,MAAO4D,EAAE,cACT8E,cAAc,EACdtI,QAASqE,KAGZ7C,GACC,kBAACf,EAAA,EAAM,CACLC,UAAU,+BACVd,MAAO4D,EAAE,eACT8E,cAAc,EACdjI,SAAUoE,KACVzE,QAASuE,KAGZ7C,GACC,kBAACjB,EAAA,EAAM,CACLC,UAAU,+BACVd,MAAO4D,EAAE,eACT8E,cAAc,EACdtI,QAAS,WACP2B,GAAuB,GACvBmB,EAAkB1B,SAUlCgB,EAAe5C,UAAYA,EAEZ4C,QC1VAA,O,qBCFf,IAAItF,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,4XAA6X,M,qBCLtZ,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,8RAA+R,M,qBCLxT,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,2sDAA8sD,KAGvuD0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,29HAA49H,KAGr/H0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,s2CCYvB,IAAMU,EAAY,CAChBkI,QAAShI,IAAU6I,OAAOpH,WAC1Ba,iBAAkBtC,IAAUO,KAC5BgC,kBAAmBvC,IAAUO,KAAKkB,WAClCe,wBAAyBxC,IAAUO,KAAKkB,WACxCgB,uBAAwBzC,IAAUO,KAAKkB,WACvCqH,kBAAmB9I,IAAUO,KAC7BwI,mBAAoB/I,IAAUO,KAC9ByI,kBAAmBhJ,IAAUO,KAC7B0I,WAAYjJ,IAAUY,KACtBsI,iBAAkBlJ,IAAUY,KAC5BuI,mBAAoBnJ,IAAUY,MAG1BU,EAAU8H,sBACd,SAAiB,EAcff,GACA,IAbEL,EAAO,EAAPA,QACA1F,EAAgB,EAAhBA,iBACA2G,EAAU,EAAVA,WACAC,EAAgB,EAAhBA,iBACAC,EAAkB,EAAlBA,mBACAL,EAAiB,EAAjBA,kBACAC,EAAkB,EAAlBA,mBACAC,EAAiB,EAAjBA,kBACAzG,EAAiB,EAAjBA,kBACAC,EAAuB,EAAvBA,wBACAC,EAAsB,EAAtBA,uBAII4G,EAAW5F,aAAY,SAACC,GAAK,OAAKC,IAAU2F,YAAY5F,MAC9D,EAUIf,qBAAWvB,KATbmI,EAAoB,EAApBA,qBACAC,EAAiB,EAAjBA,kBACAC,EAAe,EAAfA,gBACAC,EAAmB,EAAnBA,oBACAC,EAAkB,EAAlBA,mBACA3G,EAAiB,EAAjBA,kBACA4G,EAAwB,EAAxBA,yBACA3G,EAAiB,EAAjBA,kBACAK,EAAc,EAAdA,eAGI5B,EAAc0E,IAAayD,QAAQ7B,GAE6B,IAAlChE,mBAAS4F,GAAyB,GAA/DhI,EAAU,KAAEC,EAAa,KACmB,IAAfmC,oBAAS,GAAM,GAA5CK,EAAU,KAAEC,EAAa,KACyB,IAAfN,oBAAS,GAAM,GAAlD8F,EAAc,KAAEC,EAAe,KAC6B,IAAnB/F,wBAASgG,GAAU,GAA5DC,EAAgB,KAAEC,EAAmB,KAEtCC,EAAWC,cAEXC,EAAa7F,iBAAO,MAC1BsE,EAAkBuB,GAClBtB,EAAmBuB,cAAiB,CAAEC,sBAAsB,IAC5DvB,EAAkBqB,GAClB,IAAMG,EAAUvB,EAAa,GAAM,EACnCwB,8BAAoBpC,GAAK,iBAAO,CAC9BqC,QAAS,kBAAML,EAAWpF,aAG5BD,qBAAU,WAEgB,OAAtBwE,GACGA,IAAsB9H,GACtB8H,EAAkBmB,WAAWjJ,IAEhCG,GAAc,KAEf,CAAC2H,EAAmBG,EAAoB3B,IAE3C4C,2BAAgB,WACd/I,EAAc+H,KACb,CAACA,IAEJgB,2BAAgB,WACdtG,GAAc,GACdyF,GAAgB,GAEZJ,GAAsBH,IAAsB9H,GAC9CG,GAAc,KAEf,CAACwH,IAEJ,IAoBqCwB,GAC7BC,GACAC,GACAC,GAvBFC,GAAgBC,uBAAY,WAChChF,IAAKiF,YAAYnD,GAGbuB,EADJ7H,IAAgB8H,EACS,KACA9H,GAGrBiI,IACFD,GAAoB,GACpBpG,KAGE8H,eACFjB,EAASkB,IAAQC,aAAa,gBAE/B,CAACnB,EAAUZ,EAAsBC,EAAmBG,EAAoB3B,IAErEuD,GAAW9B,EAAgBzB,GASjC,OACE,yBACEK,KAAOsB,GAAsB3G,GAAqBC,EAAqBoH,EAAa,KACpFrJ,UAAU,yBACVmH,MAAO,CAAEqC,YAET,yBAAKxJ,UAAU,oBAAoBmH,MAAO,CAAEqC,QAAStB,EAAmB,EAAI,KAC5E,kBAACsC,EAAA,EAAkB,CACjBxK,UAAWyK,IAAW,CACpB,qCAAqC,EACrC,QAAWpH,GAAcyF,EACzB,SAAYzF,IAAeyF,EAC3B,SAAYyB,KAEdG,SAAU,EACVjD,UAAW,SAAClB,GACA,UAAVA,EAAEE,KAAmBwD,KACrB1D,EAAEmB,mBAEJpI,QAAS,SAACiH,GACRA,EAAEmB,kBACGrE,GAAeyF,GAA+B,IAAbvC,EAAEoE,QACtCzB,EAAoB0B,WAAWX,GAAe,OAGlDrD,cAAe,WACRvD,GAAeyF,GAClB+B,aAAa5B,KAIjB,kBAACvH,EAAA,EAAc,CACblB,KAAMwG,EAAQ8D,UACdpK,YAAaA,EACbI,kBAAmBuC,EACnBtC,mBAAoBuC,EACpBtC,sBAAuB8H,EACvB7H,uBAAwB8H,EACxB5H,UAAW6F,EAAQzC,OA7CUsF,GA6C0B7C,EAAQzC,MA5C/DuF,GAA0B,IAAjBD,GAAa,EACtBE,GAA0B,IAAjBF,GAAa,EACtBG,GAA0B,IAAjBH,GAAa,EACrB,OAAP,OAAcC,GAAM,aAAKC,GAAM,aAAKC,GAAM,MAyCoC,KACxE1I,iBAAkBA,EAClBV,WAAYA,EACZC,cAAeA,EACfU,kBAAmBA,EACnBC,wBAAyBA,EACzBC,uBAAwBA,GAEvBuF,EAAQ+D,gBAIb,yBAAK/K,UAAU,oBAAoBmH,MAAO,CAAEqC,QAASrB,EAAqB,EAAI,KAE7EQ,GAAsB4B,IACrB,kBAACC,EAAA,EAAkB,CAACxK,UAAU,6CAC5B,yBACEA,UAAU,0BACVmH,MAAO,CAAE6D,WAAmD,GAAvC5F,IAAa6F,eAAejE,MAEnD,kBAACtF,EAAA,EAAc,CACbf,UAAU,EACVH,KAAM,GACNU,SAAU,kBAAMwH,GAAoB,WASlDpI,EAAQxB,UAAYA,EAEpB,IAAMoM,EAAgBC,YACpB9M,IAAUC,QACV,CACE8M,MAAK,SAACtL,EAAOuL,EAAmBC,GAC9B,GAAKA,EAAL,CAIA,IAAMC,EAAaF,EAAkBG,UACrC,GAAKD,EAAL,CAIA,IAAQE,EAAgCF,EAAhCE,YAAaC,EAAmBH,EAAnBG,eACJC,EAAgB7L,EAAzBkH,QAEF4E,EAAiBN,EAAoB5B,UAC3C,GAAKgC,GAAmBE,EAAxB,CAKA,GAD4CF,EAAeG,SAASD,GAIlE,OAFAL,EAAWK,oBAAiB5C,OAC5BuC,EAAWO,aAAetN,IAAaI,SAIzC2M,EAAWK,eAAiBA,EAC5B,IAAMG,EAAYN,EAAYO,MACxBC,EAAaN,EAAYK,MAC/B,GAAIP,EAAYS,SAAWP,EAAYO,QAAUH,IAAcE,EAA/D,CAIA,IAAME,EAAyBP,EAAeQ,wBACxCC,GAAiCF,EAAuBG,OAASH,EAAuBI,KAAO,EAE/FC,EADenB,EAAkBoB,kBACAC,EAAIP,EAAuBI,IAClE,QAAQ,GACN,KAAKC,GAAqBH,EAAgCxN,KAAe2N,GAAqBH,EAAgCxN,IAC5H0M,EAAWO,aAAetN,IAAaC,8BACnC4M,EAAkBsB,OAAO,CAAEC,SAAS,KACtChB,EAAeiB,UAAUC,IAAI,aAE/BlC,YAAW,YACLW,aAAU,EAAVA,EAAYK,kBAAmBA,GACjCA,EAAeiB,UAAUE,OAAO,eAEjC,KACH,MACF,KAAKP,EAAoBH,EAAgCxN,IACvD0M,EAAWO,aAAetN,IAAaG,aACvCiN,EAAeiB,UAAUE,OAAO,aAChC,MACF,KAAKP,EAAoBH,EAAgCxN,IACvD0M,EAAWO,aAAetN,IAAaE,aACvCkN,EAAeiB,UAAUE,OAAO,aAChC,MACF,QACExB,EAAWO,aAAetN,IAAaI,QACvCgN,EAAeiB,UAAUE,OAAO,aAGpCC,YAAUC,IAAOC,aACf,CACEC,cAAexB,EACfyB,eAAgB7B,EAAWE,YAC3BK,aAAcP,EAAWO,oBAI/BuB,KAAI,SAACvN,EAAOuL,EAAmBC,GAC7B,GAAKA,EAAL,CAGA,IAAMC,EAAaF,EAAkBG,UAC7BC,EAAgCF,EAAhCE,YAAaG,EAAmBL,EAAnBK,eACJD,EAAoF7L,EAA7FkH,QAAsBzF,EAAuEzB,EAAvEyB,kBAAmBC,EAAoD1B,EAApD0B,wBAAyBC,EAA2B3B,EAA3B2B,uBAE1E,GAAKmK,EAAL,CAIA,OAAQL,EAAWO,cACjB,KAAKtN,IAAaC,8BAChB8C,EAAkBkK,EAAaE,GAC/B,MACF,KAAKnN,IAAaE,aAChB8C,EAAwBiK,EAAaE,GACrC,MACF,KAAKnN,IAAaG,aAChB8C,EAAuBgK,EAAaE,GAMxCC,EAAeiB,UAAUE,OAAO,aAChCC,YAAUC,IAAOK,aACf,CACEH,cAAexB,EACfyB,eAAgB3B,EAChBK,aAAcP,EAAWO,eAG7BP,EAAWO,aAAetN,IAAaI,aAG3C,SAAC2O,EAASC,GAAe,cAAM,CAC7BxF,kBAAmBuF,EAAQE,aAC3BvF,iBAAkBsF,EAAgBb,OAAO,CAAEC,SAAS,MAAqC,QAAzB,EAAAY,EAAgBhC,iBAAS,aAAzB,EAA2BM,gBAAiBtN,IAAaE,aACzHyJ,mBAAoBqF,EAAgBb,OAAO,CAAEC,SAAS,MAAqC,QAAzB,EAAAY,EAAgBhC,iBAAS,aAAzB,EAA2BM,gBAAiBtN,IAAaG,gBAhHzGwM,CAkHpBuC,YACArP,IAAUC,QACV,CACEqP,UAAW,SAAC7N,EAAO8N,EAAmBC,GAAmB,MAAM,CAC7DC,SAAUF,EAAkBE,SAC5BrC,YAAa3L,EAAMkH,QACnB0E,eAAgBmC,EAAoBnE,UACpCoC,aAActN,IAAaI,UAE7BmP,QAAO,WACL,OAAIC,KACFC,QAAQC,KAAK,qDACN,KAEJhJ,IAAKiJ,qBACRF,QAAQC,KAAK,uDACN,MAKb,SAACX,EAASa,GAAe,MAAM,CAC7BtG,kBAAmByF,EAAQc,aAC3BtG,mBAAoBwF,EAAQe,cAC5BrG,WAAYmG,EAAgBnG,gBAxB9ByF,CA0BApN,IAEF4K,EAAcpM,UAAYA,EAEXoM,QC5VA5K,a,+bCETiO,EAAc,CAClBC,SAAU,QACVC,cAAe,OACfC,OAAQ,MACRC,KAAM,EACNpC,IAAK,EACLqC,MAAO,OACPC,OAAQ,QAGJC,EAAgB,SAACC,EAAeC,GACpC,IAAKD,IAAkBC,EACrB,MAAO,CACLC,QAAS,QAGb,IAAQC,EAASF,EAATE,EAAGxC,EAAMsC,EAANtC,EACLyC,EAAY,kBAAH,OAAqBD,EAAC,2BAAmBxC,EAAC,eACzD,MAAO,CACLyC,YACAC,gBAAiBD,IAIRE,EAAoB,WAC/B,MAMIC,aAAa,SAACC,GAAc,MAAM,CACpCC,SAAUD,EAAeE,cACzBC,KAAMH,EAAe/D,UACrBvD,WAAYsH,EAAetH,aAC3B8G,cAAeQ,EAAeI,+BAC9BX,cAAeO,EAAe9C,sBAV9B+C,EAAQ,EAARA,SACAE,EAAI,EAAJA,KACAzH,EAAU,EAAVA,WACA8G,EAAa,EAAbA,cACAC,EAAa,EAAbA,cA4BF,OAAK/G,EAKH,yBAAKd,MAAOoH,GACV,yBACEvO,UAAU,4CACVmH,MAAO2H,EAAcC,EAAeC,IA3BnB,WACrB,IAAKU,EACH,OAAO,KAGT,IAAQjE,EAAgBiE,EAAhBjE,YAER,OAAQ+D,GACN,KAAKnR,IAAUC,QACb,OACE,oCACGmN,EAAYX,WAGnB,QACE,OAAO,MAcN8E,KATE,M,4wBC9DX,8lGAAAnT,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,ygBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SA2BA,IAAMoT,EAAgB,SAAH,GAA2B,QAArBC,cAAM,IAAG,GAAK,EAoBpC,IAXGrN,aACF,SAACC,GAAK,MAAK,CACTC,IAAUoN,kBAAkBrN,EAAO+C,IAAauK,eAChDrN,IAAU2F,YAAY5F,GACtBC,IAAUsN,yBAAyBvN,GACnCC,IAAUiG,yBAAyBlG,GACnCC,IAAUuN,eAAexN,GACzBC,IAAUwN,cAAczN,GACxBC,IAAUC,gBAAgBF,MAE5BG,KACD,GAlBCuN,EAAU,KACV/H,EAAQ,KACRgI,EAAqB,KACrBzH,EAAwB,KACxB0H,EAAW,KACXC,EAAU,KACVC,EAAY,KAcRC,EAAkB,YAClBC,EAAsB,iBACtBC,EAAmB,CAAEzB,EAAG,EAAGxC,EAAG,GAEmC,IAAzB1J,mBAASyN,GAAgB,GAAhE5O,EAAe,KAAE+O,EAAkB,KACgC,IAA1B5N,mBAAS2N,GAAiB,GAAnEE,EAAgB,KAAEC,EAAmB,KACuB,IAArB9N,mBAASsN,GAAY,GAA5D1O,EAAe,KAAEmP,EAAkB,KACqB,IAAf/N,oBAAS,GAAM,GAAxDf,EAAiB,KAAE+O,EAAkB,KACoB,KAAdhO,mBAAS,MAAK,GAAzDwF,GAAiB,MAAED,GAAoB,MACmB,KAAfvF,oBAAS,GAAM,GAA1D2F,GAAkB,MAAED,GAAmB,MACY,KAAZ1F,mBAAS,IAAG,GAAnDlB,GAAe,MAAEC,GAAkB,MAC2B,KAAfiB,oBAAS,GAAM,GAA9DiO,GAAoB,MAAEC,GAAqB,MACa,KAAflO,oBAAS,GAAM,GAAxDhB,GAAiB,MAAEmP,GAAkB,MACgB,KAAZnO,mBAAS,IAAG,GAArDX,GAAgB,MAAE+O,GAAmB,MACtC5O,GAAiBgO,EAAahO,eAE7BM,GAAqB,EAAhBC,cAAgB,GAApB,GACFoG,GAAWC,cACXiI,GAAc7N,iBAAO,MACrB8N,GAAY,+BACZC,GAAOrM,IAAKsM,QAAQF,IACpBG,GAAWjO,mBAEjBoG,2BAAgB,WACdlB,IAAoB,GAEQ,OAAxB2I,GAAYpN,UACdsE,GAAqB8I,GAAYpN,SACjCoN,GAAYpN,QAAU,MAGiC,IAApBoE,EAASlL,QAE5CgU,IAAmB,KAEpB,CAAC9I,IAEJrE,qBAAU,WACRgN,EAAmB9L,IAAKiJ,oBAAsBkC,KAC7C,CAACA,IAEJrM,qBAAU,WACR,IAAM0N,EAAeC,OAAOC,OAAO9P,IAAiB+P,MAAK,SAACrK,GAAK,OAAKA,KACpE0J,GAAsBQ,KACrB,CAAC5P,GAAiBuG,IAErBrE,qBAAU,WACR,IAAM8N,EAAmB,SAACC,GACxBnB,EAAmBmB,EAAmB,OAAIA,EAAWC,cAAc,qBAAuBtB,GAC1FI,EAAoB,CAAE5B,EAAG6C,EAAc,EAAGrF,EAAGqF,EAAc,IAC3DhB,EAAmBgB,EAAuB,aAGtCE,EAA6B,WACjC/M,IAAKoD,aAAY,SAACD,GAChBc,GAASkB,IAAQ6H,YAAY7J,QAI3B8J,EAAmB,WACvB5J,GAAqB,OAMvB,OAHArD,IAAKkN,iBAAiB,wBAAyBN,GAC/ClV,OAAOwV,iBAAiB,0BAA2BH,GACnD/M,IAAKkN,iBAAiB,iBAAkBD,GACjC,WACLjN,IAAKmN,oBAAoB,wBAAyBP,GAClDlV,OAAOyV,oBAAoB,0BAA2BJ,GACtD/M,IAAKmN,oBAAoB,iBAAkBF,MAE5C,IAEHnO,qBAAU,WAER,IAAMsO,EAAmB,SAACC,GACpBA,EAAM/L,OAAOqG,UAAUhB,SAAS,0BAClCtD,GAAqB,MACrB6I,GAAoB,MAQxB,OAJIK,GAASxN,SACXwN,GAASxN,QAAQmO,iBAAiB,QAASE,GAGtC,WACDb,GAASxN,SACXwN,GAASxN,QAAQoO,oBAAoB,QAASC,MAGjD,IAEH,IAAME,GAA4B,SAACC,EAAS,GAAa,IAAXvD,EAAC,EAADA,EAAGxC,EAAC,EAADA,EAG/C,OAFYxH,IAAKwN,oBAAoBC,cAE1BC,qBAAqBH,EAASvD,EAAGxC,IAGxCxK,GAAa,6BAAG,WAAOkF,GAAI,mFAgB9B,GAhB8B,EAChBoL,GAA0B5Q,EAAiBiP,GAApD3B,EAAC,EAADA,EAAGxC,EAAC,EAADA,EACLmG,EAAczL,EACb,CAACqJ,EAAiBC,GAAqBhN,SAAS7B,IAAqBuF,EAE9DA,IACVyL,EAAc/P,GAAE,qBAFhB+P,EAAchR,EAAgBiR,MAAM,EAAG,IAKnCC,EAAM7N,IAAKwN,oBAAoBC,eAE/BK,EAAeD,EAAIE,gBAAgBrR,GAAmB,MACvChF,OAAOsW,KAAKC,aAAaC,MAAQJ,IAAiBpW,OAAOsW,KAAKC,aAAaE,QACxFC,EAAMpE,EACZA,EAAIxC,EACJA,EAAI4G,GAEkB,IAApBjL,EAASlL,OAAY,iCACjBiI,IAAamO,eAAeV,EAAajR,EAAiBsN,EAAGxC,EAAG,GAAE,gDAElEtH,IAAalD,cAAc2Q,EAAarK,GAAmB5G,EAAiBsN,EAAGxC,EAAG,GAAE,QAG5FpK,KAAiB,4CAClB,gBAxBkB,sCA0BbA,GAAiB,WACrBkR,cACAtO,IAAKoD,aAAY,SAACD,GAChBc,GAASkB,IAAQ6H,YAAY7J,OAE/BoL,KACA1R,GAAmB,KAGf8G,GAAU,SAAC7B,GACf,OAAO5B,IAAayD,QAAQ7B,IAGxByM,GAA0B,WAC9BvO,IAAKC,YAAYuO,KACjB9C,EAAmBH,GACnBK,EAAoBH,GACpBI,EAAmBT,GACnBiB,GAAKkC,2BAGDrR,GAAiB,6BAAG,WAAO1B,GAAW,iFAQzC,OARyC,EAC3B8R,GAA0B5Q,EAAiBiP,GAApD3B,EAAC,EAADA,EAAGxC,EAAC,EAADA,EACHqG,EAAM7N,IAAKwN,oBAAoBC,eAC/BK,EAAeD,EAAIE,gBAAgBrR,GAAmB,MACvChF,OAAOsW,KAAKC,aAAaC,MAAQJ,IAAiBpW,OAAOsW,KAAKC,aAAaE,QACxFC,EAAMpE,EACZA,EAAIxC,EACJA,EAAI4G,GACL,SACKlO,IAAauO,sBAAsBjT,EAAakB,EAAiBsN,EAAGxC,EAAG,GAAE,OAC/E2E,GAAYpN,QAAUvD,EACtB4B,KAAiB,2CAClB,gBAZsB,sCAcvB0B,qBAAU,WACJnC,IAAoB4O,GACtBM,EAAmBT,KAEpB,CAACzO,EAAiByO,IAErBtM,qBAAU,WACJ2E,GACFzD,IAAKC,YAAYmM,IAEjBmC,OAED,CAAC9K,KAEJ,IAAMiL,GAA2B,SAACnI,EAAaE,EAAakI,GAC1D,IAAMC,EAAWjL,GAAQ4C,GACnBsI,EAAWlL,GAAQ8C,GACzByF,GAAoB,IAEpByC,EAAcG,KAAK5O,IAAc0O,EAAUC,GAAUE,MAAK,SAACC,GACzD5R,KACA+O,GAAYpN,QAAUiQ,KAExBhP,IAAKiF,YAAYsB,IAGbhK,GAAyB,SAACgK,EAAaE,GAC3CiI,GAAyBnI,EAAaE,EAAavG,IAAa3D,yBAG5DD,GAA0B,SAACiK,EAAaE,GAC5CiI,GAAyBnI,EAAaE,EAAavG,IAAa5D,0BAG5DD,GAAoB,SAACkK,EAAaE,GACtCiI,GAAyBnI,EAAaE,EAAavG,IAAa+O,sBAG5DhS,GAAa,6BAAG,WAAOzB,EAAa0T,GAAO,iFACzChP,IAAaiP,eAAe3T,EAAa0T,GAAQ,OACvD9R,KAAiB,2CAClB,gBAHkB,wCAKbC,GAAc,6BAAG,WAAO+R,GAAgB,uEAC5CA,EAAiBC,OAAOC,UAClBC,EAAsB,CAC1BC,QAAS5R,GAAE,iCACX6R,MAAO7R,GAAE,+BACT8R,eAAgB9R,GAAE,iBAClB+R,UAAW,WAAF,kBAAE,sFACApY,EAAI,EAAC,YAAEA,EAAI6X,EAAiBnX,QAAM,gBACJ,OAA/B2X,EAAYR,EAAiB7X,GAAE,SAC/B2I,IAAa2P,cAAcD,GAAU,OAFArY,IAAG,sBAIhD6F,KACAiG,GAAqB,MACrB6I,GAAoB,IAAI,4CACzB,kDARU,IAUbjI,GAASkB,IAAQ2K,mBAAmBP,IAAsB,2CAC3D,gBAjBmB,sCAmBpB,OAAIrE,EACK,KAIP,yBACEpQ,UAAWyK,IAAW,6CAA8C,CAAE,mBAAoBjI,KAC1FyS,eAAcxP,IAAauK,cAAe3I,IAAKoK,IAE/C,yBAAKzR,UAAU,iCACb,wBAAIA,UAAU,gBACX8C,GAAE,4BAEJb,IACED,GACC,kBAACnC,EAAA,EAAU,CACTG,UAAU,kCACVX,YAAaoG,IAAayP,qBAC1BhW,MAAO4D,GAAE,uCACTnD,SAAUgJ,GACVrJ,QAAS,kBAAM6R,IAAmB,IAClC3R,UAAS,UAAKsD,GAAE,uCAAsC,YAAIA,GAAE,kBAG9D,kBAACjD,EAAA,EAAU,CACTG,UAAU,kCACVX,YAAaoG,IAAayP,qBAC1BhW,MAAO4D,GAAE,uCACTnD,SAAUgJ,IAA0C,IAApBN,EAASlL,OACzCmC,QAAS,WACP6R,IAAmB,GACnBC,GAAoB,KAEtB5R,UAAS,UAAKsD,GAAE,eAAc,YAAIA,GAAE,gCAK5C,kBAAC,IAAeqS,SAAQ,CACtB3N,MAAO,CACL5F,gBAAiB2O,EAAW3O,EAAkB,GAC9CC,kBACA0G,wBACAC,qBACAC,gBAAiB,SAACzB,GAAO,OAAK6B,GAAQ7B,KAAawB,IACnDE,uBACAC,sBACA5G,sBACAD,mBACAO,oBACA4O,wBACAjP,qBACA4G,2BACA3G,oBACAC,iBACAI,kBACAH,iBACAC,qBACAG,oBAGF,kBAAC6S,EAAA,EAAW,CAACC,QAASC,IAAiBC,IAAeC,KACpD,kBAAC,EAAiB,MAElB,yBAAKxV,UAAU,yBACX2I,IAA0C,IAApBN,EAASlL,QAC/B,yBAAK6C,UAAU,+BAA+B8C,GAAE,uBAElD,kBAAC,IAAQ,CAAC2S,KAAMpN,EAAUqN,YAAa,SAAC1J,EAAOhF,GAAO,OACpD,kBAAC1G,EAAA,QAAO,CACNmG,IAAKrB,IAAa6B,aAAaD,GAC/BA,QAASA,EACT1F,iBAAkB,SAAC4S,EAAM1M,GACnBnF,GAAiBsT,MAAK,SAAC3O,GAAO,OAAKA,IAAYkN,KAC5C1M,GACH4J,GAAoB/O,GAAiBuT,QAAO,SAAC5O,GAAO,OAAKA,IAAYkN,MAGnE1M,GACF4J,GAAoB,GAAD,SAAK/O,IAAgB,CAAE6R,MAIhD3S,kBAAmBA,GACnBC,wBAAyBA,GACzBC,uBAAwBA,MAEzBoU,iBAAkB/F,EAASzH,EAASlL,YAAS6L,IAC/CL,IAA4C,OAAtBH,IACrB,kBAACgC,EAAA,EAAkB,CAACxK,UAAU,6CAC5B,kBAAC0B,EAAA,EAAc,CACbf,UAAU,EACVH,KAAM,GACNU,SAAU,kBAAMwH,IAAoB,SAO7CzG,GACC,kBAACuI,EAAA,EAAkB,CACjBxK,UAAU,0BACVX,YAAaoG,IAAaqQ,kCAEzB9T,GACC,oCACE,kBAACjC,EAAA,EAAM,CACLC,UAAU,yBACVjB,IAAI,gBACJS,UAAS,UAAKsD,GAAE,cAAa,YAAIA,GAAE,4BACnCnD,SAAU0C,GAAiBlF,OAAS,GAAKwL,IAAsBsI,GAC/D3R,QAAS,kBAAMoJ,IAAoB,MAErC,kBAAC3I,EAAA,EAAM,CACLC,UAAU,yBACVjB,IAAI,mBACJY,SAAsC,IAA5B0C,GAAiBlF,QAAgB8T,GAC3C3R,QAAS,kBAAMiD,GAAeF,QAIlC,kBAACxC,EAAA,EAAU,CACTG,UAAU,iDACVjB,IAAI,gBACJM,YAAaoG,IAAasQ,uBAC1BpW,SAAUgJ,IAAsBsI,GAChC/R,MAAK,UAAK4D,GAAE,cAAa,YAAIA,GAAE,2BAC/BxD,QAAS,kBAAMoJ,IAAoB,IACnClJ,UAAS,UAAKsD,GAAE,cAAa,YAAIA,GAAE,kCAUpC7C,MAAMC,KAAK2P,GCzZXA", "file": "chunks/chunk.34.js", "sourcesContent": ["var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/index.js!../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../node_modules/sass-loader/dist/cjs.js!./bookmarksOutlinesShared.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "export const ItemTypes = {\n  OUTLINE: 'outline',\n  PORTFOLIO: 'portfolio',\n};\n\nexport const DropLocation = {\n  ON_TARGET_HORIZONTAL_MIDPOINT: 'onTargetHorizontalMidPoint',\n  ABOVE_TARGET: 'aboveTarget',\n  BELOW_TARGET: 'belowTarget',\n  INITIAL: 'initial',\n};\n\nexport const BUFFER_ROOM = 8;", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport Button from '../Button';\n\nimport './TextButton.scss';\n\nconst propTypes = {\n  img: PropTypes.string,\n  label: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  dataElement: PropTypes.string,\n  onClick: PropTypes.func,\n  ariaLabel: PropTypes.string,\n  ariaControls: PropTypes.string,\n  role: PropTypes.string,\n  disabled: PropTypes.bool\n};\n\nconst TextButton = (props) => {\n  const {\n    img,\n    dataElement,\n    onClick,\n    label,\n    ariaLabel,\n    ariaControls,\n    role,\n    disabled\n  } = props;\n\n  return (<Button\n    className='TextButton'\n    img={img}\n    label={label}\n    dataElement={dataElement}\n    onClick={onClick}\n    ariaLabel={ariaLabel}\n    ariaControls={ariaControls}\n    role={role}\n    disabled={disabled}\n  />);\n};\n\nTextButton.propTypes = propTypes;\n\nexport default React.memo(TextButton);", "import TextButton from './TextButton';\n\nexport default TextButton;", "import PanelListItem from './PanelListItem';\n\nexport default PanelListItem;", "import React from 'react';\n\nconst OutlineContext = React.createContext();\n\nexport default OutlineContext;", "exports = module.exports = require(\"../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".bookmark-outline-panel{display:flex;padding-left:var(--padding);padding-right:var(--padding-small)}.bookmark-outline-control-button{width:auto}.bookmark-outline-control-button span{color:inherit}.bookmark-outline-control-button,.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{color:var(--secondary-button-text)}.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{opacity:.5}.bookmark-outline-control-button.disabled span,.bookmark-outline-control-button[disabled] span{color:inherit}.bookmark-outline-control-button:not(.disabled):active,.bookmark-outline-control-button:not(.disabled):hover,.bookmark-outline-control-button:not([disabled]):active,.bookmark-outline-control-button:not([disabled]):hover{color:var(--secondary-button-hover)}.bookmark-outline-panel-header{display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center;padding:var(--padding-tiny);border-bottom:1px solid var(--divider)}.bookmark-outline-panel-header .header-title{font-size:16px}.bookmark-outline-row{flex-grow:1;overflow-y:auto}.msg-no-bookmark-outline{color:var(--placeholder-text);text-align:center}.bookmark-outline-single-container{display:flex;flex-flow:row nowrap;align-items:flex-start;border-radius:4px;margin-left:2px;margin-right:2px}.bookmark-outline-single-container.default{padding:var(--padding-tiny);border:1px solid transparent}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:hover,.bookmark-outline-single-container.default[focus-within]{cursor:pointer}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:focus-within,.bookmark-outline-single-container.default:hover{cursor:pointer}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button,.bookmark-outline-single-container.default[focus-within] .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:focus-within .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default[focus-within]{border-color:transparent}.bookmark-outline-single-container.default:focus-within{border-color:transparent}.bookmark-outline-single-container.default .bookmark-outline-label-row{overflow:hidden}.bookmark-outline-single-container.default.focus-visible,.bookmark-outline-single-container.default:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.editing{background-color:var(--faded-component-background);padding:var(--padding-medium) 20px}.bookmark-outline-single-container.editing.focus-visible,.bookmark-outline-single-container.editing:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.preview{display:inline-flex;margin-top:0;padding:var(--padding-small);background-color:var(--component-background);box-shadow:0 0 3px var(--note-box-shadow)}.bookmark-outline-single-container .bookmark-outline-checkbox{flex-grow:0;flex-shrink:0;margin-top:2px;margin-bottom:2px;margin-right:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-label-row{flex-grow:1;flex-shrink:1;display:flex;flex-flow:row wrap;align-items:flex-start;position:relative;overflow:hidden}.bookmark-outline-single-container .bookmark-outline-label{font-weight:600;flex-grow:1;flex-shrink:1;margin-bottom:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-input,.bookmark-outline-single-container .bookmark-outline-text{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 22px);margin-top:2px;margin-bottom:2px}.bookmark-outline-single-container .bookmark-text-input{margin-left:var(--padding-large)}.bookmark-outline-single-container .bookmark-outline-input{color:var(--text-color);width:calc(100% - var(--padding-large));padding:var(--padding-small);border:1px solid var(--border)}.bookmark-outline-single-container .bookmark-outline-input:focus{border-color:var(--outline-color)}.bookmark-outline-single-container .bookmark-outline-input::-moz-placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-input::placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-more-button{display:none;flex-grow:0;flex-shrink:0;width:16px;height:16px;margin:2px 2px 2px var(--padding-tiny)}.bookmark-outline-single-container .bookmark-outline-more-button .Icon{width:14px;height:14px}.bookmark-outline-single-container .bookmark-outline-more-button.icon-only:hover:not(:disabled):not(.disabled){box-shadow:none;outline:solid 1px var(--hover-border)}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within].icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within.icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within] .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-editing-controls{padding:2px;flex-basis:100%;display:flex;flex-flow:row wrap;justify-content:flex-end;align-items:center;margin-top:var(--padding-medium)}.bookmark-outline-single-container .bookmark-outline-cancel-button,.bookmark-outline-single-container .bookmark-outline-save-button{width:auto;padding:6px var(--padding)}.bookmark-outline-single-container .bookmark-outline-cancel-button{color:var(--secondary-button-text)}.bookmark-outline-single-container .bookmark-outline-cancel-button:hover{color:var(--secondary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button{color:var(--primary-button-text);background-color:var(--primary-button);margin-left:var(--padding-tiny);border-radius:4px}.bookmark-outline-single-container .bookmark-outline-save-button:hover{background-color:var(--primary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button.disabled,.bookmark-outline-single-container .bookmark-outline-save-button:disabled{background-color:var(--primary-button)!important;opacity:.5}.bookmark-outline-single-container .bookmark-outline-save-button.disabled span,.bookmark-outline-single-container .bookmark-outline-save-button:disabled span{color:var(--primary-button-text)}.bookmark-outline-footer{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);display:flex;justify-content:center;align-items:center}.bookmark-outline-footer .add-new-button .Icon{width:14px;height:14px;margin-right:var(--padding-tiny);color:inherit;fill:currentColor}.bookmark-outline-footer .add-new-button.disabled .Icon.disabled,.bookmark-outline-footer .add-new-button.disabled .Icon.disabled path,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled path{color:inherit;fill:currentColor}.bookmark-outline-footer .multi-selection-button{width:auto;padding:7px}.bookmark-outline-footer .multi-selection-button .Icon{width:18px;height:18px}.bookmark-outline-footer .multi-selection-button:not(:first-child){margin-left:var(--padding-tiny)}.bookmark-outline-footer .multi-selection-button:hover{background-color:transparent}.bookmark-outline-footer .multi-selection-button.disabled:hover,.bookmark-outline-footer .multi-selection-button:disabled:hover{box-shadow:none}\", \"\"]);\n\n// exports\n", "import React, { useContext, useEffect, useRef, useState, lazy } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport core from 'core';\nimport selectors from 'selectors';\nimport Button from '../Button';\nimport TextButton from '../TextButton';\nimport { menuTypes } from 'helpers/outlineFlyoutHelper';\nimport OutlineContext from '../Outline/Context';\nimport './OutlineContent.scss';\nimport '../../constants/bookmarksOutlinesShared.scss';\nimport DataElements from 'constants/dataElement';\nimport PanelListItem from '../PanelListItem';\nimport outlineUtils from 'helpers/OutlineUtils';\n\nconst Outline = lazy(() => import('../Outline'));\n\nconst propTypes = {\n  text: PropTypes.string.isRequired,\n  outlinePath: PropTypes.string,\n  isAdding: PropTypes.bool,\n  isExpanded: PropTypes.bool,\n  setIsExpanded: PropTypes.func,\n  isOutlineRenaming: PropTypes.bool,\n  setOutlineRenaming: PropTypes.func,\n  isOutlineChangingDest: PropTypes.bool,\n  setOutlineChangingDest: PropTypes.func,\n  onCancel: PropTypes.func,\n  textColor: PropTypes.string,\n  children: PropTypes.array,\n  setMultiSelected: PropTypes.func,\n  moveOutlineInward: PropTypes.func,\n  moveOutlineBeforeTarget: PropTypes.func,\n  moveOutlineAfterTarget: PropTypes.func,\n};\n\nconst OutlineContent = ({\n  text,\n  outlinePath,\n  isAdding,\n  isExpanded,\n  setIsExpanded,\n  isOutlineRenaming,\n  setOutlineRenaming,\n  isOutlineChangingDest,\n  setOutlineChangingDest,\n  onCancel,\n  textColor,\n  children,\n  setMultiSelected,\n  moveOutlineInward,\n  moveOutlineBeforeTarget,\n  moveOutlineAfterTarget\n}) => {\n  const {\n    currentDestPage,\n    currentDestText,\n    editingOutlines,\n    setEditingOutlines,\n    isMultiSelectMode,\n    isOutlineEditable,\n    addNewOutline,\n    renameOutline,\n    updateOutlineDest,\n    selectedOutlines,\n    updateOutlines,\n    removeOutlines,\n  } = useContext(OutlineContext);\n\n  const featureFlags = useSelector((state) => selectors.getFeatureFlags(state), shallowEqual);\n  const customizableUI = featureFlags.customizableUI;\n\n  const [t] = useTranslation();\n  const TOOL_NAME = 'OutlineDestinationCreateTool';\n\n  const [isDefault, setIsDefault] = useState(false);\n  const [outlineText, setOutlineText] = useState(text);\n  const [isRenaming, setIsRenaming] = useState(false);\n  const inputRef = useRef();\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      e.stopPropagation();\n      if (isAdding) {\n        onAddOutline();\n      }\n      if (isOutlineRenaming && !isRenameButtonDisabled()) {\n        onRenameOutline();\n      }\n    }\n    if (e.key === 'Escape') {\n      onCancelOutline();\n    }\n  };\n\n  const isSelected = selectedOutlines?.includes(outlinePath) || false;\n\n  const onAddOutline = () => {\n    addNewOutline(outlineText.trim() === '' ? '' : outlineText);\n  };\n\n  const onRenameOutline = () => {\n    setOutlineRenaming(false);\n    setIsRenaming(false);\n    renameOutline(outlinePath, outlineText);\n  };\n\n  const onCancelOutline = () => {\n    updateOutlines();\n    if (isOutlineRenaming) {\n      setOutlineRenaming(false);\n      setIsRenaming(false);\n      setOutlineText(text);\n    }\n    if (isOutlineChangingDest) {\n      setOutlineChangingDest(false);\n    }\n    if (isAdding) {\n      onCancel();\n    }\n  };\n\n  const isRenameButtonDisabled = () => {\n    return !outlineText || text === outlineText;\n  };\n\n  useEffect(() => {\n    if (outlineText !== text) {\n      setOutlineText(text);\n    }\n  }, [text]);\n\n  useEffect(() => {\n    if (isAdding || isOutlineRenaming) {\n      inputRef.current.focus();\n      inputRef.current.select();\n    }\n\n    setIsDefault(!isAdding && !isOutlineRenaming && !isOutlineChangingDest);\n  }, [isOutlineRenaming, isOutlineChangingDest]);\n\n  useEffect(() => {\n    const editingOutlinesClone = { ...editingOutlines };\n    const isOutlineEditing = isOutlineRenaming || isOutlineChangingDest;\n    if (isOutlineEditing) {\n      editingOutlinesClone[outlinePath] = (isOutlineEditing);\n    } else {\n      delete editingOutlinesClone[outlinePath];\n    }\n    setEditingOutlines({ ...editingOutlinesClone });\n  }, [isOutlineRenaming, isOutlineChangingDest]);\n\n  const textStyle = {\n    color: textColor || 'auto'\n  };\n\n  const handleOnClick = async (val) => {\n    switch (val) {\n      case menuTypes.RENAME:\n        setOutlineRenaming(true);\n        setIsRenaming(true);\n        break;\n      case menuTypes.SETDEST:\n        setOutlineChangingDest(true);\n        core.setToolMode(TOOL_NAME);\n        break;\n      case menuTypes.DELETE:\n        removeOutlines([outlinePath]);\n        break;\n      case menuTypes.MOVE_UP: {\n        await outlineUtils.moveOutlineUp(outlinePath);\n        updateOutlines();\n        break;\n      }\n      case menuTypes.MOVE_DOWN: {\n        await outlineUtils.moveOutlineDown(outlinePath);\n        updateOutlines();\n        break;\n      }\n      case menuTypes.MOVE_LEFT: {\n        await outlineUtils.moveOutlineOutward(outlinePath);\n        updateOutlines();\n        break;\n      }\n      case menuTypes.MOVE_RIGHT: {\n        await outlineUtils.moveOutlineInward(outlinePath);\n        updateOutlines();\n        break;\n      }\n      default:\n        break;\n    }\n  };\n\n  const flyoutSelector = DataElements.BOOKMARK_OUTLINE_FLYOUT;\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n  const type = 'outline';\n\n  const contentMenuFlyoutOptions = {\n    shouldHideDeleteButton: false,\n    currentFlyout: currentFlyout,\n    flyoutSelector: flyoutSelector,\n    type: type,\n    handleOnClick: handleOnClick\n  };\n\n  const contextMenuMoreButtonOptions = {\n    flyoutToggleElement: 'bookmarkOutlineFlyout',\n    moreOptionsDataElement: `outline-more-button-${outlinePath}`,\n  };\n\n  const checkboxOptions = {\n    id:`outline-checkbox-${outlinePath}`,\n    checked: isSelected,\n    onChange: (e) => {\n      setMultiSelected(outlinePath, e.target.checked);\n    },\n    ariaLabel: text,\n    disabled: !isMultiSelectMode\n  };\n\n  const onDoubleClick = () => {\n    if (isOutlineEditable) {\n      setOutlineRenaming(true);\n      setIsRenaming(true);\n    }\n  };\n\n  const renderContent = (outline) => {\n    return (\n      <Outline\n        key={outlineUtils.getOutlineId(outline)}\n        outline={outline}\n        setMultiSelected={setMultiSelected}\n        moveOutlineInward={moveOutlineInward}\n        moveOutlineBeforeTarget={moveOutlineBeforeTarget}\n        moveOutlineAfterTarget={moveOutlineAfterTarget}\n      />\n    );\n  };\n\n  return (\n    <div className=\"bookmark-outline-label-row\">\n      {isAdding &&\n        <div className=\"bookmark-outline-label\">\n          {t('component.newOutlineTitle')}\n        </div>\n      }\n      {isRenaming &&\n        <div className=\"bookmark-outline-label\">\n          {t('component.outlineTitle')}\n        </div>\n      }\n\n      {isDefault &&\n        <PanelListItem\n          key={outlinePath}\n          labelHeader={text}\n          textColor={textColor}\n          enableMoreOptionsContextMenuFlyout={true}\n          onDoubleClick={onDoubleClick}\n          checkboxOptions={checkboxOptions}\n          contentMenuFlyoutOptions={contentMenuFlyoutOptions}\n          contextMenuMoreButtonOptions={contextMenuMoreButtonOptions}\n          expanded={isExpanded}\n          setIsExpandedHandler={setIsExpanded}\n        >\n          {children.map((outline) => {\n            return renderContent(outline);\n          })}\n        </PanelListItem>\n      }\n\n      {isOutlineChangingDest &&\n        <div\n          className=\"bookmark-outline-text outline-text\"\n          style={textStyle}\n        >\n          {text}\n        </div>\n      }\n\n      {(isAdding || isOutlineRenaming) &&\n        <input\n          type=\"text\"\n          name=\"outline\"\n          ref={inputRef}\n          className=\"bookmark-outline-input\"\n          placeholder={customizableUI ? '' : t('component.outlineTitle')}\n          aria-label={t('component.newOutlineTitle')}\n          value={outlineText}\n          onKeyDown={handleKeyDown}\n          onChange={(e) => setOutlineText(e.target.value)}\n        />\n      }\n\n      {(isAdding || isRenaming || isOutlineChangingDest) &&\n        <div className=\"outline-destination\">\n          {t('component.destination')}: {t('component.bookmarkPage')} {currentDestPage},\n          <span style={{ fontStyle: 'italic' }}> “{currentDestText}”</span>\n        </div>\n      }\n\n      {(isAdding || isOutlineRenaming || isOutlineChangingDest) &&\n        <div className=\"bookmark-outline-editing-controls\">\n          <TextButton\n            className=\"bookmark-outline-cancel-button\"\n            label={t('action.cancel')}\n            ariaLabel={`${t('action.cancel')} ${t('component.outlineTitle')}`}\n            onClick={onCancelOutline}\n          />\n          {isAdding &&\n            <Button\n              className=\"bookmark-outline-save-button\"\n              label={t('action.add')}\n              isSubmitType={true}\n              onClick={onAddOutline}\n            />\n          }\n          {isOutlineRenaming &&\n            <Button\n              className=\"bookmark-outline-save-button\"\n              label={t('action.save')}\n              isSubmitType={true}\n              disabled={isRenameButtonDisabled()}\n              onClick={onRenameOutline}\n            />\n          }\n          {isOutlineChangingDest &&\n            <Button\n              className=\"bookmark-outline-save-button\"\n              label={t('action.save')}\n              isSubmitType={true}\n              onClick={() => {\n                setOutlineChangingDest(false);\n                updateOutlineDest(outlinePath);\n              }}\n            />\n          }\n        </div>\n      }\n    </div>\n  );\n};\n\nOutlineContent.propTypes = propTypes;\n\nexport default OutlineContent;\n", "import OutlineContent from './OutlineContent';\n\nexport default OutlineContent;\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./TextButton.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".TextButton{color:var(--secondary-button-text);width:-moz-fit-content;width:fit-content;padding-left:8px;padding-right:8px}.TextButton .Icon{display:flex;align-items:center}.TextButton svg{color:var(--secondary-button-text);height:14px;width:14px}.TextButton:hover{box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./OutlineContent.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".outline-destination,.outline-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.outline-destination{flex-basis:100%;font-size:10px;color:var(--faded-text);margin-top:var(--padding-small)}.bookmark-outline-label-row .ToggleElementButton .Button{padding:0;min-width:16px}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Outline.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.outline-drag-container{border-radius:4px}.outline-drag-container.isNesting>.bookmark-outline-single-container{background-color:var(--popup-button-active);border-color:var(--focus-border)}.outline-treeview-toggle{flex-grow:0;flex-shrink:0;margin-right:var(--padding-small);margin-top:2px;margin-bottom:2px;min-width:14px;transition:transform .1s ease}.outline-treeview-toggle.expanded{transform:rotate(90deg)}.outline-treeview-toggle .Button{width:auto;height:auto}.outline-treeview-toggle .Button .Icon{width:16px;height:16px}.outline-drag-line{margin-left:var(--padding);margin-right:var(--padding);border-top:1px solid var(--focus-border);position:relative}.outline-drag-line:before{content:\\\"\\\";display:block;position:absolute;width:5px;height:5px;top:-3px;left:0;background-color:var(--focus-border);border-radius:50%}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./OutlinesPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel{margin:16px;padding:0;overflow-y:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel{margin:16px;padding:0;overflow-y:auto}}.OutlinesPanel .bookmark-outline-single-container:not(.editing){height:-moz-fit-content;height:fit-content}.OutlinesPanel .bookmark-outline-row{padding-top:6px}.OutlinesPanel .msg-no-bookmark-outline{margin-top:6px}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .panel-list-label-header .Button{width:-moz-fit-content;width:fit-content;height:100%;padding:0 8px;justify-content:start}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .panel-list-label-header .Button:hover{cursor:pointer;border:none;border-radius:4px;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .panel-list-label-header .Button:focus{border-radius:4px;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-more-button.active{display:flex}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-more-button.active .Icon{color:var(--blue-5)}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container.default.hover:not(.selected),.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container.default:hover:not(.selected){background:none}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container.focus-visible,.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container:focus-visible{outline:var(--focus-visible-outline)!important}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-input{font-size:13px}}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-input:active{border-color:var(--focus-border)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel .bookmark-outline-cancel-button,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel .bookmark-outline-control-button,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel .bookmark-outline-save-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel .bookmark-outline-cancel-button,.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel .bookmark-outline-control-button,.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel .bookmark-outline-save-button{font-size:13px}}.Panel.OutlinesPanel{overflow:auto;-webkit-overflow-scrolling:touch;flex:1;flex-direction:column}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useCallback, useContext, useEffect, useLayoutEffect, forwardRef, useImperativeHandle, useRef } from 'react';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { DragSource, DropTarget } from 'react-dnd';\nimport { getEmptyImage } from 'react-dnd-html5-backend';\nimport { ItemTypes, DropLocation, BUFFER_ROOM } from 'constants/dnd';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\n\nimport Events from 'constants/events';\nimport fireEvent from 'helpers/fireEvent';\nimport outlineUtils from 'helpers/OutlineUtils';\nimport { isMobile, isIE } from 'helpers/device';\nimport OutlineContext from './Context';\nimport OutlineContent from 'src/components/OutlineContent';\nimport DataElementWrapper from '../DataElementWrapper';\n\nimport './Outline.scss';\nimport '../../constants/bookmarksOutlinesShared.scss';\n\nconst propTypes = {\n  outline: PropTypes.object.isRequired,\n  setMultiSelected: PropTypes.func,\n  moveOutlineInward: PropTypes.func.isRequired,\n  moveOutlineBeforeTarget: PropTypes.func.isRequired,\n  moveOutlineAfterTarget: PropTypes.func.isRequired,\n  connectDragSource: PropTypes.func,\n  connectDragPreview: PropTypes.func,\n  connectDropTarget: PropTypes.func,\n  isDragging: PropTypes.bool,\n  isDraggedUpwards: PropTypes.bool,\n  isDraggedDownwards: PropTypes.bool,\n};\n\nconst Outline = forwardRef(\n  function Outline(\n    {\n      outline,\n      setMultiSelected,\n      isDragging,\n      isDraggedUpwards,\n      isDraggedDownwards,\n      connectDragSource,\n      connectDragPreview,\n      connectDropTarget,\n      moveOutlineInward,\n      moveOutlineBeforeTarget,\n      moveOutlineAfterTarget\n    },\n    ref\n  ) {\n    const outlines = useSelector((state) => selectors.getOutlines(state));\n    const {\n      setActiveOutlinePath,\n      activeOutlinePath,\n      isOutlineActive,\n      setAddingNewOutline,\n      isAddingNewOutline,\n      isMultiSelectMode,\n      shouldAutoExpandOutlines,\n      isOutlineEditable,\n      updateOutlines,\n    } = useContext(OutlineContext);\n\n    const outlinePath = outlineUtils.getPath(outline);\n\n    const [isExpanded, setIsExpanded] = useState(shouldAutoExpandOutlines);\n    const [isRenaming, setIsRenaming] = useState(false);\n    const [isChangingDest, setChangingDest] = useState(false);\n    const [clearSingleClick, setClearSingleClick] = useState(undefined);\n\n    const dispatch = useDispatch();\n\n    const elementRef = useRef(null);\n    connectDragSource(elementRef);\n    connectDragPreview(getEmptyImage(), { captureDraggingState: true });\n    connectDropTarget(elementRef);\n    const opacity = isDragging ? 0.5 : 1;\n    useImperativeHandle(ref, () => ({\n      getNode: () => elementRef.current,\n    }));\n\n    useEffect(() => {\n      const shouldExpandOutline =\n        activeOutlinePath !== null\n        && activeOutlinePath !== outlinePath\n        && activeOutlinePath.startsWith(outlinePath);\n      if (shouldExpandOutline) {\n        setIsExpanded(true);\n      }\n    }, [activeOutlinePath, isAddingNewOutline, outline]);\n\n    useLayoutEffect(() => {\n      setIsExpanded(shouldAutoExpandOutlines);\n    }, [shouldAutoExpandOutlines]);\n\n    useLayoutEffect(() => {\n      setIsRenaming(false);\n      setChangingDest(false);\n\n      if (isAddingNewOutline && activeOutlinePath === outlinePath) {\n        setIsExpanded(true);\n      }\n    }, [outlines]);\n\n    const onSingleClick = useCallback(() => {\n      core.goToOutline(outline);\n\n      outlinePath === activeOutlinePath\n        ? setActiveOutlinePath(null)\n        : setActiveOutlinePath(outlinePath);\n\n\n      if (isAddingNewOutline) {\n        setAddingNewOutline(false);\n        updateOutlines();\n      }\n\n      if (isMobile()) {\n        dispatch(actions.closeElement('leftPanel'));\n      }\n    }, [dispatch, setActiveOutlinePath, activeOutlinePath, isAddingNewOutline, outline]);\n\n    const isActive = isOutlineActive(outline);\n\n    const convertRgbObjectToRgbString = (rgbObject) => {\n      const rValue = rgbObject['r'] * 255;\n      const gValue = rgbObject['g'] * 255;\n      const bValue = rgbObject['b'] * 255;\n      return `rgb(${rValue}, ${gValue}, ${bValue})`;\n    };\n\n    return (\n      <div\n        ref={(!isAddingNewOutline && isMultiSelectMode && isOutlineEditable) ? elementRef : null}\n        className=\"outline-drag-container\"\n        style={{ opacity }}\n      >\n        <div className=\"outline-drag-line\" style={{ opacity: isDraggedUpwards ? 1 : 0 }} />\n        <DataElementWrapper\n          className={classNames({\n            'bookmark-outline-single-container': true,\n            'editing': isRenaming || isChangingDest,\n            'default': !isRenaming && !isChangingDest,\n            'selected': isActive,\n          })}\n          tabIndex={0}\n          onKeyDown={(e) => {\n            e.key === 'Enter' && onSingleClick();\n            e.stopPropagation();\n          }}\n          onClick={(e) => {\n            e.stopPropagation();\n            if (!isRenaming && !isChangingDest && e.detail === 1) {\n              setClearSingleClick(setTimeout(onSingleClick, 300));\n            }\n          }}\n          onDoubleClick={() => {\n            if (!isRenaming && !isChangingDest) {\n              clearTimeout(clearSingleClick);\n            }\n          }}\n        >\n          <OutlineContent\n            text={outline.getName()}\n            outlinePath={outlinePath}\n            isOutlineRenaming={isRenaming}\n            setOutlineRenaming={setIsRenaming}\n            isOutlineChangingDest={isChangingDest}\n            setOutlineChangingDest={setChangingDest}\n            textColor={outline.color ? convertRgbObjectToRgbString(outline.color) : null}\n            setMultiSelected={setMultiSelected}\n            isExpanded={isExpanded}\n            setIsExpanded={setIsExpanded}\n            moveOutlineInward={moveOutlineInward}\n            moveOutlineBeforeTarget={moveOutlineBeforeTarget}\n            moveOutlineAfterTarget={moveOutlineAfterTarget}\n          >\n            {outline.getChildren()}\n          </OutlineContent>\n        </DataElementWrapper>\n\n        <div className=\"outline-drag-line\" style={{ opacity: isDraggedDownwards ? 1 : 0 }} />\n\n        {isAddingNewOutline && isActive && (\n          <DataElementWrapper className=\"bookmark-outline-single-container editing\">\n            <div\n              className=\"outline-treeview-toggle\"\n              style={{ marginLeft: outlineUtils.getNestedLevel(outline) * 12 }}\n            ></div>\n            <OutlineContent\n              isAdding={true}\n              text={''}\n              onCancel={() => setAddingNewOutline(false)}\n            />\n          </DataElementWrapper>\n        )}\n      </div>\n    );\n  }\n);\n\nOutline.propTypes = propTypes;\n\nconst OutlineNested = DropTarget(\n  ItemTypes.OUTLINE,\n  {\n    hover(props, dropTargetMonitor, dropTargetContainer) {\n      if (!dropTargetContainer) {\n        return;\n      }\n\n      const dragObject = dropTargetMonitor.getItem();\n      if (!dragObject) {\n        return;\n      }\n\n      const { dragOutline, dragSourceNode } = dragObject;\n      const { outline: dropOutline } = props;\n\n      const dropTargetNode = dropTargetContainer.getNode();\n      if (!dragSourceNode || !dropTargetNode) {\n        return;\n      }\n\n      const outlineIsBeingDraggedIntoDescendant = dragSourceNode.contains(dropTargetNode);\n      if (outlineIsBeingDraggedIntoDescendant) {\n        dragObject.dropTargetNode = undefined;\n        dragObject.dropLocation = DropLocation.INITIAL;\n        return;\n      }\n\n      dragObject.dropTargetNode = dropTargetNode;\n      const dragIndex = dragOutline.index;\n      const hoverIndex = dropOutline.index;\n      if (dragOutline.parent === dropOutline.parent && dragIndex === hoverIndex) {\n        return;\n      }\n\n      const dropTargetBoundingRect = dropTargetNode.getBoundingClientRect();\n      const dropTargetVerticalMiddlePoint = (dropTargetBoundingRect.bottom - dropTargetBoundingRect.top) / 2;\n      const clientOffset = dropTargetMonitor.getClientOffset();\n      const dropTargetClientY = clientOffset.y - dropTargetBoundingRect.top;\n      switch (true) {\n        case dropTargetClientY <= dropTargetVerticalMiddlePoint + BUFFER_ROOM && dropTargetClientY >= dropTargetVerticalMiddlePoint - BUFFER_ROOM:\n          dragObject.dropLocation = DropLocation.ON_TARGET_HORIZONTAL_MIDPOINT;\n          if (dropTargetMonitor.isOver({ shallow: true })) {\n            dropTargetNode.classList.add('isNesting');\n          }\n          setTimeout(() => {\n            if (dragObject?.dropTargetNode !== dropTargetNode) {\n              dropTargetNode.classList.remove('isNesting');\n            }\n          }, 100);\n          break;\n        case dropTargetClientY > dropTargetVerticalMiddlePoint + BUFFER_ROOM:\n          dragObject.dropLocation = DropLocation.BELOW_TARGET;\n          dropTargetNode.classList.remove('isNesting');\n          break;\n        case dropTargetClientY < dropTargetVerticalMiddlePoint - BUFFER_ROOM:\n          dragObject.dropLocation = DropLocation.ABOVE_TARGET;\n          dropTargetNode.classList.remove('isNesting');\n          break;\n        default:\n          dragObject.dropLocation = DropLocation.INITIAL;\n          dropTargetNode.classList.remove('isNesting');\n          break;\n      }\n      fireEvent(Events.DRAG_OUTLINE,\n        {\n          targetOutline: dropOutline,\n          draggedOutline: dragObject.dragOutline,\n          dropLocation: dragObject.dropLocation\n        }\n      );\n    },\n    drop(props, dropTargetMonitor, dropTargetContainer) {\n      if (!dropTargetContainer) {\n        return;\n      }\n      const dragObject = dropTargetMonitor.getItem();\n      const { dragOutline, dropTargetNode } = dragObject;\n      const { outline: dropOutline, moveOutlineInward, moveOutlineBeforeTarget, moveOutlineAfterTarget } = props;\n\n      if (!dropTargetNode) {\n        return;\n      }\n\n      switch (dragObject.dropLocation) {\n        case DropLocation.ON_TARGET_HORIZONTAL_MIDPOINT:\n          moveOutlineInward(dragOutline, dropOutline);\n          break;\n        case DropLocation.ABOVE_TARGET:\n          moveOutlineBeforeTarget(dragOutline, dropOutline);\n          break;\n        case DropLocation.BELOW_TARGET:\n          moveOutlineAfterTarget(dragOutline, dropOutline);\n          break;\n        default:\n          break;\n      }\n\n      dropTargetNode.classList.remove('isNesting');\n      fireEvent(Events.DROP_OUTLINE,\n        {\n          targetOutline: dropOutline,\n          draggedOutline: dragOutline,\n          dropLocation: dragObject.dropLocation\n        }\n      );\n      dragObject.dropLocation = DropLocation.INITIAL;\n    }\n  },\n  (connect, dropTargetState) => ({\n    connectDropTarget: connect.dropTarget(),\n    isDraggedUpwards: dropTargetState.isOver({ shallow: true }) && (dropTargetState.getItem()?.dropLocation === DropLocation.ABOVE_TARGET),\n    isDraggedDownwards: dropTargetState.isOver({ shallow: true }) && (dropTargetState.getItem()?.dropLocation === DropLocation.BELOW_TARGET),\n  })\n)(DragSource(\n  ItemTypes.OUTLINE,\n  {\n    beginDrag: (props, dragSourceMonitor, dragSourceContainer) => ({\n      sourceId: dragSourceMonitor.sourceId,\n      dragOutline: props.outline,\n      dragSourceNode: dragSourceContainer.getNode(),\n      dropLocation: DropLocation.INITIAL,\n    }),\n    canDrag() {\n      if (isIE) {\n        console.warn('Drag and drop outlines for IE11 is not supported');\n        return false;\n      }\n      if (!core.isFullPDFEnabled()) {\n        console.warn('Full API must be enabled to drag and drop outlines');\n        return false;\n      }\n      return true;\n    }\n  },\n  (connect, dragSourceState) => ({\n    connectDragSource: connect.dragSource(),\n    connectDragPreview: connect.dragPreview(),\n    isDragging: dragSourceState.isDragging(),\n  })\n)(Outline));\n\nOutlineNested.propTypes = propTypes;\n\nexport default OutlineNested;\n", "import Outline from './Outline';\n\nexport default Outline;\n", "import React from 'react';\nimport { useDragLayer } from 'react-dnd';\nimport { ItemTypes } from 'constants/dnd';\n\nconst layerStyles = {\n  position: 'fixed',\n  pointerEvents: 'none',\n  zIndex: 99999,\n  left: 0,\n  top: 0,\n  width: '100%',\n  height: '100%'\n};\n\nconst getItemStyles = (initialOffset, currentOffset) => {\n  if (!initialOffset || !currentOffset) {\n    return {\n      display: 'none'\n    };\n  }\n  const { x, y } = currentOffset;\n  const transform = `translate(calc(${x}px - 50%), calc(${y}px - 100%))`;\n  return {\n    transform,\n    WebkitTransform: transform,\n  };\n};\n\nexport const OutlinesDragLayer = () => {\n  const {\n    itemType,\n    item,\n    isDragging,\n    initialOffset,\n    currentOffset\n  } = useDragLayer((dragLayerState) => ({\n    itemType: dragLayerState.getItemType(),\n    item: dragLayerState.getItem(),\n    isDragging: dragLayerState.isDragging(),\n    initialOffset: dragLayerState.getInitialSourceClientOffset(),\n    currentOffset: dragLayerState.getClientOffset(),\n  }));\n\n  const renderDragItem = () => {\n    if (!item) {\n      return null;\n    }\n\n    const { dragOutline } = item;\n\n    switch (itemType) {\n      case ItemTypes.OUTLINE:\n        return (\n          <>\n            {dragOutline.getName()}\n          </>\n        );\n      default:\n        return null;\n    }\n  };\n\n  if (!isDragging) {\n    return null;\n  }\n\n  return (\n    <div style={layerStyles}>\n      <div\n        className=\"bookmark-outline-single-container preview\"\n        style={getItemStyles(initialOffset, currentOffset)}\n      >\n        {renderDragItem()}\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useLayoutEffect, useRef, useEffect } from 'react';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport { DndProvider } from 'react-dnd';\nimport { isMobileDevice } from 'helpers/device';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport TouchBackEnd from 'react-dnd-touch-backend';\nimport Outline from 'components/Outline';\nimport OutlineContext from 'components/Outline/Context';\nimport Button from 'components/Button';\nimport TextButton from '../TextButton';\nimport OutlineContent from 'components/OutlineContent';\nimport DataElementWrapper from 'components/DataElementWrapper';\n\nimport core from 'core';\nimport outlineUtils from 'helpers/OutlineUtils';\nimport { shouldEndAccessibleReadingOrderMode } from 'helpers/accessibility';\nimport DataElements from 'constants/dataElement';\nimport defaultTool from 'constants/defaultTool';\nimport actions from 'actions';\nimport selectors from 'selectors';\n\nimport '../../constants/bookmarksOutlinesShared.scss';\nimport './OutlinesPanel.scss';\nimport { OutlinesDragLayer } from './OutlinesDragLayer';\nimport classNames from 'classnames';\nimport { Virtuoso } from 'react-virtuoso';\n\nconst OutlinesPanel = ({ isTest = false }) => {\n  const [\n    isDisabled,\n    outlines,\n    outlineEditingEnabled,\n    shouldAutoExpandOutlines,\n    currentPage,\n    pageLabels,\n    featureFlags,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementDisabled(state, DataElements.OUTLINE_PANEL),\n      selectors.getOutlines(state),\n      selectors.getOutlineEditingEnabled(state),\n      selectors.shouldAutoExpandOutlines(state),\n      selectors.getCurrentPage(state),\n      selectors.getPageLabels(state),\n      selectors.getFeatureFlags(state),\n    ],\n    shallowEqual,\n  );\n\n  const defaultDestText = 'Full Page';\n  const areaDestinationText = 'Area Selection';\n  const defaultDestCoord = { x: 0, y: 0 };\n\n  const [currentDestText, setCurrentDestText] = useState(defaultDestText);\n  const [currentDestCoord, setCurrentDestCoord] = useState(defaultDestCoord);\n  const [currentDestPage, setCurrentDestPage] = useState(currentPage);\n  const [isOutlineEditable, setOutlineEditable] = useState(false);\n  const [activeOutlinePath, setActiveOutlinePath] = useState(null);\n  const [isAddingNewOutline, setAddingNewOutline] = useState(false);\n  const [editingOutlines, setEditingOutlines] = useState({});\n  const [isAnyOutlineRenaming, setAnyOutlineRenaming] = useState(false);\n  const [isMultiSelectMode, setMultiSelectMode] = useState(false);\n  const [selectedOutlines, setSelectedOutlines] = useState([]);\n  const customizableUI = featureFlags.customizableUI;\n\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const nextPathRef = useRef(null);\n  const TOOL_NAME = 'OutlineDestinationCreateTool';\n  const tool = core.getTool(TOOL_NAME);\n  const panelRef = useRef();\n\n  useLayoutEffect(() => {\n    setAddingNewOutline(false);\n\n    if (nextPathRef.current !== null) {\n      setActiveOutlinePath(nextPathRef.current);\n      nextPathRef.current = null;\n    }\n\n    const shouldResetMultiSelectedMode = outlines.length === 0;\n    if (shouldResetMultiSelectedMode) {\n      setMultiSelectMode(false);\n    }\n  }, [outlines]);\n\n  useEffect(() => {\n    setOutlineEditable(core.isFullPDFEnabled() && outlineEditingEnabled);\n  }, [outlineEditingEnabled]);\n\n  useEffect(() => {\n    const isAnyEditing = Object.values(editingOutlines).some((value) => value);\n    setAnyOutlineRenaming(isAnyEditing);\n  }, [editingOutlines, outlines]);\n\n  useEffect(() => {\n    const onSetDestination = (annotation) => {\n      setCurrentDestText(annotation['IsText'] ? annotation.getCustomData('trn-annot-preview') : areaDestinationText);\n      setCurrentDestCoord({ x: annotation['X'], y: annotation['Y'] });\n      setCurrentDestPage(annotation['PageNumber']);\n    };\n\n    const onOutlinesBookmarksChanged = () => {\n      core.getOutlines((outlines) => {\n        dispatch(actions.setOutlines(outlines));\n      });\n    };\n\n    const onDocumentLoaded = () => {\n      setActiveOutlinePath(null);\n    };\n\n    core.addEventListener('outlineSetDestination', onSetDestination);\n    window.addEventListener('outlineBookmarksChanged', onOutlinesBookmarksChanged);\n    core.addEventListener('documentLoaded', onDocumentLoaded);\n    return () => {\n      core.removeEventListener('outlineSetDestination', onSetDestination);\n      window.removeEventListener('outlineBookmarksChanged', onOutlinesBookmarksChanged);\n      core.removeEventListener('documentLoaded', onDocumentLoaded);\n    };\n  }, []);\n\n  useEffect(() => {\n    // deselect outlines when clicking inside of the outline panel\n    const handlePanelClick = (event) => {\n      if (event.target.classList.contains('bookmark-outline-row')) {\n        setActiveOutlinePath(null);\n        setSelectedOutlines([]);\n      }\n    };\n\n    if (panelRef.current) {\n      panelRef.current.addEventListener('click', handlePanelClick);\n    }\n\n    return () => {\n      if (panelRef.current) {\n        panelRef.current.removeEventListener('click', handlePanelClick);\n      }\n    };\n  }, []);\n\n  const getCurrentDestViewerCoord = (pageNum, { x, y }) => {\n    const doc = core.getDocumentViewer().getDocument();\n    // convert annotation coordinates to viewerCoordinates because PDFNet used PDF coordinates\n    return doc.getViewerCoordinates(pageNum, x, y);\n  };\n\n  const addNewOutline = async (name) => {\n    let { x, y } = getCurrentDestViewerCoord(currentDestPage, currentDestCoord);\n    let outlineName = name;\n    if (![defaultDestText, areaDestinationText].includes(currentDestText) && !name) {\n      outlineName = currentDestText.slice(0, 40);\n    } else if (!name) {\n      outlineName = t('message.untitled');\n    }\n\n    const doc = core.getDocumentViewer().getDocument();\n\n    const pageRotation = doc.getPageRotation(currentDestPage) / 90;\n    if (pageRotation === window.Core.PageRotation.E_90 || pageRotation === window.Core.PageRotation.E_270) {\n      const tmp = x;\n      x = y;\n      y = tmp;\n    }\n    if (outlines.length === 0) {\n      await outlineUtils.addRootOutline(outlineName, currentDestPage, x, y, 0);\n    } else {\n      await outlineUtils.addNewOutline(outlineName, activeOutlinePath, currentDestPage, x, y, 0);\n    }\n\n    updateOutlines();\n  };\n\n  const updateOutlines = () => {\n    shouldEndAccessibleReadingOrderMode();\n    core.getOutlines((outlines) => {\n      dispatch(actions.setOutlines(outlines));\n    });\n    clearOutlineDestination();\n    setEditingOutlines({});\n  };\n\n  const getPath = (outline) => {\n    return outlineUtils.getPath(outline);\n  };\n\n  const clearOutlineDestination = () => {\n    core.setToolMode(defaultTool);\n    setCurrentDestText(defaultDestText);\n    setCurrentDestCoord(defaultDestCoord);\n    setCurrentDestPage(currentPage);\n    tool.clearOutlineDestination();\n  };\n\n  const updateOutlineDest = async (outlinePath) => {\n    let { x, y } = getCurrentDestViewerCoord(currentDestPage, currentDestCoord);\n    const doc = core.getDocumentViewer().getDocument();\n    const pageRotation = doc.getPageRotation(currentDestPage) / 90;\n    if (pageRotation === window.Core.PageRotation.E_90 || pageRotation === window.Core.PageRotation.E_270) {\n      const tmp = x;\n      x = y;\n      y = tmp;\n    }\n    await outlineUtils.setOutlineDestination(outlinePath, currentDestPage, x, y, 0);\n    nextPathRef.current = outlinePath;\n    updateOutlines();\n  };\n\n  useEffect(() => {\n    if (currentDestText === defaultDestText) {\n      setCurrentDestPage(currentPage);\n    }\n  }, [currentDestText, currentPage]);\n\n  useEffect(() => {\n    if (isAddingNewOutline) {\n      core.setToolMode(TOOL_NAME);\n    } else {\n      clearOutlineDestination();\n    }\n  }, [isAddingNewOutline]);\n\n  const generalMoveOutlineAction = (dragOutline, dropOutline, moveDirection) => {\n    const dragPath = getPath(dragOutline);\n    const dropPath = getPath(dropOutline);\n    setSelectedOutlines([]);\n\n    moveDirection.call(outlineUtils, dragPath, dropPath).then((path) => {\n      updateOutlines();\n      nextPathRef.current = path;\n    });\n    core.goToOutline(dragOutline);\n  };\n\n  const moveOutlineAfterTarget = (dragOutline, dropOutline) => {\n    generalMoveOutlineAction(dragOutline, dropOutline, outlineUtils.moveOutlineAfterTarget);\n  };\n\n  const moveOutlineBeforeTarget = (dragOutline, dropOutline) => {\n    generalMoveOutlineAction(dragOutline, dropOutline, outlineUtils.moveOutlineBeforeTarget);\n  };\n\n  const moveOutlineInward = (dragOutline, dropOutline) => {\n    generalMoveOutlineAction(dragOutline, dropOutline, outlineUtils.moveOutlineInTarget);\n  };\n\n  const renameOutline = async (outlinePath, newName) => {\n    await outlineUtils.setOutlineName(outlinePath, newName);\n    updateOutlines();\n  };\n\n  const removeOutlines = async (outlinesToRemove) => {\n    outlinesToRemove.sort().reverse();\n    const confirmationWarning = {\n      message: t('warning.deleteOutline.message'),\n      title: t('warning.deleteOutline.title'),\n      confirmBtnText: t('action.delete'),\n      onConfirm: async () => {\n        for (let i = 0; i < outlinesToRemove.length; i++) {\n          const fullIndex = outlinesToRemove[i];\n          await outlineUtils.deleteOutline(fullIndex);\n        }\n        updateOutlines();\n        setActiveOutlinePath(null);\n        setSelectedOutlines([]);\n      },\n    };\n    dispatch(actions.showWarningMessage(confirmationWarning));\n  };\n\n  if (isDisabled) {\n    return null;\n  }\n\n  return (\n    <div\n      className={classNames('Panel OutlinesPanel bookmark-outline-panel', { 'modular-ui-panel': customizableUI })}\n      data-element={DataElements.OUTLINE_PANEL} ref={panelRef}\n    >\n      <div className=\"bookmark-outline-panel-header\">\n        <h2 className=\"header-title\">\n          {t('component.outlinesPanel')}\n        </h2>\n        {isOutlineEditable &&\n          (isMultiSelectMode ?\n            <TextButton\n              className=\"bookmark-outline-control-button\"\n              dataElement={DataElements.OUTLINE_MULTI_SELECT}\n              label={t('option.bookmarkOutlineControls.done')}\n              disabled={isAddingNewOutline}\n              onClick={() => setMultiSelectMode(false)}\n              ariaLabel={`${t('option.bookmarkOutlineControls.done')} ${t('action.edit')}`}\n            />\n            :\n            <TextButton\n              className=\"bookmark-outline-control-button\"\n              dataElement={DataElements.OUTLINE_MULTI_SELECT}\n              label={t('option.bookmarkOutlineControls.edit')}\n              disabled={isAddingNewOutline || outlines.length === 0}\n              onClick={() => {\n                setMultiSelectMode(true);\n                setSelectedOutlines([]);\n              }}\n              ariaLabel={`${t('action.edit')} ${t('component.outlinesPanel')}`}\n            />\n          )\n        }\n      </div>\n      <OutlineContext.Provider\n        value={{\n          currentDestPage: pageLabels[currentDestPage - 1],\n          currentDestText,\n          setActiveOutlinePath,\n          activeOutlinePath,\n          isOutlineActive: (outline) => getPath(outline) === activeOutlinePath,\n          setAddingNewOutline,\n          isAddingNewOutline,\n          setEditingOutlines,\n          editingOutlines,\n          selectedOutlines,\n          isAnyOutlineRenaming,\n          isMultiSelectMode,\n          shouldAutoExpandOutlines,\n          isOutlineEditable,\n          addNewOutline,\n          updateOutlines,\n          renameOutline,\n          updateOutlineDest,\n          removeOutlines,\n        }}\n      >\n        <DndProvider backend={isMobileDevice ? TouchBackEnd : HTML5Backend}>\n          <OutlinesDragLayer />\n\n          <div className=\"bookmark-outline-row\">\n            {!isAddingNewOutline && outlines.length === 0 &&\n              <div className=\"msg msg-no-bookmark-outline\">{t('message.noOutlines')}</div>\n            }\n            <Virtuoso data={outlines} itemContent={(index, outline) => (\n              <Outline\n                key={outlineUtils.getOutlineId(outline)}\n                outline={outline}\n                setMultiSelected={(path, value) => {\n                  if (selectedOutlines.find((outline) => outline === path)) {\n                    if (!value) {\n                      setSelectedOutlines(selectedOutlines.filter((outline) => outline !== path));\n                    }\n                  } else {\n                    if (value) {\n                      setSelectedOutlines([...selectedOutlines, path]);\n                    }\n                  }\n                }}\n                moveOutlineInward={moveOutlineInward}\n                moveOutlineBeforeTarget={moveOutlineBeforeTarget}\n                moveOutlineAfterTarget={moveOutlineAfterTarget}\n              />\n            )} initialItemCount={isTest ? outlines.length : undefined}/>\n            {isAddingNewOutline && activeOutlinePath === null && (\n              <DataElementWrapper className=\"bookmark-outline-single-container editing\">\n                <OutlineContent\n                  isAdding={true}\n                  text={''}\n                  onCancel={() => setAddingNewOutline(false)}\n                />\n              </DataElementWrapper>\n            )}\n          </div>\n        </DndProvider>\n\n        {isOutlineEditable &&\n          <DataElementWrapper\n            className=\"bookmark-outline-footer\"\n            dataElement={DataElements.OUTLINE_ADD_NEW_BUTTON_CONTAINER}\n          >\n            {isMultiSelectMode ?\n              <>\n                <Button\n                  className=\"multi-selection-button\"\n                  img=\"icon-menu-add\"\n                  ariaLabel={`${t('action.add')} ${t('component.outlinesPanel')}`}\n                  disabled={selectedOutlines.length > 0 || isAddingNewOutline || isAnyOutlineRenaming}\n                  onClick={() => setAddingNewOutline(true)}\n                />\n                <Button\n                  className=\"multi-selection-button\"\n                  img=\"icon-delete-line\"\n                  disabled={selectedOutlines.length === 0 || isAnyOutlineRenaming}\n                  onClick={() => removeOutlines(selectedOutlines)}\n                />\n              </>\n              :\n              <TextButton\n                className=\"bookmark-outline-control-button add-new-button\"\n                img=\"icon-menu-add\"\n                dataElement={DataElements.OUTLINE_ADD_NEW_BUTTON}\n                disabled={isAddingNewOutline || isAnyOutlineRenaming}\n                label={`${t('action.add')} ${t('component.outlinePanel')}`}\n                onClick={() => setAddingNewOutline(true)}\n                ariaLabel={`${t('action.add')} ${t('component.outlinesPanel')}`}\n              />\n            }\n          </DataElementWrapper>\n        }\n      </OutlineContext.Provider>\n    </div>\n  );\n};\n\nexport default React.memo(OutlinesPanel);\n", "import OutlinesPanel from './OutlinesPanel';\n\nexport default OutlinesPanel;\n"], "sourceRoot": ""}