{"version": 3, "sources": ["webpack:///./src/ui/src/components/SignatureModal/InkSignature/InkSignature.scss?25f0", "webpack:///./src/ui/src/components/SignatureModal/InkSignature/InkSignature.scss", "webpack:///./src/ui/src/components/SignatureModal/TextSignature/TextSignature.scss?8866", "webpack:///./src/ui/src/components/SignatureModal/TextSignature/TextSignature.scss", "webpack:///./src/ui/src/components/SignatureModal/ImageSignature/ImageSignature.scss?8ebc", "webpack:///./src/ui/src/components/SignatureModal/ImageSignature/ImageSignature.scss", "webpack:///./src/ui/src/components/SignatureModal/SavedSignatures/SavedSignatures.scss?5d12", "webpack:///./src/ui/src/components/SignatureModal/SavedSignatures/SavedSignatures.scss", "webpack:///./src/ui/src/components/SignatureModal/SignatureModal.scss?db39", "webpack:///./src/ui/src/components/SignatureModal/SignatureModal.scss", "webpack:///./src/ui/src/components/SignatureModal/InkSignature/InkSignature.js", "webpack:///./src/ui/src/components/SignatureModal/InkSignature/index.js", "webpack:///./src/ui/src/helpers/cropImageFromCanvas.js", "webpack:///./src/ui/src/components/SignatureModal/TextSignature/TextSignature.js", "webpack:///./src/ui/src/components/SignatureModal/TextSignature/index.js", "webpack:///./src/ui/src/components/SignatureModal/ImageSignature/ImageSignature.js", "webpack:///./src/ui/src/components/SignatureModal/ImageSignature/index.js", "webpack:///./src/ui/src/components/SignatureModal/SavedSignatures/SavedSignatures.js", "webpack:///./src/ui/src/components/SignatureModal/SavedSignatures/index.js", "webpack:///./src/ui/src/components/SignatureModal/SignatureModal.js", "webpack:///./src/ui/src/components/SignatureModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "isModalOpen", "PropTypes", "bool", "isTabPanelSelected", "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "func", "enableCreateButton", "isInitialsModeEnabled", "InkSignature", "setIt", "fullSignatureCanvas", "useRef", "initialsCanvas", "fullSignaturePathsRef", "initialsPathsRef", "t", "useTranslation", "useState", "dimension", "setDimension", "fullSignatureDrawn", "setFullSignatureDrawn", "initialsDrawn", "setInitialsDrawn", "forceUpdate", "it", "useEffect", "signatureTool", "core", "getTool", "canvas", "current", "setSignatureCanvas", "multiplier", "Core", "getCanvasMultiplier", "getContext", "scale", "second<PERSON><PERSON><PERSON>", "setInitialsCanvas", "clearFullSignatureCanvas", "clearInitialsCanvas", "signatureToolArray", "getToolsFromAllDocumentViewers", "setSignature", "setInitials", "resizeCanvas", "SignatureModes", "FULL_SIGNATURE", "INITIALS", "resizeFullSignatureCanvas", "resizeInitialsCanvas", "height", "width", "resizeCanvasAsyncCall", "useCallback", "clearSignatureCanvas", "handleFinishDrawingFullSignature", "isEmptySignature", "deepCopy", "getFullSignatureAnnotation", "getPaths", "handleFinishDrawingInitials", "getInitialsAnnotation", "paths", "pathsCopy", "j", "Math", "Point", "x", "y", "toolStyles", "defaults", "initialsContainerStyle", "display", "bounds", "onResize", "measureRef", "className", "ref", "onSwiping", "event", "stopPropagation", "onMouseUp", "onTouchEnd", "onMouseLeave", "onClick", "disabled", "aria-label", "style", "Dropdown", "id", "placeholder", "ColorPalette", "color", "property", "onStyleChange", "value", "setToolStyles", "StrokeColor", "handleColorInputChange", "overridePalette2", "COMMON_COLORS", "BASIC_PALETTE", "cropImageFromCanvas", "index", "ctx", "pixels", "imageData", "getImageData", "data", "sort", "a", "b", "n", "cut", "putImageData", "toDataURL", "DEFAULT_FONT_COLOR", "CANVAS_MULTIPLIER", "parseInitialsFromFullSignature", "fullSiganture", "split", "map", "join", "toUpperCase", "getSignatureLength", "text", "fontSize", "fontFamily", "font", "textSpan", "createElement", "visibility", "rootNode", "getRootNode", "textContent", "signature<PERSON>idth", "getBoundingClientRect", "remove", "scaleFontSize", "currentFontSize", "minFontSize", "maxFontSize", "FONT_SIZE", "floor", "measurementReference", "overrideMultiplier", "max", "setFontInCanvas", "selectedFontFamily", "fontColor", "fillStyle", "textAlign", "textBaseline", "drawTextInCanvas", "clearRect", "fillText", "TextSignature", "fonts", "useSelector", "state", "selectors", "getSignatureFonts", "textSignatureCanvasMultiplier", "getTextSignatureQuality", "fullSignature", "setFullSiganture", "initials", "isDefaultValue", "setIsDefaultValue", "Annotations", "Color", "setFontColor", "setFontSize", "inputRef", "fullSignatureHiddenCanvasRef", "initialsHiddenCanvasRef", "hiddenFullSignatureRef", "hiddenInitialsRef", "setSelectedFontFamily", "includes", "setInitialsInTool", "currentUser", "getDisplayAuthor", "getCurrentUser", "focus", "isIOS", "setSelectionRange", "select", "onUpdateAnnotationPermission", "addEventListener", "removeEventListener", "trim", "base64", "tool", "handleDropdownSelectionChange", "newFontSize", "isDisabled", "initialsInputStyle", "type", "onChange", "e", "target", "replace", "toHexString", "classNames", "items", "getCustomItemStyle", "item", "<PERSON><PERSON><PERSON>", "translationPrefix", "showLabelInList", "getDisplayValue", "onClickItem", "currentSelectionKey", "maxHeight", "isMobile", "dataElement", "signatureType", "acceptedFileTypes", "acceptedFileSize", "readImageFile", "file", "Promise", "resolve", "reject", "fileReader", "FileReader", "imageSource", "result", "some", "indexOf", "fileSize", "size", "i18next", "readAsDataURL", "ImageSignature", "fullSignatureImage", "setFullSignatureImage", "fullSignatureFileSize", "setFullSignatureFileSize", "initialsImage", "setInitialsImage", "initialsFileSize", "setInitialsFileSize", "draggingSignatureType", "setDraggingSignatureType", "fullSignatureErrorMessage", "setFullSignatureErrorMessage", "initialsErrorMessage", "setInitialsErrorMessage", "fullSignatureInputRef", "initialsInputRef", "handleFullSignatureFileChange", "readFullSignatureFile", "files", "handleInitialsFileChange", "readInitialsFile", "handleDragEnter", "preventDefault", "handleDragOver", "handleDragLeave", "parentNode", "contains", "relatedTarget", "handleDragExit", "handleFullSignatureFileDrop", "dataTransfer", "handleInitialsFileDrop", "handleFullSignatureDragEnter", "handleFullSignatureDragLeave", "handleInitialsDragEnter", "handleInitialsDragLeave", "renderPrompt", "hasLimit", "fullSignatureFileSizeCheck", "initialsFileSizeCheck", "fullSignatureContainerClass", "mobile", "dragging", "initialsContainerClass", "src", "alt", "max<PERSON><PERSON><PERSON>", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "onDragExit", "accept", "click", "SavedSignatures", "selectedIndex", "setSelectedIndex", "dispatch", "useDispatch", "getDisplayedSignatures", "getSelectedDisplayedSignatureIndex", "isElementDisabled", "getSavedInitials", "getSelectedDisplayedInitialsIndex", "getSignatureMode", "getInitialsOffset", "displayedSignatures", "selectedDisplayedSignatureIndex", "isSignatureDeleteButtonDisabled", "savedInitials", "selectedDisplayedInitialsIndex", "signatureMode", "initialsOffset", "deleteSignatureAndInitials", "isFullSignature", "skipInitial", "initialsIndex", "actions", "setInitialsOffset", "deleteSavedInitials", "setSelectedDisplayedInitialsIndex", "hidePreview", "setToolMode", "defaultTool", "deleteSavedSignature", "setSelectedDisplayedSignatureIndex", "displayedIntials", "Array", "concat", "empty", "signature", "key", "active", "checked", "imgSrc", "data-element", "Icon", "glyph", "SignatureModal", "DataElements", "SIGNATURE_MODAL", "isElementOpen", "getActiveToolName", "getActiveDocumentViewerKey", "getIsInitialsModeEnabled", "SAVED_SIGNATURES_TAB", "getSelectedTab", "isOpen", "activeToolName", "activeDocumentViewerKey", "isSavedTabDisabled", "selectedTab", "createButtonDisabled", "setCreateButtonDisabled", "useDidUpdate", "closeElements", "SIGNATURE_OVERLAY", "PRINT_MODAL", "LOADING_MODAL", "PROGRESS_MODAL", "ERROR_MODAL", "closeModal", "clearLocation", "closeElement", "createSignatures", "createFullSignature", "createInitials", "saveSignatures", "hasLocation", "addSignature", "showPreview", "saveInitials", "isEmptyInitialsSignature", "addInitials", "showInitialsPreview", "setSignatureMode", "annotation", "modalClass", "Modal", "open", "closed", "isSavedTabSelected", "ModalWrapper", "title", "<PERSON><PERSON><PERSON><PERSON>", "onCloseClick", "swipeToClose", "onMouseDown", "Tabs", "useFocusOnClose"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,+1EAAg2E,KAGz3E0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,iuEAAkuE,KAG3vE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,0/FAA2/F,KAGphG0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,28GAA48G,KAGr+G0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,q3gBAAs3gB,KAG/4gB0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,wtBCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,gbAAAA,EAAA,gDAAAA,GAAA,oCAAAA,OAAA,isBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,ygBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAaA,IAKM4B,EAAY,CAChBC,YAAaC,IAAUC,KACvBC,mBAAoBF,IAAUC,KAC9BE,oBAAqBH,IAAUI,KAC/BC,mBAAoBL,IAAUI,KAC9BE,sBAAuBN,IAAUC,MAG7BM,EAAe,SAAH,GAMZ,IAlBKC,EAaTT,EAAW,EAAXA,YACAG,EAAkB,EAAlBA,mBACAC,EAAmB,EAAnBA,oBACAE,EAAkB,EAAlBA,mBAAkB,IAClBC,6BAAqB,IAAG,GAAK,EAEvBG,EAAsBC,mBACtBC,EAAiBD,mBAIjBE,EAAwBF,mBACxBG,EAAmBH,mBAElBI,EAAqB,EAAhBC,cAAgB,GAApB,GACsC,IAAZC,mBAAS,IAAG,GAAvCC,EAAS,KAAEC,EAAY,KACqC,IAAfF,oBAAS,GAAM,GAA5DG,EAAkB,KAAEC,EAAqB,KACS,IAAfJ,oBAAS,GAAM,GAAlDK,EAAa,KAAEC,EAAgB,KAEhCC,GAhCGf,EAAwB,EAAfQ,oBAAS,GAAM,GAAnB,GACP,kBAAMR,GAAM,SAACgB,GAAE,OAAMA,OAiC5BC,qBAAU,WACR,IAAMC,EAAgBC,IAAKC,QAAQ,6BAC7BC,EAASpB,EAAoBqB,QAEnCJ,EAAcK,mBAAmBF,GACjC,IAAMG,EAAa3D,OAAO4D,KAAKC,sBAC/BL,EAAOM,WAAW,MAAMC,MAAMJ,EAAYA,GAG1C,IAAMK,EAAe1B,EAAemB,QACpCJ,EAAcY,kBAAkBD,GAChCA,EAAaF,WAAW,MAAMC,MAAMJ,EAAYA,KAC/C,IAEHP,qBAAU,WACJ1B,IACFwC,IACAC,OAED,CAACzC,IAEJ0B,qBAAU,WACgC,aAUvC,OAVuC,cAAxC,8FACM1B,IAAeG,EAAkB,iBAC7BuC,EAAqBd,IAAKe,+BAA+B,6BAA4B,IAC/DD,GAAkB,yDAG5C,OAHSf,EAAa,SACRiB,aAAa/B,EAAsBkB,SACjDJ,EAAckB,YAAY/B,EAAiBiB,SAC3C,UACMJ,EAAcmB,aAAaC,IAAeC,gBAAe,wMAGpE,sBAGkC,aAQlC,OARkC,cAAnC,+FACMhD,GAAeG,GAAsBI,GAAqB,iBACtDmC,EAAqBd,IAAKe,+BAA+B,6BAA4B,IAC/DD,GAAkB,yDACQ,OAD3Cf,EAAa,SACRkB,YAAY/B,EAAiBiB,SAAS,UAC9CJ,EAAcmB,aAAaC,IAAeE,UAAS,wMAG9D,uBAtBa,WAC0B,wBA+BxCC,GArBC,WAGkC,wBAmBnCC,GARM5C,EACDM,EAAsBkB,SAAWjB,EAAiBiB,QAAWzB,IAAuBF,IAErFS,EAAsBkB,QAAUzB,IAAuBF,MAO1D,CAACD,EAAoBH,EAAaO,IAErCmB,qBAAU,WAC4B,aASnC,OATmC,cAApC,wFACMR,EAAUkC,SAAUlC,EAAUmC,MAAK,gBAC0B,OAAzD1B,EAAgBC,IAAKC,QAAQ,6BAA4B,SACzDF,EAAcmB,eAAc,WAE9BvC,EAAuB,CAAF,+BACjBoB,EAAcmB,aAAaC,IAAeE,UAAS,4CAG9D,uBAVa,WACsB,wBAUpCK,KACC,CAACpC,EAAWX,IAEfmB,qBAAU,YACJN,GAAwBb,IAAyBe,EAGnDlB,IAFAE,MAID,CAACgB,EAAeF,EAAoBb,IAEvC,IAAMiC,EAA2Be,uBAAY,WACrB3B,IAAKC,QAAQ,6BACrB2B,uBACd3C,EAAsBkB,QAAU,KAChCV,GAAsB,KACrB,IAEGoB,EAAsBc,uBAAY,WAChB3B,IAAKC,QAAQ,6BACrBY,sBACd3B,EAAiBiB,QAAU,KAC3BR,GAAiB,KAChB,IAEGkC,EAAgC,6BAAG,oFACwB,OAAzD9B,EAAgBC,IAAKC,QAAQ,6BAA4B,SACnDF,EAAc+B,mBAAkB,iCAI1C7C,EAAsBkB,QAAU4B,EAAShC,EAAciC,6BAA6BC,YACpFxC,GAAsB,GAAM,2CAE/B,kBATqC,mCAWhCyC,EAA2B,6BAAG,oFAC5BnC,EAAgBC,IAAKC,QAAQ,6BACRF,EAAcoC,0BAKvCjD,EAAiBiB,QAAU4B,EAAShC,EAAcoC,wBAAwBF,YAC1EtC,GAAiB,IAClB,2CACF,kBAVgC,mCA4B3BoC,EAAW,SAACK,GAEhB,IADA,IAAMC,EAAY,GACT9F,EAAI,EAAGA,EAAI6F,EAAMnF,SAAUV,EAClC,IAAK,IAAI+F,EAAI,EAAGA,EAAIF,EAAM7F,GAAGU,SAAUqF,EAChCD,EAAU9F,KACb8F,EAAU9F,GAAK,IAEjB8F,EAAU9F,GAAG+F,GAAK,IAAI5F,OAAO4D,KAAKiC,KAAKC,MAAMJ,EAAM7F,GAAG+F,GAAGG,EAAGL,EAAM7F,GAAG+F,GAAGI,GAI5E,OAAOL,GAIHM,EADgB3C,IAAKC,QAAQ,6BACF2C,SAC3BC,EAAyBlE,EAAwB,GAAK,CAAEmE,QAAS,QAEvE,OACE,kBAAC,IAAO,CAACC,QAAM,EAACC,SAAU,gBAAGD,EAAM,EAANA,OAAM,OAAOxD,EAAawD,MACpD,gBAAGE,EAAU,EAAVA,WAAU,OACZ,yBAAKC,UAAU,gBAAgBC,IAAKF,GAClC,kBAAC,IAAS,CACRG,UAAW,YAAQ,SAALC,MAAkBC,mBAChCJ,UAAU,iCAEV,yBAAKA,UAAU,oCACb,yBAAKA,UAAU,kCACb,4BACEA,UAAU,uBACVK,UAAW1B,EACX2B,WAAY3B,EACZ4B,aAAc5B,EACdsB,IAAKrE,IAEP,yBAAKoE,UAAU,0BACb,yBAAKA,UAAU,oBACZ/D,EAAE,wCAEL,4BACE+D,UAAU,yBACVQ,QAAS9C,EACT+C,UAAWnE,EACXoE,aAAYzE,EAAE,0BAEbA,EAAE,mBAIT,yBAAK+D,UAAU,2BAA2BW,MAAOhB,GAC/C,4BACEK,UAAU,uBACVK,UAAWrB,EACXsB,WAAYtB,EACZuB,aAAcvB,EACdiB,IAAKnE,IAEP,yBAAKkE,UAAU,0BACb,yBAAKA,UAAU,oBACZ/D,EAAE,sCAEL,4BACE+D,UAAU,yBACVQ,QAAS7C,EACT8C,UAAWjE,EACXkE,aAAYzE,EAAE,wBAEbA,EAAE,oBAKX,yBAAK+D,UAAU,gCACb,yBAAKA,UAAU,2BACb,kBAACY,EAAA,EAAQ,CACPC,GAAG,8BACHJ,UAAU,EACVK,YAAa,gBAEf,yBAAKd,UAAU,yBACf,yBAAKA,UAAU,YACf,kBAACe,EAAA,EAAY,CACXC,MAAOvB,EAAwB,YAC/BwB,SAAS,cACTC,cAAe,SAACD,EAAUE,GAAK,OApGhB,SAACF,EAAUE,GACxCC,YAAc,4BAA6BH,EAAUE,GACrD,IAAMtE,EAAgBC,IAAKC,QAAQ,6BAC/BF,EAAciC,+BAChBjC,EAAciC,6BAA6BuC,YAAcF,EACzDtE,EAAcmB,aAAaC,IAAeC,iBAGxCrB,EAAcoC,0BAChBpC,EAAcoC,wBAAwBoC,YAAcF,EACpDtE,EAAcmB,aAAaC,IAAeE,WAG5CzB,IAuFkD4E,CAAuBL,EAAUE,IAErEI,iBAAkB,CAACC,IAAqB,MAAGC,IAAc,IAAKA,IAAc,cAW9F/F,EAAaT,UAAYA,EAEVS,ICvRAA,EDuRAA,E,6DEjPAgG,MAtCf,SAA6B1E,GAC3B,IAKIuC,EACAC,EACAmC,EAPEC,EAAM5E,EAAOM,WAAW,MAC1BiB,EAAQvB,EAAOuB,MACfD,EAAStB,EAAOsB,OACduD,EAAS,CAAEtC,EAAG,GAAIC,EAAG,IACrBsC,EAAYF,EAAIG,aAAa,EAAG,EAAG/E,EAAOuB,MAAOvB,EAAOsB,QAK9D,IAAKkB,EAAI,EAAGA,EAAIlB,EAAQkB,IACtB,IAAKD,EAAI,EAAGA,EAAIhB,EAAOgB,IACrBoC,EAA0B,GAAjBnC,EAAIjB,EAAQgB,GACjBuC,EAAUE,KAAKL,EAAQ,GAAK,IAC9BE,EAAOtC,EAAEhF,KAAKgF,GACdsC,EAAOrC,EAAEjF,KAAKiF,IAIpBqC,EAAOtC,EAAE0C,MAAK,SAASC,EAAGC,GACxB,OAAOD,EAAIC,KAEbN,EAAOrC,EAAEyC,MAAK,SAASC,EAAGC,GACxB,OAAOD,EAAIC,KAEb,IAAMC,EAAIP,EAAOtC,EAAExF,OAAS,EAE5BwE,EAAQ,EAAIsD,EAAOtC,EAAE6C,GAAKP,EAAOtC,EAAE,GACnCjB,EAAS,EAAIuD,EAAOrC,EAAE4C,GAAKP,EAAOrC,EAAE,GACpC,IAAM6C,EAAMT,EAAIG,aAAaF,EAAOtC,EAAE,GAAIsC,EAAOrC,EAAE,GAAIjB,EAAOD,GAM9D,OAJAtB,EAAOuB,MAAQA,EACfvB,EAAOsB,OAASA,EAChBsD,EAAIU,aAAaD,EAAK,EAAG,GAElBrF,EAAOuF,a,wjCCrBhB,IAAMtH,EAAY,CAChBC,YAAaC,IAAUC,KACvBC,mBAAoBF,IAAUC,KAC9BE,oBAAqBH,IAAUI,KAC/BC,mBAAoBL,IAAUI,KAC9BE,sBAAuBN,IAAUC,MAM7BoH,EAAqBhB,IAAqB,MAC1CiB,EAAoBjJ,OAAO4D,KAAKC,sBAQhCqF,EAAiC,SAACC,GACtC,OAAOA,aAAa,EAAbA,EAAeC,MAAM,KAAKC,KAAI,SAACtD,GAAC,OAAKA,EAAE,MAAIuD,KAAK,IAAIC,eAGvDC,EAAqB,SAACC,EAAMC,EAAUC,GAC1C,IAAMC,EAAO,GAAH,OAAMF,EAAQ,cAAMC,GACxBE,EAAW3J,SAAS4J,cAAc,QACxCD,EAASxC,GAAK,WACdwC,EAAS1C,MAAMf,QAAU,eACzByD,EAAS1C,MAAM4C,WAAa,SAC5BF,EAAS1C,MAAMyC,KAAOA,EACtB,IAAMI,EAAWC,cACbjK,OAAOC,8BACT+J,EAAS5J,YAAYyJ,GAErBG,EAAS1J,qBAAqB,QAAQ,GAAGF,YAAYyJ,GAGvDA,EAASK,YAAcT,EAEvB,IAAMU,EAAiBN,EAASO,wBAAwBrF,MAExD,OADA8E,EAASQ,SACFF,GAGHG,EAAgB,SAACb,EAAME,GAK3B,IAJA,IAEIY,EAFAC,EAAc,EACdC,EAtC6BC,GAyC1BF,GAAeC,GAAa,CACjCF,EAAkB1E,KAAK8E,OAAOH,EAAcC,GAAe,GAEpCjB,EAAmBC,EAAMc,EAAiBZ,GA3CxC,IA6CvBc,EAAcF,EAAkB,EAEhCC,EAAcD,EAAkB,EAGpC,OAAOA,GAGH/F,EAAe,SAAChB,EAAQoH,GAAiD,IAA3BC,EAAqB,UAAH,6CAAG,EACvE,EAAwBD,EAAqBnH,QAAQ2G,wBAA/CrF,EAAK,EAALA,MAAOD,EAAM,EAANA,OACbC,GApDwB,IAqDxBD,GArDwB,IAsDxBtB,EAAO2D,MAAMpC,MAAQ,GAAH,OAAMA,EAAK,MAC7BvB,EAAO2D,MAAMrC,OAAS,GAAH,OAAMA,EAAM,MAC/BtB,EAAOuB,MAAQA,EAAQc,KAAKiF,IAAID,EAAoB5B,GACpDzF,EAAOsB,OAASA,EAASe,KAAKiF,IAAID,EAAoB5B,IAGlD8B,EAAkB,SAAH,GAAgF,IAA1EvH,EAAM,EAANA,OAAQiG,EAAI,EAAJA,KAAMuB,EAAkB,EAAlBA,mBAAoBC,EAAS,EAATA,UAAS,IAAEJ,0BAAkB,IAAG,IAAC,EACtFzC,EAAM5E,EAAOM,WAAW,MACxB4F,EAAWY,EAAcb,EAAMuB,GACrC5C,EAAI8C,UAAYD,EAChB7C,EAAI+C,UAAY,SAChB/C,EAAIgD,aAAe,SACnBhD,EAAIwB,KAAO,GAAH,OAAMF,EAAW7D,KAAKiF,IAAID,EAAoB5B,GAAkB,cAAM+B,IAG1EK,EAAmB,SAAC7H,EAAQiG,GAChC,IAAMrB,EAAM5E,EAAOM,WAAW,MACtBiB,EAAkBvB,EAAlBuB,MAAOD,EAAWtB,EAAXsB,OACfsD,EAAIkD,UAAU,EAAG,EAAGvG,EAAOD,GAC3BsD,EAAImD,SAAS9B,EAAM1E,EAAQ,EAAGD,EAAS,IAGnC0G,EAAgB,SAAH,GAMb,IA/EKrJ,EA0ETT,EAAW,EAAXA,YACAG,EAAkB,EAAlBA,mBACAC,EAAmB,EAAnBA,oBACAE,EAAkB,EAAlBA,mBAAkB,IAClBC,6BAAqB,IAAG,GAAK,EAEvBwJ,EAAQC,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,MAC3DG,EAAgCJ,aAAY,SAACC,GAAK,OAAKC,IAAUG,wBAAwBJ,MACzC,IAAZhJ,mBAAS,IAAG,GAA/CqJ,EAAa,KAAEC,EAAgB,KACM,IAAZtJ,mBAAS,IAAG,GAArCuJ,EAAQ,KAAE3H,EAAW,KAC8B,IAAd5B,oBAAS,GAAK,GAAnDwJ,EAAc,KAAEC,EAAiB,KACyD,IAA/DzJ,mBAAS,IAAI3C,OAAO4D,KAAKyI,YAAYC,MAAMtD,IAAoB,GAA1FiC,EAAS,KAAEsB,EAAY,KACqC,IAAnC5J,mBA7FC+H,IA6FkC,GAA5DhB,EAAQ,KAAE8C,EAAW,KACtBC,EAAWpK,mBACXqK,EAA+BrK,mBAC/BsK,EAA0BtK,mBAC1BuK,EAAyBvK,mBACzBwK,EAAoBxK,mBACnBI,EAAqB,EAAhBC,cAAgB,GAApB,GAE8D,IAAlBC,mBAAS8I,EAAM,IAAG,GAA/DT,EAAkB,KAAE8B,EAAqB,KAE1C5J,GAhGGf,EAAwB,EAAfQ,oBAAS,GAAM,GAAnB,GACP,kBAAMR,GAAM,SAACgB,GAAE,OAAMA,OAmG5BC,qBAAU,YACJ4I,aAAa,EAAbA,EAAezL,QAAS,KAAO0B,GAAyBiK,GAC1DlK,IAEAF,MAED,CAACoK,EAAUF,EAAe/J,IAE7BmB,qBAAU,WAGHqI,EAAMsB,SAAS/B,IAClB8B,EAAsBrB,EAAM,MAE7B,CAACT,EAAoBS,IAExBrI,qBAAU,WACR,IAAMd,EAAiBqK,EAAwBlJ,QAE3C5B,IACF2C,EAAalC,EAAgBuK,GAC7B9B,EAAgB,CACdvH,OAAQlB,EACRmH,KAAMyC,EACNlB,qBACAC,cAEFI,EAAiB/I,EAAgB4J,GAE7BxK,GACFsL,QAGH,CAACnL,EAAoBqK,EAAUT,EAAOR,EAAWD,IAEpD5H,qBAAU,WACR,IAAMhB,EAAsBsK,EAA6BjJ,QAErD5B,IACF2C,EAAapC,EAAqBwK,EAAwBd,GAC1Df,EAAgB,CACdvH,OAAQpB,EACRqH,KAAMuC,EACNhB,qBACAC,YACAJ,mBAAoBiB,IAEtBT,EAAiBjJ,EAAqB4J,GAClCtK,GACF4C,QAGH,CAACzC,EAAoBmK,EAAeP,EAAOR,EAAWD,IAEzD5H,qBAAU,WAER,GADAmJ,EAAatB,GACTvJ,GAAeG,EAAoB,CACrC,IAAMoL,EAAc3J,IAAK4J,iBAAiB5J,IAAK6J,kBAC/ClB,EAAiBgB,GACjB1I,EAAY2E,EAA+B+D,IAC3C3I,QAED,CAAC5C,EAAaG,IAEjBuB,qBAAU,WACgB,MAApBvB,IACc,QAAhB,EAAA4K,EAAShJ,eAAO,OAAhB,EAAkB2J,QAEdC,IACFZ,EAAShJ,QAAQ6J,kBAAkB,EAAG,MAEtCb,EAAShJ,QAAQ8J,YAGpB,CAAC1L,IAEJuB,qBAAU,WACR,IAAMoK,EAA+B,WACnC,GAAIrB,EAAgB,CAClB,IAAMc,EAAc3J,IAAK4J,iBAAiB5J,IAAK6J,kBAC/ClB,EAAiBgB,GACjB1I,EAAY2E,EAA+B+D,IAC3CjL,MAKJ,OADAsB,IAAKmK,iBAAiB,6BAA8BD,GAC7C,WACLlK,IAAKoK,oBAAoB,6BAA8BF,MAExD,CAACrB,IAEJ,IAAM7H,GAAe,WACnB,IAAMF,EAAqBd,IAAKe,+BAA+B,6BACzDb,EAASkJ,EAA6BjJ,QAG5C,IADuBuI,GAAiB,IACrB2B,OAAQ,CACzB,IAAMC,EAAS1F,EAAoB1E,GACnCY,EAAmBvD,SAAQ,SAACgN,GAAI,OAAKA,EAAKvJ,aAAasJ,WAEvDxJ,EAAmBvD,SAAQ,SAACgN,GAAI,OAAKA,EAAKvJ,aAAa,UAIrD0I,GAAoB,WACxB,IAAM5I,EAAqBd,IAAKe,+BAA+B,6BACzDb,EAASmJ,EAAwBlJ,QAGvC,IADsByI,GAAY,IAChByB,OAAQ,CACxB,IAAMC,EAAS1F,EAAoB1E,GACnCY,EAAmBvD,SAAQ,SAACgN,GAAI,OAAKA,EAAKtJ,YAAYqJ,WAEtDxJ,EAAmBvD,SAAQ,SAACgN,GAAI,OAAKA,EAAKtJ,YAAY,UA8BpDuJ,GAAgC,SAAClE,GACrCkD,EAAsBlD,GACtB,IAAMmE,EAAczD,EAAc0B,EAAepC,GACjD4C,EAAYuB,IA4DRC,KAAetM,GAAeG,GAC9BoM,GAAqBhM,EAAwB,GAAK,CAAEmE,QAAS,QAEnE,OACE,yBAAKI,UAAU,kBACb,yBAAKA,UAAU,oCACb,yBAAKA,UAAU,kCACb,+BACE,2BACEA,UAAU,uBACVC,IAAKgG,EACLvF,aAAYzE,EAAE,uCACdyL,KAAK,OACLvG,MAAOqE,EACPmC,SAvGsB,SAACC,GACjChC,GAAkB,GAElB,IAAMzE,EAAQyG,EAAEC,OAAO1G,MAAM2G,QAAQ,QAAS,IAC9ChK,KACA2H,EAAiBtE,GACjBpD,EAAY2E,EAA+BvB,IAC3C,IAAMoG,EAAczD,EAAc3C,EAAOqD,GACzCwB,EAAYuB,IAgGF5G,MAAO,CAAEwC,WAAYqB,GAAsBS,EAAO/B,WAAUlC,MAAOyD,EAAUsD,eAC7EtH,SAAU+G,MAGd,yBAAKxH,UAAU,0BACb,yBAAKA,UAAU,oBACZ/D,EAAE,wCAEL,4BACE+D,UAAU,yBACVU,aAAYzE,EAAE,yBACduE,QAAS,WACPiF,EAAiB,IACjBQ,EAAShJ,QAAQ2J,SAEnBnG,SAAU+G,IAAuC,IAAzBhC,EAAczL,QAErCkC,EAAE,mBAIT,yBAAK+D,UAAU,2BAA2BW,MAAO8G,IAC/C,+BACE,2BACEzH,UAAU,uBACV0H,KAAK,OACLvG,MAAOuE,EACPhF,aAAYzE,EAAE,qCACd0L,SAzHiB,SAACC,GAC5BhC,GAAkB,GAElB,IAAMF,EAAWkC,EAAEC,OAAO1G,MAAM2G,QAAQ,QAAS,IACjDhK,KACAC,EAAY2H,IAqHF/E,MAAO,CAAEwC,WAAYqB,GAAsBS,EAAO/B,WAAUlC,MAAOyD,EAAUsD,eAC7EtH,SAAU+G,MAGd,yBAAKxH,UAAU,0BACb,yBAAKA,UAAU,oBACZ/D,EAAE,sCAEL,4BACE+D,UAAU,yBACVU,aAAYzE,EAAE,uBACduE,QAAS,kBAAMzC,EAAY,KAC3B0C,SAAU+G,IAAkC,IAApB9B,EAAS3L,QAEhCkC,EAAE,oBA/GX,yBACE+D,UAAWgI,IAAW,CACpB,uBAAuB,IAEzBrH,MAAO,CAAEwC,WAAYqB,EAAoBtB,SA1Q/B,GA0QoDlC,MAAOyD,EAAUsD,gBAE/E,yBACE/H,UAAU,iBACVC,IAAKmG,GAEJZ,GAEH,yBACExF,UAAU,iBACVC,IAAKoG,GAEJX,IAqGL,4BAAQzF,IAAKiG,IACb,4BAAQjG,IAAKkG,IACb,yBAAKnG,UAAU,gCACb,yBAAKA,UAAU,2BA/FG,KAAlBwF,KADkC/J,GAAsC,KAAbiK,IACAjK,EAU7D,kBAACmF,EAAA,EAAQ,CACPC,GAAG,+BACHoH,MAAOhD,EAAMpC,KAAI,SAACO,GAAI,MAAM,CAAEA,OAAMjC,MAAO,GAAF,OAAKqE,EAAa,YAAI/J,EAAwBiK,EAAW,QAClGwC,mBAAoB,SAACC,GAAI,MAAM,CAAEhF,WAAYgF,EAAK/E,OAClDgF,OAAQ,SAACD,GAAI,OAAKA,EAAK/E,MACvBiF,kBAAkB,sCAClBC,iBAAe,EACfC,gBAAiB,SAACJ,GAChB,OAAOA,EAAKhH,OAASgH,EAAK/E,MAE5BoF,YAAalB,GACbmB,oBAAqBjE,GAAsBS,EAAM,GACjDyD,UAAWC,cAAa,GAAK,KAC7BC,YAAY,iCArBZ,kBAAChI,EAAA,EAAQ,CACPC,GAAG,+BACHJ,UAAU,EACVK,YAAa7E,EAAE,qCA4Ff,yBAAK+D,UAAU,yBACf,yBAAKA,UAAU,YACf,kBAACe,EAAA,EAAY,CACXC,MAAOyD,EACPxD,SAAS,YACTC,cAAe,SAACD,EAAUE,GAAK,OAhJV,SAACF,EAAUE,GACxC4E,EAAa5E,GAGbzE,IA4I4C4E,CAAuBL,EAAUE,IAErEI,iBAAkB,CAACC,IAAqB,MAAGC,IAAc,IAAKA,IAAc,UAQxFuD,EAAc/J,UAAYA,EAEX+J,ICvaAA,EDuaAA,E,qaExaf,8lGAAA3L,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SASA,IAAM4B,GAAY,CAChBC,YAAaC,IAAUC,KACvBC,mBAAoBF,IAAUC,KAC9BE,oBAAqBH,IAAUI,KAC/BC,mBAAoBL,IAAUI,KAC9BE,sBAAuBN,IAAUC,MAG7ByN,GACY,gBADZA,GAEM,WAENC,GAAoB,CAAC,MAAO,MAAO,QACrCC,GAAmB,KAEvB,SAASC,GAAcC,GACrB,OAAO,IAAIC,SAAQ,SAACC,EAASC,GAC3B,IAAMC,EAAa,IAAIC,WAEvBD,EAAW1O,OAAS,SAACwF,GACnB,IAAMoJ,EAAcpJ,EAAM0H,OAAO2B,OACbV,GAAkBW,MACpC,SAAC/B,GAAI,OAA+C,IAA1C6B,EAAYG,QAAQ,SAAD,OAAUhC,OAIvCyB,EAAQ,CACNI,cACAI,SAAUV,EAAKW,OAGjBR,EAAOS,IAAQ5N,EAAE,0CAA2C,CAC1D6M,kBAAmBA,GAAkBhG,KAAK,UAKhDuG,EAAWS,cAAcb,MAK7B,IAAMc,GAAiB,SAAH,GAMd,IALJ7O,EAAW,EAAXA,YACAG,EAAkB,EAAlBA,mBACAC,EAAmB,EAAnBA,oBACAE,EAAkB,EAAlBA,mBAAkB,IAClBC,6BAAqB,IAAG,GAAK,EAEqC,IAAdU,mBAAS,MAAK,GAA3D6N,EAAkB,KAAEC,EAAqB,KACwB,IAAd9N,mBAAS,MAAK,GAAjE+N,EAAqB,KAAEC,EAAwB,KACE,IAAdhO,mBAAS,MAAK,GAAjDiO,EAAa,KAAEC,EAAgB,KACwB,IAAdlO,mBAAS,MAAK,GAAvDmO,EAAgB,KAAEC,EAAmB,KAC0B,IAAZpO,mBAAS,IAAG,GAA/DqO,EAAqB,KAAEC,EAAwB,KAC0B,IAAdtO,mBAAS,MAAK,GAAzEuO,EAAyB,KAAEC,EAA4B,KACQ,IAAdxO,mBAAS,MAAK,GAA/DyO,EAAoB,KAAEC,EAAuB,KAC9CC,EAAwBjP,mBACxBkP,EAAmBlP,mBAClBI,EAAqB,EAAhBC,cAAgB,GAApB,GAERU,qBAAU,WACR,IAAMgB,EAAqBd,IAAKe,+BAA+B,6BAC/DkL,GAAmBnL,EAAmB,GAAuB,mBACxD1C,EAKMA,GAAeG,IACxBuC,EAAmBvD,SAAQ,SAACwC,GAAa,OAAKA,EAAciB,aAAakM,EAAoBE,MAC7FtM,EAAmBvD,SAAQ,SAACwC,GAAa,OAAKA,EAAckB,YAAYqM,EAAeE,OACtFN,GAAwBvO,IAAyB2O,EAAyC9O,IAAvBE,MAPpEyO,EAAsB,MACtBI,EAAiB,MACjBE,EAAoB,MACpBA,EAAoB,SAMrB,CAACP,EAAoBI,EAAe/O,EAAoBH,EAAaoP,EAAkBJ,EAAuBzO,IAEjH,IAAMuP,EAAgC,SAAC7K,GACrC8K,EAAsB9K,EAAM0H,OAAOqD,MAAM,KAGrCC,EAA2B,SAAChL,GAChCiL,EAAiBjL,EAAM0H,OAAOqD,MAAM,KAGhCG,EAAkB5M,uBAAY,SAAC0B,EAAO0I,GAC1C1I,EAAMmL,iBACNb,EAAyB5B,KACxB,IAEG0C,EAAiB9M,uBAAY,SAAC0B,GAClCA,EAAMmL,mBACL,IAEGE,EAAkB/M,uBAAY,SAAC0B,GACnCA,EAAMmL,iBAEDnL,EAAM0H,OAAO4D,WAAWC,SAASvL,EAAMwL,gBAC1ClB,EAAyB,QAE1B,IAEGmB,EAAiBnN,uBAAY,SAAC0B,GAClCA,EAAMmL,iBACNb,EAAyB,QACxB,IAEGoB,EAA8B,SAAC1L,GACnCA,EAAMmL,iBACNb,EAAyB,MACzB,IAAQS,EAAU/K,EAAM2L,aAAhBZ,MAEJA,EAAMnR,QACRkR,EAAsBC,EAAM,KAI1BD,EAAqB,6BAAG,WAAOhC,GAAI,oGAEhBD,GAAcC,GAAK,OAAlCO,EAAS,EAAH,KACJD,EAA0BC,EAA1BD,YAAaI,EAAaH,EAAbG,SACrBgB,EAA6B,IAC7BV,EAAsBV,GACtBY,EAAyBR,GAAU,kDAEnCgB,EAA6B,EAAD,IAAe,0DAE9C,gBAV0B,sCAYrBoB,EAAyB,SAAC5L,GAC9BA,EAAMmL,iBACNb,EAAyB,MACzB,IAAQS,EAAU/K,EAAM2L,aAAhBZ,MAEJA,EAAMnR,QACRqR,EAAiBF,EAAM,KAIrBE,EAAgB,6BAAG,WAAOnC,GAAI,oGAEXD,GAAcC,GAAK,OAAlCO,EAAS,EAAH,KACJD,EAA0BC,EAA1BD,YAAaI,EAAaH,EAAbG,SACrBkB,EAAwB,IACxBR,EAAiBd,GACjBgB,EAAoBZ,GAAU,kDAE9BkB,EAAwB,EAAD,IAAe,0DAEzC,gBAVqB,sCAYhBmB,EAA+BvN,uBAAY,SAAC0B,GAChDkL,EAAgBlL,EAAO0I,MACtB,CAACwC,IAEEY,EAA+BxN,uBAAY,SAAC0B,GAChDqL,EAAgBrL,EAAO0I,MACtB,CAAC2C,IAGEU,EAA0BzN,uBAAY,SAAC0B,GAC3CkL,EAAgBlL,EAAO0I,MACtB,CAACwC,IAEEc,EAA0B1N,uBAAY,SAAC0B,GAC3CqL,EAAgBrL,EAAO0I,MACtB,CAAC2C,IAEEY,GAAe,WACnB,OAAIzD,cAEA,yBAAK3I,UAAU,6BACZ/D,EAAE,sCAKP,oCACE,yBAAK+D,UAAU,uBACZ/D,EAAE,sCAEL,yBAAK+D,UAAU,6BACZ/D,EAAE,+BA0ELoQ,GAAuC,iBAArBtD,IAAiCA,GAAmB,EACtEuD,IAA8BD,IAAYnC,EAAwBnB,GAClEwD,IAAyBF,IAAY/B,EAAmBvB,GACxDyD,GAA8BxE,IAAW,mCAAoC,CAAEyE,OAAQ9D,cAAY+D,SAAUlC,IAA0B3B,KACvI8D,GAAyB3E,IAAW,mCAAoC,CAAEyE,OAAQ9D,cAAY+D,SAAUlC,IAA0B3B,KAClIpB,GAAqBhM,EAAwB,GAAK,CAAEmE,QAAS,QACnE,OACE,yBAAKI,UAAU,mBACb,yBAAKA,UAAU,oCACb,yBAAKA,UAAU,wCACZgK,GAAsBsC,GA9ES,yBAAKM,IAAK5C,EAAoB6C,IAAK5Q,EAAE,wCAAyC0E,MAAO,CAAEmM,SAAU,OAAQpE,UAAW,UAG1J,yBACE1I,UAAWwM,GACXO,YAAaf,EACbgB,YAAaf,EACbgB,WAAY1B,EACZ2B,OAAQrB,EACRsB,WAAYvB,GAEXQ,KACD,yBAAKpM,UAAU,0BACb,2BACEC,IAAK6K,EACLjK,GAAG,SACH6G,KAAK,OACL0F,OAAQtE,GAAkBjG,KAAI,SAAC6E,GAAI,iBAASA,MAAQ5E,KAAK,KACzD6E,SAAUqD,EACVvK,WAAYvF,GAAeG,KAE7B,4BACEmF,QAAS,kBAAMsK,EAAsB7N,QAAQoQ,SAC7CrN,UAAU,qBAET/D,EAAE,qCAGNyO,GACC,yBAAK1K,UAAU,yBAAyB0K,KAsDxC,yBAAK1K,UAAU,iCAAiCW,MAAO8G,IACpD2C,GAAiBmC,GAlDS,yBAAKK,IAAKxC,EAAeyC,IAAK5Q,EAAE,sCAAuC0E,MAAO,CAAEmM,SAAU,OAAQpE,UAAW,UAG9I,yBACE1I,UAAW2M,GACXI,YAAab,EACbc,YAAab,EACbc,WAAY1B,EACZ2B,OAAQnB,EACRoB,WAAYvB,GAEXQ,KACD,yBAAKpM,UAAU,0BACb,2BACEC,IAAK8K,EACLlK,GAAG,SACH6G,KAAK,OACL0F,OAAQtE,GAAkBjG,KAAI,SAAC6E,GAAI,iBAASA,MAAQ5E,KAAK,KACzD6E,SAAUwD,EACV1K,WAAYvF,GAAeG,KAE7B,4BACEmF,QAAS,kBAAMuK,EAAiB9N,QAAQoQ,SACxCrN,UAAU,qBAET/D,EAAE,4CAGN2O,GACC,yBAAK5K,UAAU,yBAAyB4K,QA8BhDb,GAAe9O,UAAYA,GAEZ8O,ICjSAA,GDiSAA,G,6aElSf,gmGAAA1Q,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,kbAAAA,EAAA,gDAAAA,GAAA,oCAAAA,OAAA,ggBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,KAAAA,IAAA,6gBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAYA,ICXeiU,GDWS,SAAH,GAA4C,IAAtCC,EAAa,EAAbA,cAAeC,EAAgB,EAAhBA,iBAClCC,EAAWC,cACTzR,EAAMC,cAAND,EAmBP,KAVGiJ,aACF,SAACC,GAAK,MAAK,CACTC,IAAUuI,uBAAuBxI,GACjCC,IAAUwI,mCAAmCzI,GAC7CC,IAAUyI,kBAAkB1I,EAAO,gCACnCC,IAAU0I,iBAAiB3I,GAC3BC,IAAU2I,kCAAkC5I,GAC5CC,IAAU4I,iBAAiB7I,GAC3BC,IAAU6I,kBAAkB9I,OAE/B,GAjBC+I,EAAmB,KACnBC,EAA+B,KAC/BC,EAA+B,KAC/BC,EAAa,KACbC,EAA8B,KAC9BC,EAAa,KACbC,EAAc,KAaV5Q,EAAqBd,IAAKe,+BAA+B,6BAEzD4Q,EAA0B,eArClC,EAqCkC,GArClC,EAqCkC,WAAG,WAAO9M,GAAK,oFAI7C,GAHM+M,EAAkBH,IAAkBtQ,IAAeC,eAEnDyQ,GADAC,EAAgBjN,EAAQ6M,GACM,EAYlCf,EAASoB,KAAQC,kBAAkBN,EAAiB,SARpD,GAFA5Q,EAAmB,GAAGmR,oBAAoBH,GACPN,IAAmCM,IAAkBD,EACxD,CAC9BlB,EAASoB,KAAQG,kCAAkC,IAAI,KAC3BpR,GAAkB,IAA9C,IAAK,EAAL,qBAAwB,QACRqR,cACf,8BACDnS,IAAKoS,YAAYC,MAOrB,GAFAvR,EAAmB,GAAGwR,qBAAqBzN,GACPwM,IAAoCxM,EACvC,CAC/B8L,EAASoB,KAAQQ,mCAAmC,IAAI,KAC5BzR,GAAkB,IAA9C,IAAK,EAAL,qBAAwB,QACRqR,cACf,8BACDnS,IAAKoS,YAAYC,WACRT,EAAkB/M,EAAQwM,EAAkCS,EAAgBN,IACrFb,EAASoB,KAAQQ,mCAAmClB,EAAkC,IACvF,0CAhEL,iLAiEG,gBA5B+B,sCA8B1BmB,EAAmB,IAAIC,MAAMf,GAAgBgB,OAAOnB,GAE1D,OACE,yBAAKrO,UAAWgI,IAAW,kBAAmB,CAAEyH,OAAQvB,GAAuBA,EAAoBnU,OAAS,KACzGmU,EAAoBnU,OACnBmU,EAAoBrL,KAAI,SAAC6M,EAAW/N,GAAK,OAAK,yBAC5CgO,IAAKhO,EACL3B,UAAWgI,IAAW,gBAAiB,CAAE4H,OAAQrC,IAAkB5L,EAAO,gBAAgB2N,WAAmB3N,MAC7GnB,QAAS,kBAAMgN,EAAiB7L,KAEhC,yBAAK3B,UAAU,kBACb,2BAAO0H,KAAK,QAAQC,SAAU,kBAAM6F,EAAiB7L,IAAQkO,QAASlO,IAAU4L,IAChF,yBAAKvN,UAAU,oBACb,yBAAKA,UAAU,gBACb,yBAAK6M,IAAK5Q,EAAE,wCAAyC2Q,IAAKsB,EAAoBvM,GAAOmO,WAEtFR,aAAgB,EAAhBA,EAAmB3N,KAAU,yBAAK3B,UAAU,gBAC3C,yBAAK6M,IAAK5Q,EAAE,wCAAyC2Q,IAAK0C,EAAiB3N,GAAOmO,WAElF1B,GACA,4BACEpO,UAAU,cACV+P,eAAa,+BACbvP,QAAS,kBAAMiO,EAA2B9M,KAE1C,kBAACqO,GAAA,EAAI,CAACC,MAAM,wBAKpB,yBAAKjQ,UAAU,kBACb,yBAAKA,UAAU,kBAAkB/D,EAAE,2BAClCqT,aAAgB,EAAhBA,EAAmB3N,KAAU,yBAAK3B,UAAU,gBAAgB/D,EAAE,8BAKnE,yBAAK+D,UAAU,sBACZ/D,EAAE,yC,ubEzGb,gmGAAA5C,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,kvBAAAA,EAAA,gDAAAA,GAAA,oCAAAA,OAAA,sYAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,KAAAA,IAAA,6gBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAoBA,IA2Pe6W,GA3PQ,WACrB,IAsBE,KAXEhL,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUyI,kBAAkB1I,EAAOgL,KAAaC,iBAChDhL,IAAUiL,cAAclL,EAAOgL,KAAaC,iBAC5ChL,IAAUkL,kBAAkBnL,GAC5BC,IAAU4I,iBAAiB7I,GAC3BC,IAAUmL,2BAA2BpL,GACrCC,IAAUoL,yBAAyBrL,GACnCC,IAAUyI,kBAAkB1I,EAAOgL,KAAaM,sBAChDrL,IAAUsL,eAAevL,EAAOgL,KAAaC,iBAC7ChL,IAAUuI,uBAAuBxI,GACjCC,IAAU0I,iBAAiB3I,OAC3B,IArBAqC,EAAU,KACVmJ,EAAM,KACNC,EAAc,KACdrC,EAAa,KACbsC,EAAuB,KACvBpV,EAAqB,KACrBqV,EAAkB,KAClBC,EAAW,KACX7C,EAAmB,KACnBG,EAAa,KAcTzQ,EAAqBd,IAAKe,+BAA+B,6BACO,KAAd1B,oBAAS,GAAK,GAA/D6U,EAAoB,KAAEC,EAAuB,KACC,KAAX9U,mBAAS,GAAE,GAA9CoR,EAAa,KAAEC,EAAgB,KAEhCC,EAAWC,cACVzR,EAAqB,GAAhBC,cAAgB,GAApB,GAGRgV,cAAa,WACY,8BAAnBN,GACFnD,EACEoB,KAAQsC,cAAc,CACpBhB,KAAaC,gBACbD,KAAaiB,uBAIlB,CAAC3D,EAAUmD,IAEdhU,qBAAU,WACJ+T,GACFlD,EACEoB,KAAQsC,cAAc,CACpBhB,KAAakB,YACblB,KAAamB,cACbnB,KAAaoB,eACbpB,KAAaqB,iBAIlB,CAAC/D,EAAUkD,IAEd,IAAMc,EAAa,WAAM,IACuB,EADvB,KACK7T,GAAkB,IAA9C,IAAK,EAAL,qBAAgD,KAArCf,EAAa,QACtBA,EAAc6U,gBACd7U,EAAciB,aAAa,MAC3BjB,EAAckB,YAAY,OAC3B,8BACD0P,EAASoB,KAAQ8C,aAAaxB,KAAaC,mBAGvCwB,EAAgB,+BAAG,+EACvBC,IAEIpW,GACFqW,IACD,2CACF,kBANqB,mCAQhBD,EAAmB,+BAAG,6FAC1BjU,EAAmB,GAAGmU,eAAenU,EAAmB,GAAGkB,8BAClDzF,EAAI,EAAC,YAAEA,EAAIuE,EAAmB7D,QAAM,gCACrC6D,EAAmBvE,GAAGyE,aAAaF,EAAmB,GAAGkB,8BAA6B,OAD/CzF,IAAG,sBAImB,OAA/DwD,EAAgBe,EAAmBiT,EAA0B,GAAE,UAEzDhU,EAAc+B,mBAAkB,mCACI,GAA9C9B,IAAKoS,YAAY,6BAEbX,IAAkBtQ,IAAeC,eAAc,qBAC7CrB,EAAcmV,cAAe,CAAF,iCACvBnV,EAAcoV,eAAc,qCAENrU,GAAkB,2DAAtB,OAAbf,EAAa,kBAChBA,EAAcqV,cAAa,iJAIrCzE,EAASoB,KAAQ8C,aAAaxB,KAAaC,kBAAkB,iEAGlE,kBAvBwB,mCAyBnB0B,EAAc,+BAAG,6FACrBlU,EAAmB,GAAGuU,aAAavU,EAAmB,GAAGqB,yBAChD5F,EAAI,EAAC,YAAEA,EAAIuE,EAAmB7D,QAAM,gCACrC6D,EAAmBvE,GAAG8Y,aAAavU,EAAmB,GAAGqB,yBAAwB,OAD1C5F,IAAG,sBAImB,OAA/DwD,EAAgBe,EAAmBiT,EAA0B,GAAE,UACzDhU,EAAcuV,2BAA0B,mCACJ,GAA9CtV,IAAKoS,YAAY,6BAEbX,IAAkBtQ,IAAeE,SAAQ,qBACvCtB,EAAcmV,cAAe,CAAF,iCACvBnV,EAAcwV,cAAa,qCAELzU,GAAkB,2DAAtB,OAAbf,EAAa,kBAChBA,EAAcyV,sBAAqB,iJAK/C7E,EAASoB,KAAQ8C,aAAaxB,KAAaC,kBAE3C3C,EAASoB,KAAQ0D,iBAAiBtU,IAAeC,iBAAiB,iEAErE,kBAxBmB,mCA0BdJ,EAAY,+BAAG,WAAO6D,GAAK,kFACzB+M,EAAkBH,IAAkBtQ,IAAeC,eACzDuP,EAASoB,KAAQH,EAAkB,qCAAuC,qCAAqC/M,IAAQ,EAChG+M,EAAkBR,EAAoBvM,GAAS0M,EAAc1M,GAA5E6Q,EAAU,EAAVA,WACR1V,IAAKoS,YAAY,6BAA6B,KAClBtR,GAAkB,yDAAtB,OAAbf,EAAa,kBAChBA,EAAc6R,EAAkB,eAAiB,eAAe8D,GAAW,YAC7E3V,EAAcmV,cAAe,CAAF,iCACvBnV,EAAc6R,EAAkB,eAAiB,iBAAgB,iDAEjE7R,EAAc6R,EAAkB,cAAgB,yBAAwB,+IAGlFjB,EAASoB,KAAQ8C,aAAaxB,KAAaC,kBAAkB,gEAC9D,gBAdiB,sCAgBZ9U,EAAsBmD,uBAAY,WACtCwS,GAAwB,KACvB,CAACD,IAEExV,EAAqBiD,uBAAY,WACrCwS,GAAwB,KACvB,CAACD,IAEEyB,EAAazK,IAAW,CAC5B0K,OAAO,EACPxC,gBAAgB,EAChByC,KAAMhC,EACNiC,QAASjC,IAELkC,EAAqC,8BAAhB9B,EAE3B,OAAIvJ,EACK,KAIP,yBACExH,UAAWyS,EACX1C,eAAcI,KAAaC,iBAE3B,kBAAC0C,GAAA,EAAY,CACXC,MAAO9W,EAAE,mCACT+W,aAAcvB,EACdwB,aAAcxB,EACdd,OAAQA,EACRuC,cAAY,GAEZ,yBACElT,UAAWgI,IAAW,YAAa,CAAE,mBAAoBvM,IACzD0X,YAAa,SAACvL,GAAC,OAAKA,EAAExH,oBAEtB,yBAAKJ,UAAU,oBACf,kBAACoT,EAAA,EAAI,CAACvS,GAAG,kBACP,yBAAKb,UAAU,yBACb,yBAAKA,UAAU,aACX8Q,GACA,oCACE,kBAAC,IAAG,CAAClI,YAAY,6BACf,4BAAQ5I,UAAU,sBACf/D,EAAE,uBAGP,yBAAK+D,UAAU,yBAGnB,kBAAC,IAAG,CAAC4I,YAAY,2BACf,4BAAQ5I,UAAU,sBACf/D,EAAE,iBAGP,yBAAK+D,UAAU,wBACf,kBAAC,IAAG,CAAC4I,YAAY,4BACf,4BAAQ5I,UAAU,sBACf/D,EAAE,iBAGP,yBAAK+D,UAAU,wBACf,kBAAC,IAAG,CAAC4I,YAAY,6BACf,4BAAQ5I,UAAU,sBACf/D,EAAE,sBAKT6U,GAAsB,kBAAC,IAAQ,CAAClI,YAAY,uBAC5C,kBAAC,GAAe,CAAO2E,gBAAeC,sBAExC,kBAAC,IAAQ,CAAC5E,YAAY,qBACpB,kBAAC,EAAY,CACX1N,YAAayV,EACbnV,mBAAoBA,EACpBF,oBAAqBA,EACrBG,sBAAuBA,KAG3B,kBAAC,IAAQ,CAACmN,YAAY,sBACpB,kBAAC,EAAa,CACZ1N,YAAayV,EACbnV,mBAAoBA,EACpBF,oBAAqBA,EACrBG,sBAAuBA,KAG3B,kBAAC,IAAQ,CAACmN,YAAY,uBACpB,kBAAC,GAAc,CACb1N,YAAayV,EACbnV,mBAAoBA,EACpBF,oBAAqBA,EACrBG,sBAAuBA,KAG3B,yBAAKuE,UAAU,UACb,4BAAQA,UAAU,mBAAmBQ,QAAS6S,aAAgBR,EAAqB,kBAAM/U,EAAayP,IAAiBqE,GACrHnR,SAAUoS,GAAuBA,IAAuB3E,EAAoBnU,SAAW4W,GAAaA,GAAWK,EAC/G+B,MAAOtX,EAAwBQ,EAAE,6BAA+B,IAC/DA,EAAE4W,EAAqB,eAAiB,wBCpQ1C3C", "file": "chunks/chunk.39.js", "sourcesContent": ["var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./InkSignature.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ink-signature{width:100%;height:100%;padding:16px}.ink-signature-canvas-container{display:flex;flex-direction:column;background:var(--signature-draw-background);border-radius:4px;border:1px solid var(--modal-stroke-and-border);padding:8px;height:200px}.ink-signature-canvas-container.full-signature{flex:1.5 0 0}.ink-signature-canvas-container.initials{flex:1 0 0}.ink-signature .ink-signature-canvas{height:144px;border-bottom:1px solid var(--gray-6)}@media(max-height:500px){.App:not(.is-web-component) .ink-signature .ink-signature-canvas{height:116px}}@container (max-height: 500px){.App.is-web-component .ink-signature .ink-signature-canvas{height:116px}}@media(max-height:320px){.App:not(.is-web-component) .ink-signature .ink-signature-canvas{height:86px}}@container (max-height: 320px){.App.is-web-component .ink-signature .ink-signature-canvas{height:86px}}.ink-signature-canvas{z-index:1;width:inherit;height:inherit;cursor:crosshair}.ink-signature-footer{display:flex;flex-direction:row;border-top:1px solid var(--modal-stroke-and-border);padding-top:7px}.ink-signature-sign-here{color:var(--faded-text);height:48px;text-align:center;font-size:10px;flex:2 0 0}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.ink-signature-sign-here{left:44.5%}}.ink-signature .canvas-colorpalette-container{padding:0;box-sizing:border-box;display:flex;flex-direction:column}.ink-signature .colorpalette-clear-container{display:flex;height:44px;justify-content:space-between}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./TextSignature.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.text-signature{width:100%;height:100%;padding:16px}.text-signature canvas{display:none}.text-signature-input{outline:none;width:100%;background:transparent;border:none;border-bottom:1px solid var(--gray-6);border-radius:0;text-align:center;height:85px}@media(max-height:500px){.App:not(.is-web-component) .text-signature-input{font-size:42px!important;height:50px}}@container (max-height: 500px){.App.is-web-component .text-signature-input{font-size:42px!important;height:50px}}@media(max-height:320px){.App:not(.is-web-component) .text-signature-input{font-size:34px!important}}@container (max-height: 320px){.App.is-web-component .text-signature-input{font-size:34px!important}}.text-signature-input:focus{border:none;border-bottom:1px solid var(--gray-6)}.text-signature-inner-container{position:absolute;height:100%;min-width:100%}.text-signature-text{display:flex;align-items:center;background:var(--signature-draw-background);white-space:nowrap;border-radius:4px;padding:0 8px;margin-top:10px;overflow-y:auto;flex-grow:1;visibility:hidden;position:fixed}.text-signature-text:hover{cursor:pointer}.text-signature-text .text-container{margin:auto;padding:0 20px}.text-signature label{margin-top:60px;width:100%}.text-signature .Dropdown__items{top:unset!important}.text-signature .Dropdown__items [data-testid=sig-no-result]{font-size:13px!important}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./ImageSignature.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.image-signature{width:100%;height:100%;padding:16px}@media(max-height:500px){.App:not(.is-web-component) .image-signature{height:192px}}@container (max-height: 500px){.App.is-web-component .image-signature{height:192px}}@media(max-height:320px){.App:not(.is-web-component) .image-signature{height:162px}}@container (max-height: 320px){.App.is-web-component .image-signature{height:162px}}.image-signature .image-signature-modal-overlay{position:absolute;width:100%;height:480px;background:transparent;z-index:9999;left:0;top:-100px}.image-signature-image-container{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.image-signature-image-container img{max-height:100%;max-width:100%;width:auto;height:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.image-signature-image-container img{flex-grow:1;flex-basis:0}}.image-signature-image-container .Button{position:absolute;bottom:0;right:0}.image-signature .mobile{background:var(--signature-draw-background);border:1px solid var(--modal-stroke-and-border)}.image-signature-upload-container{position:relative;border-radius:4px;border:1px dashed var(--modal-stroke-and-border);display:flex;flex-direction:column;justify-content:center;align-items:center;width:100%;height:100%}.image-signature-upload-container.dragging{background:var(--image-signature-drop-background);border:1px dashed var(--image-signature-drop-border)}.image-signature-dnd,.image-signature-separator{color:var(--faded-text)}.image-signature-upload input[type=file]{display:none}.image-signature-upload .pick-image-button{height:24px;border-radius:4px;border:1px solid var(--primary-button);color:var(--primary-button);display:flex;align-items:center;padding:0 11px;cursor:pointer}.image-signature-upload .pick-image-button:hover{border:1px solid var(--primary-button-hover);color:var(--primary-button-hover)}.image-signature-upload .pick-image-button.focus-visible,.image-signature-upload .pick-image-button:focus-visible{outline:var(--focus-visible-outline)}.image-signature-separator{margin:10px}.image-signature-error{position:absolute;color:red;bottom:0;right:0;margin:0 5px 5px 0}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./SavedSignatures.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SavedSignatures{width:100%;height:276px;padding:16px;overflow-y:auto;display:flex;grid-gap:16px;gap:16px;flex-direction:column}@media(max-height:500px){.App:not(.is-web-component) .SavedSignatures{height:192px}}@container (max-height: 500px){.App.is-web-component .SavedSignatures{height:192px}}@media(max-height:320px){.App:not(.is-web-component) .SavedSignatures{height:162px}}@container (max-height: 320px){.App.is-web-component .SavedSignatures{height:162px}}.SavedSignatures .signature-row{display:flex;flex-direction:column;justify-content:center;height:25%;width:100%}.SavedSignatures .signature-row .inputContainer{width:100%;display:grid;grid-gap:8px;gap:8px;align-items:center;grid-template-columns:20px 1fr}.SavedSignatures .signature-row .inputContainer input{width:16px;height:16px}.SavedSignatures .signature-row .inputContainer input[type=radio]:checked{accent-color:var(--blue-5)}.SavedSignatures .signature-row .inputContainer .contentContainer{padding-bottom:4px;margin-bottom:4px;border-bottom:1px solid var(--gray-5);display:grid;align-items:center;grid-template-columns:1fr 1fr 1fr}.SavedSignatures .signature-row .inputContainer .contentContainer .icon-button{justify-self:end;padding:0;border:none;background-color:transparent;display:flex;align-items:center;justify-content:center;width:34px;border-radius:4px;cursor:pointer}:host(:not([data-tabbing=true])) .SavedSignatures .signature-row .inputContainer .contentContainer .icon-button,html:not([data-tabbing=true]) .SavedSignatures .signature-row .inputContainer .contentContainer .icon-button{outline:none}.SavedSignatures .signature-row .inputContainer .contentContainer .icon-button:hover{background:var(--tools-overlay-button-hover)}.SavedSignatures .signature-row .inputContainer .contentContainer .radioButton{border-radius:100px;border:1px solid var(--blue-5);width:16px;height:16px;border-spacing:2px}.SavedSignatures .signature-row .inputContainer .contentContainer .radioButton.selected{background-color:var(--blue-5)}.SavedSignatures .signature-row .inputContainer .contentContainer .imgContainer{width:162px;height:26px}.SavedSignatures .signature-row .inputContainer .contentContainer .imgContainer img{max-width:100%;max-height:100%}.SavedSignatures .signature-row .labelContainer{display:grid;grid-template-columns:1fr 2fr;padding-left:28px;color:var(--gray-7)}.SavedSignatures .signature-row.no-initials .contentContainer{grid-template-columns:1fr 2fr}.SavedSignatures .emptyListContainer{height:100%;display:flex;align-items:center;justify-content:center;text-align:center}.SavedSignatures.empty{grid-template-rows:1fr}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./SignatureModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.SignatureModal{visibility:visible}.closed.SignatureModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SignatureModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.SignatureModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.SignatureModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.SignatureModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.SignatureModal .footer .modal-button.cancel:hover,.SignatureModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.SignatureModal .footer .modal-button.cancel,.SignatureModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.SignatureModal .footer .modal-button.cancel.disabled,.SignatureModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.SignatureModal .footer .modal-button.cancel.disabled span,.SignatureModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.SignatureModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.SignatureModal .modal-container .wrapper .modal-content{padding:10px}.SignatureModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.SignatureModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.SignatureModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.SignatureModal .footer .modal-button.confirm{margin-left:4px}.SignatureModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .footer .modal-button{padding:23px 8px}}.SignatureModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .swipe-indicator{width:32px}}.SignatureModal .modal-container .tab-list{width:100%;height:28px;display:flex;border-radius:4px;color:var(--text-color)}.SignatureModal .modal-container .tab-list .tab-options-button{text-align:center;vertical-align:middle;line-height:24px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;flex:1;border-radius:0;cursor:pointer}.SignatureModal .modal-container .tab-list .tab-options-button:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.SignatureModal .modal-container .tab-list .tab-options-button:last-child{border-bottom-right-radius:4px;border-top-right-radius:4px}.SignatureModal .modal-container .tab-list .tab-options-button:hover{background:var(--popup-button-hover)}.SignatureModal .modal-container .tab-list .tab-options-button.selected{cursor:default}.SignatureModal .modal-container .tab-list .tab-options-button.focus-visible,.SignatureModal .modal-container .tab-list .tab-options-button:focus-visible{outline:var(--focus-visible-outline)}.SignatureModal .modal-container .tab-panel{width:100%;display:flex;flex-direction:column;align-items:center}.SignatureModal .modal-container .tab-panel.focus-visible,.SignatureModal .modal-container .tab-panel:focus-visible{outline:var(--focus-visible-outline)!important}.SignatureModal .modal-container{display:flex;flex-direction:column;justify-content:space-between;width:480px;padding:0;border-radius:4px;background:var(--component-background);overflow-y:visible}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .modal-container{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .modal-container{width:100%}}.SignatureModal .modal-container.include-initials{width:664px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .modal-container{border-radius:0;height:440px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .modal-container{border-radius:0;height:440px}}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .modal-container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}@container (max-height: 320px){.App.is-web-component .SignatureModal .modal-container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}.SignatureModal .modal-container .tabs-header-container{padding:16px 16px 0}.SignatureModal .modal-container .header{margin:0;display:flex;align-items:center;width:100%;height:24px}.SignatureModal .modal-container .header p{font-size:16px;font-weight:700;width:calc(100% - 32px);margin:0 16px 0 0}.SignatureModal .modal-container .header .signatureModalCloseButton{position:static;height:32px;width:32px}.SignatureModal .modal-container .header .signatureModalCloseButton:hover{background:var(--gray-2);border-radius:4px}.SignatureModal .modal-container .header .signatureModalCloseButton.selected{background:var(--view-header-button-active);cursor:default}.SignatureModal .modal-container .StylePopup{border-radius:0;box-shadow:none}.SignatureModal .modal-container .tab-panel{overflow-y:auto}.SignatureModal .modal-container .tab-list{font-size:14px}.SignatureModal .modal-container .tab-list .tab-options-button{padding:0;border:none;background-color:transparent}:host(:not([data-tabbing=true])) .SignatureModal .modal-container .tab-list .tab-options-button,html:not([data-tabbing=true]) .SignatureModal .modal-container .tab-list .tab-options-button{outline:none}.SignatureModal .modal-container .signature-style-options{width:90%;display:flex}.SignatureModal .modal-container .signature-style-options .ColorPalette{margin:8px;grid-template-columns:repeat(3,1fr);width:100px}.SignatureModal .modal-container .signature-style-options .ColorPalette :not(:last-child){margin-right:8px}.SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container{width:28px;height:28px}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container{width:22px;height:22px}}@container (max-height: 320px){.App.is-web-component .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container{width:22px;height:22px}}.SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer .cell{width:16px;height:16px}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer .cell{width:14px;height:14px}}@container (max-height: 320px){.App.is-web-component .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer .cell{width:14px;height:14px}}.SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer.active{width:28px;height:28px}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer.active{width:22px;height:22px}}@container (max-height: 320px){.App.is-web-component .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer.active{width:22px;height:22px}}.SignatureModal .modal-container .signature-clear{background-color:transparent;color:var(--secondary-button-text);padding:0 16px;display:flex;align-items:center;justify-content:center;cursor:pointer;border:none;height:28px;width:63px;margin:auto 8px;font-size:13px}:host(:not([data-tabbing=true])) .SignatureModal .modal-container .signature-clear,html:not([data-tabbing=true]) .SignatureModal .modal-container .signature-clear{outline:none}.SignatureModal .modal-container .signature-clear:enabled:hover{color:var(--secondary-button-hover)}.SignatureModal .modal-container .signature-clear:disabled{opacity:.5}.SignatureModal .modal-container .footer{display:flex;padding:16px;align-items:center;justify-content:flex-end;width:100%;box-shadow:inset 0 1px 0 var(--modal-stroke-and-border);margin:0}.SignatureModal .modal-container .footer .signature-create{border:none;background-color:transparent;background:var(--primary-button);border-radius:4px;padding:0 8px;height:32px;width:72px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer}:host(:not([data-tabbing=true])) .SignatureModal .modal-container .footer .signature-create,html:not([data-tabbing=true]) .SignatureModal .modal-container .footer .signature-create{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .modal-container .footer .signature-create{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .modal-container .footer .signature-create{font-size:13px}}.SignatureModal .modal-container .footer .signature-create:enabled:hover{background:var(--primary-button-hover)}.SignatureModal .modal-container .footer .signature-create:disabled{opacity:.5;cursor:default}.SignatureModal .modal-container .footer .signature-create.focus-visible,.SignatureModal .modal-container .footer .signature-create:focus-visible{outline:var(--focus-visible-outline)}.SignatureModal .modal-container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.SignatureModal .modal-container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.SignatureModal .modal-container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.SignatureModal .modal-container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.SignatureModal .modal-container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.SignatureModal .modal-container .tab-list .tab-options-button:hover+button,.SignatureModal .modal-container .tab-list .tab-options-button:hover+div{border-left:none}.SignatureModal .modal-container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.SignatureModal .modal-container .tab-list .tab-options-button.selected+button,.SignatureModal .modal-container .tab-list .tab-options-button.selected+div{border-left:none!important}.SignatureModal .modal-container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}.SignatureModal .colorpalette-clear-container{display:flex;height:38px;box-sizing:border-box;justify-content:space-between;align-items:baseline;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.SignatureModal .colorpalette-clear-container .signature-style-options{width:90%;display:flex}.SignatureModal .colorpalette-clear-container .signature-style-options .divider{display:inline-block;border-left:1px solid var(--modal-stroke-and-border);margin:auto 8px auto 16px;height:16px}.SignatureModal .colorpalette-clear-container .signature-style-options .placeholder-dropdown{width:160px;height:31px;margin:auto 0}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper{width:160px;height:31px;position:absolute;margin:8px 0 auto}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown{width:100%!important;height:100%;text-align:left}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .picked-option-text{margin-right:-18px;padding-left:2px;font-size:13px}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .arrow{flex:unset}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__items{bottom:auto;top:100%;width:100%;left:0;right:auto}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__items button:nth-child(n){font-size:15px}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__items button:nth-child(2){font-size:13px}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__items button:nth-child(4){font-size:18px}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__item{display:block;padding-right:16px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;text-align:left}.SignatureModal .footer-signature-clear{padding:0;background-color:transparent;color:var(--secondary-button-text);display:flex;flex:0 0 0;align-items:baseline;justify-content:center;cursor:pointer;border:none;height:28px;width:63px;font-size:13px}:host(:not([data-tabbing=true])) .SignatureModal .footer-signature-clear,html:not([data-tabbing=true]) .SignatureModal .footer-signature-clear{outline:none}.SignatureModal .footer-signature-clear:enabled:hover{color:var(--secondary-button-hover)}.SignatureModal .footer-signature-clear:disabled{opacity:.5}.SignatureModal .footer-signature-clear.focus-visible,.SignatureModal .footer-signature-clear:focus-visible{outline:var(--focus-visible-outline)}.SignatureModal .signature-input{background:var(--signature-draw-background);border:1px solid var(--modal-stroke-and-border);display:flex;flex-direction:column;grid-gap:8px;gap:8px;align-items:center;justify-content:center;padding:8px;width:100%;height:200px;border-radius:4px}.SignatureModal .signature-input.full-signature{flex:1.5 0 0}.SignatureModal .signature-input.initials{flex:1 0 0}@media(max-height:500px){.App:not(.is-web-component) .SignatureModal .signature-input{height:116px}}@container (max-height: 500px){.App.is-web-component .SignatureModal .signature-input{height:116px}}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .signature-input{height:86px}}@container (max-height: 320px){.App.is-web-component .SignatureModal .signature-input{height:86px}}.SignatureModal .signature-input.image{height:244px}.SignatureModal .signature-input-footer{display:flex;flex-direction:row;width:100%}.SignatureModal .signature-input .signature-prompt{font-size:10px;color:var(--faded-text);flex:3 0 0;text-align:center}.SignatureModal .signature-and-initials-container{display:flex;flex-direction:row;grid-gap:8px;gap:8px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport Measure from 'react-measure';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport setToolStyles from 'helpers/setToolStyles';\nimport { Swipeable } from 'react-swipeable';\nimport ColorPalette from 'components/ColorPalette';\nimport Dropdown from 'components/Dropdown';\nimport SignatureModes from 'constants/signatureModes';\nimport core from 'core';\nimport { COMMON_COLORS, BASIC_PALETTE } from 'constants/commonColors';\n\nimport './InkSignature.scss';\n\nconst useForceUpdate = () => {\n  const [, setIt] = useState(false);\n  return () => setIt((it) => !it);\n};\n\nconst propTypes = {\n  isModalOpen: PropTypes.bool,\n  isTabPanelSelected: PropTypes.bool,\n  disableCreateButton: PropTypes.func,\n  enableCreateButton: PropTypes.func,\n  isInitialsModeEnabled: PropTypes.bool,\n};\n\nconst InkSignature = ({\n  isModalOpen,\n  isTabPanelSelected,\n  disableCreateButton,\n  enableCreateButton,\n  isInitialsModeEnabled = false\n}) => {\n  const fullSignatureCanvas = useRef();\n  const initialsCanvas = useRef();\n  // the ref holds the path points of the underlying freehand annotation\n  // when users switch to a different tab the underlying signature annotation will change\n  // so this ref is used for setting the current uderlying annotation back when users switch back to the ink tab\n  const fullSignaturePathsRef = useRef();\n  const initialsPathsRef = useRef();\n  // the ref holds an id that will be used to check if the newly added signature annotation is the same as the freehand annotation that's drawn in the canvas\n  const [t] = useTranslation();\n  const [dimension, setDimension] = useState({});\n  const [fullSignatureDrawn, setFullSignatureDrawn] = useState(false);\n  const [initialsDrawn, setInitialsDrawn] = useState(false);\n\n  const forceUpdate = useForceUpdate();\n\n  useEffect(() => {\n    const signatureTool = core.getTool('AnnotationCreateSignature');\n    const canvas = fullSignatureCanvas.current;\n\n    signatureTool.setSignatureCanvas(canvas);\n    const multiplier = window.Core.getCanvasMultiplier();\n    canvas.getContext('2d').scale(multiplier, multiplier);\n\n    // Now set the initials canvas\n    const secondCanvas = initialsCanvas.current;\n    signatureTool.setInitialsCanvas(secondCanvas);\n    secondCanvas.getContext('2d').scale(multiplier, multiplier);\n  }, []);\n\n  useEffect(() => {\n    if (isModalOpen) {\n      clearFullSignatureCanvas();\n      clearInitialsCanvas();\n    }\n  }, [isModalOpen]);\n\n  useEffect(() => {\n    async function resizeFullSignatureCanvas() {\n      if (isModalOpen && isTabPanelSelected) {\n        const signatureToolArray = core.getToolsFromAllDocumentViewers('AnnotationCreateSignature');\n        for (const signatureTool of signatureToolArray) {\n          signatureTool.setSignature(fullSignaturePathsRef.current);\n          signatureTool.setInitials(initialsPathsRef.current);\n          // use resizeCanvas here mainly for redawing the underlying signature annotation to make it show on the canvas\n          await signatureTool.resizeCanvas(SignatureModes.FULL_SIGNATURE);\n        }\n      }\n    }\n\n\n    async function resizeInitialsCanvas() {\n      if (isModalOpen && isTabPanelSelected && isInitialsModeEnabled) {\n        const signatureToolArray = core.getToolsFromAllDocumentViewers('AnnotationCreateSignature');\n        for (const signatureTool of signatureToolArray) {\n          signatureTool.setInitials(initialsPathsRef.current);\n          await signatureTool.resizeCanvas(SignatureModes.INITIALS);\n        }\n      }\n    }\n\n    function checkEnableCreateButton() {\n      if (isInitialsModeEnabled) {\n        (fullSignaturePathsRef.current && initialsPathsRef.current) ? enableCreateButton() : disableCreateButton();\n      } else {\n        fullSignaturePathsRef.current ? enableCreateButton() : disableCreateButton();\n      }\n    }\n\n    resizeFullSignatureCanvas();\n    resizeInitialsCanvas();\n    checkEnableCreateButton();\n  }, [isTabPanelSelected, isModalOpen, isInitialsModeEnabled]);\n\n  useEffect(() => {\n    async function resizeCanvasAsyncCall() {\n      if (dimension.height && dimension.width) {\n        const signatureTool = core.getTool('AnnotationCreateSignature');\n        await signatureTool.resizeCanvas();\n\n        if (isInitialsModeEnabled) {\n          await signatureTool.resizeCanvas(SignatureModes.INITIALS);\n        }\n      }\n    }\n    resizeCanvasAsyncCall();\n  }, [dimension, isInitialsModeEnabled]);\n\n  useEffect(() => {\n    if (fullSignatureDrawn && (!isInitialsModeEnabled || initialsDrawn)) {\n      enableCreateButton();\n    } else {\n      disableCreateButton();\n    }\n  }, [initialsDrawn, fullSignatureDrawn, isInitialsModeEnabled]);\n\n  const clearFullSignatureCanvas = useCallback(() => {\n    const signatureTool = core.getTool('AnnotationCreateSignature');\n    signatureTool.clearSignatureCanvas();\n    fullSignaturePathsRef.current = null;\n    setFullSignatureDrawn(false);\n  }, []);\n\n  const clearInitialsCanvas = useCallback(() => {\n    const signatureTool = core.getTool('AnnotationCreateSignature');\n    signatureTool.clearInitialsCanvas();\n    initialsPathsRef.current = null;\n    setInitialsDrawn(false);\n  }, []);\n\n  const handleFinishDrawingFullSignature = async () => {\n    const signatureTool = core.getTool('AnnotationCreateSignature');\n    if (!(await signatureTool.isEmptySignature())) {\n      // need to deep copy the paths because it will be modified\n      // when the annotation is added to the document\n      // we want to keep the unmodified paths so that users can keep drawing on the canvas\n      fullSignaturePathsRef.current = deepCopy(signatureTool.getFullSignatureAnnotation().getPaths());\n      setFullSignatureDrawn(true);\n    }\n  };\n\n  const handleFinishDrawingInitials = async () => {\n    const signatureTool = core.getTool('AnnotationCreateSignature');\n    const initialsAnnotation = signatureTool.getInitialsAnnotation();\n    if (initialsAnnotation) {\n      // need to deep copy the paths because it will be modified\n      // when the annotation is added to the document\n      // we want to keep the unmodified paths so that users can keep drawing on the canvas\n      initialsPathsRef.current = deepCopy(signatureTool.getInitialsAnnotation().getPaths());\n      setInitialsDrawn(true);\n    }\n  };\n\n  const handleColorInputChange = (property, value) => {\n    setToolStyles('AnnotationCreateSignature', property, value);\n    const signatureTool = core.getTool('AnnotationCreateSignature');\n    if (signatureTool.getFullSignatureAnnotation()) {\n      signatureTool.getFullSignatureAnnotation().StrokeColor = value;\n      signatureTool.resizeCanvas(SignatureModes.FULL_SIGNATURE);\n    }\n\n    if (signatureTool.getInitialsAnnotation()) {\n      signatureTool.getInitialsAnnotation().StrokeColor = value;\n      signatureTool.resizeCanvas(SignatureModes.INITIALS);\n    }\n    // hack for tool styles for signature not being on state\n    forceUpdate();\n  };\n\n  const deepCopy = (paths) => {\n    const pathsCopy = [];\n    for (let i = 0; i < paths.length; ++i) {\n      for (let j = 0; j < paths[i].length; ++j) {\n        if (!pathsCopy[i]) {\n          pathsCopy[i] = [];\n        }\n        pathsCopy[i][j] = new window.Core.Math.Point(paths[i][j].x, paths[i][j].y);\n      }\n    }\n\n    return pathsCopy;\n  };\n\n  const signatureTool = core.getTool('AnnotationCreateSignature');\n  const toolStyles = signatureTool.defaults;\n  const initialsContainerStyle = isInitialsModeEnabled ? {} : { display: 'none' };\n\n  return (\n    <Measure bounds onResize={({ bounds }) => setDimension(bounds)}>\n      {({ measureRef }) => (\n        <div className=\"ink-signature\" ref={measureRef}>\n          <Swipeable\n            onSwiping={({ event }) => event.stopPropagation()}\n            className=\"canvas-colorpalette-container\"\n          >\n            <div className='signature-and-initials-container'>\n              <div className='signature-input full-signature'>\n                <canvas\n                  className=\"ink-signature-canvas\"\n                  onMouseUp={handleFinishDrawingFullSignature}\n                  onTouchEnd={handleFinishDrawingFullSignature}\n                  onMouseLeave={handleFinishDrawingFullSignature}\n                  ref={fullSignatureCanvas}\n                />\n                <div className=\"signature-input-footer\">\n                  <div className=\"signature-prompt\">\n                    {t('option.signatureModal.drawSignature')}\n                  </div>\n                  <button\n                    className=\"footer-signature-clear\"\n                    onClick={clearFullSignatureCanvas}\n                    disabled={!fullSignatureDrawn}\n                    aria-label={t('action.clearSignature')}\n                  >\n                    {t('action.clear')}\n                  </button>\n                </div>\n              </div>\n              <div className='signature-input initials' style={initialsContainerStyle}>\n                <canvas\n                  className=\"ink-signature-canvas\"\n                  onMouseUp={handleFinishDrawingInitials}\n                  onTouchEnd={handleFinishDrawingInitials}\n                  onMouseLeave={handleFinishDrawingInitials}\n                  ref={initialsCanvas}\n                />\n                <div className=\"signature-input-footer\">\n                  <div className=\"signature-prompt\">\n                    {t('option.signatureModal.drawInitial')}\n                  </div>\n                  <button\n                    className=\"footer-signature-clear\"\n                    onClick={clearInitialsCanvas}\n                    disabled={!initialsDrawn}\n                    aria-label={t('action.clearInitial')}\n                  >\n                    {t('action.clear')}\n                  </button>\n                </div>\n              </div>\n            </div>\n            <div className=\"colorpalette-clear-container\">\n              <div className=\"signature-style-options\">\n                <Dropdown\n                  id=\"ink-signature-font-dropdown\"\n                  disabled={true}\n                  placeholder={'Text Styles'}\n                />\n                <div className=\"placeholder-dropdown\"></div>\n                <div className=\"divider\"></div>\n                <ColorPalette\n                  color={toolStyles['StrokeColor']}\n                  property=\"StrokeColor\"\n                  onStyleChange={(property, value) => handleColorInputChange(property, value)}\n                  /* eslint-disable-next-line custom/no-hex-colors */\n                  overridePalette2={[COMMON_COLORS['black'], BASIC_PALETTE[12], BASIC_PALETTE[7]]}\n                />\n              </div>\n            </div>\n          </Swipeable>\n        </div>\n      )}\n    </Measure>\n  );\n};\n\nInkSignature.propTypes = propTypes;\n\nexport default InkSignature;\n", "import InkSignature from './InkSignature';\n\nexport default InkSignature;", "\n// Crops the image more accurately by removing white space from all side of the image.\nfunction cropImageFromCanvas(canvas) {\n  const ctx = canvas.getContext('2d');\n  let width = canvas.width;\n  let height = canvas.height;\n  const pixels = { x: [], y: [] };\n  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n  let x;\n  let y;\n  let index;\n\n  for (y = 0; y < height; y++) {\n    for (x = 0; x < width; x++) {\n      index = (y * width + x) * 4;\n      if (imageData.data[index + 3] > 0) {\n        pixels.x.push(x);\n        pixels.y.push(y);\n      }\n    }\n  }\n  pixels.x.sort(function(a, b) {\n    return a - b;\n  });\n  pixels.y.sort(function(a, b) {\n    return a - b;\n  });\n  const n = pixels.x.length - 1;\n\n  width = 1 + pixels.x[n] - pixels.x[0];\n  height = 1 + pixels.y[n] - pixels.y[0];\n  const cut = ctx.getImageData(pixels.x[0], pixels.y[0], width, height);\n\n  canvas.width = width;\n  canvas.height = height;\n  ctx.putImageData(cut, 0, 0);\n\n  return canvas.toDataURL();\n}\n\nexport default cropImageFromCanvas;", "import React, { useState, useEffect, useRef } from 'react';\nimport { useSelector } from 'react-redux';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport ColorPalette from 'components/ColorPalette';\nimport Dropdown from 'components/Dropdown';\nimport core from 'core';\nimport { isIOS, isMobile } from 'helpers/device';\nimport cropImageFromCanvas from 'helpers/cropImageFromCanvas';\nimport selectors from 'selectors';\nimport { useTranslation } from 'react-i18next';\nimport { COMMON_COLORS, BASIC_PALETTE } from 'constants/commonColors';\n\nimport './TextSignature.scss';\nimport getRootNode from 'helpers/getRootNode';\n\nconst propTypes = {\n  isModalOpen: PropTypes.bool,\n  isTabPanelSelected: PropTypes.bool,\n  disableCreateButton: PropTypes.func,\n  enableCreateButton: PropTypes.func,\n  isInitialsModeEnabled: PropTypes.bool,\n};\n\nconst FONT_SIZE = 72;\nconst TYPED_SIGNATURE_FONT_SIZE = (FONT_SIZE * 2) / 3;\nconst MAX_SIGNATURE_LENGTH = 350;\nconst DEFAULT_FONT_COLOR = COMMON_COLORS['black'];\nconst CANVAS_MULTIPLIER = window.Core.getCanvasMultiplier();\nconst TEXT_CLIP_PADDING = 100;\n\nconst useForceUpdate = () => {\n  const [, setIt] = useState(false);\n  return () => setIt((it) => !it);\n};\n\nconst parseInitialsFromFullSignature = (fullSiganture) => {\n  return fullSiganture?.split(' ').map((x) => x[0]).join('').toUpperCase();\n};\n\nconst getSignatureLength = (text, fontSize, fontFamily) => {\n  const font = `${fontSize}px ${fontFamily}`;\n  const textSpan = document.createElement('span');\n  textSpan.id = 'textSpan';\n  textSpan.style.display = 'inline-block';\n  textSpan.style.visibility = 'hidden';\n  textSpan.style.font = font;\n  const rootNode = getRootNode();\n  if (window.isApryseWebViewerWebComponent) {\n    rootNode.appendChild(textSpan);\n  } else {\n    rootNode.getElementsByTagName('body')[0].appendChild(textSpan);\n  }\n\n  textSpan.textContent = text;\n\n  const signatureWidth = textSpan.getBoundingClientRect().width;\n  textSpan.remove();\n  return signatureWidth;\n};\n\nconst scaleFontSize = (text, fontFamily) => {\n  let minFontSize = 0;\n  let maxFontSize = TYPED_SIGNATURE_FONT_SIZE;\n  let currentFontSize;\n\n  while (minFontSize <= maxFontSize) {\n    currentFontSize = Math.floor((minFontSize + maxFontSize) / 2);\n\n    const signatureWidth = getSignatureLength(text, currentFontSize, fontFamily);\n    if (signatureWidth > MAX_SIGNATURE_LENGTH) {\n      maxFontSize = currentFontSize - 1;\n    } else {\n      minFontSize = currentFontSize + 1;\n    }\n  }\n  return currentFontSize;\n};\n\nconst resizeCanvas = (canvas, measurementReference, overrideMultiplier = 0) => {\n  let { width, height } = measurementReference.current.getBoundingClientRect();\n  width += TEXT_CLIP_PADDING;\n  height += TEXT_CLIP_PADDING;\n  canvas.style.width = `${width}px`;\n  canvas.style.height = `${height}px`;\n  canvas.width = width * Math.max(overrideMultiplier, CANVAS_MULTIPLIER);\n  canvas.height = height * Math.max(overrideMultiplier, CANVAS_MULTIPLIER);\n};\n\nconst setFontInCanvas = ({ canvas, text, selectedFontFamily, fontColor, overrideMultiplier = 0 }) => {\n  const ctx = canvas.getContext('2d');\n  const fontSize = scaleFontSize(text, selectedFontFamily);\n  ctx.fillStyle = fontColor;\n  ctx.textAlign = 'center';\n  ctx.textBaseline = 'middle';\n  ctx.font = `${fontSize * Math.max(overrideMultiplier, CANVAS_MULTIPLIER)}px ${selectedFontFamily}`;\n};\n\nconst drawTextInCanvas = (canvas, text) => {\n  const ctx = canvas.getContext('2d');\n  const { width, height } = canvas;\n  ctx.clearRect(0, 0, width, height);\n  ctx.fillText(text, width / 2, height / 2);\n};\n\nconst TextSignature = ({\n  isModalOpen,\n  isTabPanelSelected,\n  disableCreateButton,\n  enableCreateButton,\n  isInitialsModeEnabled = false,\n}) => {\n  const fonts = useSelector((state) => selectors.getSignatureFonts(state));\n  const textSignatureCanvasMultiplier = useSelector((state) => selectors.getTextSignatureQuality(state));\n  const [fullSignature, setFullSiganture] = useState('');\n  const [initials, setInitials] = useState('');\n  const [isDefaultValue, setIsDefaultValue] = useState(true);\n  const [fontColor, setFontColor] = useState(new window.Core.Annotations.Color(DEFAULT_FONT_COLOR));\n  const [fontSize, setFontSize] = useState(TYPED_SIGNATURE_FONT_SIZE);\n  const inputRef = useRef();\n  const fullSignatureHiddenCanvasRef = useRef();\n  const initialsHiddenCanvasRef = useRef();\n  const hiddenFullSignatureRef = useRef();\n  const hiddenInitialsRef = useRef();\n  const [t] = useTranslation();\n\n  const [selectedFontFamily, setSelectedFontFamily] = useState(fonts[0]);\n\n  const forceUpdate = useForceUpdate();\n\n  // Create button is only enabled when there's a signature\n  // if initials are enabled, then those must also be filled in\n  useEffect(() => {\n    if (fullSignature?.length > 0 && (!isInitialsModeEnabled || initials)) {\n      enableCreateButton();\n    } else {\n      disableCreateButton();\n    }\n  }, [initials, fullSignature, isInitialsModeEnabled]);\n\n  useEffect(() => {\n    // this can happen when an user added a new signature font, select it and then removed it\n    // in this case we just assume there's at least one font and set the active index to 0\n    if (!fonts.includes(selectedFontFamily)) {\n      setSelectedFontFamily(fonts[0]);\n    }\n  }, [selectedFontFamily, fonts]);\n\n  useEffect(() => {\n    const initialsCanvas = initialsHiddenCanvasRef.current;\n\n    if (isTabPanelSelected) {\n      resizeCanvas(initialsCanvas, hiddenInitialsRef);\n      setFontInCanvas({\n        canvas: initialsCanvas,\n        text: initials,\n        selectedFontFamily,\n        fontColor,\n      });\n      drawTextInCanvas(initialsCanvas, initials);\n\n      if (isModalOpen) {\n        setInitialsInTool();\n      }\n    }\n  }, [isTabPanelSelected, initials, fonts, fontColor, selectedFontFamily]);\n\n  useEffect(() => {\n    const fullSignatureCanvas = fullSignatureHiddenCanvasRef.current;\n\n    if (isTabPanelSelected) {\n      resizeCanvas(fullSignatureCanvas, hiddenFullSignatureRef, textSignatureCanvasMultiplier);\n      setFontInCanvas({\n        canvas: fullSignatureCanvas,\n        text: fullSignature,\n        selectedFontFamily,\n        fontColor,\n        overrideMultiplier: textSignatureCanvasMultiplier,\n      });\n      drawTextInCanvas(fullSignatureCanvas, fullSignature);\n      if (isModalOpen) {\n        setSignature();\n      }\n    }\n  }, [isTabPanelSelected, fullSignature, fonts, fontColor, selectedFontFamily]);\n\n  useEffect(() => {\n    setFontColor(fontColor);\n    if (isModalOpen && isTabPanelSelected) {\n      const currentUser = core.getDisplayAuthor(core.getCurrentUser());\n      setFullSiganture(currentUser);\n      setInitials(parseInitialsFromFullSignature(currentUser));\n      setSignature();\n    }\n  }, [isModalOpen, isTabPanelSelected]);\n\n  useEffect(() => {\n    if (isTabPanelSelected) {\n      inputRef.current?.focus();\n\n      if (isIOS) {\n        inputRef.current.setSelectionRange(0, 9999);\n      } else {\n        inputRef.current.select();\n      }\n    }\n  }, [isTabPanelSelected]);\n\n  useEffect(() => {\n    const onUpdateAnnotationPermission = () => {\n      if (isDefaultValue) {\n        const currentUser = core.getDisplayAuthor(core.getCurrentUser());\n        setFullSiganture(currentUser);\n        setInitials(parseInitialsFromFullSignature(currentUser));\n        enableCreateButton();\n      }\n    };\n\n    core.addEventListener('updateAnnotationPermission', onUpdateAnnotationPermission);\n    return () => {\n      core.removeEventListener('updateAnnotationPermission', onUpdateAnnotationPermission);\n    };\n  }, [isDefaultValue]);\n\n  const setSignature = () => {\n    const signatureToolArray = core.getToolsFromAllDocumentViewers('AnnotationCreateSignature');\n    const canvas = fullSignatureHiddenCanvasRef.current;\n\n    const signatureValue = fullSignature || '';\n    if (signatureValue.trim()) {\n      const base64 = cropImageFromCanvas(canvas);\n      signatureToolArray.forEach((tool) => tool.setSignature(base64));\n    } else {\n      signatureToolArray.forEach((tool) => tool.setSignature(null));\n    }\n  };\n\n  const setInitialsInTool = () => {\n    const signatureToolArray = core.getToolsFromAllDocumentViewers('AnnotationCreateSignature');\n    const canvas = initialsHiddenCanvasRef.current;\n\n    const initialsValue = initials || '';\n    if (initialsValue.trim()) {\n      const base64 = cropImageFromCanvas(canvas);\n      signatureToolArray.forEach((tool) => tool.setInitials(base64));\n    } else {\n      signatureToolArray.forEach((tool) => tool.setInitials(null));\n    }\n  };\n\n  const handleFullSignatureChange = (e) => {\n    setIsDefaultValue(false);\n    // Use regex instead of 'trimStart' for IE11 compatibility\n    const value = e.target.value.replace(/^\\s+/g, '');\n    setSignature();\n    setFullSiganture(value);\n    setInitials(parseInitialsFromFullSignature(value));\n    const newFontSize = scaleFontSize(value, selectedFontFamily);\n    setFontSize(newFontSize);\n  };\n\n  const handleInitialsChange = (e) => {\n    setIsDefaultValue(false);\n    // Use regex instead of 'trimStart' for IE11 compatibility\n    const initials = e.target.value.replace(/^\\s+/g, '');\n    setSignature();\n    setInitials(initials);\n  };\n\n  const handleColorInputChange = (property, value) => {\n    setFontColor(value);\n    // hack for tool styles for signature not being on state\n    // Note from ADBG : But why tho?\n    forceUpdate();\n  };\n\n  const handleDropdownSelectionChange = (font) => {\n    setSelectedFontFamily(font);\n    const newFontSize = scaleFontSize(fullSignature, font);\n    setFontSize(newFontSize);\n  };\n\n  // These elements are hidden from the user but are used as references to measure\n  // how the text fits, and then resize the canvas accordingly\n  const renderHiddenSignatureElements = () => {\n    return (\n      <div\n        className={classNames({\n          'text-signature-text': true,\n        })}\n        style={{ fontFamily: selectedFontFamily, fontSize: FONT_SIZE, color: fontColor.toHexString() }}\n      >\n        <div\n          className=\"text-container\"\n          ref={hiddenFullSignatureRef}\n        >\n          {fullSignature}\n        </div>\n        <div\n          className=\"text-container\"\n          ref={hiddenInitialsRef}\n        >\n          {initials}\n        </div>\n      </div>\n    );\n  };\n\n  // Renders the font options if initials and text signature are occupied\n  const renderFontOptions = () => {\n    const isInitialsModeEnabledAndEmpty = isInitialsModeEnabled && initials === '';\n    if (fullSignature === '' && (isInitialsModeEnabledAndEmpty || !isInitialsModeEnabled)) {\n      return (\n        <Dropdown\n          id='text-signature-font-dropdown'\n          disabled={true}\n          placeholder={t('option.signatureModal.fontStyle')}\n        />\n      );\n    }\n    return (\n      <Dropdown\n        id=\"text-signature-font-dropdown\"\n        items={fonts.map((font) => ({ font, value: `${fullSignature} ${isInitialsModeEnabled ? initials : ''}` }))}\n        getCustomItemStyle={(item) => ({ fontFamily: item.font })}\n        getKey={(item) => item.font}\n        translationPrefix='option.signatureModal.textSignature'\n        showLabelInList\n        getDisplayValue={(item) => {\n          return item.value || item.font;\n        }}\n        onClickItem={handleDropdownSelectionChange}\n        currentSelectionKey={selectedFontFamily || fonts[0]}\n        maxHeight={isMobile() ? 80 : null}\n        dataElement=\"text-signature-font-dropdown\"\n      />\n    );\n  };\n\n  const isDisabled = !(isModalOpen && isTabPanelSelected);\n  const initialsInputStyle = isInitialsModeEnabled ? {} : { display: 'none' };\n\n  return (\n    <div className=\"text-signature\">\n      <div className=\"signature-and-initials-container\">\n        <div className=\"signature-input full-signature\">\n          <label>\n            <input\n              className=\"text-signature-input\"\n              ref={inputRef}\n              aria-label={t('option.signatureModal.typeSignature')}\n              type=\"text\"\n              value={fullSignature}\n              onChange={handleFullSignatureChange}\n              style={{ fontFamily: selectedFontFamily || fonts, fontSize, color: fontColor.toHexString() }}\n              disabled={isDisabled}\n            />\n          </label>\n          <div className=\"signature-input-footer\">\n            <div className='signature-prompt'>\n              {t('option.signatureModal.typeSignature')}\n            </div>\n            <button\n              className=\"footer-signature-clear\"\n              aria-label={t('action.clearSignature')}\n              onClick={() => {\n                setFullSiganture('');\n                inputRef.current.focus();\n              }}\n              disabled={isDisabled || fullSignature.length === 0}\n            >\n              {t('action.clear')}\n            </button>\n          </div>\n        </div>\n        <div className=\"signature-input initials\" style={initialsInputStyle}>\n          <label>\n            <input\n              className=\"text-signature-input\"\n              type=\"text\"\n              value={initials}\n              aria-label={t('option.signatureModal.typeInitial')}\n              onChange={handleInitialsChange}\n              style={{ fontFamily: selectedFontFamily || fonts, fontSize, color: fontColor.toHexString() }}\n              disabled={isDisabled}\n            />\n          </label>\n          <div className=\"signature-input-footer\">\n            <div className='signature-prompt'>\n              {t('option.signatureModal.typeInitial')}\n            </div>\n            <button\n              className=\"footer-signature-clear\"\n              aria-label={t('action.clearInitial')}\n              onClick={() => setInitials('')}\n              disabled={isDisabled || initials.length === 0}\n            >\n              {t('action.clear')}\n            </button>\n          </div>\n        </div>\n      </div>\n      {renderHiddenSignatureElements()}\n      <canvas ref={fullSignatureHiddenCanvasRef} />\n      <canvas ref={initialsHiddenCanvasRef} />\n      <div className=\"colorpalette-clear-container\">\n        <div className=\"signature-style-options\">\n          {renderFontOptions()}\n          <div className=\"placeholder-dropdown\"></div>\n          <div className=\"divider\"></div>\n          <ColorPalette\n            color={fontColor}\n            property=\"fontColor\"\n            onStyleChange={(property, value) => handleColorInputChange(property, value)}\n            /* eslint-disable-next-line custom/no-hex-colors */\n            overridePalette2={[COMMON_COLORS['black'], BASIC_PALETTE[12], BASIC_PALETTE[7]]}\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nTextSignature.propTypes = propTypes;\n\nexport default TextSignature;\n", "import TextSignature from './TextSignature';\n\nexport default TextSignature;", "import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport { isMobile } from 'helpers/device';\nimport classNames from 'classnames';\nimport i18next from 'i18next';\nimport core from 'core';\n\nimport './ImageSignature.scss';\n\nconst propTypes = {\n  isModalOpen: PropTypes.bool,\n  isTabPanelSelected: PropTypes.bool,\n  disableCreateButton: PropTypes.func,\n  enableCreateButton: PropTypes.func,\n  isInitialsModeEnabled: PropTypes.bool,\n};\n\nconst signatureType = {\n  FULL_SIGNATURE: 'fullSignature',\n  INITIALS: 'initials',\n};\nconst acceptedFileTypes = ['png', 'jpg', 'jpeg'];\nlet acceptedFileSize = null;\n\nfunction readImageFile(file) {\n  return new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n\n    fileReader.onload = (event) => {\n      const imageSource = event.target.result;\n      const isValidType = acceptedFileTypes.some(\n        (type) => imageSource.indexOf(`image/${type}`) !== -1,\n      );\n\n      if (isValidType) {\n        resolve({\n          imageSource,\n          fileSize: file.size,\n        });\n      } else {\n        reject(i18next.t('message.imageSignatureAcceptedFileTypes', {\n          acceptedFileTypes: acceptedFileTypes.join(', '),\n        }));\n      }\n    };\n\n    fileReader.readAsDataURL(file);\n  });\n}\n\n\nconst ImageSignature = ({\n  isModalOpen,\n  isTabPanelSelected,\n  disableCreateButton,\n  enableCreateButton,\n  isInitialsModeEnabled = false,\n}) => {\n  const [fullSignatureImage, setFullSignatureImage] = useState(null);\n  const [fullSignatureFileSize, setFullSignatureFileSize] = useState(null);\n  const [initialsImage, setInitialsImage] = useState(null);\n  const [initialsFileSize, setInitialsFileSize] = useState(null);\n  const [draggingSignatureType, setDraggingSignatureType] = useState('');\n  const [fullSignatureErrorMessage, setFullSignatureErrorMessage] = useState(null);\n  const [initialsErrorMessage, setInitialsErrorMessage] = useState(null);\n  const fullSignatureInputRef = useRef();\n  const initialsInputRef = useRef();\n  const [t] = useTranslation();\n\n  useEffect(() => {\n    const signatureToolArray = core.getToolsFromAllDocumentViewers('AnnotationCreateSignature');\n    acceptedFileSize = signatureToolArray[0]['ACCEPTED_FILE_SIZE'];\n    if (!isModalOpen) {\n      setFullSignatureImage(null);\n      setInitialsImage(null);\n      setInitialsFileSize(null);\n      setInitialsFileSize(null);\n    } else if (isModalOpen && isTabPanelSelected) {\n      signatureToolArray.forEach((signatureTool) => signatureTool.setSignature(fullSignatureImage, fullSignatureFileSize));\n      signatureToolArray.forEach((signatureTool) => signatureTool.setInitials(initialsImage, initialsFileSize));\n      (fullSignatureImage && (!isInitialsModeEnabled || initialsImage)) ? enableCreateButton() : disableCreateButton();\n    }\n  }, [fullSignatureImage, initialsImage, isTabPanelSelected, isModalOpen, initialsFileSize, fullSignatureFileSize, isInitialsModeEnabled]);\n\n  const handleFullSignatureFileChange = (event) => {\n    readFullSignatureFile(event.target.files[0]);\n  };\n\n  const handleInitialsFileChange = (event) => {\n    readInitialsFile(event.target.files[0]);\n  };\n\n  const handleDragEnter = useCallback((event, signatureType) => {\n    event.preventDefault();\n    setDraggingSignatureType(signatureType);\n  }, []);\n\n  const handleDragOver = useCallback((event) => {\n    event.preventDefault();\n  }, []);\n\n  const handleDragLeave = useCallback((event) => {\n    event.preventDefault();\n\n    if (!event.target.parentNode.contains(event.relatedTarget)) {\n      setDraggingSignatureType(null);\n    }\n  }, []);\n\n  const handleDragExit = useCallback((event) => {\n    event.preventDefault();\n    setDraggingSignatureType(null);\n  }, []);\n\n  const handleFullSignatureFileDrop = (event) => {\n    event.preventDefault();\n    setDraggingSignatureType(null);\n    const { files } = event.dataTransfer;\n\n    if (files.length) {\n      readFullSignatureFile(files[0]);\n    }\n  };\n\n  const readFullSignatureFile = async (file) => {\n    try {\n      const result = await readImageFile(file);\n      const { imageSource, fileSize } = result;\n      setFullSignatureErrorMessage('');\n      setFullSignatureImage(imageSource);\n      setFullSignatureFileSize(fileSize);\n    } catch (errorMessage) {\n      setFullSignatureErrorMessage(errorMessage);\n    }\n  };\n\n  const handleInitialsFileDrop = (event) => {\n    event.preventDefault();\n    setDraggingSignatureType(null);\n    const { files } = event.dataTransfer;\n\n    if (files.length) {\n      readInitialsFile(files[0]);\n    }\n  };\n\n  const readInitialsFile = async (file) => {\n    try {\n      const result = await readImageFile(file);\n      const { imageSource, fileSize } = result;\n      setInitialsErrorMessage('');\n      setInitialsImage(imageSource);\n      setInitialsFileSize(fileSize);\n    } catch (errorMessage) {\n      setInitialsErrorMessage(errorMessage);\n    }\n  };\n\n  const handleFullSignatureDragEnter = useCallback((event) => {\n    handleDragEnter(event, signatureType.FULL_SIGNATURE);\n  }, [handleDragEnter]);\n\n  const handleFullSignatureDragLeave = useCallback((event) => {\n    handleDragLeave(event, signatureType.FULL_SIGNATURE);\n  }, [handleDragLeave]);\n\n\n  const handleInitialsDragEnter = useCallback((event) => {\n    handleDragEnter(event, signatureType.INITIALS);\n  }, [handleDragEnter]);\n\n  const handleInitialsDragLeave = useCallback((event) => {\n    handleDragLeave(event, signatureType.INITIALS);\n  }, [handleDragLeave]);\n\n  const renderPrompt = () => {\n    if (isMobile()) {\n      return (\n        <div className=\"image-signature-separator\">\n          {t('option.signatureModal.selectImage')}\n        </div>\n      );\n    }\n    return (\n      <>\n        <div className=\"image-signature-dnd\">\n          {t('option.signatureModal.dragAndDrop')}\n        </div>\n        <div className=\"image-signature-separator\">\n          {t('option.signatureModal.or')}\n        </div>\n      </>\n    );\n  };\n\n  const renderFullSignatureImage = () => (<img src={fullSignatureImage} alt={t('option.signatureModal.imageSignature')} style={{ maxWidth: '100%', maxHeight: '100%' }} />);\n\n  const renderFullSignaturePicker = () => (\n    <div\n      className={fullSignatureContainerClass}\n      onDragEnter={handleFullSignatureDragEnter}\n      onDragLeave={handleFullSignatureDragLeave}\n      onDragOver={handleDragOver}\n      onDrop={handleFullSignatureFileDrop}\n      onDragExit={handleDragExit}\n    >\n      {renderPrompt()}\n      <div className=\"image-signature-upload\">\n        <input\n          ref={fullSignatureInputRef}\n          id=\"upload\"\n          type=\"file\"\n          accept={acceptedFileTypes.map((type) => `.${type}`).join(',')}\n          onChange={handleFullSignatureFileChange}\n          disabled={!(isModalOpen && isTabPanelSelected)}\n        />\n        <button\n          onClick={() => fullSignatureInputRef.current.click()}\n          className=\"pick-image-button\"\n        >\n          {t('option.signatureModal.pickImage')}\n        </button>\n      </div>\n      {fullSignatureErrorMessage && (\n        <div className=\"image-signature-error\">{fullSignatureErrorMessage}</div>\n      )}\n    </div>\n  );\n\n  const renderInitialsImage = () => (<img src={initialsImage} alt={t('option.signatureModal.imageInitial')} style={{ maxWidth: '100%', maxHeight: '100%' }} />);\n\n  const renderInitialsPicker = () => (\n    <div\n      className={initialsContainerClass}\n      onDragEnter={handleInitialsDragEnter}\n      onDragLeave={handleInitialsDragLeave}\n      onDragOver={handleDragOver}\n      onDrop={handleInitialsFileDrop}\n      onDragExit={handleDragExit}\n    >\n      {renderPrompt()}\n      <div className=\"image-signature-upload\">\n        <input\n          ref={initialsInputRef}\n          id=\"upload\"\n          type=\"file\"\n          accept={acceptedFileTypes.map((type) => `.${type}`).join(',')}\n          onChange={handleInitialsFileChange}\n          disabled={!(isModalOpen && isTabPanelSelected)}\n        />\n        <button\n          onClick={() => initialsInputRef.current.click()}\n          className=\"pick-image-button\"\n        >\n          {t('option.signatureModal.pickInitialsFile')}\n        </button>\n      </div>\n      {initialsErrorMessage && (\n        <div className=\"image-signature-error\">{initialsErrorMessage}</div>\n      )}\n    </div>\n  );\n\n  const hasLimit = typeof acceptedFileSize === 'number' && acceptedFileSize > 0;\n  const fullSignatureFileSizeCheck = !hasLimit || fullSignatureFileSize < acceptedFileSize;\n  const initialsFileSizeCheck = !hasLimit || initialsFileSize < acceptedFileSize;\n  const fullSignatureContainerClass = classNames('image-signature-upload-container', { mobile: isMobile(), dragging: draggingSignatureType === signatureType.FULL_SIGNATURE });\n  const initialsContainerClass = classNames('image-signature-upload-container', { mobile: isMobile(), dragging: draggingSignatureType === signatureType.INITIALS });\n  const initialsInputStyle = isInitialsModeEnabled ? {} : { display: 'none' };\n  return (\n    <div className=\"image-signature\">\n      <div className=\"signature-and-initials-container\">\n        <div className=\"signature-input image full-signature\">\n          {fullSignatureImage && fullSignatureFileSizeCheck ?\n            renderFullSignatureImage() :\n            renderFullSignaturePicker()\n          }\n        </div>\n        <div className=\"signature-input image initials\" style={initialsInputStyle}>\n          {initialsImage && initialsFileSizeCheck ?\n            renderInitialsImage() :\n            renderInitialsPicker()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nImageSignature.propTypes = propTypes;\n\nexport default ImageSignature;\n", "import ImageSignature from './ImageSignature';\n\nexport default ImageSignature;", "import React from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport './SavedSignatures.scss';\nimport SignatureModes from 'constants/signatureModes';\nimport defaultTool from 'constants/defaultTool';\nimport Icon from 'components/Icon';\nimport { useTranslation } from 'react-i18next';\nimport core from 'core';\nimport classNames from 'classnames';\n\n\nconst SavedSignatures = ({ selectedIndex, setSelectedIndex }) => {\n  const dispatch = useDispatch();\n  const { t } = useTranslation();\n  const [\n    displayedSignatures,\n    selectedDisplayedSignatureIndex,\n    isSignatureDeleteButtonDisabled,\n    savedInitials,\n    selectedDisplayedInitialsIndex,\n    signatureMode,\n    initialsOffset,\n  ] = useSelector(\n    (state) => [\n      selectors.getDisplayedSignatures(state),\n      selectors.getSelectedDisplayedSignatureIndex(state),\n      selectors.isElementDisabled(state, 'defaultSignatureDeleteButton'),\n      selectors.getSavedInitials(state),\n      selectors.getSelectedDisplayedInitialsIndex(state),\n      selectors.getSignatureMode(state),\n      selectors.getInitialsOffset(state),\n    ]\n  );\n\n  const signatureToolArray = core.getToolsFromAllDocumentViewers('AnnotationCreateSignature');\n\n  const deleteSignatureAndInitials = async (index) => {\n    const isFullSignature = signatureMode === SignatureModes.FULL_SIGNATURE;\n    const initialsIndex = index - initialsOffset;\n    const skipInitial = initialsIndex < 0;\n    if (!skipInitial) {\n      signatureToolArray[0].deleteSavedInitials(initialsIndex);\n      const isDeletingSelectedInitials = selectedDisplayedInitialsIndex === initialsIndex && !skipInitial;\n      if (isDeletingSelectedInitials) {\n        dispatch(actions.setSelectedDisplayedInitialsIndex(0));\n        for (const signatureTool of signatureToolArray) {\n          signatureTool.hidePreview();\n        }\n        core.setToolMode(defaultTool);\n      }\n    } else {\n      dispatch(actions.setInitialsOffset(initialsOffset - 1));\n    }\n    signatureToolArray[0].deleteSavedSignature(index);\n    const isDeletingSelectedSignature = selectedDisplayedSignatureIndex === index;\n    if (isDeletingSelectedSignature) {\n      dispatch(actions.setSelectedDisplayedSignatureIndex(0));\n      for (const signatureTool of signatureToolArray) {\n        signatureTool.hidePreview();\n      }\n      core.setToolMode(defaultTool);\n    } else if (isFullSignature ? index < selectedDisplayedSignatureIndex : initialsIndex < selectedDisplayedInitialsIndex) {\n      dispatch(actions.setSelectedDisplayedSignatureIndex(selectedDisplayedSignatureIndex - 1));\n    }\n  };\n\n  const displayedIntials = new Array(initialsOffset).concat(savedInitials);\n\n  return (\n    <div className={classNames('SavedSignatures', { empty: !displayedSignatures || displayedSignatures.length < 1 })}>\n      {displayedSignatures.length ?\n        displayedSignatures.map((signature, index) => <div\n          key={index}\n          className={classNames('signature-row', { active: selectedIndex === index, 'no-initials': !displayedIntials?.[index] })}\n          onClick={() => setSelectedIndex(index)}\n        >\n          <div className=\"inputContainer\">\n            <input type='radio' onChange={() => setSelectedIndex(index)} checked={index === selectedIndex} />\n            <div className=\"contentContainer\">\n              <div className=\"imgContainer\">\n                <img alt={t('option.toolsOverlay.signatureAltText')} src={displayedSignatures[index].imgSrc} />\n              </div>\n              {displayedIntials?.[index] && <div className=\"imgContainer\">\n                <img alt={t('option.toolsOverlay.signatureAltText')} src={displayedIntials[index].imgSrc} />\n              </div>}\n              {!isSignatureDeleteButtonDisabled && (\n                <button\n                  className=\"icon-button\"\n                  data-element=\"defaultSignatureDeleteButton\"\n                  onClick={() => deleteSignatureAndInitials(index)}\n                >\n                  <Icon glyph=\"icon-delete-line\" />\n                </button>\n              )}\n            </div>\n          </div>\n          <div className=\"labelContainer\">\n            <div className=\"signatureLabel\">{t('option.type.signature')}</div>\n            {displayedIntials?.[index] && <div className=\"intialsLabel\">{t('option.type.initials')}</div>}\n          </div>\n        </div>\n        )\n        :\n        <div className='emptyListContainer'>\n          {t('option.signatureModal.noSignatures')}\n        </div>\n      }\n    </div>\n  );\n};\n\nexport default SavedSignatures;\n", "import SavedSignatures from './SavedSignatures';\n\nexport default SavedSignatures;", "import React, { useCallback, useEffect, useState } from 'react';\nimport classNames from 'classnames';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport { Tabs, Tab, TabPanel } from 'components/Tabs';\nimport InkSignature from 'components/SignatureModal/InkSignature';\nimport TextSignature from 'components/SignatureModal/TextSignature';\nimport ImageSignature from 'components/SignatureModal/ImageSignature';\nimport SavedSignatures from 'components/SignatureModal/SavedSignatures';\n\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport SignatureModes from 'constants/signatureModes';\nimport DataElements from 'constants/dataElement';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport ModalWrapper from 'components/ModalWrapper';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nimport './SignatureModal.scss';\n\nconst SignatureModal = () => {\n  const [\n    isDisabled,\n    isOpen,\n    activeToolName,\n    signatureMode,\n    activeDocumentViewerKey,\n    isInitialsModeEnabled,\n    isSavedTabDisabled,\n    selectedTab,\n    displayedSignatures,\n    savedInitials,\n  ] = useSelector((state) => [\n    selectors.isElementDisabled(state, DataElements.SIGNATURE_MODAL),\n    selectors.isElementOpen(state, DataElements.SIGNATURE_MODAL),\n    selectors.getActiveToolName(state),\n    selectors.getSignatureMode(state),\n    selectors.getActiveDocumentViewerKey(state),\n    selectors.getIsInitialsModeEnabled(state),\n    selectors.isElementDisabled(state, DataElements.SAVED_SIGNATURES_TAB),\n    selectors.getSelectedTab(state, DataElements.SIGNATURE_MODAL),\n    selectors.getDisplayedSignatures(state),\n    selectors.getSavedInitials(state),\n  ]);\n\n  const signatureToolArray = core.getToolsFromAllDocumentViewers('AnnotationCreateSignature');\n  const [createButtonDisabled, setCreateButtonDisabled] = useState(true);\n  const [selectedIndex, setSelectedIndex] = useState(0);\n\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  // Hack to close modal if hotkey to open other tool is used.\n  useDidUpdate(() => {\n    if (activeToolName !== 'AnnotationCreateSignature') {\n      dispatch(\n        actions.closeElements([\n          DataElements.SIGNATURE_MODAL,\n          DataElements.SIGNATURE_OVERLAY\n        ]),\n      );\n    }\n  }, [dispatch, activeToolName]);\n\n  useEffect(() => {\n    if (isOpen) {\n      dispatch(\n        actions.closeElements([\n          DataElements.PRINT_MODAL,\n          DataElements.LOADING_MODAL,\n          DataElements.PROGRESS_MODAL,\n          DataElements.ERROR_MODAL,\n        ]),\n      );\n    }\n  }, [dispatch, isOpen]);\n\n  const closeModal = () => {\n    for (const signatureTool of signatureToolArray) {\n      signatureTool.clearLocation();\n      signatureTool.setSignature(null);\n      signatureTool.setInitials(null);\n    }\n    dispatch(actions.closeElement(DataElements.SIGNATURE_MODAL));\n  };\n\n  const createSignatures = async () => {\n    createFullSignature();\n\n    if (isInitialsModeEnabled) {\n      createInitials();\n    }\n  };\n\n  const createFullSignature = async () => {\n    signatureToolArray[0].saveSignatures(signatureToolArray[0].getFullSignatureAnnotation());\n    for (let i = 1; i < signatureToolArray.length; i++) {\n      await signatureToolArray[i].setSignature(signatureToolArray[0].getFullSignatureAnnotation());\n    }\n\n    const signatureTool = signatureToolArray[activeDocumentViewerKey - 1];\n\n    if (!(await signatureTool.isEmptySignature())) {\n      core.setToolMode('AnnotationCreateSignature');\n\n      if (signatureMode === SignatureModes.FULL_SIGNATURE) {\n        if (signatureTool.hasLocation()) {\n          await signatureTool.addSignature();\n        } else {\n          for (const signatureTool of signatureToolArray) {\n            await signatureTool.showPreview();\n          }\n        }\n\n        dispatch(actions.closeElement(DataElements.SIGNATURE_MODAL));\n      }\n    }\n  };\n\n  const createInitials = async () => {\n    signatureToolArray[0].saveInitials(signatureToolArray[0].getInitialsAnnotation());\n    for (let i = 1; i < signatureToolArray.length; i++) {\n      await signatureToolArray[i].saveInitials(signatureToolArray[0].getInitialsAnnotation());\n    }\n\n    const signatureTool = signatureToolArray[activeDocumentViewerKey - 1];\n    if (!(await signatureTool.isEmptyInitialsSignature())) {\n      core.setToolMode('AnnotationCreateSignature');\n\n      if (signatureMode === SignatureModes.INITIALS) {\n        if (signatureTool.hasLocation()) {\n          await signatureTool.addInitials();\n        } else {\n          for (const signatureTool of signatureToolArray) {\n            await signatureTool.showInitialsPreview();\n          }\n        }\n      }\n\n      dispatch(actions.closeElement(DataElements.SIGNATURE_MODAL));\n      // back to the default mode\n      dispatch(actions.setSignatureMode(SignatureModes.FULL_SIGNATURE));\n    }\n  };\n\n  const setSignature = async (index) => {\n    const isFullSignature = signatureMode === SignatureModes.FULL_SIGNATURE;\n    dispatch(actions[isFullSignature ? 'setSelectedDisplayedSignatureIndex' : 'setSelectedDisplayedInitialsIndex'](index));\n    const { annotation } = isFullSignature ? displayedSignatures[index] : savedInitials[index];\n    core.setToolMode('AnnotationCreateSignature');\n    for (const signatureTool of signatureToolArray) {\n      await signatureTool[isFullSignature ? 'setSignature' : 'setInitials'](annotation);\n      if (signatureTool.hasLocation()) {\n        await signatureTool[isFullSignature ? 'addSignature' : 'addInitials']();\n      } else {\n        await signatureTool[isFullSignature ? 'showPreview' : 'showInitialsPreview']();\n      }\n    }\n    dispatch(actions.closeElement(DataElements.SIGNATURE_MODAL));\n  };\n\n  const disableCreateButton = useCallback(() => {\n    setCreateButtonDisabled(true);\n  }, [createButtonDisabled]);\n\n  const enableCreateButton = useCallback(() => {\n    setCreateButtonDisabled(false);\n  }, [createButtonDisabled]);\n\n  const modalClass = classNames({\n    Modal: true,\n    SignatureModal: true,\n    open: isOpen,\n    closed: !isOpen,\n  });\n  const isSavedTabSelected = selectedTab === 'savedSignaturePanelButton';\n\n  if (isDisabled) {\n    return null;\n  }\n\n  return (\n    <div\n      className={modalClass}\n      data-element={DataElements.SIGNATURE_MODAL}\n    >\n      <ModalWrapper\n        title={t('option.signatureModal.modalName')}\n        closeHandler={closeModal}\n        onCloseClick={closeModal}\n        isOpen={isOpen}\n        swipeToClose\n      >\n        <div\n          className={classNames('container', { 'include-initials': isInitialsModeEnabled })}\n          onMouseDown={(e) => e.stopPropagation()}\n        >\n          <div className=\"swipe-indicator\" />\n          <Tabs id=\"signatureModal\">\n            <div className=\"tabs-header-container\">\n              <div className=\"tab-list\">\n                {!isSavedTabDisabled &&\n                  <>\n                    <Tab dataElement=\"savedSignaturePanelButton\">\n                      <button className=\"tab-options-button\">\n                        {t('option.type.saved')}\n                      </button>\n                    </Tab>\n                    <div className=\"tab-options-divider\" />\n                  </>\n                }\n                <Tab dataElement=\"inkSignaturePanelButton\">\n                  <button className=\"tab-options-button\">\n                    {t('action.draw')}\n                  </button>\n                </Tab>\n                <div className=\"tab-options-divider\" />\n                <Tab dataElement=\"textSignaturePanelButton\">\n                  <button className=\"tab-options-button\">\n                    {t('action.type')}\n                  </button>\n                </Tab>\n                <div className=\"tab-options-divider\" />\n                <Tab dataElement=\"imageSignaturePanelButton\">\n                  <button className=\"tab-options-button\">\n                    {t('action.upload')}\n                  </button>\n                </Tab>\n              </div>\n            </div>\n            {!isSavedTabDisabled && <TabPanel dataElement=\"savedSignaturePanel\">\n              <SavedSignatures {...{ selectedIndex, setSelectedIndex }} />\n            </TabPanel>}\n            <TabPanel dataElement=\"inkSignaturePanel\">\n              <InkSignature\n                isModalOpen={isOpen}\n                enableCreateButton={enableCreateButton}\n                disableCreateButton={disableCreateButton}\n                isInitialsModeEnabled={isInitialsModeEnabled}\n              />\n            </TabPanel>\n            <TabPanel dataElement=\"textSignaturePanel\">\n              <TextSignature\n                isModalOpen={isOpen}\n                enableCreateButton={enableCreateButton}\n                disableCreateButton={disableCreateButton}\n                isInitialsModeEnabled={isInitialsModeEnabled}\n              />\n            </TabPanel>\n            <TabPanel dataElement=\"imageSignaturePanel\">\n              <ImageSignature\n                isModalOpen={isOpen}\n                enableCreateButton={enableCreateButton}\n                disableCreateButton={disableCreateButton}\n                isInitialsModeEnabled={isInitialsModeEnabled}\n              />\n            </TabPanel>\n            <div className=\"footer\">\n              <button className=\"signature-create\" onClick={useFocusOnClose(isSavedTabSelected ? () => setSignature(selectedIndex) : createSignatures)}\n                disabled={isSavedTabSelected ? (!isSavedTabSelected || !displayedSignatures.length || !isOpen) : (!(isOpen) || createButtonDisabled)}\n                title={isInitialsModeEnabled ? t('message.signatureRequired') : ''}>\n                {t(isSavedTabSelected ? 'action.apply' : 'action.create')}\n              </button>\n            </div>\n          </Tabs>\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default SignatureModal;\n", "import SignatureModal from './SignatureModal';\n\nexport default SignatureModal;"], "sourceRoot": ""}