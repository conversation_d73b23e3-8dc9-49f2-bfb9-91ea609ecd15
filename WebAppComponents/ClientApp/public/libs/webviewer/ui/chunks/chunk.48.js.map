{"version": 3, "sources": ["webpack:///./src/ui/src/components/Spinner/Spinner.js", "webpack:///./src/ui/src/components/Spinner/index.js", "webpack:///./src/ui/src/components/Spinner/Spinner.scss?ed1d", "webpack:///./src/ui/src/components/Spinner/Spinner.scss", "webpack:///./src/ui/src/components/FileAttachmentPanel/FileAttachmentPanel.scss?eb1a", "webpack:///./src/ui/src/components/FileAttachmentPanel/FileAttachmentPanel.scss", "webpack:///./src/ui/src/components/FileAttachmentPanel/FileAttachmentPanel.js", "webpack:///./src/ui/src/components/FileAttachmentPanel/index.js"], "names": ["Spinner", "height", "width", "spinnerStyle", "className", "style", "api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "getActualFileName", "filename", "match", "renderAttachment", "onClickCallback", "key", "showFileIdProcessSpinner", "fileExtension", "split", "pop", "toUpperCase", "handleKeyDown", "event", "onClick", "onKeyDown", "cursor", "background", "border", "padding", "type", "initialFilesDefault", "embeddedFiles", "fileAttachmentAnnotations", "FileAttachmentPanel", "initialFiles", "t", "useTranslation", "dispatch", "useDispatch", "useState", "fileAttachments", "setFileAttachments", "isMultiTab", "useSelector", "selectors", "getIsMultiTab", "tabManager", "getTabManager", "setFileIdProcessSpinner", "useEffect", "updateFileAttachments", "getFileAttachments", "attachments", "core", "addEventListener", "removeEventListener", "Object", "entries", "Icon", "glyph", "panelData", "panelNames", "FILE_ATTACHMENT", "icon", "attachmentPanelItemOnClick", "fileAttachmentAnnot", "actions", "openElement", "DataElements", "LOADING_MODAL", "setTimeout", "getFileData", "blob", "addTab", "newTabId", "closeElement", "LEFT_PANEL", "setActiveTab", "getAnnotationManager", "trigger", "map", "file", "idx", "getEmbeddedFileData", "fileObject", "then", "saveAs", "pageNumber", "fileAttachmentAnnotsPerPage", "setCurrentPage", "selectAnnotation"], "mappings": "gHAceA,G,QAXC,SAAH,GAA4C,QAAtCC,cAAM,IAAG,SAAM,MAAEC,MAC5BC,EAAe,CACnBF,SACAC,WAHqC,IAAG,SAAM,GAMhD,OACE,yBAAKE,UAAU,UAAUC,MAAOF,MCRrBH,O,qBCFf,IAAIM,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,goCAAioC,KAG1pC0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,u+GAAw+G,KAGjgH0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,2tBCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAcA,IAAM4B,EAAoB,SAACC,GAEzB,OAAOA,EAASC,MADM,cACe,IAGjCC,EAAmB,SAACF,EAAUG,EAAiBC,EAAKC,GAExD,IAAMC,GADNN,EAAWD,EAAkBC,IACEO,MAAM,KAAKC,MAAMC,cAE1CC,EAAgB,SAACC,GACH,UAAdA,EAAMP,KAAiC,MAAdO,EAAMP,KACjCD,KAIJ,OAAIE,IAA6BD,EAE7B,wBAAIA,IAAKA,GACP,4BACExC,UAAU,eACVgD,QAAST,EACTU,UAAWH,EACX7C,MAAO,CAAEiD,OAAQ,UAAWC,WAAY,OAAQC,OAAQ,OAAQC,QAAS,GACzEC,KAAK,UAAQ,WAERZ,EAAa,aAAKN,GAAW,kBAACxC,EAAA,EAAO,CAACC,OAAQ,GAAIC,MAAO,OAMpE,wBAAI0C,IAAKA,GACP,4BACExC,UAAU,eACVgD,QAAST,EACTU,UAAWH,EACX7C,MAAO,CAAEiD,OAAQ,UAAWC,WAAY,OAAQC,OAAQ,OAAQC,QAAS,GACzEC,KAAK,UAAQ,WAERZ,EAAa,aAAKN,MAQzBmB,EAAsB,CAAEC,cAAe,GAAIC,0BAA2B,IAqG7DC,EAnGa,SAAH,GAA+C,QAAzCC,oBAAY,IAAG,EAAAJ,EAAmB,EACxDK,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cACmD,IAAtBC,mBAASL,GAAa,GAA7DM,EAAe,KAAEC,EAAkB,KACpCC,EAAaC,YAAYC,IAAUC,eACnCC,EAAaH,YAAYC,IAAUG,eACiC,IAAdR,mBAAS,MAAK,GAAnEvB,EAAwB,KAAEgC,EAAuB,KAgBxD,GAdAC,qBAAU,WACR,IAAMC,EAAqB,6BAAG,oGACFC,cAAoB,OAAxCC,EAAc,EAAH,KACjBX,EAAmBW,GAAa,2CACjC,kBAH0B,mCAO3B,OAHAC,IAAKC,iBAAiB,oBAAqBJ,GAC3CG,IAAKC,iBAAiB,iBAAkBJ,GACxCA,IACO,WACLG,IAAKE,oBAAoB,oBAAqBL,GAC9CG,IAAKE,oBAAoB,iBAAkBL,MAE5C,IAGwC,IAAzCV,EAAgBT,cAAcvC,QACuC,IAArEgE,OAAOC,QAAQjB,EAAgBR,2BAA2BxC,OAE1D,OACE,yBAAKjB,UAAU,uBACb,yBAAKA,UAAU,yBACb,kBAACmF,EAAA,EAAI,CAACnF,UAAU,aAAaoF,MAAOC,IAAUC,IAAWC,iBAAiBC,OAC1E,yBAAKxF,UAAU,iBAAiB4D,EAAE,4BAM1C,IAAM6B,EAA0B,6BAAG,WAAOC,GAAmB,qEACvDvB,EAAY,CAAF,eACZL,EAAS6B,IAAQC,YAAYC,IAAaC,gBAC1CC,WAAU,YAAC,wGACUL,EAAoBM,cAAa,OACY,OAD1DC,EAAO,EAAH,KACJ7D,EAAWD,EAAkBuD,EAAoBtD,UAAS,SACzCmC,EAAW2B,OAAOD,EAAM,CAAE7D,aAAW,OAEJ,OAFlD+D,EAAW,EAAH,KACdrC,EAAS6B,IAAQS,aAAaP,IAAaC,gBAC3ChC,EAAS6B,IAAQS,aAAaP,IAAaQ,aAAa,UAClD9B,EAAW+B,aAAaH,GAAS,4CACtC,KAAK,+CAEDrB,IAAKyB,uBAAuBC,QAAQ,0BAA2Bd,IAAoB,2CAE7F,gBAd+B,sCAgBhC,OACE,yBAAK1F,UAAU,uBACb,yBAAKA,UAAU,WACZiE,EAAgBT,cAAcvC,OAAS,wBAAIjB,UAAU,SAAS4D,EAAE,0BAAiC,KAClG,wBAAI5D,UAAU,gBACXiE,EAAgBT,cAAciD,KAAI,SAACC,EAAMC,GAAG,OAAKrE,EAChDH,EAAkBuE,EAAKtE,WACvB,WACEqC,EAAwB,gBAAD,OAAiBkC,IACxCC,YAAoBF,EAAKG,YAAYC,MAAK,SAACb,GACzCc,iBAAOd,EAAM9D,EAAkBuE,EAAKtE,cACpC,SAAS,WACTqC,EAAwB,WAE3B,uBACekC,GAChBlE,QAMLwC,OAAOC,QAAQjB,EAAgBR,2BAA2BgD,KAAI,YAA+C,aAA7CO,EAAU,KAAEC,EAA2B,KACtG,OACE,yBAAKzE,IAAKwE,EAAYhH,UAAU,WAC9B,wBAAIA,UAAU,SACX4D,EAAE,mBAAmB,IAAEoD,GAE1B,wBAAIhH,UAAU,gBACXiH,EAA4BR,KAAI,SAACf,EAAqBiB,GAAG,OAAKrE,EAC7DH,EAAkBuD,EAAoBtD,UAAS,YAC/C,8EAE6C,OAD3C0C,IAAKoC,eAAexB,EAAgC,YACpDZ,IAAKqC,iBAAiBzB,GAAqB,SACrCD,EAA2BC,GAAoB,2CACtD,mCAC2BiB,cCtJ7BjD", "file": "chunks/chunk.48.js", "sourcesContent": ["import React from 'react';\nimport './Spinner.scss';\n\nconst Spinner = ({ height = '50px', width = '54px' }) => {\n  const spinnerStyle = {\n    height,\n    width,\n  };\n\n  return (\n    <div className=\"spinner\" style={spinnerStyle}></div>\n  );\n};\n\nexport default Spinner;", "import Spinner from './Spinner';\n\nexport default Spinner;\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Spinner.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.spinner{border-top:5px solid var(--border);border:5px solid var(--border);border-top-color:var(--focus-border);border-radius:50%;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./FileAttachmentPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.fileAttachmentPanel{z-index:65;display:flex;flex-direction:column;transition:transform .3s ease,visibility 0s ease .3s}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileAttachmentPanel{top:0;width:100%;height:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileAttachmentPanel{top:0;width:100%;height:100%}}.open.fileAttachmentPanel{transform:none;visibility:visible;transition:transform .3s ease,visibility 0s ease 0s}.fileAttachmentPanel .empty-panel-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;width:100%;padding:36px;grid-gap:8px;gap:8px}.fileAttachmentPanel .empty-panel-container .empty-icon{width:60px;height:60px;color:var(--gray-6);fill:var(--gray-6)}.fileAttachmentPanel .empty-panel-container .empty-icon svg{width:60px;height:60px}.fileAttachmentPanel .empty-panel-container .empty-message{text-align:center;max-width:131px;font-size:13px}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileAttachmentPanel .empty-panel-container .empty-message{line-height:15px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileAttachmentPanel .empty-panel-container .empty-message{line-height:15px}}.fileAttachmentPanel{display:block;font-size:var(--font-size-default)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileAttachmentPanel{margin:16px;width:auto;flex-grow:1;overflow-y:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileAttachmentPanel{margin:16px;width:auto;flex-grow:1;overflow-y:auto}}.fileAttachmentPanel .section{margin-bottom:8px}.fileAttachmentPanel .section h2.title{font-weight:700;font-size:13px;padding-left:var(--fileAttachment-title-padding);margin:12px 0}.fileAttachmentPanel .section ul.downloadable{padding-left:var(--fileAttachment-list-padding)}.fileAttachmentPanel .section ul.downloadable>li{width:100%;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;padding:4px 0}.fileAttachmentPanel .section ul.downloadable>li>button{cursor:pointer;color:var(--secondary-button-text);text-overflow:ellipsis;overflow:hidden;white-space:nowrap;padding:4px 0}.fileAttachmentPanel .section ul.downloadable>li>button:hover{color:var(--secondary-button-hover)}.fileAttachmentPanel .section ul.downloadable .embedSpinner{display:flex;flex-direction:row;justify-content:space-between}.fileAttachmentPanel .section ul.downloadable .embedSpinner .spinner{margin:0}.fileAttachmentPanel .section ul ul{padding-left:10px;list-style-type:none}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { getFileAttachments, getEmbeddedFileData } from 'helpers/getFileAttachments';\r\nimport Spinner from '../Spinner';\r\nimport { saveAs } from 'file-saver';\r\nimport Icon from 'components/Icon';\r\nimport core from 'core';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport selectors from 'selectors';\r\nimport actions from 'actions';\r\nimport DataElements from 'constants/dataElement';\r\n\r\nimport './FileAttachmentPanel.scss';\r\nimport { panelData, panelNames } from 'constants/panel';\r\n\r\nconst getActualFileName = (filename) => {\r\n  const fileNameRegex = /[^\\\\\\/]+$/g;\r\n  return filename.match(fileNameRegex)[0];\r\n};\r\n\r\nconst renderAttachment = (filename, onClickCallback, key, showFileIdProcessSpinner) => {\r\n  filename = getActualFileName(filename);\r\n  const fileExtension = filename.split('.').pop().toUpperCase();\r\n\r\n  const handleKeyDown = (event) => {\r\n    if (event.key === 'Enter' || event.key === ' ') {\r\n      onClickCallback();\r\n    }\r\n  };\r\n\r\n  if (showFileIdProcessSpinner === key) {\r\n    return (\r\n      <li key={key}>\r\n        <button\r\n          className='embedSpinner'\r\n          onClick={onClickCallback}\r\n          onKeyDown={handleKeyDown}\r\n          style={{ cursor: 'pointer', background: 'none', border: 'none', padding: 0 }}\r\n          type=\"button\"\r\n        >\r\n          {`[${fileExtension}] ${filename}`}<Spinner height={15} width={15}/>\r\n        </button>\r\n      </li>\r\n    );\r\n  }\r\n  return (\r\n    <li key={key}>\r\n      <button\r\n        className='embedSpinner'\r\n        onClick={onClickCallback}\r\n        onKeyDown={handleKeyDown}\r\n        style={{ cursor: 'pointer', background: 'none', border: 'none', padding: 0 }}\r\n        type=\"button\"\r\n      >\r\n        {`[${fileExtension}] ${filename}`}\r\n      </button>\r\n    </li>\r\n  );\r\n};\r\n\r\n\r\n\r\nconst initialFilesDefault = { embeddedFiles: [], fileAttachmentAnnotations: [] };\r\n\r\nconst FileAttachmentPanel = ({ initialFiles = initialFilesDefault }) => {\r\n  const [t] = useTranslation();\r\n  const dispatch = useDispatch();\r\n  const [fileAttachments, setFileAttachments] = useState(initialFiles);\r\n  const isMultiTab = useSelector(selectors.getIsMultiTab);\r\n  const tabManager = useSelector(selectors.getTabManager);\r\n  const [showFileIdProcessSpinner, setFileIdProcessSpinner] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const updateFileAttachments = async () => {\r\n      const attachments = await getFileAttachments();\r\n      setFileAttachments(attachments);\r\n    };\r\n    core.addEventListener('annotationChanged', updateFileAttachments);\r\n    core.addEventListener('documentLoaded', updateFileAttachments);\r\n    updateFileAttachments();\r\n    return () => {\r\n      core.removeEventListener('annotationChanged', updateFileAttachments);\r\n      core.removeEventListener('documentLoaded', updateFileAttachments);\r\n    };\r\n  }, []);\r\n\r\n  if (\r\n    fileAttachments.embeddedFiles.length === 0 &&\r\n    Object.entries(fileAttachments.fileAttachmentAnnotations).length === 0\r\n  ) {\r\n    return (\r\n      <div className=\"fileAttachmentPanel\">\r\n        <div className=\"empty-panel-container\">\r\n          <Icon className=\"empty-icon\" glyph={panelData[panelNames.FILE_ATTACHMENT].icon}/>\r\n          <div className=\"empty-message\">{t('message.noAttachments')}</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const attachmentPanelItemOnClick = async (fileAttachmentAnnot) => {\r\n    if (isMultiTab) {\r\n      dispatch(actions.openElement(DataElements.LOADING_MODAL));\r\n      setTimeout(async () => {\r\n        const blob = await fileAttachmentAnnot.getFileData();\r\n        const filename = getActualFileName(fileAttachmentAnnot.filename);\r\n        const newTabId = await tabManager.addTab(blob, { filename });\r\n        dispatch(actions.closeElement(DataElements.LOADING_MODAL));\r\n        dispatch(actions.closeElement(DataElements.LEFT_PANEL));\r\n        await tabManager.setActiveTab(newTabId);\r\n      }, 100);\r\n    } else {\r\n      return core.getAnnotationManager().trigger('annotationDoubleClicked', fileAttachmentAnnot);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fileAttachmentPanel\">\r\n      <div className=\"section\">\r\n        {fileAttachments.embeddedFiles.length ? <h2 className=\"title\">{t('message.embeddedFiles')}</h2> : null}\r\n        <ul className=\"downloadable\">\r\n          {fileAttachments.embeddedFiles.map((file, idx) => renderAttachment(\r\n            getActualFileName(file.filename),\r\n            () => {\r\n              setFileIdProcessSpinner(`embeddedFile_${idx}`);\r\n              getEmbeddedFileData(file.fileObject).then((blob) => {\r\n                saveAs(blob, getActualFileName(file.filename));\r\n              }).finally(() => {\r\n                setFileIdProcessSpinner(null);\r\n              });\r\n            },\r\n            `embeddedFile_${idx}`,\r\n            showFileIdProcessSpinner\r\n          )\r\n          )}\r\n        </ul>\r\n      </div>\r\n\r\n      {Object.entries(fileAttachments.fileAttachmentAnnotations).map(([pageNumber, fileAttachmentAnnotsPerPage]) => {\r\n        return (\r\n          <div key={pageNumber} className=\"section\">\r\n            <h2 className=\"title\">\r\n              {t('message.pageNum')} {pageNumber}\r\n            </h2>\r\n            <ul className=\"downloadable\">\r\n              {fileAttachmentAnnotsPerPage.map((fileAttachmentAnnot, idx) => renderAttachment(\r\n                getActualFileName(fileAttachmentAnnot.filename),\r\n                async () => {\r\n                  core.setCurrentPage(fileAttachmentAnnot['PageNumber']);\r\n                  core.selectAnnotation(fileAttachmentAnnot);\r\n                  await attachmentPanelItemOnClick(fileAttachmentAnnot);\r\n                },\r\n                `fileAttachmentAnnotation_${idx}`,\r\n              ),\r\n              )}\r\n            </ul>\r\n          </div>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileAttachmentPanel;\r\n", "import FileAttachmentPanel from './FileAttachmentPanel';\r\n\r\nexport default FileAttachmentPanel;\r\n"], "sourceRoot": ""}