{"version": 3, "sources": ["webpack:///./src/ui/src/components/AnnotationContentOverlay/AnnotationContentOverlay.scss?f138", "webpack:///./src/ui/src/components/AnnotationContentOverlay/AnnotationContentOverlay.scss", "webpack:///./src/ui/src/components/AnnotationContentOverlay/FormFieldWidgetOverlay.js", "webpack:///./src/ui/src/components/AnnotationContentOverlay/AnnotationContentOverlay.js", "webpack:///./src/ui/src/components/AnnotationContentOverlay/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "annotation", "PropTypes", "object", "overlayPosition", "overlayRef", "FormFieldWidgetOverlay", "t", "useTranslation", "formFieldWidgetName", "getField", "name", "className", "data-element", "DataElements", "ANNOTATION_CONTENT_OVERLAY", "style", "ref", "widget", "Core", "Annotations", "TextWidgetAnnotation", "SignatureWidgetAnnotation", "CheckButtonWidgetAnnotation", "ListWidgetAnnotation", "ChoiceWidgetAnnotation", "RadioButtonWidgetAnnotation", "PushButtonWidgetAnnotation", "mapWidgetypeToTranslation", "clientXY", "AnnotationContentOverlay", "useSelector", "state", "selectors", "isElementDisabled", "isElementOpen", "getAnnotationContentOverlayHandler", "shallowEqual", "isDisabled", "isOverlayOpen", "customHandler", "useState", "left", "top", "setOverlayPosition", "isUsingCustomHandler", "useRef", "contents", "getContents", "fitWindowSize", "useCallback", "clientX", "clientY", "overlayRect", "current", "getBoundingClientRect", "width", "innerWidth", "height", "innerHeight", "host", "getRootNode", "hostBoundingRect", "scrollLeft", "scrollTop", "useEffect", "numberOfReplies", "getReplies", "preRenderedElements", "customRender", "isMobileDevice", "undefined", "CustomElement", "render", "isInFormFieldCreationMode", "core", "getFormFieldCreationManager", "WidgetAnnotation", "getDisplayAuthor", "slice", "count"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,4gDAA6gD,KAGtiD0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,6gFCJvB,IAAMC,EAAY,CAChBC,WAAYC,IAAUC,OACtBC,gBAAiBF,IAAUC,OAC3BE,WAAYH,IAAUC,QAGlBG,EAAyB,SAAH,GAAoD,IAA9CL,EAAU,EAAVA,WAAYG,EAAe,EAAfA,gBAAiBC,EAAU,EAAVA,WACtDE,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAsBR,EAAWS,WAAWC,KAuBlD,OACE,yBACEC,UAAU,mCACVC,eAAcC,IAAaC,2BAC3BC,MAAK,KAAOZ,GACZa,IAAKZ,GAEL,6BACE,0BAAMW,MAAO,CAAE,WAAc,SAAWT,EAAE,kBAAkB,MAC3DA,EA9B2B,SAACW,GACjC,QAAQ,GACN,KAAKA,aAAkB3C,OAAO4C,KAAKC,YAAYC,qBAC7C,MAAO,uBACT,KAAKH,aAAkB3C,OAAO4C,KAAKC,YAAYE,0BAC7C,MAAO,4BACT,KAAKJ,aAAkB3C,OAAO4C,KAAKC,YAAYG,4BAC7C,MAAO,2BACT,KAAKL,aAAkB3C,OAAO4C,KAAKC,YAAYI,qBAC7C,MAAO,0BACT,KAAKN,aAAkB3C,OAAO4C,KAAKC,YAAYK,uBAC7C,MAAO,2BACT,KAAKP,aAAkB3C,OAAO4C,KAAKC,YAAYM,4BAC7C,MAAO,wBACT,KAAKR,aAAkB3C,OAAO4C,KAAKC,YAAYO,2BAC7C,MAAO,yBACT,QACE,QAaGC,CAA0B3B,KAE/B,6BACE,0BAAMe,MAAO,CAAE,WAAc,SAAWT,EAAE,sCAAsC,MAC/EE,KAMTH,EAAuBN,UAAYA,EACpBM,Q,oxEC3Cf,IAEMN,EAAY,CAChBC,WAAYC,IAAUC,OACtB0B,SAAU3B,IAAUC,QAGhB2B,EAA2B,SAAH,GAAiC,IAA3B7B,EAAU,EAAVA,WAAY4B,EAAQ,EAARA,SAU9B,IAJZE,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,kBAAkBF,EAAOlB,IAAaC,4BAChDkB,IAAUE,cAAcH,EAAOlB,IAAaC,4BAC5CkB,IAAUG,mCAAmCJ,MAC5CK,KAAa,GARdC,EAAU,KACVC,EAAa,KAEbC,EAAa,KAORjC,EAAqB,EAAhBC,cAAgB,GAApB,GAIN,IAH4CiC,mBAAS,CACrDC,MAAO,MACPC,KAAM,QACN,GAHKvC,EAAe,KAAEwC,EAAkB,KAKpCC,EAAyC,OAAlBL,EACvBnC,EAAayC,iBAAO,MACpBC,EAAW9C,aAAU,EAAVA,EAAY+C,cAKvBC,EAAgBC,uBAAY,SAACC,EAASC,EAASV,EAAMC,GACzD,IAAMU,EAAchD,EAAWiD,QAAQC,wBAcvC,GAZIb,EAAOW,EAAYG,MAAQjF,OAAOkF,aACpCf,EAAOS,EAAUE,EAAYG,MANrB,IASNb,EAAMU,EAAYK,OAASnF,OAAOoF,cACpChB,EAAMS,EAAUC,EAAYK,OAVpB,IAaNf,GAAO,IACTA,EAAM,GAGJpE,OAAOC,8BAA+B,OAClCoF,EAAoB,QAAhB,EAAGC,qBAAa,aAAb,EAAeD,KACtBE,EAAmBF,aAAI,EAAJA,EAAML,wBAC3BO,IACFpB,GAAQoB,EAAiBpB,KACzBC,GAAOmB,EAAiBnB,IAGxBD,GAAQkB,EAAKG,WACbpB,GAAOiB,EAAKI,WAIhB,MAAO,CAAEtB,OAAMC,SACd,IAEHsB,qBAAU,WACR,GAAI5D,EAAWiD,SAAWrD,EAAY,CACpC,IAAQkD,EAAqBtB,EAArBsB,QAASC,EAAYvB,EAAZuB,QACjB,EAAsBH,EAAcE,EAASC,EAASD,EApC9C,GAoC6DC,EApC7D,IAoCAV,EAAI,EAAJA,KAAMC,EAAG,EAAHA,IACdC,EAAmB,CAAEF,OAAMC,WAE5B,CAAC1C,EAAY4B,EAAUoB,IAE1B,IAAMiB,EAAkBjE,aAAU,EAAVA,EAAYkE,aAAarF,OAC3CsF,EAAsBvB,GAAwB5C,EAAauC,EAAcvC,GAAc,KACvFoE,EAAenB,uBAAY,kBAAMkB,IAAqB,CAACA,IAuB7D,GAAI9B,GAAcgC,MAAmBrE,EACnC,OAAO,KAGT,GAAI4C,GAAwBN,QAAyCgC,IAAxBH,EAC3C,OAAIA,EAEA,yBACExD,UAAU,mCACVC,eAAcC,IAAaC,2BAC3BC,MAAK,KAAOZ,GACZa,IAAKZ,GAEL,kBAACmE,EAAA,EAAa,CAACC,OAAQJ,KAItB,KAGT,IAAMK,EAA4BC,IAAKC,8BAA8BF,4BAErE,OAAInC,GAAiBmC,GAA6BzE,aAAsB1B,OAAO4C,KAAKC,YAAYyD,iBAE5F,kBAAC,EAAsB,CACrB5E,WAAYA,EACZG,gBAAiBA,EACjBC,WAAYA,IAKd0C,GAAYR,EApDd,yBACE3B,UAAU,mCACVC,eAAcC,IAAaC,2BAC3BC,MAAK,KAAOZ,GACZa,IAAKZ,GAEL,yBAAKO,UAAU,UAAU+D,IAAKG,iBAAiB7E,EAAmB,SAClE,yBAAKW,UAAU,YACZmC,EAASjE,OApFK,IAoFkB,UAC1BiE,EAASgC,MAAM,EArFP,KAqFyB,OACpChC,GAELmB,EAAkB,GACjB,yBAAKtD,UAAU,WACZL,EAAE,+BAAgC,CAAEyE,MAAOd,MA0C7C,MAGTpC,EAAyB9B,UAAYA,EAEtB8B,QCrJAA", "file": "chunks/chunk.57.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./AnnotationContentOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.AnnotationContentOverlay{visibility:visible}.closed.AnnotationContentOverlay{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.AnnotationContentOverlay{word-wrap:break-word;-ms-word-break:break-all;word-break:break-all;word-break:break-word;-webkit-hyphens:auto;hyphens:auto}.always-hide{display:none}.AnnotationContentOverlay{position:absolute;z-index:95;border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);display:flex;flex-direction:column;max-width:215px;padding:8px}.AnnotationContentOverlay .author{font-weight:700}.AnnotationContentOverlay .contents,.AnnotationContentOverlay .replies{margin-top:5px;white-space:pre-wrap}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "\nimport React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport DataElements from 'constants/dataElement';\nimport PropTypes from 'prop-types';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  overlayPosition: PropTypes.object,\n  overlayRef: PropTypes.object,\n};\n\nconst FormFieldWidgetOverlay = ({ annotation, overlayPosition, overlayRef }) => {\n  const [t] = useTranslation();\n  const formFieldWidgetName = annotation.getField().name;\n\n  const mapWidgetypeToTranslation = (widget) => {\n    switch (true) {\n      case widget instanceof window.Core.Annotations.TextWidgetAnnotation:\n        return 'formField.types.text';\n      case widget instanceof window.Core.Annotations.SignatureWidgetAnnotation:\n        return 'formField.types.signature';\n      case widget instanceof window.Core.Annotations.CheckButtonWidgetAnnotation:\n        return 'formField.types.checkbox';\n      case widget instanceof window.Core.Annotations.ListWidgetAnnotation:\n        return 'formField.types.listbox';\n      case widget instanceof window.Core.Annotations.ChoiceWidgetAnnotation:\n        return 'formField.types.combobox';\n      case widget instanceof window.Core.Annotations.RadioButtonWidgetAnnotation:\n        return 'formField.types.radio';\n      case widget instanceof window.Core.Annotations.PushButtonWidgetAnnotation:\n        return 'formField.types.button';\n      default:\n        return undefined;\n    }\n  };\n\n  return (\n    <div\n      className=\"Overlay AnnotationContentOverlay\"\n      data-element={DataElements.ANNOTATION_CONTENT_OVERLAY}\n      style={{ ...overlayPosition }}\n      ref={overlayRef}\n    >\n      <div>\n        <span style={{ 'fontWeight': 'bold' }}>{t('formField.type')}: </span>\n        {t(mapWidgetypeToTranslation(annotation))}\n      </div>\n      <div>\n        <span style={{ 'fontWeight': 'bold' }}>{t('formField.formFieldPopup.fieldName')}: </span>\n        {formFieldWidgetName}\n      </div>\n    </div>\n  );\n};\n\nFormFieldWidgetOverlay.propTypes = propTypes;\nexport default FormFieldWidgetOverlay;", "import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport PropTypes from 'prop-types';\nimport core from 'core';\nimport selectors from 'selectors';\n\nimport { isMobileDevice } from 'helpers/device';\nimport DataElements from 'constants/dataElement';\nimport CustomElement from '../CustomElement';\nimport FormFieldWidgetOverlay from './FormFieldWidgetOverlay';\nimport './AnnotationContentOverlay.scss';\nimport getRootNode from 'src/helpers/getRootNode';\n\nconst MAX_CHARACTERS = 100;\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  clientXY: PropTypes.object,\n};\n\nconst AnnotationContentOverlay = ({ annotation, clientXY }) => {\n  const [\n    isDisabled,\n    isOverlayOpen,\n    // Clients have the option to customize how the tooltip is rendered by passing a handler\n    customHand<PERSON>,\n  ] = useSelector((state) => [\n    selectors.isElementDisabled(state, DataElements.ANNOTATION_CONTENT_OVERLAY),\n    selectors.isElementOpen(state, DataElements.ANNOTATION_CONTENT_OVERLAY),\n    selectors.getAnnotationContentOverlayHandler(state),\n  ], shallowEqual);\n\n  const [t] = useTranslation();\n  const [overlayPosition, setOverlayPosition] = useState({\n    left: -99999,\n    top: -99999,\n  });\n\n  const isUsingCustomHandler = customHandler !== null;\n  const overlayRef = useRef(null);\n  const contents = annotation?.getContents();\n  // the gap between the component and the mouse, to make sure that the mouse won't be on component element\n  // so that the underlying annotation will always be hovered\n  const gap = 20;\n\n  const fitWindowSize = useCallback((clientX, clientY, left, top) => {\n    const overlayRect = overlayRef.current.getBoundingClientRect();\n\n    if (left + overlayRect.width > window.innerWidth) {\n      left = clientX - overlayRect.width - gap;\n    }\n\n    if (top + overlayRect.height > window.innerHeight) {\n      top = clientY - overlayRect.height - gap;\n    }\n\n    if (top <= 0) {\n      top = 0;\n    }\n\n    if (window.isApryseWebViewerWebComponent) {\n      const host = getRootNode()?.host;\n      const hostBoundingRect = host?.getBoundingClientRect();\n      if (hostBoundingRect) {\n        left -= hostBoundingRect.left;\n        top -= hostBoundingRect.top;\n\n        // Include host scroll offsets\n        left += host.scrollLeft;\n        top += host.scrollTop;\n      }\n    }\n\n    return { left, top };\n  }, []);\n\n  useEffect(() => {\n    if (overlayRef.current && annotation) {\n      const { clientX, clientY } = clientXY;\n      const { left, top } = fitWindowSize(clientX, clientY, clientX + gap, clientY + gap);\n      setOverlayPosition({ left, top });\n    }\n  }, [annotation, clientXY, fitWindowSize]);\n\n  const numberOfReplies = annotation?.getReplies().length;\n  const preRenderedElements = isUsingCustomHandler && annotation ? customHandler(annotation) : null;\n  const customRender = useCallback(() => preRenderedElements, [preRenderedElements]);\n\n  const renderContents = () => (\n    <div\n      className=\"Overlay AnnotationContentOverlay\"\n      data-element={DataElements.ANNOTATION_CONTENT_OVERLAY}\n      style={{ ...overlayPosition }}\n      ref={overlayRef}\n    >\n      <div className=\"author\">{core.getDisplayAuthor(annotation['Author'])}</div>\n      <div className=\"contents\">\n        {contents.length > MAX_CHARACTERS\n          ? `${contents.slice(0, MAX_CHARACTERS)}...`\n          : contents}\n      </div>\n      {numberOfReplies > 0 && (\n        <div className=\"replies\">\n          {t('message.annotationReplyCount', { count: numberOfReplies })}\n        </div>\n      )}\n    </div>\n  );\n\n  if (isDisabled || isMobileDevice || !annotation) {\n    return null;\n  }\n\n  if (isUsingCustomHandler && isOverlayOpen && preRenderedElements !== undefined) {\n    if (preRenderedElements) {\n      return (\n        <div\n          className=\"Overlay AnnotationContentOverlay\"\n          data-element={DataElements.ANNOTATION_CONTENT_OVERLAY}\n          style={{ ...overlayPosition }}\n          ref={overlayRef}\n        >\n          <CustomElement render={customRender} />\n        </div>\n      );\n    }\n    return null;\n  }\n\n  const isInFormFieldCreationMode = core.getFormFieldCreationManager().isInFormFieldCreationMode();\n\n  if (isOverlayOpen && isInFormFieldCreationMode && annotation instanceof window.Core.Annotations.WidgetAnnotation) {\n    return (\n      <FormFieldWidgetOverlay\n        annotation={annotation}\n        overlayPosition={overlayPosition}\n        overlayRef={overlayRef}\n      />\n    );\n  }\n\n  if (contents && isOverlayOpen) {\n    return renderContents();\n  }\n\n  return null;\n};\n\nAnnotationContentOverlay.propTypes = propTypes;\n\nexport default AnnotationContentOverlay;\n", "import AnnotationContentOverlay from './AnnotationContentOverlay';\n\nexport default AnnotationContentOverlay;"], "sourceRoot": ""}