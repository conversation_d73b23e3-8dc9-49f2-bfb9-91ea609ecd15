{"version": 3, "sources": ["webpack:///./src/ui/src/components/ColorPickerModal/ColorPickerModal.scss?3dc7", "webpack:///./src/ui/src/components/ColorPickerModal/ColorPickerModal.scss", "webpack:///./src/ui/src/components/ColorPickerModal/ColorPickerModal.js", "webpack:///./src/ui/src/components/ColorPickerModal/ColorPickerModalContainer.js", "webpack:///./src/ui/src/components/ColorPickerModal/ColorPickerModalRedux.js", "webpack:///./src/ui/src/components/ColorPickerModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "EditableInput", "prototype", "componentDidUpdate", "prevProps", "prevState", "this", "props", "value", "state", "activeElementExt", "activeElement", "getRootNode", "input", "setState", "blurValue", "String", "toUpperCase", "ColorPickerModal", "isDisabled", "isOpen", "color", "closeModal", "handleChangeSave", "handleChangeCancel", "t", "useTranslation", "useState", "selectedColor", "setSelectedColor", "modalClass", "classNames", "Modal", "open", "closed", "useEffect", "escFunction", "e", "key", "stopPropagation", "preventDefault", "closeModalHandler", "addEventListener", "removeEventListener", "A", "r", "R", "g", "G", "b", "B", "a", "useFocusOnClose", "handleChangeCancelWithFocus", "handleSaveWithFocus", "className", "data-element", "DataElements", "COLOR_PICKER_MODAL", "onMouseDown", "ModalWrapper", "<PERSON><PERSON><PERSON><PERSON>", "onCloseClick", "swipeToClose", "accessibleLabel", "disable<PERSON><PERSON>pha", "onChange", "newColor", "rgb", "presetColors", "<PERSON><PERSON>", "onClick", "label", "ColorPickerModalContainer", "closeColorPicker", "onColorChange", "rest", "useCallback", "newProps", "ColorPickerModalRedux", "dispatch", "useDispatch", "useSelector", "selectors", "isElementDisabled", "isElementOpen", "getCustomColor", "convertedColor", "Core", "Annotations", "Color", "actions", "setCustomColor", "closeElement"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,+zOAAg0O,KAGz1O0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,g2CCKvBC,EAAcC,UAAUC,mBAAqB,SAA4BC,EAAWC,GAClF,GAAIC,KAAKC,MAAMC,QAAUF,KAAKG,MAAMD,QAAUJ,EAAUI,QAAUF,KAAKC,MAAMC,OAASH,EAAUG,QAAUF,KAAKG,MAAMD,OAAQ,CAC3H,IAAIE,EAAmBhC,SAASiC,cAC5BC,gBACFF,EAAmBE,cAAcD,eAE/BL,KAAKO,QAAUH,EACjBJ,KAAKQ,SAAS,CAAEC,UAAWC,OAAOV,KAAKC,MAAMC,OAAOS,gBAEpDX,KAAKQ,SAAS,CAAEN,MAAOQ,OAAOV,KAAKC,MAAMC,OAAOS,cAAeF,WAAYT,KAAKG,MAAMM,WAAaC,OAAOV,KAAKC,MAAMC,OAAOS,kBAKlI,IAsEeC,EAtEU,SAAH,GAAwF,IAAlFC,EAAU,EAAVA,WAAYC,EAAM,EAANA,OAAQC,EAAK,EAALA,MAAOC,EAAU,EAAVA,WAAYC,EAAgB,EAAhBA,iBAAkBC,EAAkB,EAAlBA,mBAC5EC,EAAqB,EAAhBC,cAAgB,GAApB,GAC8C,IAAZC,mBAAS,IAAG,GAA/CC,EAAa,KAAEC,EAAgB,KAChCC,EAAaC,IAAW,CAC5BC,OAAO,EACPd,kBAAkB,EAClBe,KAAMb,EACNc,QAASd,IAGXe,qBAAU,WACR,IAAMC,EAAc,SAACC,GACL,WAAVA,EAAEC,MACJD,WAAGE,kBACHF,WAAGG,iBACHC,MAKJ,OADAjE,OAAOkE,iBAAiB,UAAWN,GAC5B,kBAAM5D,OAAOmE,oBAAoB,UAAWP,MAClD,IAEHD,qBAAU,WAEJd,GAAqB,IAAZA,EAAMuB,EACjBf,EAAiB,CAAEgB,EAAGxB,EAAMyB,EAAGC,EAAG1B,EAAM2B,EAAGC,EAAG5B,EAAM6B,EAAGC,EAAG,IAE1DtB,EAJY,CAAEgB,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAGE,EAAG,MAMpC,CAAC9B,IAEJ,IAQMoB,EAAoBW,YAAgB9B,GACpC+B,EAA8BD,YAAgB5B,GAC9C8B,EAAsBF,aANT,WACjB7B,EAAiBK,MAOnB,OAAOT,EAAa,KAClB,yBAAKoC,UAAWzB,EAAY0B,eAAcC,IAAaC,mBAAoBC,YAAarC,GACtF,kBAACsC,EAAA,EAAY,CACXxC,OAAQA,EACRyC,aAAcpB,EACdqB,aAAcrB,EACdsB,cAAY,EACZC,gBAAiB,+BAEjB,yBAAKT,UAAU,YAAYI,YAAa,SAACtB,GAAC,OAAKA,EAAEE,oBAC/C,kBAAC,IAAY,CACXlB,MAAOO,EACPqC,cAAY,EACZC,SAzBmB,SAACC,GAC5BtC,EAAiBsC,EAASC,MAyBlBC,aAAc,KAEhB,yBAAKd,UAAU,WACb,kBAACe,EAAA,EAAM,CAACf,UAAU,gBAAgBgB,QAASlB,EAA6BmB,MAAO/C,EAAE,mBACjF,kBAAC6C,EAAA,EAAM,CAACf,UAAU,cAAcgB,QAASjB,EAAqBkB,MAAO/C,EAAE,oB,koDC9DpEgD,MA1Bf,SAAmClE,GACjC,IAAQmE,EAA6CnE,EAA7CmE,iBAAkBC,EAA2BpE,EAA3BoE,cAAkBC,EAAI,EAAKrE,EAAK,GAEpDe,EAAauD,uBAAY,WAC7BH,MACC,CAACA,IAOElD,EAAqBqD,uBAAY,WACrCH,MACC,CAACA,IAEEI,EAAW,EAAH,KACTF,GAAI,IACPtD,aACAC,iBAZuB,SAACK,GACxB+C,EAAc/C,GACd8C,KAWAlD,uBAGF,OAAO,kBAAC,EAAqBsD,I,kwECQhBC,MA5Bf,SAA+BxE,GAC7B,IAAMyE,EAAWC,cAKf,IAJkCC,aAAY,SAACzE,GAAK,MAAK,CACzD0E,IAAUC,kBAAkB3E,EAAO,oBACnC0E,IAAUE,cAAc5E,EAAO,oBAC/B0E,IAAUG,eAAe7E,OACzB,GAJKU,EAAU,KAAEC,EAAM,KAAEC,EAAK,KAe1ByD,EAAW,OACZvE,GAAK,IACRc,QACAsD,cAZoB,SAAC/C,GACrB,IAAM2D,EAAiB,IAAI/G,OAAOgH,KAAKC,YAAYC,MAAM9D,EAAciB,EAAGjB,EAAcmB,EAAGnB,EAAcqB,EAAGrB,EAAcuB,GAC1H6B,EAASW,IAAQC,eAAeL,KAWhCpE,aACAuD,iBATuB,WACvBM,EAASW,IAAQE,aAAa,sBAS9BzE,WAEF,OAAO,kBAAC,EAA8B0D,IC7BzBC", "file": "chunks/chunk.59.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ColorPickerModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.ColorPickerModal{visibility:visible}.closed.ColorPickerModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ColorPickerModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.ColorPickerModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.ColorPickerModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.ColorPickerModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.ColorPickerModal .footer .modal-button.cancel:hover,.ColorPickerModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.ColorPickerModal .footer .modal-button.cancel,.ColorPickerModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.ColorPickerModal .footer .modal-button.cancel.disabled,.ColorPickerModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.ColorPickerModal .footer .modal-button.cancel.disabled span,.ColorPickerModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.ColorPickerModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.ColorPickerModal .modal-container .wrapper .modal-content{padding:10px}.ColorPickerModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.ColorPickerModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.ColorPickerModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.ColorPickerModal .footer .modal-button.confirm{margin-left:4px}.ColorPickerModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ColorPickerModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ColorPickerModal .footer .modal-button{padding:23px 8px}}.ColorPickerModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ColorPickerModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .ColorPickerModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ColorPickerModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ColorPickerModal .swipe-indicator{width:32px}}.ColorPickerModal .container{display:flex;flex-direction:column;justify-content:center;align-items:center;background:var(--component-background);padding:15px 16px;border-radius:4px;width:250px;overflow-y:unset}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ColorPickerModal .container{width:100%;padding:24px 24px 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ColorPickerModal .container{width:100%;padding:24px 24px 16px}}.ColorPickerModal .container .sketch-picker{border-radius:0!important;box-shadow:none!important;width:220px!important;padding:0!important;background:var(--component-background)!important}.ColorPickerModal .container .sketch-picker>div{border-radius:2px!important}.ColorPickerModal .container .sketch-picker .saturation-white>div>div{width:12px!important;height:12px!important;transform:translateX(-6px) translateY(-6px)!important}.ColorPickerModal .container .sketch-picker .flexbox-fix:nth-child(2) span{color:var(--text-color)!important;cursor:default!important}.ColorPickerModal .container .sketch-picker .flexbox-fix:nth-child(2)>div:first-child{border-radius:2px}.ColorPickerModal .container .sketch-picker .flexbox-fix:nth-child(2)>div:first-child>div{margin-top:6px;cursor:ew-resize!important;overflow:visible!important}.ColorPickerModal .container .sketch-picker .flexbox-fix:nth-child(2) .hue-horizontal{width:97%;border-radius:2px}.ColorPickerModal .container .sketch-picker .flexbox-fix:nth-child(2) .hue-horizontal div div{transform:translateX(-7px) translateY(-3px)!important;height:14px!important;width:14px!important;border-radius:14px!important;border:1px solid var(--gray-6)!important}.ColorPickerModal .container .sketch-picker .flexbox-fix:nth-child(2)>div:nth-child(2){height:24px!important;border-radius:12px!important}.ColorPickerModal .container .sketch-picker .flexbox-fix:nth-child(2)>div:nth-child(2)>div{border-radius:12px!important}.ColorPickerModal .container .sketch-picker .flexbox-fix:nth-child(3) input{width:100%!important;text-align:center;border-radius:2px}.ColorPickerModal .container .sketch-picker .flexbox-fix:nth-child(3) label{color:var(--text-color)!important}.ColorPickerModal .container .buttons{width:220px;text-align:right;font-size:13px;margin-top:20px;display:flex;justify-content:flex-end}.ColorPickerModal .container .buttons .save-button{background-color:transparent;color:var(--primary-button-text);padding:6px 16px;background:var(--primary-button);border-radius:4px;border:0;height:32px;cursor:pointer;width:-moz-fit-content;width:fit-content}.ColorPickerModal .container .buttons .save-button:hover{background:var(--blue-6)}.ColorPickerModal .container .buttons .cancel-button{cursor:pointer;background:none;border:0;color:var(--secondary-button-text);padding:6px 16px;margin-right:4px;height:32px;width:-moz-fit-content;width:fit-content}.ColorPickerModal .container .buttons .cancel-button:hover{color:var(--secondary-button-hover)}.ColorPickerModal .flexbox-fix input{padding:8px!important;font-size:13px!important;border:1px solid var(--gray-6)!important;box-shadow:none!important}.ColorPickerModal .flexbox-fix input:focus{border:1px solid var(--blue-5)!important;box-shadow:none!important}.ColorPickerModal .flexbox-fix label{font-size:13px!important}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport { useTranslation } from 'react-i18next';\nimport { SketchPicker } from 'react-color';\nimport DataElements from 'src/constants/dataElement';\nimport Button from 'components/Button';\nimport ModalWrapper from 'components/ModalWrapper';\nimport './ColorPickerModal.scss';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\nimport getRootNode from 'helpers/getRootNode';\nimport { EditableInput } from 'react-color/es/components/common';\n\n\n// This workaround resolves the issue where the 'Hex' input in the 'react-color'\n// library is not editable within WebComponent environments\nEditableInput.prototype.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n  if (this.props.value !== this.state.value && (prevProps.value !== this.props.value || prevState.value !== this.state.value)) {\n    let activeElementExt = document.activeElement;\n    if (getRootNode()) {\n      activeElementExt = getRootNode().activeElement;\n    }\n    if (this.input === activeElementExt) {\n      this.setState({ blurValue: String(this.props.value).toUpperCase() });\n    } else {\n      this.setState({ value: String(this.props.value).toUpperCase(), blurValue: !this.state.blurValue && String(this.props.value).toUpperCase() });\n    }\n  }\n};\n\nconst ColorPickerModal = ({ isDisabled, isOpen, color, closeModal, handleChangeSave, handleChangeCancel }) => {\n  const [t] = useTranslation();\n  const [selectedColor, setSelectedColor] = useState({});\n  const modalClass = classNames({\n    Modal: true,\n    ColorPickerModal: true,\n    open: isOpen,\n    closed: !isOpen,\n  });\n\n  useEffect(() => {\n    const escFunction = (e) => {\n      if (e.key === 'Escape') {\n        e?.stopPropagation();\n        e?.preventDefault();\n        closeModalHandler();\n      }\n    };\n\n    window.addEventListener('keydown', escFunction);\n    return () => window.removeEventListener('keydown', escFunction);\n  }, []);\n\n  useEffect(() => {\n    const black = { r: 0, g: 0, b: 0, a: 1 };\n    if (color && color.A !== 0) {\n      setSelectedColor({ r: color.R, g: color.G, b: color.B, a: 1 });\n    } else {\n      setSelectedColor(black);\n    }\n  }, [color]);\n\n  const handleChangeComplete = (newColor) => {\n    setSelectedColor(newColor.rgb);\n  };\n\n  const handleSave = () => {\n    handleChangeSave(selectedColor);\n  };\n\n  const closeModalHandler = useFocusOnClose(closeModal);\n  const handleChangeCancelWithFocus = useFocusOnClose(handleChangeCancel);\n  const handleSaveWithFocus = useFocusOnClose(handleSave);\n\n  return isDisabled ? null : (\n    <div className={modalClass} data-element={DataElements.COLOR_PICKER_MODAL} onMouseDown={closeModal}>\n      <ModalWrapper\n        isOpen={isOpen}\n        closeHandler={closeModalHandler}\n        onCloseClick={closeModalHandler}\n        swipeToClose\n        accessibleLabel={'colorPickerModal.modalTitle'}\n      >\n        <div className=\"container\" onMouseDown={(e) => e.stopPropagation()}>\n          <SketchPicker\n            color={selectedColor}\n            disableAlpha\n            onChange={handleChangeComplete}\n            presetColors={[]}\n          />\n          <div className=\"buttons\">\n            <Button className=\"cancel-button\" onClick={handleChangeCancelWithFocus} label={t('action.cancel')} />\n            <Button className=\"save-button\" onClick={handleSaveWithFocus} label={t('action.ok')} />\n          </div>\n        </div>\n      </ModalWrapper >\n    </div>\n  );\n};\n\nexport default ColorPickerModal;\n", "import React, { useCallback } from 'react';\r\nimport ColorPickerModal from './ColorPickerModal';\r\n\r\nfunction ColorPickerModalContainer(props) {\r\n  const { closeColorPicker, onColorChange, ...rest } = props;\r\n\r\n  const closeModal = useCallback(() => {\r\n    closeColorPicker();\r\n  }, [closeColorPicker]);\r\n\r\n  const handleChangeSave = (selectedColor) => {\r\n    onColorChange(selectedColor);\r\n    closeColorPicker();\r\n  };\r\n\r\n  const handleChangeCancel = useCallback(() => {\r\n    closeColorPicker();\r\n  }, [closeColorPicker]);\r\n\r\n  const newProps = {\r\n    ...rest,\r\n    closeModal,\r\n    handleChangeSave,\r\n    handleChangeCancel,\r\n  };\r\n\r\n  return <ColorPickerModal {...newProps} />;\r\n}\r\n\r\nexport default ColorPickerModalContainer;\r\n", "import React from 'react';\r\nimport selectors from 'selectors';\r\nimport actions from 'actions';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport ColorPickerModalContainer from './ColorPickerModalContainer';\r\n\r\nfunction ColorPickerModalRedux(props) {\r\n  const dispatch = useDispatch();\r\n  const [isDisabled, isOpen, color] = useSelector((state) => [\r\n    selectors.isElementDisabled(state, 'ColorPickerModal'),\r\n    selectors.isElementOpen(state, 'ColorPickerModal'),\r\n    selectors.getCustomColor(state),\r\n  ]);\r\n\r\n  const onColorChange = (selectedColor) => {\r\n    const convertedColor = new window.Core.Annotations.Color(selectedColor.r, selectedColor.g, selectedColor.b, selectedColor.a);\r\n    dispatch(actions.setCustomColor(convertedColor));\r\n  };\r\n\r\n  const closeColorPicker = () => {\r\n    dispatch(actions.closeElement('ColorPickerModal'));\r\n  };\r\n\r\n  const newProps = {\r\n    ...props,\r\n    color,\r\n    onColorChange,\r\n    isDisabled,\r\n    closeColorPicker,\r\n    isOpen,\r\n  };\r\n  return <ColorPickerModalContainer {...newProps} />;\r\n}\r\n\r\nexport default ColorPickerModalRedux;\r\n", "import ColorPickerModalRedux from './ColorPickerModalRedux';\n\nexport default ColorPickerModalRedux;"], "sourceRoot": ""}