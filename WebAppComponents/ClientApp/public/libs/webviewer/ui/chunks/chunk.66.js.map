{"version": 3, "sources": ["webpack:///./src/ui/src/components/HeaderFooterOptionsModal/HeaderFooterOptionsModal.scss?f8fd", "webpack:///./src/ui/src/components/HeaderFooterOptionsModal/HeaderFooterOptionsModal.scss", "webpack:///./src/ui/src/components/HeaderFooterOptionsModal/HeaderFooterOptionsModal.js", "webpack:///./src/ui/src/components/HeaderFooterOptionsModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "HeaderFooterOptionsModal", "t", "useTranslation", "dispatch", "useDispatch", "isOpen", "useSelector", "state", "selectors", "isElementOpen", "DataElements", "HEADER_FOOTER_OPTIONS_MODAL", "useState", "headerToTopOnOpen", "setHeaderToTopOnOpen", "footerToBottomOnOpen", "setFooterToBottomOnOpen", "headerToTop", "setHeaderToTop", "footerToBottom", "setFooterToBottom", "differentFirstPageEnabled", "setDifferentFirstPageEnabled", "oddEvenEnabled", "setOddEvenEnabled", "maxMarginsInInches", "setMaxMarginsInInches", "currentUnit", "setCurrentUnit", "validateInput", "input", "validatedInput", "replace", "maxMarginsConverted", "CM_PER_INCH", "parseFloat", "toFixed", "inchesToCurrentUnit", "inches", "val", "currentUnitToInches", "onSave", "actions", "closeElement", "headerToTopInches", "footerToBottomInches", "pageNumber", "HeaderFooterModalState", "getPageNumber", "headerFooterDistanceChanged", "Promise", "all", "core", "getOfficeEditor", "setDifferentFirstPage", "setOddEven", "setHeaderFooterMarginsInInches", "useEffect", "getHeaderFooterMarginsInInches", "headerDistanceToTop", "footerDistanceToBottom", "headerDistanceToTopConverted", "footerDistanceToBottomConverted", "getDifferentFirstPage", "getOddEven", "getMaxHeaderFooterDistance", "ma<PERSON><PERSON><PERSON><PERSON>", "closeModal", "setTimeout", "focusContent", "modalClass", "classNames", "className", "data-element", "ModalWrapper", "title", "closehandler", "onCloseClick", "swipeToClose", "htmlFor", "Input", "type", "id", "data-testid", "onChange", "e", "target", "value", "onBlur", "min", "step", "Choice", "label", "aria-label", "checked", "aria-checked", "event", "<PERSON><PERSON>", "onClick"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,w0NAA20N,KAGp2N0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,wuBCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAeA,IAoLe4B,EApLkB,WAC/B,IAAOC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAEXC,EAASC,aAAY,SAACC,GAAK,OAAKC,IAAUC,cAAcF,EAAOG,IAAaC,gCACpB,IAAZC,mBAAS,IAAG,GAAvDC,EAAiB,KAAEC,EAAoB,KACsB,IAAZF,mBAAS,IAAG,GAA7DG,EAAoB,KAAEC,EAAuB,KACF,IAAZJ,mBAAS,IAAG,GAA3CK,EAAW,KAAEC,EAAc,KACsB,IAAZN,mBAAS,IAAG,GAAjDO,EAAc,KAAEC,EAAiB,KACyC,IAAfR,oBAAS,GAAM,GAA1ES,EAAyB,KAAEC,EAA4B,KACH,IAAfV,oBAAS,GAAM,GAApDW,EAAc,KAAEC,EAAiB,KACuB,IAAXZ,mBAAS,GAAE,GAAxDa,EAAkB,KAAEC,EAAqB,KACI,IAAdd,mBAAS,MAAK,GAA7Ce,EAAW,KAAEC,EAAc,KAE5BC,EAAgB,SAACC,GACrB,GAAIA,GAASA,GAAS,EACpB,MAAO,IAGT,IAAMC,EAAiBD,EAAME,QAAQ,YAAa,IAE9CC,EAAsBR,EAM1B,MAJoB,OAAhBE,IACFM,EAAsBR,EAAqBS,KAGzCC,WAAWJ,GAAkBE,EACxBA,EAAoBG,QAAQ,GAG9BL,GAyBHM,EAAsB,SAACC,GAC3B,IAAIC,EAAMD,EAIV,MAHoB,OAAhBX,IACFY,GAAOJ,WAAWG,GAAUJ,KAAaE,QAAQ,IAE5CG,GAGHC,EAAsB,SAACV,GAC3B,IAAIS,EAAMJ,WAAWL,GAIrB,MAHoB,OAAhBH,IACFY,GAAYL,KAEPK,GAGHE,EAAM,6BAAG,0FAKmG,OAJhHtC,EAASuC,IAAQC,aAAajC,IAAaC,8BACrCiC,EAAoBJ,EAAoBvB,GACxC4B,EAAuBL,EAAoBrB,GAC3C2B,EAAaC,IAAuBC,gBACpCC,EAA8BpC,IAAsBI,GAAeF,IAAyBI,EAAc,kBACzG+B,QAAQC,IAAI,CACjBC,IAAKC,kBAAkBC,sBAAsBR,EAAYzB,GACzD+B,IAAKC,kBAAkBE,WAAWhC,GAClC0B,GAA+BG,IAAKC,kBAAkBG,+BAA+BV,EAAYF,EAAmBC,MACpH,2CACH,kBAXW,mCAaZY,oBAAS,YAAC,oGACJpD,EAAQ,CAAF,gBAEiD,OADzDuB,EAAe,MACTkB,EAAaC,IAAuBC,gBAAe,SACKI,IAAKC,kBAAkBK,+BAA+BZ,GAAW,OAQnG,OARmG,SAAvHa,EAAmB,EAAnBA,oBAAqBC,EAAsB,EAAtBA,uBACvBC,EAA+BxB,EAAoBsB,GACnDG,EAAkCzB,EAAoBuB,GAC5D1C,EAAe2C,GACfzC,EAAkB0C,GAClBhD,EAAqB+C,GACrB7C,EAAwB8C,GAAiC,KAEzDxC,EAA4B,UAAO8B,IAAKC,kBAAkBU,sBAAsBjB,GAAW,QAC1E,OAD0E,gCAC3FtB,EAAiB,UAAO4B,IAAKC,kBAAkBW,aAAY,oDAElCZ,IAAKC,kBAAkBY,2BAA2BnB,GAAW,QAAhFoB,EAAa,EAAH,KAChBxC,EAAsBwC,GAAY,4CAEnC,CAAC7D,IAEJ,IAAM8D,EAAa,WACjBhE,EAASuC,IAAQC,aAAajC,IAAaC,8BAE3CyD,YAAW,WACThB,IAAKC,kBAAkBgB,iBACtB,IAGCC,EAAaC,IAAW,CAC5B,0BAA4B,IAG9B,OAAOlE,GACL,yBAAKmE,UAAWF,EAAYG,eAAc/D,IAAaC,6BACrD,kBAAC+D,EAAA,EAAY,CACXrE,OAAQA,EACRsE,MAAO1E,EAAE,+CACT2E,aAAcT,EACdU,aAAcV,EACdW,cAAY,GAEZ,yBAAKN,UAAU,cACb,yBAAKA,UAAU,SAASvE,EAAE,kDAC1B,yBAAKuE,UAAU,mBACb,2BAAOO,QAAQ,mBAAmBP,UAAU,SAASvE,EAAE,wDACvD,kBAAC+E,EAAA,EAAK,CACJC,KAAK,SACLC,GAAG,mBACHC,cAAY,mBACZC,SAxFgB,SAACC,GAC3B,IAAM9C,EAAMV,EAAcwD,EAAEC,OAAOC,OACnCrE,EAAeqB,IAuFLiD,OArGc,SAACH,GACF,KAAnBA,EAAEC,OAAOC,OACXrE,EAAe,MAoGPqE,MAAOtE,EACPwE,IAAI,IACJC,KAAK,SAGT,yBAAKlB,UAAU,mBACb,2BAAOO,QAAQ,sBAAsBP,UAAU,SAASvE,EAAE,2DAC1D,kBAAC+E,EAAA,EAAK,CACJC,KAAK,SACLC,GAAG,sBACHC,cAAY,sBACZC,SAhGmB,SAACC,GAC9B,IAAM9C,EAAMV,EAAcwD,EAAEC,OAAOC,OACnCnE,EAAkBmB,IA+FRiD,OA5Gc,SAACH,GACF,KAAnBA,EAAEC,OAAOC,OACXnE,EAAkB,MA2GVmE,MAAOpE,EACPsE,IAAI,IACJC,KAAK,SAGT,yBAAKlB,UAAU,SAASvE,EAAE,yDAC1B,kBAAC0F,EAAA,EAAM,CACLT,GAAI,uBACJU,MAAO3F,EAAE,oEACT4F,aAAY5F,EAAE,oEACd6F,QAASzE,EACT0E,eAAc1E,EACd+D,SAAU,SAACY,GAAK,OAAK1E,EAA6B0E,EAAMV,OAAOQ,YAEjE,kBAACH,EAAA,EAAM,CACLT,GAAI,qBACJU,MAAO3F,EAAE,uEACT4F,aAAY5F,EAAE,uEACd6F,QAASvE,EACTwE,eAAcxE,EACd6D,SAAU,SAACY,GAAK,OAAKxE,EAAkBwE,EAAMV,OAAOQ,aAGxD,yBAAKtB,UAAU,UACb,kBAACyB,EAAA,EAAM,CAACC,QAASzD,EAAQmD,MAAO3F,EAAE,qBC3L7BD", "file": "chunks/chunk.66.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./HeaderFooterOptionsModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.HeaderFooterOptionsModal{visibility:visible}.closed.HeaderFooterOptionsModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.HeaderFooterOptionsModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.HeaderFooterOptionsModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.cancel:hover,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.HeaderFooterOptionsModal .footer .modal-button.cancel,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled span,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.HeaderFooterOptionsModal .modal-container .wrapper .modal-content{padding:10px}.HeaderFooterOptionsModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.HeaderFooterOptionsModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.HeaderFooterOptionsModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.HeaderFooterOptionsModal .footer .modal-button.confirm{margin-left:4px}.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}.HeaderFooterOptionsModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}.HeaderFooterOptionsModal{flex-direction:column}.HeaderFooterOptionsModal .modal-container{display:flex;flex-direction:column;height:auto;width:480px}.HeaderFooterOptionsModal .modal-container .modal-body{padding:16px;display:flex;flex-direction:column;font-size:var(--font-size-default);font-family:var(--font-family);grid-gap:16px;gap:16px}.HeaderFooterOptionsModal .modal-container .modal-body .title{line-height:16px;font-weight:var(--font-weight-bold)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container{display:flex;flex-direction:column;grid-gap:8px;gap:8px}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .label{color:var(--gray-12);display:block;text-align:left;font-weight:var(--font-weight-normal)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input{border-color:var(--gray-5);position:relative}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input:after{content:\\\"cm\\\";font-size:13px;color:var(--gray-8);position:absolute;right:16px;pointer-events:none}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input.ui__input--focused{box-shadow:none;border-color:var(--focus-border)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input{padding:8px 40px 8px 8px;height:32px;font-size:var(--font-size-default)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input[type=number]{-moz-appearance:textfield}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-inner-spin-button,.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container{display:flex;flex-direction:column;grid-gap:16px;gap:16px;margin:0}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container .ui__choice--checked .ui__choice__input__check{border-color:var(--blue-5)}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container .ui__choice__input__check{border-color:var(--gray-7)}.HeaderFooterOptionsModal .modal-container .footer{padding:16px;display:flex;justify-content:flex-end;border-top:1px solid var(--gray-5)}.HeaderFooterOptionsModal .modal-container .footer button{border:none;border-radius:4px;background:var(--primary-button);min-width:59px;width:auto;padding:8px 16px;height:32px;color:var(--primary-button-text)}.HeaderFooterOptionsModal .modal-container .footer button:hover{background:var(--primary-button-hover)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport classNames from 'classnames';\nimport DataElements from 'constants/dataElement';\nimport Button from 'components/Button';\nimport ModalWrapper from 'components/ModalWrapper';\nimport { Choice, Input } from '@pdftron/webviewer-react-toolkit';\nimport core from 'core';\nimport { CM_PER_INCH } from 'constants/officeEditor';\nimport HeaderFooterModalState from 'helpers/headerFooterModalState';\n\nimport './HeaderFooterOptionsModal.scss';\n\nconst HeaderFooterOptionsModal = () => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const isOpen = useSelector((state) => selectors.isElementOpen(state, DataElements.HEADER_FOOTER_OPTIONS_MODAL));\n  const [headerToTopOnOpen, setHeaderToTopOnOpen] = useState('');\n  const [footerToBottomOnOpen, setFooterToBottomOnOpen] = useState('');\n  const [headerToTop, setHeaderToTop] = useState('');\n  const [footerToBottom, setFooterToBottom] = useState('');\n  const [differentFirstPageEnabled, setDifferentFirstPageEnabled] = useState(false);\n  const [oddEvenEnabled, setOddEvenEnabled] = useState(false);\n  const [maxMarginsInInches, setMaxMarginsInInches] = useState(0);\n  const [currentUnit, setCurrentUnit] = useState('cm');\n\n  const validateInput = (input) => {\n    if (input && input <= 0) {\n      return '0';\n    }\n    // Removes leading zero unless it is followed by a decimal\n    const validatedInput = input.replace(/^0+(?!\\.)/, '');\n\n    let maxMarginsConverted = maxMarginsInInches;\n\n    if (currentUnit === 'cm') {\n      maxMarginsConverted = maxMarginsInInches * CM_PER_INCH;\n    }\n\n    if (parseFloat(validatedInput) > maxMarginsConverted) {\n      return maxMarginsConverted.toFixed(2);\n    }\n\n    return validatedInput;\n  };\n\n  const onHeaderInputBlur = (e) => {\n    if (e.target.value === '') {\n      setHeaderToTop('0');\n    }\n  };\n\n  const onFooterInputBlur = (e) => {\n    if (e.target.value === '') {\n      setFooterToBottom('0');\n    }\n  };\n\n  const onHeaderToTopChange = (e) => {\n    const val = validateInput(e.target.value);\n    setHeaderToTop(val);\n  };\n\n  const onFooterToBottomChange = (e) => {\n    const val = validateInput(e.target.value);\n    setFooterToBottom(val);\n  };\n\n  const inchesToCurrentUnit = (inches) => {\n    let val = inches;\n    if (currentUnit === 'cm') {\n      val = (parseFloat(inches) * CM_PER_INCH).toFixed(2);\n    }\n    return val;\n  };\n\n  const currentUnitToInches = (input) => {\n    let val = parseFloat(input);\n    if (currentUnit === 'cm') {\n      val = val / CM_PER_INCH;\n    }\n    return val;\n  };\n\n  const onSave = async () => {\n    dispatch(actions.closeElement(DataElements.HEADER_FOOTER_OPTIONS_MODAL));\n    const headerToTopInches = currentUnitToInches(headerToTop);\n    const footerToBottomInches = currentUnitToInches(footerToBottom);\n    const pageNumber = HeaderFooterModalState.getPageNumber();\n    const headerFooterDistanceChanged = headerToTopOnOpen !== headerToTop || footerToBottomOnOpen !== footerToBottom;\n    return Promise.all([\n      core.getOfficeEditor().setDifferentFirstPage(pageNumber, differentFirstPageEnabled),\n      core.getOfficeEditor().setOddEven(oddEvenEnabled),\n      headerFooterDistanceChanged && core.getOfficeEditor().setHeaderFooterMarginsInInches(pageNumber, headerToTopInches, footerToBottomInches),\n    ]);\n  };\n\n  useEffect(async () => {\n    if (isOpen) {\n      setCurrentUnit('cm');\n      const pageNumber = HeaderFooterModalState.getPageNumber();\n      const { headerDistanceToTop, footerDistanceToBottom } = await core.getOfficeEditor().getHeaderFooterMarginsInInches(pageNumber);\n      const headerDistanceToTopConverted = inchesToCurrentUnit(headerDistanceToTop);\n      const footerDistanceToBottomConverted = inchesToCurrentUnit(footerDistanceToBottom);\n      setHeaderToTop(headerDistanceToTopConverted);\n      setFooterToBottom(footerDistanceToBottomConverted);\n      setHeaderToTopOnOpen(headerDistanceToTopConverted);\n      setFooterToBottomOnOpen(footerDistanceToBottomConverted);\n\n      setDifferentFirstPageEnabled(await core.getOfficeEditor().getDifferentFirstPage(pageNumber));\n      setOddEvenEnabled(await core.getOfficeEditor().getOddEven());\n\n      const maxMargins = await core.getOfficeEditor().getMaxHeaderFooterDistance(pageNumber);\n      setMaxMarginsInInches(maxMargins);\n    }\n  }, [isOpen]);\n\n  const closeModal = () => {\n    dispatch(actions.closeElement(DataElements.HEADER_FOOTER_OPTIONS_MODAL));\n\n    setTimeout(() => {\n      core.getOfficeEditor().focusContent();\n    }, 0);\n  };\n\n  const modalClass = classNames({\n    'HeaderFooterOptionsModal': true\n  });\n\n  return isOpen && (\n    <div className={modalClass} data-element={DataElements.HEADER_FOOTER_OPTIONS_MODAL}>\n      <ModalWrapper\n        isOpen={isOpen}\n        title={t('officeEditor.headerFooterOptionsModal.title')}\n        closehandler={closeModal}\n        onCloseClick={closeModal}\n        swipeToClose\n      >\n        <div className='modal-body'>\n          <div className='title'>{t('officeEditor.headerFooterOptionsModal.margins')}</div>\n          <div className='input-container'>\n            <label htmlFor='headerToTopInput' className='label'>{t('officeEditor.headerFooterOptionsModal.headerFromTop')}</label>\n            <Input\n              type='number'\n              id='headerToTopInput'\n              data-testid=\"headerToTopInput\"\n              onChange={onHeaderToTopChange}\n              onBlur={onHeaderInputBlur}\n              value={headerToTop}\n              min='0'\n              step='any'\n            />\n          </div>\n          <div className='input-container'>\n            <label htmlFor='footerToBottomInput' className='label'>{t('officeEditor.headerFooterOptionsModal.footerFromBottom')}</label>\n            <Input\n              type='number'\n              id='footerToBottomInput'\n              data-testid=\"footerToBottomInput\"\n              onChange={onFooterToBottomChange}\n              onBlur={onFooterInputBlur}\n              value={footerToBottom}\n              min='0'\n              step='any'\n            />\n          </div>\n          <div className='title'>{t('officeEditor.headerFooterOptionsModal.layouts.layout')}</div>\n          <Choice\n            id={'different-first-page'}\n            label={t('officeEditor.headerFooterOptionsModal.layouts.differentFirstPage')}\n            aria-label={t('officeEditor.headerFooterOptionsModal.layouts.differentFirstPage')}\n            checked={differentFirstPageEnabled}\n            aria-checked={differentFirstPageEnabled}\n            onChange={(event) => setDifferentFirstPageEnabled(event.target.checked)}\n          />\n          <Choice\n            id={'different-odd-even'}\n            label={t('officeEditor.headerFooterOptionsModal.layouts.differentEvenOddPages')}\n            aria-label={t('officeEditor.headerFooterOptionsModal.layouts.differentEvenOddPages')}\n            checked={oddEvenEnabled}\n            aria-checked={oddEvenEnabled}\n            onChange={(event) => setOddEvenEnabled(event.target.checked)}\n          />\n        </div>\n        <div className='footer'>\n          <Button onClick={onSave} label={t('action.save')} />\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default HeaderFooterOptionsModal;\n", "import HeaderFooterOptionsModal from './HeaderFooterOptionsModal';\n\nexport default HeaderFooterOptionsModal;\n"], "sourceRoot": ""}