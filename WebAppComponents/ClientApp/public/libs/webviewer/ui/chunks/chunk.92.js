(window.webpackJsonp=window.webpackJsonp||[]).push([[92],{1975:function(e,t,a){"use strict";a.r(t);a(97),a(36),a(16),a(60),a(44);var n=a(0),s=a.n(n),r=a(84),c=a(4),i=a.n(c),o=a(68),l=a(71),p=a(149);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}var y={type:i.a.oneOf(Object.values(p.b)).isRequired,isFlyoutItem:i.a.bool,style:i.a.object,className:i.a.string},b=Object(n.forwardRef)((function(e,t){var a=e.isFlyoutItem,n=e.type,c=e.style,i=e.className,p="cell".concat(n.charAt(0).toUpperCase()).concat(n.slice(1)),y=l.b[p],b=y.dataElement,d=y.icon,f=y.title,m=function(){};return a?s.a.createElement(o.a,u({},e,{ref:t,onClick:m,additionalClass:""})):s.a.createElement(r.a,{key:n,isActive:!1,onClick:m,dataElement:b,title:f,img:d,ariaPressed:!1,style:c,className:i})}));b.propTypes=y,b.displayName="ColorPickerButton",t.default=b}}]);
//# sourceMappingURL=chunk.92.js.map