{"action": {"addParagraph": "Tambahkan Paragraf", "add": "Menambahkan", "addSheet": "Tambahkan Lembar", "apply": "Berlaku", "applyAll": "<PERSON><PERSON><PERSON> semua", "calendar": "<PERSON><PERSON><PERSON>", "calibrate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "Membatalkan", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentPageIs": "Halaman saat ini adalah", "clear": "<PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON> semua", "close": "<PERSON><PERSON><PERSON>", "undo": "Membuka", "redo": "Siap", "comment": "Komentar", "reply": "<PERSON><PERSON><PERSON> balasan", "copy": "Salinan", "cut": "Memotong", "paste": "Tempel", "pasteWithoutFormatting": "Tempel tanpa memformat", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleted": "<PERSON><PERSON><PERSON>", "group": "Kelompok", "ungroup": "Pisahkan grup", "download": "<PERSON><PERSON><PERSON>", "edit": "Sunting", "collapse": "<PERSON><PERSON><PERSON>", "expand": "<PERSON><PERSON><PERSON><PERSON>", "extract": "Ekstrak", "extractPage": "Ekstrak Halaman", "enterFullscreen": "<PERSON><PERSON> penuh", "exitFullscreen": "<PERSON><PERSON><PERSON> dari layar penuh", "fit": "Bugar", "fitToPage": "<PERSON><PERSON><PERSON><PERSON> dengan halaman", "fitToWidth": "<PERSON><PERSON><PERSON> dengan lebar", "more": "<PERSON><PERSON>", "openFile": "Membuka file", "showMoreFiles": "<PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON>", "of": "dari", "pagePrev": "Halaman sebelumnya", "pageNext": "Halaman <PERSON>", "pageSet": "<PERSON><PERSON>", "print": "Mencetak", "proceed": "Melanjutkan", "name": "<PERSON><PERSON>", "rename": "Ganti nama", "remove": "<PERSON><PERSON><PERSON><PERSON>", "ok": "oke", "rotate": "<PERSON><PERSON>", "rotate3D": "Memutar", "rotateClockwise": "Putar searah jarum jam", "rotateCounterClockwise": "Putar berlawanan arah jarum jam", "rotatedClockwise": "diputar searah jarum jam", "rotatedCounterClockwise": "diputar berlawanan arah jarum jam", "rotationIs": "rotasi halaman saat ini adalah", "movedToBottomOfDocument": "dipindahkan ke bagian bawah dokumen", "movedToTopofDocument": "dipindahkan ke bagian atas dokumen", "extracted": "diekstraks<PERSON>", "save": "Menyimpan", "post": "Pos", "create": "Membuat", "update": "<PERSON><PERSON><PERSON><PERSON>", "showMoreResults": "<PERSON><PERSON><PERSON><PERSON> hasil la<PERSON>ya", "sign": "<PERSON><PERSON>", "style": "<PERSON><PERSON>", "submit": "<PERSON><PERSON>", "zoom": "<PERSON><PERSON><PERSON>", "zoomIn": "<PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "zoomSet": "Setel zoom", "zoomChanged": "Zoom saat ini adalah", "zoomControls": "Kontrol Zoom", "draw": "Seri", "type": "<PERSON><PERSON>", "upload": "Mengunggah", "link": "Tautan", "unlink": "Hapus Tau<PERSON>", "fileAttachmentDownload": "Unduh file terlampir", "prevResult": "<PERSON><PERSON>", "nextResult": "<PERSON><PERSON>", "prev": "Sebelumnya", "next": "<PERSON><PERSON><PERSON><PERSON>", "startFormEditing": "<PERSON><PERSON>", "exitFormEditing": "<PERSON><PERSON><PERSON> dari Mode Pengeditan Formulir", "exit": "keluar", "addOption": "Tambahkan Opsi", "formFieldEdit": "<PERSON> <PERSON>", "formFieldEditMode": "<PERSON> <PERSON>", "contentEditMode": "Sunting Konten", "viewShortCutKeysFor3D": "Lihat Tombol Pintasan", "markAllRead": "tandai semua telah dibaca", "pageInsertion": "<PERSON><PERSON><PERSON><PERSON>", "insertPage": "<PERSON><PERSON><PERSON><PERSON>", "insert": "Memasukkan", "pageManipulation": "<PERSON><PERSON><PERSON><PERSON>", "replace": "Mengganti", "replacePage": "<PERSON><PERSON>", "modal": "modal", "isOpen": "terb<PERSON>", "setDestination": "Tetapkan <PERSON>", "showLess": "tampilkan lebih sedikit", "showMore": "...lagi", "chooseFile": "Pilih file", "changeDate": "Ganti tanggal", "browse": "<PERSON><PERSON><PERSON> be<PERSON>s", "selectYourOption": "<PERSON><PERSON><PERSON> opsi Anda", "open": "Membuka", "deselectAll": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "moveToTop": "Pindah ke atas", "moveToBottom": "Pindah ke bawah", "movePageToTop": "Pindahkan <PERSON> ke Atas", "movePageToBottom": "Pindahkan <PERSON> ke Bawah", "moveUp": "Pindah ke atas", "moveDown": "Pindah ke bawah", "moveLeft": "Pindah ke Kiri", "moveRight": "Pindah ke Kanan", "backToMenu": "Ke<PERSON>li ke menu", "redactPages": "<PERSON><PERSON><PERSON>", "playAudio": "Putar audio", "pauseAudio": "jeda audio", "selectAll": "<PERSON><PERSON><PERSON>", "unselect": "Batalkan pilihan", "addMark": "Tambahkan Tandai", "viewFile": "Lihat file", "multiReplyAnnotations": "<PERSON><PERSON><PERSON> anotasi yang dipilih ({{count}})", "comparePages": "<PERSON><PERSON><PERSON> halaman", "startComparison": "<PERSON><PERSON>", "showComparison": "<PERSON><PERSON><PERSON><PERSON>", "highlightChanges": "<PERSON><PERSON>", "back": "Kembali", "clearSignature": "Tanda tangan yang jelas", "clearInitial": "<PERSON><PERSON>al yang jelas", "readOnlySignature": "Tanda tangan hanya-baca tidak dapat dihapus", "newDocument": "<PERSON><PERSON><PERSON>", "sideBySideView": "<PERSON><PERSON><PERSON>", "pageNumberInput": "<PERSON><PERSON><PERSON> nomor halaman", "addNewColor": "Tambahkan Warna Baru", "deleteColor": "<PERSON><PERSON> ya<PERSON>", "copySelectedColor": "<PERSON><PERSON> ya<PERSON>", "showMoreColors": "<PERSON><PERSON><PERSON><PERSON>", "showLessColors": "<PERSON><PERSON><PERSON><PERSON>", "fromCustomColorPicker": "<PERSON><PERSON> <PERSON><PERSON>", "newSpreadsheetDocument": "<PERSON><PERSON><PERSON>"}, "annotation": {"areaMeasurement": "<PERSON><PERSON><PERSON>", "arc": "<PERSON><PERSON>", "arcMeasurement": "Pengukuran busur", "arrow": "<PERSON><PERSON>ah", "callout": "Memanggil", "crop": "<PERSON><PERSON><PERSON>", "caret": "kekurangan", "dateFreeText": "<PERSON><PERSON><PERSON>", "formFillCheckmark": "<PERSON><PERSON>", "formFillCross": "Menyeberang", "distanceMeasurement": "Jarak", "rectangularAreaMeasurement": "<PERSON><PERSON>", "ellipseMeasurement": "<PERSON><PERSON><PERSON>", "countMeasurement": "Hitung pengukuran", "ellipse": "Elips", "eraser": "<PERSON><PERSON><PERSON><PERSON>", "fileattachment": "Lampiran File", "freehand": "<PERSON><PERSON>", "freeHandHighlight": "<PERSON><PERSON><PERSON>", "freetext": "<PERSON><PERSON>", "markInsertText": "Sisipkan Teks", "markReplaceText": "Ganti Teks", "highlight": "<PERSON><PERSON><PERSON>", "image": "Gambar", "line": "<PERSON><PERSON>", "perimeterMeasurement": "keliling", "polygon": "Poligon", "polygonCloud": "<PERSON><PERSON>", "polyline": "garis poli", "rectangle": "Empat persegi panjang", "redact": "<PERSON><PERSON><PERSON>", "formFillDot": "Dot", "signature": "<PERSON>da tangan", "snipping": "Alat Potong", "squiggly": "berlekuk-lekuk", "stamp": "Stempel", "stickyNote": "Catatan", "strikeout": "Menyerang", "underline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "rubberStamp": "Cap", "note": "Catatan", "textField": "Bidang Teks", "signatureFormField": "Bidang Tanda Tang<PERSON>", "checkBoxFormField": "Bidang Kotak Centang", "radioButtonFormField": "Bidang Tombol Radio", "listBoxFormField": "Bidang Kotak Daftar", "comboBoxFormField": "Bidang Kotak Kombo", "link": "Tautan", "other": "<PERSON><PERSON><PERSON>", "3D": "3D", "sound": "<PERSON><PERSON>", "changeView": "Ubah Tampilan", "newImage": "Gambar baru", "defaultCustomStampTitle": "Stempel Kustom"}, "rubberStamp": {"Approved": "Disetuju<PERSON>", "AsIs": "<PERSON><PERSON>", "Completed": "Lengka<PERSON>", "Confidential": "<PERSON><PERSON><PERSON>", "Departmental": "departemen", "Draft": "<PERSON><PERSON>", "Experimental": "Eksperimental", "Expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Final": "<PERSON><PERSON><PERSON>", "ForComment": "Untuk Komentar", "ForPublicRelease": "Untuk Rilis Publik", "InformationOnly": "Informasi Saja", "NotApproved": "Tidak disetujui", "NotForPublicRelease": "Bukan Untuk Rilis Publik", "PreliminaryResults": "<PERSON><PERSON>", "Sold": "<PERSON><PERSON><PERSON><PERSON>", "TopSecret": "Sangat rahasia", "Void": "Kosong", "SHSignHere": "<PERSON><PERSON><PERSON>", "SHWitness": "<PERSON><PERSON><PERSON>", "SHInitialHere": "<PERSON><PERSON><PERSON>i", "SHAccepted": "Diterima", "SBRejected": "<PERSON><PERSON><PERSON>"}, "component": {"attachmentPanel": "<PERSON><PERSON><PERSON>", "leftPanel": "<PERSON> Kiri", "toolsHeader": "Peralatan", "searchOverlay": "<PERSON><PERSON><PERSON>", "searchPanel": "<PERSON><PERSON><PERSON>", "menuOverlay": "Tidak bisa", "notesPanel": "Komentar", "indexPanel": "Panel Indeks", "outlinePanel": "<PERSON><PERSON> besar", "outlinesPanel": "<PERSON><PERSON> besar", "newOutlineTitle": "<PERSON><PERSON><PERSON>", "outlineTitle": "<PERSON><PERSON><PERSON>", "destination": "<PERSON><PERSON><PERSON>", "bookmarkPanel": "<PERSON><PERSON> buku", "bookmarksPanel": "Bookmark", "bookmarkTitle": "Judul <PERSON>mark", "bookmarkPage": "<PERSON><PERSON>", "signaturePanel": "<PERSON>da tangan", "layersPanel": "lapisan", "thumbnailsPanel": "Gambar kecil", "toolsButton": "Peralatan", "redaction": "<PERSON><PERSON><PERSON>", "viewControls": "<PERSON><PERSON>", "pageControls": "<PERSON><PERSON><PERSON>", "calibration": "<PERSON><PERSON><PERSON>", "zoomOverlay": "Tampilan Zoom", "textPopup": "Mu<PERSON>ulan <PERSON>", "createStampButton": "Buat Stempel Baru", "filter": "Saring", "multiSelectButton": "<PERSON><PERSON><PERSON>", "pageReplaceModalTitle": "<PERSON><PERSON>", "files": "File", "file": "Mengajukan", "editText": "<PERSON>", "redactionPanel": "Panel Redaksi", "tabLabel": "<PERSON>n tulis", "noteGroupSection": {"open": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "comparePanel": "Bandingkan Panel", "watermarkPanel": "Panel Tanda Air", "mainMenu": "Main Menu"}, "message": {"showMore": "Menampilkan lebih banyak", "showLess": "<PERSON><PERSON><PERSON><PERSON>", "toolsOverlayNoPresets": "Tidak Ada Preset", "badDocument": "Gagal memuat dokumen. Dokumen tersebut rusak atau tidak valid.", "customPrintPlaceholder": "misalnya 3, 4-10", "encryptedAttemptsExceeded": "Gagal memuat dokumen terenkripsi. Terlalu banyak upaya.", "encryptedUserCancelled": "Gagal memuat dokumen terenkripsi. Entri kata sandi dibatalkan.", "enterPassword": "Do<PERSON>men ini dilindungi kata sandi. <PERSON><PERSON><PERSON> masukkan kata sandi", "incorrectPassword": "<PERSON><PERSON>, upaya tersisa: {{ residualAttempts }}", "noAnnotations": "<PERSON><PERSON><PERSON> membuat anotasi untuk meninggalkan komentar.", "noAnnotationsReadOnly": "Dokumen ini tidak memiliki anotasi.", "noAnnotationsFilter": "<PERSON><PERSON> membuat anotasi dan filter akan muncul di sini.", "noBookmarks": "Tidak ada bookmark yang tersedia", "noOutlines": "Dokumen ini tidak memiliki garis besar.", "noAttachments": "Dokumen ini tidak memiliki lampiran.", "noResults": "Tidak ada hasil yang di<PERSON>n.", "numResultsFound": "<PERSON>il di<PERSON>n", "loadError": "<PERSON><PERSON><PERSON><PERSON>at Memuat Dokumen", "notSupported": "Tipe file tidak didukung.", "passwordRequired": "<PERSON>a sandi <PERSON>an", "enterPasswordPlaceholder": "<PERSON><PERSON><PERSON>n kata kunci", "preparingToPrint": "Bersiap untuk mencetak...", "annotationReplyCount": "{{hitung}} <PERSON><PERSON>", "annotationReplyCount_plural": "{{hitung}} <PERSON><PERSON><PERSON>", "printTotalPageCount": "Total: {{count}} halaman", "printTotalPageCount_plural": "Total: {{count}} halaman", "processing": "<PERSON><PERSON><PERSON>...", "searching": "<PERSON><PERSON><PERSON>...", "searchCommentsPlaceholder": "<PERSON><PERSON> komentar", "searchDocumentPlaceholder": "Cari dokumen", "searchSettingsPlaceholder": "Pengaturan pencarian", "searchSuggestionsPlaceholder": "<PERSON><PERSON>ian", "signHere": "Tanda tangan di sini", "insertTextHere": "Sisipkan teks di sini", "imageSignatureAcceptedFileTypes": "<PERSON>ya {{acceptedFileTypes}} yang diterima", "signatureRequired": "Tanda tangan dan inisial diperlukan untuk melanjutkan", "enterMeasurement": "<PERSON><PERSON><PERSON><PERSON> pengukuran antara dua titik", "errorEnterMeasurement": "<PERSON><PERSON> yang <PERSON>a ma<PERSON>kkan tidak valid, <PERSON><PERSON> dapat memasukkan nilai seperti 7,5 atau 7 1/2", "linkURLorPage": "Tautan URL atau Halaman", "warning": "Peringatan", "svgMalicious": "Skrip SVG diabaikan demi keamanan", "doNotShowAgain": "<PERSON><PERSON> tunjukkan ini lagi", "doNotAskAgain": "<PERSON>an tanya lagi", "enterReplacementText": "<PERSON><PERSON><PERSON><PERSON> teks yang ingin Anda ganti", "sort": "Menyortir", "sortBy": "Menyortir", "emptyCustomStampInput": "Teks stempel tidak boleh kosong", "unpostedComment": "Komentar yang Belum Diposkan", "lockedLayer": "<PERSON><PERSON><PERSON> terk<PERSON>", "layerVisibililtyNoChange": "Visibilitas lapisan tidak dapat diubah", "noLayers": "Dokumen ini tidak memiliki lapisan.", "noSignatureFields": "Dokumen ini tidak memiliki bidang tanda tangan.", "untitled": "<PERSON><PERSON>", "selectHowToLoadFile": "<PERSON><PERSON>h cara memuat dokumen <PERSON>a", "openFileByUrl": "Buka file dengan URL:", "enterUrlHere": "Masukkan URL di sini", "openLocalFile": "Buka file lokal:", "selectFile": "Pilih file", "selectPageToReplace": "<PERSON><PERSON><PERSON> halaman dalam dokumen yang ingin <PERSON>a ganti.", "embeddedFiles": "File Tertanam", "pageNum": "<PERSON><PERSON>", "viewBookmark": "Lihat Bookmark di Halaman", "error": "<PERSON><PERSON><PERSON>", "errorPageNumber": "Nomor halaman tidak valid. Batasnya adalah", "errorBlankPageNumber": "<PERSON><PERSON>ukan nomor halaman", "errorLoadingDocument": "Ada masalah saat membaca dokumen ini dan beberapa halaman mungkin tidak ditampilkan. Ini menunjukkan dokumen mungkin rusak. <PERSON><PERSON><PERSON> halaman total adalah {{totalPageCount}} dan jumlah halaman yang ditampilkan adalah {{displayedPageCount}}.", "noRevisions": "Dokumen ini tidak memiliki revisi."}, "option": {"type": {"caret": "kekurangan", "custom": "<PERSON><PERSON><PERSON><PERSON>", "ellipse": "Elips", "fileattachment": "Lampiran File", "freehand": "<PERSON><PERSON>", "callout": "Memanggil", "freetext": "<PERSON><PERSON>", "line": "<PERSON><PERSON>", "polygon": "Poligon", "polyline": "garis poli", "rectangle": "Empat persegi panjang", "redact": "<PERSON><PERSON><PERSON>", "signature": "<PERSON>da tangan", "stamp": "Stempel", "stickyNote": "<PERSON>esan tempel", "highlight": "<PERSON><PERSON><PERSON>", "strikeout": "Menyerang", "underline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "squiggly": "berlekuk-lekuk", "3D": "3D", "other": "<PERSON><PERSON><PERSON>", "initials": "Inisial", "saved": "Diselamatkan"}, "notesOrder": {"dropdownLabel": "<PERSON><PERSON><PERSON><PERSON>", "position": "<PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON><PERSON>", "status": "Status", "author": "Pengarang", "type": "<PERSON><PERSON>", "color": "<PERSON><PERSON>", "createdDate": "Tanggal Dibuat", "modifiedDate": "<PERSON><PERSON> yang diubah"}, "toolbarGroup": {"dropdownLabel": "Grup Toolbar", "flyoutLabel": "Pita", "toolbarGroup-View": "Melihat", "toolbarGroup-Annotate": "Memb<PERSON>uhi keterangan", "toolbarGroup-Shapes": "Bentuk", "toolbarGroup-Insert": "Memasukkan", "toolbarGroup-Measure": "Ukuran", "toolbarGroup-Edit": "Sunting", "toolbarGroup-EditText": "<PERSON>", "toolbarGroup-FillAndSign": "<PERSON><PERSON> dan <PERSON>", "toolbarGroup-Forms": "<PERSON><PERSON><PERSON>", "toolbarGroup-Redact": "<PERSON><PERSON><PERSON>", "toolbarGroup-oe-Home": "<PERSON><PERSON><PERSON>", "toolbarGroup-oe-Insert": "Menyisip<PERSON>", "toolbarGroup-oe-Review": "Tinjauan"}, "annotationColor": {"StrokeColor": "Stroke", "FillColor": "<PERSON><PERSON><PERSON>", "TextColor": "Teks"}, "colorPalette": {"colorLabel": "<PERSON><PERSON>"}, "colorPalettePicker": {"addColor": "Tambahkan warna baru", "selectColor": "<PERSON><PERSON><PERSON> warna"}, "displayMode": {"layout": "<PERSON>ta letak halaman", "pageTransition": "Transisi <PERSON>"}, "documentControls": {"selectTooltip": "<PERSON><PERSON><PERSON> be<PERSON> halaman", "closeTooltip": "<PERSON><PERSON><PERSON>"}, "bookmarkOutlineControls": {"edit": "Sunting", "done": "Se<PERSON><PERSON>", "reorder": "<PERSON><PERSON>"}, "layout": {"cover": "Halaman Muka <PERSON>", "double": "<PERSON><PERSON>", "single": "Halaman tunggal"}, "mathSymbols": "Simbol matematika", "notesPanel": {"separator": {"today": "<PERSON> ini", "yesterday": "<PERSON><PERSON><PERSON>", "unknown": "Tidak dikenal"}, "noteContent": {"noName": "(tanpa nama)", "noDate": "(tidak ada tanggal)"}, "toggleMultiSelect": "Alihkan Multi Pilih untuk Anotasi"}, "pageTransition": {"continuous": "Halaman Berkelanju<PERSON>", "default": "Halaman demi <PERSON>", "reader": "Pembaca"}, "print": {"all": "<PERSON><PERSON><PERSON>", "current": "Halaman saat ini", "pages": "Halaman untuk dicetak", "specifyPages": "<PERSON><PERSON><PERSON>", "view": "Pandangan saat ini", "pageQuality": "Kualitas cetak", "qualityNormal": "Normal", "qualityHigh": "Tingg<PERSON>", "includeAnnotations": "Sertakan anota<PERSON>", "includeComments": "Sertakan komentar", "printSettings": "Pengaturan Cetak", "printGrayscale": "Cetak Skala abu-abu", "printCurrentDisabled": "<PERSON><PERSON><PERSON> saat ini hanya tersedia saat melihat satu halaman."}, "printInfo": {"author": "Pengarang", "subject": "Subjek", "date": "Tanggal"}, "redaction": {"markForRedaction": "Tandai untuk redaksi"}, "searchPanel": {"caseSensitive": "<PERSON> - hal sensitif", "wholeWordOnly": "<PERSON><PERSON><PERSON><PERSON> kata", "wildcard": "Wildcard", "replace": "Mengganti", "replaceAll": "Menggantikan semua", "replaceText": "Ganti Teks", "confirmMessageReplaceAll": "Anda yakin ingin mengganti semua teks?", "confirmMessageReplaceOne": "Anda yakin ingin mengganti teks ini?", "moreOptions": "<PERSON><PERSON>h banyak pilihan", "lessOptions": "<PERSON><PERSON><PERSON> sedikit pilihan", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "toolsOverlay": {"currentStamp": "Stempel saat ini", "currentSignature": "Tanda Tangan Sa<PERSON> In<PERSON>", "signatureAltText": "<PERSON>da tangan"}, "stampOverlay": {"addStamp": "Tambahkan Stempel Baru"}, "signatureOverlay": {"addSignature": "Tambahkan Tanda Tangan Baru", "addSignatureOrInitials": "Tanda tangan/Inisial"}, "signatureModal": {"modalName": "Buat Tanda Tangan Baru", "dragAndDrop": "Seret & Jatuhkan gambar Anda di sini", "or": "<PERSON><PERSON>", "pickImage": "<PERSON><PERSON><PERSON> tanda tangan", "selectImage": "<PERSON><PERSON><PERSON> gambar <PERSON> di sini", "typeSignature": "Ketik <PERSON>*", "typeInitial": "Ketik Inisial*", "drawSignature": "<PERSON><PERSON><PERSON>*", "drawInitial": "Gambar Inisial*", "imageSignature": "Tanda Tangan Gambar", "imageInitial": "Inisial Gambar", "pickInitialsFile": "<PERSON><PERSON><PERSON>", "noSignatures": "Saat ini tidak ada tanda tangan yang disimpan.", "fontStyle": "gaya tulisan", "textSignature": {"dropdownLabel": "<PERSON><PERSON><PERSON><PERSON>"}}, "pageReplacementModal": {"yourFiles": "File <PERSON>a", "chooseFile": "Pilih file", "localFile": "<PERSON> Lokal", "pageReplaceInputLabel": "<PERSON><PERSON>", "pageReplaceInputFromSource": "<PERSON><PERSON> halaman", "warning": {"title": "<PERSON><PERSON><PERSON> dari halaman ganti?", "message": "<PERSON><PERSON><PERSON> akan membatalkan semua pilihan yang telah Anda buat sejauh ini. <PERSON><PERSON><PERSON>h Anda yakin masih ingin keluar?"}}, "filterAnnotModal": {"color": "<PERSON><PERSON>", "includeReplies": "<PERSON><PERSON><PERSON>", "filters": "Filter", "user": "Pengguna", "type": "<PERSON><PERSON>", "status": "Status", "filterSettings": "<PERSON><PERSON><PERSON><PERSON>lter", "filterDocument": "Filter dokumen dan panel komentar"}, "state": {"accepted": "Diterima", "rejected": "<PERSON><PERSON><PERSON>", "completed": "Lengka<PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "set": "Tetapkan status:", "setBy": "ditetapkan oleh", "none": "Tidak ada", "marked": "Ditandai", "unmarked": "Tidak bertanda"}, "measurementOverlay": {"scale": "<PERSON><PERSON>", "angle": "<PERSON><PERSON><PERSON>", "distance": "Jarak", "perimeter": "keliling", "area": "<PERSON><PERSON><PERSON>", "distanceMeasurement": "Pengukuran Jarak", "perimeterMeasurement": "Pengu<PERSON>n keliling", "arcMeasurement": "Pengukuran busur", "areaMeasurement": "Pengukuran Area", "countMeasurement": "Hitung pengukuran", "radius": "<PERSON><PERSON>", "count": "Menghitung", "length": "Panjang", "xAxis": "Sumbu X", "yAxis": "Sumbu Y"}, "freeTextOption": {"autoSizeFont": "Skala ukuran font secara dinamis"}, "measurementOption": {"scale": "<PERSON><PERSON><PERSON>", "selectScale": "<PERSON><PERSON><PERSON>", "selectScaleDropdown": "<PERSON><PERSON><PERSON> Dropdown"}, "measurement": {"scaleModal": {"calibrate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "fractionalUnits": "<PERSON><PERSON><PERSON>", "precision": "presisi", "preset": "Prasetel", "paperUnits": "Unit Kertas", "displayUnits": "Unit Tampilan", "fractionUnitsTooltip": "Satuan pecahan hanya berlaku untuk in dan ft-in", "incorrectSyntax": "Sintaks salah", "units": "Satuan"}, "scaleOverlay": {"addNewScale": "Tambahkan Skala Baru", "selectTwoPoints": "<PERSON><PERSON>h dua titik dari dimensi yang diketahui untuk dikalibrasi", "inputKnowDimension": "Masukkan dimensi dan unit yang diketahui untuk dikalibrasi", "multipleScales": "Beber<PERSON>"}, "deleteScaleModal": {"deleteScale": "<PERSON><PERSON>", "scaleIsOn-delete-info": "Skala ini sedang digunakan pada", "page-delete-info": "halaman", "appliedTo-delete-info": "dan di<PERSON><PERSON><PERSON> pada", "measurement": "<PERSON><PERSON><PERSON><PERSON>", "measurements": "<PERSON><PERSON><PERSON><PERSON>", "deletionIs": "Penghapusan adalah", "irreversible": "ireversibel", "willDeleteMeasurement": "dan akan menghapus pengukuran terkait.", "confirmDelete": "Anda yakin ingin menghapus skala ini?", "thisCantBeUndone": " Hal ini tidak dapat di<PERSON>.", "ifChangeScale": "Jika Anda mengubah skala untuk pengukuran atau alat yang dipilih, skala", "notUsedWillDelete": " tidak akan lagi digunakan oleh pengukuran atau alat apa pun dan akan dihapus. Penghapusan tidak dapat diubah.", "ifToContinue": "Anda yakin ingin melanjutkan?"}}, "contentEdit": {"deletionModal": {"title": "Hapus Konten", "message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus konten yang dipilih? Ini tidak dapat diurungkan."}, "digitalSign": {"title": "<PERSON><PERSON><PERSON>", "message": "Dokumen ini telah ditandatangani dan tidak dapat diubah atau diubah."}}, "stylePopup": {"textStyle": "<PERSON><PERSON>", "colors": "warna", "invalidFontSize": "Ukuran font harus berada dalam kisaran berikut", "labelText": "Teks Label", "labelTextPlaceholder": "Tambahkan teks label"}, "styleOption": {"style": "<PERSON><PERSON>", "solid": "Pa<PERSON>t", "cloudy": "<PERSON><PERSON><PERSON>"}, "slider": {"opacity": "Kegelapan", "thickness": "Stroke", "text": "Ukuran teks"}, "shared": {"page": "<PERSON><PERSON>", "precision": "presisi", "enableSnapping": "Aktifkan gertakan untuk alat pengukuran"}, "watermark": {"title": "tanda air", "addWatermark": "Tambahkan Tanda Air", "size": "Ukuran", "location": "Pilih lokasi untuk mengedit tanda air", "text": "Teks", "style": "<PERSON><PERSON>", "resetAllSettings": "At<PERSON> U<PERSON>", "font": "Membuat", "addNew": "Tambah baru", "locations": {"center": "Tengah", "topLeft": "<PERSON><PERSON>", "topRight": "<PERSON><PERSON> atas", "topCenter": "tengah atas", "bottomLeft": "<PERSON><PERSON> bawah", "bottomRight": "<PERSON><PERSON> bawah", "bottomCenter": "<PERSON><PERSON><PERSON> bawah"}}, "thumbnailPanel": {"delete": "<PERSON><PERSON><PERSON><PERSON>", "rotateClockwise": "Searah jarum jam", "rotateCounterClockwise": "Berlawanan arah jarum jam", "rotatePageClockwise": "Putar Halaman Searah Jarum Jam", "rotatePageCounterClockwise": "<PERSON><PERSON>", "moreOptions": "<PERSON><PERSON>h banyak pilihan", "moreOptionsMenu": "<PERSON><PERSON>", "enterPageNumbers": "<PERSON><PERSON><PERSON>n nomor halaman untuk dipilih", "multiSelectPages": "Halaman Multi-Pilihan", "multiSelectPagesExample": "misalnya 1, 3, 5-10"}, "thumbnailsControlOverlay": {"move": "Pi<PERSON><PERSON><PERSON> halaman"}, "richText": {"bold": "Mencolok", "italic": "miring", "underline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strikeout": "Menyerang", "alignLeft": "Teks rata kiri", "alignRight": "Teks rata kanan", "alignCenter": "Pusat perataan teks", "justifyCenter": "Pusat pembenaran teks", "alignTop": "Rata atas", "alignMiddle": "Rata tengah", "alignBottom": "<PERSON>a bawah"}, "customStampModal": {"modalName": "Buat Stempel Baru", "stampText": "teks perangko", "timestampText": "Teks stempel waktu", "Username": "<PERSON><PERSON>", "Date": "Tanggal", "Time": "<PERSON><PERSON><PERSON>", "fontStyle": "gaya tulisan", "dateFormat": "Format tanggal", "month": "<PERSON><PERSON><PERSON>", "day": "<PERSON>", "year": "<PERSON><PERSON>", "hour": "Jam", "minute": "Menit", "second": "Kedua", "textColor": "Warna teks", "backgroundColor": "<PERSON><PERSON> latar belakang", "dateToolTipLabel": "Info lebih lanjut tentang format tanggal", "previewCustomStamp": "<PERSON><PERSON><PERSON><PERSON><PERSON> dari"}, "pageRedactModal": {"addMark": "Tambahkan Tandai", "pageSelection": "<PERSON><PERSON><PERSON>", "current": "Halaman saat ini", "specify": "<PERSON><PERSON><PERSON>", "odd": "<PERSON><PERSON> ganjil saja", "even": "<PERSON><PERSON> halaman yang genap"}, "lineStyleOptions": {"title": "<PERSON><PERSON>"}, "settings": {"settings": "<PERSON><PERSON><PERSON><PERSON>", "searchSettings": "Pengaturan pencarian", "general": "<PERSON><PERSON>", "language": "Bahasa", "theme": "<PERSON><PERSON>", "darkMode": "Mode gelap", "lightMode": "Mode cahaya", "advancedSetting": "Pen<PERSON><PERSON><PERSON>", "viewing": "Melihat", "disableFadePageNavigationComponent": "Nonaktifkan Komponen Navigasi Hal<PERSON>", "disableFadePageNavigationComponentDesc": "Selalu simpan Komponen Navigasi Hal<PERSON> di layar. Perilaku default adalah memudarnya setelah periode tidak aktif tertentu.", "disableNativeScrolling": "Nonaktifkan <PERSON>", "disableNativeScrollingDesc": "Nonaktifkan perilaku pengguliran perangkat seluler asli jika sebelumnya telah diaktifkan. Perhatikan bahwa perilaku pengguliran perangkat seluler asli dinonaktifkan secara default.", "annotations": "<PERSON><PERSON><PERSON>", "disableToolDefaultStyleUpdateFromAnnotationPopup": "Nonaktifkan Pembaruan Gaya Default Alat Dari Popup Anotasi", "disableToolDefaultStyleUpdateFromAnnotationPopupDesc": "Menonaktifkan sinkronisasi pembaruan gaya anotasi ke alat terkait yang membuat anotasi. <PERSON><PERSON>, jika gaya anotasi diubah, gaya default alat tidak akan diperbarui.", "notesPanel": "Panel Catatan", "disableNoteSubmissionWithEnter": "Nonaktifkan Pengiriman Catatan Dengan Enter", "disableNoteSubmissionWithEnterDesc": "Nonaktifkan kemampuan untuk mengirimkan catatan dengan hanya menekan Enter jika sebelumnya telah diaktifkan. Ini akan mengembalikan pengiriman catatan ke default yaitu Ctrl/Cmd + Enter.", "disableAutoExpandCommentThread": "Nonaktifkan Otomatis <PERSON> Koment<PERSON>", "disableAutoExpandCommentThreadDesc": "Menonaktifkan perluasan otomatis semua utas komentar di Panel Catatan.", "disableReplyCollapse": "Nonaktifkan Balasan <PERSON>", "disableReplyCollapseDesc": "Menonaktifkan penciutan balasan di Panel Catatan.", "disableTextCollapse": "Nonaktifkan Ciutkan Teks", "disableTextCollapseDesc": "Menonaktifkan penciutan teks anotasi di Panel Catatan.", "search": "<PERSON><PERSON><PERSON>", "disableClearSearchOnPanelClose": "Nonaktifkan Hapus Pencarian Pada Tutup Panel", "disableClearSearchOnPanelCloseDesc": "Nonaktifkan menghapus hasil pencarian saat pengguna menutup panel pencarian. Saat dinonaktifkan, hasil pencarian disimpan meskipun pengguna menutup dan membuka kembali panel pencarian. <PERSON><PERSON><PERSON><PERSON>, perangkat seluler tidak pernah menghapus hasil penelusuran meskipun setelan ini diaktifkan. Ini karena panel perlu ditutup untuk melihat hasil pencarian pada dokumen.", "pageManipulation": "<PERSON><PERSON><PERSON><PERSON>", "disablePageDeletionConfirmationModal": "Nonaktifkan Modal Konfirmasi Penghapusan Halaman", "disablePageDeletionConfirmationModalDesc": "Nonaktifkan modal konfirmasi saat menghapus halaman dari tampilan thumbnail", "disableMultiselect": "Nonaktifkan <PERSON>", "disableMultiselectDesc": "Nonaktifkan multi pilih di panel gambar mini kiri", "miscellaneous": "<PERSON><PERSON> ragam", "keyboardShortcut": "Pintasan Keyboard", "command": "Memerintah", "description": "Keterangan", "action": "<PERSON><PERSON><PERSON>", "rotateDocumentClockwise": "Putar dokumen searah jarum jam", "rotateDocumentCounterclockwise": "Putar dokumen berlawanan arah jarum jam", "copyText": "<PERSON>in teks atau anotasi yang dipilih", "pasteText": "Tempel teks atau anotasi", "undoChange": "<PERSON>rung<PERSON> per<PERSON>han anotasi", "redoChange": "<PERSON><PERSON><PERSON> per<PERSON> anotasi", "openFile": "<PERSON><PERSON> pemilih file", "openSearch": "Buka hamparan pencarian", "zoomOptions": "Opsi Zoom", "zoomIn": "<PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "setHeaderFocus": "<PERSON><PERSON><PERSON> fokus ke header", "fitScreenWidth": "Sesuaikan dokumen dengan lebar layar di layar kecil (<640px), jika tidak sesuaikan dengan ukuran aslinya", "print": "Mencetak", "bookmarkOpenPanel": "Tandai halaman dengan cepat dan buka panel bookmark", "goToPreviousPage": "<PERSON>gi ke halaman sebelumnya", "goToNextPage": "<PERSON>gi ke halaman berikutnya", "goToPreviousPageArrowUp": "<PERSON><PERSON> halaman sebelumnya dalam mode tata letak tunggal (ArrowUp)", "goToNextPageArrowDown": "<PERSON>uka halaman berikutnya dalam mode tata letak tunggal (Panah Bawah)", "holdSwitchPan": "<PERSON>han untuk beralih ke mode Pan dan lepaskan untuk kembali ke alat sebelumnya", "selectAnnotationEdit": "Pilih alat AnnotationEdit", "selectPan": "<PERSON><PERSON><PERSON> al<PERSON>", "selectCreateArrowTool": "Pilih alat AnnotationCreateArrow", "selectCreateCalloutTool": "Pilih alat AnnotationCreateCallout", "selectEraserTool": "<PERSON><PERSON>h alat AnnotationEraser", "selectCreateFreeHandTool": "Pilih alat AnnotationCreateFreeHand", "selectCreateStampTool": "Pilih alat AnnotationCreateStamp", "selectCreateLineTool": "Pilih alat AnnotationCreateLine", "selectCreateStickyTool": "Pilih alat AnnotationCreateSticky", "selectCreateEllipseTool": "Pilih alat AnnotationCreateEllipse", "selectCreateRectangleTool": "Pilih alat AnnotationCreateRectangle", "selectCreateRubberStampTool": "Pilih alat AnnotationCreateRubberStamp", "selectCreateFreeTextTool": "Pilih alat AnnotationCreateFreeText", "openSignatureModal": "Buka modal tanda tangan atau overlay", "selectCreateTextSquigglyTool": "Pilih alat AnnotationCreateTextSquiggly", "selectCreateTextHighlightTool": "Pilih alat AnnotationCreateTextHighlight", "selectCreateTextStrikeoutTool": "Pilih alat AnnotationCreateTextStrikeout", "selectCreateTextUnderlineTool": "Pilih alat AnnotationCreateTextUnderline", "editKeyboardShorcut": "<PERSON> <PERSON><PERSON><PERSON> Keyboard", "setShortcut": "<PERSON><PERSON>", "editShortcut": "<PERSON>", "shortcutAlreadyExists": "Pintasan keyboard di atas sudah ada.", "close": "Tutup keterangan alat"}}, "warning": {"deletePage": {"deleteTitle": "<PERSON><PERSON>", "deleteMessage": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus halaman yang dipilih? Hal ini tidak dapat dibatalkan.", "deleteLastPageMessage": "<PERSON>a tidak dapat menghapus semua halaman dalam dokumen."}, "extractPage": {"title": "Ekstrak Halaman", "message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin mengekstrak halaman yang dipilih?", "confirmBtn": "Ekstrak", "secondaryBtn": "Ekstrak dan <PERSON>"}, "redaction": {"applyTile": "<PERSON><PERSON><PERSON>", "applyMessage": "Tindakan ini akan menghapus secara permanen semua item yang dipilih untuk diedit. Itu tidak bisa dibatalkan."}, "deleteBookmark": {"title": "<PERSON><PERSON>?", "message": "Yakin ingin menghapus bookmark ini? Anda tidak dapat membatalkan tindakan ini."}, "deleteOutline": {"title": "<PERSON><PERSON>?", "message": "Anda yakin ingin menghapus garis besar ini?\n\nMenghapus garis besar yang memiliki garis besar bersarang akan mengakibatkan seluruh struktur bagian dalam dihapus dan, jika perlu, harus dibuat ulang."}, "selectPage": {"selectTitle": "Tidak Ada Halaman yang <PERSON>", "selectMessage": "<PERSON><PERSON> pilih halaman dan coba lagi."}, "colorPicker": {"deleteTitle": "<PERSON><PERSON> <PERSON> k<PERSON>", "deleteMessage": "<PERSON>pus warna khusus yang dipilih? Ini akan dihapus dari palet warna Anda."}, "colorPalettePicker": {"deleteTitle": "<PERSON><PERSON> <PERSON> k<PERSON>"}, "multiDeleteAnnotation": {"title": "<PERSON><PERSON>?", "message": "<PERSON>gh<PERSON>us akan menghapus semua kome<PERSON>, b<PERSON><PERSON>, dan pengelompokan dan tidak dapat diurungkan.\n\n Anda yakin ingin menghapus anotasi ini?"}, "closeFile": {"title": "Tutup tanpa mengunduh?", "message": "<PERSON> perubahan yang dibuat pada dokumen ini, apa<PERSON>h Anda yakin ingin menutupnya tanpa mengunduh pekerjaan <PERSON>a? Anda tidak dapat mengurungkan tindakan ini.", "rejectDownloadButton": "<PERSON><PERSON><PERSON> tanpa unduh"}, "connectToURL": {"title": "<PERSON><PERSON><PERSON> keamanan", "message": "Dokumen ini mencoba terhubung ke:\n\n{{- uri}}\n\n Ji<PERSON> Anda mempercayai dokumen ini, klik Konfirmasi untuk membukanya."}, "sheetTabRenameIssueOne": {"title": "<PERSON>", "message": "<PERSON>a lembar ini sudah ada. <PERSON><PERSON><PERSON> masukkan nama lain."}, "sheetTabRenameIssueTwo": {"title": "<PERSON>", "message": "<PERSON>a lembar ini tidak boleh kosong."}, "sheetTabDeleteMessage": {"title": "<PERSON>", "message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus lembar ini?"}}, "shortcut": {"arrow": "(SEBUAH)", "callout": "(C)", "copy": "(Ctrl C)", "delete": "(Itu)", "ellipse": "(HAI)", "eraser": "(DAN)", "freehand": "(F)", "freetext": "(T)", "highlight": "(H)", "line": "(SAYA)", "pan": "(P)", "rectangle": "(R)", "rotateClockwise": "(Ctrl Shift +)", "rotateCounterClockwise": "(Pergeseran Ctrl-)", "select": "(ESC)", "signature": "(S)", "squiggly": "(G)", "image": "(I)", "redo": "(Ctrl Shift Z)", "redo_windows": "(Ctrl Y)", "undo": "(Ctrl Z)", "stickyNote": "(N)", "strikeout": "(KE)", "underline": "(U)", "zoomIn": "(Ctrl +)", "zoomOut": "(Ctrl-)", "richText": {"bold": "(Ctrl B)", "italic": "(Ctrl I)", "underline": "(Ctrl U)", "strikeout": "(Ctrl K)"}, "rotate3D": "Shift + Seret", "zoom3D": "Shift + Gulir"}, "tool": {"pan": "<PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "selectAnOption": "<PERSON><PERSON><PERSON> sebuah opsi", "Marquee": "Zoom <PERSON>", "Link": "Tautan URL atau Halaman", "Standard": "<PERSON>ar", "Custom": "<PERSON><PERSON><PERSON><PERSON>"}, "link": {"url": "URL", "page": "<PERSON><PERSON>", "enterurl": "Masukkan URL yang ingin Anda <PERSON>kan", "enterUrlAlt": "Masukkan URL", "insertLink": "Sisipkan <PERSON>", "insertLinkOrPage": "Sisipkan Tautan atau Halaman", "enterpage": "<PERSON><PERSON><PERSON>n nomor halaman yang ingin <PERSON>a <PERSON>kan", "urlLink": "Tautan URL"}, "Model3D": {"add3D": "Tambahkan objek 3D", "enterurl": "Masukkan URL objek 3D dalam format glTF", "enterurlOrLocalFile": "Masukkan URL atau unggah objek 3D dalam format glTF", "formatError": "Hanya format glTF (.glb) yang didukung"}, "OpenFile": {"enterUrlOrChooseFile": "Masukkan URL atau pilih file untuk dimuat ke WebViewer", "enterUrl": "Masukkan URL berkas", "extension": "Ekstensi File", "existingFile": "File sudah terbuka", "addTab": "Tambahkan Tab", "newTab": "Tab baru"}, "datePicker": {"previousMonth": "<PERSON>ulan sebel<PERSON>", "nextMonth": "<PERSON><PERSON><PERSON> depan", "months": {"0": "<PERSON><PERSON><PERSON>", "1": "<PERSON><PERSON><PERSON>", "2": "berb<PERSON>", "3": "April", "4": "<PERSON><PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "<PERSON><PERSON>", "7": "<PERSON><PERSON><PERSON>", "8": "September", "9": "Oktober", "10": "November", "11": "Desember"}, "monthsShort": {"0": "Jan", "1": "<PERSON><PERSON><PERSON>", "2": "<PERSON><PERSON>", "3": "April", "4": "<PERSON><PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "<PERSON><PERSON>", "7": "<PERSON><PERSON><PERSON>", "8": "<PERSON><PERSON><PERSON>", "9": "Oktober", "10": "November", "11": "Desember"}, "weekdays": {"0": "minggu", "1": "<PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "Sabtu"}, "weekdaysShort": {"0": "<PERSON><PERSON><PERSON>", "1": "Ku", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "<PERSON><PERSON><PERSON>"}, "today": "<PERSON> ini", "invalidDateTime": "Tanggal/Waktu Tidak Valid: Input harus sesuai dengan format"}, "formField": {"indexPanel": {"formFieldList": "Daftar Bidang Formulir", "notFields": "Dokumen ini tidak memiliki bidang formulir"}, "formFieldPopup": {"fieldName": "<PERSON><PERSON>", "fieldValue": "<PERSON><PERSON>", "readOnly": "Baca Saja", "multiSelect": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON><PERSON>", "multiLine": "Multiline", "apply": "Berlaku", "cancel": "Membatalkan", "flags": "<PERSON><PERSON>", "fieldSize": "Ukuran <PERSON>", "options": "<PERSON><PERSON><PERSON>", "properties": "Properti", "radioGroups": "Tombol radio dengan Nama Bidang yang sama akan termasuk dalam pengelompokan yang sama.", "nameRequired": "<PERSON><PERSON> bidang wajib diisi", "fieldIndicator": "<PERSON><PERSON><PERSON><PERSON>", "documentFieldIndicator": "Indikator <PERSON> Dokumen", "includeFieldIndicator": "Sertakan Indikator <PERSON>", "indicatorPlaceHolders": {"TextFormField": "Sisipkan Teks Di Sini", "CheckBoxFormField": "Memeriksa", "RadioButtonFormField": "<PERSON><PERSON><PERSON>", "ListBoxFormField": "<PERSON><PERSON><PERSON>", "ComboBoxFormField": "<PERSON><PERSON><PERSON>", "SignatureFormField": {"fullSignature": "<PERSON><PERSON><PERSON>", "initialsSignature": "<PERSON><PERSON><PERSON>i"}}, "size": "Ukuran", "width": "<PERSON><PERSON>", "height": "Tingg<PERSON>", "invalidField": {"duplicate": "<PERSON>a <PERSON>ng sudah ada", "empty": "Nama Bidang tidak boleh kosong"}}, "formFieldPanel": {"SignatureFormField": "<PERSON><PERSON><PERSON> Tan<PERSON>", "CheckBoxFormField": "Anotasi Bidang Kotak Centang", "RadioButtonFormField": "Anotasi Bidang Tombol Radio", "ListBoxFormField": "Anotasi Bidang Kotak Daftar", "ComboBoxFormField": "Anotasi Bidang Kotak Kombo", "TextFormField": "Anotasi Bidang Teks"}, "apply": "Terapkan Bidang", "type": "<PERSON><PERSON>", "types": {"text": "Teks", "signature": "<PERSON>da tangan", "checkbox": "kotak centang", "radio": "Tombol radio", "listbox": "Kotak daftar", "combobox": "Kotak kombo", "button": "Tombol"}}, "alignmentPopup": {"alignment": "Meluruskan", "alignLeft": "Rata kiri", "alignHorizontalCenter": "Sejajarkan Pusat Horizontal", "alignVerticalCenter": "Sejajarkan Pusat Vertikal", "alignRight": "<PERSON>a kanan", "alignTop": "<PERSON><PERSON><PERSON><PERSON>", "alignBottom": "Sejajar<PERSON> Bawah", "distribute": "Mendistribusikan", "distributeHorizontal": "Distribusikan Secara Horizontal", "distributeVertical": "Distribusikan Secara Vertikal"}, "digitalSignatureModal": {"certification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Certification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signature": "tanda tangan", "Signature": "<PERSON>da tangan", "valid": "sah", "invalid": "tidak sah", "unknown": "tidak dikenal", "title": "{{type}} <PERSON><PERSON><PERSON>", "header": {"documentIntegrity": "Integritas Dokumen", "identitiesTrust": "Identitas & Kepercayaan", "generalErrors": "<PERSON><PERSON><PERSON>", "digestStatus": "Status Intisari"}, "documentPermission": {"noChangesAllowed": "{{editor}} telah meneta<PERSON>kan bahwa tidak ada perubahan yang diizinkan untuk dokumen ini", "formfillingSigningAllowed": "{{editor}} telah men<PERSON> bahwa Pen<PERSON> dan <PERSON>ulir diperbolehkan untuk dokumen ini. Tidak ada perubahan lain yang di<PERSON>inkan.", "annotatingFormfillingSigningAllowed": "{{editor}} te<PERSON> bah<PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON> diperbolehkan untuk dokumen ini. Tidak ada perubahan lain yang di<PERSON>an.", "unrestricted": "{{editor}} telah menetapkan bahwa tidak ada batasan untuk dokumen ini."}, "digestAlgorithm": {"preamble": "Algoritme intisari yang digunakan untuk menandatangani tanda tangan:", "unknown": "Algoritme intisari yang digunakan untuk menandatangani tanda tangan tidak diketahui."}, "trustVerification": {"none": "Tidak ada hasil verifikasi kepercayaan terperinci yang tersedia.", "current": "Verifikasi kepercayaan dicoba sehubungan dengan waktu saat ini", "signing": "Verifikasi kepercayaan dicoba sehubungan dengan waktu penandatanganan: {{trustVerificationTime}}", "timestamp": "Verifikasi kepercayaan dicoba sehubungan dengan stempel waktu tersemat yang aman: {{trustVerificationTime}}"}, "signerIdentity": {"preamble": "Identitas penanda tangan adalah", "valid": "sah.", "unknown": "tidak dikenal."}, "summaryBox": {"summary": "Digital {{type}} adalah {{status}}", "signedBy": ", ditandatangani oleh {{name}}"}}, "digitalSignatureVerification": {"certifier": "menyatakan", "certified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Certified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Certification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signer": "tanda", "signed": "tertanda", "Signed": "Tertanda", "Signature": "<PERSON>da tangan", "by": "oleh", "on": "di", "disallowedChange": "<PERSON><PERSON><PERSON> yang <PERSON>: {{type}}, objnum: {{objnum}}", "unsignedSignatureField": "Bidang tanda tangan yang tidak ditandatangani: {{fieldName}}", "signatureProperties": "Pro<PERSON><PERSON>", "trustVerification": {"current": "Waktu verifikasi yang digunakan adalah waktu saat ini", "signing": "W<PERSON>tu verifi<PERSON>i adalah dari jam di komputer penanda tangan", "timestamp": "Waktu verifikasi berasal dari stempel waktu aman yang disematkan di dokumen", "verifiedTrust": "<PERSON><PERSON> <PERSON>ri<PERSON><PERSON> k<PERSON>: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noTrustVerification": "Tidak ada hasil verifikasi kepercayaan terperinci yang tersedia."}, "permissionStatus": {"noPermissionsStatus": "Tidak ada status izin untuk dilaporkan.", "permissionsVerificationDisabled": "Verifikasi izin telah dinonaktifkan.", "hasAllowedChanges": "Dokumen memiliki perubahan yang diizinkan oleh pengaturan izin tanda tangan.", "invalidatedByDisallowedChanges": "Dokumen memiliki perubahan yang tidak diizinkan oleh pengaturan izin tanda tangan.", "unmodified": "<PERSON><PERSON><PERSON> belum diubah sejak itu", "unsupportedPermissionsFeatures": "<PERSON><PERSON>ukan fitur izin yang tidak didukung."}, "trustStatus": {"trustVerified": "Membangun keper<PERSON>an di {{verificationType}} dengan sukses.", "untrusted": "Kepercayaan tidak dapat dibangun.", "trustVerificationDisabled": "Verifikasi kepercayaan telah dinonaktifkan.", "noTrustStatus": "Tidak ada status kepercayaan untuk dilaporkan."}, "digestStatus": {"digestInvalid": "Intisarinya tidak benar.", "digestVerified": "<PERSON><PERSON><PERSON><PERSON> ben<PERSON>.", "digestVerificationDisabled": "Veri<PERSON><PERSON>i intisari telah dinonaktifkan.", "weakDigestAlgorithmButDigestVerifiable": "<PERSON><PERSON><PERSON> benar, tetapi algoritma intisari lemah dan tidak aman.", "noDigestStatus": "Tidak ada status intisari untuk dilaporkan.", "unsupportedEncoding": "Tidak ada SignatureHandler yang terpasang yang dapat mengenali penyandian tanda tangan", "documentHasBeenAltered": "Dokumen telah diubah atau rusak sejak ditandatangani."}, "documentStatus": {"noError": "Tidak ada kesalahan umum untuk dilaporkan.", "corruptFile": "SignatureHandler melaporkan kerusakan file.", "unsigned": "Tanda tangan belum ditandatangani secara kriptografis.", "badByteRanges": "SignatureHandler melaporkan kerusakan pada ByteRanges dalam tanda tangan digital.", "corruptCryptographicContents": "SignatureHandler melaporkan kerusakan pada Isi tanda tangan digital."}, "signatureDetails": {"signatureDetails": "Det<PERSON>", "contactInformation": "Kontak informasi", "location": "<PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "signingTime": "<PERSON><PERSON><PERSON>", "noContactInformation": "Tidak ada informasi kontak yang disediakan", "noLocation": "Tidak ada lokasi yang disediakan", "noReason": "Tidak ada alasan yang diberikan", "noSigningTime": "Tidak ada waktu penandatanganan yang ditemukan"}, "panelMessages": {"noSignatureFields": "Dokumen ini tidak memiliki bidang tanda tangan", "certificateDownloadError": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mencoba mengunduh sertifikat tepercaya", "localCertificateError": "<PERSON> beberapa masalah dengan membaca sertifikat lokal"}, "verificationStatus": {"valid": "{{verificationType}} valid.", "failed": "{{verificationType}} gagal."}}, "cropPopUp": {"title": "Halaman untuk Dipangkas", "allPages": "<PERSON><PERSON><PERSON>", "singlePage": "Halaman saat ini", "multiPage": "<PERSON><PERSON><PERSON>", "cropDimensions": "<PERSON><PERSON><PERSON>", "dimensionInput": {"unitOfMeasurement": "Satuan", "autoTrim": "<PERSON><PERSON><PERSON> otom<PERSON>", "autoTrimCustom": "<PERSON><PERSON><PERSON><PERSON>"}, "cropModal": {"applyTitle": "Terapkan pangkas?", "applyMessage": "Tindakan ini akan secara permanen memotong semua halaman terpilih yang dipilih. Itu tidak bisa dibatalkan.", "cancelTitle": "Batalkan pemang<PERSON>an?", "cancelMessage": "Anda yakin ingin membatalkan pemotongan semua halaman yang dipilih?"}}, "snippingPopUp": {"title": "Alat Potong", "clipboard": "<PERSON>in ke Clipboard", "download": "<PERSON><PERSON><PERSON>", "cropAndRemove": "<PERSON>tong dan <PERSON>", "snippingModal": {"applyTitle": "Terapkan pemotongan?", "applyMessage": "Tindakan ini akan memotong area yang ditentukan secara permanen dan menghapus halaman lainnya. Ini tidak dapat dibatalkan.", "cancelTitle": "Batal pemotongan?", "cancelMessage": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghentikan pemotongan?"}}, "textEditingPanel": {"paragraph": "<PERSON><PERSON><PERSON>", "text": "Teks"}, "redactionPanel": {"noMarkedRedactions": "<PERSON><PERSON> menyunting dengan menandai teks, wilayah, halaman, atau melakukan pencarian.", "redactionSearchPlaceholder": "<PERSON><PERSON> be<PERSON><PERSON> keyboard atau pola", "redactionCounter": "Ditandai untuk Redaksi", "clearMarked": "<PERSON><PERSON><PERSON>", "redactAllMarked": "<PERSON><PERSON><PERSON>", "redactionItems": "<PERSON><PERSON>", "redactionItem": {"regionRedaction": "<PERSON><PERSON><PERSON> wilayah", "fullPageRedaction": "<PERSON><PERSON><PERSON> halaman penuh", "fullVideoFrameRedaction": "Redaksi Video", "audioRedaction": "Redaksi Audio", "fullVideoFrameAndAudioRedaction": "Redaksi Video dan Audio", "image": "Gambar"}, "expand": "Mengembangkan", "collapse": "<PERSON><PERSON><PERSON>", "searchResults": "<PERSON><PERSON>", "search": {"creditCards": "Kartu kredit", "phoneNumbers": "Nomor telepon", "images": "Gambar-gambar", "emails": "Email", "pattern": "Pola", "start": "<PERSON><PERSON><PERSON>n <PERSON>"}}, "wv3dPropertiesPanel": {"propertiesHeader": "Properti", "miscValuesHeader": "<PERSON><PERSON><PERSON>", "emptyPanelMessage": "<PERSON>lih elemen untuk melihat proper<PERSON>."}, "watermarkPanel": {"textWatermark": "Tanda Air Teks", "uploadImage": "Unggah Gambar", "browse": "<PERSON><PERSON><PERSON><PERSON>", "watermarkOptions": "Opsi Tanda Air", "watermarks": "Tanda air"}, "portfolio": {"createPDFPortfolio": "Buat Portofolio PDF", "uploadFiles": "<PERSON><PERSON><PERSON> berkas", "uploadFolder": "Unggah Folder", "addFiles": "Tambahkan File", "addFile": "Tambah berkas", "addFolder": "Tambah Folder", "createFolder": "Membuat folder", "portfolioPanelTitle": "Portofolio PDF", "portfolioNewFolder": "Folder baru", "portfolioDocumentTitle": "<PERSON><PERSON><PERSON> do<PERSON>men", "portfolioFolderPlaceholder": "Nama folder", "portfolioFilePlaceholder": "Nama file", "folderNameAlreadyExists": "Nama folder sudah ada", "fileNameAlreadyExists": "Nama file sudah ada", "openFile": "<PERSON><PERSON> <PERSON>", "fileAlreadyExists": "File Sudah Ada", "fileAlreadyExistsMessage": "File \"{{fileName}}\" sudah ada di portofolio.", "deletePortfolio": "Anda yakin ingin menghapus \"{{fileName}}\"?", "reselect": "<PERSON><PERSON><PERSON>"}, "languageModal": {"selectLanguage": "<PERSON><PERSON><PERSON> bahasa"}, "officeEditor": {"bold": "<PERSON><PERSON>", "italic": "Miring", "underline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textColor": "Warna teks", "leftAlign": "Rata kiri", "centerAlign": "Rata tengah", "rightAlign": "<PERSON>a kanan", "justify": "Membenar<PERSON>", "lineSpacing": "Penspasian baris & paragraf", "lineSpacingMenu": "Spasi Baris", "bulletList": "<PERSON><PERSON><PERSON> poin", "numberList": "<PERSON><PERSON><PERSON> berno<PERSON>", "decreaseIndent": "Kurangi indentasi", "increaseIndent": "Tingkatkan indentasi", "nonPrintingCharacters": "Karakter non-cetak", "insertLink": "<PERSON><PERSON><PERSON><PERSON>", "insertImage": "Sisipkan gambar", "image": "Gambar", "table": "<PERSON><PERSON>", "insertRowAbove": "Sisipkan Baris Di Atas", "insertRowBelow": "Sisipkan Baris Di Bawah", "insertColumnRight": "<PERSON><PERSON><PERSON><PERSON>", "insertColumnLeft": "<PERSON><PERSON><PERSON><PERSON>", "deleteRow": "Hapus Baris", "deleteColumn": "<PERSON><PERSON>", "deleteTable": "<PERSON><PERSON>", "deleted": "Dihapus:", "added": "Ditambahkan:", "editing": "<PERSON><PERSON><PERSON>", "editingDescription": "Sunting dokumen", "reviewing": "<PERSON><PERSON><PERSON><PERSON>", "reviewingDescription": "Membuat saran", "viewOnly": "<PERSON><PERSON>", "viewOnlyDescription": "<PERSON><PERSON> tanpa saran", "notSupportedOnMobile": "Pengeditan Office tidak didukung di perangkat seluler.", "previewAllChanges": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>a <PERSON>", "accept": "<PERSON><PERSON><PERSON>", "reject": "Menolak", "pastingTitle": "Penempelan tidak tersedia", "pastingMessage": "Penempelan tidak didukung di browser Anda. Se<PERSON>ai gantinya, <PERSON><PERSON> dapat menggunakan pintasan keyboard", "pastingWithoutFormatTitle": "Menempel tanpa memformat tidak tersedia", "pastingWithoutFormatMessage": "Menempel tanpa memformat tidak didukung di browser Anda. Sebagai gantinya, <PERSON><PERSON> dapat menggunakan pintasan keyboard", "breaks": "<PERSON><PERSON><PERSON><PERSON>", "pageBreak": "<PERSON><PERSON><PERSON><PERSON>", "pageBreakDescription": "<PERSON><PERSON><PERSON> halaman dan mulai di halaman baru", "sectionBreakNextPage": "<PERSON><PERSON>isah <PERSON> - <PERSON><PERSON>", "sectionBreakNextPageDescription": "<PERSON><PERSON><PERSON><PERSON> pemisah bagian dan mulai di halaman berikutnya", "sectionBreakContinuous": "<PERSON><PERSON><PERSON><PERSON> - Berkelanjutan", "sectionBreakContinuousDescription": "<PERSON><PERSON><PERSON><PERSON> pemisah bagian dan lanjutkan pada halaman yang sama", "section": "Bagian", "header": {"0": "<PERSON><PERSON><PERSON>", "1": "Header <PERSON><PERSON>", "2": "Header <PERSON>", "3": "Header <PERSON><PERSON>", "-1": "Header tida<PERSON> valid"}, "footer": {"0": "Catatan kaki", "1": "Catatan <PERSON>", "2": "<PERSON><PERSON><PERSON>", "3": "Footer <PERSON>", "-1": "Footer <PERSON><PERSON>"}, "options": "<PERSON><PERSON><PERSON>", "pageOptions": "<PERSON><PERSON>", "removeHeader": "<PERSON><PERSON>", "removeFooter": "<PERSON><PERSON>", "headerFooterOptionsModal": {"title": "Format header dan footer", "margins": "<PERSON><PERSON>", "headerFromTop": "Header <PERSON><PERSON>", "footerFromBottom": "Footer <PERSON><PERSON>", "layouts": {"layout": "<PERSON><PERSON>", "noSelection": "Tidak <PERSON>", "differentFirstPage": "<PERSON><PERSON>", "differentEvenOddPages": "Halaman Genap & Ganji<PERSON> yang <PERSON>", "differentFirstEvenOddPages": "<PERSON><PERSON>, <PERSON><PERSON>, dan <PERSON><PERSON><PERSON><PERSON>"}}, "lineSpacingOptions": {"15": "1.5", "115": "1.15", "single": "Lajan<PERSON>", "double": "<PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>"}, "numberDropdown": {"6": "Angka Latin Romawi 1", "7": "<PERSON><PERSON>", "8": "Angka Latin Romawi 2", "10": "Latin Romawi", "11": "<PERSON><PERSON>", "dropdownLabel": "Preset Penomoran"}, "bulletDropdown": {"0": "Peluru", "1": "Kotak Peluru", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON>", "4": "Memeriksa", "5": "<PERSON><PERSON>ah", "dropdownLabel": "Preset Peluru"}, "fontSize": {"dropdownLabel": "Ukuran Font"}, "fontStyles": {"dropdownLabel": "<PERSON><PERSON>"}, "fontFamily": {"dropdownLabel": "<PERSON><PERSON><PERSON><PERSON>"}}, "spreadsheetEditor": {"editing": "<PERSON><PERSON><PERSON>", "viewOnly": "Melihat", "editingDescription": "<PERSON> dokumen", "viewOnlyDescription": "<PERSON><PERSON> saja", "bold": "<PERSON><PERSON>", "italic": "Miring", "underline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strikethrough": "Dicoret", "cellBorderStyle": "<PERSON><PERSON>", "merge": "Menggabungkan", "unmerge": "Pisahkan", "cellFormat": "Format Sel", "automatic": "<PERSON><PERSON><PERSON><PERSON>", "plainText": "<PERSON><PERSON> <PERSON>", "increaseDecimal": "Meningkatkan desimal", "decreaseDecimal": "<PERSON><PERSON><PERSON> desimal", "number": "Nomor", "percent": "<PERSON><PERSON>", "accounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "financial": "Finansial", "currency": "<PERSON>", "currencyRounded": "<PERSON> u<PERSON> di<PERSON>", "calendar": "Tanggal", "clockHour": "<PERSON><PERSON><PERSON>", "calendarTime": "Tanggal W<PERSON>", "formatAsCurrency": "Format sebagai mata uang", "formatAsPercent": "Format sebagai persen", "formatAsDecDecimal": "Kurangi titik desimal", "formatAsIncDecimal": "Meningkatkan titik desimal", "fontColor": "<PERSON><PERSON>", "cellBackgroundColor": "<PERSON><PERSON>", "textAlignment": "Penyelarasan Teks", "alignLeft": "Rata kiri", "alignCenter": "Sejajarkan tengah", "alignRight": "<PERSON>a kanan", "alignTop": "<PERSON><PERSON><PERSON><PERSON> bagian atas", "alignMiddle": "Sejajarkan tengah", "alignBottom": "<PERSON><PERSON><PERSON><PERSON> bagian bawah", "cellAdjustment": "Pen<PERSON><PERSON><PERSON>", "insertColLeft": "Sisipkan kolom kiri", "columnInsertLeft": "Sisipkan kolom kiri", "columnInsertRight": "Sisipkan kolom kanan", "rowInsertTop": "Sisipkan baris di atas", "rowInsertBottom": "Sisipkan baris di bawah", "columnInsertShiftDown": "<PERSON><PERSON><PERSON><PERSON> sel dan geser ke bawah", "columnInsertShiftRight": "<PERSON><PERSON><PERSON><PERSON> sel dan geser ke kanan", "columnDelete": "Ha<PERSON> kolom", "rowDelete": "Ha<PERSON> baris", "columnDeleteShiftUp": "<PERSON><PERSON> sel dan geser ke atas", "columnDeleteShiftLeft": "<PERSON><PERSON> sel dan geser ke kiri"}, "insertPageModal": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tabs": {"blank": "Kosong", "upload": "Mengunggah"}, "pagePlacements": {"header": "<PERSON><PERSON><PERSON><PERSON>", "above": "<PERSON>", "below": "<PERSON>"}, "pageLocations": {"header": "<PERSON><PERSON>", "specify": "<PERSON><PERSON><PERSON>", "specifyLocation": "Tentukan <PERSON>", "amount": "<PERSON><PERSON><PERSON>", "total": "Total", "pages": "halaman"}, "pageDimensions": {"header": "<PERSON><PERSON><PERSON>", "subHeader": "Preset", "presets": {"letter": "Surat", "halfLetter": "<PERSON><PERSON><PERSON> huru<PERSON>", "juniorLegal": "<PERSON><PERSON><PERSON> junior", "custom": "<PERSON><PERSON><PERSON><PERSON>"}, "units": "Satuan"}, "browse": "<PERSON><PERSON><PERSON> be<PERSON>s", "fileSelected": {"title": "<PERSON><PERSON><PERSON> untuk Ditambah"}, "button": "<PERSON><PERSON><PERSON>", "selectPages": "<PERSON><PERSON><PERSON> untuk Ditambah", "page": "<PERSON><PERSON>", "warning": {"title": "<PERSON><PERSON><PERSON>, sisi<PERSON>kan halaman baru?", "message": "<PERSON><PERSON><PERSON> akan membatalkan semua pilihan yang telah Anda buat sejauh ini. <PERSON><PERSON><PERSON>h Anda yakin masih ingin keluar?"}}, "multiViewer": {"dragAndDrop": "Seret dan <PERSON> file Anda di sini untuk membandingkan", "or": "<PERSON><PERSON>", "browse": "<PERSON><PERSON><PERSON> be<PERSON>s", "startSync": "<PERSON><PERSON>", "stopSync": "Hentikan <PERSON>ron<PERSON>", "closeDocument": "Tutup Dokumen", "save": "Simpan Dokumen", "comparePanel": {"Find": "Temukan di dokumen", "changesList": "<PERSON><PERSON> daftar", "change": "Mengubah", "old": "<PERSON><PERSON>", "new": "<PERSON><PERSON>", "page": "<PERSON><PERSON>", "textContent": "Konten Teks", "delete": "<PERSON><PERSON><PERSON><PERSON>", "insert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "sunting"}}, "saveModal": {"close": "<PERSON><PERSON><PERSON>", "saveAs": "Simpan <PERSON>", "general": "<PERSON><PERSON>", "fileName": "Nama file", "fileType": "<PERSON><PERSON>", "fileLocation": "Lokasi file", "browse": "<PERSON><PERSON><PERSON><PERSON>", "pageRange": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "currentView": "Pandangan saat ini", "currentPage": "Halaman saat ini", "specifyPage": "<PERSON><PERSON><PERSON>", "properties": "Properti", "includeAnnotation": "Sertakan <PERSON>", "includeComments": "Sertakan <PERSON>", "watermark": "Tanda air", "addWatermark": "Tambahkan Tanda Air", "save": "Menyimpan file", "pageError": "<PERSON>lakan masukkan rentang antara 1 - ", "fileNameCannotBeEmpty": "Nama File tidak boleh kosong"}, "filePicker": {"dragAndDrop": "Seret & Jatuhkan file Anda di sini", "selectFile": "<PERSON><PERSON><PERSON> file Anda di sini", "or": "<PERSON><PERSON>"}, "stylePanel": {"headings": {"styles": "<PERSON><PERSON>", "annotation": "<PERSON><PERSON><PERSON>", "annotations": "<PERSON><PERSON><PERSON>", "tool": "Alat", "textStyles": "<PERSON><PERSON>", "currentColor": "<PERSON><PERSON>", "customColors": "<PERSON><PERSON>", "redactionTextLabel": "Label Teks", "redactionMarkOutline": "Tandai Garis Besar", "redactionFill": "<PERSON><PERSON>", "redactionTextPlaceholder": "Sisipkan label teks", "contentEdit": "Pengeditan Konten"}, "lineStyles": {"startLineStyleLabel": "<PERSON><PERSON>", "middleLineStyleLabel": "<PERSON><PERSON>", "endLineStyleLabel": "<PERSON><PERSON>"}, "addColorToCustom": "Tambahkan ke warna khusus", "noToolSelected": "<PERSON>lih alat untuk melihat properti alat", "noToolStyle": "Alat tidak mengandung properti penataan gaya apa pun.", "lineEnding": {"start": {"dropdownLabel": "<PERSON><PERSON>"}, "end": {"dropdownLabel": "<PERSON><PERSON>"}, "middle": {"dropdownLabel": "<PERSON><PERSON>"}}, "borderStyle": {"dropdownLabel": "<PERSON><PERSON><PERSON><PERSON>"}}, "signatureListPanel": {"header": "<PERSON><PERSON><PERSON>", "newSignature": "<PERSON><PERSON>", "newSignatureAndInitial": "Tanda Tangan & Inisial Baru", "signatureList": {"signature": "<PERSON>da tangan", "initials": "Inisial"}}, "rubberStampPanel": {"header": "<PERSON><PERSON><PERSON>", "standard": "<PERSON><PERSON><PERSON>"}, "colorPickerModal": {"modalTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "accessibility": {"landmarks": {"topHeader": "<PERSON><PERSON><PERSON>", "leftHeader": "Header <PERSON><PERSON>", "rightHeader": "Header <PERSON>", "bottomHeader": "Header <PERSON>", "documentContent": "Konten Dokumen"}, "label": "Aksesibilitas", "accessibilityMode": "Mode Aksesibilitas", "skipTo": "<PERSON><PERSON> ke", "document": "Dokumen", "notes": "Catatan"}, "formulaBar": {"label": "<PERSON><PERSON><PERSON>", "range": "Jang<PERSON><PERSON>", "formulas": "<PERSON><PERSON><PERSON>", "sumif": "<PERSON><PERSON><PERSON> be<PERSON> di seluruh rentang", "sumsq": "Mengembalikan jumlah kuadrat suatu rentang", "sum": "Menambahkan semua angka dalam suatu rentang", "asinh": "Mengembalikan sinus hiperbolik invers dari suatu angka", "acos": "Mengembalikan arccosine dari suatu ang<PERSON>, dalam radian", "cosh": "Mengembalikan kosinus hiperbolik suatu angka", "iseven": "Memeriksa apakah suatu angka genap", "isodd": "Memeriksa apakah suatu angka ganjil"}}