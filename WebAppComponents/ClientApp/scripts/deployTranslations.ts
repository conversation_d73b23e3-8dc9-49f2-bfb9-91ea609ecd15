// imports
import * as fs from 'fs'
import { getJsonForGlob } from '@i18n/translation-service'

// get WebApp translations
console.log('get translations for WebApp')
const translationJson = await getJsonForGlob('../../WebApp/')

// get WebAppComponents translations and add Components to WebApps
console.log('\nget translations for WebAppComponents')
translationJson['Components'] = await getJsonForGlob('src/*(components|enums|shared)/')
// check all json
let jsonString = JSON.stringify(translationJson, null, 2)

// Write Json to disk
fs.writeFileSync('../../WebApp/Translations.i18n.json', jsonString)

export default {}