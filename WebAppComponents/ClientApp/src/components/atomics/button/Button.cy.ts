import { stories } from './Button.stories'
import { storyTest } from '@test-home/support/advanced-functions'

import('./Button')

// Test suite for the example web component
describe('<lvl-example-button />', () => {

	storyTest('changes the label', stories.label, () => {
		cy.mountStory(stories.label)
		cy.get('button').should('contain.text', stories.label.getAttribute('label'))
		cy.get('lvl-button')
			.invoke('attr', 'label', 'Foo')
		cy.get('button').should('contain.text', 'Foo')
		cy.get('button .icon').should('not.exist')
	})

	storyTest('changes the icon', stories.icon, () => {
		cy.mountStory(stories.icon)
		cy.get('button .icon').should('have.class', `fa-${stories.icon.getAttribute('icon')}`)
		cy.get('lvl-button')
			.invoke('attr', 'icon', 'dog')
		cy.get('button .icon').should('have.class', 'fa-dog')
		cy.get('button span').should('not.exist')
	})

	storyTest('checks disabled property', stories.disabled, () => {
		cy.spy(Test, 'handleClick').as('spy')
		cy.mountStory(stories.disabled)
		cy.get('button').as('button')
		cy.get('lvl-button').as('lvlButton').invoke('get', 0).invoke('addEventListener', 'click', Test.handleClick)
		cy.get('@button').should('have.class', 'readonly')
		cy.get('@button').click()
		cy.get('@spy').should('have.callCount', 0)
		cy.get('@lvlButton').invoke('removeAttr', 'disabled')
		cy.get('@button').click()
		cy.get('@spy').should('have.callCount', 1)
	})

	storyTest('checks icon and label together', stories.iconAndLabel, () => {
		cy.mountStory(stories.iconAndLabel)
		cy.get('button span').should('contain.text', stories.iconAndLabel.getAttribute('label'))
		cy.get('button .icon').should('have.class', `fa-${stories.iconAndLabel.getAttribute('icon')}`)
	})

	storyTest('checks tooltip', stories.tooltip, () => {
		cy.mountStory(stories.tooltip)
		cy.get('lvl-tooltip').find('.popup').should('exist')
		cy.get('lvl-tooltip').should('contain.text', stories.tooltip.getAttribute('tooltip'))
		cy.get('lvl-button')
			.invoke('attr', 'tooltip', 'Bar')
		cy.get('lvl-tooltip').should('contain.text', 'Bar')
	})
	
	storyTest('switch between soft and normal loading and check click function', stories.loading, () => {
		cy.spy(Test, 'handleClick').as('spy')
		
		cy.mountStory(stories.loading)
		cy.get('button').as('button')
		cy.get('lvl-button').as('lvlButton').invoke('get', 0).invoke('addEventListener', 'click', Test.handleClick)
		cy.get('@lvlButton').click()
		cy.get('@spy').should('have.callCount', 0)
		cy.get('@lvlButton').find('button > *').should('not.be.visible')
		
		cy.get('@lvlButton').invoke('removeAttr', 'loading')
		cy.get('@lvlButton').invoke('attr', 'soft-loading', '')
		cy.get('@lvlButton').click()
		cy.get('@spy').should('have.callCount', 1)
	})
})

class Test {
	static handleClick() {
		console.log('Test')
	}
}