import { css, html, LitElement, TemplateResult } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { ClassInfo, classMap } from 'lit/directives/class-map.js'
import { IconOptions, renderIcon } from '@/shared/component-html.ts'
import * as styles from '@/shared/component-styles.ts'
import { ifDefined } from 'lit-html/directives/if-defined.js'
import { PopupPlacement } from '@/components/popup/Popup.ts'
import { ButtonGroup } from '@/components/button-group/ButtonGroup.ts'
import { query } from 'lit/decorators/query.js'
import { IconStyle } from '@/enums/icon-style.ts'
import { Size } from '@/enums/size.ts'
import { ColorState } from '@/enums/color-state.ts'
import { when } from 'lit/directives/when.js'
import { fontAwesome } from '@/shared/font-awesome.ts'

export type ButtonProperties = {
	icon?: string
	iconStyle?: string
	trailingIcon?: string
	label?: string
	type: ButtonType
	size: Size
	color?: ColorState
	disabled?: boolean
	tooltip?: string
	tooltipPlacement?: PopupPlacement
	value?: string
	selected: boolean
	skeleton: boolean
	rounded: boolean
	stacked: boolean
	withBadge: boolean
	badgeText?: string
	badgeColor?: string
	badgeOutlineColor?: string
	loading: boolean
	softLoading: boolean
	bold: boolean
}

export const enum ButtonType {
	Primary = 'primary',
	Secondary = 'secondary',
	Tertiary = 'tertiary'
}

/**
 * Web component as button with icon and label
 */
@customElement('lvl-button')
export class Button extends LitElement implements ButtonProperties {

	static styles = [
		styles.base,
		styles.color,
		styles.animation,
		styles.skeleton,
		styles.icon,
		styles.badge,
		fontAwesome,
		css`
			:host {
				position: relative;
				display: inline-block;
				font-size: var(--size-text-m);

				--text-color: currentColor;
				--background-color: inherit;
				--border: unset;
				--height: 3.2rem;
				--border-radius: var(--button-radius, var(--size-radius-m));
				--border-radius-left: var(--button-radius-left, var(--border-radius));
				--border-radius-right: var(--button-radius-right, var(--border-radius));
				--flex-direction: row;

				--badge-offset: 0.3rem;

				border-radius: var(--border-radius-left) var(--border-radius-right) var(--border-radius-right) var(--border-radius-left);
			}

			:host(.with-min-size), :host([label]:not([stacked])) {
				min-width: 7.2rem;
			}

			:host(:hover:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-state-hover);
			}

			:host(:is([selected], [data-popup-open]):not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-state-selected);
			}

			:host([color=active]) {
				--text-color: var(--cp-clr-state-active);
			}

			:host([type=secondary][color=active]:hover:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-info-light);
			}

			:host([type=secondary][color=active][selected]:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-info-medium);
			}

			:host([color=inactive]) {
				--text-color: var(--cp-clr-state-inactive);
			}

			:host([color=inactive][selected]:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-state-readonly);
			}

			:host([color=info]) {
				--text-color: var(--cp-clr-signal-info);
			}

			:host([type=secondary][color=info]:hover:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-info-light);
			}

			:host([type=secondary][color=info][selected]:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-info-medium);
			}

			:host([color=success]) {
				--text-color: var(--cp-clr-signal-success);
			}

			:host([type=secondary][color=success]:hover:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-success-light);
			}

			:host([type=secondary][color=success][selected]:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-success-medium);
			}

			:host([color=error]) {
				--text-color: var(--cp-clr-signal-error);
			}

			:host([type=secondary][color=error]:hover:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-error-light);
			}

			:host([type=secondary][color=error][selected]:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-error-medium);
			}

			:host([color=warning]) {
				--text-color: var(--cp-clr-signal-warning);
			}

			:host([type=secondary][color=warning]:hover:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-warning-light);
			}

			:host([type=secondary][color=warning][selected]:not(:is([disabled], [loading]))) {
				--background-color: var(--cp-clr-signal-warning-medium);
			}

			:host([type=primary]) {
				--text-color: var(--cp-clr-text-primary-negativ);
				--background-color: var(--cp-clr-state-active);
			}

			:host([type=primary]:is(:hover, [selected], [data-popup-open])) {
				--text-color: var(--cp-clr-text-primary-negativ);
				--background-color: var(--cp-clr-state-active-hover);
			}

			:host([type=primary]:is([disabled], [loading])) button {
				--background-color: var(--cp-clr-state-inactive);
				--text-color: var(--cp-clr-text-primary-negativ);
			}

			:host([selected]:is([disabled], [loading])) {
				--background-color: var(--cp-clr-state-inactive);
				--text-color: var(--cp-clr-text-primary-negativ);
			}

			:host([type=primary][color=success]) {
				--background-color: var(--cp-clr-signal-success);
			}

			:host([type=primary][color=success]:is(:hover, [selected], [data-popup-open])) {
				--background-color: var(--cp-clr-signal-success-hover);
			}

			:host([type=primary][color=error]) {
				--background-color: var(--cp-clr-signal-error);
			}

			:host([type=primary][color=error]:is(:hover, [selected], [data-popup-open])) {
				--background-color: var(--cp-clr-signal-error-hover);
			}

			:host([type=secondary]) {
				--border: 1px solid var(--text-color);
			}

			:host(:is([disabled], [loading])) {
				--text-color: var(--cp-clr-state-inactive);
			}

			:host([size=medium]) {
				--height: 2.4rem;
				--button-padding: var(--size-spacing-s);
			}

			:host([size=small]) {
				--height: 2rem;
				--badge-offset: 0.1rem;
			}

			button:focus {
				outline: none;
			}

			button:focus-visible {
				outline: 2px solid var(--cp-clr-state-focus);
				outline-offset: var(--outline-offset, var(--size-spacing-xxs));
				z-index: 1;
			}

			:host([icon]:not([label]):not([trailing-icon]):not([show-dropdown-anchor])) {
				width: var(--height);

				& button {
					padding: 0;
				}
			}

			:host([show-dropdown-anchor]:not([label]):not([trailing-icon]):not([icon])) {
				width: 1.6rem;

				& button {
					padding: 0;
				}
			}

			:host([rounded]) {
				--border-radius: var(--size-radius-pill);
			}

			:host([stacked]) {
				--height: 4rem;
				--flex-direction: column;

				& .button__content {
					flex-direction: column;
					gap: var(--size-spacing-s);
				}

				& span {
					font-size: var(--size-text-s);
				}
			}

			:host(:is([disabled], [loading])) button {
				cursor: default;
			}

			:host([bold]) :is(span, .icon) {
				font-weight: bold;
			}

			button {
				position: relative;
				display: flex;
				gap: var(--size-spacing-s);
				align-items: center;
				justify-content: center;

				width: 100%;
				height: var(--height);
				max-height: 100%;

				border: var(--border);
				border-radius: var(--border-radius-left) var(--border-radius-right) var(--border-radius-right) var(--border-radius-left);

				font-family: var(--font-family-default);
				color: var(--text-color);
				background-color: var(--background-color);
				padding: 0 var(--button-padding, var(--size-spacing-m));
				font-size: inherit;

				transition: height var(--animation-time-medium) ease, background-color var(--animation-time-medium) ease;
				overflow: hidden;
			}

			:host([hidden]) {
				display: none;
			}

			button.skeleton__block {
				--background-color: transparent;
				--text-color: transparent;
				overflow: unset;
				border-color: var(--cp-clr-state-inactive);
			}

			button.selected:hover {
				cursor: default;
			}

			button .icon:before {
				display: inline-block;
				min-width: 1.6rem;
				text-align: center;
			}

			button .icon:last-of-type {
				transition: rotate var(--animation-time-medium) ease-in-out;
				rotate: var(--icon-rotation, 0deg);
			}

			.button__content {
				position: relative;
				display: flex;
				align-items: center;
				gap: var(--size-spacing-s);
				overflow: hidden;
				flex-direction: var(--flex-direction);
				height: 100%;
				justify-content: center;
				flex-grow: 1;
				user-select: none;

				& .icon:first-of-type {
					color: var(--icon-color, currentColor);
				}

				& span {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					font-size: var(--button-text-size, 1.2rem)
				}

				& i {
					font-size: var(--button-icon-size, 1.2rem)
				}
			}

			.button--loading::after {
				content: "\\f3f4";
				font-family: "Font Awesome 6 Pro", system-ui;
				font-weight: 300;
				animation-name: fa-spin;
				animation-duration: var(--fa-animation-duration, 2s);
				animation-iteration-count: var(--fa-animation-iteration-count, infinite);
				animation-timing-function: var(--fa-animation-timing, linear);

				visibility: visible;
				position: absolute;
				left: 50%;
				top: 50%;
				translate: -50% -50%;
			}

			.button--loading > * {
				visibility: hidden;
			}

			*:not(:defined) {
				display: none;
			}

			.badge {
				position: absolute;
				top: 0.6rem;
				right: 0.2rem;
				--min-size: 1.6rem;
			}
		`,
	]

	//#region attributes

	@property()
	label?: string

	@property()
	tooltip?: string

	@property({ attribute: 'tooltip-placement' })
	tooltipPlacement?: PopupPlacement

	@property()
	icon?: string

	@property({ attribute: 'trailing-icon' })
	trailingIcon?: string

	@property({ attribute: 'icon-style' })
	iconStyle?: IconStyle = IconStyle.Light

	@property()
	type: ButtonType = ButtonType.Secondary

	@property()
	size: Size = Size.Large

	@property({ type: Boolean, reflect: true })
	hidden: boolean = false

	@property()
	value?: string

	@property({ type: Boolean, reflect: true })
	selected: boolean = false

	@property({ type: Boolean, reflect: true })
	skeleton: boolean = false

	@property({ type: Boolean })
	rounded: boolean = false

	@property({ type: Boolean })
	stacked: boolean = false

	@property()
	color?: ColorState

	@property({ attribute: 'with-badge', type: Boolean })
	withBadge: boolean = false

	@property({ attribute: 'badge-text' })
	badgeText?: string

	@property({ attribute: 'badge-color' })
	badgeColor?: string

	@property({ attribute: 'badge-outline-color' })
	badgeOutlineColor?: string

	@property({ type: Boolean, reflect: true })
	disabled: boolean = false

	@property({ type: Boolean, reflect: true })
	bold: boolean = false

	@property({ type: Boolean, reflect: true })
	loading: boolean = false

	@property({ type: Boolean, attribute: 'soft-loading', reflect: true })
	softLoading: boolean = false

	@property({ attribute: 'data-popup-open', type: Boolean })
	private dropdownOpen: boolean = false

	@property({ attribute: 'show-dropdown-anchor', type: Boolean })
	private hasDropdown: boolean = false

	//#endregion

	@query('button')
	public button!: HTMLElement

	//#private properties

	private buttonGroup?: ButtonGroup

	private abortController?: AbortController

	@property({ type: Boolean, reflect: true })
	public initDone: boolean = false

	public onClick?: () => void

	//#endregion

	focus() {
		this.button.focus()
	}

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		return html`
			${this.renderButton()}
			${this.tooltip && !this.dropdownOpen && !this.disabled ? html`
				<lvl-tooltip name="buttonTooltip" placement="${ifDefined(this.buttonGroup != undefined ? PopupPlacement.Top : this.tooltipPlacement ?? undefined)}" flip
										 shift
										 ?orbit="${this.buttonGroup == undefined}" delayed>
					${this.tooltip}
				</lvl-tooltip>
			` : ''}
		`
	}

	//#region private methods
	connectedCallback() {
		super.connectedCallback()
		this.abortController = new AbortController()

		// is this button part of a button group?
		this.buttonGroup = this.closest('lvl-button-group') as ButtonGroup
		if (this.buttonGroup) {
			// set initial selected flag
			this.selected = this.value != undefined && this.buttonGroup.value == this.value
		}

		// call onclick callback if set
		this.addEventListener('click', () => {
			if (!this.disabled && this.onClick)
				this.onClick()
		}, { signal: this.abortController.signal })

		this.initDone = true
	}

	disconnectedCallback() {
		super.disconnectedCallback()
		this.abortController?.abort()
	}

	private renderButton() {
		// we use class maps to embed only passed attributes to the class attribute of html element
		const classes: ClassInfo = {
			readonly: this.disabled,
			clickable: true,
			hasDropdown: this.hasDropdown,
			skeleton__block: this.skeleton || this.buttonGroup?.skeleton == true,
			'button--loading': this.loading,
			iconOnly: !!(this.icon && !this.label),
		} as const

		const iconDefaultOptions: IconOptions = { iconStyle: this.iconStyle, inline: true, additionalClasses: [ 'fa-fw' ] }
		const iconDropDownOptions: IconOptions = { iconStyle: this.iconStyle, inline: true }

		// add icons and label to complete content
		const getHtmlContent = () => {
			let content: TemplateResult[] = []
			if (this.icon || this.label) {
				const innerContent = html`
					<div class="button__content ${!this.loading && this.softLoading ? 'button--loading' : ''}">
						${when(this.icon, () => renderIcon(this.icon!, iconDefaultOptions))}
						${when(this.label, () => html`<span>${this.label}</span>`)}
					</div>
				`
				content.push(innerContent)
			}

			if (this.trailingIcon || this.hasDropdown)
				content.push(renderIcon(this.hasDropdown ? 'chevron-down' : this.trailingIcon || '', this.hasDropdown ? iconDropDownOptions : iconDefaultOptions))

			return content
		}

		const styles = []
		if (this.withBadge) {
			if (this.badgeColor)
				styles.push(`--badge-color: ${this.badgeColor}`)
			if (this.badgeOutlineColor)
				styles.push(`--badge-outline-color: ${this.badgeOutlineColor}`)
		}

		return html`
			<button class="${classMap(classes)}" data-tooltip="${ifDefined(this.tooltip ? 'buttonTooltip' : '')}"
							tabindex="${this.disabled || this.skeleton || this.loading || (this.buttonGroup && this.value != undefined && (!this.selected || this.buttonGroup.readonly)) ? -1 : 0}"
							@click="${this.handleClick}">
				${getHtmlContent()}
			</button>
			${this.withBadge ? html`
				<div class="badge with-badge" data-badge-text="${this.withBadge ? this.badgeText : ''}" style="${styles.join(';')}"></div>` : null}`
	}

	//#endregion

	private handleClick(event: MouseEvent) {
		if (this.disabled || this.loading) {
			event.preventDefault()
			event.stopPropagation()
			return
		}

		if (this.buttonGroup != undefined && this.value != null)
			this.buttonGroup.value = this.value
	}
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-button': Button
	}
}