import { css, html, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { renderIcon } from '@/shared/component-html.ts'
import { unsafeHTML } from 'lit/directives/unsafe-html.js'
import { fontAwesome } from '@/shared/font-awesome.ts'

export type ChipType = {
	value: string
	label?: string
	icon?: string
	readonly: boolean
	disabled: boolean
}

/**
 * Chip web component using LIT (https://lit.dev)
 */
@customElement('lvl-chip')
export class Chip extends LitElement implements ChipType {

	/* All css styling must    be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		fontAwesome,
		css`
			:host {
				--background: var(--cp-clr-background-lvl-0);
				--border-color: var(--cp-clr-state-active);
				--color: var(--cp-clr-text-primary-positiv);
				--outline-offset: 1px;
				--outline-width: 2px;

				color: var(--color);
			}

			:host([readonly]) {
				--background: var(--cp-clr-background-lvl-1);
				--border-color: transparent;
				--color: var(--cp-clr-text-secondary);
			}

			:host([readonly]) .chip__label {
				color: currentColor;
			}

			:host([disabled]) {
				--border-color: var(--cp-clr-state-inactive);
				--color: var(--cp-clr-state-inactive);
			}

			:host([disabled]) :is(.chip__label, .chip__icon) {
				color: currentColor;
			}

			.chip {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				gap: var(--size-spacing-s);
				background-color: var(--background);
				border: 1px solid var(--border-color);
				border-radius: var(--size-radius-m);
				margin: calc(var(--outline-width) + var(--outline-offset));
				padding: var(--size-spacing-s) var(--size-spacing-m);
				cursor: pointer;
			}

			.chip:hover {
				--background: var(--cp-clr-background-lvl-1);
			}

			.chip:focus-visible {
				outline: var(--outline-width) solid var(--cp-clr-state-focus);
				outline-offset: var(--outline-offset);
			}

			.chip__label {
				font-size: var(--size-text-xs);
				color: var(--cp-clr-text-secondary);
			}

			.chip__icon {
				color: var(--cp-clr-state-active);
			}

			.icon {
				padding: 0 var(--size-spacing-xs);
			}

			.chip__content {
				flex-grow: 1;
				font-size: var(--size-text-s);
				text-align: start;

				> * {
					display: block;
				}
			}
		`,
	]

	//#region attributes

	@property()
	label?: string

	@property()
	value: string = ''

	@property()
	icon?: string

	@property({ type: Boolean })
	readonly: boolean = false

	@property({ type: Boolean })
	disabled: boolean = false

	//#endregion

	//#region states
	//#endregion states

	//#region private properties
	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		return html`
			<div class="chip" tabindex="${this.readonly || this.disabled ? -1 : 0}" @keyup="${this.handleKeydown}">
				${this.icon ? renderIcon(this.icon, { additionalClasses: [ 'chip__icon' ], inline: true }) : ''}
				<div class="chip__content">
					${this.label ? html`<span class="chip__label">${this.label}</span>` : ''}
					<span>${unsafeHTML(this.value)}</span>
				</div>
				${this.readonly ? '' : renderIcon('close', {
					data: { action: 'close' },
					inline: true,
					onClick: (event: MouseEvent) => this.handleCloseIconClick(event),
				})}
			</div>
		`
	}

	//#region lifecycle callbacks
	//#endregion

	//#region public methods
	//#endregion

	//#region private methods

	private handleCloseIconClick(event: MouseEvent) {
		this.closeChip()
		event.stopPropagation()
	}

	private handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ')
			this.closeChip()
		event.stopPropagation()
		event.preventDefault()
	}

	private closeChip() {
		this.dispatchEvent(new CustomEvent('chip-close:click', { bubbles: true }))
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-chip': Chip
	}
}