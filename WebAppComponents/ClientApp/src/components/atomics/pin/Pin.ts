import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { repeat } from 'lit/directives/repeat.js'
import { query } from 'lit/decorators/query.js'
import { v4 as uuid } from 'uuid'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { classMap } from 'lit/directives/class-map.js'
import { WorkflowInfo, WorkflowNode } from '@/shared/types.ts'
import { Size } from '@/enums/size.ts'
import { PopupPlacement } from '@/components/popup/Popup.ts'
import { DropdownMenuItem } from '@/components'
import CommunicationServiceProvider, { CommunicationResponseType } from '@/shared/communication-service.ts'
import { WorkflowStateType } from '@/enums/workflow-state-type.ts'

export type PinType = {
	id?: string
	icon?: string
	text?: string
	tooltip?: string
	color?: string
	zoom?: number
	disabled?: boolean
	position?: PinPositionType
	thumbnailUrl?: string
}

export type PinPositionType = {
	x: number,
	y: number
}

export type PinDimensionType = {
	width: number,
	height: number
}

/**
 * Blueprint Viewer
 */
@customElement('lvl-pin')
export class Pin extends LitElement implements PinType {
	
	static TOOLTIP_MIN_GAP = 16; // how many pixels should the tooltip be away from the viewport border?
	static ANIMATION_TIME_SLOW = 300;
	
	/* All css styling must    be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		styles.animation,
		fontAwesome,
		css`			
			#pinPoint {
				position: relative;
				width: 0;
				height: 0;
				opacity: 1;
				pointer-events: none;

				&:not(.hidden) {
					filter: drop-shadow(0 0 0.4rem var(--cp-clr-shadow-medium));
				}
				
				&.hasClickAction {
					cursor: pointer;
				}

				&.hidden {
					display: block !important;
					opacity: 0 !important;
					pointer-events: none;

					& > .pin {
						pointer-events: none;
					}
				}

				& > .pin {
					position: absolute;
					background-color: white;
					width: 6rem;
					height: 8rem;
					bottom: 0;
					left: 0;
					transform: translate(-50%, 0) scale(calc(0.5 + calc(0.5 * var(--zoom))));
					transform-origin: bottom center;
					pointer-events: all;
					display: flex;
					justify-content: center;
					color: var(--cp-clr-text-primary-negativ);
					font-family: var(--font-family-default);
					font-size: var(--size-text-xl);
					font-weight: 600;
					line-height: 5.9rem;
					clip-path: path("M33.703 78C41.719 67.969 60 43.656 60 30 60 13.438 46.563 0 30 0S0 13.438 0 30c0 13.656 18.281 37.969 26.297 48 1.922 2.391 5.484 2.391 7.406 0");

					& > i {
						font-size: 2rem;
						line-height: 5.9rem;
						pointer-events: none;
					}

					&:before {
						content: "";
						position: absolute;
						width: 5rem;
						height: 7rem;
						top: 0.5rem;
						background-color: var(--color, #007ACC);
						clip-path: path("M47.47 34.969c-1.703 4.047-4.094 8.438-6.813 12.828-5.328 8.641-11.625 16.906-15.609 21.875-3.969-4.969-10.281-13.234-15.609-21.875C6.736 43.406 4.33 39 2.626 34.969.908 30.844.064 27.501.064 25.063c0-13.813 11.188-25 25-25s25 11.188 25 25c0 2.438-.844 5.781-2.594 9.906");
						z-index: -1;
					}
				}
			}

			:host([transitions]) #pinPoint {
				transition: opacity var(--animation-time-slow) ease-in-out;
			}

			:host([open]) {
				z-index: 999;
			}
			
			:host([closing]) {
				z-index: 998;
			}
			
			:host([moving]) .pin {
				pointer-events: none !important;
			}

			#pinPoint.disabled {
				opacity: 0.3;
			}
			
			#pinInfo {
				position: absolute;
				left: 0;
				bottom: calc(4.4rem + calc(4rem * var(--zoom)));
				transform: translate(-50%, 0);
				background-color: var(--cp-clr-background-lvl-0);
				border-radius: var(--size-radius-l);
				display: flex;
				flex-direction: column;
				width: 29.5rem;
				z-index: 9999;
				cursor: default;
				overflow: hidden;
				user-select: none;
				
				opacity: 0;
				pointer-events: none;
				transition: opacity var(--animation-time-slow) ease;
				
				&[open] {
					opacity: 1;
					pointer-events: auto;
				}
				
				& > img {
					height: 12.2rem;
				}
			}

			#pinInfoContent {
				margin: var(--size-spacing-l) var(--size-spacing-l) var(--size-spacing-m);
				display: flex;
				flex-direction: column;
				gap: var(--size-spacing-m);
			}
			
			#pinPoint.previewEditMode {
				& #pinInfo {
					width: auto;
				}
				
				& #pinInfoContent {
					margin: var(--size-spacing-m);
					
					& #pinInfoHeader {
						display: none
					}
					
					& #pinInfoActions lvl-button[disabled] {
						display: none;
					}
				}
			}

			#pinInfoHeader {
				display: flex;
				flex-direction: row;
				gap: 8px;
				
				& > div {
					flex-grow: 1;
					display: flex;
					flex-direction: column;
					gap: 8px;
				}

				& label {
					font-size: 1.4rem;
					line-height: 140%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: normal;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}

				& legend {
					color: var(--cp-clr-text-secondary);
					line-height: 19px;
					padding: 0;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					max-width: 263px;
					
					& > i {
						margin-right: var(--size-spacing-s);
					}
				}
				
				& i {
					display: inline-block;
					flex-shrink: 0;
				}

				& i:before {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					color: var(--cp-clr-text-secondary);
				}
			}

			#pinInfoActions {
				display: flex;
				white-space: nowrap;
				align-items: stretch;
				gap: var(--size-spacing-s);
				
				& lvl-button {
					flex-grow: 1;
				}
			}
			
			.workflow-items {
				display: flex;
				flex-direction: row;
				gap: var(--size-spacing-m);
				
				& lvl-workflow-item {
					flex-grow: 1;
					max-width: calc(50% - var(--size-spacing-s));
				}
			}
		`,
	]

	//#region attributes
	
	@property()
	public id: string = uuid()
	
	@property()
	public set color(value: string | undefined) {
		this._color = value
	}
	public get color(): string {
		// if we have a fixed color, we are good to go
		if (this._color)
			return this._color
		
		// if we have exactly one workflow, we take the workflows state color
		if (this.workflowInfos?.length == 1) {
			const wfInfo = this.workflowInfos[0]
			switch (wfInfo.state?.toLowerCase()) {
				case WorkflowStateType.Start:
				case WorkflowStateType.InProgress:
					return '#FDBA74'
				case WorkflowStateType.Positive:
					return '#22C55E'
				case WorkflowStateType.Negative:
					return '#F87171'
			}
		}
		
		// otherwise we default to blue
		return this._color ?? '#007ACC'
	}
	private _color?: string
	
	@property({type: Number})
	public get zoom() {
		return this._zoom > 100 ? 100 : this._zoom < 0 ? 0 : this._zoom;
	}
	public set zoom(value: number) {
		this._zoom = value;
	}
	
	@property({type: Boolean, reflect: true})
	public disabled: boolean = false;
	
	@property()
	public text?: string
	
	@property({attribute: 'tooltip'})
	public tooltip?: string

	@property({attribute: 'tooltip-legend-top'})
	public tooltipLegendTop?: string

	@property({attribute: 'tooltip-legend-bottom'})
	public tooltipLegendBottom?: string

	@property({attribute: 'tooltip-icon'})
	public tooltipIcon?: string

	@property({attribute: 'tooltip-icon-color'})
	public tooltipIconColor?: string

	@property({attribute: 'thumbnail-url'})
	public thumbnailUrl?: string
	
	@property({type: Boolean, reflect: true})
	public open: boolean = false

	@property({type: Boolean, reflect: true})
	public opening: boolean = false

	@property({type: Boolean, reflect: true})
	public closing: boolean = false
	
	@property({type: Object})
	public position?: PinPositionType
	
	@property()
	public offset?: PinPositionType

	@property({type: Boolean, reflect: true})
	public transitions: boolean = false

	@property({type: Boolean, reflect: true})
	public resetting: boolean = false

	@property({type: Boolean, reflect: true})
	public moving: boolean = false

	@property({type: Boolean, attribute: 'has-click-action', reflect: true})
	public hasClickAction: boolean = false

	@property({ type: Boolean, attribute: 'preview-edit-mode' })
	public previewEditMode: boolean = false
	
	public workflowInfos: WorkflowInfo[] = []
	
	@property()
	public get icon(): string | undefined {
		if ((!this._icon && !this.text) || (this._icon && this.zoom < 50))
			return 'circle'
		return this._icon
	}
	
	public set icon(value: string | undefined) {
		this._icon = value
	}

	@property()
	public href?: string

	@property({type: Boolean, attribute: 'allow-move'})
	public allowMove: boolean = false

	@property({type: Boolean, attribute: 'allow-copy'})
	public allowCopy: boolean = false

	@property({type: Boolean, attribute: 'allow-discard'})
	public allowDiscard: boolean = false

	@property({type: Boolean, attribute: 'allow-state-change'})
	public allowStateChange: boolean = false
	
	public get iconCode(): string {		
		if (!this.icon)
			return ""

		if (Pin.iconCache[this.icon])
			return Pin.iconCache[this.icon];

		const tempElement = document.createElement("i");
		tempElement.className = 'fa-'+this.icon;

		document.body.appendChild(tempElement);
		const character = window.getComputedStyle(tempElement, ':before').getPropertyValue('content').replaceAll(`"`, "");
		tempElement.remove();

		if (character)
			Pin.iconCache[this.icon] = character;
		return character;
	}
	
	//#endregion

	//#region states

	@state()
	private _hidden: boolean = true
	private _showing: boolean = false
	public get hidden() {
		return this._hidden && !this._showing
	}
	private set hidden(value: boolean) {
		this._hidden = value
	}
	
	//#endregion states

	//#region private properties
	private _zoom: number = 100

	private _icon?: string
	
	private _resetPosition?: PinPositionType
	
	private static iconCache: Record<string, string> = {};

	private static readonly _localizer: StringLocalizer = new StringLocalizer('Pin')
	
	@query('#pinInfo')
	private pinInfo!: HTMLElement

	private localize(key: string) {
		return Pin._localizer.localize(key)
	}

	private abortController?: AbortController
	
	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		const classes = {
			hasClickAction: this.tooltip || this.tooltipLegendTop || this.tooltipLegendBottom || this.hasClickAction,
			previewEditMode: this.previewEditMode,
			hidden: this._hidden,
			disabled: this.disabled
		}
		return html`
			<div id="pinPoint" class="${classMap(classes)}" style="--color:${this.color}">
				<div class="pin" @click="${this.handleClick}">${this.text ?? html`<i class="fas fa-${this.icon}"></i>`}</div>
				${this.opening || this.open || this.closing ? html`<div id="pinInfo" ?open="${!this.opening && this.open}">
					${this.thumbnailUrl ? html`<img src="${this.thumbnailUrl}?width=295&height=122&crop=true" alt="" />` : null}
					<div id="pinInfoContent">
						<div id="pinInfoHeader">
							<div>
								<label>${this.tooltip}</label>
								<legend>
									${this.tooltipLegendTop ? html`${this.tooltipLegendTop}` : null}
									${this.tooltipLegendBottom ? this.tooltipLegendTop ? html` • ${this.tooltipLegendBottom}` : html`${this.tooltipLegendBottom}` : null}
								</legend>
							</div>
							${this.tooltipIcon ? html`<i id="pinInfoIcon" class="fal fa-${this.tooltipIcon}" style="background-color:${this.tooltipIconColor ?? this.color};"></i>` : null}
						</div>
						${this.workflowInfos?.length > 0 ? html`
							<div class="workflow-items">
								${this.workflowInfos.map(workflowInfo => this.renderWorkflowItem(workflowInfo))}
							</div>
						` : null}
						<div id="pinInfoActions">
							<lvl-button icon="arrow-up-right-from-square" label="${this.localize('open')}" stacked @click="${this.handleOpen}" ?disabled="${!this.href}"></lvl-button>
							<lvl-button icon="arrows-up-down-left-right" label="${this.localize('move')}" stacked @click="${this.handleMoveStart}" ?disabled="${!this.allowMove}"></lvl-button>
							${this.allowCopy ? html`<lvl-button icon="copy" label="${this.localize('copy')}" stacked @click="${this.handleCopy}" ?disabled="${!this.allowCopy}"></lvl-button>` : null}
							<lvl-button icon="pen" label="${this.localize('changeState')}" data-dropdown="state-dropdown" stacked ?disabled="${!this.allowStateChange || !this.workflowInfos}"></lvl-button>
							<lvl-dropdown name="state-dropdown" placement="${PopupPlacement.BottomEnd}" flip>
								<lvl-menu>
									${this.workflowInfos.length == 1 ? html`
										${repeat(this.workflowInfos, (workflowInfo: WorkflowInfo) => html`
												${repeat(workflowInfo.nodes?.sort(node => node.sorting) ?? [], (workflowNode: WorkflowNode) => this.renderWorkflowNode(workflowInfo, workflowNode))}
										`)}
									` : html`
										${repeat(this.workflowInfos, (workflowInfo: WorkflowInfo) => html`
											<lvl-menu-item>
												${workflowInfo.workflowName}
												<lvl-menu slot="submenu">
													${repeat(workflowInfo.nodes?.sort(node => node.sorting) ?? [], (workflowNode: WorkflowNode) => this.renderWorkflowNode(workflowInfo, workflowNode))}
												</lvl-menu>
											</lvl-menu-item>
										`)}
									`}
								</lvl-menu>
							</lvl-dropdown>
							<lvl-button icon="minus-circle" color="error" label="${this.localize('discard')}" stacked @click="${this.handleDiscard}" ?disabled="${!this.allowDiscard}"></lvl-button>
						</div>
					</div>` : null}
				</div>
			</div>
		`
	}

	//#region lifecycle callbacks
	
	connectedCallback() {
		super.connectedCallback()
		this.abortController = new AbortController()
		this.addEventListener('click', (event: MouseEvent) => {
			// re-fire click event on actual target inside the shadow root
			if (event.target == this) {
				const targetElement = this.shadowRoot?.elementFromPoint(event.clientX, event.clientY)
				if (targetElement != this)
					targetElement?.dispatchEvent(new MouseEvent('click', {bubbles: false}))
			}
		}, { signal: this.abortController.signal })
	}
	
	disconnectedCallback() {
		this.abortController?.abort()
		window.removeEventListener('click', this.handleWindowClick)
	}

	protected async willUpdate(_changedProperties: PropertyValues) {
		super.willUpdate(_changedProperties)

		// calling hide removes the element from the dom which improves blueprint viewer zoom performance
		if (_changedProperties.has("_hidden") && this.hidden && _changedProperties.get('_hidden') == undefined && this.parentElement)
			this.hide(true)
		
		// inform viewer that position was changed
		if (_changedProperties.has("position"))
			this.dispatchEvent(new CustomEvent<PinPositionType>("pin:position:changed", { detail: this.position, bubbles: true }))
	}
	
	//#endregion

	//#region public methods

	public getDimensions(zoom: number): PinDimensionType {
		// faster and more precise (because predicted by target zoom instead of calculated during active transitions)
		const pinWidthFull = 60
		const pinHeightFull = 80
		let calculatedWidth = (pinWidthFull/2 + (pinWidthFull/2 * zoom))
		let calculatedHeight = (pinHeightFull/2 + (pinHeightFull/2 * zoom))
		return { width: calculatedWidth, height: calculatedHeight }
	}
	
	public closeInfo = () => {
		this.open = false
		this.closing = true
		window.removeEventListener('click', this.handleWindowClick)
		setTimeout(() => {
			this.closing = false
		}, Pin.ANIMATION_TIME_SLOW)
	}
	
	//#endregion

	//#region private methods
	
	private _lastClickEvent: number = -1
	private handleClick() {
		if (!this.tooltip && !this.tooltipLegendTop && !this.tooltipLegendBottom && !this.previewEditMode)
			return

		// ignore second click if it happens within 500ms (double clicks shouldn't close the tooltip immediately after opening it)
		if (this._lastClickEvent && Date.now() - this._lastClickEvent < 500)
			return
		this._lastClickEvent = Date.now()
		
		this.open = !this.open
		if (this.open) {
			this.opening = true
			setTimeout(() => {
				this.opening = false
				
				// check tooltip dimensions and move viewport if necessary
				let tooltipDimensions = this.pinInfo.getBoundingClientRect()
				let viewerDimensions = this.closest('.openseadragon-container')?.getBoundingClientRect()
				
				if (viewerDimensions) {
					let offsetX = Math.abs(Math.min(tooltipDimensions.left - viewerDimensions.left - Pin.TOOLTIP_MIN_GAP, 0))
							offsetX = offsetX > 0 ? offsetX : Math.min(viewerDimensions.right - tooltipDimensions.right - Pin.TOOLTIP_MIN_GAP, 0)
					let offsetY = Math.abs(Math.min(tooltipDimensions.top - viewerDimensions.top - Pin.TOOLTIP_MIN_GAP, 0))
					
					if (offsetX != 0 || offsetY != 0)
						this.dispatchEvent(new CustomEvent<PinPositionType>('viewer:request-pan', { detail: {x: offsetX, y: offsetY}, bubbles: true }))
				}
				window.addEventListener('click', this.handleWindowClick)
			})
		}
	}
	
	private handleOpen() {
		if (!this.href)
			return
		
		if (typeof window.Page !== 'undefined')
			return window.Page.openNewTab(this.href, true)

		window.open(this.href)
	}

	private handleMoveStart(event: MouseEvent) {
		if (!this.allowMove)
			return
		
		// close tooltip
		this.open = false
		
		// remember current position before starting move
		if (this.position)
			this._resetPosition = { x: this.position.x, y: this.position.y }
		
		// mark current pin as moving and hide it
		this.moving = true
		this.transitions = false
		this.hidden = true

		// dispatch event to inform viewer about movement
		this.dispatchEvent(new CustomEvent<MouseEvent>('pin:move:start', {detail: event}))
	}
	
	// show pin
	public show(immediate: boolean = false) {
		if (!this.hidden)
			return
		this._showing = true
		if (immediate)
			this.hidden = false
		else if (!this.moving)
			this.transitions = true
		if (!this.parentElement && this._parentContainer) {
			this._parentContainer?.appendChild(this)
		}
		setTimeout(() => {
			this.hidden = false
			this._showing = false
		},25)
	}
	
	// hide pin
	private _parentContainer: HTMLElement | null = null
	public hide(immediate: boolean = false) {
		if (!this.parentElement)
			return
		
		this.hidden = true
		this.open = false
		if (immediate)
			this.skipTransitions()
	}
	
	public updatePosition(position: PinPositionType, immediate: boolean = false) {
		this.position = { x: position.x, y: position.y }
		if (immediate)
			this.skipTransitions()
	}

	// temporarily disable css transitions
	private skipTransitions() {
		this.transitions = false
		setTimeout(() => {
			this.transitions = true
		})
	}
	
	public resetPosition() {
		if (!this._resetPosition)
			return
		
		this.moving = false
		this.transitions = true
		this.resetting = true
		this.position = { x: this._resetPosition.x, y: this._resetPosition.y }
		setTimeout(() => {
			this.resetting = false
		}, Pin.ANIMATION_TIME_SLOW)
	}

	private handleCopy(event: MouseEvent) {
		if (!this.allowCopy)
			return

		// close tooltip
		this.open = false
		
		// dispatch event
		this.dispatchEvent(new CustomEvent<MouseEvent>('pin:copy:start', {detail: event}))
	}

	private handleDiscard() {
		if (!this.allowDiscard)
			return
		
		// close tooltip
		this.open = false

		// dispatch event
		this.dispatchEvent(new CustomEvent<Pin>('pin:delete', { detail: this }))
	}
	
	private handleWindowClick = (mouseEvent: MouseEvent) => {
		// we just want to handle clicks outside of the blueprint viewer here
		let targetElement = mouseEvent.target
		while (targetElement instanceof HTMLElement && targetElement.shadowRoot) {
			if (targetElement.tagName == 'LVL-BLUEPRINT' && targetElement.shadowRoot.contains(this))
				return
			targetElement = targetElement.shadowRoot.elementFromPoint(mouseEvent.clientX, mouseEvent.clientY)
		}
		
		this.closeInfo()
	}
	
	private renderWorkflowItem(workflowInfo: WorkflowInfo) {
		return html`<lvl-workflow-item size="${Size.Medium}"
																	 workflow-name="${workflowInfo.workflowName}"
																	 label="${workflowInfo.nodeName}"
																	 icon="${workflowInfo.nodeIcon}"
																	 state-type="${workflowInfo.state?.toLowerCase()}"></lvl-workflow-item>`
	}
	
	private renderWorkflowNode(workflowInfo: WorkflowInfo, workflowNode: WorkflowNode) {
		return html`<lvl-menu-item icon-left="${workflowNode.icon}" data-workflow-id="${workflowInfo.workflowId}" data-id="${workflowNode.id}" ?selected="${workflowNode.id == workflowInfo.nodeId}" @click="${this.handleNodeChange}">${workflowNode.name}</lvl-menu-item>`
	}
	
	private async handleNodeChange(event: MouseEvent) {
		const menuItem = event.target as DropdownMenuItem
		const workflowId = menuItem.dataset.workflowId
		const nodeId = menuItem.dataset.id
		if (!workflowId || !nodeId)
			return

		this.disabled = true
		this.open = false
		const patchUrl = `/Api/Workflows/${workflowId}/Elements/${this.id}/ChangeStatus/`
		const response = await CommunicationServiceProvider.post(patchUrl, nodeId, {
			headers: { 'Content-Type': 'application/json' },
		})
		
		if (response.state == CommunicationResponseType.Ok) {
			const workflowInfo = this.workflowInfos.find(workflowInfo => workflowInfo.workflowId === workflowId)!
			const nodeInfo = workflowInfo.nodes!.find(nodeInfo => nodeInfo.id == nodeId)!
			workflowInfo.nodeId = nodeId
			workflowInfo.nodeName = nodeInfo.name
			workflowInfo.nodeIcon = nodeInfo.icon
			workflowInfo.state = nodeInfo.state
		}
		this.disabled = false
	}
	
	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-pin': Pin
	}
}