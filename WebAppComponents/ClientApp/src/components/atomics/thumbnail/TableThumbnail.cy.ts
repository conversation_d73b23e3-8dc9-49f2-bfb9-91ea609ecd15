import { stories } from './TableThumbnail.stories'
import { storyTest } from '@test-home/support/advanced-functions'

import('./TableThumbnail')

// Test suite for the example web component
describe('<lvl-table-thumbnail />', () => {

	storyTest('checks the default component', stories.default, () => {
		cy.intercept('/Api/DataSources/**/Thumbnail*', {
			statusCode: 200,
			headers: {
				contentType: 'image/png',
			},
			fixture: 'thumbnailFile.png,null',
		}).as('thumbnail')
		
		cy.mountStory(stories.default)
		
		cy.wait('@thumbnail')
		cy.wait('@thumbnail')
		
		cy.get('lvl-table-thumbnail').shadow().find('img').should('be.visible')
		cy.get('lvl-table-thumbnail').trigger('mouseover')
		cy.get('lvl-table-thumbnail').shadow().find('.preview-content').find('img').should('exist')
		cy.get('lvl-table-thumbnail').shadow().find('.preview-content').contains('Torsten')
		cy.get('lvl-table-thumbnail').click()
		cy.get('lvl-table-thumbnail').shadow().find('lvl-dialog').should('have.attr', 'open')
		cy.get('lvl-table-thumbnail').shadow().find('lvl-dialog lvl-viewer').should('exist')
		cy.get('lvl-table-thumbnail').shadow().find('lvl-dialog').find('lvl-button[data-action="open"]').click({ force: true })
		cy.get('lvl-table-thumbnail').shadow().find('lvl-dialog').should('not.exist')
	})

	storyTest('checks component with no file attributes set', stories.noFile, () => {
		cy.mountStory(stories.noFile)

		cy.get('lvl-table-thumbnail').shadow().find('i').should('be.visible')
		cy.get('lvl-table-thumbnail').trigger('mouseover')
		cy.get('lvl-table-thumbnail').shadow().find('i').should('be.visible')
		cy.get('lvl-table-thumbnail').shadow().find('.preview-content').should('not.exist')
	})

	storyTest('checks component with file for which no file thumbnail can be generated', stories.unsupportedFileType, () => {
		cy.mountStory(stories.unsupportedFileType)

		cy.get('lvl-table-thumbnail').shadow().find('i').should('be.visible')
		cy.get('lvl-table-thumbnail').trigger('mouseover')
		cy.get('lvl-table-thumbnail').shadow().find('i').should('be.visible')
		cy.get('lvl-table-thumbnail').shadow().find('.preview-content').should('not.exist')
	})
})