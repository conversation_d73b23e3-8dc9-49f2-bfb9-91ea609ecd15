import type { <PERSON><PERSON>, <PERSON>Obj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands.ts'
import { PopupPlacement } from '@/components/popup/Popup.ts'
import { renderIcon } from '@/shared/component-html.ts'
import { TooltipType } from '@/components/atomics/tooltip/Tooltip.ts'
import { ifDefined } from 'lit-html/directives/if-defined.js'

import('./Tooltip')
import('@/components/atomics/button/Button')

/* 
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

export type TooltipStory = Story<Partial<TooltipType>>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-tooltip',
	tags: [ 'autodocs' ],
	render: (_args: Partial<TooltipType>) => html`
		<button data-tooltip="tooltip" style="margin:100px;height:26px;">${renderIcon('sparkles')}<span style="margin-left:5px;">Suspicious Button</span></button>
		<lvl-tooltip name="tooltip" placement="${_args.placement}" ?delayed="${_args.delayed}"
								 offset-block="${ifDefined(_args.blockOffset)}" offset-inline="${ifDefined(_args.inlineOffset)}"
								 offset-block-x="${ifDefined(_args.blockOffsetX)}" offset-block-y="${ifDefined(_args.blockOffsetY)}"
								 offset-inline-x="${ifDefined(_args.inlineOffsetX)}" offset-inline-y="${ifDefined(_args.inlineOffsetY)}">
			${_args.content}

		</lvl-tooltip>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		content: {
			control: 'text',
			description: 'content to display',
			table: {
				type: { summary: 'string' },
			},
		},
		placement: {
			control: 'select',
			description: 'default placement position if there is enough space',
			options: [
				PopupPlacement.Top,
				PopupPlacement.TopStart,
				PopupPlacement.TopEnd,
				PopupPlacement.Right,
				PopupPlacement.RightStart,
				PopupPlacement.RightEnd,
				PopupPlacement.Bottom,
				PopupPlacement.BottomStart,
				PopupPlacement.BottomEnd,
				PopupPlacement.Left,
				PopupPlacement.LeftStart,
				PopupPlacement.LeftEnd,
			],
			table: {
				type: { summary: 'PopupPlacement' },
				defaultValue: {
					summary: 'BottomStart',
				},
			},
		},
		blockOffset: {
			control: 'number',
			description: 'Offset if tooltip is placed on top / below the trigger element. Combined X/Y-Value.',
			table: {
				type: { summary: 'number' },
			},
		},
		inlineOffset: {
			control: 'number',
			description: 'Offset if tooltip is placed left / right of the trigger element. Combined X/Y-Value',
			table: {
				type: { summary: 'number' },
			},
		},
		blockOffsetX: {
			control: 'number',
			description: 'Offset if tooltip is placed on top / below the trigger element. X-Value.',
			table: {
				type: { summary: 'number' },
			},
		},
		blockOffsetY: {
			control: 'number',
			description: 'Offset if tooltip is placed on top / below the trigger element. Y-Value.',
			table: {
				type: { summary: 'number' },
			},
		},
		inlineOffsetX: {
			control: 'number',
			description: 'Offset if tooltip is placed left / right of the trigger element. X-Value',
			table: {
				type: { summary: 'number' },
			},
		},
		inlineOffsetY: {
			control: 'number',
			description: 'Offset if tooltip is placed left / right of the trigger element. Y-Value',
			table: {
				type: { summary: 'number' },
			},
		},
		delayed: {
			control: 'boolean',
			description: 'Should the tooltip be displayed after a certain time or immediately?',
			table: {
				type: { summary: 'boolean' },
			},
		},
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a default tooltip
 */
export const Default: TooltipStory = {
	args: {
		placement: PopupPlacement.RightStart,
		content: 'This is a cute little tooltip with more than one row but without any helpful content 😊',
	},
}

/**
 * Appearance of a nested tooltip
 */
export const Nested: TooltipStory = {
	args: {
		placement: PopupPlacement.RightStart,
		delayed: true
	},
	render: (_args: Partial<TooltipType>) => html`
		<div style="margin:100px; height:26px; display:flex; gap:1rem" data-tooltip="outer-tooltip">
			<span style="font-size: 2rem">Container</span>
			<button data-tooltip="tooltip">${renderIcon('sparkles')}<span style="margin-left:5px;">Suspicious Button</span></button>
			<lvl-tooltip name="tooltip" placement="${_args.placement}" ?delayed="${_args.delayed}"
									 offset-block="${ifDefined(_args.blockOffset)}" offset-inline="${ifDefined(_args.inlineOffset)}"
									 offset-block-x="${ifDefined(_args.blockOffsetX)}" offset-block-y="${ifDefined(_args.blockOffsetY)}"
									 offset-inline-x="${ifDefined(_args.inlineOffsetX)}" offset-inline-y="${ifDefined(_args.inlineOffsetY)}">
				Inner Tooltip
			</lvl-tooltip>
		</div>
		<lvl-tooltip name="outer-tooltip" placement="${_args.placement}" ?delayed="${_args.delayed}"
								 offset-block="${ifDefined(_args.blockOffset)}" offset-inline="${ifDefined(_args.inlineOffset)}"
								 offset-block-x="${ifDefined(_args.blockOffsetX)}" offset-block-y="${ifDefined(_args.blockOffsetY)}"
								 offset-inline-x="${ifDefined(_args.inlineOffsetX)}" offset-inline-y="${ifDefined(_args.inlineOffsetY)}">
			Outer Tooltip
		</lvl-tooltip>
	`
}

//#endregion

export const stories = {
	default: new LevelStory(meta, Default),
	nested: new LevelStory(meta, Nested, 'Nested tooltip'),
} as const