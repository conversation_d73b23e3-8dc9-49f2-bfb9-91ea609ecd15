import { LitElement, css, html } from 'lit'
import {customElement, property, state} from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { Popup, PopupPlacement } from '@/components/popup/Popup.ts'
import { ifDefined } from 'lit-html/directives/if-defined.js'
import { query } from 'lit/decorators/query.js'
import { ClassInfo, classMap } from 'lit/directives/class-map.js'

const DISPLAY_TIMEOUT = 800 as const

@customElement('lvl-tooltip')
export class Tooltip extends LitElement {

	static styles = [
		styles.base,
		styles.tooltip,
		css`
			:host {
				display: contents;
			}

			slot[name=trigger] {
				cursor: help;
			}

			.tooltip {
				display: inline-block;
				color: var(--cp-clr-text-primary-negativ);
				background-color: var(--cp-clr-background-lvl-0-tooltip);
				min-height: 3.2rem;
				line-height: 1.6;
				align-content: center;
				border-radius: var(--size-radius-m);
				user-select: none;
				pointer-events: none;
			}

			.tooltip-content {
				--max-lines: 8;
				
				display: -webkit-box;
				overflow: hidden;
				-webkit-line-clamp: var(--max-lines);
				-webkit-box-orient: vertical;
				
				width: max-content;
				max-width: 200px;
				max-height: calc(calc(var(-line-height) * 1em) * var(--max-lines));
				margin: var(--size-spacing-s) var(--size-spacing-m);
				white-space: normal;
			}

			.tooltip.medium .tooltip-content {
				max-width: 350px;
			}
			
			.tooltip.large .tooltip-content {
				max-width: 500px;
			}
		`,
	]

	@property({type: Number, attribute: 'offset-block'})
	blockOffset?: number

	@property({type: Number, attribute: 'offset-block-x'})
	blockOffsetX: number = 0

	@property({type: Number, attribute: 'offset-block-y'})
	blockOffsetY: number = 0

	@property({type: Number, attribute: 'offset-inline'})
	inlineOffset?: number

	@property({type: Number, attribute: 'offset-inline-x'})
	inlineOffsetX: number = 0

	@property({type: Number, attribute: 'offset-inline-y'})
	inlineOffsetY: number = 0

	@property()
	placement: PopupPlacement = PopupPlacement.RightStart
	
	@property({type: Boolean})
	orbit: boolean = false
	
	@property({type: Boolean})
	delayed: boolean = false

	@property()
	name?: string
	
	@property()
	size: TooltipSize = TooltipSize.Small

	get trigger(): HTMLElement | undefined {
		if (this._trigger)
			return this._trigger

		if (this.name) {
			let parentNode = this.parentElement || this.getRootNode() as ShadowRoot
			this._trigger = parentNode?.querySelector(`[data-tooltip="${this.name}"]`) || undefined
		}

		return this._trigger
	}
	private _trigger?: HTMLElement

	//#region states

	@state()
	visible: boolean = false

	@query('slot')
	private _slot!: HTMLSlotElement

	@query('.tooltip')
	private _tooltip!: HTMLSlotElement
	
	@query('lvl-popup')
	private _popup!: Popup
	
	private static hasMouseOver?: Tooltip

	private _timeout?: NodeJS.Timeout

	//#endregionn

	//#region Lit functions

	/**
	 * render component
	 */
	render() {
		const classes: ClassInfo = {
			tooltip: true,
			small: this.size == TooltipSize.Small,
			medium: this.size == TooltipSize.Medium,
			large: this.size == TooltipSize.Large
		}
		
		return html`
			<lvl-popup placement="${this.placement}" ?open="${this.visible}"
								 offset-block="${ifDefined(this.blockOffset || undefined)}"
								 offset-block-x="${ifDefined(this.blockOffsetX || undefined)}"
								 offset-block-y="${ifDefined(this.blockOffsetY || undefined)}"
								 offset-inline="${ifDefined(this.inlineOffset || undefined)}"
								 offset-inline-x="${ifDefined(this.inlineOffsetX || undefined)}"
								 offset-inline-y="${ifDefined(this.inlineOffsetY || undefined)}"
								 arrow-color="var(--cp-clr-background-lvl-0-tooltip)" arrow flip shift ?orbit="${this.orbit}">
				<div class="${classMap(classes)}">
					<div class="tooltip-content">
						<slot></slot>
					</div>
				</div>
			</lvl-popup>`
	}

	firstUpdated() {
		// attach mutation observer to tooltip-slot-nodes in order to dispatch slotchange events to popup slot (used for popup repositioning)
		let tooltip = this._tooltip
		let observer = new MutationObserver(() => {
			tooltip.assignedSlot!.dispatchEvent(new Event('slotchange'))
		})
		let options = { characterData: true, attributes: false, childList: true, subtree: true }
		for (const node of this._slot.assignedNodes()) {
			observer.observe(node, options)
		}
	}
	
	connectedCallback() {
		super.connectedCallback()
		this.trigger?.addEventListener('mouseover', this.handleMouseover)
		this.trigger?.addEventListener('mouseleave', this.handleMouseleave)
	}
	
	disconnectedCallback() {
		if (Tooltip.hasMouseOver === this)
			Tooltip.hasMouseOver = undefined
		this.trigger?.removeEventListener('mouseover', this.handleMouseover)
		this.trigger?.removeEventListener('mouseleave', this.handleMouseleave)
	}

	//#endregion

	//#region lifecycle callbacks

	// show on mouseover
	private handleMouseover = () => {
		clearTimeout(this._timeout)
		this._timeout = setTimeout(() => {
			if (Tooltip.hasMouseOver?.visible === false)
				Tooltip.hasMouseOver = undefined
			
			// ignore the mouseover of a tooltip which is already open
			if (Tooltip.hasMouseOver === this) {
				// fix popups which SHOULD be open but aren't (tooltip on blueprint fullscreen button is an example for this if clicked before tooltip opens)
				if (!this._popup.open)
					this._popup.open = true
				return
			}

			// if inner tooltips wants to claim the right then close the existing one
			const currentTooltipHolder = Tooltip.hasMouseOver?.trigger?.dataset?.tooltip
			if (currentTooltipHolder && this.trigger?.closest(`[data-tooltip=${currentTooltipHolder}]`) != null)
				Tooltip.hasMouseOver?.trigger?.dispatchEvent(new MouseEvent('mouseleave'))

			// claim tooltip
			Tooltip.hasMouseOver = this
			this.visible = true
			
		}, this.delayed ? DISPLAY_TIMEOUT : 0)
	}

	private handleMouseleave = () => {
		// revoke tooltip right
		if(Tooltip.hasMouseOver === this)
			Tooltip.hasMouseOver = undefined

		clearTimeout(this._timeout)
		this.visible = false
	}

	//#endregion

	//#region private methods

	//#endregion
}

export type TooltipType = {
	placement: PopupPlacement
	content: string
	blockOffset: number
	blockOffsetX: number
	blockOffsetY: number
	inlineOffset: number
	inlineOffsetX: number
	inlineOffsetY: number
	delayed: boolean
}

export const enum TooltipSize {
	Small = 'small',
	Medium = 'medium',
	Large = 'large'
}

declare global {
	interface HTMLElementTagNameMap {
		'lvl-tooltip': Tooltip
	}
}