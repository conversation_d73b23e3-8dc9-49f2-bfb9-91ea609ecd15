import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands.ts'
import { ValueFormatterType } from '@/components/atomics/value-formatter/ValueFormatter.ts'
import { DataType } from '@/enums/data-type.ts'
import { InputFieldType } from '@/components/inputs/InputElement.ts'
import { ifDefined } from 'lit-html/directives/if-defined.js'
import { Align } from '@/enums/align.ts'

import('./ValueFormatter')

/* 
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

type ValueFormatterProperties = Partial<ValueFormatterType> 
export type ValueFormatterStory = Story<ValueFormatterProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-value-formatter',
	tags: [ 'autodocs' ],
	render: (_args: ValueFormatterProperties) => html`
		<lvl-value-formatter name="${_args.name}" type="${_args.type}" value="${_args.value}" decimal-places="${_args.decimalPlaces}"
												 ?rich-text="${_args.richText}" text-align=${ifDefined(_args.textAlign)}
												 style="color:#333;font-size:12px;font-family:sans-serif;width:200px;padding:5px;background-color:#CCC;"></lvl-value-formatter>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		name: {
			control: 'text',
			description: 'unique name of the element<br/>(for changing its value later on via JS)',
			table: {
				type: { summary: 'string' },
			},
		},
		value: {
			control: 'text',
			description: 'value which gets parsed to the configured data type',
			table: {
				type: { summary: 'string' },
			},
		},
		type: {
			control: 'select',
			description: 'data type of the field',
			options: [
				DataType.String,
				DataType.Integer,
				DataType.Long,
				DataType.Double,
				DataType.Date,
				DataType.DateTime,
				DataType.Time,
				DataType.Text,
				DataType.Boolean,
				InputFieldType.Url,
				InputFieldType.Color,
			],
			table: {
				type: { summary: 'DataType' },
			},
		},
		decimalPlaces: {
			control: 'number',
			description: 'number of decimal places',
			table: {
				type: { summary: 'number' },
			},
			if: {
				arg: 'type',
				eq: DataType.Double,
			},
		},
		richText: {
			control: 'boolean',
			description: 'Is the content a json string to parse as rich text content?',
			table: {
				type: { summary: 'boolean' },
			},
			if: {
				arg: 'type',
				eq: DataType.Text,
			},
		},
		textAlign: {
			control: 'select',
			description: 'how should the text be aligned?',
			options: [ Align.Left, Align.Center, Align.Right ],
			table: {
				type: { summary: 'TextAlign' },
			},
		},
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Tag which helps to format simple values inside the DOM without the overhead of an input field or a shadow root.<br/>
 * <span style="color:red">Should <b>not</b> be used inside other Components! Use formatData() in this case!</span>
 */
export const Default: ValueFormatterStory = {
	args: {
		name: 'formatter1',
		value: '123456',
		type: DataType.Double,
		decimalPlaces: 2,
	},
}

export const RichText: ValueFormatterStory = {
	args: {
		name: 'formatter2',
		value: '{"type":"doc","content":[{"type":"paragraph","attrs":{"textAlign":"left"},"content":[{"type":"text","text":"The programs that "},{"type":"text","marks":[{"type":"textStyle","attrs":{"color":"hsl(0, 100%, 50%)"}},{"type":"bold"},{"type":"underline"}],"text":"Chuck Norris"},{"type":"text","text":" writes don\'t have version numbers because he only writes them once. If a user reports a bug or has a feature request they don\'t live to see the sun set"}]}]}',
		type: DataType.Text,
		richText: true,
	},
}

//#endregion

export const stories: Record<string, LevelStory<ValueFormatterStory>> = {
	default: new LevelStory(meta, Default),
	richText: new LevelStory(meta, RichText),
}