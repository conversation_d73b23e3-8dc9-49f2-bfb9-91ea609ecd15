import type { <PERSON><PERSON>, <PERSON>Obj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands'
import { ButtonGroupType } from './ButtonGroup'
import { ifDefined } from 'lit-html/directives/if-defined.js'

import('./ButtonGroup')
import('@/components/atomics/button/Button')
import('@/components/atomics/tooltip/Tooltip')
import('@/components/dropdown/Dropdown')
import('@/components/dropdown/DropdownMenu')
import('@/components/dropdown/DropdownMenuItem')
import('@/components/dropdown/DropdownMenuDivider')

/* 
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

type ButtonGroupProperties = Partial<ButtonGroupType>
type ButtonGroupStory = Story<ButtonGroupProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-button-group',
	tags: [ 'autodocs' ],
	render: (_args: ButtonGroupProperties) => html`
		<lvl-button-group style="margin:20px 0 50px" name="${_args.name}" value="${_args.value}" ?readonly="${_args.readonly}"
											?skeleton="${ifDefined(_args.skeleton)}">
			<lvl-button label="Alle" tooltip="Alle Datenquellen anzeigen" value="ALL"></lvl-button>
			<lvl-button label="Reale" tooltip="Nur reale Datenquellen anzeigen" value="REAL"></lvl-button>
			<lvl-button label="Virtuelle" tooltip="Nur virtuelle Datenquellen anzeigen" value="VIRTUAL"></lvl-button>
			<lvl-button label="Views" tooltip="Nur Views anzeigen" value="VIEWS"></lvl-button>
			<lvl-button label="Fehler" tooltip="Nur fehlerhafte Datenquellen anzeigen, der Rest interessiert gerade nicht" value="ERROR"></lvl-button>
		</lvl-button-group>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		name: {
			control: 'text',
			table: {
				type: { summary: 'text' },
			},
			description: 'name of the buttongroup (used to grab the value of this group)',
		},
		value: {
			control: 'select',
			options: [ 'ALL', 'REAL', 'VIRTUAL', 'VIEWS', 'ERROR' ],
			table: {
				type: { summary: 'text' },
			},
			description: 'currently selected value (if any)',
		},
		readonly: {
			control: 'boolean',
			description: 'if set to true, the group is grayed out and can not be toggled',
			table: {
				type: { summary: 'boolean' }
			}
		},
		skeleton: {
			control: 'boolean',
			description: 'if set to true, the input including the label are displayed as a pulsating bar (used as a loading animation while the data is not ready)',
			table: {
				type: { summary: 'boolean' },
			},
		}
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a Buttongroup with a few selectable buttons
 */
export const Default: ButtonGroupStory = {
	args: {
		name: 'defaultGroup',
		value: 'REAL',
		readonly: false,
		skeleton: false,
	}
}

/**
 * Appearance of a Buttongroup with some disabled buttons
 */
export const DisabledButtons: ButtonGroupStory = {
	render: (_args: ButtonGroupProperties) => html`
		<lvl-button-group style="margin:20px 0 50px" name="${_args.name}" value="${_args.value}" ?readonly="${_args.readonly}" ?skeleton="${ifDefined(_args.skeleton)}">
			<lvl-button label="Alle" tooltip="Alle Datenquellen anzeigen" value="ALL"></lvl-button>
			<lvl-button label="Reale" tooltip="Nur reale Datenquellen anzeigen" value="REAL" disabled></lvl-button>
			<lvl-button label="Virtuelle" tooltip="Nur virtuelle Datenquellen anzeigen" value="VIRTUAL"></lvl-button>
			<lvl-button label="Views" tooltip="Nur Views anzeigen" value="VIEWS"></lvl-button>
			<lvl-button label="Fehler" tooltip="Nur fehlerhafte Datenquellen anzeigen, der Rest interessiert gerade nicht" value="ERROR" disabled></lvl-button>
		</lvl-button-group>
	`,
	args: {
		name: 'disabledButtonsGroup',
		value: 'ALL'
	}
}

/**
	* Appearance of a readonly Buttongroup
*/
export const Readonly: ButtonGroupStory = {
	args: {
		name: 'readonlyGroup',
		value: 'REAL',
		readonly: true
	}
}

/**
 * Appearance of a Buttongroup with non-selectable buttons
 */
export const NoSelect: ButtonGroupStory = {
	render: (_args: ButtonGroupProperties) => html`
		<lvl-button-group style="margin:20px 0">
			<lvl-button label="Button 1" tooltip="First Button" onclick="alert('Button 1 pressed');"></lvl-button>
			<lvl-button label="Button 2" tooltip="Second Button" onclick="alert('Button 2 pressed');"></lvl-button>
			<lvl-button label="Button 3" tooltip="Third Button" onclick="alert('Button 3 pressed');"></lvl-button>
		</lvl-button-group>
	`,
	args: {
		name: 'noSelectGroup',
		value: 'REAL'
	}
}

/**
 * Appearance of a Buttongroup containing a dropdown
 */
export const Dropdown: ButtonGroupStory = {
	render: (_args: ButtonGroupProperties) => html`
		<lvl-button-group style="margin:0 0 150px">
			<lvl-button label="Button 1" tooltip="First Button" onclick="alert('Button 1 pressed');"></lvl-button>
			<lvl-button label="Button 2" tooltip="Second Button" onclick="alert('Button 2 pressed');"></lvl-button>
			<lvl-button label="Button 3" tooltip="Third Button" data-dropdown="exampleDropdown"></lvl-button>
			<lvl-dropdown name="exampleDropdown">
				<lvl-menu background-color="var(--cp-clr-state-inactive)" border-color="var(--cp-clr-state-inactive)">
					<lvl-menu-item>Item 1</lvl-menu-item>
					<lvl-menu-item>Item 2</lvl-menu-item>
					<lvl-menu-divider></lvl-menu-divider>
					<lvl-menu-item>Item 3</lvl-menu-item>
					<lvl-menu-item>
						Item 4
						<lvl-menu slot="submenu">
							<lvl-menu-item>Subitem 1</lvl-menu-item>
							<lvl-menu-item>Subitem 2</lvl-menu-item>
							<lvl-menu-item>
								Subitem 3
								<lvl-menu slot="submenu">
									<lvl-menu-item>Bla</lvl-menu-item>
									<lvl-menu-item>Blub</lvl-menu-item>
									<lvl-menu-item>ASD</lvl-menu-item>
									<lvl-menu-item>123</lvl-menu-item>
								</lvl-menu>
							</lvl-menu-item>
						</lvl-menu>
					</lvl-menu-item>
					<lvl-menu-item>Item 5</lvl-menu-item>
				</lvl-menu>
			</lvl-dropdown>
		</lvl-button-group>
	`,
	args: {
		name: 'dropdownGroup',
		value: 'REAL'
	}
}

/**
 * Appearance of a loading Buttongroup
 */
export const Skeleton: ButtonGroupStory = {
	args: {
		name: 'skeletonGroup',
		value: 'REAL',
		readonly: true,
		skeleton: true,
	},
}

/**
 * Appearance of a Buttongroup with icons only
 */
export const IconGroup: ButtonGroupStory = {
	render: (_args: ButtonGroupProperties) => html`
		<lvl-button-group name="${_args.name}" value="${_args.value}" style="width: 400px">
			<lvl-button value="dog" icon="dog"></lvl-button>
			<lvl-button value="cat" icon="cat"></lvl-button>
			<lvl-button value="democrat" icon="democrat"></lvl-button>
		</lvl-button-group>
	`,
	args: {
		name: 'animals',
		value: 'cat'
	}
}

//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
	disabled: new LevelStory(meta, DisabledButtons, 'Disabled Button'),
	readonly: new LevelStory(meta, Readonly, 'Readonly ButtonGroup'),
	noSelect: new LevelStory(meta, NoSelect, 'No Selected Buttons'),
	dropdown: new LevelStory(meta, Dropdown, 'Button with Dropdown')
} as const