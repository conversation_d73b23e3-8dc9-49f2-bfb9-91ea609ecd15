import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, query, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import CommunicationServiceProvider, { CommunicationResponseType } from '@/shared/communication-service.ts'
import { Operator, operatorHR, toOperatorType } from '@/enums/operator.ts'
import { classMap } from 'lit/directives/class-map.js'
import { DataType, toDataType } from '@/enums/data-type.ts'
import { Autocomplete } from '@/components/inputs/autocomplete/Autocomplete.ts'
import { Input } from '@/components/inputs/input/Input.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { SortDirection } from '@/enums/sort-direction.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'

export type FilterConfigType = {
	url?: string
}

type FilterConditionType = {
	id: string,
	filterFieldId: string
	filterFieldName: string
	filterFieldType: DataType
	operator: Operator
	compareValue: string
}

/**
 * Grid used to position draggable grid-elements
 */
@customElement('lvl-filter-config')
export class FilterConfig extends LitElement implements FilterConfigType {

	static styles = [
		styles.base,
		styles.color,
		styles.animation,
		styles.skeleton,
		fontAwesome,
		css`
			:host {
			}

			.filter-condition {
				display: flex;
				align-items: center;
				padding-left: var(--size-spacing-m);
				cursor: pointer;
			}

			.filter-condition-text {
				display: flex;
				gap: var(--size-spacing-s);
				flex-grow: 1;
			}
			
			.filter-value {
				font-weight: bold;
			}

			.action-row {
				padding: var(--size-spacing-s) 0 var(--size-spacing-s) var(--size-spacing-m);
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: var(--size-spacing-m);
			}
			
			.create-row:not(.show-create) .create-input,
			.create-row:not(.show-create) .create-button,
			.create-row:not(.show-create) lvl-button[data-action=abort],
			.create-row:not(.show-create) lvl-button[data-action=save] {
				display:none;
			}

			.create-row.show-create lvl-button[data-action=add],
			.create-row.edit-active lvl-button[data-action=add] {
				display: none;
			}
			
			.empty-filter-list {
				height: var(--size-text-xxl);
				padding: var(--size-spacing-m) 0;
				margin: 0 var(--size-spacing-m);
				color: var(--cp-clr-text-tertiary);
				display: flex;
				align-items: center;
				gap: var(--size-spacing-m);
				justify-content: center;
			}
		`
	]

	//#region attributes

	@property()
	public url?: string
	
	@property({ attribute: 'field-url' })
	public fieldUrl?: string

	@property({ attribute: 'create-url' })
	private _createUrl?: string

	set createUrl(url: string | undefined) {
		this._createUrl = url
	}
	
	get createUrl(): string | undefined {
		return this._createUrl || this.url
	}
	
	@property({ type: Boolean, attribute: 'multi-group' })
	public multiGroup: boolean = false

	@property({ attribute: 'parent-element-key' })
	parentElementKey: string = ""

	@property({ attribute: 'parent-element-value' })
	parentElementValue?: string
	
	//#endregion

	//#private properties
	
	@state()
	private filters: FilterConditionType[] = []
		
	@state()
	private showCreate: boolean = false
	
	@state()
	private filterFieldType?: DataType | null

	@state()
	private loading: boolean = false
	
	@state()
	private activeFilterId?: string
	
	@query('lvl-autocomplete[name="filter-field-id"]')
	private _htmlFilterField!: Autocomplete

	@query('lvl-input[name="compare-value"]')
	private _htmlCompareValue!: Input

	private static readonly _localizer: StringLocalizer = new StringLocalizer('FilterConfig')
	
	private localize(key: string) {
		return FilterConfig._localizer.localize(key)
	}
	
	//#endregion

	//#region Lit functions

	// (re-)render component
	render() {
		let classList = {
			'action-row': true,
			'create-row': true,
			'show-create': this.showCreate,
			'edit-active': this.activeFilterId != null && this.activeFilterId != "",
		}
		
		return html`
			${this.renderFilters()}
			<div class="${classMap(classList)}">
				<lvl-autocomplete url="${this.fieldUrl}" class="create-input" name="filter-field-id" required @change="${this.handleFilterFieldChange}"></lvl-autocomplete>
				<lvl-input class="create-input" name="compare-value" compare-operator="equals" negative-operators ?readonly="${this.filterFieldType == null}" target-type="${this.filterFieldType || 'string'}"></lvl-input>
				<lvl-button data-action="abort" type="secondary" size="medium" color="active" label="${this.localize('abort')}" @click="${this.hideCreateInputs}"></lvl-button>
				<lvl-button data-action="save" type="primary" size="medium" icon="save" label="${this.localize('save')}" @click="${this.handleFilterCreate}"></lvl-button>
				<lvl-button data-action="add" type="secondary" size="medium" icon="plus-circle" color="active" label="${this.localize('addFilter')}"
										?skeleton="${this.loading}" @click="${this.showCreateInputs}"></lvl-button>
			</div>
		`
	}

	//#endregion

	//#region lifecycle callbacks

	private renderFilters() {
		if (this.filters.length == 0) {
			let classList = {
				'empty-filter-list': true,
				'skeleton__block': this.loading
			}
			return html`
				<div class="${classMap(classList)}">
					<i class="fal fa-filter-slash"></i>
					<span>${this.localize('noConfiguredFilters')}</span>
				</div>
			`
		}

		return this.filters.map(filter => this.renderFilter(filter))
	}
	
	protected updated(changedProperties: PropertyValues<this>) {
		// list existing filters
		if (changedProperties.has('url') || changedProperties.has('parentElementValue')) {
			this.clear()
			this.queryFilters()
		}
	}

	//#endregion

	//#region public methods

	public clear() {		
		this.resetCreateInputs()
		this.activeFilterId = undefined
	}
	
	//#endregion

	//#region private methods

	private showCreateInputs() {
		this.showCreate = true
	}

	private hideCreateInputs() {
		this.showCreate = false
		this.resetCreateInputs()
	}
	
	private resetCreateInputs() {
		this._htmlFilterField.clear()
		this._htmlCompareValue.compareOperator = Operator.Equals
		this._htmlCompareValue.clear()
		this.filterFieldType = null
	}
	
	private handleFilterCreate() {
		if (!this.createUrl)
			return
		
		CommunicationServiceProvider.post(this.createUrl, null, {
			body: {
				[this.parentElementKey]: this.parentElementValue,
				filterFieldId: this._htmlFilterField.value,
				operator: toOperatorType(this._htmlCompareValue.compareOperator),
				compareValue: this._htmlCompareValue.value
			}
		}).then(() => {
			this.hideCreateInputs()
			this.queryFilters()
		}).catch((e) => {
			console.error('Failed to create new filter condition', e)
			this.hideCreateInputs()
		})
	}
	
	private handleFilterClick(event: MouseEvent) {
		// we don't want create and edit to be active at the same time 
		if (this.showCreate)
			return
		
		const filterId = (event.target as HTMLElement).closest(".filter-condition")!.id
		if (!this.createUrl || !filterId)
			return
		
		this.activeFilterId = filterId
	}
	
	private async handleFilterDelete(event: MouseEvent) {
		// prevent click event from bubbling up and triggering the filter edit mode
		event.stopPropagation();
		
		const filterId = (event.target as HTMLElement).closest(".filter-condition")!.id
		if (!this.createUrl || !filterId)
			return
			
		await CommunicationServiceProvider.delete(this.createUrl, filterId, {
			headers: { 'Content-Type': 'application/json' },
		})
		this.queryFilters()
	}
	
	private handleFilterFieldChange(event: Event) {
		const filterField = (event.target as Autocomplete)
		this.filterFieldType = filterField.value ? toDataType(filterField.columnValues["type"]) : null
	}
	
	private handleEditAbort() {
		this.activeFilterId = undefined
		this.filterFieldType = undefined
	}
	
	private handleEditSubmit() {
		if (!this.createUrl || !this.activeFilterId)
			return

		CommunicationServiceProvider.patch(this.createUrl, this.activeFilterId, {
			body: {
				filterFieldId: this._htmlFilterField.value,
				operator: toOperatorType(this._htmlCompareValue.compareOperator),
				compareValue: this._htmlCompareValue.value
			}
		}).then((response) => {
			const currentFilter = this.filters.find(filter => filter.id == this.activeFilterId)
			this.activeFilterId = undefined
			
			if (!currentFilter || response.state != CommunicationResponseType.Ok)
				return this.queryFilters()
			
			// update current filter instead of querying all filters
			currentFilter.filterFieldId = response.data.filterFieldId
			currentFilter.filterFieldName = response.data.filterFieldName
			currentFilter.filterFieldType = toDataType(response.data.filterFieldType)!
			currentFilter.operator = toOperatorType(response.data.operator)!
			currentFilter.compareValue = response.data.compareValue
			
		}).catch((e) => {
			console.error('Failed to update filter condition', e)
			this.activeFilterId = undefined
		})
	}
	
	private renderFilter(filter: FilterConditionType) {
		if (this.activeFilterId && filter.id == this.activeFilterId) {
			// set fieldType of filter if the edit section isn't already open
			this.filterFieldType = this.filterFieldType || filter.filterFieldType
			return html`
			<div class="action-row" id="${filter.id}">
				<lvl-autocomplete url="${this.fieldUrl}" class="edit-input" name="filter-field-id" required @change="${this.handleFilterFieldChange}" display-value="${filter.filterFieldName}" value="${filter.filterFieldId}"></lvl-autocomplete>
				<lvl-input class="edit-input" name="compare-value" compare-operator="equals" negative-operators ?readonly="${this.filterFieldType == null}" value="${filter.compareValue}" compare-operator="${filter.operator}" target-type="${this.filterFieldType || 'string'}"></lvl-input>
				<lvl-button data-action="abort" type="secondary" size="medium" color="active" label="${this.localize('abort')}" @click="${this.handleEditAbort}"></lvl-button>
				<lvl-button data-action="save" type="primary" size="medium" icon="save" label="${this.localize('save')}" @click="${this.handleEditSubmit}"></lvl-button>
			</div>
		`
		}
			
		return html`
			<div class="filter-condition" id="${filter.id}" @click="${this.handleFilterClick}">
				<div class="filter-condition-text">
					<b>${filter.filterFieldName}</b>
					<span class="filter-operator">${operatorHR(filter.operator)}</span>
					<span class="filter-value ${filter.operator == Operator.IsNull || filter.operator == Operator.IsNotNull ? 'hidden' : ''}">${filter.compareValue}</span>
				</div>
				<!--lvl-button icon="pen" tooltip="${this.localize("edit")}"></lvl-button-->
				<lvl-button data-action="delete" icon="trash" color="error" tooltip="${this.localize("delete")}" @click="${this.handleFilterDelete}"></lvl-button>
			</div>
		`
	}
	
	private queryFilters() {
		this.filters = []
		
		// clear filters and abort if parent element id is not set
		if (!this.url || !this.parentElementValue) {
			this.loading = false
			return
		}
		
		this.loading = true
		CommunicationServiceProvider.get(this.url, {
			searchParams: {
				filters: [
					{ filterColumn: this.parentElementKey, operator: Operator.Equals, compareValue: this.parentElementValue }
				],
				sortings: [
					{ orderColumn: "Created", direction: SortDirection.Asc }
				]
			}
		}).then((response) => {
			if(response.state != CommunicationResponseType.Ok) {
				this.loading = false
				return
			}
			this.filters = response.data.rows.map((row: Record<string, any>)  => { row.operator = toOperatorType(row.operator); row.filterFieldType = toDataType(row.filterFieldType); return row; })
			this.loading = false
		})
	}
	
	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-filter-config': FilterConfig
	}
}