import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands'
import { FilterPanelType } from './FilterPanel'
import { FilterPanelSectionType } from '@/components/data-organizers/filter-panel/FilterPanelSection.ts'
import { ifDefined } from 'lit/directives/if-defined.js'
import { DataType } from '@/enums'
import { http } from 'msw'
import { getSuccessCountResolver, getSuccessListResolver } from '@test-home/support/advanced-functions.ts'
import states from '@test-home/fixtures/state-grouping.json'
import { Operator } from '@/enums/operator.ts'
import { DateSpan } from '@/enums/date-span.ts'
import { BooleanFormat } from '@/enums/boolean-format.ts'

import('@/components/atomics/button/Button')
import('@/components/button-group/ButtonGroup')
import('@/components/list-views/select-list/SelectListItem')
import('@/components/list-views/select-list/SelectList')
import('@/components/atomics/fry/Fry')
import('./FilterPanelSection')
import('./FilterPanel')


/* 
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

type FilterPanelProperties = Partial<FilterPanelType & {
	sections: Partial<FilterPanelSectionType>[]
}>
type FilterPanelStory = Story<FilterPanelProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-filter-panel',
	render: (_args: FilterPanelProperties) => html`
		<lvl-filter-panel ?with-favorite="${_args.withFavorite}" ?with-inactive="${_args.withInactive}" ?open="${_args.open}"
											?skeleton="${_args.skeleton}"
											count-url="${ifDefined(_args.countUrl)}" .predefinedFilters="${ifDefined(_args.predefinedFilters)}"
											search-text="${ifDefined(_args.searchText)}" style="height: 600px">
			${_args.sections?.map(section => html`
				<lvl-filter-panel-section
					label="${ifDefined(section.label)}"
					name="${ifDefined(section.name)}"
					type="${ifDefined(section.type)}"
					?collapsed="${section.collapsed}"
					?multi-value="${section.multiValue}"
					?value-preview="${section.valuePreview}"
					value="${ifDefined(section.value)}"
					placeholder="${ifDefined(section.placeholder)}"
					format-type="${ifDefined(section.formatType)}"
					label-true="${ifDefined(section.labelTrue)}"
					label-false="${ifDefined(section.labelFalse)}"
					url="${ifDefined(section.url)}"
					?hidden="${section.hidden}"
					possible-values="${ifDefined(section.possibleValues ? JSON.stringify(section.possibleValues) : undefined)}"
				>
				</lvl-filter-panel-section>
			`)}
		</lvl-filter-panel>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		favorite: {
			control: 'boolean',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '' },
			},
			description: 'Filter by favorites',
		},
		rejected: {
			control: 'boolean',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '' },
			},
			description: 'Filter by rejected records',
		},
		offline: {
			control: 'boolean',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '' },
			},
			description: 'Filter by offline records',
		},
		open: {
			control: 'boolean',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '' },
			},
			description: 'Opens or closes the panel',
		},
		searchText: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '' },
			},
			description: 'Fulltext search value',
		},
	},
	parameters: {
		msw: {
			handlers: [
				http.get('/filter-panel/statename*', async ({ request }) => {
					return getSuccessListResolver(request, states, true)
				}),
				http.get('/filter-panel/count*', async ({ request }) => {
					return getSuccessCountResolver(request, states, true)
				}),
			],
		},
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a default FilterPanel without attributes
 */
export const Default: FilterPanelStory = {
	args: {
		open: true,
		sections: [
			{ label: 'Paid', name: 'paid', formatType: BooleanFormat.CustomText, labelTrue: 'Soon', labelFalse: 'Not yet', type: DataType.Boolean },
			{ label: 'State name', name: 'stateName', multiValue: true, valuePreview: true, url: '/filter-panel/statename/multi' },
			{ label: 'Project number', name: 'projectNumber', type: DataType.Integer, placeholder: 'e.g. 6543210' },
			{ label: 'Projects', name: 'projects', multiValue: true, placeholder: 'Add projects to filter by' },
			{ label: 'Single State name', name: 'stateNameSingle', valuePreview: true, url: '/filter-panel/statename/single' },
			{ label: 'Date', name: 'date', type: DataType.Date },
			{
				label: 'Enum', name: 'enum', type: DataType.Enum, valuePreview: true,
				possibleValues: [
					{ label: 'Einzeldatensatz', value: 'SingleData' }, { label: 'Auflistung', value: 'MultiData' }, { label: 'Formular', value: 'Create' },
				],
			},
		],
		countUrl: '/filter-panel/count',
	},
}

/**
 * Appearance of a default FilterPanel without attributes
 */
export const Skeleton: FilterPanelStory = {
	args: {
		open: true,
		skeleton: true,
		sections: [
			{ label: 'Paid', name: 'paid', labelTrue: 'Soon', labelFalse: 'Not yet', type: DataType.Boolean },
			{ label: 'State name', name: 'stateName', multiValue: true, valuePreview: true, url: '/filter-panel/statename/multi' },
			{ label: 'Project number', name: 'projectNumber', type: DataType.Integer, placeholder: 'e.g. 6543210' },
			{ label: 'Projects', name: 'projects', multiValue: true, placeholder: 'Add projects to filter by' },
			{ label: 'Single State name', name: 'stateNameSingle', valuePreview: true, url: '/filter-panel/statename/single' },
			{ label: 'Date', name: 'date', type: DataType.Date },
		],
	},
}

/**
 * Appearance of a FilterPanel with predefined values
 */
export const Preset: FilterPanelStory = {
	args: {
		withFavorite: true,
		withInactive: true,
		open: true,
		sections: [
			{ label: 'Paid', name: 'paid', labelTrue: 'Soon', labelFalse: 'Not yet', type: DataType.Boolean },
			{ label: 'State name', name: 'stateName', multiValue: true, valuePreview: true, url: '/filter-panel/statename/multi' },
			{ label: 'Project number', name: 'projectNumber', type: DataType.Integer, placeholder: 'e.g. 6543210' },
			{ label: 'Projects', name: 'projects', multiValue: true, placeholder: 'Add projects to filter by' },
			{ label: 'Single State name', name: 'stateNameSingle', valuePreview: true, url: '/filter-panel/statename/single' },
			{ label: 'Date', name: 'date', type: DataType.Date },
			{
				label: 'Enum',
				name: 'enum',
				type: DataType.Enum,
				valuePreview: true,
				possibleValues: [
					{ label: 'Einzeldatensatz', value: 'SingleData' }, { label: 'Auflistung', value: 'MultiData' }, { label: 'Formular', value: 'Create' },
				],
			},
		],
		predefinedFilters: [
			{ filterColumn: 'paid', operator: Operator.Equals, compareValue: false },
			{ filterColumn: 'stateName', operator: Operator.Contains, compareList: [ { value: states[0].value, label: states[0].label } ] },
			{ filterColumn: 'projectNumber', operator: Operator.Equals, compareValue: 123456 },
			{ filterColumn: 'stateNameSingle', operator: Operator.Equals, compareValue: states[2].value, compareLabel: states[2].label },
			{ filterColumn: 'date', operator: Operator.Equals, compareValue: DateSpan.Today },
			{ filterColumn: 'enum', operator: Operator.Equals, compareValue: 'SingleData' },
			{ filterColumn: '', operator: Operator.Favorite },
			{ filterColumn: '', operator: Operator.Inactive },
		],
		countUrl: '/filter-panel/count',
	},
}


//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
	preset: new LevelStory(meta, Preset, 'Preset value'),
} as const