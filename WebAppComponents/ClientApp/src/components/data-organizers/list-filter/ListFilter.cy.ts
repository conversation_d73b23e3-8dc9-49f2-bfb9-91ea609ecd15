import { stories } from './ListFilter.stories'
import { getMockedSuccessResponse, storyTest } from '@test-home/support/advanced-functions'
import { DataColumnDefinitionType } from '@/components/list-views/DataEnumerationDataColumn.ts'
import { DataType } from '@/enums/data-type.ts'
import employees from '@test-home/fixtures/employees.json'
import { DataFilter, DataSorting } from '@/shared/types.ts'
import { FilterField, ListFilter } from '@/components/data-organizers/list-filter/ListFilter.ts'

import('@/components/dropdown/Dropdown')
import('@/components/dropdown/DropdownMenu')
import('@/components/dropdown/DropdownMenuItem')
import('./ListFilter')

// Test suite for the example web component
describe('<lvl-list-filter />', () => {

	storyTest('checks the default component', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('lvl-list-filter').shadow().find('lvl-search-field-menu').first().shadow().find('lvl-menu').should('exist')
		cy.get('lvl-button[icon="bars-filter"]', {timeout:10000}).as('filterButton')
			.should('exist').should('not.have.attr', 'data-popup-open')
		cy.get('@filterButton').click({ force: true })
		cy.get('@filterButton').should('have.attr', 'data-popup-open')
		cy.get('@filterButton').click({ force: true })
	})

	storyTest('checks filtering', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('lvl-list-filter').shadow().find('lvl-search-field-menu').first().shadow().find('lvl-menu').should('exist')
		cy.get('lvl-button[icon="bars-filter"]').click({ force: true })
		cy.get('lvl-dropdown[name="filter-dropdown"] > lvl-menu').as('baseMenu')
		cy.get('@baseMenu').find('> lvl-menu-item:nth-of-type(1)').as('firstFilterColumn')
		cy.get('@firstFilterColumn').should('exist').should('not.have.attr', 'submenu-open')
		cy.get('@firstFilterColumn').click({ force: true })
		cy.wait(100)
		cy.get('@firstFilterColumn').should('have.attr', 'sub-menu-open')
		cy.get('@firstFilterColumn').click({ force: true })
		cy.wait(100)
		cy.get('@firstFilterColumn').should('not.have.attr', 'sub-menu-open')
		
		// open a boolean column and click on it to toggle between activated and deactivated
		const columns = stories.default.getAttribute<Array<DataColumnDefinitionType>>('columns')!
		const boolColumn = columns.find(column => column.type === DataType.Boolean)
		expect(boolColumn).to.be.not.null

		cy.get('@baseMenu').find('> lvl-menu-item:not(sortable)').contains(boolColumn?.label!).as('boolItem')
		cy.get('@boolItem').click({ force: true })
		cy.get('lvl-menu[slot="submenu"] lvl-menu-item[data-value="true"]').as('boolTrueItem')
		cy.get('lvl-menu[slot="submenu"] lvl-menu-item[data-value="false"]').as('boolFalseItem')
		
		// click on activated
		cy.get('@boolTrueItem').should('not.have.attr', 'icon-right', 'check')
		cy.get('@boolTrueItem').click({ force: true })
		cy.get('@boolTrueItem').should('have.attr', 'icon-right', 'check')
		
		// click on deactivated should remove the check mark on the 'activated' menu item
		cy.get('@boolFalseItem').should('not.have.attr', 'icon-right', 'check')
		cy.get('@boolFalseItem').click({ force: true })
		cy.get('@boolFalseItem').should('have.attr', 'icon-right', 'check')
		cy.get('@boolTrueItem').should('not.have.attr', 'icon-right', 'check')
		
		// click on a selected item should unselect it
		cy.get('@boolFalseItem').click({ force: true })
		cy.get('@boolFalseItem').should('not.have.attr', 'icon-right', 'check')

		// open a date column and click on it to toggle between today and yesterday
		const dateColumn = columns.find(column => column.type === DataType.Date)
		expect(dateColumn).to.be.not.null

		cy.get('@baseMenu').find('> lvl-menu-item:not(sortable)').contains(dateColumn?.label!).as('dateItem')
		cy.get('@dateItem').click({ force: true })
		cy.get('lvl-menu[slot="submenu"] lvl-menu-item[data-value="today"]').as('todayItem')
		cy.get('lvl-menu[slot="submenu"] lvl-menu-item[data-value="yesterday"]').as('yesterdayItem')

		// click on today
		cy.get('@todayItem').should('not.have.attr', 'icon-right', 'check')
		cy.get('@todayItem').click({ force: true })
		cy.get('@todayItem').should('have.attr', 'icon-right', 'check')

		// click on yesterday should remove the check mark on the 'today' menu item
		cy.get('@yesterdayItem').should('not.have.attr', 'icon-right', 'check')
		cy.get('@yesterdayItem').click({ force: true })
		cy.get('@yesterdayItem').should('have.attr', 'icon-right', 'check')
		cy.get('@todayItem').should('not.have.attr', 'icon-right', 'check')

		// click on a selected item should unselect it
		cy.get('@yesterdayItem').click({ force: true })
		cy.get('@yesterdayItem').should('not.have.attr', 'icon-right', 'check')
	})

	storyTest('checks sorting', stories.default, () => {
		cy.mountStory(stories.default)
		
		cy.get('lvl-list-filter').shadow().find('lvl-search-field-menu').first().shadow().find('lvl-menu').should('exist')
		const columns = stories.default.getAttribute<Array<DataColumnDefinitionType>>('columns')!
		cy.get('lvl-button[icon="bars-filter"]').click({ force: true })
		cy.get('lvl-dropdown[name="filter-dropdown"] > lvl-menu').as('baseMenu')
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[0].label!).as('firstColumn')
			.should('exist')
		
		// check toggling of sort columns
		cy.get('@firstColumn').click({ force: true })
		cy.get('@firstColumn').should('have.attr', 'sorting', 'asc')
		cy.get('@firstColumn').click({ force: true })
		cy.get('@firstColumn').should('have.attr', 'sorting', 'desc')
		cy.get('@firstColumn').click({ force: true })
		cy.get('@firstColumn').should('have.attr', 'sorting', 'asc')
		cy.get('@firstColumn').click({ force: true })
		cy.get('@firstColumn').should('have.attr', 'sorting', 'desc')
		cy.get('@firstColumn').click({ ctrlKey: true })

		// check priority - order by column 3 at first, then column 2. Columns 2 should be after 3
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[2].label!).then((element) => {
			const firstSortIndex = element.parent().children('lvl-menu-item[sortable]').first().index()
			const startIndex = element.index()
			expect(startIndex).to.be.above(firstSortIndex)

			cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[2].label!).click({ ctrlKey: true })
			cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[1].label!).click({ ctrlKey: true })
			cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[2].label!).then((_element) => {
				expect(_element.index()).to.equal(firstSortIndex)
			})
			cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[1].label!).then((_element) => {
				expect(_element.index()).to.equal(firstSortIndex + 1)
			})
			cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[2].label!).click({ ctrlKey: true })
			cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[2].label!).then((_element) => {
				expect(_element.index()).to.equal(startIndex)
			})
			cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[1].label!).click({ ctrlKey: true })
			cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[1].label!).then((_element) => {
				expect(_element.index()).to.equal(startIndex-1)
			})
		})

		// check deselect - order by column 1 at first, then click on column 2 which should deselect column 1
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[0].label!).click({ force: true })
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[0].label!).should('have.attr', 'sorting', 'asc')
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[1].label!).click({ force: true })
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[1].label!).should('have.attr', 'sorting', 'asc')
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[0].label!).should('have.attr', 'sorting', '')
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[2].label!).click({ force: true })
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[2].label!).should('have.attr', 'sorting', 'asc')
		cy.get('@baseMenu').find('lvl-menu-item[sortable]').contains(columns[1].label!).should('have.attr', 'sorting', '')
	})

	storyTest('checks the init component', stories.initSortingAndFilters, () => {
		cy.intercept('GET', `/employees*`, async request => {
			const parameterList = new URLSearchParams(request.url.split('?')[1])
			let response = await getMockedSuccessResponse(employees, parameterList,50)
			request.reply(response)
		})
		
		cy.mountStory(stories.initSortingAndFilters)
		
		cy.get('lvl-list-filter').shadow().find('lvl-search-field-menu').first().shadow().find('lvl-menu').should('exist')
		cy.get('lvl-button[icon="bars-filter"]').click({ force: true })
		cy.get('lvl-dropdown[name="filter-dropdown"] > lvl-menu').as('baseMenu')
		
		// check preselected filter options
		const filters = stories.initSortingAndFilters.getAttribute<Array<DataFilter>>('filters')!
		filters?.forEach(filter => {
			cy.get('@baseMenu').find(`lvl-menu-item[data-column="${filter.filterColumn}"]:not([sortable])`)
				.click({ force: true })
				.find(`lvl-menu lvl-menu-item[data-value="${filter.compareValue}"]`)
				.should('exist')
				.should('have.attr', 'icon-right', 'check')
		})
		
		// check preselected sorting options
		const sortings = stories.initSortingAndFilters.getAttribute<Array<DataSorting>>('sortings')!
		sortings?.forEach(sorting => {
			cy.get('@baseMenu').find(`lvl-menu-item[data-column="${sorting.orderColumn}"][sortable]`)
				.should('exist')
				.should('have.attr', 'sorting', sorting.direction)
		})
	})

	storyTest('validate filter and sorting', stories.default, () => {
		cy.intercept('GET', `/employees*`, async request => {
			const parameterList = new URLSearchParams(request.url.split('?')[1])
			let response = await getMockedSuccessResponse(employees, parameterList,50)
			request.reply(response)
		})

		cy.mountStory(stories.default)
		cy.get('lvl-list-filter').shadow().find('lvl-search-field-menu').first().shadow().find('lvl-menu').should('exist')
		cy.get('lvl-button[icon="bars-filter"]').click({ force: true })
		cy.get('lvl-dropdown[name="filter-dropdown"] > lvl-menu').as('baseMenu')

		// check preselected filter options
		const filters = stories.initSortingAndFilters.getAttribute<Array<DataFilter>>('filters')!
		filters?.forEach(filter => {
			cy.get('@baseMenu').find(`lvl-menu-item[data-column="${filter.filterColumn}"]:not([sortable])`).click({ force: true })
			cy.get(`lvl-menu lvl-menu-item[data-value="${filter.compareValue}"]`)
				.should('exist')
				.click({ force: true })
			cy.get('@baseMenu').find(`lvl-menu-item[data-column="${filter.filterColumn}"]:not([sortable])`).click({ force: true })
		})

		// check preselected sorting options
		const sortings = stories.initSortingAndFilters.getAttribute<Array<DataSorting>>('sortings')!
		sortings?.forEach(sorting => {
			cy.get('@baseMenu').find(`lvl-menu-item[data-column="${sorting.orderColumn}"][sortable]`).click({ ctrlKey: true, force: true })
			if(sorting.direction === 'desc')
				cy.get('@baseMenu').find(`lvl-menu-item[data-column="${sorting.orderColumn}"][sortable]`).click({ force: true })
			cy.get('@baseMenu').find(`lvl-menu-item[data-column="${sorting.orderColumn}"][sortable]`)
				.should('have.attr', 'sorting', sorting.direction)
		})

		// compare presets with current filter
		cy.get('lvl-list-filter').then(element => {
			const listFilter = element[0] as ListFilter
			const filters = [ ...listFilter.filters ]
			filters.forEach(filter => filter.compareLabel = undefined)
			expect(listFilter.sortings).to.deep.equals(sortings)
			expect(listFilter.filters).to.deep.equals(filters)
		})
	})
	
	storyTest('checks enum type usage', stories.default, () => {
		cy.intercept('GET', `/employees*`, async request => {
			const parameterList = new URLSearchParams(request.url.split('?')[1])
			let response = await getMockedSuccessResponse(employees, parameterList,50)
			request.reply(response)
		})

		cy.mountStory(stories.default)
		cy.get('lvl-list-filter').shadow().find('lvl-search-field-menu').first().shadow().find('lvl-menu').should('exist')
		cy.get('lvl-button[icon="bars-filter"]').click({ force: true })
		cy.get('lvl-dropdown[name="filter-dropdown"] > lvl-menu').as('baseMenu')

		const column = stories.default.getAttribute<Array<FilterField>>('columns')?.find(column => column.type === DataType.Enum)
		if(column && column.values != null){
			cy.get('@baseMenu').find(`lvl-menu-item[data-column="${column.name}"]:not([sortable])`).click({ force: true })
			column.values?.forEach(entry => {
				cy.get('@baseMenu').find(`lvl-menu-item[data-value="${entry.value}"]`)
					.should('exist')
					.should('contain.text', entry.label)
					.click({ force: true })
			})
			
			cy.get('lvl-list-filter').then(filterElement => {
				const filters = (filterElement[0] as ListFilter).filters
				filters.forEach(filter => {
					expect(filter.filterColumn).to.equal(column.name)
					expect(column.values!.map(entry => entry.value)).to.include(filter.compareValue)
					expect(column.values!.map(entry => entry.label)).to.include(filter.compareLabel)
				})
			})
		}
	})
	
	storyTest('prefilters another column', stories.default, () => {
		cy.intercept('GET', `/employees*`, async request => {
			const parameterList = new URLSearchParams(request.url.split('?')[1])
			let response = await getMockedSuccessResponse(employees, parameterList,50)
			request.reply(response)
		})

		cy.mountStory(stories.default)
		cy.get('lvl-list-filter').shadow().find('lvl-search-field-menu').first().shadow().find('lvl-menu').should('exist')
		cy.get('lvl-button[icon="bars-filter"]').click({ force: true })
		cy.get('lvl-dropdown[name="filter-dropdown"] > lvl-menu').as('baseMenu')
		
		cy.get('@baseMenu').find('lvl-menu-item[data-column="lastName"]:not([sortable])').click({ force: true })
		cy.get('@baseMenu').find('lvl-menu-item[data-value="Walter"]', { timeout: 10000 }).click({ force: true })

		// only one item should still exist because we only have 1 record with Walter as last name
		cy.get('@baseMenu').find('lvl-menu-item[data-column="firstName"]:not([sortable])').as('firstNameItem').click({ force: true })
		cy.get('@firstNameItem').find('lvl-menu-item[data-value="Martin"]').should('exist')
		cy.get('@firstNameItem').find('.results lvl-menu-item').should('have.length', 1)
	})
})