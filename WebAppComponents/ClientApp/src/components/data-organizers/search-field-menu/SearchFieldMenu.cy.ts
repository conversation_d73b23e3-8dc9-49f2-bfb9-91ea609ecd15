import employees from '@test-home/fixtures/employees.json'
import { stories } from './SearchFieldMenu.stories'
import { getMockedSuccessResponse, storyTest } from '@test-home/support/advanced-functions'
import { SearchFieldMenu } from '@/components/data-organizers/search-field-menu/SearchFieldMenu.ts'
import { DataEntryType } from '@/shared/dtos.ts'

import('./SearchFieldMenu')

// Test suite for the example web component
describe('<lvl-search-field-menu />', () => {
	
	beforeEach(() => {
		cy.intercept('GET', `/employees*`, async request => {
			const parameterList = new URLSearchParams(request.url.split('?')[1])
			let response = await getMockedSuccessResponse(employees, parameterList, 50)
			request.reply(response)
		})
	})

	storyTest('checks the basic functionality', stories.preselected, () => {
		cy.mountStory(stories.preselected)
		
		// check that all init elected results are checked
		const selectedResults = stories.preselected.getAttribute<Array<DataEntryType>>('selectedResults')?.sort() ?? []
		const resultSize = selectedResults?.length ?? 0
		cy.get('lvl-search-field-menu').shadow().get('lvl-menu').then(() => {
			selectedResults?.forEach((result, index) => {
				cy.get(`.results lvl-menu-item[icon-right="check"]:nth-of-type(${index + 1})`)
					.should('exist')
					.should('contain.text', result.label)
			})
		})
		
		// let results load data ~500ms
		cy.get('.skeleton__block').should('not.exist')
		cy.get('.results > lvl-menu-item').should('have.length.above', resultSize)
		
		// check select and unselect functionality
		cy.get('.results').find(`lvl-menu-item:nth-of-type(${(resultSize + 1)})`)
			.should('exist')
			.should('not.have.a.property', 'icon-right', 'check')
		cy.get('.results').find(`lvl-menu-item:nth-of-type(${(resultSize + 1)})`).then(element => {
			const name = element.data('value')
			cy.get('lvl-menu-item[data-value="'+ name +'"]').click()
			cy.get('.results').find('lvl-menu-item[icon-right="check"]').should('have.length', resultSize + 1)
			cy.get('lvl-menu-item[data-value="'+ name +'"]').click()
			cy.get('.results').find('lvl-menu-item[icon-right="check"]').should('have.length', resultSize)
		})
		
		// unselect initial ones
		cy.get('.results').then(() => {
			selectedResults?.forEach((result) => {
				cy.get(`.results lvl-menu-item[icon-right="check"]:nth-of-type(1)`)
					.should('exist')
					.should('contain.text', result.label)
					.click()
			})
		})
		cy.get('.results').find('lvl-menu-item[icon-right="check"]').should('have.length', 0)
		
		// last test only if there are some preselected values
		if(resultSize == 0)
			return
		
		// search for the first preselected name
		cy.get('#searchInput').shadow().find('input').as('searchInput').type(selectedResults[0].label)
		cy.get('.results').find('lvl-menu-item').should('have.length', 1)
		cy.get('.results lvl-menu-item').contains(selectedResults[0].label)
			.should('exist')
			.click()
		cy.get('.results lvl-menu-item').contains(selectedResults[0].label)
			.should('have.attr', 'icon-right', 'check')
		
		// first preselected name should be in the last search list
		cy.get('lvl-menu > lvl-menu-item:nth-of-type(1)')
			.should('have.attr', 'icon-left', 'clock-rotate-left')
			.should('contain.text', selectedResults[0].label)
	})

	storyTest('put more values in the search list than possible', stories.default, () => {
		cy.mountStory(stories.default)
		cy.get('.skeleton__block').should('not.exist')
		cy.get('#searchInput').shadow().find('input').as('searchInput').clear().type('1').blur({force:true})
		cy.get('@searchInput').clear().type('2').blur({force:true})
		cy.get('@searchInput').clear().type('3').blur({force:true})
		cy.get('@searchInput').clear().type('4').blur({force:true})

		cy.get('lvl-menu > lvl-menu-item').should('have.length', 3)
		cy.get('.skeleton__block').should('not.exist')
		
		//only 2-4 should be listed
		cy.get('lvl-menu > lvl-menu-item:nth-of-type(3)').should('exist').should('contain.text', '2')
		cy.get('lvl-menu > lvl-menu-item:nth-of-type(2)').should('exist').should('contain.text', '3')
		cy.get('lvl-menu > lvl-menu-item:nth-of-type(1)').should('exist').should('contain.text', '4')
	})

	storyTest('uses the last searched entries to filter', stories.default, () => {
		cy.mountStory(stories.default)
		cy.get('.skeleton__block').should('not.exist')

		// put two values in the selected list
		const selectedResults = stories.preselected.getAttribute<Array<DataEntryType>>('selectedResults')?.sort() ?? []
		cy.get('#searchInput').shadow().find('input').as('searchInput').clear().type(selectedResults[0].label).blur({force:true})
		cy.get('.results').find('lvl-menu-item').should('have.length', 1)
		cy.get('.results lvl-menu-item').contains(selectedResults[0].label)
			.should('exist')
		
		cy.get('@searchInput').clear().type(selectedResults[1].label).blur({force:true})
		cy.get('.results').find('lvl-menu-item').should('have.length', 1)
		cy.get('.results lvl-menu-item').contains(selectedResults[1].label)
			.should('exist')
		
		// click on the 'last search' entry we typed at first
		cy.get('lvl-menu > lvl-menu-item:nth-of-type(2)')
			.should('have.attr', 'icon-left', 'clock-rotate-left')
			.should('contain.text', selectedResults[0].label)
			.click()
		cy.get('.results').find('lvl-menu-item').should('have.length', 1)
		cy.get('.results lvl-menu-item').contains(selectedResults[0].label)
			.should('exist')
	})

	storyTest('check the correct display of each value', stories.reference, () => {
		cy.mountStory(stories.reference)
		cy.get('.results').as('results')

		const dataList = stories.reference.getAttribute<Array<Record<string, any>>>('data')
		const name = stories.reference.getAttribute<string>('refField') ?? ''
		const label = stories.reference.getAttribute<string>('field') ?? ''
		dataList?.forEach(data => {
			cy.get('@results').contains(data[label]).should('exist').click()
		})

		cy.get('lvl-search-field-menu').then(menu => {
			(menu[0] as SearchFieldMenu).selectedResults?.forEach(selected => {
				expect(dataList?.some(data => data[name] === selected.value)).to.be.true
			})
		})
	})
})