import { stories } from './Section.stories'
import { storyTest } from '@test-home/support/advanced-functions'

import('@/components/list-views/multi-data-view/table/Table')
import('@/components/list-views/multi-data-view/MultiDataView')
import('./Section')

// Test suite for the example web component
describe('<lvl-section />', () => {

	storyTest('change the heading', stories.default, () => {
		cy.mountStory(stories.default)
		
		cy.get('.section__title').should('contain.text', stories.default.getAttribute('heading'))
		cy.get('lvl-section').invoke('attr', 'heading', 'test')
		cy.get('.section__title').should('contain.text', 'test')
	})

	storyTest('change the subtitle', stories.subtitle, () => {
		cy.mountStory(stories.subtitle)

		cy.get('.section__subtitle').should('contain.text', stories.subtitle.getAttribute('subtitle'))
		cy.get('lvl-section').invoke('attr', 'subtitle', 'test')
		cy.get('.section__subtitle').should('contain.text', 'test')
	})
	
	storyTest('checks collapse function', stories.forbiddenCollapse, () => {
		cy.mountStory(stories.forbiddenCollapse)
		
		cy.get('lvl-section').as('section')
		cy.get('@section').should('not.have.attr', 'collapsed')
		cy.get('.section__content').invoke('outerHeight').should('be.greaterThan', 0)
		
		// collapse button should be gone 
		cy.get('@section').find('.section__toggle').should('not.exist')
		
		// enable collapse button
		cy.get('@section').invoke('attr', 'allow-collapse', '')
		cy.get('@section').should('not.have.attr', 'collapsed')
		cy.get('@section').find('.section__toggle').as('sectionToggle').should('exist')
		
		// minimize section
		cy.get('@sectionToggle').click()
		cy.get('@section').should('have.attr', 'collapsed')
		cy.get('.section__content').invoke('outerHeight').should('equal', 0)
	})

	storyTest('hides the section', stories.default, () => {
		cy.mountStory(stories.default)
		
		cy.get('lvl-section').as('section').invoke('attr', 'hidden', '')
		cy.get('@section')
	})

	storyTest('checks the correct height', stories.maxHeight, () => {
		cy.mountStory(stories.maxHeight)

		cy.get('section').invoke('outerHeight').should('equal', stories.maxHeight.getAttribute<number>('maxHeight'))
	})

	storyTest('checks the correct height when an embedded table is embedded', stories.embeddedTable, () => {
		cy.mountStory(stories.embeddedTable)

		const sectionHeight = stories.embeddedTable.getAttribute<number>('maxHeight') || 0
		
		cy.get('lvl-multi-data-view').shadow().find('.view').should('exist')
		cy.get('lvl-table').shadow().find('.table__row:not(.skeleton)').should('exist')
		cy.get('lvl-section').invoke('outerHeight').should('equal', sectionHeight)
		cy.get('.section__header').invoke('outerHeight').then((headerHeight) => cy.get('lvl-multi-data-view').invoke('outerHeight').should('be.lessThan', Math.round(Number(sectionHeight - headerHeight!))))
	})
})