import { storyTest } from '@test-home/support/advanced-functions.ts'
import { stories } from './DateTimePicker.stories.ts'

import('@/components/date-time-picker/DateTimePicker')

describe('<lvl-date-time-picker />', () => {
	storyTest('checks date time picker options', stories.default, () => {
		cy.mountStory(stories.default)
		cy.mockBrowserLanguage('en')
		cy.get('#testPicker').invoke('attr', 'value', '10/11/2026 12:02 PM')

		// date time picker is showing
		cy.get('#testPicker').find('.date-picker-container').should('be.visible')
		cy.get('#testPicker').find('.time-picker-container').should('be.visible')
		cy.get('#testPicker').find('.time-picker-button-panel lvl-button:nth-child(2)').should('not.exist')

		// correct values should be selected/displayed (date: 10/11/2026 12:02 PM)
		cy.get('#testPicker').find('.calendar-box.day[data-day="11"]').should('have.class', 'selected')
		cy.get('#testPicker').find('.date-header lvl-button[data-dropdown="monthSelectDropdown"]').as('monthButton').should('have.attr', 'label', 'October')
		cy.get('#testPicker').find('.date-header lvl-button[data-dropdown="yearSelectDropdown"]').as('yearButton').should('have.attr', 'label', '2026')
		cy.get('#testPicker').find('.hour-wheel').as('hourWheel').find('span.clickable').should('have.text', '12')
		cy.get('#testPicker').find('.minute-wheel').as('minuteWheel').find('span.clickable').should('have.text', '02')
		cy.get('#testPicker').find('.day-period-switch lvl-button-group').as('dayPeriodSwitch').should('have.value', 'PM')

		// selected on click
		cy.get('#testPicker').find('.calendar-box.day[data-day="16"]').click()
		cy.get('#testPicker').find('.calendar-box.day[data-day="16"]').should('have.class', 'selected')

		// buttons for switching hours
		cy.get('@hourWheel').find('> lvl-button:last-child').click().click()
		cy.get('@hourWheel').find('> lvl-button:first-child').click()
		cy.get('@hourWheel').find('span.clickable').should('have.text', '11')
		cy.get('@dayPeriodSwitch').should('have.value', 'PM')

		// buttons für switching minutes
		cy.get('@minuteWheel').find('> lvl-button:last-child').click().click().click()
		cy.get('@hourWheel').find('span.clickable').should('have.text', '11')
		cy.get('@minuteWheel').find('span.clickable').should('have.text', '59')
		cy.get('@minuteWheel').find('> lvl-button:first-child').click().click().click().click().click().click()
		cy.get('@hourWheel').find('span.clickable').should('have.text', '11')
		cy.get('@minuteWheel').find('span.clickable').should('have.text', '05')

		// button for AM/PM
		cy.get('@dayPeriodSwitch').should('have.value', 'PM')
		cy.get('@dayPeriodSwitch').find('lvl-button[value="AM"]').click()
		cy.get('@dayPeriodSwitch').should('have.value', 'AM')

		// buttons for switching the month
		cy.get('#testPicker').find('.picker-header.date-header lvl-button[data-action="previous-month"]').as('prevMonthButton').click()
		cy.get('@monthButton').should('have.attr', 'label', 'September')
		cy.get('@yearButton').should('have.attr', 'label', '2026')
		cy.get('#testPicker').find('.picker-header.date-header lvl-button[data-action="next-month"]').as('nextMonthButton').click().click().click().click().click()
		cy.get('@monthButton').should('have.attr', 'label', 'February')
		cy.get('@yearButton').should('have.attr', 'label', '2027')
		cy.get('@prevMonthButton').click().click().click()
		cy.get('@monthButton').should('have.attr', 'label', 'November')
		cy.get('@yearButton').should('have.attr', 'label', '2026')

		// opening the month selection
		cy.get('@monthButton').click()
		// selecting a different month
		cy.get('#testPicker').find('lvl-dropdown[name="monthSelectDropdown"]').as('monthDropdown').should('have.attr', 'open')
		cy.get('@monthDropdown').find('lvl-select-list-item[label="November"]').should('have.attr', 'selected')
		cy.get('@monthDropdown').find('lvl-select-list-item[label="August"]').should('not.have.attr', 'selected')
		cy.get('@monthDropdown').find('lvl-select-list-item[label="August"]').click()
		cy.get('@monthDropdown').should('not.have.attr', 'open')
		cy.get('@monthButton').should('have.attr', 'label', 'August')

		// opening the year selection
		cy.get('@yearButton').click()
		// selecting a different year
		cy.get('#testPicker').find('lvl-dropdown[name="yearSelectDropdown"]').as('yearDropdown').should('have.attr', 'open')
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2026"]').should('have.attr', 'selected')
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2028"]').should('not.have.attr', 'selected')
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2028"]').click()
		cy.get('@yearDropdown').should('not.have.attr', 'open')
		cy.get('@yearButton').should('have.attr', 'label', '2028')

		// testing mouse wheel functionality in year selection
		cy.get('@yearButton').click()
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2028"]').should('have.attr', 'selected')
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2023"]').should('exist')
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2022"]').should('not.exist')
		// scroll up
		cy.get('@yearDropdown').find('lvl-select-list').as('selectList').trigger('wheel', { deltaY: -100, force: true })
		cy.wait(150)
		cy.get('@selectList').trigger('wheel', { deltaY: -100, force: true })
		cy.wait(500)
		cy.get('@selectList').trigger('wheel', { deltaY: -100, force: true })
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2022"]').should('exist')
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2021"]').should('not.exist')
		
		// scroll down
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2038"]').should('exist')
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2039"]').should('not.exist')
		cy.get('@yearDropdown').find('lvl-select-list').as('selectList').trigger('wheel', { deltaY: 100, force: true })
		cy.wait(150)
		cy.get('@selectList').trigger('wheel', { deltaY: 100, force: true })
		cy.wait(150)
		cy.get('@selectList').trigger('wheel', { deltaY: 100, force: true })
		cy.wait(500)
		cy.get('@selectList').trigger('wheel', { deltaY: 100, force: true })
		cy.get('@yearDropdown').find('lvl-select-list-item[label="2039"]').should('exist')
		cy.get('@yearButton').click()

		// testing mouse wheel functionality for hours
		cy.get('@hourWheel').trigger('wheel', { deltaY: 1 })
		cy.get('@hourWheel').find('span.clickable').should('have.text', '10')
		cy.get('@dayPeriodSwitch').should('have.value', 'AM')
		cy.get('@hourWheel').trigger('wheel', { deltaY: -1 }).trigger('wheel', { deltaY: -1 }).trigger('wheel', { deltaY: -1 })
		cy.get('@hourWheel').find('span.clickable').should('have.text', '01')
		cy.get('@dayPeriodSwitch').should('have.value', 'AM')

		// testing mouse wheel functionality for minutes
		cy.get('@minuteWheel').trigger('wheel', { deltaY: 1 })
		cy.get('@minuteWheel').find('span.clickable').should('have.text', '04')
		cy.get('@dayPeriodSwitch').should('have.value', 'AM')
		cy.get('@minuteWheel').trigger('wheel', { deltaY: -1 }).trigger('wheel', { deltaY: -1 }).trigger('wheel', { deltaY: -1 })
		cy.get('@minuteWheel').find('span.clickable').should('have.text', '07')
		cy.get('@dayPeriodSwitch').should('have.value', 'AM')

		// testing "today" button 
		cy.get('#testPicker').find('.date-picker-button-panel lvl-button:first-child').click()
		let date = new Date()
		cy.get('@monthButton').should('have.attr', 'label', `${date.toLocaleString('en-US', { month: 'long' })}`)
		cy.get('@yearButton').should('have.attr', 'label', `${date.getFullYear()}`)
		cy.get('#testPicker').find(`.calendar-box.day[data-day="${date.getDate()}"]`).should('have.class', 'today').should('have.class', 'selected')
		

		// testing "now" button
		cy.get('#testPicker').find('.time-picker-button-panel lvl-button:first-child').click()
		date = new Date()
		let currentHour: number
		new Intl.DateTimeFormat('en-US', { hour: 'numeric' }).formatToParts(date).map(part => {
			if (part.type === 'hour') {
				currentHour = parseInt(part.value)
				return
			}
		})
		let currentMinute = date.getMinutes()
		cy.get('@hourWheel').find('span.clickable').then(($span) => {
			const dataHour = parseInt($span.text())
			cy.expectNumbersInRange(currentHour, dataHour, 1)
		})
		cy.get('@minuteWheel').find('span.clickable').then(($span) => {
			let dataMinute = parseInt($span.text())
			if (dataMinute == 59 && currentMinute == 0) {
				dataMinute = -1
			} else if (dataMinute == 0 && currentMinute == 59) {
				currentMinute = -1
			}
			cy.expectNumbersInRange(currentMinute, dataMinute, 1)
		})
	})

	storyTest('checks time picker rendering', stories.time, () => {
		cy.mountStory(stories.time)
		cy.mockBrowserLanguage('en')
		cy.get('#testPicker').invoke('attr', 'value', '12:30 PM')
		
		// only time picker is showing
		cy.get('#testPicker').find('.date-picker-container').should('not.exist')
		cy.get('#testPicker').find('.time-picker-container').as('timePicker').should('be.visible')
		cy.get('@timePicker').find('.time-picker-button-panel lvl-button:nth-child(2)').should('exist')

		// correct values should be selected/displayed (time: 12:30 PM)
		cy.get('#testPicker').find('.hour-wheel span.clickable').should('have.text', '12')
		cy.get('#testPicker').find('.minute-wheel span.clickable').should('have.text', '30')
		cy.get('#testPicker').find('.day-period-switch lvl-button-group').should('have.value', 'PM')
	})
})