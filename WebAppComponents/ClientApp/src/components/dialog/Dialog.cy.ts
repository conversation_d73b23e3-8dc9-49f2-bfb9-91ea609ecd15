import { stories } from './Dialog.stories.ts'
import { storyTest } from '@test-home/support/advanced-functions'

import('./Dialog.ts')

// Test suite for the example web component
describe('<lvl-dialog />', () => {

	storyTest('checks the main content', stories.content, () => {
		cy.mountStory(stories.content)

		cy.get('#dialog main slot')
			.then($slot => {
				const assignedSlot = ($slot[0] as HTMLSlotElement).assignedNodes()[0]
				expect(assignedSlot.textContent).to.contain(stories.content.getAttribute('content'))
			})

		cy.get('lvl-dialog').invoke('html', '<h2>This is a new content</h2>')
		cy.get('#dialog main slot')
			.then($slot => {
				const assignedSlot = ($slot[0] as HTMLSlotElement).assignedNodes()[0]
				expect(assignedSlot.firstChild!.textContent).to.contain('This is a new content')
			})
	})

	storyTest('open and closes a dialog', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('lvl-dialog').as('component').invoke('attr', 'open', true)
		cy.get('#dialog').as('dialog').should('be.visible')

		cy.get('@component').invoke('removeAttr', 'open')
		cy.get('@dialog').should('be.not.visible')
	})

	storyTest('closes dialog via buttons and events', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('lvl-dialog').as('component').invoke('attr', 'open', true)
		cy.get('#dialog').as('dialog').should('be.visible')

		// Cancel button
		cy.get('[data-action=cancel]').click({ force: true })
		cy.get('@component').should('not.have.attr', 'open')

		// Close button
		cy.get('@component').invoke('attr', 'open', true)
		cy.get('[data-action=close]').click({ force: true })
		cy.get('@component').should('not.have.attr', 'open')

		// Fixed: Escape on keyboard
		cy.get('@component').invoke('attr', 'open', true)
		cy.get('#dialog').trigger('keydown', { key: 'Escape' })
		cy.get('@component').should('not.have.attr', 'open')
	})

	storyTest('shows label and icon', stories.titleAndIcon, () => {
		cy.mountStory(stories.titleAndIcon)

		cy.get('#dialog header h1').as('heading').should('contain.text', stories.titleAndIcon.getAttribute('heading'))
		cy.get('#dialog header .icon:not([data-action=close])').as('icon').should('exist')

		cy.get('lvl-dialog').invoke('attr', 'heading', '')
		cy.get('@heading').should('not.contain.text')

		cy.get('lvl-dialog').invoke('attr', 'icon', '')
		cy.get('@icon').should('not.exist')
	})

	storyTest('hides header', stories.noHeader, () => {
		cy.mountStory(stories.noHeader)

		cy.get('#dialog header').should('not.exist')
	})
})