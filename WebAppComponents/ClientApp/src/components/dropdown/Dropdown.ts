import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { PopupPlacement } from '@/components/popup/Popup.ts'
import { Intensity } from '@/enums/intensity.ts'
import { ifDefined } from 'lit-html/directives/if-defined.js'

export type DropdownGroups = Record<string, Dropdown[]>
let dropdownGroups: DropdownGroups = {}

@customElement('lvl-dropdown')
export class Dropdown extends LitElement implements DropdownType {

	static styles = [
		styles.base,
		styles.animation,
		css`
			:host {
				display: contents;
				font-size: 0;
			}

			lvl-menu {
				font-size: 12px;
				background-color: var(--clr-tooltip-background);
				display: block;
			}
			
			::slotted(lvl-select-list) {
				min-width: 18rem;
				background-color: var(--cp-clr-background-lvl-0);
			}
		`,
	]

	//#region attributes

	@property()
	placement: PopupPlacement = PopupPlacement.BottomStart

	@property({ type: Boolean })
	flip: boolean = false

	@property({ type: Boolean })
	shift: boolean = false

	@property({ type: Boolean })
	orbit: boolean = false

	get trigger(): HTMLElement | undefined {

		if (this._trigger)
			return this._trigger

		if (this.name) {
			let parentNode = this.parentElement || this.getRootNode() as ShadowRoot
			this._trigger = parentNode?.querySelector(`[data-dropdown="${this.name}"]`) || undefined
		}

		return this._trigger
	}

	private _trigger?: HTMLElement

	private _intersectionObserver?: IntersectionObserver

	//#endregion

	//#region states

	@property({ type: Boolean, reflect: true })
	open: boolean = false

	@property()
	name?: string

	/**
	 * Keep the dropdown open, if its content was clicked
	 */
	@property({ type: Boolean, attribute: 'keep-open' })
	keepOpen?: boolean = false

	@property({ attribute: 'border-radius' })
	borderRadius?: string

	@property({ attribute: 'box-shadow' })
	boxShadow: Intensity = Intensity.Medium

	@property()
	border?: string

	@property({ type: Number, attribute: 'offset-block-x' })
	blockOffsetX: number = 0

	@property({ type: Number, attribute: 'offset-block-y' })
	blockOffsetY: number = 0

	@property({ type: Number, attribute: 'offset-inline-x' })
	inlineOffsetX: number = 0

	@property({ type: Number, attribute: 'offset-inline-y' })
	inlineOffsetY: number = 0

	//#endregion
	
	//#region private properties
	
	private get dropdownGroupId(): string {
		return this.dataset['dropdownGroup'] ?? '_'
	}
	
	private get dropdownGroup(): Dropdown[] | null {
		return dropdownGroups != null ? dropdownGroups[this.dropdownGroupId] : null
	}
	
	private abortController?: AbortController
	
	//#endregion

	//#region LIT Functions

	/**
	 * remove appended event listeners when component gets disconnected
	 */
	disconnectedCallback() {
		this.abortController?.abort()
		this._intersectionObserver?.disconnect()
		this.unregisterDropdown()
	}

	/**
	 * append event listeners
	 */
	connectedCallback() {
		super.connectedCallback()
		this.registerDropdown()

		if (!this.trigger)
			return

		this.abortController = new AbortController()

		this.trigger.addEventListener('click', this.handleClick.bind(this), { signal: this.abortController.signal})
		this.addEventListener('click', this.handleDropdownClick, { signal: this.abortController.signal})
		document.documentElement.addEventListener('keydown', this.handleKeydown.bind(this), { signal: this.abortController.signal})
	}

	/**
	 * render component
	 */
	render() {
		return html`
			<lvl-popup ?open="${this.open}" placement="${this.placement}" ?flip="${this.flip}" ?shift="${this.shift}" ?orbit="${this.orbit}"
								 border="${ifDefined(this.border)}" border-radius="${ifDefined(this.borderRadius)}" box-shadow="${ifDefined(this.boxShadow)}"
								 offset-block-x="${ifDefined(this.blockOffsetX)}" offset-block-y="${ifDefined(this.blockOffsetY)}"
								 offset-inline-x="${ifDefined(this.inlineOffsetX)}" offset-inline-y="${ifDefined(this.inlineOffsetY)}">
				<slot></slot>
			</lvl-popup>
		`
	}

	/**
	 * handle property changes
	 * @param changedProperties
	 * @protected
	 */
	protected updated(changedProperties: PropertyValues<this>) {
		// close child sub menus if any
		if (changedProperties.has('open')) {
			if(this.open)
				this.closeOtherDropdowns()
			else
				this.querySelector(':scope > lvl-menu > lvl-menu-item[sub-menu-open]')?.removeAttribute('sub-menu-open')
		}
	}

	//#endregion

	//#region Event Handlers
	private handleClick(event: MouseEvent) {
		if ((event.target as HTMLElement).hasAttribute("disabled"))
			return
		
		this.open = !this.open
		if (this.open) {
			this.trigger?.setAttribute('data-popup-open', '')
			this.dispatchEvent(new Event('open'))
		} else {
			this.trigger?.removeAttribute('data-popup-open')
			this.dispatchEvent(new Event('close'))
		}

		if (!this.open)
			return

		// append event to close the dropdown upon clicking somewhere inside the window
		window.addEventListener('click', this.handleWindowClick)

		// close if trigger gets scrolled outside of visible viewport
		this._intersectionObserver = new IntersectionObserver((entries) => {
			entries.forEach((entry) => {
				if (this.open && !entry.isIntersecting)
					this.closeDropdown()
			});
		}, { threshold: 0.75 });
		if (this.trigger)
			this._intersectionObserver.observe(this.trigger)

		// prevent click action from immediately closing the dropdown after clicking the trigger element
		event.stopImmediatePropagation()
	}
	
	private handleKeydown(event: KeyboardEvent) {
		if (event.key == "Escape" && this.open)
			this.closeDropdown()
	}

	private handleDropdownClick(event: MouseEvent) {
		if (this.keepOpen)
			event.stopImmediatePropagation()
	}

	private handleWindowClick = (event: MouseEvent) => {
		let currentTarget = event.target instanceof Element ? event.target : null
		while (currentTarget !== this.trigger && currentTarget?.shadowRoot) {
			const newTarget = currentTarget.shadowRoot.elementFromPoint(event.clientX, event.clientY)
			if (currentTarget == newTarget)
				break
			currentTarget = newTarget
		}
		
		if (currentTarget !== this.trigger)
			this.closeDropdown()
	}
	
	private closeDropdown() {
		this.open = false
		this.trigger?.removeAttribute('data-popup-open')
		this.dispatchEvent(new Event('close'))
		window.removeEventListener('click', this.handleWindowClick)
		this._intersectionObserver?.disconnect()
		this._intersectionObserver = undefined
	}
	
	private registerDropdown() {
		// initialize dropdown group if missing
		if(this.dropdownGroup == null) {
			if(dropdownGroups == null)
				dropdownGroups = {}
			dropdownGroups[this.dropdownGroupId] = []
		}
		
		// add it to the group
		this.dropdownGroup!.push(this)
	}
	
	private unregisterDropdown() {
		dropdownGroups[this.dropdownGroupId] = this.dropdownGroup!.filter(dropdown => dropdown != this)
	}

	// close other popups with the same popup id
	private closeOtherDropdowns() {
		this.dropdownGroup?.forEach(dropdownElement => {
			if(dropdownElement === this)
				return
			dropdownElement.closeDropdown()
		})
	}
	
	//#endregion
}

export type DropdownType = {
	placement: PopupPlacement,
	flip: Boolean,
	shift: Boolean,
	orbit: Boolean,
	borderRadius?: string,
	boxShadow: Intensity,
	border?: string,
	blockOffsetX: number,
	blockOffsetY: number
}

declare global {
	interface HTMLElementTagNameMap {
		'lvl-dropdown': Dropdown
	}
}