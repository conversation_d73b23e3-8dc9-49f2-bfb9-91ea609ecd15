import { <PERSON>a, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { LevelStory } from '@story-home/support/commands.ts'
import { html } from 'lit'
import { BorderPosition, DropdownMenuDividerType } from '@/components/dropdown/DropdownMenuDivider.ts'
import { ifDefined } from 'lit-html/directives/if-defined.js'

import('@/components/dropdown/Dropdown')
import('@/components/dropdown/DropdownMenu')
import('@/components/dropdown/DropdownMenuItem')
import('@/components/dropdown/DropdownMenuDivider')

export type DropdownMenuDividerStory = Story<Partial<DropdownMenuDividerType>>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-menu-item',
	tags: [ 'autodocs' ],
	render: (_args: Partial<DropdownMenuDividerType>) => html`
		<lvl-menu>
			<lvl-menu-item>Item A</lvl-menu-item>
			<lvl-menu-item>Item B</lvl-menu-item>
			<lvl-menu-divider label="${ifDefined(_args.label)}" border="${ifDefined(_args.border)}" spacing="${ifDefined(_args.spacing)}"></lvl-menu-divider>
			<lvl-menu-item icon-left="star">Special 1</lvl-menu-item>
			<lvl-menu-item icon-left="star">Special 2</lvl-menu-item>
		</lvl-menu>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		'label': {
			control: 'text',
			description: 'optional label which is used as a headline for the subsequent menu items',
			table: {
				type: { summary: 'string' },
			},
		},
		'border': {
			control: 'select',
			description: 'should the border be displayed on top or below the label (if at all)?',
			options: [ BorderPosition.Top, BorderPosition.Bottom, BorderPosition.Both, BorderPosition.None ],
			table: {
				type: { summary: 'BorderPosition' },
			},
		},
		'spacing': {
			control: 'number',
			description: 'should there be a specific spacing before the divider starts?',
			table: {
				type: { summary: 'number' },
			},
		}
	},
	includeStories: /^[A-Z]/,
	args: {
		label: 'My Favorites',
		border: BorderPosition.Top,
		spacing: 0
	}
}

export default meta

//#region Stories

/**
 * Appearance of a default dropdown
 */
export const Default: DropdownMenuDividerStory = {
	args: {
		label: 'My Favorites',
		border: BorderPosition.Top,
		spacing: 0
	}
}

//#endregion
export const stories = {
	default: new LevelStory(meta, Default)
} as const