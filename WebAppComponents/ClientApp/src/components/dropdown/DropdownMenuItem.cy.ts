import { getExpectedColor, storyTest } from '@test-home/support/advanced-functions.ts'
import { stories } from './DropdownMenuItem.stories.ts'

import('@/components/dropdown/Dropdown')
import('@/components/dropdown/DropdownMenu')
import('@/components/dropdown/DropdownMenuItem')
import('@/components/dropdown/DropdownMenuDivider')

describe('<lvl-menu-item />', () => {

	storyTest('checks menu item options', stories.default, () => {
		cy.mountStory(stories.default)
		
		// menu item is showing
		cy.get('#testItem').should('be.visible')
		
		// no space on the left
		cy.get('#testItem').find('.menu-icon-left').should('not.exist')
		
		// no space on the right
		cy.get('#testItem').find('.menu-icon-right').should('not.exist')
		
		// icon left working
		checkIcon('left');
		
		// icon right working
		checkIcon('right');
		
		// selected
		cy.get('#testItem').then((item) => {
			item[0].setAttribute(`selected`, '')
		});
		cy.get('#testItem').find('.menu-item').should('have.css', 'font-weight', '400')

		cy.get('#testItem').then((item) => {
			item[0].removeAttribute(`selected`)
		});
		cy.get('#testItem').find('.menu-item').should('have.css', 'font-weight', '400')
		
		// checkable
		cy.get('#testItem').then((item) => {
			item[0].setAttribute(`checkable`, '')
		});
		cy.get('#testItem').find('.menu-icon-left').should('exist')

		cy.get('#testItem').then((item) => {
			item[0].removeAttribute(`checkable`)
		});
		cy.get('#testItem').find('.menu-icon-left').should('not.exist')
		
		// checked
		cy.get('#testItem').then((item) => {
			item[0].setAttribute(`checkable`, '')
			item[0].setAttribute(`checked`, '')
		});
		cy.get('#testItem').find('.menu-icon-left').should('exist')
		cy.get('#testItem').find('.menu-icon-left').find('i').should('exist')
			.should('have.class', 'icon')
			.should('have.class', 'fa-light')
			.should('have.class', 'fa-check')
			.should('have.css', 'color', 'rgb(0, 122, 204)')

		cy.get('#testItem').then((item) => {
			item[0].removeAttribute(`checked`)
			cy.get('#testItem').find('.menu-icon-left').should('exist')
			cy.get('#testItem').find('.menu-icon-left').find('i').should('not.exist')
		});

		cy.get('#testItem').then((item) => {
			item[0].removeAttribute(`checkable`)
			cy.get('#testItem').find('.menu-icon-left').should('not.exist')
		});

		// sorting
		cy.get('#testItem').then((item) => {
			item[0].setAttribute(`sortable`, '')
			item[0].setAttribute(`sorting`, 'asc')
		});
		cy.get('#testItem').find('.menu-icon-right').should('exist')
		cy.get('#testItem').find('.menu-icon-right').find('i').should('exist')
			.should('have.class', 'icon')
			.should('have.class', 'fa-light')
			.should('have.class', 'fa-arrow-up')
			.should('have.css', 'color', 'rgb(0, 122, 204)')

		cy.get('#testItem').then((item) => {
			item[0].setAttribute(`sorting`, 'desc')
		});
		cy.get('#testItem').find('.menu-icon-right').find('i').should('exist')
			.should('have.class', 'icon')
			.should('have.class', 'fa-light')
			.should('have.class', 'fa-arrow-down')
			.should('have.css', 'color', 'rgb(0, 122, 204)')

		cy.get('#testItem').then((item) => {
			item[0].removeAttribute(`sorting`)
			cy.get('#testItem').find('.menu-icon-right').should('exist')
			cy.get('#testItem').find('.menu-icon-right').find('i').should('not.exist')
		});

		cy.get('#testItem').then((item) => {
			item[0].removeAttribute(`sortable`)
			cy.get('#testItem').find('.menu-icon-right').should('not.exist')
		});
		
		// padding-left
		cy.get('#testItem').then((item) => {
			item[0].setAttribute(`padding-left`, '5px')
			cy.get('#testItem').find('.menu-item').should('have.css', 'padding-left', '5px')
		});
	})
})

function checkIcon(iconName: string) {
	cy.get('#testItem').then((item) => {
		item[0].setAttribute(`icon-${iconName}`, "user")
	});
	cy.get('#testItem').find(`.menu-icon-${iconName}`).should('exist')
	cy.get('#testItem').find(`.menu-icon-${iconName}`).find('i')
		.should('have.class', 'icon')
		.should('have.class', 'fa-light')
		.should('have.class', 'fa-user')

	cy.get('#testItem').then((item) => {
		item[0].setAttribute(`icon-${iconName}-color`, "red")
	});
	cy.get('#testItem').find(`.menu-icon-${iconName}`).find('i').should('have.css', 'color', 'rgb(255, 0, 0)')
	
	cy.get('#testItem').invoke('removeAttr', `icon-${iconName}-color`)
	cy.get(`#testItem`).find(`.menu-icon-${iconName}`).find('i').should('have.css', 'color', getExpectedColor( 'rgb(32, 42, 49)', 'rgb(175, 191, 202)'))

	cy.get('#testItem').then((item) => {
		item[0].removeAttribute(`icon-${iconName}`)
	});
	cy.get('#testItem').find(`.menu-icon-${iconName}`).should('not.exist')
}