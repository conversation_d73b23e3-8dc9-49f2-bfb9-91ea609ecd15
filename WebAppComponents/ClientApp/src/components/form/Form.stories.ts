import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands'
import { FormType } from '@/components/form/Form.ts'
import { ifDefined } from 'lit-html/directives/if-defined.js'

import('@/components/color-picker/ColorPicker')
import('@/components/dropdown/DropdownMenuItem')
import('@/components/dropdown/DropdownMenu')
import('@/components/dropdown/Dropdown')
import('@/components/dialog/Dialog')

import('@/components/inputs/InputElement.ts')
import('@/components/inputs/input/Input.ts')
import('@/components/inputs/InputIcon')
import('@/components/inputs/InputButton')
import('@/components/inputs/autocomplete/Autocomplete')
import('@/components/inputs/textarea/Textarea')
import('@/components/atomics/button/Button')
import('@/components/button-group/ButtonGroup')
import('@/components/atomics/tooltip/Tooltip')
import('@/components/inputs/toggle/Toggle.ts')
import('@/components/inputs/checkbox/Checkbox')
import('@/components/checkbox-group/CheckboxGroup')
import('@/components/inputs/rich-text/RichText')
import('./Form.ts')

export type FormStory = Story<Partial<FormType>>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-form',
	tags: [ 'autodocs' ],
	render: (_args: Partial<FormType>) => html`
		<lvl-form id="${ifDefined(_args.id)}" ?skeleton="${ifDefined(_args.skeleton)}">
			<section style="display:grid;grid-template-columns: repeat(2, 1fr);gap:1.6rem 0.8rem;break-inside: avoid;">
				<lvl-input name="stringInput" type="string" value="I am a required string" label="string input" required></lvl-input>
				<lvl-input name="intInput" type="integer" value="15" label="readonly int input" readonly></lvl-input>
				<lvl-input name="doubleInput" type="double" value="1234.5678" decimalPlaces="2" sign="€" text-align="right" label="double input"></lvl-input>
				<lvl-input name="invalidDoubleInput" type="double" value="1" decimalPlaces="2" text-align="right" label="invalid double input"></lvl-input>
				<lvl-input name="dateInput" type="date" value="2024-04-04" label="date input"></lvl-input>
				<lvl-input name="dateTimeInput" type="datetime" value="2024-05-01 13:45" label="datetime input"></lvl-input>
				<lvl-input name="urlInput" type="url" value="https://www.levelbuild.com" label="url input"></lvl-input>
				<lvl-input name="colorInput" type="color" value="green" label="color input"></lvl-input>
				<lvl-autocomplete name="autocompleteInput" label="autocomplete input" style="grid-column:1 / -1;">
					<lvl-head>
						<lvl-th name="name" type="string">Name</lvl-th>
						<lvl-th name="city" type="string">City</lvl-th>
						<lvl-th name="country" type="string">Country</lvl-th>
					</lvl-head>
					<lvl-body>
						<lvl-option value="levelbuild">
							<lvl-td>Levelbuild AG</lvl-td>
							<lvl-td>Leipzig</lvl-td>
							<lvl-td>Germany</lvl-td>
						</lvl-option>
						<lvl-option value="jaeger">
							<lvl-td>Jaeger Gruppe</lvl-td>
							<lvl-td>Dortmund</lvl-td>
							<lvl-td>Germany</lvl-td>
						</lvl-option>
						<lvl-option value="mainka">
							<lvl-td>Mainka Bau GmbH & Co. KG</lvl-td>
							<lvl-td>Lingen</lvl-td>
							<lvl-td>Germany</lvl-td>
						</lvl-option>
					</lvl-body>
				</lvl-autocomplete>
				<lvl-checkbox name="checkboxInput" label="I am a single checkbox" style="grid-column:1 / 2;"></lvl-checkbox>
				<lvl-button-group style="grid-column:1 / -1;" name="defaultGroup" value="REAL">
					<lvl-button label="Alle" tooltip="Alle Datenquellen anzeigen" value="ALL"></lvl-button>
					<lvl-button label="Reale" tooltip="Nur reale Datenquellen anzeigen" value="REAL"></lvl-button>
					<lvl-button label="Virtuelle" tooltip="Nur virtuelle Datenquellen anzeigen" value="VIRTUAL"></lvl-button>
				</lvl-button-group>
				<lvl-textarea name="textInput" style="grid-column: 1 / -1;">Hello there. This is some really interesting text. It might be more, but it isn't :)
				</lvl-textarea>
				<lvl-rich-text name="richInput" style="grid-column: 1 / -1;">I'm sure that in 1985, plutonium is available in every corner drugstore, but in 1955, it's a little hard to come by.</lvl-rich-text>
				<lvl-checkbox-group name="checkboxGroupInput" label="some fruits anyone?" style="grid-column: 1 / -1;">
					<lvl-checkbox value="Apple">Apple</lvl-checkbox>
					<lvl-checkbox value="Cherry">Cherry</lvl-checkbox>
					<lvl-checkbox value="Banana">Banana</lvl-checkbox>
					<lvl-checkbox value="Strawberry">Strawberry</lvl-checkbox>
					<lvl-checkbox value="Peach">Peach</lvl-checkbox>
					<lvl-checkbox value="Orange">Orange</lvl-checkbox>
					<lvl-checkbox value="Blueberry">Blueberry</lvl-checkbox>
					<lvl-checkbox value="Raspberry">Raspberry</lvl-checkbox>
					<lvl-checkbox value="Pineapple">Pineapple</lvl-checkbox>
				</lvl-checkbox-group>
				<lvl-toggle name="toggle" value="false" label="toggle"></lvl-toggle>
				<section>
		</lvl-form>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		id: {
			control: 'text',
			description: 'unique id of the form',
			table: {
				type: { summary: 'string' },
			},
		},
		skeleton: {
			control: 'boolean',
			description: 'if set to true, the input including the label are displayed as a pulsating bar (used as a loading animation while the data is not ready)',
			table: {
				type: { summary: 'boolean' },
			},
		},
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a default Form
 */
export const Default: FormStory = {
	args: {
		id: 'defaultForm',
		skeleton: false,
	},
}

/**
 * Appearance of a loading Form
 */
export const Skeleton: FormStory = {
	args: {
		id: 'skeletonForm',
		skeleton: true,
	},
}


//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
} as const