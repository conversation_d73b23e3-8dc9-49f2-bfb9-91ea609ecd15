import { css, html, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'
import { ifDefined } from 'lit-html/directives/if-defined.js'
import { renderIcon } from '@/shared/component-html.ts'
import { PopupPlacement } from '@/components/popup/Popup.ts'
import { IconStyle } from '@/enums/icon-style.ts'
import * as styles from '@/shared/component-styles.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'

@customElement('lvl-input-button')
export class InputButton extends LitElement {

	static styles = [
		styles.animation,
		styles.skeleton,
		fontAwesome,
		css`
			:host {
				position: relative;
				order: 3;
			}
	
			:host([left]),
			:host([only]) {
				order: -3;
			}
			
			button {
				position: relative;
				border: none;
				outline: none;
				min-width: 35px;
				height: 100%;
				margin-left: 0;
				border-left: 1px solid transparent;
				background-color: transparent;
				pointer-events: auto;
				transition: all var(--animation-time-medium) ease;
				transition-property: color, background-color, border-color;
				justify-content: center;
				--tooltip-spacing-horizontal: 1px;
				--tooltip-offset-horizontal: -1px;
				--tooltip-offset-vertical: -1px;
				display: flex;
				gap: var(--size-spacing-m);
				align-items: center;
				font-size: var(--size-text-m);
				color: var(--cp-clr-text-primary-positiv);
				padding: 0 var(--input-padding-inline);
				border-radius: 0 var(--size-radius-m) var(--size-radius-m) 0;
			}
			
			:host([rounded]) button {
				border-top-right-radius: var(--input-height);
				border-bottom-right-radius: var(--input-height);
			}

			button.skeleton__block {
				--skeleton-inset: 0 0 0 3px;
				pointer-events: none;
			}
			
			.button-text {
				flex-grow: 1;
				text-align: left;
				white-space: nowrap;
			}
			
			button.left {
				border-radius: calc(var(--size-radius-m) - 1px) 0 0 calc(var(--size-radius-m) - 1px);
				border-left: none;
				border-right: 1px solid transparent;
				background-color: var(--cp-clr-background-lvl-1);
				color: var(--cp-clr-text-primary-positiv);
			}

			button.only {
				border-radius: calc(var(--size-radius-m) - 1px);
				border-left: none;
				border-right: 1px solid transparent;
				background-color: var(--cp-clr-background-lvl-1);
				color: var(--cp-clr-text-primary-positiv);
			}
	
			button.external {
				border: none;
				background-color: transparent;
				color: #9DA8B0;
				height: 32px;
				padding: 0 10px 0 9px;
				font-size: var(--size-text-l);
				text-align: center;
				cursor: pointer;
				position: relative;
				--tooltip-spacing-horizontal: calc(-1 * var(--input-padding-inline));
				--tooltip-spacing-vertical: -8px;
				--tooltip-offset-vertical: -1px;
			}
	
			button:before {
				content: "";
				position: absolute;
				top: 4px;
				bottom: 4px;
				left: -1px;
				transition: all var(--animation-time-medium) ease;
			}
	
			button.left:before {
				left: auto;
				right: -1px;
			}

			button.only:before {
				content: none;
			}
	
			button:not(.external):before {
				border-left: 1px solid var(--cp-clr-border-medium);
			}
	
			button.left:not(.external):before {
				border-left: 1px solid transparent;
			}
	
			button:not(.readonly):hover,
			button:not(.readonly).active {
				color: var(--cp-clr-text-primary-positiv);
				background-color: var(--cp-clr-background-lvl-1);
				cursor: pointer;
			}
	
			button.left:not(.readonly):hover,
			button.left:not(.readonly).active {
				background-color: var(--cp-clr-background-lvl-2);
			}
	
			button.external:not(.readonly):focus-visible,
			button.external:not(.readonly):hover {
				color: var(--cp-clr-state-focus);
				outline: none;
			}
			
			button:not(.readonly):hover::before,
			button:not(.readonly).active::before {
				top: 0;
				bottom: 0;
			}
	
			button.readonly {
				color: var(--cp-clr-text-secondary);
				cursor: default;
			}
	
			button.bubble::after {
				content: "";
				position: absolute;;
				height: 0.6rem;
				aspect-ratio: 1 / 1;
				border-radius: 50%;
				border: 0.2rem solid var(--cp-clr-background-lvl-0);
				background-color: var(--cp-clr-state-active);
				top: 0.5rem;
				right:0.4rem;
			}
	
			button > .icon {
				font-size: 14px;
				text-align: center;
				display: flex;
				flex-direction: column;
				justify-content: center;
				height: 100%;
				transform: rotate(var(--icon-rotation));
				color: var(--icon-color, var(--cp-clr-text-primary-positiv));
				transition: transform var(--animation-time-medium) ease, color var(--animation-time-medium) ease;
			}

			button.readonly > .icon {
				color: var(--cp-clr-text-secondary);
			}
	
			button.external i {
				font-size: var(--size-text-l);
			}
	
			lvl-tooltip:not(:defined) {
				display: none;
			}
		`,
	]

	//#region attributes

	@property()
	label?: string

	@property({attribute: 'icon-css'})
	iconCSS?: string

	@property({attribute: 'icon-color'})
	iconColor?: string

	@property({attribute: 'icon-style'})
	iconStyle: IconStyle = IconStyle.Thin
	
	@property({type: Number, attribute: 'icon-rotation'})
	iconRotation: Number = 0

	@property()
	tooltip?: string

	@property({ type: Boolean })
	readonly: boolean = false

	@property({ type: Boolean })
	external: boolean = false

	@property({ type: Boolean })
	showBubble: boolean = false
	
	@property({ type: Boolean })
	left: boolean = false

	@property({ type: Boolean })
	only: boolean = false
	
	@property({ attribute: 'min-width' })
	minWidth?: string

	@property({ type: Boolean, reflect: true })
	skeleton: boolean = false
	
	@property({ type: Boolean, reflect: true })
	rounded: boolean = false
	
	@property({ attribute: 'data-popup-open', type: Boolean })
	private dropdownOpen: boolean = false
	
	//#endregion

	//#region Lit functions

	/**
	 * render component
	 */
	render() {
		const classes = {
			readonly: this.readonly,
			external: this.external,
			bubble: this.showBubble,
			left: this.left,
			only: this.only,
			active: this.dropdownOpen,
			skeleton__block: this.skeleton,
		}

		let iconHTML
		if (this.iconCSS)
			iconHTML = renderIcon(this.iconCSS, { iconStyle: this.iconStyle })
		let renderTooltip = this.tooltip && !this.readonly && !this.dropdownOpen
		
		return html`
			<button tabindex="${ifDefined(this.external ? undefined : -1)}"
							class="${classMap(classes)}"
							style="${this.minWidth ? `min-width:${this.minWidth};` : ''}${this.iconColor ? `--icon-color:${this.iconColor};` : ''}--icon-rotation:${this.iconRotation}deg;"
							data-tooltip="${ifDefined(renderTooltip ? 'inputButtonTooltip' : '')}" @click="${this.handleClick}">
				<slot></slot>
				${this.label}${iconHTML}
			</button>
			${renderTooltip ? html`
				<lvl-tooltip name="inputButtonTooltip" placement="${this.left ? PopupPlacement.Left : PopupPlacement.Right}">
					${this.tooltip}
				</lvl-tooltip>
			` : ''}
		`
	}

	//#endregion
	
	//#region private functions
	
	handleClick(event: MouseEvent) {
		if(this.readonly)
			event.stopPropagation()
	}
	
	//#endregion
}

declare global {
	interface HTMLElementTagNameMap {
		'lvl-input-button': InputButton
	}
}