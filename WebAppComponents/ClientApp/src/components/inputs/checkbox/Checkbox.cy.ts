import { storyTest } from '@test-home/support/advanced-functions.ts'
import { stories } from './Checkbox.stories.ts'

import('./Checkbox.ts')

describe('<lvl-toggle />', () => {
	
	describe('Check toggle', () => {

		storyTest('checks toggle on/off', stories.default, () => {
			cy.mountStory(stories.default)

			let input = cy.get('input[type="checkbox"]');
			input.should("exist")
			input.should("be.checked")
			input.click()
			input.should("not.be.checked")
		})

		storyTest('checks indeterminate state handling', stories.indeterminate, () => {
			cy.mountStory(stories.indeterminate)

			let input = cy.get('input[type="checkbox"]');
			input.should("exist")

			// initially the checkbox should be indeterminate
			cy.get('lvl-checkbox').then((element) => {
				expect(element[0].indeterminate).to.equal(true)
			})
			cy.get('input').then((element) => {
				expect(element[0].indeterminate).to.equal(true)
			})
			
			// setting the checked attribute should remove the indeterminate flag
			cy.get('lvl-checkbox').invoke('attr', 'checked', '')
			cy.wait(50)
			cy.get('lvl-checkbox').then((element) => {
				expect(element[0].indeterminate).to.equal(false)
				expect(element[0].checked).to.equal(true)
			})
			cy.get('input').then((element) => {
				expect(element[0].indeterminate).to.equal(false)
				expect(element[0].checked).to.equal(true)
			})
			
			// setting indeterminate to true should remove the checked flag
			cy.get('lvl-checkbox').invoke('attr', 'indeterminate', '')
			cy.wait(50)
			cy.get('lvl-checkbox').then((element) => {
				expect(element[0].indeterminate).to.equal(true)
				expect(element[0].checked).to.equal(false)
			})
			cy.get('input').then((element) => {
				expect(element[0].indeterminate).to.equal(true)
				expect(element[0].checked).to.equal(false)
			})
		})

		storyTest('checks disabled toggle which is on', stories.readonlyAndOn, () => {
			cy.mountStory(stories.readonlyAndOn)
			
			let input = cy.get('input[type="checkbox"]');
			input.should("be.checked")
			input.click({force:true})
			input.should("be.checked")
		})

		storyTest('checks disabled toggle which is off', stories.readonlyAndOff, () => {
			cy.mountStory(stories.readonlyAndOff)
			
			let input = cy.get('input[type="checkbox"]');
			input.should("not.be.checked")
			input.click({force:true})
			input.should("not.be.checked")
		})
	})
})