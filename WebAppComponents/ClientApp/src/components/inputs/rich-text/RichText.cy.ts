import { stories } from './RichText.stories'
import { getExpectedColor, storyTest } from '@test-home/support/advanced-functions'
import { JSONContent } from '@tiptap/core'
import { RichText } from '@/components/inputs/rich-text/RichText.ts'

import('./RichText')

// Test suite for the example web component
describe('<lvl-rich-text />', () => {
	
	const TIP_TAP_HISTORY_DELAY = 500

	storyTest('checks whether initial content is formatted correctly', stories.default, () => {
		cy.viewport(800, 500)
		cy.mountStory(stories.default)

		cy.get('lvl-rich-text').as('editor')
		cy.get('.editor__toolbar').as('editorToolbar')
		cy.get('.editor__content > [contenteditable=true]').as('editorContent').should('have.attr', 'contenteditable', 'true')

		// inject content
		cy.get('@editor').then(($richtextElement) => {
			const richtTextComponent = $richtextElement[0] as RichText
			const content = stories.default.getAttribute<JSONContent>('content')
			if (content)
				richtTextComponent.value = content
		})

		// text size
		cy.get('@editorContent').containsExact('H1').should('have.prop', 'tagName', 'H1')
		cy.get('@editorContent').containsExact('H2').should('have.prop', 'tagName', 'H2')
		cy.get('@editorContent').containsExact('H3').should('have.prop', 'tagName', 'H3')
		cy.get('@editorContent').containsExact('Text').should('have.prop', 'tagName', 'P')
		
		// -> change text size
		cy.get('@editorContent').containsExact('H1').click()
		cy.get('@editorToolbar').find('[data-action=open-text-size]').should('have.attr', 'icon', 'h1').click()
		cy.get('@editorToolbar').find('[name=size-dropdown]').should('have.attr','open')
		cy.get('@editorToolbar').find('[data-action=h1]').should('have.prop', 'selected', true)
		cy.get('@editorToolbar').find('[data-action=h2]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').containsExact('H1').should('have.prop', 'tagName', 'H2')
		cy.get('@editorToolbar').find('[data-action=h3]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').containsExact('H1').should('have.prop', 'tagName', 'H3')
		cy.get('@editorToolbar').find('[data-action=text]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').containsExact('H1').should('have.prop', 'tagName', 'P')
		cy.get('@editorToolbar').find('[data-action=h1]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').containsExact('H1').should('have.prop', 'tagName', 'H1')
		

		// color
		cy.get('@editorContent').containsExact('BLAU')
			.should('have.prop', 'tagName', 'SPAN')
			.should('have.css', 'color', 'rgb(59, 59, 186)')
		
		// -> change color
		cy.get('@editorContent').containsExact('BLAU').dblclick()
		cy.get('@editorToolbar').find('[data-action=color]').find('.button__content .icon').should('have.css', 'color', 'rgb(59, 59, 186)')
		cy.get('@editorToolbar').find('[data-action=color]').click()
		cy.get('[name=text-color-picker]').find('[data-key=palette-0-100-50]').click({ force: true })
		cy.get('@editorContent').containsExact('BLAU').should('have.css', 'color', 'rgb(255, 0, 0)')
		cy.get('@editorToolbar').find('[data-action=color]').find('.button__content .icon').should('have.css', 'color', 'rgb(255, 0, 0)')
		cy.get('@editorToolbar').find('[data-action=color]').click()
		cy.get('[name=text-color-picker]').find('[slot=special] > .color__block').click({ force: true })
		cy.get('@editorToolbar').find('[data-action=color]').should('not.have.attr', 'style')
		cy.get('@editorContent').contains('BLAU').should('not.have.attr', 'style')
			
		// highlight
		cy.get('@editorContent').containsExact('GRÜN')
			.should('have.prop', 'tagName', 'MARK')
			.should('have.css', 'backgroundColor', 'rgb(23, 207, 93)')
		
		// -> change highlight
		cy.get('@editorContent').containsExact('GRÜN').dblclick()
		cy.get('@editorToolbar').find('[data-action=highlight]').find('.button__content .icon').should('have.css', 'color', 'rgb(23, 207, 93)')
		cy.get('@editorToolbar').find('[data-action=highlight]').click()
		cy.get('[name=background-color-picker]').find('[data-key=palette-0-100-50]').click({ force: true })
		cy.get('@editorContent').containsExact('GRÜN').should('have.css', 'backgroundColor', 'rgb(255, 0, 0)')
		cy.get('@editorToolbar').find('[data-action=highlight]').find('.button__content .icon').should('have.css', 'color', 'rgb(255, 0, 0)')
		cy.get('@editorToolbar').find('[data-action=highlight]').click()
		cy.get('[name=background-color-picker]').find('[slot=special] > .color__block').click({ force: true })
		cy.get('@editorToolbar').find('[data-action=highlight]').should('not.have.attr', 'style')
		cy.get('@editorContent').contains('GRÜN').should('not.have.prop', 'tagName', 'MARK')

		// text style + change
		cy.get('@editorContent').containsExact('DICK').should('have.prop', 'tagName', 'STRONG').selectAll()
		cy.get('@editorToolbar').find('[data-action=bold]').should('have.prop', 'selected', true).click()
		cy.get('@editorContent').contains('DICK').should('not.have.prop', 'tagName', 'STRONG')
		cy.get('@editorToolbar').find('[data-action=bold]').should('have.prop', 'selected', false).click()
		cy.get('@editorContent').containsExact('DICK').should('have.prop', 'tagName', 'STRONG')
		
		cy.get('@editorContent').containsExact('KURSIV').should('have.prop', 'tagName', 'EM').selectAll()
		cy.get('@editorToolbar').find('[data-action=italic]').should('have.prop', 'selected', true).click()
		cy.get('@editorContent').contains('DICK').should('not.have.prop', 'tagName', 'EM')
		cy.get('@editorToolbar').find('[data-action=italic]').should('have.prop', 'selected', false).click()
		cy.get('@editorContent').containsExact('KURSIV').should('have.prop', 'tagName', 'EM')
		
		cy.get('@editorContent').containsExact('UNTERSTRICHEN').should('have.prop', 'tagName', 'U').selectAll()
		cy.get('@editorToolbar').find('[data-action=underline]').should('have.prop', 'selected', true).click()
		cy.get('@editorContent').contains('UNTERSTRICHEN').should('not.have.prop', 'tagName', 'U')
		cy.get('@editorToolbar').find('[data-action=underline]').should('have.prop', 'selected', false).click()
		cy.get('@editorContent').containsExact('UNTERSTRICHEN').should('have.prop', 'tagName', 'U')
		
		cy.get('@editorContent').containsExact('DURCHGESTRICHEN').should('have.prop', 'tagName', 'S').selectAll()
		cy.get('@editorToolbar').find('[data-action=strike-through]').should('have.prop', 'selected', true).click()
		cy.get('@editorContent').contains('DURCHGESTRICHEN').should('not.have.prop', 'tagName', 'S')
		cy.get('@editorToolbar').find('[data-action=strike-through]').should('have.prop', 'selected', false).click()
		cy.get('@editorContent').containsExact('DURCHGESTRICHEN').should('have.prop', 'tagName', 'S')
		
		cy.get('@editorContent').containsExact('ZUSAMMEN').then($element => {
			expect($element.closest('strong')[0]).to.be.not.null
			expect($element.closest('em')[0]).to.be.not.null
			expect($element.closest('u')[0]).to.be.not.null
			expect($element.closest('s')[0]).to.be.not.null
		})
		
		// remove styling with unmark button
		cy.get('@editorContent').containsExact('ZUSAMMEN').closest('strong').selectAll()
		cy.get('@editorToolbar').find('[data-action=bold]').should('have.prop', 'selected', true)
		cy.get('@editorToolbar').find('[data-action=italic]').should('have.prop', 'selected', true)
		cy.get('@editorToolbar').find('[data-action=underline]').should('have.prop', 'selected', true)
		cy.get('@editorToolbar').find('[data-action=strike-through]').should('have.prop', 'selected', true)
		
		cy.get('@editorToolbar').find('[data-action=unmark]').click()
		cy.get('@editorToolbar').find('[data-action=bold]').should('have.prop', 'selected', false)
		cy.get('@editorToolbar').find('[data-action=italic]').should('have.prop', 'selected', false)
		cy.get('@editorToolbar').find('[data-action=underline]').should('have.prop', 'selected', false)
		cy.get('@editorToolbar').find('[data-action=strike-through]').should('have.prop', 'selected', false)
		cy.get('@editorContent').contains('ZUSAMMEN').then($element => {
			expect($element.closest('strong')[0]).to.be.undefined
			expect($element.closest('em')[0]).to.be.undefined
			expect($element.closest('u')[0]).to.be.undefined
			expect($element.closest('s')[0]).to.be.undefined
		})
		
		// alignment
		cy.get('@editorContent').contains('LINKS').should('not.have.attr', 'style')
		cy.get('@editorContent').contains('MITTIG').should('have.css', 'textAlign', 'center')
		cy.get('@editorContent').contains('RECHTS').should('have.css', 'textAlign', 'right')
		cy.get('@editorContent').contains('BLOCKIG').should('have.css', 'textAlign', 'justify')
		
		// -> change alignment
		cy.get('@editorContent').contains('LINKS').click()
		cy.get('@editorToolbar').find('[data-action=open-alignment]').should('have.attr', 'icon', 'align-left').click()
		cy.get('@editorToolbar').find('[name=alignment-dropdown]').should('have.attr','open')
		cy.get('@editorToolbar').find('[data-action=align-left]').should('have.prop', 'selected', true)
		cy.get('@editorToolbar').find('[data-action=align-center]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').contains('LINKS').should('have.css', 'text-align', 'center')
		cy.get('@editorToolbar').find('[data-action=align-right]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').contains('LINKS').should('have.css', 'text-align', 'right')
		cy.get('@editorToolbar').find('[data-action=align-justify]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').contains('LINKS').should('have.css', 'text-align', 'justify')
		cy.get('@editorToolbar').find('[data-action=align-left]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').contains('LINKS').should('not.have.attr', 'style')

		// lists
		cy.get('@editorContent').contains('FOKUSSIERT').then($element => expect($element.closest('ul')[0]).to.be.not.undefined)
		cy.get('@editorContent').contains('LEIDENSCHAFTLICH').then($element => expect($element.closest('ul')[0]).to.be.not.undefined)
		cy.get('@editorContent').contains('PARTNERSCHAFTLICH').then($element => expect($element.closest('ul')[0]).to.be.not.undefined)

		cy.get('@editorContent').contains('TÜTCHEN AUF').then($element => expect($element.closest('ol')[0]).to.be.not.undefined)
		cy.get('@editorContent').contains('DRÜWER').then($element => expect($element.closest('ol')[0]).to.be.not.undefined)
		cy.get('@editorContent').contains('FERTISCH').then($element => expect($element.closest('ol')[0]).to.be.not.undefined)

		// -> change ul
		cy.get('@editorContent').contains('FOKUSSIERT').click()
		cy.get('@editorToolbar').find('[data-action=open-list]').should('have.attr', 'icon', 'list-ul').click()
		cy.get('@editorToolbar').find('[name=list-dropdown]').should('have.attr','open')
		cy.get('@editorToolbar').find('[data-action=list-ul]').should('have.prop', 'selected', true)
		cy.get('@editorToolbar').find('[data-action=list-ol]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').contains('FOKUSSIERT').then($element => expect($element.closest('ol')[0]).to.be.not.undefined)
		cy.get('@editorContent').contains('LEIDENSCHAFTLICH').then($element => expect($element.closest('ul')[0]).to.be.not.undefined)
		cy.get('@editorToolbar').find('[data-action=open-list]').should('have.attr', 'icon', 'list-ol')
		cy.get('@editorToolbar').find('[data-action=list-none]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').contains('FOKUSSIERT').then($element => expect($element.closest('ol,ul')[0]).to.be.undefined)
		cy.get('@editorContent').contains('LEIDENSCHAFTLICH').then($element => expect($element.closest('ul')[0]).to.be.not.undefined)
		cy.get('@editorToolbar').find('[data-action=open-list]').should('have.attr', 'icon', 'list')
		cy.get('@editorToolbar').find('[data-action=list-ul]')
			.should('have.prop', 'selected', false).click({force: true})
			.should('have.prop', 'selected', true)
		cy.get('@editorContent').contains('FOKUSSIERT').then($element => expect($element.closest('ul')[0]).to.be.not.undefined)
		cy.get('@editorToolbar').find('[data-action=open-list]').should('have.attr', 'icon', 'list-ul')
		
		// link
		cy.get('@editorContent').containsExact('LINK')
			.should('have.prop', 'tagName', 'A')
			.should('have.attr', 'href').and('match', /^https:\/\/de\.wikipedia\.org/)

		// -> change link
		cy.get('@editorContent').containsExact('LINK').selectAll()
		cy.get('@editorToolbar').find('[data-action=open-link]').should('have.attr', 'icon', 'solid/link').click()
		cy.get('@editorToolbar').find('[name=link-dropdown]').should('have.attr','open')
		cy.get('@editorToolbar').find('[data-action=add-link]').click({ force: true })
				
		cy.get('[name=link-dialog]').as('linkDialog').find('[name=text]').as('linkText').should('have.prop', 'value', 'LINK')
		cy.get('@linkText').invoke('attr', 'value', 'ZELDA')
		cy.get('@linkDialog').find('[name=url]').as('linkUrl').should('have.prop', 'value').and('match', /^https:\/\/de\.wikipedia\.org/)
		cy.get('@linkUrl').invoke('attr', 'value', 'https://www.pcgames.de/The-Legend-of-Zelda-Echoes-of-Wisdom-Spiel-74729/News/Wie-bitte-Neues-Zelda-So-gnadenlos-ist-Nintendo-mit-den-Switch-Fans-1454947/')
		cy.get('@linkDialog').find('[data-action=confirm]').click()

		cy.get('@editorContent').containsExact('ZELDA')
			.should('have.prop', 'tagName', 'A')
			.should('have.attr', 'href').and('match', /^https:\/\/www\.pcgames\.de/)
		
		// -> create new link
		cy.get('@editorContent').contains('abbiegen').click()
		cy.get('@editorToolbar').find('[data-action=open-link]').should('have.attr', 'icon', 'link').click()
		cy.get('@editorToolbar').find('[name=link-dropdown]').should('have.attr','open')
		cy.get('@editorToolbar').find('[data-action=add-link]').click({ force: true })
		cy.get('@linkText').invoke('attr', 'value', 'LINK')
		cy.get('@linkDialog').find('[data-action=confirm]').click()
		cy.get('@linkDialog').should('have.attr','open')
		cy.get('@linkUrl').invoke('attr', 'value', 'www.google.de')
		cy.get('@linkDialog').find('[data-action=confirm]').click()
		cy.get('@linkDialog').should('not.have.attr','open')
		cy.get('@editorContent').containsExact('LINK')
			.should('have.prop', 'tagName', 'A')
			.should('have.attr', 'href', 'https:\/\/www.google.de')
		cy.get('@editorContent').contains('abbiegen').should('exist')
		
		// -> remove link
		cy.get('@editorContent').containsExact('LINK')
		cy.get('@editorToolbar').find('[data-action=open-link]').click()
		cy.get('@editorToolbar').find('[data-action=remove-link]').click({ force: true })
		cy.get('@editorContent').contains('LINK').should('not.have.prop', 'tagName', 'A')
		
		// images
		cy.get('@editorContent').contains('Bilder als Link').next().find('img:first-child')
			.should('have.attr', 'alt', 'It\'s fine gif')
			.should('have.attr', 'title', 'It\'s fine')
			.should('have.attr', 'src').and('match', /^http/)

		cy.get('@editorContent').contains('Bilder als Base64').next().find('img:first-child')
			.should('have.attr', 'src').and('match', /^data:image/)
		
		// -> create new image
		cy.get('@editorContent').contains('Bilder als Link').click()
		cy.get('@editorToolbar').find('[data-action=add-image]').click()
		cy.get('[name=image-dialog]').as('imageDialog').find('[name=url]').invoke('attr', 'value', 'https://de.wikipedia.org/static/images/project-logos/dewiki-1.5x.png')
		cy.get('@imageDialog').find('[name=alt]').invoke('attr', 'value', 'test alt')
		cy.get('@imageDialog').find('[name=title]').invoke('attr', 'value', 'test title')
		cy.get('@imageDialog').find('[data-action=confirm]').click()

		cy.get('@editorContent').find('img[alt="test alt"]')
			.should('exist')
			.should('have.attr', 'title', 'test title')
			.should('have.attr', 'src', 'https://de.wikipedia.org/static/images/project-logos/dewiki-1.5x.png')

		// undo/redo
		cy.get('@editorContent').find('.is-editor-empty').should('not.exist')
		cy.get('@editorContent').parent().scrollTo('center').type('{selectAll}{del}')
		cy.get('@editorContent').find('.is-editor-empty').should('exist')
		cy.wait(TIP_TAP_HISTORY_DELAY)
		cy.get('@editorContent').type('Das ist neu').blur()
		cy.get('@editorContent').find('.is-editor-empty').should('not.exist')
		cy.get('@editorToolbar').find('[data-action=undo]').click()
		cy.get('@editorContent').find('.is-editor-empty').should('exist')
		cy.get('@editorToolbar').find('[data-action=redo]').click()
		cy.get('@editorContent').contains('Das ist neu').should('exist')
	})
	
	storyTest('change size steps', stories.large, () => {
		cy.viewport(800, 500)
		cy.mountStory(stories.large)
		
		cy.get('.editor').should('have.css', 'height', '322px')
		cy.get('lvl-rich-text').as('editor').invoke('attr', 'size', 'medium')
		cy.get('.editor').should('have.css', 'height', '184px')
		
		cy.get('@editor').invoke('attr', 'size', 'small')
		cy.get('.editor').should('have.css', 'height', '126px')

		cy.get('@editor').invoke('attr', 'size', 'large')
		cy.get('.editor').should('have.css', 'height', '322px')
		
		cy.get('.editor__toolbar').as('editorToolbar').find('[data-action=open-dots]').should('not.exist')
		cy.get('@editorToolbar').children('[data-action=color]').should('exist')
		cy.get('@editorToolbar').children('[data-action=highlight]').should('exist')
		cy.get('@editorToolbar').children('[data-action=unmark]').should('exist')
		cy.get('@editorToolbar').children('[data-action=open-alignment]').should('exist')
		cy.get('@editorToolbar').children('[data-action=open-list]').should('exist')
		cy.get('@editorToolbar').children('[data-action=open-link]').should('exist')
		cy.get('@editorToolbar').children('[data-action=add-image]').should('exist')

		cy.viewport(550, 500)
		cy.get('@editorToolbar').children('[data-action=color]').should('exist')
		cy.get('@editorToolbar').children('[data-action=highlight]').should('exist')
		cy.get('@editorToolbar').children('[data-action=unmark]').should('exist')

		cy.get('@editorToolbar').children('[data-action=open-alignment]').should('not.exist')
		cy.get('@editorToolbar').children('[data-action=open-list]').should('not.exist')
		cy.get('@editorToolbar').children('[data-action=open-link]').should('not.exist')
		cy.get('@editorToolbar').children('[data-action=add-image]').should('not.exist')
		
		cy.get('@editorToolbar').find('[data-action=open-dots]').should('exist')
		cy.get('@editorToolbar').find('[name=dots-dropdown] lvl-menu').as('dotsMenu')
		cy.get('@dotsMenu').children('[data-action=open-alignment]').should('exist')
		cy.get('@dotsMenu').children('[data-action=open-list]').should('exist')
		cy.get('@dotsMenu').children('[data-action=open-link]').should('exist')
		cy.get('@dotsMenu').children('[data-action=add-image]').should('exist')

		cy.viewport(350, 500)
		cy.get('@editorToolbar').children('[data-action=color]').should('exist')
		cy.get('@editorToolbar').children('[data-action=highlight]').should('exist')
		cy.get('@editorToolbar').children('[data-action=unmark]').should('exist')
		cy.get('@editorToolbar').children('[data-action=open-alignment]').should('not.exist')
		cy.get('@editorToolbar').children('[data-action=open-list]').should('not.exist')
		cy.get('@editorToolbar').children('[data-action=open-link]').should('not.exist')
		cy.get('@editorToolbar').children('[data-action=add-image]').should('not.exist')

		cy.get('@editorToolbar').find('[data-action=open-dots]').should('exist')
		cy.get('@editorToolbar').find('[name=dots-dropdown] lvl-menu').as('dotsMenu')
		cy.get('@dotsMenu').children('[data-action=open-alignment]').should('exist')
		cy.get('@dotsMenu').children('[data-action=open-list]').should('exist')
		cy.get('@dotsMenu').children('[data-action=open-link]').should('exist')
		cy.get('@dotsMenu').children('[data-action=add-image]').should('exist')
		cy.get('@dotsMenu').children('[data-action=open-alignment]').should('exist')
		cy.get('@dotsMenu').children('[data-action=open-list]').should('exist')
		cy.get('@dotsMenu').children('[data-action=open-link]').should('exist')
		cy.get('@dotsMenu').children('[data-action=add-image]').should('exist')
	})
	
	storyTest('checks visible skeleton', stories.skeleton, () => {
		cy.mountStory(stories.skeleton)
		cy.get('.skeleton__block').should('exist')
	})

	storyTest('checks placeholder content', stories.placeholder, () => {
		cy.mountStory(stories.placeholder)

		cy.get('lvl-rich-text').as('editor')
		cy.get('@editor').find(`[contenteditable] [data-placeholder="${stories.placeholder.getAttribute<string>('placeholder')}"]`)
	})

	storyTest('toggle required', stories.required, () => {
		cy.mountStory(stories.required)

		cy.get('lvl-rich-text').as('editorComponent')
		cy.get('@editorComponent').find(`.editor`).as('editor').should('not.have.css', 'borderLeftColor', getExpectedColor('rgb(222, 88, 88)', 'rgb(248, 113, 113)'))
		cy.get('@editor').find('.editor__content').scrollTo('center').type('{selectAll}{del}')
		cy.get('@editorComponent').find(`.editor`).as('editor').should('have.css', 'borderLeftColor', getExpectedColor('rgb(222, 88, 88)', 'rgb(248, 113, 113)'))
	})

	storyTest('check readonly restrictions', stories.readonly, () => {
		cy.mountStory(stories.readonly)

		cy.get('lvl-rich-text').as('editor')
		cy.get('@editor').find(`.editor__content > [contenteditable]`).as('editorContent')
		cy.get('@editor').find('.editor__toolbar').should('not.exist')
		cy.get('@editorContent').click().type('Das ist neu', { force: true })
		cy.get('@editor').should('have.prop', 'value', stories.readonly.getAttribute('value'))
	})

	storyTest('checks placeholder content', stories.error, () => {
		cy.mountStory(stories.error)

		cy.get('lvl-rich-text').as('editor')
		cy.get('@editor').find('label:has(~ .editor)').should('have.text', stories.error.getAttribute('label'))
		cy.get('@editor').find('.editor ~ legend').should('have.text', stories.error.getAttribute('legend'))
		cy.get('@editor').find('.editor > lvl-input-icon').should('have.attr', 'tooltip' , stories.error.getAttribute('error'))
		
		cy.get('@editor').invoke('attr', 'label', 'test label')
		cy.get('@editor').invoke('attr', 'legend', 'test legend')
		cy.get('@editor').invoke('attr', 'error', 'test error')

		cy.get('@editor').find('label:has(~ .editor)').should('have.text', 'test label')
		cy.get('@editor').find('.editor ~ legend').should('have.text', 'test legend')
		cy.get('@editor').find('.editor > lvl-input-icon').should('have.attr', 'tooltip' , 'test error')
	})
})