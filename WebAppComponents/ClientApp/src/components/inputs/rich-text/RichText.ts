import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { InputElement, InputElementType } from '@/components/inputs/InputElement.ts'
import { Size } from '@/enums/size.ts'
import { query } from 'lit/decorators/query.js'
import { Editor, JSONContent } from '@tiptap/core'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { Align } from '@/enums/align.ts'
import { ifDefined } from 'lit/directives/if-defined.js'
import { ColorFormat } from '@/enums/color-format.ts'
import { ColorPicker } from '@/components/color-picker/ColorPicker.ts'
import { Dialog } from '@/components/dialog/Dialog'
import { Form } from '@/components/form/Form.ts'
import { ButtonType } from '@/components/atomics/button/Button.ts'
import { ColorState } from '@/enums/color-state'
import { inputStyles } from '../inputStyles'
import Placeholder from '@tiptap/extension-placeholder'
import { PopupPlacement } from '@/components/popup/Popup.ts'
import { editorExtensions } from '@/shared/rich-text-helper.ts'

export type RichTextType = InputElementType & {
	size: Size
	maximized: boolean
	skeleton: boolean
}

const enum TextType {
	Title = 'h1',
	Heading = 'h2',
	Subheading = 'h3',
	Text = 'text-size'
}

const enum ListType {
	Ordered = 'list-ol',
	Unordered = 'list-ul'
}

const enum ColorType {
	Text = 'text',
	Highlight = 'highlight'
}

const enum ContentSizeType {
	Large = 625,
	Medium = 470,
	Small = 335
}

/**
 * Example web component using LIT (https://lit.dev)
 * https://tiptap.dev/docs/editor/getting-started/overview
 */
@customElement('lvl-rich-text')
export class RichText extends InputElement(LitElement) implements RichTextType {

	// enable animations for vanishing scrollbar
	static {
		const documentStyles = css`
			@property --scrollbar-color {
				syntax: "<color>";
				inherits: true;
				initial-value: transparent;
			}
    `;
		document.adoptedStyleSheets.push(documentStyles.styleSheet!);
	}
	
	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		styles.vanishingScrollbar,
		styles.skeleton,
		styles.animation,
		styles.richTextStyles,
		inputStyles,
		css`
			:host {
				width: 100%;
				max-width: 100%;

				--second-column: 0rem;
				--toolbar-height: 3.2rem;
			}

			:host(:not([size])) {
				display: flex;
				flex-direction: column;
				height: var(--editor-height);

				& .editor {
					flex-grow: 1;
					height: auto;
				}

				& > legend, label {
					flex-shrink: 0;
				}
			}

			:host([size]) {
				display: block;

				& .editor {
					resize: vertical;
					height: var(--editor-height);
					min-height: var(--editor-min-height);
				}
			}

			:host([size=small]) {
				--editor-height: 12.6rem;
				--editor-min-height: 12.6rem;
			}

			:host([size=medium]), :host {
				--editor-height: 18.4rem;
				--editor-min-height: 18.4rem;
			}

			:host([size=large]) {
				--editor-height: 32.2rem;
				--editor-min-height: 32.2rem;
			}

			:host([maximized]) .editor {
				position: fixed;
				height: 100vh;
				width: 100vw;
				z-index: 10000;
				left: 0;
				top: 0;

				& .editor {
					width: 100%;
					height: 100%;
				}
			}

			:host([error]) {
				--second-column: 2.4rem
			}

			:host([required]:not([readonly])) .editor {
				border-left: 3px solid var(--cp-clr-border-medium);
			}

			:host([required]:not([readonly])) .editor:has(.editor__content .is-editor-empty) {
				border-left-color: var(--cp-clr-signal-error);
			}

			:host([readonly]) .editor {
				background-color: var(--cp-clr-state-readonly);
				border-color: var(--cp-clr-state-readonly);
				color: var(--cp-clr-text-secondary);
			}

			:host([skeleton]) .editor * {
				visibility: hidden;
			}

			.editor__separator {
				height: 2rem;
				border-left: 1px solid var(--cp-clr-border-medium);
			}

			.editor {
				display: grid;
				grid-template-rows: 0 auto;
				grid-template-columns: auto var(--second-column);
				width: 100%;

				background-color: var(--cp-clr-background-lvl-0);

				border: 1px solid var(--cp-clr-border-medium);
				border-radius: var(--size-radius-m);

				overflow-y: auto;
				overflow-x: hidden;

				transition: grid-template-rows linear var(--animation-time-medium);

				&:has([contenteditable]:focus-visible) {
					outline: 2px solid var(--cp-clr-state-focus);
				}
			}

			:host(:is(:focus-visible, :focus-within, :focus)) .editor {
				grid-template-rows: var(--toolbar-height) auto;

				& .editor__toolbar {
					translate: 0;
				}

				& .editor__content {
					padding-top: 0;
				}
			}

			lvl-input-icon {
				padding: 0;
				margin: calc(-1 * var(--toolbar-height)) 0 0;
			}

			.editor__toolbar {
				grid-column: span 2;
				overflow-x: hidden;
				padding: 0 var(--size-spacing-m);
				height: var(--toolbar-height);
				translate: 0 calc(-1 * var(--toolbar-height) + var(--size-spacing-s));
				transition: translate linear var(--animation-time-medium);
			}

			.editor__toolbar:has(lvl-button:not(:defined)) {
				visibility: hidden;
			}

			.editor__content {
				overflow: auto;
				padding: var(--size-spacing-m) var(--size-spacing-m) var(--size-spacing-m) var(--size-spacing-l);

				grid-row-start: 2;
				grid-row-end: -1;

				transition: padding linear var(--animation-time-medium);
			}

			.editor__content:is(:focus-visible, :focus-within, :focus) > [contenteditable] {
				outline: none;
			}

			.editor__content > [contenteditable] {
				min-height: 100%;
				cursor: text;
			}

			.flex-list {
				display: flex;
				align-items: center;
				column-gap: var(--size-spacing-s);
			}

			.flex--end {
				flex-grow: 1;
				justify-content: end;
			}

			.dialog__content {
				display: grid;
				gap: var(--size-spacing-m);
				padding: var(--size-spacing-l);
			}

			.color__block {
				position: relative;
				width: 2rem;
				height: 2rem;
				border-radius: var(--size-radius-m);
				border: 1px solid var(--cp-clr-border-medium);
				cursor: pointer;
				overflow: hidden;
			}

			.color__block--text {
				background-color: var(--cp-clr-text-primary-positiv);
			}

			.color__block--background {
				background: transparent;

				&::after {
					content: '';
					position: absolute;
					left: 0;
					width: 25px;
					border: 1px solid var(--cp-clr-signal-error);
					rotate: -45deg;
					transform-origin: left top;
					bottom: 0;
				}
			}

			lvl-button[trailing-icon] {
				width: 4.6rem;
				min-width: 4.6rem;
			}

			*:not(:defined) {
				display: none;
			}
		`,
	]

	//#region attributes

	@property()
	size: Size = Size.Medium

	@property({ type: Boolean, reflect: true })
	maximized: boolean = false

	@property({ type: Boolean, reflect: true })
	skeleton: boolean = false

	//#endregion

	//#region states

	public get value(): string {
		return this.editor?.getText() ?? ''
	}

	@property()
	public set value(value: string | JSONContent) {
		const passContent = (content: string | JSONContent) => {
			// try to parse string as json
			try {
				if(typeof content == 'string')
					content = JSON.parse(content)
			} catch(_){}
			this.editor.commands.setContent(content)
		}

		if (this.editor)
			passContent(value)
		else
			this.updateComplete.then(() => passContent(value))
	}

	/* Text style */

	@state()
	private isBold: boolean = false

	@state()
	private isItalic: boolean = false

	@state()
	private isUnderline: boolean = false

	@state()
	private isStrikethrough: boolean = false

	@state()
	private alignment: Align = Align.Left

	@state()
	private textType: TextType = TextType.Text

	@state()
	private color?: string

	@state()
	private markerColor?: string

	@state()
	private listType: ListType | null = null

	@state()
	private isLink: boolean = false

	@state()
	private widthType: ContentSizeType = ContentSizeType.Large

	//#endregion states

	//#region private properties

	@query('.editor')
	private _htmlEditor!: HTMLElement

	@query('.editor__content')
	private _htmlEditorContent!: HTMLElement

	@query('[name="link-dialog"]')
	private _htmlLinkDialog!: Dialog

	@query('[name="image-dialog"]')
	private _htmlImageDialog!: Dialog

	@query('[name=text-color-picker]')
	private _htmlColorPicker!: ColorPicker

	@query('[name=background-color-picker]')
	private _htmlMarkerPicker!: ColorPicker

	private editor!: Editor

	private resizeObserver?: ResizeObserver

	private static readonly _localizer: StringLocalizer = new StringLocalizer('RichText')

	private localize(key: string) {
		return RichText._localizer.localize(key)
	}

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		return html`
			${this.labelHTML}
			<div class="editor ${this.skeleton ? 'skeleton__block' : ''}" tabindex="-1" style="min-width: ${ContentSizeType.Small}px">
				${this.readonly ? '' : html`
					<div class="editor__toolbar flex-list">
						<!-- history -->
						<lvl-button data-action="undo" size="${Size.Medium}" icon="arrow-turn-left" @click="${() => this.editor.chain().focus().undo().run()}" tooltip="${this.localize('undo')}" tabindex="-1"></lvl-button>
						<lvl-button data-action="redo" size="${Size.Medium}" icon="arrow-turn-right" @click="${() => this.editor.chain().focus().redo().run()}" tooltip="${this.localize('redo')}" tabindex="-1"></lvl-button>
	
						<!-- text -->
						<span class="editor__separator"></span>
						<lvl-button data-action="open-text-size" size="${Size.Medium}" icon="${this.textTypeIcon}" trailing-icon="angle-down" data-dropdown="size-dropdown"
												tooltip="${this.localize('textSize')}" tabindex="-1"></lvl-button>
						<lvl-dropdown name="size-dropdown">
							<lvl-menu>
								<lvl-select-list .value="${this.textType}" separated>
									<lvl-select-list-item data-action="h1" icon="h1" label="${this.localize('title')}" @click="${() => this.setTextLevel(1)}" .value="${TextType.Title}"></lvl-select-list-item>
									<lvl-select-list-item data-action="h2" icon="h2" label="${this.localize('heading')}" @click="${() => this.setTextLevel(2)}" .value="${TextType.Heading}"></lvl-select-list-item>
									<lvl-select-list-item data-action="h3" icon="h3" label="${this.localize('subheading')}" @click="${() => this.setTextLevel(3)}" .value="${TextType.Subheading}"></lvl-select-list-item>
									<lvl-select-list-item data-action="text" icon="text-size" label="${this.localize('text')}" @click="${() => this.unsetTextLevel()}" .value="${TextType.Text}"></lvl-select-list-item>
								</lvl-select-list>
							</lvl-menu>
						</lvl-dropdown>
	
						<!-- style -->
						<lvl-button data-action="bold" size="${Size.Medium}" icon="solid/bold" @click="${this.toggleBold}" .selected="${this.isBold}"
												tooltip="${this.localize('bold')}" tabindex="-1"></lvl-button>
						<lvl-button data-action="italic" size="${Size.Medium}" icon="italic" @click="${this.toggleItalic}" .selected="${this.isItalic}"
												tooltip="${this.localize('italic')}" tabindex="-1"></lvl-button>
						<lvl-button data-action="underline" size="${Size.Medium}" icon="underline" @click="${this.toggleUnderline}" .selected="${this.isUnderline}"
												tooltip="${this.localize('underline')}" tabindex="-1"></lvl-button>
						<lvl-button data-action="strike-through" size="${Size.Medium}" icon="strikethrough" @click="${this.toggleStrikethrough}" .selected="${this.isStrikethrough}"
												tooltip="${this.localize('strikethrough')}" tabindex="-1"></lvl-button>
	
						${this.widthType >= ContentSizeType.Medium ? this.renderMediumPackage() : ''}
						${this.widthType == ContentSizeType.Large ? this.renderLargePackage() : ''}
						${this.widthType < ContentSizeType.Large ? this.renderThreeDotsMenu() : ''}
						
	
						<!-- maximize -->
						<div class="flex-list flex--end">
							<lvl-button icon="arrows-maximize" size="${Size.Medium}" @click="${this.toggleMaximize}" tabindex="-1" style="display: none;"></lvl-button>
						</div>
					</div>
				`}
				<div class="editor__content vanishing-scrollbar static-scrollbar html__wrapper"></div>
				${this.errorHTML}
			</div>
			${this.legendHTML}

			${this.readonly ? '' : html`
				<!-- dialogs -->
				<lvl-dialog name="link-dialog" icon="link" heading="${this.localize('addLink')}" includes-section="true">
					<lvl-form class="dialog__content">
						<lvl-input name="text" label="${this.localize('linkDialogText')}"></lvl-input>
						<lvl-input name="url" label="URL" required></lvl-input>
					</lvl-form>
					<lvl-button slot="button-left" data-action="cancel" label="${this.localize('buttonCancel')}" color="${ColorState.Info}"
											@click="${this.handleDialogCancelClick}"></lvl-button>
					<lvl-button slot="button-right" type="${ButtonType.Primary}" data-action="confirm" label="${this.localize('buttonConfirm')}"
											@click="${this.handleLinkDialogConfirmClick}"></lvl-button>
				</lvl-dialog>
				<lvl-dialog name="image-dialog" icon="image" heading="${this.localize('addImage')}" includes-section="true">
					<lvl-form class="dialog__content">
						<lvl-input name="url" label="URL" required></lvl-input>
						<lvl-input name="alt" label="${this.localize('imageDialogAlt')}"></lvl-input>
						<lvl-input name="title" label="${this.localize('imageDialogTitle')}"></lvl-input>
					</lvl-form>
					<lvl-button slot="button-left" data-action="cancel" label="${this.localize('buttonCancel')}" color="${ColorState.Info}"
											@click="${this.handleDialogCancelClick}"></lvl-button>
					<lvl-button slot="button-right" type="${ButtonType.Primary}" data-action="confirm" label="${this.localize('buttonConfirm')}"
											@click="${this.handleImageDialogConfirmClick}"></lvl-button>
				</lvl-dialog>
			`}
		`
	}

	private renderMediumPackage = () => html`
		<!-- color -->
		<lvl-button data-action="color" size="${Size.Medium}" icon="${this.color ? 'solid/' : ''}palette" trailing-icon="angle-down" data-dropdown="text-color-dropdown"
								tooltip="${this.localize('textColor')}" tabindex="-1"
								style="${ifDefined(this.color ? `--icon-color: ${this.color};` : undefined)}"></lvl-button>
		<lvl-dropdown name="text-color-dropdown">
			${this.renderColorPicker()}
		</lvl-dropdown>

		<!-- highlight -->
		<lvl-button data-action="highlight" size="${Size.Medium}" icon="${this.markerColor ? 'solid/' : ''}highlighter" trailing-icon="angle-down"
								data-dropdown="background-color-dropdown" tooltip="${this.localize('backgroundColor')}" tabindex="-1"
								style="${ifDefined(this.markerColor ? `--icon-color: ${this.markerColor};` : undefined)}"></lvl-button>
		<lvl-dropdown name="background-color-dropdown">
			${this.renderMarkerPicker()}
		</lvl-dropdown>

		<!-- unmark -->
		<lvl-button data-action="unmark" size="${Size.Medium}" icon="text-slash" @click="${this.unmark}" tooltip="${this.localize('removeMark')}" tabindex="-1"></lvl-button>
	`

	private renderMenuMediumPackage = () => html`
		<!-- color -->
		<lvl-menu-item data-action="color" icon-left="${this.color ? 'solid/' : ''}palette" .selected="${this.color != null}">
			${this.localize('textColor')}
			${this.renderColorPicker(true)}
		</lvl-menu-item>

		<!-- highlight -->
		<lvl-menu-item data-action="highlight" icon-left="${this.markerColor ? 'solid/' : ''}highlighter" .selected="${this.markerColor != null}">
			${this.localize('backgroundColor')}
			${this.renderMarkerPicker(true)}
		</lvl-menu-item>

		<!-- unmark -->
		<lvl-menu-item data-action="unmark" icon-left="text-slash" @click="${this.unmark}">${this.localize('removeMark')}</lvl-menu-item>
		<lvl-menu-divider></lvl-menu-divider>
	`

	private renderLargePackage = () => html`
		<!-- alignment -->
		<span class="editor__separator"></span>
		<lvl-button data-action="open-alignment" size="${Size.Medium}" icon="align-${this.alignment}" trailing-icon="angle-down" data-dropdown="alignment-dropdown"
								tooltip="${this.localize('alignment')}" tabindex="-1"></lvl-button>
		<lvl-dropdown name="alignment-dropdown">
			<lvl-menu>
				<lvl-select-list .value="${this.alignment}" separated>
					${this.renderAlignmentSelectItems()}
				</lvl-select-list>
			</lvl-menu>
		</lvl-dropdown>
		
		<!-- list -->
		<lvl-button data-action="open-list" size="${Size.Medium}" icon="${this.listType ?? 'list'}" trailing-icon="angle-down" data-dropdown="list-dropdown"
								tooltip="${this.localize('list')}" tabindex="-1"></lvl-button>
		<lvl-dropdown name="list-dropdown">
			<lvl-menu>
				<lvl-select-list .value="${this.listType}" separated>
					${this.renderListSelectItems()}
				</lvl-select-list>
			</lvl-menu>
		</lvl-dropdown>

		<!-- link -->
		<span class="editor__separator"></span>
		<lvl-button data-action="open-link" size="${Size.Medium}" icon="${this.isLink ? 'solid/' : ''}link" trailing-icon="angle-down" data-dropdown="link-dropdown"
								tooltip="${this.localize('linkTitle')}" tabindex="-1"></lvl-button>
		<lvl-dropdown name="link-dropdown">
			<lvl-menu size="${Size.Medium}">
				${this.renderLinkMenuItems()}
			</lvl-menu>
		</lvl-dropdown>

		<!-- media -->
		<lvl-button data-action="add-image" size="${Size.Medium}" icon="image" @click="${this.handleImageDialogOpenClick}" tooltip="${this.localize('addImage')}" tabindex="-1"></lvl-button>
	`

	private renderLargeHiddenPackage = () => html`
		<!-- alignment -->
		<lvl-menu-item data-action="open-alignment" icon-left="align-${this.alignment}">
			${this.localize('alignment')}
			<lvl-menu slot="submenu" size="${Size.Medium}">
				<lvl-select-list .value="${this.alignment}" separated>
					${this.renderAlignmentSelectItems()}
				</lvl-select-list>
			</lvl-menu>
		</lvl-menu-item>
		
		<!-- list -->
		<lvl-menu-item data-action="open-list" icon-left="${this.listType ?? 'list'}">
			${this.localize('list')}
			<lvl-menu slot="submenu" size="${Size.Medium}">
				<lvl-select-list .value="${this.listType}" separated>
					${this.renderListSelectItems()}
				</lvl-select-list>
			</lvl-menu>
		</lvl-menu-item>

		<!-- link -->
		<lvl-menu-divider></lvl-menu-divider>
		<lvl-menu-item data-action="open-link" icon-left="${this.isLink ? 'solid/' : ''}link">
			${this.localize('linkTitle')}
			<lvl-menu slot="submenu" size="${Size.Medium}">
				${this.renderLinkMenuItems()}
			</lvl-menu>
		</lvl-menu-item>

		<!-- media -->
		<lvl-menu-item data-action="add-image" icon-left="image" @click="${this.handleImageDialogOpenClick}">${this.localize('addImage')}</lvl-menu-item>
	`

	private renderColorPicker = (asSubmenu?: boolean) => html`
		<!-- color-picker -->
		<lvl-color-picker slot="${ifDefined(asSubmenu ? 'submenu' : undefined)}" name="text-color-picker" placement="${asSubmenu ? PopupPlacement.RightStart : PopupPlacement.BottomEnd}" relative
											label="${this.localize('textColor')}" value="${ifDefined(this.color)}" format="${ColorFormat.Hex}" lite-mode>
			<div slot="special" class="color">
				<div class="color__block color__block--text" @click="${(event: MouseEvent) => this.handleUnsetColorClick(event, ColorType.Text)}"></div>
				<span class="color__label">${this.localize('textColorResetLabel')}</span>
			</div>
		</lvl-color-picker>
	`

	private renderMarkerPicker = (asSubmenu?: boolean) => html`
		<!-- marker-picker -->
		<lvl-color-picker slot="${ifDefined(asSubmenu ? 'submenu' : undefined)}" name="background-color-picker" relative placement="${asSubmenu ? PopupPlacement.RightStart : PopupPlacement.BottomEnd}"
											label="${this.localize('backgroundColor')}" value="${ifDefined(this.markerColor)}" format="${ColorFormat.Hex}" lite-mode>
			<div slot="special" class="color">
				<div class="color__block color__block--background" @click="${(event: MouseEvent) => this.handleUnsetColorClick(event, ColorType.Highlight)}"></div>
				<span class="color__label">${this.localize('backgroundColorResetLabel')}</span>
			</div>
		</lvl-color-picker>
	`

	private renderAlignmentSelectItems = () => html`
		<lvl-select-list-item data-action="align-left" icon="align-left" label="${this.localize('alignLeft')}" @click="${() => this.setAlignment(Align.Left)}"
													.value="${Align.Left}"></lvl-select-list-item>
		<lvl-select-list-item data-action="align-center" icon="align-center" label="${this.localize('alignCenter')}"
													@click="${() => this.setAlignment(Align.Center)}"
													.value="${Align.Center}"></lvl-select-list-item>
		<lvl-select-list-item data-action="align-right" icon="align-right" label="${this.localize('alignRight')}" @click="${() => this.setAlignment(Align.Right)}"
													.value="${Align.Right}"></lvl-select-list-item>
		<lvl-select-list-item data-action="align-justify" icon="align-justify" label="${this.localize('alignJustify')}" @click="${() => this.setAlignment(Align.Justify)}"
													.value="${Align.Justify}"></lvl-select-list-item>
	`

	private renderListSelectItems = () => html`
		<lvl-select-list-item data-action="list-none" icon="minus" label="${this.localize('noList')}" @click="${() => this.unsetListType()}"
													.value="${null}"></lvl-select-list-item>
		<lvl-select-list-item data-action="list-ol" icon="list-ol" label="${this.localize('orderedList')}" @click="${() => this.setListType(ListType.Ordered)}"
													.value="${ListType.Ordered}"></lvl-select-list-item>
		<lvl-select-list-item data-action="list-ul" icon="list-ul" label="${this.localize('unorderedList')}" @click="${() => this.setListType(ListType.Unordered)}"
													.value="${ListType.Unordered}"></lvl-select-list-item>
	`

	private renderLinkMenuItems = () => html`
		<lvl-menu-item data-action="add-link" icon-left="link" @click="${this.handleLinkDialogOpenClick}">${this.localize('addLink')}</lvl-menu-item>
		<lvl-menu-item data-action="remove-link" icon-left="link-slash" @click="${this.removeLink}">${this.localize('removeLink')}</lvl-menu-item>
	`

	private renderThreeDotsMenu = () => html`
		<span class="editor__separator"></span>
		<lvl-button data-action="open-dots" size="${Size.Medium}" icon="ellipsis-vertical" data-dropdown="dots-dropdown" tabindex="-1"></lvl-button>
		<lvl-dropdown name="dots-dropdown" placement="${PopupPlacement.BottomEnd}">
			<lvl-menu size="${Size.Medium}">
				${this.widthType < ContentSizeType.Medium ? this.renderMenuMediumPackage() : ''}
				${this.widthType < ContentSizeType.Large ? this.renderLargeHiddenPackage() : ''}
			</lvl-menu>
		</lvl-dropdown>
	`

	//#region lifecycle callbacks
	
	connectedCallback() {
		super.connectedCallback()
		
		if(this.innerHTML?.trim())
			this.value = this.innerHTML
	}

	protected firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)

		this.initEditor()
		this.initResizeObserver()

		// react to clicking on a color to change it for the rich text editor
		this._htmlEditor.addEventListener('color-picker-change', event => {
			const picker = event.target as ColorPicker
			if (picker == this._htmlColorPicker) {
				this.color = picker.value
				this.editor!.chain().focus().extendMarkRange('textStyle').setColor(this.color).run()
				return
			}
			
			if (picker == this._htmlMarkerPicker) {
				this.markerColor = picker.value
				this.editor.chain().focus().extendMarkRange('highlight').setHighlight({ color: this.markerColor }).run()
				return
			}
		})
	}

	disconnectedCallback() {
		super.disconnectedCallback()
		this.editor?.destroy()
		this.resizeObserver?.disconnect()
	}

	//#endregion

	//#region public methods

	/**
	 * Returns the JSON representation of the editor content
	 */
	public get formValue() {
		if (!this.editor)
			return null

		return this.editor.getJSON()
	}

	//#endregion

	//#region private methods

	// create an instance for the tiptap editor
	private initEditor() {
		const initContent = this._initValue ?? undefined
		const richText = this

		let timeout: NodeJS.Timeout
		this.editor = new Editor({
			element: this._htmlEditorContent,
			extensions: [
				...editorExtensions, Placeholder.configure({
					placeholder: this.placeholder,
				}),
			],
			content: initContent,
			editable: !this.readonly,
			onSelectionUpdate() {
				richText.updateToolbarButtonState()
			},
			onUpdate: () => {
				// The editor isn’t focused anymore
				clearTimeout(timeout)
				timeout = setTimeout(() => {
					this.dispatchEvent(new Event('change', { bubbles: true }))
				}, 250)
			},
		})
	}

	private initResizeObserver() {
		this.resizeObserver = new ResizeObserver((entries: ResizeObserverEntry[]) => {
			window.requestAnimationFrame(() => {
				if (!Array.isArray(entries) || !entries.length)
					return
				
				const { inlineSize } = entries[0].borderBoxSize[0]
				
				// width was changed
				if (inlineSize > ContentSizeType.Large) {
					this.widthType = ContentSizeType.Large
					return
				}

				if (inlineSize > ContentSizeType.Medium) {
					this.widthType = ContentSizeType.Medium
					return
				}

				this.widthType = ContentSizeType.Small
			})
		})
		this.resizeObserver.observe(this._htmlEditor, { box: 'border-box' })
	}

	private updateStateTimeout?: NodeJS.Timeout

	updateToolbarButtonState() {
		clearTimeout(this.updateStateTimeout)
		this.updateStateTimeout = setTimeout(() => {

			// update marks
			this.isBold = this.editor.isActive('bold')
			this.isItalic = this.editor.isActive('italic')
			this.isUnderline = this.editor.isActive('underline')
			this.isStrikethrough = this.editor.isActive('strike')

			// update textsize
			if (!this.editor.isActive('heading'))
				this.textType = TextType.Text
			else if (this.editor.isActive('heading', { level: 1 }))
				this.textType = TextType.Title
			else if (this.editor.isActive('heading', { level: 2 }))
				this.textType = TextType.Heading
			else if (this.editor.isActive('heading', { level: 3 }))
				this.textType = TextType.Subheading

			// update colors
			this.color = this.editor.getAttributes('textStyle').color
			this.markerColor = this.editor.isActive('highlight') ? this.editor.getAttributes('highlight').color : undefined

			// update alignment
			if (this.editor.isActive({ textAlign: 'center' }))
				this.alignment = Align.Center
			else if (this.editor.isActive({ textAlign: 'right' }))
				this.alignment = Align.Right
			else if (this.editor.isActive({ textAlign: 'justify' }))
				this.alignment = Align.Justify
			else
				this.alignment = Align.Left

			// update list
			if (this.editor.isActive('bulletList'))
				this.listType = ListType.Unordered
			else if (this.editor.isActive('orderedList'))
				this.listType = ListType.Ordered
			else
				this.listType = null

			// update link
			this.isLink = this.editor.isActive('link')
		}, 150)
	}

	private toggleBold() {
		this.editor.chain().focus().toggleBold().run()
		this.isBold = this.editor.isActive('bold') ?? false
	}

	private toggleItalic() {
		this.editor.chain().focus().toggleItalic().run()
		this.isItalic = this.editor.isActive('italic') ?? false
	}

	private toggleUnderline() {
		this.editor.chain().focus().toggleUnderline().run()
		this.isUnderline = this.editor.isActive('underline') ?? false
	}

	private toggleStrikethrough() {
		this.editor.chain().focus().toggleStrike().run()
		this.isStrikethrough = this.editor.isActive('strike') ?? false
	}

	private get textTypeIcon() {
		return this.textType.toString()
	}

	private setTextLevel(level: 1 | 2 | 3) {
		this.editor.chain().focus().setHeading({ level }).run()
		switch (level) {
			case 1:
				this.textType = TextType.Title
				break
			case 2:
				this.textType = TextType.Heading
				break
			case 3:
				this.textType = TextType.Subheading
				break
		}
	}

	private unsetTextLevel() {
		this.editor.chain().focus().setParagraph().run()
		this.textType = TextType.Text
	}

	private handleUnsetColorClick(event: MouseEvent, type: ColorType) {
		const target = event.currentTarget as HTMLElement
		const picker = target.closest('lvl-color-picker') as ColorPicker

		if (!picker)
			return

		// unset the color
		switch (type) {
			case ColorType.Highlight:
				this.markerColor = undefined
				this.editor.chain().focus().unsetHighlight().run()
				break
			case ColorType.Text:
				this.color = undefined
				this.editor.chain().focus().unsetColor().run()
				break
		}
	}

	private setListType(type: ListType) {
		this.listType = type
		
		if (type === ListType.Ordered && !this.editor.isActive('orderedList')) {
			this.editor.commands.focus()
			if(this.editor.isActive('bulletList'))
				this.editor.commands.toggleBulletList()
			this.editor.commands.toggleOrderedList()
			return
		} 
		
		if (type === ListType.Unordered && !this.editor.isActive('bulletList')) {
			this.editor.commands.focus()
			if(this.editor.isActive('orderedList'))
				this.editor.commands.toggleOrderedList()
			this.editor.commands.toggleBulletList()
			return
		}
	}

	private unsetListType() {
		this.listType = null
		
		if (this.editor.isActive('orderedList'))
			this.editor.chain().focus().toggleOrderedList().run()
		else if (this.editor.isActive('bulletList'))
			this.editor.chain().focus().toggleBulletList().run()
	}

	private setAlignment(alignment: Align) {
		this.alignment = alignment
		if (alignment == Align.Left) {
			this.editor.chain().focus().unsetTextAlign().run()
			return
		}
		
		this.editor.chain().focus().setTextAlign(alignment).run()
	}

	private unmark() {
		this.editor.chain().focus().unsetAllMarks().run()
		this.updateToolbarButtonState()
	}

	private addLink(options: Record<string, any>) {
		let url = options.url
		if (!new RegExp(/^\w+?:\/\//).test(url))
			url = 'https://' + url

		const text = options.text || url
		this.editor.chain().focus().extendMarkRange('link').insertContent(`<a href="${url}">${text}</a>`).setLink({ href: url }).run()
		this.isLink = true
	}

	private removeLink() {
		this.editor.chain().focus().unsetLink().run()
	}

	private addImage(options: Record<string, any>) {
		let url = options.url
		if (!new RegExp(/^\w+?:\/\//).test(url))
			url = 'https://' + url

		this.editor.commands.setImage({
			src: url,
			alt: options.alt,
			title: options.title,
		})
	}

	private toggleMaximize() {
		this.maximized = !this.maximized
	}

	private handleLinkDialogOpenClick() {
		this._htmlLinkDialog.open = true
		const form = this._htmlLinkDialog.querySelector('lvl-form') as Form
		if (!form)
			return

		// mark the whole link if possible
		this.editor.commands.extendMarkRange('link')

		// fill in the form if text has been selected
		const { view, state } = this.editor
		const { from, to } = view.state.selection
		const url = this.editor.getAttributes('link').href
		let text = state.doc.textBetween(from, to, '')

		form.setValues({ text, url })
		return
	}

	private handleLinkDialogConfirmClick() {
		const form = this._htmlLinkDialog.querySelector('lvl-form') as Form
		if (!form)
			return

		if (form.hasEmptyRequiredElements())
			return

		const values = form.getValues()
		this.addLink(values)
		this._htmlLinkDialog.open = false
		form.reset()
	}

	private handleImageDialogOpenClick() {
		this._htmlImageDialog.open = true
	}

	private handleImageDialogConfirmClick() {
		const form = this._htmlImageDialog.querySelector('lvl-form') as Form
		if (!form)
			return

		if (form.hasEmptyRequiredElements())
			return

		const values = form.getValues()
		this.addImage(values)
		this._htmlImageDialog.open = false
		form.reset()
	}

	private handleDialogCancelClick(event: MouseEvent) {
		const dialog = (event.target as HTMLElement)?.parentElement as Dialog
		if (!dialog)
			return
		dialog.open = false
		this.clearDialogForm(dialog)
	}

	private clearDialogForm(dialog: Dialog) {
		const form = dialog.querySelector('lvl-form') as Form
		form.reset()
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-rich-text': RichText
	}
}