import { css, html, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { InputElement } from '@/components/inputs/InputElement.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { ifDefined } from 'lit-html/directives/if-defined.js'
import { live } from 'lit/directives/live.js'
import { renderIcon } from '@/shared/component-html.ts'
import { Size } from '@/enums/size.ts'
import { DataFilter } from '@/shared/types.ts'
import { Operator } from '@/enums/operator.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'

export type SearchbarType = {
	rounded: boolean
	mainBar: boolean
	withSorting: boolean
	size: Size
	disabled: boolean
}

/**
 * Example web component using LIT (https://lit.dev)
 */
@customElement('lvl-search')
export class Searchbar extends InputElement(LitElement) implements SearchbarType {

	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		fontAwesome,
		css`
			:host {
				--size: 3.2rem;
				--radius: var(--size-radius-m);
				--inline-radius: var(--size-radius-m);
				
				display: inline-block;
			}

			:host([size=small]) {
				--size: 2.4rem;
			}

			:host([rounded]) {
				--radius: var(--size-radius-pill);
				--inline-radius: var(--size-radius-circle);
			}
			
			:host(:hover) {
				--border-color: var(--cp-clr-border-strong);
			}
			
			:host([disabled]) {
				--border-color: var(--cp-clr-state-inactive);
				
				& .input-wrapper {
					cursor: default;
				}
				
				& input {
					background: unset;
				}
				
				& .search-icon, input, [data-action="sort"], input::placeholder {
					color: var(--cp-clr-state-inactive);
				}
			}

			:host(:not([disabled])) .input-wrapper:focus-within {
				--border-color: var(--cp-clr-state-focus);
			}
			
			.searchbar {
				display: flex;
				align-items: center;
				column-gap: var(--size-spacing-s);
			}

			.input-wrapper {
				position: relative;
				max-width: 100%;
				
				display: flex;
				flex-grow: 1;
				align-items: center;
				column-gap: var(--size-spacing-s);

				height: var(--size);

				padding: 0 var(--size-spacing-m);
				border: 1px solid var(--border-color, var(--cp-clr-border-medium));
				border-radius: var(--radius);
				background-color: var(--cp-clr-background-lvl-0);
				cursor: text;
				overflow: clip;
			}

			:host([main-bar]:not([disabled])) .input-wrapper:not(:focus-within):has(input:placeholder-shown)::after {
				content: attr(data-short-key);
				position: absolute;
				font-size: var(--size-text-s);
				top: 50%;
				right: var(--size-spacing-m);
				color: var(--cp-clr-state-inactive);
				translate: 0 -50%;
			}

			:host([disabled]) .clear-button, input:placeholder-shown ~ .clear-button {
				visibility: hidden;
				pointer-events: none;
			}

			input {
				flex-grow: 1;

				width: 0;
				background-color: var(--cp-clr-background-lvl-0);
				color: var(--cp-clr-text-primary-positiv);
				outline: none;
				border: none;
			}

			input::placeholder {
				color: var(--cp-clr-text-tertiary);
			}

			.search-icon {
				color: var(--cp-clr-text-secondary);
			}
		`,
	]

	//#region attributes

	@property({ type: Boolean, reflect: true })
	rounded: boolean = false

	@property({ type: Boolean, attribute: 'main-bar' })
	mainBar: boolean = false

	@property({ type: Boolean, attribute: 'with-sorting' })
	withSorting: boolean = false

	@property({ reflect: true })
	size: Size = Size.Medium

	//#endregion

	//#region states
	//#endregion states

	//#region private properties

	private static readonly _localizer: StringLocalizer = new StringLocalizer('Searchbar')

	private abortController: AbortController = new AbortController()

	private localize(key: string) {
		return Searchbar._localizer.localize(key)
	}

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		return html`
			<div class="searchbar">
				${this.withSorting ? html`<lvl-button icon="bars-filter" ?disabled="${true}" tooltip="${this.localize('mvpTooltip')}" size="${this.size === Size.Small ? Size.Medium : Size.Large}"
										?rounded="${this.rounded}" data-action="sort"></lvl-button>` : ''}
				<div class="input-wrapper" data-short-key="${this.localize('shortKey')}">
					${renderIcon('search', { additionalClasses: [ 'search-icon' ] })}
					<input .value="${live(ifDefined(this.displayValue ?? undefined))}"
								 placeholder="${ifDefined(this.localize('search'))}"
								 ?required="${this.required}"
								 ?disabled="${this.disabled}"
								 tabindex="${this.skeleton || this.preview || this.readonly ? -1 : 0}"
								 @change="${(event: MouseEvent) => event.stopPropagation()}"
								 @keydown="${this.handleKeydown}"/>
					${renderIcon('close', {
						additionalClasses: [ 'clear-button' ],
						title: this.localize('clear'),
						onClick: event => this.handleClearButtonClick(event),
						disabled: this.readonly,
						data: { action: 'clear' },
					})}
				</div>
			</div>
		`
	}

	//#region lifecycle callbacks

	connectedCallback() {
		super.connectedCallback()
		window.addEventListener('keydown', (event: KeyboardEvent) => this.handleWindowKeydown(event), { signal: this.abortController.signal })
	}

	disconnectedCallback() {
		super.disconnectedCallback()
		this.abortController.abort()
	}

	//#endregion

	//#region public methods

	/**
	 * Adds the fulltext search value to the passed filters or removes it 
	 * @param filters
	 */
	public addToFilters(filters?: DataFilter[]): DataFilter[] {
		const newFilters = filters?.filter(filter => filter.operator !== Operator.FulltextSearch) ?? []
		if(this._input.value)
			newFilters.push({ filterColumn: '', operator: Operator.FulltextSearch, compareValue: this._input.value })
		return newFilters
	}
	
	//#endregion

	//#region private methods

	private handleWindowKeydown(event: KeyboardEvent) {
		if (!this.mainBar)
			return
		if (event.ctrlKey && event.key === 'f') {
			this._input?.focus()
			event.preventDefault()
		}
	}

	private handleClearButtonClick(event: MouseEvent) {
		this._input!.value = ''
		this.value = ''
		this._input.focus()
		event.stopPropagation()
	}

	private handleKeydown(event: KeyboardEvent) {
		switch (event.key) {
			case 'Enter':
				this._input!.blur()
				this.value = this._input!.value
				break
			case 'Escape':
				this._input!.blur()
		}
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-search': Searchbar
	}
}