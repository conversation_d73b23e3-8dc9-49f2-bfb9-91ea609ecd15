import { css, LitElement, PropertyValues } from 'lit'
import { property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { Unit } from '@/enums/unit.ts'

type Constructor<T> = new (...args: any[]) => T;

/**
 * Interface for every column with user defined attributes
 */
export type BaseColumnProperties = {
	// column style properties
	name: string
	label?: string
	width?: number
	widthUnit?: Unit
	minWidth?: number
	minWidthUnit?: Unit
	maxWidth?: number
	maxWidthUnit?: Unit
	withConverter?: boolean
	converter?: (data: Record<string, any>, index: number) => any
	sticky?: boolean
}

export type BaseColumnInterface = BaseColumnProperties & {
	// column style properties
	isData: boolean
	isReady: boolean
}

/**
 * Mixin Web component using LIT (https://lit.dev)
 */
export const BaseColumnElement = <T extends Constructor<LitElement>>(superClass: T) => {
	class ColumnElement extends superClass {

		static styles = [
			styles.base,
			styles.color,
			css`
				:host {

				}
			`,
		]

		//#region attributes

		@property()
		label?: string

		@property()
		name: string = ''

		@property({ type: Number })
		width: number = 0

		@property({ attribute: 'width-unit' })
		widthUnit: Unit = Unit.Pixel
		
		@property({ type: Number, attribute: 'min-width' })
		minWidth: number = 0

		@property({ attribute: 'min-width-unit' })
		minWidthUnit: Unit = Unit.Pixel

		@property({ type: Number, attribute: 'max-width' })
		maxWidth: number = 0

		@property({ attribute: 'max-width-unit' })
		maxWidthUnit: Unit = Unit.Pixel

		@property({ type: Boolean, attribute: 'with-converter' })
		withConverter: boolean = false

		@property({ type: Function })
		converter?: (data: Record<string, any>, index: number) => any
		
		@property({ type: Boolean })
		sticky: boolean = false

		isReady: boolean = false

		isData: boolean = false
		
		isThumbnail: boolean = false

		//#endregion

		//#region states
		//#endregion states

		//#region private properties
		//#endregion

		//#region lifecycle callbacks
		
		protected createRenderRoot() {
			return this
		}

		connectedCallback() {
			super.connectedCallback();
		}

		protected firstUpdated(_changedProperties: PropertyValues) {
			super.firstUpdated(_changedProperties)
			this.isReady = true
		}

		protected updated(_changedProperties: PropertyValues<this>) {
			super.updated(_changedProperties)
			if (_changedProperties.has('converter') && this.converter)
				this.dispatchEvent(new CustomEvent('converter:registered', { bubbles: true }))
			this.dispatchEvent(new CustomEvent('column:changed', { bubbles: true, detail: _changedProperties }))
		}
		
		//#endregion


		//#region public methods
		//#endregion

		//#region private methods
		
		//#endregion

	}

	return ColumnElement as Constructor<BaseColumnInterface> & T
}
