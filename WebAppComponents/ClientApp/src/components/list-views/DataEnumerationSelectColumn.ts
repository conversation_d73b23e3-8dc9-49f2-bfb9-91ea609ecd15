import { DataEnumerationSpecialColumn } from '@/components/list-views/DataEnumerationColumn.ts'
import { html } from 'lit'
import { CheckboxState } from '@/enums/checkbox-state.ts'
import { DataEnumeration, DataEnumerationRow } from '@/components/list-views/DataEnumeration.ts'

export type SelectEventParams = {
	row: DataEnumerationRow
	column: DataEnumerationSelectColumn
}

/**
 * Class representation for multi select function within an enumeration
 */
export class DataEnumerationSelectColumn extends DataEnumerationSpecialColumn {

	selectState: CheckboxState = CheckboxState.Off

	constructor(host: DataEnumeration) {
		super(host, {
			position: -1,
			name: 'selectAll',
			isSpecial: true,
		})
		this.setFixedWidth(50)
	}

	renderInHead() {
		return html`
			<div class="column cell cell--special">
				<lvl-checkbox class="clickable" data-action="select-all"
											@click="${(event: MouseEvent) => this.handleClickInHead(event)}"
											?checked="${this.selectState === CheckboxState.On}"
											?indeterminate="${this.selectState === CheckboxState.Inderterminate}">
			</div>
		`
	}

	renderInRow(row: DataEnumerationRow) {
		return html`
			<div class="cell row__select cell--special">
				${row.skeleton ? '' : html`
					<lvl-checkbox class="row__select-input clickable" data-row-action="select"
												@click="${(event: MouseEvent) => this.handleClickInRow(event, row)}"
												?checked="${row.selected}">
				`}
			</div>
		`
	}

	private handleClickInRow(event: MouseEvent, row: DataEnumerationRow) {
		let customEvent = new CustomEvent<SelectEventParams>('select-single:click', {
			bubbles: true,
			detail: { row, column: this },
		})
		event.target!.dispatchEvent(customEvent)
	}

	private handleClickInHead(event: MouseEvent) {
		let customEvent = new CustomEvent('select-all:click', {
			bubbles: true,
		})
		event.target!.dispatchEvent(customEvent)
	}
}