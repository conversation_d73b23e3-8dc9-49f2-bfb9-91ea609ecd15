import { LitElement, PropertyValues } from 'lit'
import { property, state } from 'lit/decorators.js'
import { DataFilter, DataSorting, SelectParameters } from '@/shared/types.ts'
import { ApiController } from '@/controllers/api-controller.ts'
import { includesAny } from '@/shared/data-operations.ts'
import { ContentData, ItemType, QueryViewFilterInterface, QueryViewItemPatchEvent, QueryViewRowClickEvent } from '@/components/list-views/types.ts'
import { QueryViewNavigationInterface } from '@/components/list-views/QueryViewNavigation.ts'
import { offsetToPage } from '@/components/list-views/calculations.ts'
import { FilterableElement } from '@/components/data-organizers/filter-mixins/FilterableElement.ts'
import { QueryViewContent } from '@/components/list-views/QueryViewContent.ts'
import CommunicationServiceProvider, { CommunicationResponseType } from '@/shared/communication-service.ts'

type Constructor<T> = new (...args: any[]) => T

export type QueryViewProperties = {
	id?: string
	url?: string
	startOffset: number
	limit?: number
	sorting: DataSorting[]
	filterPlaceholders: string[]
	filters: DataFilter[]
	skeleton: boolean
	loadingOverlay: boolean
	onRowClick?: (record: ItemType, index: number, newTab: boolean) => void
	onCreateClick?: () => void
	payload: Record<string, any>
}

export type QueryViewInterface = QueryViewProperties & {
	payload: Record<string, any>
	apiController: ApiController
	totalCount: number
	items: ItemType[]
	itemCount: number
	lastDataFetch: number
	reload: () => Promise<void>
	updateContent: () => Promise<void>
}

const DEPENDENT_FIELDS_FOR_RELOAD = [ 'sorting', 'filters', 'url', 'limit', 'startOffset', 'placeholderValues' ]

/**
 * Mixin Web component using LIT (https://lit.dev)
 */
export const QueryView = <T extends Constructor<LitElement>>(superClass: T) => {
	class QueryViewElement extends FilterableElement(superClass) {

		//#region controllers

		apiController = new ApiController(
			this,
			(rows, isLoading) => {
				this.totalCount = this.apiController.count
				this.setContentState(rows, isLoading)
			},
			this.getAttribute('data-request-key'),
		)

		//#endregion

		//#region attributes

		@property()
		url?: string

		@property({ type: Array<DataSorting> })
		sorting: DataSorting[] = []

		@property({ type: Number })
		limit?: number

		@property({ type: Number, attribute: 'offset' })
		startOffset: number = 0

		@property({ type: Boolean, reflect: true })
		skeleton: boolean = false

		@property({ type: Boolean, attribute: 'loading-overlay' })
		loadingOverlay: boolean = false

		@property({ type: Function })
		onRowClick?: (record: ItemType, index: number, newTab: boolean) => void

		@property({ type: Function })
		onCreateClick?: () => void
		
		@property({ type: Object })
		payload?: Record<string, any>

		//#endregion

		//#region states

		@state()
		totalCount: number = 0
		
		//#endregion

		//#region private/protected properties

		@state()
		lastDataFetch: number = 0

		protected get navigationComponent(): QueryViewNavigationInterface | null {
			return null
		}

		protected get contentComponent(): QueryViewContent | null {
			return this.querySelector(`:is(${this.validContentComponents.join(',')})[initDone]`)
		}

		protected get filterComponent(): QueryViewFilterInterface | null {
			return null
		}

		protected get validContentComponents(): string[] {
			return []
		}

		//#endregion

		//#region lifecycle functions

		connectedCallback() {
			super.connectedCallback()
			this.addEventListener('top-button:toggle', (event: Event) => {
				this.navigationComponent?.toggleTopButtonActive((event as CustomEvent<boolean>).detail)
				event.stopPropagation()
			})

			this.addEventListener('query-view:registered', async (event: Event) => {
				if (event.target != this.contentComponent)
					return

				if (this.contentComponent == null)
					return
				
				this.contentComponent.managed = true

				// set local data if no url was passed (works only for one content component!)
				if (!this.url)
					this.apiController.localData = this.contentComponent.items.filter(item =>  !item.skeleton && item.data != null).map(item => item.data as Record<string, any>)

				await this.updateContent()
			})

			this.addEventListener('query-view:row-click', (event: Event) => {
				if (!this.onRowClick)
					return

				const customEvent = event as CustomEvent<QueryViewRowClickEvent>
				this.onRowClick(customEvent.detail.record, customEvent.detail.index, customEvent.detail.newTab)
			})

			this.addEventListener('query-view:create-click', () => {
				if (!this.onCreateClick)
					return

				this.onCreateClick()
			})
		}

		async willUpdate(_changedProperties: PropertyValues) {
			super.willUpdate(_changedProperties)

			// Offset has to be changed in relation to the changedLimit
			if (_changedProperties.has('startOffset') && this.navigationComponent)
				this.navigationComponent.page = offsetToPage(this.startOffset, this.limit)

			// reset offset when limit has been changed to prevent an overflow
			if (_changedProperties.get('limit')) {
				this.startOffset = 0
				if (this.navigationComponent != null)
					this.navigationComponent.page = 1
			}

			// pass sorting to the data columns
			if (_changedProperties.has('sorting') && this.contentComponent)
				this.contentComponent.displaySorting(this.sorting)
			
			// check if placeholder values was really changed (lit detects a change everytime a new object gets set, even if the values inside are identical)
			if (_changedProperties.has('placeholderValues') && JSON.stringify(_changedProperties.get('placeholderValues')) == JSON.stringify(this.placeholderValues))
				_changedProperties.delete('placeholderValues')
			
			if (includesAny(Array.from(_changedProperties.keys()) as string[], DEPENDENT_FIELDS_FOR_RELOAD))
				await this.reload()
		}

		protected firstUpdated(_changedProperties: PropertyValues) {
			super.firstUpdated(_changedProperties)

			if (this.contentComponent)
				this.contentComponent.managed = true

			this.addEventListener('query-view:scroll', (event: Event) => {
				this.handleContentScrolling().then()
				event.stopPropagation()
			})

			this.shadowRoot!.addEventListener('navigation:changed', async (event: Event) => {
				this.updateOffsetByPage().then()
				event.stopPropagation()
			})

			this.shadowRoot!.addEventListener('top-button:click', (event: Event) => {
				this.contentComponent?.scrollableElement?.scrollTo({ top: 0, behavior: 'smooth'})
				event.stopPropagation()
			})

			this.addEventListener('query-view:sorting:change', (event: Event) => {
				const customEvent = event as CustomEvent<DataSorting[]>
				this.sorting = customEvent.detail
			})

			this.addEventListener('query-view:patch-item', async (event: Event) => {
				const customEvent = event as CustomEvent<QueryViewItemPatchEvent>
				const item = customEvent.detail
				event.stopPropagation()

				if (!this.url)
					return
				
				const response = await CommunicationServiceProvider.patch(this.url, item.slug, {
					body: JSON.stringify({ ...(this.payload ?? {}), ...item.data }),
					headers: { 'Content-Type': 'application/json' },
				})

				// reset input value to the origin value
				if (response.state == CommunicationResponseType.Error) {
					if (item.errorCallback != null)
						item.errorCallback()
				}
			})
		}
		
		updated(_changedProperties: PropertyValues) {
			if (_changedProperties.has('filters'))
				this.dispatchEvent(new CustomEvent('filter:changed'))
			if (_changedProperties.has('sorting'))
				this.dispatchEvent(new CustomEvent('sorting:changed'))
		}

		//#endregion

		//#region public methods

		/**
		 * redraw the content component with current data
		 */
		public async updateContent() {
			if (this.contentComponent == null)
				return

			// update sorting
			this.contentComponent.displaySorting(this.sorting)
			
			const fetchInfo = this.apiController.fetchInfo
			const areRequiredFieldsNotIncluded = () => fetchInfo?.fields && this.contentComponent!.queryFields.some(field => !fetchInfo?.fields!.includes(field))

			// make full loading if no data was fetched until now
			if (!this.lastDataFetch || !fetchInfo?.fileInfoIncluded && this.contentComponent.isFileInfoRequired() || areRequiredFieldsNotIncluded()) {
				await this.reload()
				return
			}

			// pass rows to content component if at least one fetch was made and all requirements are fulfilled
			await this.setContentState(this.apiController.rows, this.apiController.isLoading)
		}
		
		public get items(): ItemType[] {
			return this.contentComponent?.items ?? []
		}

		/**
		 * Retrieves the number of visible rows
		 */
		public get itemCount() {
			return this.apiController.rows.length
		}

		/**
		 * Reloads the enumeration and fetches the data
		 */
		public async reload() {
			if (!this.contentComponent || !this.contentComponent.managed)
				return
			
			// prevent reload as long as filter placeholder values are not set
			if (this.filterPlaceholders.length > 0 && Object.keys(this.placeholderValues).length == 0)
				return

			const selectParameters: SelectParameters = {
				sortings: this.sorting,
				filters: this.filters,
				...(this.limit ? {
					limit: this.limit,
					offset: this.startOffset,
				} : {}),
			}

			selectParameters.fields = this.contentComponent.queryFields
			selectParameters.includeFileInfo = this.contentComponent.isFileInfoRequired()

			this.dispatchEvent(new CustomEvent('query-view:loading-start', { bubbles: true }))
			this.contentComponent?.scrollableElement?.scrollTo({ top: 0, behavior: 'instant'})
			await this.apiController.queryRows(this.url, selectParameters)
			this.dispatchEvent(new CustomEvent('query-view:loading-done', { bubbles: true }))

			// if more then the first page is already visible on the viewport the next page needs to be reloaded  
			if (!this.limit && this.totalCount > 0)
				return
			
			setTimeout(async () => {
				let offset = this.limit! + this.startOffset
				let hasNewSkeletons = await this.tryAppendSkeletons()
				while (hasNewSkeletons) {
					await this.apiController.queryRows(this.url, {
						...selectParameters,
						offset: offset + this.startOffset,
					}, { insertAt: offset })
					hasNewSkeletons = await this.tryAppendSkeletons()
					offset += this.limit!
				}
			})

			return
		}

		public get scrollTop() {
			return this.contentComponent?.scrollTop ?? 0
		}

		public set scrollTop(position: number) {
			if (this.contentComponent)
				this.contentComponent.scrollTop = position
		}

		//#endregion

		//#region private methods

		/**
		 * Update the content component with the newest rows
		 */
		private async setContentState(rows: ContentData[], isLoading: boolean) {
			// Is the content already up to date?
			this.lastDataFetch = this.apiController.rowChangedTimestamp

			if (!this.contentComponent)
				return

			this.contentComponent.rows = rows

			if (isLoading)
				return

			// update filter dialog if any
			if (this.filterComponent)
				this.filterComponent.data = this.contentComponent.items.filter(item => !item.skeleton && item.data != null).map(item => item.data)

			await this.contentComponent.updateComplete
			this.dispatchEvent(new CustomEvent('query-view:changed'))
		}

		protected async updateOffsetByPage() {
			if (!this.navigationComponent)
				return

			const oldOffset = this.startOffset
			if (this.limit)
				this.startOffset = (this.navigationComponent.page - 1) * this.limit

			// offset changes triggers the reload automatically -> therefore we have to reload manually if the offset does not change 
			if (oldOffset === this.startOffset)
				await this.reload()
		}

		private async handleContentScrolling() {
			if (!this.limit)
				return

			// if the page is reloading no page append procedure is necessary because the request replace all existing data
			if (this.apiController.isLoading && !this.apiController.hiddenLoading)
				return

			await this.tryAppendSkeletons()

			const visiblePages = this.contentComponent?.findVisiblePages(this.startOffset, this.limit ?? 0)
			if (!visiblePages)
				return

			// set page number in navigation bar		
			if (this.navigationComponent)
				this.navigationComponent.page = visiblePages[0].pageNumber

			const parameters: SelectParameters = {
				limit: this.limit,
				sortings: this.sorting,
				filters: this.filters,
			}

			if (this.contentComponent) {
				parameters.fields = this.contentComponent.queryFields
				parameters.includeFileInfo = this.contentComponent.isFileInfoRequired()
			}

			await this.apiController.appendPages(this.url, parameters, visiblePages)
		}

		// append skeletons to the list as a sign for loading more
		private async tryAppendSkeletons(): Promise<boolean> {
			const skeletonOffset = this.itemCount + this.startOffset
			const skeletonsNeeded = this.itemCount !== 0 && skeletonOffset < this.apiController.count && this.contentComponent && this.contentComponent.isLastLineVisible()
			if (!skeletonsNeeded)
				return false

			const skeletonCount = Math.min(this.apiController.count - skeletonOffset, this.limit ?? 0)
			if (skeletonCount === 0)
				return false

			const newRows = this.apiController.rows
			for (let index = 0; index < skeletonCount; index++) {
				newRows.push(undefined)
			}
			this.contentComponent!.rows = [ ...newRows ]
			await this.contentComponent!.updateComplete
			return true
		}

		//#endregion

	}

	return QueryViewElement as Constructor<QueryViewInterface> & T
}