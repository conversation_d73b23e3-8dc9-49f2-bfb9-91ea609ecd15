import { LitElement } from "lit";
import { ContentData, ItemType, PageType } from '@/components/list-views/types.ts'
import { DataSorting } from '@/shared/types.ts'

export abstract class QueryViewContent extends LitElement {
	managed: boolean = false
	abstract set rows(data: ContentData[])
	abstract get rows(): ContentData[]
	abstract get items(): ItemType[]

	/**
	 * 'Scroll To the Top' button was clicked
	 */
	abstract get scrollableElement(): HTMLElement

	/**
	 * Which packages of items are currently visible
	 * @param offset
	 * @param limit
	 */
	abstract findVisiblePages(offset: number, limit: number): PageType[]

	/**
	 * Is the last item in the content component visible?
	 */
	abstract isLastLineVisible(): boolean

	/**
	 * Is called when the sorting was changed from outside
	 * @param sorting
	 */
	abstract displaySorting(sorting: DataSorting[]): void

	/**
	 * Does the client have to ask for file information?
	 */
	abstract isFileInfoRequired(): boolean

	/**
	 * Gets the field names for the fetch request
	 */
	abstract get queryFields(): string[]
	
	get selectedItems() {
		return this.items?.filter(item => item.selected) ?? []
	}
}