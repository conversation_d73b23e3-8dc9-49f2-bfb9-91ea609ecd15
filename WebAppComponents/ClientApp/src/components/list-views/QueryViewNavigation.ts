import { css, html, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { IconOptions, renderIcon } from '@/shared/component-html.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { query } from 'lit/decorators/query.js'
import { getPageCount } from '@/components/list-views/calculations.ts'
import { Size } from '@/enums/size.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'

export type QueryViewNavigationProperties = {
	page: number
	count: number
	limit?: number
	withPagination?: boolean
}

export type QueryViewNavigationInterface = QueryViewNavigationProperties & {
	toggleTopButtonActive: (enable: boolean) => void
}

/**
 * Example web component using LIT (https://lit.dev)
 */
@customElement('lvl-query-view-navigation')
export class QueryViewNavigation extends LitElement implements QueryViewNavigationInterface {

	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		styles.icon,
		fontAwesome,
		css`
			:host {
				border-top: 1px solid var(--cp-clr-border-medium);
			}

			.control {
				display: grid;
				column-gap: 0.5rem;
				align-items: center;
				overflow-x: auto;
				white-space: nowrap;
				grid-auto-flow: column;
			}

			.control:has(.control__left + .control__center + .control__right) {
				grid-template-columns: 1fr max-content 1fr;
			}

			.control__left {
				justify-self: start;

				display: flex;
				align-items: center;
				gap: var(--size-spacing-m);
			}

			.control__center {
				justify-self: center;
			}

			.control__right {
				justify-self: end;
				
				& #paging-summary {
					margin-right: var(--size-spacing-l);
				}
			}

			#navigation {
				user-select: none;
				height: 100%;

				/* to top button */

				& [data-action="to-top"] {
					margin-left: var(--size-spacing-m);
					padding: var(--size-spacing-xs);
				}

				& .navigation__text {
					width: 70px;
					text-align: center;
				}

				& .pagination {
					display: flex;
					gap: 0.5rem;
					justify-self: center;
					align-items: center;

					& .icon {
						font-size: 0.75em;
						width: 2em;
						height: 2em;
					}
				}
			}
		`,
	]

	//#region attributes

	@property({ type: Number, reflect: true })
	page: number = 1

	@property({ type: Number, reflect: true })
	count: number = 0

	@property({ type: Number })
	limit: number = 0
	
	@property({ type: Boolean, attribute: 'with-pagination' })
	withPagination: boolean = false 

	//#endregion

	//#region states	
	//#endregion states

	//#region private properties

	@query('[data-action="to-top"]')
	private _htmlTopButton!: HTMLElement

	private static readonly _baseLocalizer: StringLocalizer = new StringLocalizer('DataEnumeration')

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		return html`
			<nav id="navigation" class="scrollbar-styled control">
				<div class="control__left">
					<lvl-button data-action="to-top" label="${QueryViewNavigation._baseLocalizer.localize('ButtonTop')}"
											class="disabled"
											icon="arrow-up-to-line"
											size="${Size.Medium}"
											@click="${() => this.handleToTopButtonClick()}"></lvl-button>
				</div>
				<div class="control__center pagination">
					${this.limit && this.count && this.withPagination ? html`
						${this.renderNavigationDoubleLeftIcon()}
						${this.renderNavigationLeftIcon()}
						<span
							class="navigation__text">${this.page} ${QueryViewNavigation._baseLocalizer.localize('NavigationOf')} ${getPageCount(this.count, this.limit)}</span>
						${this.renderNavigationRightIcon()}
						${this.renderNavigationDoubleRightIcon()}
					` : ''}
				</div>
				<div class="control__right">
					<span id="paging-summary">${this.getNavigationCountText()}</span>
				</div>
			</nav>
		`
	}

	// First paging icon
	private renderNavigationDoubleLeftIcon = () => {
		let options: IconOptions = { data: { 'action': 'first' } }
		if (this.page === 1)
			options.additionalClasses = [ 'disabled' ]
		else {
			options.onClick = () => this.updatePage(1)
		}
		return renderIcon('angles-left', options)
	}

	// Left paging icon
	private renderNavigationLeftIcon = () => {
		let options: IconOptions = { data: { 'action': 'previous' } }
		if (this.page === 1)
			options.additionalClasses = [ 'disabled' ]
		else {
			options.onClick = () => this.updatePage(this.page - 1)
		}
		return renderIcon('angle-left', options)
	}

	// Right paging icon 
	private renderNavigationRightIcon = () => {
		let options: IconOptions = { data: { 'action': 'next' } }
		if (this.page === getPageCount(this.count, this.limit))
			options.additionalClasses = [ 'disabled' ]
		else {
			options.onClick = () => this.updatePage(this.page + 1)
		}
		return renderIcon('angle-right', options)
	}

	// Last paging icon 
	private renderNavigationDoubleRightIcon = () => {
		let options: IconOptions = { data: { 'action': 'last' } }
		if (this.page === getPageCount(this.count, this.limit))
			options.additionalClasses = [ 'disabled' ]
		else {
			options.onClick = () => {
				this.updatePage(getPageCount(this.count, this.limit))
			}
		}
		return renderIcon('angles-right', options)
	}

	//#region lifecycle callbacks
	//#endregion

	//#region public methods

	toggleTopButtonActive(enable: boolean) {
		if (enable)
			this._htmlTopButton.classList.remove('disabled')
		else
			this._htmlTopButton.classList.add('disabled')
	}

	//#endregion

	//#region private methods
	
	private updatePage(newPage: number) {
		this.page = newPage
		this.dispatchEvent(new CustomEvent('navigation:changed', { bubbles: true }))
	}

	private handleToTopButtonClick() {
		this.dispatchEvent(new CustomEvent('top-button:click', { bubbles: true }))
	}

	private getNavigationCountText() {
		let navigationText: string
		switch (this.count) {
			case 0:
				navigationText = QueryViewNavigation._baseLocalizer.localize('ZeroEntries')
				break
			case 1:
				navigationText = QueryViewNavigation._baseLocalizer.localize('SingleEntry')
				break
			default:
				if (this.limit) {
					navigationText = QueryViewNavigation._baseLocalizer.localize('entryCount', this.count)
				} else
					navigationText = QueryViewNavigation._baseLocalizer.localize('MultipleEntries', this.count)
				break
		}
		return navigationText
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-query-view-navigation': QueryViewNavigation
	}
}