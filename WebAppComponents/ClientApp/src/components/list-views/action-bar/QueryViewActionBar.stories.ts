import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { html, TemplateResult } from 'lit'
import { LevelStory } from '@story-home/support/commands'
import { QueryViewActionBarType } from './QueryViewActionBar'
import { Size } from '@/enums/size.ts'
import { ifDefined } from 'lit/directives/if-defined.js'

import('@/components/atomics/tooltip/Tooltip')
import('@/components/popup/Popup')
import('@/components/atomics/button/Button')
import('@/components/atomics/fry/Fry')
import('@/components/inputs/InputButton')
import('@/components/inputs/autocomplete/Autocomplete')
import('@/components/list-views/select-list/SelectListItem')
import('@/components/list-views/select-list/SelectList')
import('@/components/dropdown/DropdownMenuDivider')
import('@/components/dropdown/DropdownMenu')
import('@/components/dropdown/Dropdown')
import('./QueryViewActionBar')

/* 
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

export type QueryViewActionBarProperties = Partial<QueryViewActionBarType & { children: TemplateResult[] }>
type QueryViewActionBarStory = Story<QueryViewActionBarProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-query-view-action-bar',
	tags: [ 'autodocs' ],
	render: (_args: QueryViewActionBarProperties) => html`
		<lvl-query-view-action-bar ?disabled="${_args.disabled}" ?with-create="${_args.withCreate}" ?with-select-all="${_args.withSelectAll}" ?with-search="${_args.withSearch}"
															 ?with-columns="${_args.withColumns}" ?with-display-type="${_args.withDisplayType}" ?with-favorite-action="${_args.withFavoriteAction}" 
															 ?with-inactive-action="${_args.withInactiveAction}" ?embedded="${_args.embedded}" count="${ifDefined(_args.count)}" 
															 selected-count="${ifDefined(_args.selectedCount)}" current-view="${_args.currentView}" .views="${ifDefined(_args.views)}">
			${_args.children}
		</lvl-query-view-action-bar>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		disabled: {
			control: 'boolean',
			description: 'disables all actions',
			table: {
				type: { summary: 'boolean' },
			},
		},
		withSelectAll: {
			control: 'boolean',
			description: 'adds a select all button',
			table: {
				type: { summary: 'boolean' },
			},
		},
		withFavoriteAction: {
			control: 'boolean',
			description: 'adds a favorite button',
			table: {
				type: { summary: 'boolean' },
			},
		},
		withInactiveAction: {
			control: 'boolean',
			description: 'adds an inactive button',
			table: {
				type: { summary: 'boolean' },
			},
		},
		withSearch: {
			control: 'boolean',
			description: 'adds a search option',
			table: {
				type: { summary: 'boolean' },
			},
		},
		withColumns: {
			control: 'boolean',
			description: 'adds a dropdown to show and hide columns',
			table: {
				type: { summary: 'boolean' },
			},
		},
		withDisplayType: {
			control: 'boolean',
			description: 'adds an option to switch between different display types, likes table, grid and others',
			table: {
				type: { summary: 'boolean' },
			},
		},
		embedded: {
			control: 'boolean',
			description: 'Is the bar used for an emb pages?',
			table: {
				type: { summary: 'boolean' },
			},
		},
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a default QueryViewActionBar
 */
export const Default: QueryViewActionBarStory = {
	args: {
		withSelectAll: true,
		withSearch: false,
		withColumns: true,
		withDisplayType: true,
	},
}

/**
 * Appearance of a default QueryViewActionBar
 */
export const Embedded: QueryViewActionBarStory = {
	args: {
		withSelectAll: true,
		withSearch: true,
		withColumns: true,
		withDisplayType: true,
		embedded: true
	},
}

/**
 * Appearance of a QueryViewActionBar with disabled state
 */
export const Disabled: QueryViewActionBarStory = {
	args: {
		withSelectAll: true,
		withSearch: true,
		withColumns: true,
		withDisplayType: true,
		disabled: true,
	},
}

/**
	* Appearance of a QueryViewActionBar with default actions
	*/
export const DefaultActions: QueryViewActionBarStory = {
	args: {
		withSelectAll: true,
		withFavoriteAction: true,
		withInactiveAction: true,
		selectedCount: 0,
		count: 10
	},
}

/**
 * Appearance of a QueryViewActionBar with custom selects and buttons
 */
export const Custom: QueryViewActionBarStory = {
	args: {
		withSelectAll: true,
		withSearch: true,
		selectedCount: 10,
		count: 10,
		children: [
			html`
				<lvl-autocomplete placeholder="Choose responsible">
					<lvl-body>
						<lvl-option value="me">Me</lvl-option>
						<lvl-option value="notMe">Not me</lvl-option>
					</lvl-body>
				</lvl-autocomplete>`,
			html`
				<lvl-autocomplete placeholder="Choose body part">
					<lvl-body>
						<lvl-option value="arm">Arm</lvl-option>
						<lvl-option value="leg">Leg</lvl-option>
						<lvl-option value="ear">Ear</lvl-option>
					</lvl-body>
				</lvl-autocomplete>`,
			html`
				<lvl-button size="${Size.Medium}" icon="sliders-simple" label="Other filter"></lvl-button>`,
		],
		withDisplayType: true,
		currentView: 'gallery',
		views: [
			{ label: 'Table', value: 'table', icon: 'table-list' }, 
			{ label: 'Gallery', value: 'gallery', icon: 'image' },
			{ label: 'Grid', value: 'grid', icon: 'objects-column' },
		],
	},
}


//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
	embedded: new LevelStory(meta, Embedded, 'Embedded bar'),
	disabled: new LevelStory(meta, Disabled, 'Disabled action bar'),
	custom: new LevelStory(meta, Custom, 'Action bar with custom buttons'),
	actions: new LevelStory(meta, DefaultActions, 'Action bar with default actions'),
} as const