import { <PERSON>a, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands.ts'
import { ifDefined } from 'lit-html/directives/if-defined.js'
import { DataColumnDefinitionType } from '@/components/list-views/DataEnumerationDataColumn.ts'
import * as storyHome from '@/components/list-views/DataEnumeration.storyhome.ts'
import { DataEnumerationProperties, getBaseLevelStories } from '@/components/list-views/DataEnumeration.storyhome.ts'
import { DataType } from '@/enums/data-type.ts'
import { DataListType } from '@/components/list-views/data-list/DataList.ts'
import { SortDirection } from '@/enums/sort-direction.ts'

import('./DataList')

export type DataListProperties = Partial<DataListType> & DataEnumerationProperties
export type DataListStory = Story<DataListProperties>

const meta: Meta = {
	component: 'lvl-data-list',
	render: (_args: DataListProperties) => {
		return html`
			<lvl-data-list rows="${ifDefined(JSON.stringify(_args.rows))}"
										 style="${`width: ${_args.width || '100%'};`}${_args.height ? `height: ${_args.height};` : ''}"
										 url="${ifDefined(_args.url)}"
										 .loadData="${ifDefined(_args.loadData)}"
										 .onRowClick="${ifDefined(_args.onRowClick)}"
										 offset="${ifDefined(_args.offset)}"
										 limit="${ifDefined(_args.limit)}"
										 filters="${ifDefined(JSON.stringify(_args.filters))}"
										 sortings="${ifDefined(JSON.stringify(_args.sortings))}"
										 ?paging=${_args.paging}
										 identity-column="${ifDefined(_args.identityColumn)}"
										 label-column="${ifDefined(_args.labelColumn)}"
										 ?responsive=${_args.responsive}
										 ?searchbar=${_args.searchBar}
										 group-by="${ifDefined(_args.groupByField)}"
										 ?selectable=${_args.selectable}
			>
				${_args.columns?.map((column: DataColumnDefinitionType) => html`
					<lvl-data-column
						position="${column.position}"
						name="${column.name}"
						label="${column.label}"
						?hidden="${column.hidden}"
						?hide-label=${column.hideLabel}
						type="${ifDefined(column.type)}"
						text-align="${ifDefined(column.textAlign)}"
						min-width="${ifDefined(column.minWidth)}"
						max-width="${ifDefined(column.maxWidth)}"
						decimal-places="${ifDefined(column.decimalPlaces)}"
						sign="${ifDefined(column.sign)}"
						?live-editable="${column.liveEditable}"
						with-thousand-separators="${ifDefined(column.withThousandSeparators)}"
					>
					</lvl-data-column>
				`)}
				${_args.selectable ? html`
					<lvl-button slot="select-action" data-name=approve label="Approve" tooltip="Approve this list" icon="list-check"></lvl-button>
					<lvl-button slot="select-action" label="Move to" tooltip="Move items to another place" icon="file-export"></lvl-button>
					<lvl-button slot="select-action" tooltip="Add to your favorites" icon="bookmark"></lvl-button>
				` : html``}
			</lvl-data-list>
		`
	},
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		labelColumn: {
			control: 'text', description: 'Display an icon as own column at the start of each list item',
			table: {
				type: { summary: 'text' },
			},
		},
		responsive: {
			control: 'boolean',
			description: 'Display more than one records next to each other to save space',
			table: {
				type: { summary: 'boolean' },
			},
		},
		searchBar: {
			control: 'boolean',
			description: 'Display search bar in the form of an alphabet',
			table: {
				type: { summary: 'boolean' },
			},
		},
		groupBy: {
			control: 'text', description: 'Grouping records',
			table: {
				type: { summary: 'text' },
			},
		},
	},
	includeStories: /^[A-Z]/,
	parameters: storyHome.parameters,
}

export default meta

//#region Stories

/**
 * Appearance of a default data list without attributes
 */
export const Default: DataListStory = storyHome.Default

/**
 * Appearance of a data list with fixed width
 */
export const FixedWidth: DataListStory = storyHome.FixedWidth

/**
 * Appearance of a data list with fixed height
 */
export const FixedHeight: DataListStory = storyHome.FixedHeight

/**
 * Appearance of a data list where at least one column has a column with minWidth
 */
export const MinColumn: DataListStory = storyHome.MinColumn

/**
 * Appearance of a data list where at least one column has a column with maxWidth
 */
export const MaxColumn: DataListStory = storyHome.MaxColumn

/**
 * Appearance of a data list with all data types
 */
export const DataTypes: DataListStory = storyHome.DataTypes

/**
 * Appearance of a data list with enough data to use paging and change the limit
 */
export const Paging: DataListStory = storyHome.Paging

/**
 * Appearance of a data list with Data from URL
 */
export const UrlData: DataListStory = storyHome.UrlData

/**
 * Appearance of a data list with Data from URL and failed to store the data when toggling a row
 */
export const UrlDataWithToggleFailure: DataListStory = storyHome.UrlDataWithToggleFailure

/**
 * Appearance of a Data list with Data from URL
 */
export const UrlWithNoData: DataListStory = storyHome.UrlWithNoData

/**
 * Appearance of a data list with data which is loaded via callback from outside
 */
export const DataCallback: DataListStory = storyHome.DataCallback


/**
 * Appearance of a data list with a lot of data for stress testing
 */
export const BigData: DataListStory = storyHome.BigData

/**
 * Appearance of a data list with one row and column
 */
export const LittleData: DataListStory = storyHome.LittleData

/**
 * Appearance of a data list with a click function for each row
 */
export const OnClickRow: DataListStory = storyHome.OnClickRow

/**
 * Appearance of a data list with hidden id column
 */
export const HiddenColumn: DataListStory = storyHome.HiddenColumn

/**
 * Appearance of a data list with selectable checkboxes at first column for multi selection
 */
export const SelectColumn: DataListStory = storyHome.SelectColumn

/**
 * Appearance of a Datatable where all data are shown without paging
 */
export const ShowAll: DataListStory = storyHome.ShowAll

/**
 * Appearance of a data list where infinite scrolling is shown with large data
 */
export const InfiniteScrolling: DataListStory = storyHome.InfiniteScrolling

/**
 * Appearance of a data list with an icon per row at the start
 */
export const LabelColumn: DataListStory = {
	args: {
		columns: [
			{ position: 1, name: 'name' },
			{ position: 2, name: 'type', label: 'Type' },
			{ position: 3, name: 'dataSourceCount', label: 'Data Sources', type: DataType.Integer },
			{ position: 4, name: 'enabledLive', type: DataType.Boolean, liveEditable: true },
			{ position: 5, name: 'enabled', type: DataType.Boolean, maxWidth: 100 },
		],
		rows: [
			{ labelIcon: 'dog', name: 'Dog', type: 'DaguWeb', dataSourceCount: 4, enabled: true, enabledLive: true },
			{ labelIcon: 'cat', name: 'Cat', type: 'DaguWeb', dataSourceCount: 6, enabled: false, enabledLive: false },
			{ labelIcon: 'democrat', name: 'Donkey', type: 'DaguStorage', dataSourceCount: 12, enabled: true, enabledLive: true },
			{ labelIcon: 'hippo', name: 'Happy Hippo', type: 'DaguStorage', dataSourceCount: 12, enabled: true, enabledLive: true },
		],
		labelColumn: 'labelIcon',
	},
}

/**
 * Appearance of a data list with no label in first and fourth column
 */
export const ColumnWithoutLabel: DataListStory = {
	args: {
		columns: [
			{ position: 1, name: 'name' },
			{ position: 2, name: 'type', label: 'Type' },
			{ position: 3, name: 'dataSourceCount', label: 'Data Sources', type: DataType.Integer },
			{ position: 4, name: 'enabledLive', type: DataType.Boolean, liveEditable: true },
			{ position: 5, name: 'enabled', type: DataType.Boolean, maxWidth: 100 },
		],
		rows: [
			{ name: 'Bernburg', type: 'DaguWeb', dataSourceCount: 4, enabled: true, enabledLive: true },
			{ name: 'DaguRemoteAccess', type: 'DaguWeb', dataSourceCount: 6, enabled: false, enabledLive: false },
			{ name: 'DaguStorage_Kunde', type: 'DaguStorage', dataSourceCount: 12, enabled: true, enabledLive: true },
		],
	},
}

/**
 * Appearance of a data list with a responsive multi column design
 */
export const ResponsiveColumns: DataListStory = {
	args: {
		url: '/address-book',
		columns: [
			{ position: 0, name: 'id', label: 'ID' },
			{ position: 1, name: 'name', label: 'NAME', minWidth: 150 },
			{ position: 2, name: 'enabled', label: 'ENABLED', type: DataType.Boolean, liveEditable: true, minWidth: 70 },
		],
		responsive: true,
		height: '600px',
	},
}

/**
 * Appearance of a data list with a responsive multi column design
 */
export const ResponsiveColumnsNoData: DataListStory = {
	args: {
		url: '/no-data',
		columns: [
			{ position: 0, name: 'id', label: 'ID' },
			{ position: 1, name: 'name', label: 'NAME', minWidth: 150 },
			{ position: 2, name: 'enabled', label: 'ENABLED', type: DataType.Boolean, liveEditable: true, minWidth: 70 },
		],
		responsive: true,
		height: '600px',
	},
}

/**
 * Appearance of a data list with a A to Z Searchbar like an address book.
 */
export const SearchColumn: DataListStory = {
	args: {
		url: '/address-book',
		columns: [
			{ position: 0, name: 'id', label: 'ID', minWidth: 50 },
			{ position: 1, name: 'name', label: 'NAME', minWidth: 300 },
			{ position: 2, name: 'enabled', label: 'ENABLED', type: DataType.Boolean, liveEditable: true },
		],
		sortings: [
			{
				orderColumn: 'name',
				direction: SortDirection.Asc,
			},
		],
		searchBar: true,
		groupByField: 'name',
		labelColumn: 'name',
		height: '500px',
	},
}

//#endregion
export const stories: Record<string, LevelStory<DataListStory>> = {
	...getBaseLevelStories(meta),
	labelColumn: new LevelStory<DataListStory>(meta, LabelColumn, 'Icon Label Column'),
	columnWithoutLabel: new LevelStory<DataListStory>(meta, ColumnWithoutLabel, 'No Label in Column'),
	responsive: new LevelStory<DataListStory>(meta, ResponsiveColumns, 'Responsiveness Listing'),
	search: new LevelStory<DataListStory>(meta, SearchColumn, 'A to Z Search'),
}
