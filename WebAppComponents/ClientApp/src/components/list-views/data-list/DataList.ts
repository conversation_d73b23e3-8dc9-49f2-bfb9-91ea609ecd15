import { customElement, property } from 'lit/decorators.js'
import { DataEnumeration, DataEnumerationRow, DataEnumerationType } from '@/components/list-views/DataEnumeration'
import { css, html, PropertyValues } from 'lit'
import * as styles from '@/shared/component-styles'
import enumerationStyles from '@/components/list-views/styles'
import { getTextAlignClass } from '@/enums/align.ts'
import { unsafeHTML } from 'lit/directives/unsafe-html.js'
import { formatData } from '@/enums/data-type.ts'
import { DataEnumerationDataColumn } from '@/components/list-views/DataEnumerationDataColumn.ts'
import { DataEnumerationSpecialColumn } from '@/components/list-views/DataEnumerationColumn.ts'
import { DataEnumerationLabelColumn } from '@/components/list-views/DataEnumerationLabelColumn.ts'
import { classMap } from 'lit/directives/class-map.js'

export type DataListType = DataEnumerationType & {
	labelColumn?: string
	responsive: boolean
	searchBar: boolean
}

const ALPHABET = [ 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z' ] as const
const SMALL_SCREEN_SIZE = 700

/**
 * Web component to display multiple data records in list format.
 * The List has common functionality like paging and sorting and can be transformed to a multi columns list.
 * @deprecated Use 'lvl-list' instead.
 */
@customElement('lvl-data-list')
export class DataList extends DataEnumeration {
	static styles = [
		styles.base,
		styles.color,
		styles.icon,
		styles.scrollbar,
		styles.animation,
		styles.skeleton,
		enumerationStyles,
		// need to use it to prevent rider from destroying the css via auto reformat when using container queries
		css`
		@container(max-width: ${SMALL_SCREEN_SIZE}px) {
			#main {
				--column-count: 13;
			}
		}
		`,
		css`
			:host {
				--background: var(--cp-clr-background-lvl-1);
				--hover-background: var(--cp-clr-background-lvl-2);
				--text-secondary: var(--cp-clr-text-secondary);
				--row-height: 4.8rem;

				container-type: inline-size;
			}

			:host([searchbar]) #main {
				grid-template-rows: auto minmax(0px, 1fr);
			}

			:host([searchbar]) #actions {
				border: none;
				margin-bottom: 0;
			}

			#main {
				display: grid;
				row-gap: var(--size-spacing-m);
				align-content: start;

				& .cell {
					padding: var(--cell-padding);
					min-height: var(--cell-height);
				}

				& .cell--special {
					display: grid;
					align-items: center;
					justify-content: center;
				}

				& .row__label {
					position: relative;
					overflow: hidden;
					background-color: var(--cp-clr-background-lvl-2);
					padding-left: var(--size-spacing-s);
					padding-right: var(--size-spacing-s);
					border-top-left-radius: var(--border-radius);
					border-bottom-left-radius: var(--border-radius);
				}

				& .cell--data {
					position: relative;
					display: flex;
					flex-direction: column;
					align-items: start;
					justify-content: start;
				}

				& .cell__label {
					font-size: 0.7em;
					color: var(--text-secondary);
					white-space: nowrap;
				}

				& .cell__content {
					display: flex;
				}

				& .cell--data.align-right {
					align-items: end;
				}

				& .cell--data.align-center {
					align-items: center;
				}

				& .cell--data:not(:has(> .cell__label)) {
					display: grid;
					align-items: center;
				}

				& .cell--data.align-right:not(:has(> .cell__label)) {
					justify-content: end;
				}

				& .cell--data.align-center:not(:has(> .cell__label)) {
					justify-content: center;
				}

				& .cell--data:last-child {
					border-right: none;
				}

				& .row {
					display: grid;
					grid-column: span var(--columns);
					grid-template-columns: subgrid;
					background-color: var(--background);
					border-radius: var(--border-radius);
					cursor: pointer;
					padding: 0 var(--size-spacing-m);
					min-height: var(--row-height);
				}

				& .row.selected {
					background-color: var(--background-selected);
				}

				& .row:hover {
					background-color: var(--hover-background);
				}
			}

			:host([label-column]) #main .row {
				padding-left: 0;
			}

			#body {
				--grid-behaviour: auto-fit;

				position: relative;
				overflow: auto;
				padding: 0 0 var(--size-spacing-m) 0;
			}

			#body.auto-fill {
				--grid-behaviour: auto-fill;
			}

			#body .row-group {
				display: grid;
				grid-template-columns: var(--grid-template-columns);
				align-content: start;
				border-bottom-left-radius: var(--border-radius);
				border-bottom-right-radius: var(--border-radius);
				row-gap: var(--size-spacing-m);
			}

			:host([responsive]) #root:not(.minimal-columns) #body .row-group {
				grid-template-columns: repeat(var(--grid-behaviour), var(--grid-template-columns));
				column-gap: var(--size-spacing-m);
			}
			
			:host([skeleton]) #root > * {
				opacity: 0;
			}

			#body:has(.no-data) {
				grid-template-rows: 1fr;
				align-items: center;
				justify-items: center;
			}

			#searchbar {
				border-bottom: 1px solid var(--border-color);
			}

			.search-button__group {
				--_column-count: var(--column-count, 26);

				user-select: none;
				position: relative;
				display: grid;
				grid-template-columns: repeat(var(--_column-count), minmax(max-content, 1fr));
				gap: var(--size-spacing-xxs);
				align-items: center;
				justify-content: center;

				border: none;
				border-radius: var(--size-radius-m);
				overflow: hidden;

				margin: var(--size-spacing-m) 0;
				padding: 0;

				& .search-button {
					background-color: var(--cp-clr-background-lvl-2);
					border: none;
					padding: 0;
				}

				& .search-button:hover {
					background-color: var(--cp-clr-background-lvl-3);
				}

				& .search-button.disabled {
					background-color: var(--cp-clr-background-lvl-1);
				}

				& .search-button__label {
					display: block;
					height: 2em;
					line-height: 2em;
					text-align: center;
					cursor: pointer;
				}
			}

			.group__title {
				position: sticky;
				left: 0;
				
				font-size: var(--font-size);
				color: var(--text-secondary);
				padding: var(--size-spacing-m) 0;
				margin-bottom: var(--size-spacing-m);
				border-bottom: 1px solid var(--border-color);
				font-weight: 400;
				white-space: nowrap;

				& span {
					display: inline-block;
					width: 2em;
					text-align: center;
				}
			}
		`,
	]

	//#region attributes

	@property({ attribute: 'label-column' })
	labelColumn?: string

	// Responsive behaviour of the data rows (instead of a list -> multiple item columns)
	@property({ type: Boolean })
	responsive: boolean = false

	@property({ type: Boolean, attribute: 'searchbar' })
	searchBar: boolean = false

	//#endregion

	//#region private properties

	private initialColumnResizing: boolean = false

	//#endregion

	render() {
		let currentRowNumber = 0
		return this.renderHtml(html`
			${this.renderSearchBar()}
			<div id="body" class="scrollbar-styled ${this.groupByField && Object.keys(this.groupedRows).length > 1 ? 'auto-fill' : ''}">
				${this.visibleRows.length > 0 ? this.groupByField ? Object.keys(this.groupedRows).map(group => {
					return html`
						<h6 id="group-${group.toLowerCase()}" class="group__title"><span>${group}</span></h6>
						<ul class="row-group">
							${this.groupedRows[group].map(row => {
								currentRowNumber++
								return this.renderSingleRow(row, currentRowNumber - 1)
							})}
						</ul>
					`
				}) : html`
					<ul class="row-group">
						${this.visibleRows.map(this.renderSingleRow)}
					</ul>
				` : (this.isLoading ? '' : this.renderNoDataFallback())}
			</div>
		`)
	}

	private renderSingleRow = (row: DataEnumerationRow, index: number) => {
		const classes = {
			row: true,
			selected: row.selected,
			skeleton: row.skeleton,
			'row--clicked': row.clicked,
		}
		return html`
			<li class="${classMap(classes)}" data-position="${this.offset + index + 1}" @click="${(event: MouseEvent) => this.handleRowClick(event, row, index)}">
				${this.visibleColumnsSorted.map((column) => {
					if (column instanceof DataEnumerationDataColumn) {
						return html`
							<div class="cell cell--data ${getTextAlignClass(column.textAlign)}" data-name="${column.name}"
									 @click="${(event: MouseEvent) => this.handleCellClick(event, row, column)}">
								${row.skeleton ? '' : this.renderDataContent(row, column)}
							</div>
						`
					} else if (column.isSpecial) {
						return column.renderInRow(row)
					}
				})}
			</li>
		` 
	}

	private renderDataContent = (row: DataEnumerationRow, column: DataEnumerationDataColumn) => {
		const content = row.data[column.name]
		const formattedData = unsafeHTML(formatData(content, column.type, column))
		if (!column.label)
			return formattedData

		return html`
			<span class="cell__label">
					${column.hideLabel ? '' : column.label}
			</span>
			<div class="cell__content">
				${formattedData}
			</div>
		`
	}

	private renderSearchBar = () => {
		if (!this.searchBar)
			return ''

		return html`
			<section id="searchbar">
				<div class="search-button__group">
					${ALPHABET.map(letter => {
						const enabled = Object.keys(this.groupedRows).includes(letter)
						return html`
							<button class="search-button ${enabled ? '' : 'disabled'}" tabindex="${enabled ? 0 : -1}" data-link="group-${letter.toLowerCase()}"
									 @click="${(event: MouseEvent) => this.handleSearchButtonClick(event)}">
								<label for="alpha-${letter.toLowerCase()}" class="search-button__label">${letter}</label>
							</button>
						`
					})}
				</div>
			</section>
		`
	}

	//#region lifecycle callbacks

	// set column width for the first time
	protected firstUpdated(changedProperties: PropertyValues<this>) {
		super.firstUpdated(changedProperties)
	}

	// Resize columns if columns property was changed
	protected updated(changedProperties: PropertyValues) {
		super.updated(changedProperties)
		if (changedProperties.get('_dataColumns') || changedProperties.get('_specialColumns') || (!this.initialColumnResizing && this.visibleRows.length > 0)) {
			this.initialColumnResizing = true
			if (this.responsive)
				this.resizeColumns()
			else
				this._htmlRoot.style.setProperty('--grid-template-columns', this.getGridTemplateColumns())
		}
	}

	// if data changes then columns have to be controlled for minimal column width
	protected async willUpdate(changedProperties: PropertyValues<DataEnumerationType>) {
		await super.willUpdate(changedProperties)
		const selectOptionWasChanged = changedProperties.has('rows') || changedProperties.has('sortings') || changedProperties.has('filters') || changedProperties.has('limit') || changedProperties.has('offset')
		if (selectOptionWasChanged && this.responsive) {
			this.resizeColumns()
		}
	}

	//#endregion

	//#region protected functions

	protected getSpecialColumns(): DataEnumerationSpecialColumn[] {
		let specialColumns = super.getSpecialColumns()
		if (this.labelColumn)
			specialColumns = [ new DataEnumerationLabelColumn(this), ...specialColumns ]

		return specialColumns
	}

	//#endregion

	//#region private functions

	// jump to the correct row group if searchbar buttons are used 
	private handleSearchButtonClick(event: MouseEvent) {
		const searchButton = event.currentTarget as HTMLElement
		const linkId = searchButton.dataset['link']
		const groupElement = (this._htmlBody!.querySelector('#' + linkId) as HTMLElement)
		this._htmlBody!.scrollTo({ top: groupElement.offsetTop, behavior: 'smooth' })
	}

	//#endregion
}

declare global {
	interface HTMLElementTagNameMap {
		'lvl-data-list': DataList
	}
}