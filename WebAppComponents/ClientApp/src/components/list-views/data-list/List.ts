import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, queryAll, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { ListDataColumn, ListDataColumnInterface, ListDataColumnProperties } from '@/components/list-views/data-list/ListDataColumn.ts'
import { LineSize, ListLineType } from '@/components/list-views/data-list/ListLine.ts'
import { Toggle } from '@/components/inputs/toggle/Toggle.ts'
import { ifDefined } from 'lit/directives/if-defined.js'
import { DisplayType } from '@/components/list-views/data-list/ListLineItem.ts'
import { ColumnType, ListColumn } from '@/components/list-views/data-list/ListColumn.ts'
import { unsafeHTML } from 'lit/directives/unsafe-html.js'
import { repeat } from 'lit/directives/repeat.js'
import { query } from 'lit/decorators/query.js'
import { Button } from '@/components/atomics/button/Button.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { EnumerationContentInterface } from '@/components/list-views/Enumeration.ts'
import { CheckboxState } from '@/enums/checkbox-state.ts'
import { offsetToPage } from '@/components/list-views/calculations.ts'
import { ItemType, PageType, SimpleContentData } from '@/components/list-views/types.ts'
import { DataColumnType, formatData, parseData, toggleColumnIcon } from '@/enums/data-column-type.ts'
import { DataSorting } from '@/shared/types.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { ListBaseColumn } from '@/components/list-views/data-list/ListBaseColumn.ts'

export type ListType = {
	identityColumn?: string
	lineSize?: LineSize
	rows?: SimpleContentData[]
	onRowClick?: (record: ItemType, index: number, newTab: boolean) => Promise<void>
	responsive?: boolean
	activeColumn?: string
}

type ListInterface = ListType & EnumerationContentInterface

export type ItemChangeEventType = {
	index: number
	slug: string
	data: Record<string, any>
	row: Record<string, any>
	columnName: string
	errorCallback: () => void
}

const SMALL_SCREEN_SIZE = 700

/**
 * Example web component using LIT (https://lit.dev)
 */
@customElement('lvl-list')
export class List extends LitElement implements ListInterface {

	// enable animations for vanishing scrollbar
	static {
		const documentStyles = css`
			@property --scrollbar-color {
				syntax: "<color>";
				inherits: true;
				initial-value: transparent;
			}
    `;
		document.adoptedStyleSheets.push(documentStyles.styleSheet!);
	}
	
	static styles = [
		styles.base,
		styles.color,
		styles.icon,
		styles.scrollbar,
		styles.skeleton,
		styles.vanishingScrollbar,
		styles.animation,
		fontAwesome,
		css`
		@container(max-width: ${SMALL_SCREEN_SIZE}px) {
			#main {
				--column-count: 13;
			}
		}
		`,
		css`
			:host {
				--grid-behaviour: auto-fit;
				
				display: grid;
				grid-template-rows: 1fr max-content;
				justify-items: start;
				container-type: inline-size;
			}

			:host([searchbar]) #main {
				grid-template-rows: auto minmax(0px, 1fr);
			}

			:host([searchbar]) #actions {
				border: none;
				margin-bottom: 0;
			}

			:host([responsive]) .list {
				grid-template-columns: repeat(var(--grid-behaviour), var(--grid-template-columns));
				column-gap: var(--size-spacing-m);
			}

			.list.auto-fill {
				--grid-behaviour: auto-fill;
			}

			.list {
				display: grid;
				grid-template-columns: var(--grid-template-columns);
				align-content: start;
				border-bottom-left-radius: var(--size-radius-m);
				border-bottom-right-radius: var(--size-radius-m);
				row-gap: var(--size-spacing-m);
				height: 100%;
				width: 100%;
				overflow-y: auto;
				padding: var(--size-spacing-m);
			}

			.data-icon {
				--icon-color: var(--cp-clr-state-inactive);
				
				position: relative;
				color: var(--icon-color);
				user-select: none;
				cursor: default;
			}
			
			.data-icon::after {
				content: '';
				position: absolute;
				height: 3.2rem;
				aspect-ratio: 1 / 1;
				left: 50%;
				top: 50%;
				translate: -50% -50%;
			}

			.data-icon.icon--toggleable {
				--icon-color: var(--cp-clr-text-secondary);
				cursor: pointer;
			}
			
			.data-icon:is([data-value='true']) {
				--icon-color: var(--cp-clr-state-active);
			}

			.icon--toggleable:hover{
				--icon-color: var(--cp-clr-state-active-hover);
			}
			
			.data-icon.icon--toggleable[data-value='true']:hover {
				--icon-color: var(--cp-clr-state-active-hover);
			}

			/* to top button */
			[data-action="to-top"] {
				width: max-content;
				padding: var(--size-spacing-s) var(--size-spacing-m);
			}
		`,
	]

	//#region attributes

	private _rows?: SimpleContentData[]

	@property({ attribute: false })
	set rows(data: SimpleContentData[]) {
		this._rows = data

		// are there any columns which use custom converters but don't have one -> stop rendering rows 
		if (this._htmlColumns?.some(column => column.withConverter && !column.converter))
			return
		this.items = data
	}

	get rows() {
		return this._rows ?? []
	}

	@property({ attribute: 'identity-column' })
	identityColumn: string = 'id'
	
	@property({ attribute: 'active-column' })
	activeColumn?: string

	@property({ type: Function })
	onRowClick?: (record: ListLineType, index: number, newTab: boolean) => Promise<void>

	@property({ attribute: 'size' })
	lineSize: LineSize = LineSize.Large

	@property({ type: Boolean })
	responsive: boolean = false

	@property({ type: Boolean, reflect: true, attribute: 'with-external-navigation' })
	withExternalNavigation: boolean = false

	//#endregion

	//#region states

	@state()
	private _items: ItemType[] = []

	set items(data: SimpleContentData[]) {
		this._linesSorted = undefined
		this._items = data.map((dataItem: SimpleContentData, index: number): ItemType => {
			if (!dataItem)
				return { position: index, skeleton: true }
			const identifier = dataItem[this.identityColumn]
			return { position: index, id: identifier, data: dataItem, pinned: this.pinnedLineIds.includes(identifier), selected: false }
		})
	}

	get items(): ItemType[] {
		return this._items
	}

	get selectedItems(): ItemType[] {
		return this.items.filter(line => line.selected)
	}

	managed: boolean = false
	
	//#endregion states

	//#region private properties

	@query('[data-action=to-top]')
	private _htmlToTopButton?: Button

	@query('.list')
	private _htmlList!: HTMLElement

	@queryAll('lvl-list-line')
	private _htmlLines?: ListLineType[]

	private _htmlColumns?: ListBaseColumn[]

	private _htmlDataColumns?: ListDataColumnInterface[]

	get dataColumns(): ListDataColumnInterface[] {
		return this._htmlDataColumns ?? Object.values(this.querySelectorAll('lvl-list-data-column')) as ListDataColumnInterface[] ?? []
	}

	private pinnedLineIds: string[] = []

	private _visibleColumnsSorted?: ListBaseColumn[]

	private get visibleColumnsSorted(): ListBaseColumn[] {
		if (this._visibleColumnsSorted == null)
			this._visibleColumnsSorted = this._htmlColumns?.filter(column => !(column instanceof ListDataColumn) || !column.hidden) ?? []
		return this._visibleColumnsSorted
	}

	private _linesSorted?: ItemType[]

	private get lineSorted(): ItemType[] {
		if (this._linesSorted == null)
			this._linesSorted = this.items.sort((lineA, lineB) => {
				if (lineA.pinned && lineB.pinned || !lineA.pinned && !lineB.pinned)
					return lineA.position - lineB.position
				return lineA.pinned ? -1 : 1
			}) ?? []

		return this._linesSorted
	}
	
	private alreadyClicked: boolean = false

	private static readonly _baseLocalizer: StringLocalizer = new StringLocalizer('DataEnumeration')

	//#endregion

	//#region lifecycle callbacks

	// adds all needed event listener to react on child components 
	connectedCallback() {
		super.connectedCallback()
		this._htmlColumns = Object.values(this.querySelectorAll('lvl-list-column, lvl-list-data-column')) as unknown as ListBaseColumn[]
		this._htmlDataColumns = Object.values(this.querySelectorAll('lvl-list-data-column')) as ListDataColumnInterface[]

		// get line pinned by user  
		const storagePinKey = `lvl:component:list:${this.id}:pinned-ids`
		const storagePinString = localStorage.getItem(storagePinKey)
		this.pinnedLineIds = storagePinString != null ? JSON.parse(storagePinString) : []

		// line was pinned -> save to user storage and rerender by setting the items
		this.addEventListener('column:pinned', (event: Event) => {
			this._linesSorted = undefined
			this._items = [ ...this.items ]
			this.pinnedLineIds = this.items.filter(line => line.pinned && line.id).map(line => line.id!)
			localStorage.setItem(storagePinKey, JSON.stringify(this.pinnedLineIds))
			event.stopPropagation()
		})

		// line was selected -> rerender by setting the items
		this.addEventListener('column:selected', (event: Event) => {
			this._linesSorted = undefined
			this._items = [ ...this.items ]
			event.stopPropagation()
			this.dispatchEvent(new CustomEvent<CheckboxState>('query-view:select', { detail: this.getGlobalSelectState(), bubbles: true }))
		})

		// new converter in a column was registered
		this.addEventListener('converter:registered', (event: Event) => {
			if (this._htmlColumns?.every(column => !column.withConverter || column.converter))
				this.items = this.rows
			event.stopPropagation()
		})

		// column was changed or registered -> rerender!
		this.addEventListener('column:changed', (event: Event) => {
			this._visibleColumnsSorted = undefined
			this.requestUpdate('columns', [])
			event.stopPropagation()
		})
		
		this.initResizeObserver()

		// do we have an attribute rows? parse data and set it
		if (this.hasAttribute('rows')) {
			try {
				this.rows = this.cleanupRowData(JSON.parse(this.getAttribute('rows')!))
			} catch (error) {
				console.warn('lines couldn\'t set correctly\n', error)
			}
		}
		
		// notify parent that the component is ready
		this.setAttribute('initDone', '')
	}

	protected shouldUpdate(_changedProperties: PropertyValues): boolean {
		// are all columns registered? do not render component, if any
		return !(this._htmlColumns != null && this._htmlColumns.some(column => !column.isReady))
	}

	protected async firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)
		this.addScrollBodyEventListener()

		// wait for the multi view to be initialized
		const componentHolder = 'lvl-enumeration'
		if (componentHolder)
			await customElements.whenDefined(componentHolder)

		// ... and then register yourself
		this.dispatchEvent(new CustomEvent('query-view:registered', { bubbles: true }))
	}

	protected updated(_changedProperties: PropertyValues) {
		super.updated(_changedProperties)
		if (_changedProperties.get('_items') || _changedProperties.get('withExternalNavigation') != null)
			this.checkToTopButtonUsage(this.offsetHeight)
	}

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		// self configured list?
		if (this.visibleColumnsSorted.length === 0 && this.rows.length === 0) {
			const items = Object.values(this.querySelectorAll('lvl-list-line:first-child > lvl-list-line-item'))
			const columnDimensions = Array.from({ length: items.length }, (_, index) => (
				items[index].getAttribute('type') === DisplayType.Primary ? 'auto' : 'max-content'),
			).join(' ')
			return html`
				<div class="list" style="--columns: ${items.length}; --grid-template-columns: ${columnDimensions};">
					<slot></slot>
				</div>
			`
		}

		const primaryDataColumnIndex = this._htmlColumns?.findIndex(column => column.isData)
		return html`
			<div class="list vanishing-scrollbar" style="--columns: ${this.getColumnCount()}; --grid-template-columns: ${this.getGridTemplateColumns()};">
				${repeat(this.lineSorted, line => line.position, (line, lineIndex) => html`
					<lvl-list-line size="${ifDefined(this.lineSize)}" ?skeleton=${line.data == null} data-position="${lineIndex}" ?selected="${line.selected}"
												 ?active="${this.activeColumn && line.data && line.data[this.activeColumn]}"
												 @click="${(event: MouseEvent) => this.handleRowClick(event, line, lineIndex)}"
												 @auxclick="${(event: MouseEvent) => this.handleRowClick(event, line, lineIndex)}" @mousedown="${this.handleRowMousedown}">
						${this.visibleColumnsSorted.map((column, index) => {
							// render skeletons
							if (line.skeleton || !line.data) {
								return html`
									<lvl-list-line-item skeleton data-name="${column.name}"></lvl-list-line-item>`
							}

							// render real data
							if (column instanceof ListDataColumn) {
								const formattedData = this.getLineItemContent(line, column)
								return html`
									<lvl-list-line-item type="${index == primaryDataColumnIndex ? DisplayType.Primary : DisplayType.Secondary}" label="${ifDefined(column.hideLabel || this.lineSize == LineSize.Small ? undefined : column.label)}"
																			align="${column.textAlign}" ?skeleton=${line.data == null} data-name="${column.name}"
																			@click="${(event: MouseEvent) => this.handleCellClick(event, line.data, column, lineIndex)}">
										${formattedData}
									</lvl-list-line-item>
								`
							}

							// render static columns
							const defaultColumn = column as ListColumn
							return defaultColumn.getContent(line) 
						})}
					</lvl-list-line>
				`)}
			</div>
			${this.withExternalNavigation ? '' : html`
				<lvl-button data-action="to-top" label="${List._baseLocalizer.localize('ButtonTop')}"
										class="disabled hide"
										icon="arrow-up-to-line"
										@click="${() => this._htmlList?.scrollTo({ top: 0, behavior: 'smooth' })}"></lvl-button>
			`}
		`
	}

	//#region public methods

	/**
	 * Click on the next line
	 */
	public next() {
		if(!this._htmlLines)
			return
		
		const currentClickedRowIndex = Array.from(this._htmlLines).findIndex(row => row.clicked) ?? 0
		const nextIndex = Math.min(currentClickedRowIndex + 1, (this._htmlLines?.length ?? 0) - 1)
		this.clickIndex(currentClickedRowIndex, nextIndex)
	}

	/**
	 * Click on the previous line
	 */
	public previous() {
		if(!this._htmlLines)
			return
		
		const currentClickedRowIndex = Array.from(this._htmlLines).findIndex(row => row.clicked) ?? 0
		const previousIndex = Math.max(0, currentClickedRowIndex - 1)
		this.clickIndex(currentClickedRowIndex, previousIndex)
	}

	/**
	 * trigger reload on surrounding enumeration (if there is any)
	 */
	public reload() {
		const enumeration = this.closest('lvl-enumeration')
		if (enumeration != null)
			enumeration.reload()
	}

	/**
	 * Scrolls to the first line
	 */
	public get scrollableElement() {
		return this._htmlList
	}

	public findVisiblePages(offset: number, limit: number): PageType[] {
		const listRect = this._htmlList!.getBoundingClientRect()
		if (!this._htmlLines)
			return []

		let pages: PageType[] = []
		for (const line of (Array.from(this._htmlLines))) {
			const lineRect = (line as HTMLElement).getBoundingClientRect()
			if (lineRect.top > listRect.bottom)
				break

			if (lineRect.bottom >= listRect.top) {
				const position = Number((line as HTMLElement).dataset['position'])
				const realPosition = position + offset
				const currentPage = offsetToPage(realPosition, limit)

				if (!pages.some(page => page.pageNumber === currentPage))
					pages.push({ pageNumber: currentPage, isSkeleton: line.skeleton === true })
			}
		}
		return pages
	}

	/**
	 * Check that the visible area of this list shows the end of lines
	 */
	public isLastLineVisible(): boolean {
		return this._htmlList.scrollHeight <= Math.ceil(this._htmlList.scrollTop + this._htmlList.offsetHeight)
	}

	/**
	 * Enables/Disables the Top button
	 * @param enable
	 */
	public toggleTopButtonActive(enable: boolean) {
		if (!this._htmlToTopButton) {
			this.dispatchEvent(new CustomEvent<boolean>('top-button:toggle', { bubbles: true, detail: enable }))
			return
		}
		
		if (enable)
			this._htmlToTopButton.classList.remove('disabled')
		else
			this._htmlToTopButton.classList.add('disabled')
	}

	public countSelectedItems(): number {
		return this.selectedItems.length
	}

	public hasSelectColumn(): boolean {
		if (this.visibleColumnsSorted.length === 0)
			return false
		return this.visibleColumnsSorted.some(column => (column instanceof ListColumn && column.type === ColumnType.Select))
	}

	public selectAll(): void {
		this.items.forEach(line => line.selected = true)
		this._items = [ ...this._items ]
	}

	public unselectAll(): void {
		this.items.forEach(line => line.selected = false)
		this._items = [ ...this._items ]
	}

	public get scrollTop() {
		return this._htmlList.scrollTop
	}

	public set scrollTop(position: number){
		this._htmlList.scrollTo({ top: position, behavior: 'instant' })
	}

	public isFileInfoRequired(): boolean {
		return false
	}

	public get queryFields(): string[] {
		const fields = this._htmlDataColumns?.map(column => column.name) ?? []
		if (!fields.includes(this.identityColumn))
			fields.push(this.identityColumn)
		return fields
	}

	/**
	 * Adapter method to use it in queryViews
	 * @param _
	 */
	public displaySorting(_: DataSorting[]) {}
	
	//#endregion

	//#region private methods
	
	private getLineItemContent(line: ItemType, dataColumn: ListDataColumnInterface) {
		if(!line.data)
			return ''
		
		let content = line.data[dataColumn.name]
		
		// has the data column a converter? call it and try to return it as real html
		if(dataColumn.withConverter){
			if(dataColumn.converter == null)
				return ''
			
			const innerHtml = dataColumn.converter(line.data, line.position)
			return typeof innerHtml === 'string' ? unsafeHTML(innerHtml) : innerHtml
		}
		
		content = formatData(content, dataColumn)
		return typeof content === 'string' ? unsafeHTML(content) : content
	}
	
	// determine the checkbox state for all lines in the list (all lines selected, none, part of)
	private getGlobalSelectState(): CheckboxState {
		const selectedLineCount = this.items.filter(line => line.selected).length
		if(selectedLineCount === 0)
			return CheckboxState.Off
		
		if(selectedLineCount === this.items.length)
			return CheckboxState.On
		
		return CheckboxState.Inderterminate
	}

	/* 
	* Adds an event listener, which is called when the body is scrolled
	* Used to disable and enable the To Top Button and infinite scrolling
	*/
	private addScrollBodyEventListener() {
		let timeout: NodeJS.Timeout
		this._htmlList?.addEventListener('scroll', () => {
			clearTimeout(timeout)
			timeout = setTimeout(() => {
				// dis/enable top button
				this.toggleTopButtonActive(this._htmlList.scrollTop != 0)
				this.dispatchEvent(new CustomEvent('query-view:scroll', { bubbles: true }))
			}, 100)
		})
	}
	
	// create an observer which listen on changes of the component height 
	private initResizeObserver() {
		let componentHeight = 0
		const resizeObserver = new ResizeObserver((entries) => {
			for (const entry of entries) {
				if(entry.contentRect.height != componentHeight && this._htmlList != null){
					componentHeight = entry.contentRect.height
					this.checkToTopButtonUsage(entry.contentRect.height)
				}
			}
		});

		resizeObserver.observe(this);
	}
	
	// display/hide the toTopButton considering the scrollbar, if the height of the component was changed
	private resizeTimeout?: NodeJS.Timeout
	private checkToTopButtonUsage(componentHeight: number) {
		// List within a lvl-enumeration has a foreign button
		if (this.withExternalNavigation)
			return

		clearTimeout(this.resizeTimeout)
		this.resizeTimeout = setTimeout(() => {
			const hasScrollbar = this._htmlList.scrollHeight > Math.ceil(componentHeight) // height might be a double value while scrollHeight seems to always be an int
			if (hasScrollbar)
				this._htmlToTopButton?.classList?.remove('hide')
			else
				this._htmlToTopButton?.classList?.add('hide')
		}, 100)
	}
	
	private handleRowMousedown = (event: MouseEvent) => {
		if (event.button === 1)
			event.preventDefault()
	}

	// executes the passed onRowClick function 
	private handleRowClick = async (event: MouseEvent, row: ItemType, index: number) => {
		if (!row || event.button === 2)
			return

		this._htmlLines?.forEach(currentRow => currentRow.clicked = false)
		const currentRow = (event.currentTarget as ListLineType)
		currentRow.clicked = true
		if (!this.onRowClick)
			return

		// when an element is clicked inside row which is live editable, then we ignore the row click
		const targetElement = event.target as HTMLElement
		if(targetElement && targetElement.tagName === "LVL-MENU-ITEM") {
			// @PWO: Handle menu Item Click here?
			return
		}
		
		if (targetElement && targetElement.hasAttribute('data-live-editable'))
			return

		if (!this.alreadyClicked) {
			const newTab = event.ctrlKey || event.button === 1
			this.alreadyClicked = true
			await this.onRowClick(row, index, newTab)
			this.alreadyClicked = false
		}
	}

	// handles a click on a toggle button within a row column
	private handleCellClick(event: MouseEvent, row: SimpleContentData, column: ListDataColumnInterface, lineIndex: number) {
		const targetElement = event.target as HTMLElement

		// only if a live editable was clicked, then we can update the row data 
		if (!targetElement || !targetElement.hasAttribute('data-live-editable'))
			return

		// we support currently only boolean and icon toggling
		if (column.type == DataColumnType.Boolean) {
			const toggleButton = targetElement as Toggle
			this.toggleCellItem(lineIndex, row, column, () => toggleButton.value = !toggleButton.value)
			return
		}
		
		if (column.type == DataColumnType.Icon) {
			this.toggleCellItem(lineIndex, row, column, () => toggleColumnIcon(targetElement))
			return
		}
	}

	private async toggleCellItem(lineIndex: number, row: SimpleContentData, column: ListDataColumnProperties, revertItemCallback: () => void): Promise<boolean> {
		if (!row)
			throw new Error('live editable function is not permitted in skeletons')

		row[column.name] = !row[column.name]

		const slug = row[this.identityColumn]
		if (!slug)
			return true

		const data: Record<string, any> = {}
		data[column.name] = row[column.name]

		// no url -> bubble the event
		let customEvent = new CustomEvent<ItemChangeEventType>('line:toggle:change', {
			detail: {
				index: lineIndex,
				columnName: column.name,
				slug: slug,
				errorCallback: () => {
					row[column.name] = !row[column.name]
					this._linesSorted = undefined
					revertItemCallback()
				},
				data: data,
				row: row
			},
			bubbles: true,
		})
		this.dispatchEvent(customEvent)
		return true
	}

	// parse data from string
	private cleanupRowData(rows: SimpleContentData[]): SimpleContentData[] {
		// without columns and their configuration we cannot parse data
		if (!this._htmlColumns || this._htmlColumns.length === 0)
			return rows

		return rows.map((row: SimpleContentData) => {
			if (row != null)
				this._htmlDataColumns?.forEach(column => row[column.name] = parseData(row[column.name], column.type ?? DataColumnType.String))
			return row
		})
	}

	// delivers the count of all columns (special + data)
	private getColumnCount() {
		return this.visibleColumnsSorted.length
	}

	// Create the custom value with column config for css attribute: grid-template-columns
	private getGridTemplateColumns() {
		const firstDataColumnIndex = this.visibleColumnsSorted.findIndex(column => column.isData)
		let gridTemplateColumns: string[] = this.visibleColumnsSorted.map((column, index) => column.getCssDimension(index == firstDataColumnIndex ? 'auto' : 'max-content'))
		return gridTemplateColumns.join(' ')
	}

	private clickIndex(oldIndex: number, newIndex: number) {
		if (oldIndex === newIndex || !this._htmlLines)
			return

		// remove click from old row
		const oldRow = Array.from(this._htmlLines).at(oldIndex)
		if (oldRow)
			oldRow.clicked = false

		// set click on new row
		const row = Array.from(this._htmlLines).at(newIndex)
		if (!row)
			return

		row.clicked = true

		// execute passed row click callback
		if (this.onRowClick != null)
			this.onRowClick(this.lineSorted[newIndex], newIndex, false)
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-list': List
	}
}