import { css, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { DataType } from '@/enums'
import { DataTypeOptions } from '@/shared/types.ts'
import { BooleanFormat } from '@/enums/boolean-format.ts'

export type CardInfoType = {
	name?: string
	type?: DataType
} & DataTypeOptions

/**
 * CardInfo web component using LIT (https://lit.dev)
 */
@customElement('lvl-card-info')
export class CardInfo extends LitElement implements CardInfoType {

	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		css`
			:host {
		
			}
		`,
	]

	//#region attributes
	
	@property()
	name: string = ''

	@property()
	type: DataType = DataType.String

	/* Type: Number options */

	@property({ type: Number, attribute: 'decimal-places' })
	decimalPlaces: number = 2

	@property()
	sign: string = ''

	@property({ type: Boolean, attribute: 'with-thousand-separators' })
	withThousandSeparators: boolean = false

	@property({ type: Boolean, attribute: 'live-editable' })
	liveEditable: boolean = false

	/* Type: Text options */
	@property({ type: Boolean, attribute: 'rich-text' })
	richText: boolean = false

	/* Type: Boolean options */
	@property({ attribute: 'display-format' })
	displayFormat: BooleanFormat = BooleanFormat.Check

	//#endregion

	//#region states
	//#endregion states

	//#region private properties
	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.

	//#region lifecycle callbacks

	protected createRenderRoot() {
		return this
	}
	
	//#endregion

	//#region public methods
	//#endregion

	//#region private methods
	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-card-info': CardInfo
	}
}