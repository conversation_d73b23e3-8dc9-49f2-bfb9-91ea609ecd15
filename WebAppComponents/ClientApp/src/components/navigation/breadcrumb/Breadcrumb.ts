import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { repeat } from 'lit/directives/repeat.js'
import { renderIcon } from '@/shared/component-html.ts'
import { query } from 'lit/decorators/query.js'
import { PopupPlacement } from '@/components/popup/Popup.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { IconStyle } from '@/enums/icon-style.ts'
import { Size } from '@/enums/size.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { ColorState } from '@/enums/color-state.ts'

export type BreadcrumbItem<TOptions = any> = {
	label: string
	labelWithPlaceholders?: string
	url?: string
	data?: Record<string, any>
	hidden?: boolean
	width: number
	options?: TOptions
}

export type BreadcrumbCreateItem = Omit<BreadcrumbItem, 'width'>

export type BreadcrumbType = {
	items?: BreadcrumbCreateItem[],
	static: boolean,
	addBreadcrumb: (item: BreadcrumbCreateItem) => void,
	pop: () => (BreadcrumbItem | undefined)
}

export type BreadcrumbEventDetail = {
	item: BreadcrumbItem,
	index: number,
	newTab: boolean
}

enum ResizeDirection {
	Smaller, Bigger
}

const ITEM_GAP = 8 as const

/**
 * Example web component using LIT (https://lit.dev)
 */
@customElement('lvl-breadcrumb')
export class Breadcrumb extends LitElement implements BreadcrumbType {

	static styles = [
		styles.base,
		styles.color,
		styles.icon,
		styles.skeleton,
		styles.animation,
		fontAwesome,
		css`
			:host {
				display: flex;
				width: 100%;
				font-size: var(--size-text-l);
				color: var(--cp-clr-text-secondary);
				overflow: hidden;
			}

			#breadcrumbs {
				display: inline-flex;
				align-items: center;
				column-gap: var(--size-spacing-m);
			}

			.breadcrumb__item, #home {
				display: flex;
				align-items: center;
			}

			.breadcrumbs--compressed:not(.calculate-mode) {
				overflow: hidden;

				& .breadcrumb__item:not(.breadcrumb__dropdown) {
					overflow: hidden;
				}

				& .breadcrumb__label {
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}

			.breadcrumb__item--hidden {
				display: none;
			}

			.calculate-mode .breadcrumb__item--hidden {
				display: flex;
			}

			.breadcrumb__label {
				line-height: 2.4rem;
				height: 2.4rem;
				margin-left: var(--size-spacing-m);
				cursor: pointer;
				user-select: none;
				transition: font-size var(--animation-time-medium) ease-in-out;
			}

			.breadcrumb__label:hover, #home:hover {
				color: var(--cp-clr-text-primary-positiv);
			}

			.breadcrumb__label:hover {
				text-decoration: underline;
				text-underline-offset: 0.5rem;
				text-decoration-thickness: 1px;
				text-underline-color: var(--cp-clr-text-primary-positiv);
			}

			.breadcrumb__label[data-popup-open] ~ lvl-tooltip {
				display: none;
			}

			#home {
				cursor: pointer;
				color: var(--cp-clr-text-tertiary);
			}

			#breadcrumb__three-dots {
				min-width: 0;
			}

			.breadcrumb__item:last-child .breadcrumb__label {
				font-size: 2rem;
				color: var(--cp-clr-text-primary-positiv);
			}

			.breadcrumb__item {
				white-space: nowrap;
				font-size: var(--size-text-l);
			}

			.breadcrumb__separator {
				width: 1em;
				text-align: center;
			}

			.icon {
				font-size: var(--size-text-l);
				flex-shrink: 0;
			}

			.skeleton__text {
				width: 7rem;
				height: 2.4rem;
			}
		`,
	]

	//#region attributes

	@property({ type: Array<BreadcrumbItem> })
	items: BreadcrumbItem[] = []

	@property({ type: Boolean })
	static: boolean = false

	//#endregion

	//#region states
	//#endregion states

	//#region private properties

	@query('#breadcrumbs')
	private _htmlBreadcrumb!: HTMLUListElement

	private static readonly _localizer: StringLocalizer = new StringLocalizer('Breadcrumb')

	private localize(key: string) {
		return Breadcrumb._localizer.localize(key)
	}

	//#endregion

	//#region lifecycle callbacks

	protected firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)

		// save width of each breadcrumb item to use it for hiding mechanics
		this.calculateItemsWidth()

		let previousContentWidth: number = this.offsetWidth
		const resizeObserver = new ResizeObserver((entries) => {
			const entry = entries[0]
			const componentWidth = entry.contentBoxSize[0].inlineSize
			if (previousContentWidth === componentWidth)
				return

			this.checkComponentWidth()
			previousContentWidth = componentWidth
		})

		// init breadcrumbs
		this.checkComponentWidth()
		resizeObserver.observe(this)
	}

	protected updated(_changedProperties: PropertyValues) {
		super.updated(_changedProperties)

		// calculate all breadcrumbs width which are new
		const oldItems = _changedProperties.get('items')
		if (oldItems != null) {
			this.calculateItemsWidth()
			this.checkComponentWidth()
		}
	}

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		const displayCompressed = this.items.reduce((previousValue, currentItem) => previousValue + (currentItem.hidden ? 0 : 1), 0) == 1

		return html`
			<ul id="breadcrumbs" class="${displayCompressed ? 'breadcrumbs--compressed' : ''}">
				<li id="home"
						@click="${(event: MouseEvent) => this.handleItemClick(event, { label: 'Home', url: '/', width: 0 })}"
						@auxclick="${(event: MouseEvent) => this.handleItemClick(event, { label: 'Home', url: '/', width: 0 })}">
					${renderIcon('home', { title: this.localize('HomeButton'), inline: true, iconStyle: IconStyle.Solid })}
				</li>
				${this.renderItems()}
			</ul>
		`
	}

	private renderItems() {
		if (this.items.length === 0)
			return ''

		const hiddenItems = this.items.filter(item => item.hidden)
		return html`
			${this.renderItem(this.items[0])}
			${hiddenItems.length > 0 ? html`
				<li class="breadcrumb__item breadcrumb__dropdown">
					${renderIcon('chevron-right', { inline: true, additionalClasses: [ 'breadcrumb__separator' ] })}
					<lvl-button id="breadcrumb__three-dots" class="breadcrumb__label" size="${Size.Medium}" color="${ColorState.Info}" data-dropdown="three-dots-dropdown"
											data-tooltip="three-dots-tooltip" label="..."></lvl-button>
					<lvl-tooltip name="three-dots-tooltip" placement="${PopupPlacement.Bottom}" delayed flip orbit shift>Click on the three dots to see the hidden
						breadcrumbs from a drop-down list.
					</lvl-tooltip>
					<lvl-dropdown name="three-dots-dropdown" placement="${PopupPlacement.Bottom}" shift arrow>
						<lvl-menu>
							${repeat(hiddenItems, item => item.label, (item: BreadcrumbItem) => html`
								<lvl-menu-item icon-right="chevron-right" 
															 @click="${(event: MouseEvent) => this.handleItemClick(event, item)}" 
															 @auxclick="${(event: MouseEvent) => this.handleItemClick(event, item)}">${item.label}
								</lvl-menu-item>
							`)}
						</lvl-menu>
					</lvl-dropdown>
				</li>
			` : ''}
			${repeat(this.items.slice(1), item => item.label, (item: BreadcrumbItem) => this.renderItem(item))}
		`
	}

	private renderItem = (item: BreadcrumbItem) => {
		let contentHtml
		if (item.label) {
			contentHtml = html`
				<a href="${this.static ? '' : item.url}" class="breadcrumb__label"
							@click="${(event: MouseEvent) => this.handleItemClick(event, item)}"
							@auxclick="${(event: MouseEvent) => this.handleItemClick(event, item)}">
					${item.label}
				</span>`
		} else
			contentHtml = html`<span class="breadcrumb__label skeleton__text"></span>`

		return html`
			<li class="breadcrumb__item ${item.hidden ? 'breadcrumb__item--hidden' : ''}">
				${renderIcon('chevron-right', { inline: true, additionalClasses: [ 'breadcrumb__separator' ] })}
				${contentHtml}
			</li>
		`
	}

	//#region public methods

	/**
	 * Adds a new breadcrumb to the list.
	 * @param item new breadcrumb which is inserted at the last position
	 */
	addBreadcrumb(item: BreadcrumbCreateItem) {
		if (item == null)
			return

		const oldItems = [ ...this.items ]
		this.items.push({ ...item, width: 0 })
		this.requestUpdate('items', oldItems)
	}

	/**
	 * Removes the last breadcrumb and returns it.
	 */
	pop(): BreadcrumbItem | undefined {
		const oldItems = [ ...this.items ]
		const lastBreadcrumb = this.items.pop()
		if (lastBreadcrumb === undefined)
			return undefined

		if (this.items.length > 0)
			this.items.slice(-1)[0].hidden = false
		this.requestUpdate('items', oldItems)
		return lastBreadcrumb
	}

	//#endregion

	//#region private methods

	private async handleItemClick(mouseEvent: MouseEvent, item: BreadcrumbItem) {
		// ignore right-click
		if (mouseEvent.button === 2)
			return
		
		const index = this.items.indexOf(item)
		const newTab = mouseEvent.ctrlKey || mouseEvent.button === 1

		// open new tab with the url if ctrl was clicked
		if (newTab) {
			sessionStorage.setItem('breadcrumbs:backup', JSON.stringify(this.items.slice(0, this.items.indexOf(item))))
			window.open(item.url)
			sessionStorage.removeItem('breadcrumbs:backup')
		} else if (!this.static) {
			// prevent default because of href attribute at the breadcrumb
			mouseEvent.preventDefault();

			// define callbacks for resolve (and cancel)
			const callback = () => {
				this.handleItemClick(new MouseEvent('click'), item)
			}

			// define event and put callbacks in detail
			const pageLeaveEvent = new CustomEvent('initiate-page-leave', {
				cancelable: true,
				detail: {
					resolveCallback: callback
				}
			})

			// dispatch the page-leave-event and stop if it gets canceled
			if(!window.dispatchEvent(pageLeaveEvent))
				return
			
			// remove all breadcrumbs until the clicked
			const popCount = this.items.length - (index + 1)
			for (let start = 0; start < popCount; start++) {
				this.pop()
			}
		}

		let customEvent = new CustomEvent<BreadcrumbEventDetail>('breadcrumb-item:click', {
			detail: { item, index, newTab },
			bubbles: true,
		})

		this.dispatchEvent(customEvent)
		mouseEvent.preventDefault()
	}

	// The breadcrumb list can be too large for a single line within its container. So several items must be put into '...' to save space.
	private checkComponentWidth() {
		if (this._htmlBreadcrumb.offsetWidth === 0 || this.items.length === 0)
			return

		let performRender = false
		const lastBreadcrumb = this.items[this.items.length - 1]
		if (lastBreadcrumb.hidden) {
			lastBreadcrumb.hidden = false
			performRender = true
		}

		const threeDotsWidth = 48 as const
		let breadcrumbWidth = this._htmlBreadcrumb.offsetWidth

		if (this._htmlBreadcrumb.querySelector('.breadcrumb__dropdown') == null)
			breadcrumbWidth += threeDotsWidth + ITEM_GAP
		
		const componentWidth = this.offsetWidth
		const direction = breadcrumbWidth > componentWidth ? ResizeDirection.Smaller : ResizeDirection.Bigger
		
		if (direction === ResizeDirection.Smaller) {
			performRender = this.tryAddBreadcrumbsToDropdown(componentWidth, breadcrumbWidth)
		} else if (direction === ResizeDirection.Bigger)
			performRender = this.tryRemoveBreadcrumbsFromDropdown(componentWidth, breadcrumbWidth)
		
		if (performRender)
			this.requestUpdate('items')
	}

	// too much space for visible breadcrumbs -> show some hidden
	private tryRemoveBreadcrumbsFromDropdown(componentWidth: number, breadcrumbWidth: number) {
		let performRender = false
		
		// try to display the first item in the first place if its hidden
		if (this.items[0].hidden) {
			if (componentWidth >= (breadcrumbWidth + this.items[0].width)) {
				this.items[0].hidden = false
				breadcrumbWidth += this.items[0].width
				performRender = true
			} else {
				// if the first item can't be displayed then stop and don't try to display other items
				return false
			}
		}

		for (let index = this.items.length - 1; index > 0; index--) {
			// skip item which is already visible
			if (!this.items[index].hidden)
				continue

			// no additional item has enough space -> end it
			if (componentWidth < (breadcrumbWidth + this.items[index].width))
				break

			// additional item can be marked as visible
			this.items[index].hidden = false
			breadcrumbWidth += this.items[index].width
			performRender = true
		}
		
		return performRender
	}

	// not enough space for all breadcrumbs -> transform to three dots
	private tryAddBreadcrumbsToDropdown(componentWidth: number, breadcrumbWidth: number) {
		let performRender = false
		
		for (let index = 1; index < this.items.length - 1; index++) {
			if (componentWidth >= breadcrumbWidth)
				break

			if (!this.items[index].hidden) {
				this.items[index].hidden = true
				breadcrumbWidth -= this.items[index].width
				performRender = true
			}
		}

		// hide the first one if all other items are hidden as last option
		if (componentWidth < breadcrumbWidth) {
			this.items[0].hidden = true
			performRender = true
		}
		
		return performRender
	}

	private calculateItemsWidth() {
		this._htmlBreadcrumb.classList.add('calculate-mode')
		this.items.forEach((item, index) => {
			item.width = (this._htmlBreadcrumb.querySelectorAll(`.breadcrumb__item:not(.breadcrumb__dropdown)`)[index]?.clientWidth ?? 0) + ITEM_GAP
		})
		this._htmlBreadcrumb.classList.remove('calculate-mode')
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-breadcrumb': Breadcrumb
	}
}