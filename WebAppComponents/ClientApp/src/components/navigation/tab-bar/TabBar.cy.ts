import { stories, TabProperties } from './TabBar.stories'
import { storyTest } from '@test-home/support/advanced-functions'

import('./TabBar')

// Test suite for the example web component
describe('<lvl-tab-bar />', () => {

	storyTest('checks the default component', stories.default, () => {
		cy.mountStory(stories.default)
		const tabs: TabProperties[] = stories.default.getAttribute<TabProperties[]>('tabs')!
		cy.get('lvl-tab').should('have.length', 3)
		cy.get(`lvl-tab[label="${tabs[0].label}"]`).find('.tab__item').should('exist').should('be.visible')
		cy.get(`lvl-tab[label="${tabs[1].label}"]`).find('.tab__item').should('not.exist')
		cy.get(`lvl-tab[label="${tabs[2].label}"]`).find('.tab__item').should('not.exist')

		// Test click functions
		cy.get('.tab__label:nth-child(2)').click()
		cy.get(`lvl-tab[label="${tabs[0].label}"]`).find('.tab__item').should('not.exist')
		cy.get(`lvl-tab[label="${tabs[1].label}"]`).find('.tab__item').should('exist').should('be.visible')
		cy.get(`lvl-tab[label="${tabs[2].label}"]`).find('.tab__item').should('not.exist')

		cy.get('.tab__label:nth-child(3)').click()
		cy.get(`lvl-tab[label="${tabs[0].label}"]`).find('.tab__item').should('not.exist')
		cy.get(`lvl-tab[label="${tabs[1].label}"]`).find('.tab__item').should('not.exist')
		cy.get(`lvl-tab[label="${tabs[2].label}"]`).find('.tab__item').should('exist').should('be.visible')

		cy.get('.tab__label:nth-child(1)').click()
		cy.get(`lvl-tab[label="${tabs[0].label}"]`).find('.tab__item').should('exist').should('be.visible')
		cy.get(`lvl-tab[label="${tabs[1].label}"]`).find('.tab__item').should('not.exist')
		cy.get(`lvl-tab[label="${tabs[2].label}"]`).find('.tab__item').should('not.exist')
	})

	storyTest('navigate via keyboard', stories.default, () => {
		cy.mountStory(stories.default)
		const tabs: TabProperties[] = stories.default.getAttribute<TabProperties[]>('tabs')!

		cy.get('.tab__bar').as('tabBar')
		cy.get('@tabBar').find('.tab__label.selected').focus().click()
		cy.get('@tabBar').trigger('keydown', { key: 'ArrowRight' })
		cy.get('@tabBar').trigger('keydown', { key: 'Enter' })
		cy.get(`lvl-tab[label="${tabs[0].label}"]`).find('.tab__item').should('not.exist')
		cy.get(`lvl-tab[label="${tabs[1].label}"]`).find('.tab__item').should('exist').should('be.visible')
		cy.get(`lvl-tab[label="${tabs[2].label}"]`).find('.tab__item').should('not.exist')

		cy.get('@tabBar').trigger('keydown', { key: 'ArrowRight' })
		cy.get('@tabBar').trigger('keydown', { key: 'Enter' })
		cy.get(`lvl-tab[label="${tabs[0].label}"]`).find('.tab__item').should('not.exist')
		cy.get(`lvl-tab[label="${tabs[1].label}"]`).find('.tab__item').should('not.exist')
		cy.get(`lvl-tab[label="${tabs[2].label}"]`).find('.tab__item').should('exist').should('be.visible')

		cy.get('@tabBar').trigger('keydown', { key: 'ArrowLeft', shiftKey: true, force: true })
		cy.get('@tabBar').trigger('keydown', { key: 'ArrowLeft', force: true })
		cy.get('@tabBar').trigger('keydown', { key: ' ', force: true })
		cy.get(`lvl-tab[label="${tabs[0].label}"]`).find('.tab__item').should('exist').should('be.visible')
		cy.get(`lvl-tab[label="${tabs[1].label}"]`).find('.tab__item').should('not.exist')
		cy.get(`lvl-tab[label="${tabs[2].label}"]`).find('.tab__item').should('not.exist')
	})
})