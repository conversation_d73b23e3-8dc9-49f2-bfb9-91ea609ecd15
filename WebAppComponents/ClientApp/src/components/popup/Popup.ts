import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { ClassInfo, classMap } from 'lit/directives/class-map.js'
import { query } from 'lit/decorators/query.js'
import { Intensity } from '@/enums/intensity.ts'

@customElement('lvl-popup')
export class Popup extends LitElement {

	private static ARROW_WIDTH: number = 8
	private static MARKER_OFFSET: number = 8
	private static SAFETY_GAP: number = 8

	static styles = [
		styles.base,
		styles.color,
		styles.animation,
		css`
			:host {
				display: contents;
			}
			
			.popup {
				--arrow-width: ${Popup.ARROW_WIDTH + 1}px;
				--marker-offset: ${Popup.MARKER_OFFSET}px;
			}

			/* :popover-open has been stable since jan 25 */
			[popover]:popover-open {
				opacity: 1;
				scale: 1;
			}
			
			[popover] {
				/* Final state of the exit animation */
				opacity: 0;
				scale: 0;
				overflow: visible;

				transition:
					opacity var(--animation-time-medium) ease,
					scale var(--animation-time-medium) ease,
					overlay var(--animation-time-medium) allow-discrete,
					display var(--animation-time-medium) allow-discrete;
			}

			/* Needs to be after the previous [popover]:popover-open rule
				 to take effect, as the specificity is the same */
			@starting-style {
				[popover]:popover-open {
					opacity: 0;
					scale: 0;
				}
			}

			.popup[popover].calculating,
			.popup[popover].resizing {
				display: block;
				scale: 1;
				transition: none;
			}

			.popup[popover].calculating > .popup-content {
				max-height: none !important;
			}

			.popup[popover].reset-calculation {
				display: none;
				scale: 0;
				opacity: 0;
			}

			.popup-content {
				display: flex; // needed in combination with max-height for correct scrollbars in dropdowns
				font-size: 0;
				overflow: auto;
				border-radius: var(--size-radius-m); // needed for box shadow round edges
			}

			.popup.arrow::before {
				content: "";
				border: var(--arrow-width) solid transparent;
				position: absolute;
				pointer-events: none;
				user-select: none;
			}
			
			/* boxshadow */
			.popup-shadow--weak > .popup-content {
				box-shadow: 0 0.2rem 0.4rem 0 var(--cp-clr-shadow-weak);
			}
			
			.popup-shadow--weak.top > .popup-content {
				box-shadow: 0 -0.2rem 0.4rem 0 var(--cp-clr-shadow-weak);
			}
			
			.popup-shadow--medium > .popup-content {
				box-shadow: 0 0.4rem 0.8rem 0 var(--cp-clr-shadow-medium);
			}

			.popup-shadow--medium.top > .popup-content {
				box-shadow: 0 -0.4rem 0.8rem 0 var(--cp-clr-shadow-medium);
			}

			.popup-shadow--strong > .popup-content {
				box-shadow: 0 0.6rem 1rem 0 var(--cp-clr-shadow-strong);
			}

			.popup-shadow--strong.top > .popup-content {
				box-shadow: 0 -0.6rem 1rem 0 var(--cp-clr-shadow-strong);
			}
			
			.popup-shadow--high > .popup-content {
				box-shadow: 0 0 1.6rem 0 var(--cp-clr-shadow-strong);
			}
			
			/* Top placement */

			.popup.top-start {
				transform-origin: left bottom;
			}

			.popup.top-end {
				transform-origin: right bottom;
			}

			.popup.top-center {
				transform-origin: center bottom;
			}

			.popup.arrow.top:before {
				top: calc(100% - 1px);
				border-top-color: var(--arrow-color);
			}

			/* Bottom placement */

			.popup.bottom-start {
				transform-origin: left top;
			}

			.popup.bottom-end {
				transform-origin: right top;
			}

			.popup.bottom-center {
				transform-origin: center top;
			}

			.popup.arrow.bottom:before {
				bottom: calc(100% - 1px);
				border-bottom-color: var(--arrow-color);
			}

			/* Top AND Bottom */

			.popup.arrow.top-center:before,
			.popup.arrow.bottom-center:before {
				left: calc(50% - var(--arrow-width));
			}

			.popup.arrow.top-start:before,
			.popup.arrow.bottom-start:before {
				left: var(--marker-offset);
			}

			.popup.arrow.top-end:before,
			.popup.arrow.bottom-end:before {
				right: var(--marker-offset);
			}

			/* Left placement */

			.popup.left-start {
				transform-origin: right top;
			}

			.popup.left-end {
				transform-origin: right bottom;
			}

			.popup.left-center {
				transform-origin: right center;
			}

			.popup.arrow.left-start:not(.block-center) {
				transform-origin: calc(100% + var(--arrow-width)) calc(var(--arrow-width) + var(--marker-offset));
			}

			.popup.arrow.left-end:not(.block-center) {
				transform-origin: calc(100% + var(--arrow-width)) calc(100% - var(--arrow-width) - var(--marker-offset));
			}

			.popup.arrow.left:before {
				left: calc(100% - 1px);
				border-left-color: var(--arrow-color);
			}

			/* Right */

			.popup.right-start {
				transform-origin: left top;
			}

			.popup.right-end {
				transform-origin: left bottom;
			}

			.popup.right-center {
				transform-origin: left center;
			}

			.popup.arrow.right-start:not(.block-center) {
				transform-origin: calc(-1 * var(--arrow-width)) calc(var(--arrow-width) + var(--marker-offset));
			}

			.popup.arrow.right-end:not(.block-center) {
				transform-origin: calc(-1 * var(--arrow-width)) calc(100% - var(--arrow-width) - var(--marker-offset));
			}

			.popup.arrow.right:before {
				right: calc(100% - 1px);
				border-right-color: var(--arrow-color);
			}

			/* Left AND Right */

			.popup.arrow.left-center:before,
			.popup.arrow.right-center:before {
				top: calc(50% - var(--arrow-width));
			}

			.popup.arrow.left-start:before,
			.popup.arrow.right-start:before {
				top: var(--marker-offset);
			}

			.popup.arrow.left-end:before,
			.popup.arrow.right-end:before {
				bottom: var(--marker-offset);
			}
		`,
	]

	//#region attributes

	@property()
	placement: PopupPlacement = PopupPlacement.Bottom

	@property({ type: Boolean })
	orbit: boolean = false

	@property({ type: Boolean })
	shift: boolean = false

	@property({ type: Boolean })
	flip: boolean = false

	@property({ type: Boolean })
	open: boolean = false

	@property({ type: Boolean })
	arrow: boolean = false

	@property({ type: Number, attribute: 'offset-block' })
	blockOffset?: number

	@property({ type: Number, attribute: 'offset-block-x' })
	blockOffsetX: number = 0

	@property({ type: Number, attribute: 'offset-block-y' })
	blockOffsetY: number = 0

	@property({ type: Number, attribute: 'offset-inline' })
	inlineOffset?: number

	@property({ type: Number, attribute: 'offset-inline-x' })
	inlineOffsetX: number = 0

	@property({ type: Number, attribute: 'offset-inline-y' })
	inlineOffsetY: number = 0

	@property({ attribute: 'arrow-color' })
	arrowColor: string = 'var(--cp-clr-background-lvl-0-tooltip)'

	@property({ attribute: 'z-index', type: Number })
	zIndex?: number

	@property()
	name?: string

	@property({ attribute: 'border-radius' })
	borderRadius?: string

	@property({ attribute: 'box-shadow' })
	boxShadow?: Intensity

	@property()
	border?: string

	//#endregion

	//#region states

	@state()
	private calculatedPlacement?: PopupPlacement

	@state()
	private resizing: boolean = false

	@state()
	private top?: number

	@state()
	private left?: number

	@state()
	private maxHeight?: number

	//#endregion

	//#region private properties

	@query('.popup')
	private popup!: HTMLElement

	private get anchor(): HTMLElement | undefined {
		if (this._anchor)
			return this._anchor

		// if the element has a name-attribute, look for a sibling referencing the popup by name
		if (this.name) {
			let parentNode = this.parentElement || this.getRootNode() as ShadowRoot
			this._anchor = parentNode?.querySelector(`[data-popup="${this.name}"]`) || undefined

			// if no anchors is found and the root node is a shadow root, look for sibling reference outside the shadow root
			if (!this._anchor && (this.getRootNode() instanceof ShadowRoot))
				this._anchor = (this.getRootNode() as ShadowRoot).host.parentNode?.querySelector(`[data-popup="${this.name}"]`) || undefined
		}

		// if we haven't found an anchor, try to get the parents anchor (or trigger)
		if (!this._anchor) {
			let parentNode = this.getParentNode(this)
			if (parentNode)
				this._anchor = (parentNode as any).anchor || (parentNode as any).trigger
		}

		return this._anchor
	}

	private _anchor?: HTMLElement

	// cache for allowed placements
	private _allowedPlacements?: PopupPlacement[]

	//#endregion

	//#region LIT Functions

	/**
	 * render component
	 */
	render() {
		// calculate placement
		let calculatedPlacement = this.open ? this.calculatePlacement() : this.calculatedPlacement || this.placement

		const classes: ClassInfo = {
			popup: true,
			open: this.open,
			arrow: this.arrow,
			resizing: this.resizing,
			top: [ PopupPlacement.Top, PopupPlacement.TopStart, PopupPlacement.TopEnd ].indexOf(calculatedPlacement) > -1,
			right: [ PopupPlacement.Right, PopupPlacement.RightStart, PopupPlacement.RightEnd ].indexOf(calculatedPlacement) > -1,
			bottom: [ PopupPlacement.Bottom, PopupPlacement.BottomStart, PopupPlacement.BottomEnd ].indexOf(calculatedPlacement) > -1,
			left: [ PopupPlacement.Left, PopupPlacement.LeftStart, PopupPlacement.LeftEnd ].indexOf(calculatedPlacement) > -1,
			'top-center': calculatedPlacement == PopupPlacement.Top,
			'right-center': calculatedPlacement == PopupPlacement.Right,
			'bottom-center': calculatedPlacement == PopupPlacement.Bottom,
			'left-center': calculatedPlacement == PopupPlacement.Left,
			[`popup-shadow--${this.boxShadow}`]: this.boxShadow != null,
			...{ [calculatedPlacement]: true },
		}

		return html`
			<div popover="manual" class="${classMap(classes)}"
					 style="top:${this.top}px;left:${this.left}px;--arrow-color:${this.arrowColor};--z-index:${this.zIndex};">
				<div class="popup-content" style="max-height:${this.maxHeight ? this.maxHeight + 'px' : 'none'};${this.borderRadius ? `border-radius:${this.borderRadius};` : ''}${this.border ? `border:${this.border};` : ''}">
					<slot @slotchange=${this.handleSlotChange}></slot>
				</div>
			</div>
		`
	}

	/**
	 * remove events from window before destroying component
	 */
	disconnectedCallback() {
		window.removeEventListener('resize', this.handleResize)
		window.removeEventListener('scroll', this.handleResize)
		this.getScrollParents().forEach(scrollParent => scrollParent.removeEventListener('scroll', this.handleResize))
	}

	/**
	 * remove events from window
	 */
	protected updated(changedProperties: PropertyValues<this>) {
		// append/remove resize events
		if (changedProperties.get('open') !== undefined) { // dont use "has" because on init has = true but value is empty and we don't want to trigger recalculate style by getting the scroll parents!
			if (this.open) {
				window.addEventListener('resize', this.handleResize)
				window.addEventListener('scroll', this.handleResize)
				this.getScrollParents().forEach(scrollParent => scrollParent.addEventListener('scroll', this.handleResize))
				if (this.popup.isConnected)
					this.popup.showPopover()
			} else {
				window.removeEventListener('resize', this.handleResize)
				window.removeEventListener('scroll', this.handleResize)
				this.getScrollParents().forEach(scrollParent => scrollParent.removeEventListener('scroll', this.handleResize))
				if (this.popup.isConnected)
					this.popup.hidePopover()
			}
		}

		// blockOffset
		if (changedProperties.has('blockOffset')) {
			this.blockOffsetX = this.blockOffset || 0
			this.blockOffsetY = this.blockOffset || 0
		}

		// inlineOffset
		if (changedProperties.has('inlineOffset')) {
			this.inlineOffsetX = this.inlineOffset || 0
			this.inlineOffsetY = this.inlineOffset || 0
		}

		// some placement property changed externally -> reset allowed placements
		if (changedProperties.has('placement') || changedProperties.has('flip') || changedProperties.has('shift') || changedProperties.has('orbit'))
			this._allowedPlacements = undefined
	}

	//#endregion

	//#region Event Handlers

	// recalculate placement during resize
	private handleResize = () => {
		// sync calculation event with animation frames
		if (!this.resizing) {
			window.requestAnimationFrame(() => {
				this.calculatePlacement()
				this.resizing = false
			})
			this.resizing = true
		}
	}

	// recalculate placement if popup content changes while popup is open
	private handleSlotChange() {
		if (this.open)
			this.handleResize()
	}

	//#endregion

	//#region private methods

	// calculate the placement to use
	private calculatePlacement(): PopupPlacement {
		// reset maxHeight for new calculation run
		this.maxHeight = undefined
		let calculated: PopupPlacement = this.placement

		let allowedPlacements = this.getAllowedPlacements()
		if (!this.anchor) {
			console.warn('unable to show popup', this, this.anchor)
			return calculated
		}

		let dimensions = this.getDimensions()
		let anchorDimension = this.anchor.getBoundingClientRect()
		let availableSpace = this.getAvailableSpace(this.anchor)
		
		// calculate dimensions
		const arrowWidth = this.arrow ? Popup.ARROW_WIDTH : 0
		const totalWidth = dimensions[0] + Popup.SAFETY_GAP
		let totalHeight = dimensions[1] + Popup.SAFETY_GAP
		const parentWidth = anchorDimension.width
		const parentHeight = anchorDimension.height
		
		// automatically switch to opposite side if we need more space (and other side has more space available)
		if (this.flip) {
			switch (this.placement) {
				case PopupPlacement.Top:
				case PopupPlacement.TopStart:
				case PopupPlacement.TopEnd:
					if (availableSpace.top < totalHeight && availableSpace.bottom > availableSpace.top) {
						allowedPlacements = allowedPlacements.filter(placement => !placement.toString().startsWith("top"))
						this.maxHeight = availableSpace.bottom - arrowWidth - this.blockOffsetY - Popup.SAFETY_GAP
					}
					break
				case PopupPlacement.Bottom:
				case PopupPlacement.BottomStart:
				case PopupPlacement.BottomEnd:
					if (availableSpace.bottom < totalHeight && availableSpace.top > availableSpace.bottom) {
						allowedPlacements = allowedPlacements.filter(placement => !placement.toString().startsWith("bottom"))
						this.maxHeight = availableSpace.top - arrowWidth - this.blockOffsetY - Popup.SAFETY_GAP
					}
					break
				case PopupPlacement.Left:
				case PopupPlacement.LeftStart:
				case PopupPlacement.LeftEnd:
					if (availableSpace.left < totalWidth && availableSpace.right > availableSpace.left)
						allowedPlacements = allowedPlacements.filter(placement => !placement.toString().startsWith("left"))
					break
				case PopupPlacement.Right:
				case PopupPlacement.RightStart:
				case PopupPlacement.RightEnd:
					if (availableSpace.right < totalWidth && availableSpace.left > availableSpace.right)
						allowedPlacements = allowedPlacements.filter(placement => !placement.toString().startsWith("right"))
					break
			}
		}
		
		// we calculate possible matches using the totalHeight so if this height will get limited by the maxHeight, we need to calculate with the maxHeight instead
		if (this.maxHeight)
			totalHeight = Math.min(dimensions[1], this.maxHeight) + Popup.SAFETY_GAP
		
		for (const placement of allowedPlacements) {
			let allowed = false
			switch (placement) {
				case PopupPlacement.Right:
					allowed = availableSpace.top >= 0 && availableSpace.bottom >= 0 && totalWidth + arrowWidth + this.inlineOffsetX <= availableSpace.right
						&& (totalHeight - parentHeight) / 2 <= availableSpace.top
						&& (totalHeight - parentHeight) / 2 <= availableSpace.bottom
					break
				case PopupPlacement.RightStart:
					allowed = availableSpace.top >= 0 && totalWidth + arrowWidth + this.inlineOffsetX <= availableSpace.right && totalHeight - parentHeight <= availableSpace.bottom
					break
				case PopupPlacement.RightEnd:
					allowed = availableSpace.bottom >= 0 && totalWidth + arrowWidth + this.inlineOffsetX <= availableSpace.right && totalHeight - parentHeight <= availableSpace.top
					break
				case PopupPlacement.Bottom:
					allowed = totalHeight + arrowWidth + this.blockOffsetY <= availableSpace.bottom
						&& (totalWidth - parentWidth) / 2 <= availableSpace.left
						&& (totalWidth - parentWidth) / 2 <= availableSpace.right
					break
				case PopupPlacement.BottomStart:
					allowed = availableSpace.left >= 0 && totalHeight + arrowWidth + this.blockOffsetY <= availableSpace.bottom && totalWidth - parentWidth <= availableSpace.right
					break
				case PopupPlacement.BottomEnd:
					allowed = availableSpace.right >= 0 && totalHeight + arrowWidth + this.blockOffsetY <= availableSpace.bottom && totalWidth - parentWidth <= availableSpace.left
					break
				case PopupPlacement.Left:
					allowed = availableSpace.top >= 0 && totalWidth + arrowWidth + this.inlineOffsetX <= availableSpace.left
						&& (totalHeight - parentHeight) / 2 <= availableSpace.top
						&& (totalHeight - parentHeight) / 2 <= availableSpace.bottom
					break
				case PopupPlacement.LeftStart:
					allowed = availableSpace.top >= 0 && totalWidth + arrowWidth + this.inlineOffsetX <= availableSpace.left && totalHeight - parentWidth <= availableSpace.bottom
					break
				case PopupPlacement.LeftEnd:
					allowed = availableSpace.bottom >= 0 && totalWidth + arrowWidth + this.inlineOffsetX <= availableSpace.left && totalHeight - parentWidth <= availableSpace.top
					break
				case PopupPlacement.Top:
					allowed = totalHeight + arrowWidth + this.blockOffsetY <= availableSpace.top
						&& (totalWidth - parentWidth) / 2 <= availableSpace.left
						&& (totalWidth - parentWidth) / 2 <= availableSpace.right
					break
				case PopupPlacement.TopStart:
					allowed = availableSpace.left >= 0 && totalHeight + arrowWidth + this.blockOffsetY <= availableSpace.top && totalWidth - parentWidth <= availableSpace.right
					break
				case PopupPlacement.TopEnd:
					allowed = availableSpace.right >= 0 && totalHeight + arrowWidth + this.blockOffsetY <= availableSpace.top && totalWidth - parentWidth <= availableSpace.left
					break
			}
			if (allowed) {
				calculated = placement
				break
			}
		}

		// arrow position fix?
		let arrowOffsetX = this.arrow && this.anchor.offsetWidth < 32 ? this.anchor.offsetWidth / 2 - Popup.ARROW_WIDTH - Popup.MARKER_OFFSET : 0
		let arrowOffsetY = this.arrow && this.anchor.offsetHeight < 32 ? this.anchor.offsetHeight / 2 - Popup.ARROW_WIDTH - Popup.MARKER_OFFSET : 0

		// switch vertical position if space is not sufficient (and switching is allowed)
		if (this.shift) {
			switch (calculated) {
				case PopupPlacement.LeftStart:
				case PopupPlacement.RightStart:
					if (availableSpace.bottom + parentHeight < dimensions[1] && availableSpace.top > availableSpace.bottom)
						calculated = calculated == PopupPlacement.LeftStart ? PopupPlacement.LeftEnd : PopupPlacement.RightEnd
					break
				case PopupPlacement.LeftEnd:
				case PopupPlacement.RightEnd:
					if (availableSpace.top + parentHeight < dimensions[1] && availableSpace.bottom > availableSpace.top)
						calculated = calculated == PopupPlacement.LeftEnd ? PopupPlacement.LeftStart : PopupPlacement.RightStart
					break
			}
		}

		// calculate top and maxHeight
		const offsetTop = anchorDimension.y
		switch (calculated) {
			case PopupPlacement.Top:
			case PopupPlacement.TopStart:
			case PopupPlacement.TopEnd:
				this.maxHeight = availableSpace.top - arrowWidth - Popup.SAFETY_GAP
				this.top = offsetTop - Math.min(dimensions[1], this.maxHeight) - arrowWidth - this.blockOffsetY
				break
			case PopupPlacement.Bottom:
			case PopupPlacement.BottomStart:
			case PopupPlacement.BottomEnd:
				this.top = offsetTop + anchorDimension.height + arrowWidth + this.blockOffsetY
				this.maxHeight = availableSpace.bottom - arrowWidth - Popup.SAFETY_GAP
				break
			case PopupPlacement.Left:
			case PopupPlacement.Right:
				this.top = offsetTop + anchorDimension.height / 2 - dimensions[1] / 2 + this.inlineOffsetY / 2
				break
			case PopupPlacement.LeftStart:
			case PopupPlacement.RightStart:
				this.top = offsetTop + this.inlineOffsetY + arrowOffsetY
				this.maxHeight = availableSpace.bottom + parentHeight - Popup.SAFETY_GAP
				break
			case PopupPlacement.LeftEnd:
			case PopupPlacement.RightEnd:
				this.maxHeight = availableSpace.top + parentHeight - Popup.SAFETY_GAP
				this.top = offsetTop + anchorDimension.height - Math.min(dimensions[1], this.maxHeight) - this.inlineOffsetY - arrowOffsetY
				break
		}

		// calculate left
		const offsetLeft = anchorDimension.x
		switch (calculated) {
			case PopupPlacement.Left:
			case PopupPlacement.LeftStart:
			case PopupPlacement.LeftEnd:
				this.left = offsetLeft - dimensions[0] - arrowWidth - this.inlineOffsetX
				break
			case PopupPlacement.Right:
			case PopupPlacement.RightStart:
			case PopupPlacement.RightEnd:
					this.left = offsetLeft + anchorDimension.width + arrowWidth + this.inlineOffsetX
				break
			case PopupPlacement.Top:
			case PopupPlacement.Bottom:
				this.left = offsetLeft + anchorDimension.width / 2 - dimensions[0] / 2 + this.blockOffsetX / 2
				break
			case PopupPlacement.TopStart:
			case PopupPlacement.BottomStart:
				this.left = offsetLeft + this.blockOffsetX + arrowOffsetX
				break
			case PopupPlacement.TopEnd:
			case PopupPlacement.BottomEnd:
				this.left = offsetLeft + (anchorDimension.width - dimensions[0]) - this.blockOffsetX - arrowOffsetX
				break
		}

		this.calculatedPlacement = calculated
		return this.calculatedPlacement
	}

	// Determine which placements are allowed (by setting flip, shift, orbit)
	private getAllowedPlacements() {
		if (this._allowedPlacements)
			return this._allowedPlacements

		let allowedPlacements = [ this.placement ]
		let addPlacement = (...placements: PopupPlacement[]) => {
			for (const placement of placements) {
				if (allowedPlacements.indexOf(placement) == -1)
					allowedPlacements.push(placement)
			}
		}

		// orbit = circle around the whole parent (used for tooltips)
		if (this.orbit) {
			addPlacement(PopupPlacement.Right)
			addPlacement(PopupPlacement.RightStart)
			addPlacement(PopupPlacement.RightEnd)
			addPlacement(PopupPlacement.Bottom)
			addPlacement(PopupPlacement.BottomStart)
			addPlacement(PopupPlacement.BottomEnd)
			addPlacement(PopupPlacement.Left)
			addPlacement(PopupPlacement.LeftStart)
			addPlacement(PopupPlacement.LeftEnd)
			addPlacement(PopupPlacement.Top)
			addPlacement(PopupPlacement.TopStart)
			addPlacement(PopupPlacement.TopEnd)
		}

		// shift = only move in the initial placement layer
		if (!this.orbit && this.shift) {
			switch (this.placement) {
				case PopupPlacement.Top:
				case PopupPlacement.TopStart:
				case PopupPlacement.TopEnd:
					addPlacement(PopupPlacement.Top, PopupPlacement.TopStart, PopupPlacement.TopEnd)
					break
				case PopupPlacement.Right:
				case PopupPlacement.RightStart:
				case PopupPlacement.RightEnd:
					addPlacement(PopupPlacement.Right, PopupPlacement.RightStart, PopupPlacement.RightEnd)
					break
				case PopupPlacement.Bottom:
				case PopupPlacement.BottomStart:
				case PopupPlacement.BottomEnd:
					addPlacement(PopupPlacement.Bottom, PopupPlacement.BottomStart, PopupPlacement.BottomEnd)
					break
				case PopupPlacement.Left:
				case PopupPlacement.LeftStart:
				case PopupPlacement.LeftEnd:
					addPlacement(PopupPlacement.Left, PopupPlacement.LeftStart, PopupPlacement.LeftEnd)
					break
			}
		}

		// flip = flip to the opposite side
		if (!this.orbit && this.flip) {
			// flip all allowed placements (could be multiple if shift is enabled)
			for (const placement of allowedPlacements) {
				switch (placement) {
					case PopupPlacement.Top:
						addPlacement(PopupPlacement.Bottom)
						break
					case PopupPlacement.TopStart:
						addPlacement(PopupPlacement.BottomStart)
						break
					case PopupPlacement.TopEnd:
						addPlacement(PopupPlacement.BottomEnd)
						break
					case PopupPlacement.Right:
						addPlacement(PopupPlacement.Left)
						break
					case PopupPlacement.RightStart:
						addPlacement(PopupPlacement.LeftStart)
						break
					case PopupPlacement.RightEnd:
						addPlacement(PopupPlacement.LeftEnd)
						break
					case PopupPlacement.Bottom:
						addPlacement(PopupPlacement.Top)
						break
					case PopupPlacement.BottomStart:
						addPlacement(PopupPlacement.TopStart)
						break
					case PopupPlacement.BottomEnd:
						addPlacement(PopupPlacement.TopEnd)
						break
					case PopupPlacement.Left:
						addPlacement(PopupPlacement.Right)
						break
					case PopupPlacement.LeftStart:
						addPlacement(PopupPlacement.RightStart)
						break
					case PopupPlacement.LeftEnd:
						addPlacement(PopupPlacement.RightEnd)
						break
				}
			}
		}

		this._allowedPlacements = allowedPlacements
		return this._allowedPlacements
	}

	// cached calculation of tooltip dimensions based on current value
	private getDimensions(): [ number, number ] {
		if (!this.popup)
			return [0, 0]
		
		this.popup.classList.add('calculating')
		const dimensions: [ number, number ] = [ this.popup.offsetWidth, this.popup.offsetHeight ]
		this.popup.classList.add('reset-calculation')
		this.popup.offsetHeight // needed in order to force scale back to 0 before removing the transition: none
		this.popup.classList.remove('calculating', 'reset-calculation')

		return dimensions
	}

	// calculation of available space (could be cached as well if we remember scroll position and window dimensions)
	private getAvailableSpace(element: HTMLElement, relativeTo: HTMLElement = document.documentElement) {
		let spacings = { top: 0, right: 0, bottom: 0, left: 0 }
		if (typeof element === 'undefined' || element === null)
			return spacings

		let elementRect = element.getBoundingClientRect()
		let parentRect = (typeof relativeTo !== 'undefined') ? relativeTo.getBoundingClientRect() : {
			top: 0,
			right: 0,
			bottom: 0,
			left: 0,
		}

		let boundaryTop: number = Math.max(parentRect.top, 0)
		let boundaryRight: number = Math.min(parentRect.right, window.innerWidth)
		let boundaryBottom: number = Math.min(parentRect.bottom, window.innerHeight)
		let boundaryLeft: number = Math.max(parentRect.left, 0)

		spacings['top'] = elementRect.top - boundaryTop
		spacings['right'] = boundaryRight - elementRect.right
		spacings['bottom'] = boundaryBottom - elementRect.bottom
		spacings['left'] = elementRect.left - boundaryLeft

		return spacings
	}

	private getScrollParents(): HTMLElement[] {
		let scrollParents = []
		let currentParent = this.getParentNode(this)
		while (currentParent) {
			if (this.isScrollable(currentParent))
				scrollParents.push(currentParent)

			currentParent = this.getParentNode(currentParent)
		}
		
		if (!scrollParents.length)
			scrollParents.push(document.documentElement)
		
		return scrollParents
	}

	// checks if an HTMLElement is scrollable
	private isScrollable(node: HTMLElement) {
		const style = getComputedStyle(node)
		//let position = style.getPropertyValue('position')
		return [ 'overflow', 'overflow-x', 'overflow-y' ].some((propertyName) => {
			const value = style.getPropertyValue(propertyName)
			return value === 'auto' || value === 'scroll' || value === 'hidden'
		})
	}

	// gets the parent node of an element and works with shadow roots
	private getParentNode(node: HTMLElement): HTMLElement | null {
		if (node.parentElement) {
			if (!!node.parentElement.shadowRoot) {
				if (node.slot)
					return node.parentElement.shadowRoot.querySelector(`slot[name="${node.slot}"]`)
				else
					return node.parentElement.shadowRoot.querySelector('slot:not([name])')
			}
			return node.parentElement
		}

		let rootNode = node.getRootNode()
		if (rootNode) {
			let host = (rootNode as ShadowRoot).host
			if (host instanceof HTMLElement)
				return host
		}

		return null
	}

	//#endregion
}

export const enum PopupPlacement {
	Top = 'top',
	TopStart = 'top-start',
	TopEnd = 'top-end',
	Right = 'right',
	RightStart = 'right-start',
	RightEnd = 'right-end',
	Bottom = 'bottom',
	BottomStart = 'bottom-start',
	BottomEnd = 'bottom-end',
	Left = 'left',
	LeftStart = 'left-start',
	LeftEnd = 'left-end'
}

declare global {
	interface HTMLElementTagNameMap {
		'lvl-popup': Popup
	}
}