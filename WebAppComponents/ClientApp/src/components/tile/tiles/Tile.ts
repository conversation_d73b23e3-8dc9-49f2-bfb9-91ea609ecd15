import { css, html, LitElement } from "lit";
import { customElement, property } from "lit/decorators.js";
import { renderIcon } from "@/shared/component-html";
import { IconStyle } from "@/enums/icon-style";
import * as styles from "@/shared/component-styles.ts";
import { DataType } from "@/enums";
import { formatData } from "@/enums/data-type";
import { DataTypeOptions } from "@/shared/types";
import { fontAwesome } from "@/shared/font-awesome";

export type TileAttributes = {
	subtitle?: string;
	type?: TileType;
	onTileClick?: () => void;
	// Value Tile
	value?: string;
	valueType: DataType;
	// Value Tile Options
	sign?: string;
	decimalPlaces?: number;
	withThousandSeparators?: boolean;
	divider?: number;
	ignoreTimezone?: boolean;
	// Icon Tile
	icon?: string;
	iconStyle?: IconStyle;
	skeleton?: boolean;
};

export enum TileType {
	Warn = "warn",
	Info = "info",
	Success = "success",
	Error = "error",
	Inactive = "inactive",
}

/**
 * Tile web component using LIT (https://lit.dev)
 */
@customElement("lvl-tile")
export class Tile extends LitElement implements TileAttributes {
	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static override readonly styles = [
		styles.base,
		styles.color,
		styles.skeleton,
		fontAwesome,
		css`
			:host {
				--tile-background: var(--cp-clr-background-lvl-0);
				--tile-border: transparent;
				--tile-text-primary: var(--cp-clr-text-primary-positiv);
				--tile-text-secondary: var(--cp-clr-text-secondary);
				--tile-icon: var(--cp-clr-state-active);
			}

			:host([type="warn"]) {
				--tile-background: var(--cp-clr-signal-warning-light);
				--tile-border: var(--cp-clr-signal-warning);
			}

			:host([type="info"]) {
				--tile-background: var(--cp-clr-signal-info-light);
				--tile-border: var(--cp-clr-signal-info);
			}

			:host([type="success"]) {
				--tile-background: var(--cp-clr-signal-success-light);
				--tile-border: var(--cp-clr-signal-success);
			}

			:host([type="error"]) {
				--tile-background: var(--cp-clr-signal-error-light);
				--tile-border: var(--cp-clr-signal-error);
			}

			:host([type="inactive"]), :host([disabled]) {
				--tile-text-primary: var(--cp-clr-state-inactive);
				--tile-text-secondary: var(--cp-clr-state-inactive);
				--tile-icon: var(--cp-clr-state-inactive);
			}

			.basic-tile {
				cursor: default;
				display: flex;
				flex-direction: column;
				flex-wrap: nowrap;
				align-items: center;
				justify-content: center;
				border: 1px solid var(--tile-border);
				background: var(--tile-background);
				padding: var(--size-spacing-xxl) var(--size-spacing-l) var(--size-spacing-xxl) var(--size-spacing-l);
				gap: var(--size-spacing-m);
				border-radius: var(--size-radius-m);
				box-shadow: 0 0 0.2rem 0 var(--cp-clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--cp-clr-shadow-weak);
				position: relative;
				height: 100%;
				min-height: 120px;
				min-width: 120px;

				h3 {
					font-size: var(--size-text-xl);
					font-weight: 600;
					text-align: center;
					margin: 0;
					color: var(--tile-text-primary);
					white-space: nowrap;
				}

				p {
					font-size: var(--size-text-l);
					font-weight: 400;
					text-align: center;
					margin: 0;
					color: var(--tile-text-secondary);
					white-space: nowrap;
				}

				i {
					color: var(--cp-clr-state-active);
					font-size: var(--size-text-xxl);
				}
			}

			.clickable {
				cursor: pointer;
			}

			.clickable:hover {
				border-color: #007acc !important;
			}
		`,
	];

	//#region attributes

	@property({ type: String })
	subtitle?: string;

	@property({ type: String })
	type?: TileType;

	@property({ type: Function })
	onTileClick?: () => void;

	// Value Tile

	@property({ type: String })
	value?: string;

	@property({ type: String, attribute: "value-type" })
	valueType: DataType = DataType.String;

	@property({ type: String })
	sign?: string;

	@property({ type: Number, attribute: "decimal-places" })
	decimalPlaces?: number;

	@property({ type: Boolean, attribute: "with-thousand-separators" })
	withThousandSeparators?: boolean;

	@property({ type: Number })
	divider?: number;

	@property({ type: Boolean, attribute: "ignore-timezone" })
	ignoreTimezone?: boolean;

	// Icon Tile

	@property({ type: String })
	icon?: string;

	@property({ attribute: "icon-style" })
	iconStyle?: IconStyle = IconStyle.Light;

	@property({reflect: true})
	skeleton?: boolean = false;

	//#endregion

	//#region private properties
	//#endregion

	render() {
		const iconHtml = this.icon ? renderIcon(this.icon, { iconStyle: this.iconStyle, inline: true }) : "";
		const valueHtml =
			this.value != null
				? html`<h3>
						${this._formatData(this.value, this.valueType, {
							sign: this.sign,
							decimalPlaces: this.decimalPlaces,
							withThousandSeparators: this.withThousandSeparators,
							divider: this.divider,
						})}
				  </h3>`
				: "";
		const subtitleHtml = this.subtitle ? html`<p>${this.subtitle}</p>` : "";

		return html`
			<div class="${"basic-tile" + (this.onTileClick ? " clickable" : "")} ${this.skeleton ? "skeleton__block": ""}" @click=${this.onTileClick}>${iconHtml} ${valueHtml} ${subtitleHtml}</div>
		`;
	}

	//#region states
	//#endregion states

	//#region lifecycle callbacks
	//#endregion

	//#region public methods
	//#endregion

	//#region private methods

	private _formatData(value: any, type: DataType, typeOptions: Partial<DataTypeOptions & { divider: number }>): string {
		if ([DataType.Integer, DataType.Double, DataType.Long].includes(type) && typeOptions.divider) {
			value = parseFloat(value) / typeOptions.divider;
			type = DataType.Double;
		} else if ([DataType.Integer, DataType.Double, DataType.Long].includes(type)) {
			value = parseFloat(value);
		}
		return formatData(value, type, typeOptions);
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class
declare global {
	interface HTMLElementTagNameMap {
		"lvl-tile": Tile;
	}
}
