import { stories } from './Toast.stories'
import { storyTest } from '@test-home/support/advanced-functions'
import { DESTROY_TIMEOUT } from '@/components/toaster/toast/Toast.ts'

import('./Toast')

// Test suite for the example web component
describe('<lvl-toast />', () => {

	storyTest('checks that the toast closes itself', stories.default, () => {
		cy.mountStory(stories.default)
		
		cy.get('lvl-toast').should('be.visible')
		cy.wait(DESTROY_TIMEOUT)
		cy.get('lvl-toast').should('not.be.visible')
	})

	storyTest('closes the toast', stories.permanent, () => {
		cy.mountStory(stories.permanent)

		// checks that toast does not fade away
		cy.get('lvl-toast').should('be.visible')
		cy.wait(DESTROY_TIMEOUT)
		cy.get('lvl-toast').should('be.visible')

		// close it manually
		cy.get('.toast__content .heading [data-action=close]').click()
		cy.get('lvl-toast').should('not.be.visible')
	})

	storyTest('checks configuration and changes attributes', stories.permanent, () => {
		cy.mountStory(stories.permanent)

		cy.get('.heading').should('contain.text', stories.permanent.getAttribute('heading'))
		cy.get('lvl-toast').invoke('css', 'backgroundColor').then(bgColor => {
			cy.expectColorValue(bgColor.toString(), 'rgb(229, 245, 255)', 'rgb(0, 30, 51)')
		})
		
		cy.get('#toast .icon').should('have.class', 'fa-circle-info')
		cy.get('lvl-toast').then($toastElement => {
			expect($toastElement[0].innerText).is.empty
		})

		// change heading
		cy.get('lvl-toast').invoke('attr', 'heading', 'blub')
		cy.get('.heading').should('contain.text', 'blub')

		// change type
		cy.get('lvl-toast').invoke('attr', 'type', 'warning')
		cy.get('lvl-toast').invoke('css', 'backgroundColor').then(bgColor => {
			cy.expectColorValue(bgColor.toString(), 'rgb(255, 247, 237)', 'rgb(34, 32, 42)')
		})
		cy.get('#toast .icon').should('have.class', 'fa-triangle-exclamation')
	})

	storyTest('checks the content', stories.content, () => {
		cy.mountStory(stories.content)

		cy.get('lvl-toast').then($toastElement => {
			expect($toastElement[0].innerText).is.equal(stories.content.getAttribute('content'))
		})
	})

	storyTest('checks the button functions', stories.buttonAndContent, () => {
		cy.spyConsole('log')

		cy.mountStory(stories.buttonAndContent)

		cy.get('lvl-toast lvl-button:last-child').click()
		cy.getLog((calls) => {
			expect(calls.length).to.be.greaterThan(0)
			expect(calls[calls.length - 1].args[0]).to.equal('Documentation clicked')
		})

		cy.get('lvl-toast [data-action=close]').click()
		cy.get('lvl-toast').should('not.be.visible')
	})
})