import type { <PERSON><PERSON>, <PERSON>Obj as Story } from '@storybook/web-components'
import { html, TemplateResult } from 'lit'
import { ifDefined } from 'lit/directives/if-defined.js'
import { LevelStory } from '@story-home/support/commands'
import { MessageType, ToastAttributes } from './Toast'
import { ButtonType } from '@/components/atomics/button/Button.ts'
import { ColorState } from '@/enums/color-state.ts'

import('@/components/atomics/button/Button')
import('./Toast')

/* 
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

type ToastProperties = Partial<ToastAttributes & {
	buttons: TemplateResult<1>,
	content: string
}>
type ToastStory = Story<ToastProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-toast',
	tags: [ 'autodocs' ],
	render: (_args: ToastProperties) => html`
		<lvl-toast open heading="${ifDefined(_args.heading)}" type="${_args.type}" ?permanent="${_args.permanent}">
			${ifDefined(_args.content)}
			${ifDefined(_args.buttons)}
		</lvl-toast>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		buttons: { table: { disable: true } },
		type: {
			control: 'select',
			options: [ MessageType.Info, MessageType.Success, MessageType.Warning, MessageType.Error ],
			table: {
				type: { summary: 'select' },
				defaultValue: { summary: MessageType.Info },
			},
			description: 'Which type and color scheme shall to message get?',
		},
		heading: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '' },
			},
			description: 'Heading of the message. Short and concise.',
		},
		content: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '' },
			},
			description: 'Message',
		},
		permanent: {
			control: 'boolean',
			description: 'Message closing by button action are automatically?',
			table: {
				type: { summary: 'boolean' }
			}
		},
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a default Toast
 */
export const Default: ToastStory = {
	args: {
		heading: 'Data was stored',
		type: MessageType.Info
	}
}

/**
 * Appearance of a Toast which must be closed manually
 */
export const Permanent: ToastStory = {
	args: {
		heading: 'Message must be closed manually',
		type: MessageType.Info,
		permanent: true,
	}
}

/**
 * Appearance of a default Toast with an ok button
 */
export const OkButton: ToastStory = {
	args: {
		heading: 'Unable to connect to your account',
		type: MessageType.Error,
		buttons: html`
			<lvl-button slot="button" type="${ButtonType.Secondary}" color="${ColorState.Info}" label="OK"></lvl-button>
		`
	}
}

/**
 * Appearance of a default Toast with message
 */
export const Content: ToastStory = {
	args: {
		heading: 'Action successfully executed',
		type: MessageType.Success,
		content: 'To have escaped the worst each time, that\'s a blessing. You are a very lucky man. Perhaps each time you were exactly where you were meant to be.'
	}
}

/**
 * Appearance of a default Toast with an two buttons and message
 */
export const ButtonsAndContent: ToastStory = {
	args: {
		heading: 'Whoops!',
		type: MessageType.Warning,
		content: 'Let\'s say this Twinkie represents the normal amount of psychokinetic energy in the New York area. According to this morning\'s sample, it would be a twinkie... 35 feet long and weighing approximately 600 pounds.',
		buttons: html`
			<lvl-button slot="button" label="Cancel" data-action="close" type="${ButtonType.Secondary}"></lvl-button>
			<lvl-button slot="button" label="Documentation" type="${ButtonType.Primary}" @click="${() => console.log('Documentation clicked')}"></lvl-button>
		`
	}
}


//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
	permanent: new LevelStory(meta, Permanent, 'Message with Close Button'),
	ok: new LevelStory(meta, OkButton, 'Ok-Button Message'),
	content: new LevelStory(meta, Content, 'Message with Content'),
	buttonAndContent: new LevelStory(meta, ButtonsAndContent, 'Message with Buttons and Content'),
} as const