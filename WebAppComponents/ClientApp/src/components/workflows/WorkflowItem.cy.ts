import { stories } from './WorkflowItem.stories'
import { storyTest } from '@test-home/support/advanced-functions'

import('./WorkflowItem')

// Test suite for the example web component
describe('<lvl-workflow-item />', () => {

	storyTest('checks the default component', stories.default, () => {
		cy.mountStory(stories.default)
		
		const workflowName = stories.default.getAttribute<string>('workflowName') ?? ''
		const label = stories.default.getAttribute<string>('label') ?? ''
		
		cy.get('lvl-workflow-item').find('.text .workflow-name').should('have.text', workflowName)
		cy.get('lvl-workflow-item').find('.text .workflow-state').should('have.text', label)
		cy.get('lvl-workflow-item').find('.menu lvl-button').should('be.visible')

		cy.get('lvl-workflow-item').invoke('attr', 'compressed', '')
		cy.get('lvl-workflow-item').find('.text').should('not.exist')
		cy.get('lvl-workflow-item').find('.menu lvl-button').should('not.exist')
		cy.get('lvl-workflow-item').invoke('removeAttr', 'compressed', '')
		
		cy.get('lvl-workflow-item > lvl-menu-item').then(item => item.remove())
		cy.get('lvl-workflow-item').find('.menu lvl-button').should('not.be.visible')
	})

	storyTest('checks the medium component', stories.medium, () => {
		cy.mountStory(stories.medium)

		const workflowName = stories.medium.getAttribute<string>('workflowName') ?? ''
		const label = stories.medium.getAttribute<string>('label') ?? ''

		cy.get('lvl-workflow-item').find('.text .workflow-name').should('not.exist')
		cy.get('lvl-workflow-item').find('.text .workflow-state').should('have.text', label)
		
		cy.get('lvl-tooltip').should('have.text', workflowName)

		cy.get('lvl-workflow-item').invoke('attr', 'compressed', '')
		cy.get('lvl-workflow-item').find('.text').should('not.exist')
		cy.get('lvl-tooltip').should('contain.text', workflowName)
		cy.get('lvl-tooltip').should('contain.text', label)
		cy.get('lvl-workflow-item').invoke('removeAttr', 'compressed', '')

		cy.get('lvl-workflow-item').find('.text .workflow-name').should('not.exist')
		cy.get('lvl-workflow-item').find('.text .workflow-state').should('have.text', label)
	})

	storyTest('checks the medium component', stories.small, () => {
		cy.mountStory(stories.small)

		const workflowName = stories.small.getAttribute<string>('workflowName') ?? ''
		const label = stories.small.getAttribute<string>('label') ?? ''

		cy.get('lvl-workflow-item').find('.text .workflow-name').should('not.exist')
		cy.get('lvl-workflow-item').find('.text .workflow-state').should('have.text', label)

		cy.get('lvl-tooltip').should('have.text', workflowName)

		cy.get('lvl-workflow-item').invoke('attr', 'compressed', '')
		cy.get('lvl-workflow-item').find('.text').should('not.exist')
		cy.get('lvl-tooltip').should('contain.text', workflowName)
		cy.get('lvl-tooltip').should('contain.text', label)
		cy.get('lvl-workflow-item').invoke('removeAttr', 'compressed', '')

		cy.get('lvl-workflow-item').find('.text .workflow-name').should('not.exist')
		cy.get('lvl-workflow-item').find('.text .workflow-state').should('have.text', label)
	})
})