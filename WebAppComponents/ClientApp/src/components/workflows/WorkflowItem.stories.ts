import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { WorkflowItemType } from '@/components/workflows/WorkflowItem.ts'
import { html, TemplateResult } from 'lit'
import { ifDefined } from 'lit/directives/if-defined.js'
import { LevelStory } from '@story-home/support/commands.ts'
import { WorkflowStateType } from '@/enums/workflow-state-type'
import { Size } from '@/enums/size.ts'

import('./WorkflowItem')
import('@/components/dropdown/DropdownMenuItem')
import('@/components/dropdown/DropdownMenu')
import('@/components/dropdown/Dropdown')
import('@/components/atomics/tooltip/Tooltip')
import('@/components/atomics/button/Button.ts')


type WorkflowItemProperties = Partial<WorkflowItemType & {
	states: TemplateResult
}>
type WorkflowItemStory = Story<WorkflowItemProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-chip',
	tags: [ 'autodocs' ],
	render: (_args: WorkflowItemProperties) => html`
		<lvl-workflow-item workflow-id="${ifDefined(_args.workflowId)}"
											 workflow-name="${ifDefined(_args.workflowName)}"
											 label="${ifDefined(_args.label)}"
											 state-type="${ifDefined(_args.stateType)}"
											 icon-color="${ifDefined(_args.iconColor)}"
											 item-color="${ifDefined(_args.itemColor)}"
											 icon="${ifDefined(_args.icon)}"
											 size="${ifDefined(_args.size)}"
											 ?compressed="${ifDefined(_args.compressed)}"
											 ?readonly="${ifDefined(_args.readonly)}"
		>
			${ifDefined(_args.states)}
		</lvl-workflow-item>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		size: { table: { disable: true } },
		workflowId: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: 'empty' },
			},
			description: 'Id of the current Workflow',
		},
		workflowName: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: 'empty' },
			},
			description: 'Name of the current Workflow',
		},
		label: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: 'empty' },
			},
			description: 'State of the Workflow Item',
		},
		stateType: {
			control: 'select',
			description: 'default color scheme',
			options: [
				WorkflowStateType.Start,
				WorkflowStateType.InProgress,
				WorkflowStateType.Positive,
				WorkflowStateType.Negative,
			],
			table: {
				type: { summary: 'WorkflowStateType' },
				defaultValue: {
					summary: 'InProgress',
				},
			},
		},
		icon: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: 'empty' },
			},
			description: 'alternative Icon',
		},
		iconColor: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: 'empty' },
			},
			description: 'alternative color of the icon background',
		},
		itemColor: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: 'empty' },
			},
			description: 'alternative color of the workflow item',
		},
		compressed: {
			control: 'boolean',
			table: {
				type: { summary: 'boolean' },
				defaultValue: { summary: 'false' },
			},
			description: 'hide the label',
		},
		readonly: {
			control: 'boolean',
			table: {
				type: { summary: 'boolean' },
				defaultValue: { summary: 'false' },
			},
			description: 'hides the dropdown to change the workflow node',
		}
	},
	includeStories: /^[A-Z]/,
	args: {
		workflowId: '',
		workflowName: '',
		label: '',
		stateType: WorkflowStateType.InProgress,
		icon: '',
		iconColor: '',
		itemColor: '',
	}
}

export default meta

//#region Stories

/**
 * Appearance of a default workflow item
 */
export const Default: WorkflowItemStory = {
	args: {
		workflowName: 'Workflow Name',
		label: 'In Progress',
		stateType: WorkflowStateType.InProgress,
		states: html`
			<lvl-menu-item>In Progress</lvl-menu-item>
			<lvl-menu-item>Finished</lvl-menu-item>
		`
	}
}

/**
 * Appearance of a medium workflow item (which has no edit options)
 */
export const Medium: WorkflowItemStory = {
	args: {
		workflowName: 'Great workflow',
		label: 'Finished',
		icon: 'badge-check',
		stateType: WorkflowStateType.Positive,
		size: Size.Medium
	}
}

/**
 * Appearance of a small workflow item (which has no edit options)
 */
export const Small: WorkflowItemStory = {
	args: {
		workflowName: 'Great workflow',
		label: 'Finished',
		icon: 'badge-check',
		stateType: WorkflowStateType.Positive,
		size: Size.Small
	}
}

//#endregion

export const stories = {
	default: new LevelStory(meta, Default, "Default Story"),
	medium: new LevelStory(meta, Medium, "Medium Story"),
	small: new LevelStory(meta, Small, "Small Story"),
} as const