import type { <PERSON><PERSON>, <PERSON>Obj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { ifDefined } from 'lit/directives/if-defined.js'
import { LevelStory } from '@story-home/support/commands'
import { WorkflowStateIconType } from './WorkflowStateIcon'
import { WorkflowStateType } from '@/enums/workflow-state-type.ts'

import('./WorkflowStateIcon')

/* 
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

type WorkflowStateIconProperties = Partial<WorkflowStateIconType>
type WorkflowStateIconStory = Story<WorkflowStateIconProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-workflow-state-icon',
	tags: [ 'autodocs' ],
	render: (_args: WorkflowStateIconProperties) => html`
		<lvl-workflow-state-icon .workflows="${ifDefined(_args.workflows)}">
		</lvl-workflow-state-icon>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a default WorkflowStateIcon
 */
export const Default: WorkflowStateIconStory = {
	args: {
		workflows: [
			{ name: 'Internal process', nodeName: 'Created', stateType: WorkflowStateType.Start }
		]
	}
}

/**
 * Appearance of a default WorkflowStateIcon with two workflows
 */
export const Two: WorkflowStateIconStory = {
	args: {
		workflows: [
			{ name: 'Internal process', nodeName: 'Created', stateType: WorkflowStateType.InProgress },
			{ name: 'Admission', nodeName: 'Finished', stateType: WorkflowStateType.Positive }
		]
	}
}

/**
 * Appearance of a default WorkflowStateIcon with three workflows
 */
export const Three: WorkflowStateIconStory = {
	args: {
		workflows: [
			{ name: 'Internal process', nodeName: 'Created', stateType: WorkflowStateType.InProgress },
			{ name: 'Admission', nodeName: 'Finished', stateType: WorkflowStateType.Positive },
			{ name: 'External process', nodeName: 'Expired', stateType: WorkflowStateType.Negative }
		]
	}
}

//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
	two: new LevelStory(meta, Two),
	three: new LevelStory(meta, Three),
} as const