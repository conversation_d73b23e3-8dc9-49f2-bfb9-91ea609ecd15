import { ColorConverter } from '@/shared/color-converter-helper.ts'
import { ColorFormat } from '@/enums/color-format.ts'
import { Hsl, Rgb } from '@/shared/types.ts'

describe('tests for color converter', () => {
	it('converts to hex', () => {
		const red = '#FF0000'

		// @ts-ignore
		expect(ColorConverter.convert(null, ColorFormat.Hex), 'null -> null').to.be.null

		// color name
		expect(ColorConverter.convert('red', ColorFormat.Hex), 'red').to.equal(red)
		expect(ColorConverter.convert('blub', ColorFormat.Hex), 'blub').to.be.null

		// hex
		expect(ColorConverter.convert(red, ColorFormat.Hex), '#ff0000').to.equal(red)
		expect(ColorConverter.convert('#ii0000', ColorFormat.Hex), '#ii0000').to.be.null

		// hsl
		expect(ColorConverter.convert('hsl(0, 100%, 50%)', ColorFormat.Hex), 'hsl(0, 100%, 50%)').to.equal(red)
		expect(ColorConverter.convert({ hue: 0, saturation: 100, lightness: 50 }, ColorFormat.Hex), 'hsl(0, 100%, 50%)')
			.to
			.equal(red)
		expect(ColorConverter.convert('hsl(-5, 120, -5)', ColorFormat.Hex), 'hsl(-5, 120, -5)').to.be.null
		expect(ColorConverter.convert({
			hue: -5,
			saturation: 120,
			lightness: -5,
		}, ColorFormat.Hex), 'hsl(-5, 120, -5)').to.be.null

		// rgb
		expect(ColorConverter.convert('rgb(255, 0, 0)', ColorFormat.Hex), 'rgb(255, 0, 0)').to.equal(red)
		expect(ColorConverter.convert({ red: 255, green: 0, blue: 0 }, ColorFormat.Hex), 'rgb(255, 0, 0)').to.equal(red)
		expect(ColorConverter.convert('rgb(0, 256, 256)', ColorFormat.Hex), 'rgb(0, 256, 256)').to.be.null
		expect(ColorConverter.convert({
			hue: 0,
			saturation: 256,
			lightness: 256,
		}, ColorFormat.Hex), 'rgb(0, 256, 256)').to.be.null
	})

	it('converts to rgb', () => {
		const redString: string = 'rgb(255, 0, 0)'
		const red: Rgb = { red: 255, green: 0, blue: 0 }

		// @ts-ignore
		expect(ColorConverter.convert(null, ColorFormat.Rgb), 'null -> null').to.be.null

		// color name
		expect(ColorConverter.convert('red', ColorFormat.Rgb), 'red').to.eql(red)
		expect(ColorConverter.convert('red', ColorFormat.Rgb, true), 'red -> string').to.equal(redString)
		expect(ColorConverter.convert('blub', ColorFormat.Rgb), 'blub').to.be.null
		expect(ColorConverter.convert('blub', ColorFormat.Rgb, true), 'blub').to.equal('')

		// hex
		expect(ColorConverter.convert(redString, ColorFormat.Rgb), '#ff0000').to.eql(red)
		expect(ColorConverter.convert('#0af', ColorFormat.Rgb), '#0af').to.eql({ red: 0, green: 170, blue: 255 })
		expect(ColorConverter.convert(redString, ColorFormat.Rgb, true), '#ff0000 -> string').to.equal(redString)
		expect(ColorConverter.convert('#ii0000', ColorFormat.Rgb), '#ii0000').to.be.null

		// hsl
		expect(ColorConverter.convert('hsl(0, 100%, 50%)', ColorFormat.Rgb), 'hsl(0, 100%, 50%)').to.eql(red)
		expect(ColorConverter.convert('hsl(0, 100%, 50%)', ColorFormat.Rgb, true), 'hsl(0, 100%, 50%) -> red')
			.to
			.equal('rgb(255, 0, 0)')
		expect(ColorConverter.convert('hsl(60, 100%, 50%)', ColorFormat.Rgb, true), 'hsl(60, 100%, 50%) -> red')
			.to
			.equal('rgb(255, 255, 0)')
		expect(ColorConverter.convert('hsl(120,100%,50%)', ColorFormat.Rgb, true), 'hsl(120, 100%, 50%) -> green')
			.to
			.equal('rgb(0, 255, 0)')
		expect(ColorConverter.convert('hsl(180,100%,50%)', ColorFormat.Rgb, true), 'hsl(180, 100%, 50%) -> green')
			.to
			.equal('rgb(0, 255, 255)')
		expect(ColorConverter.convert('hsl(240,100%,50%)', ColorFormat.Rgb, true), 'hsl(240, 100%, 50%) -> blue')
			.to
			.equal('rgb(0, 0, 255)')
		expect(ColorConverter.convert('hsl(300,100%,50%)', ColorFormat.Rgb, true), 'hsl(300, 100%, 50%) -> blue')
			.to
			.equal('rgb(255, 0, 255)')
		expect(ColorConverter.convert({ hue: 0, saturation: 100, lightness: 50 }, ColorFormat.Rgb), 'hsl(0, 100%, 50%)')
			.to
			.eql(red)
		expect(ColorConverter.convert({
			hue: 0,
			saturation: 100,
			lightness: 50,
		}, ColorFormat.Rgb, true), 'hsl(0, 100%, 50%) -> string').to.equal(redString)
		expect(ColorConverter.convert('hsl(-5, 120, -5)', ColorFormat.Rgb), 'hsl(-5, 120, -5)').to.be.null
		expect(ColorConverter.convert({
			hue: -5,
			saturation: 120,
			lightness: -5,
		}, ColorFormat.Rgb), 'hsl(-5, 120, -5)').to.be.null

		// rgb
		expect(ColorConverter.convert('rgb(255, 0, 0)', ColorFormat.Rgb), 'rgb(255, 0, 0)').to.eql(red)
		expect(ColorConverter.convert('rgb(255, 0, 0)', ColorFormat.Rgb, true), 'rgb(255, 0, 0) -> string')
			.to
			.equal(redString)
		expect(ColorConverter.convert({ red: 255, green: 0, blue: 0 }, ColorFormat.Rgb), 'rgb(255, 0, 0)').to.eql(red)
		expect(ColorConverter.convert({ red: 255, green: 0, blue: 0 }, ColorFormat.Rgb, true), 'rgb(255, 0, 0) -> string')
			.to
			.equal(redString)
		expect(ColorConverter.convert('rgb(0, 256, 256)', ColorFormat.Rgb), 'rgb(0, 256, 256)').to.be.null
		expect(ColorConverter.convert({
			hue: 0,
			saturation: 256,
			lightness: 256,
		}, ColorFormat.Rgb), 'rgb(0, 256, 256)').to.be.null
	})

	it('converts to hsl', () => {
		const redString: string = 'hsl(0, 100%, 50%)'
		const red: Hsl = { hue: 0, saturation: 100, lightness: 50 }

		// @ts-ignore
		expect(ColorConverter.convert(null, ColorFormat.Hsl), 'null -> null').to.be.null
		expect(ColorConverter.convert('', ColorFormat.Hsl), '"" -> null').to.be.null

		// color name
		expect(ColorConverter.convert('red', ColorFormat.Hsl), 'red').to.eql(red)
		expect(ColorConverter.convert('red', ColorFormat.Hsl, true), 'red -> string').to.equal(redString)
		expect(ColorConverter.convert('blub', ColorFormat.Hsl), 'blub').to.be.null

		// hex
		expect(ColorConverter.convert(redString, ColorFormat.Hsl), '#ff0000').to.eql(red)
		expect(ColorConverter.convert(redString, ColorFormat.Hsl, true), '#ff0000 -> string').to.equal(redString)
		expect(ColorConverter.convert('#ii0000', ColorFormat.Hsl), '#ii0000').to.be.null

		// hsl
		expect(ColorConverter.convert('hsl(0, 100%, 50%)', ColorFormat.Hsl), 'hsl(0, 50%, 50%)').to.eql(red)
		expect(ColorConverter.convert('hsl(0, 100%, 50%)', ColorFormat.Hsl, true), 'hsl(0, 100%, 50%) -> string')
			.to
			.equal(redString)
		expect(ColorConverter.convert({ hue: 0, saturation: 100, lightness: 50 }, ColorFormat.Hsl), 'hsl(0, 100%, 50%)')
			.to
			.eql(red)
		expect(ColorConverter.convert({
			hue: 0,
			saturation: 100,
			lightness: 50,
		}, ColorFormat.Hsl, true), 'hsl(0, 100%, 50%) -> string').to.equal(redString)
		expect(ColorConverter.convert('hsl(-5, 120, -5)', ColorFormat.Hsl), 'hsl(-5, 120, -5)').to.be.null
		expect(ColorConverter.convert({
			hue: -5,
			saturation: 120,
			lightness: -5,
		}, ColorFormat.Hsl), 'hsl(-5, 120, -5)').to.be.null

		// rgb
		expect(ColorConverter.convert('rgb(255, 0, 0)', ColorFormat.Hsl), 'rgb(255, 0, 0)').to.eql(red)
		expect(ColorConverter.convert('rgb(255, 0, 0)', ColorFormat.Hsl, true), 'rgb(255, 0, 0) -> string')
			.to
			.equal(redString)
		expect(ColorConverter.convert({ red: 255, green: 0, blue: 0 }, ColorFormat.Hsl), 'rgb(255, 0, 0)').to.eql(red)
		expect(ColorConverter.convert({ red: 255, green: 0, blue: 0 }, ColorFormat.Hsl, true), 'rgb(255, 0, 0) -> string')
			.to
			.equal(redString)
		expect(ColorConverter.convert('rgb(0, 256, 256)', ColorFormat.Hsl), 'rgb(0, 256, 256)').to.be.null
		expect(ColorConverter.convert({
			hue: 0,
			saturation: 256,
			lightness: 256,
		}, ColorFormat.Hsl), 'rgb(0, 256, 256)').to.be.null
	})

	it('stringify colors', () => {
		// @ts-ignore
		expect(ColorConverter.stringify(null)).to.equal('')
		// @ts-ignore
		expect(ColorConverter.stringify(1)).to.equal('')

		// rgb
		expect(ColorConverter.stringify({ red: 0, green: 255, blue: 0 })).to.equal('rgb(0, 255, 0)')
		expect(ColorConverter.stringify({ red: -5, green: 255, blue: 0 })).to.equal('rgb(-5, 255, 0)')
		expect(ColorConverter.stringify({ red: 0, green: 256, blue: 0 })).to.equal('rgb(0, 256, 0)')
		
		// hsl
		expect(ColorConverter.stringify({ hue: 0, saturation: 100, lightness: 50 })).to.equal('hsl(0, 100%, 50%)')
		expect(ColorConverter.stringify({ hue: -5, saturation: 100, lightness: 50 })).to.equal('hsl(-5, 100%, 50%)')
		expect(ColorConverter.stringify({ hue: 0, saturation: 101, lightness: 50 })).to.equal('hsl(0, 101%, 50%)')
	})

	it('checks invalid inputs', () => {
		// @ts-ignore
		expect(ColorConverter.convert(0, ColorFormat.Hex), '0 -> null').to.be.null

		// @ts-ignore
		expect(ColorConverter.convert('#ffffff', null)).to.be.null
		// @ts-ignore
		expect(ColorConverter.convert('#ffffff', 'foo')).to.be.null

		// @ts-ignore
		expect(ColorConverter.convert('rgb(0,0,0)', null)).to.be.null
		// @ts-ignore
		expect(ColorConverter.convert('rgb(0,0,0)', 'foo')).to.be.null

		// @ts-ignore
		expect(ColorConverter.convert('hsl(0,0%,0%)', null)).to.be.null
		// @ts-ignore
		expect(ColorConverter.convert('hsl(0,0%,0%)', 'foo')).to.be.null
	})
})