export const enum FileType {
	Zip = 'zip',
	Pdf = 'pdf',
	Png = 'png',
	Jpg = 'jpg',
	Jpeg = 'jpeg',
	Eml = 'eml',
	Msg = 'msg',
	Csv = 'csv',
	Doc = 'doc',
	Docx = 'docx',
	Xls = 'xls',
	Xlsx = 'xlsx',
	Ppt = 'ppt',
	Pptx = 'pptx',
	Txt = 'txt',
}

export const enum FileSize {
	Byte = 'B',
	KiloByte = 'KB',
	MegaByte = 'MB',
	GigaByte = 'GB',
	TeraByte = 'TB',
}

export function getIconByType(fileType?: FileType, fileExists: boolean = true): string {
	if (!fileExists)
		return 'file-slash'

	let icon = 'file-circle-question'
	switch (fileType?.toLowerCase() ?? '') {
		case FileType.Zip:
			icon = 'file-zip'
			break
		case FileType.Pdf:
			icon = 'file-pdf'
			break
		case FileType.Png:
		case FileType.Jpg:
		case FileType.Jpeg:
			icon = 'image'
			break
		case FileType.Eml:
		case FileType.Msg:
			icon = 'envelope-open-text'
			break
		case FileType.Csv:
			icon = 'file-csv'
			break
		case FileType.Doc:
		case FileType.Docx:
			icon = 'file-word'
			break
		case FileType.Xls:
		case FileType.Xlsx:
			icon = 'file-excel'
			break
		case FileType.Ppt:
		case FileType.Pptx:
			icon = 'file-powerpoint'
			break
		case FileType.Txt:
			icon = 'file-lines'
			break
	}

	return icon
}

export function parseFileSize(size?: number): string {
	if (size) {
		let unit = FileSize.Byte
		if (size > 100) {
			size /= 1000
			unit = FileSize.KiloByte
		}
		if (size > 100) {
			size /= 1000
			unit = FileSize.MegaByte
		}
		if (size > 100) {
			size /= 1000
			unit = FileSize.GigaByte
		}
		if (size > 100) {
			size /= 1000
			unit = FileSize.TeraByte
		}

		return `${Math.round(size * 10) / 10} ${unit}`
	}
	return '-'
}