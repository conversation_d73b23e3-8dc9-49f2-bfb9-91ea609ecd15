export const enum Size {
	Small = 'small',
	Medium = 'medium',
	Large = 'large'
}

// Convert string to enum
export function toSizeType(value: string | null | undefined): Size | null {
	if (!value)
		return null

	switch (value.toLowerCase()) {
		case 'small':
			return Size.Small
		case 'medium':
			return Size.Medium
		case 'large':
			return Size.Large

			// something unknow -> warning
		default:
			console.warn(`unable to parse: ${value}`)
			return null
	}
}

export function getSizeClass(type: Size) {
	return `size-${type.toString()}`
}

export function getFontSize(type: Size) {
	switch (type.toString()) {
		case Size.Small:
			return '1rem'
		case Size.Medium:
			return '1.5rem'
		case Size.Large:
			return '2rem'
			// something unknown -> warning
		default:
			console.warn(`unable to determine font-size for ${type}`)
			return '0'
	}
}


