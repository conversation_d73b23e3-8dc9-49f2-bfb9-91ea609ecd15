class LevelComponent {

	tag: string

	private readonly importFunction: Function

	private dependencies?: LevelComponent[]

	private _imported: boolean = false

	public get imported() {
		return this._imported
	}

	constructor(tag: string, importFunction: Function, dependencies: LevelComponent[] = []) {
		this.tag = tag
		this.importFunction = importFunction
		this.dependencies = dependencies
	}

	/**
	 * import the component including all of its dependencies
	 */
	importComponent() {
		// only import once
		if (this.imported)
			return

		// import dependencies if needed
		this.dependencies?.forEach((dependency: LevelComponent) => dependency.importComponent())

		// component
		this.importFunction()
		this._imported = true
	}
}

const webComponentsRoot = document.querySelector('body')

// list of all usable components
const fabComponent = new LevelComponent('lvl-fab', () => import('./components/atomics/floating-action-button/FloatingActionButton.ts'))
const buttonComponent = new LevelComponent('lvl-button', () => import('./components/atomics/button/Button.ts'))
const buttonGroupComponent = new LevelComponent('lvl-button-group', () => import('./components/button-group/ButtonGroup.ts'),
	[ buttonComponent ])
const chipComponent = new LevelComponent('lvl-chip', () => import('./components/atomics/chip/Chip.ts'))

/* popup components */
const popupComponent = new LevelComponent('lvl-popup', () => import('./components/popup/Popup.ts'))

/* input subcomponents*/
const inputButtonComponent = new LevelComponent('lvl-input-button', () => import('./components/inputs/InputButton.ts'))
const inputIconComponent = new LevelComponent('lvl-input-icon', () => import('./components/inputs/InputIcon.ts'))
const inputTooltipComponent = new LevelComponent('lvl-tooltip', () => import('./components/atomics/tooltip/Tooltip.ts'),
	[ popupComponent ])
const colorPickerComponent = new LevelComponent('lvl-color-picker', () => import('./components/color-picker/ColorPicker.ts'), [ popupComponent ])
const dateTimePickerComponent = new LevelComponent('lvl-date-time-picker', () => import('./components/date-time-picker/DateTimePicker.ts'), [ popupComponent ])

/* dropdown components*/
const menuComponent = new LevelComponent('lvl-menu', () => import('./components/dropdown/DropdownMenu.ts'))
const menuDividerComponent = new LevelComponent('lvl-menu-divider', () => import('./components/dropdown/DropdownMenuDivider.ts'))
const menuItemComponent = new LevelComponent('lvl-menu-item', () => import('./components/dropdown/DropdownMenuItem.ts'))
const dropdownComponent = new LevelComponent('lvl-dropdown', () => import('./components/dropdown/Dropdown.ts'),
	[ menuComponent, menuDividerComponent, menuItemComponent, popupComponent, inputTooltipComponent ])

/* table component without inputs */
const fryComponent = new LevelComponent('lvl-fry', () => import('./components/atomics/fry/Fry.ts'))
const tableColumnDataComponent = new LevelComponent('lvl-table-data-column', () => import('./components/list-views/multi-data-view/table/TableDataColumn.ts'), [
	fryComponent
])
const tableColumnThumbnailComponent = new LevelComponent('lvl-table-thumbnail-column', () => import('./components/list-views/multi-data-view/table/TableThumbnailColumn.ts'))
const tableComponent = new LevelComponent('lvl-table', () => import('./components/list-views/multi-data-view/table/Table.ts'), [
	tableColumnDataComponent,
	tableColumnThumbnailComponent
])

const thumbnailComponent = new LevelComponent('lvl-thumbnail', () => import('./components/atomics/thumbnail/Thumbnail.ts'))

/* input components */
const toggleComponent = new LevelComponent('lvl-toggle', () => import('./components/inputs/toggle/Toggle.ts'))
const textAreaComponent = new LevelComponent('lvl-textarea', () => import('./components/inputs/textarea/Textarea.ts'), [
	inputButtonComponent, inputIconComponent, inputTooltipComponent,
])
const checkBoxComponent = new LevelComponent('lvl-checkbox', () => import('./components/inputs/checkbox/Checkbox.ts'), [
	inputButtonComponent, inputIconComponent, inputTooltipComponent,
])
const checkboxGroupComponent = new LevelComponent('lvl-checkbox-group', () => import('./components/checkbox-group/CheckboxGroup.ts'),
	[ checkBoxComponent ])
const autocompleteComponent = new LevelComponent('lvl-autocomplete', () => import('./components/inputs/autocomplete/Autocomplete.ts'), [
	inputButtonComponent, inputIconComponent, inputTooltipComponent, dropdownComponent,
])
const dialogComponent = new LevelComponent('lvl-dialog', () => import('./components/dialog/Dialog.ts'))
const inputComponent = new LevelComponent('lvl-input', () => import('./components/inputs/input/Input.ts'), [
	inputButtonComponent, inputIconComponent, inputTooltipComponent, dropdownComponent, colorPickerComponent,	dateTimePickerComponent, dialogComponent,
	autocompleteComponent, tableComponent, textAreaComponent, fryComponent
])
const richTextComponent = new LevelComponent('lvl-rich-text', () => import('./components/inputs/rich-text/RichText.ts'), [
	inputButtonComponent, inputIconComponent, inputTooltipComponent, dialogComponent, dropdownComponent, colorPickerComponent, buttonComponent
])

/* navigation components */
const sideNavItemComponent = new LevelComponent('lvl-side-nav-item', () => import('./components/navigation/side-nav/SideNavItem.ts'))
const sideNavInfoComponent = new LevelComponent('lvl-side-nav-info', () => import('./components/navigation/side-nav/SideNavInfo.ts'))
const sideNavComponent = new LevelComponent('lvl-side-nav', () => import('./components/navigation/side-nav/SideNav.ts'), [
	sideNavItemComponent, sideNavInfoComponent,
])
const breadcrumbComponent = new LevelComponent('lvl-breadcrumb', () => import('./components/navigation/breadcrumb/Breadcrumb.ts'), [
	dropdownComponent, inputTooltipComponent,
])
const tabComponent = new LevelComponent('lvl-tab', () => import('./components/navigation/tab-bar/Tab.ts'))
const tabBarComponent = new LevelComponent('lvl-tab-bar', () => import('./components/navigation/tab-bar/TabBar.ts'), [
	sideNavItemComponent, sideNavInfoComponent, tabComponent,
])

/* organizer components */
const searchFieldMenuComponent = new LevelComponent('lvl-search-field-menu', () => import('./components/data-organizers/search-field-menu/SearchFieldMenu.ts'))
const listFilterComponent = new LevelComponent('lvl-list-filter', () => import('./components/data-organizers/list-filter/ListFilter.ts'), [ searchFieldMenuComponent ])
const sectionComponent = new LevelComponent('lvl-section', () => import('./components/data-organizers/section/Section.ts'))

/* enumeration components with input */
const dataListComponent = new LevelComponent('lvl-data-list', () => import('./components/list-views/data-list/DataList.ts'), [
	buttonComponent, toggleComponent, inputComponent, listFilterComponent, chipComponent,
])

const listLineItemComponent = new LevelComponent('lvl-list-line-item', () => import('./components/list-views/data-list/ListLineItem.ts'), [
	toggleComponent,
])

const listLineComponent = new LevelComponent('lvl-list-line', () => import('./components/list-views/data-list/ListLine.ts'), [
	listLineItemComponent,
])

const listColumnDataComponent = new LevelComponent('lvl-list-data-column', () => import('./components/list-views/data-list/ListDataColumn.ts'))
const listColumnComponent = new LevelComponent('lvl-list-data-column', () => import('./components/list-views/data-list/ListColumn.ts'))

const listComponent = new LevelComponent('lvl-list', () => import('./components/list-views/data-list/List.ts'), [
	listLineComponent, listColumnDataComponent, listColumnComponent,
])

const queryViewNavigationComponent = new LevelComponent('lvl-query-view-navigation', () => import('./components/list-views/QueryViewNavigation.ts'), [])
const queryViewActionBarComponent = new LevelComponent('lvl-query-view-action-bar', () => import('./components/list-views/action-bar/QueryViewActionBar.ts'), [])

const enumerationComponent = new LevelComponent('lvl-enumeration', () => import('./components/list-views/Enumeration.ts'), [
	buttonComponent, listFilterComponent, chipComponent, queryViewNavigationComponent,
])

const multiDataViewComponent = new LevelComponent('lvl-multi-data-view', () => import('./components/list-views/multi-data-view/MultiDataView.ts'), [
	tableComponent, queryViewNavigationComponent,
])

/* workflows */
const workflowItemComponent = new LevelComponent('lvl-workflow-item', () => import('./components/workflows/WorkflowItem.ts'), [buttonComponent, dropdownComponent, menuComponent, menuItemComponent])

/* wrapper */
const sliderComponent = new LevelComponent('lvl-slide-out', () => import('./components/slide-out/SlideOut.ts'))
const toasterComponent = new LevelComponent('lvl-toaster', () => import('./components/toaster/Toaster.ts'), [ buttonComponent ])

/* value formatter */
const valueFormatterComponent = new LevelComponent('lvl-value-formatter', () => import('./components/atomics/value-formatter/ValueFormatter.ts'))

/* form */
const formComponent = new LevelComponent('lvl-form', () => import('./components/form/Form.ts'))

/* page designer */
const pageDesignerComponent = new LevelComponent('lvl-page-designer', () => import('./components/individual/page-designer/PageDesigner.ts'), [ buttonGroupComponent, tabBarComponent ])

/* grid */
const gridComponent = new LevelComponent('lvl-grid', () => import('./components/individual/grid/Grid.ts'))
const gridElementComponent = new LevelComponent('lvl-grid-element', () => import('./components/individual/grid-element/GridElement.ts'))

/* viewers */
const pinComponent = new LevelComponent('lvl-pin', () => import('./components/atomics/pin/Pin.ts'))
const pinSourceComponent = new LevelComponent('lvl-pin-source', () => import('./components/atomics/pin-source/PinSource.ts'))
const blueprintViewerComponent = new LevelComponent('lvl-blueprint', () => import('./components/atomics/blueprint-viewer/BlueprintViewer.ts'), [pinComponent, pinSourceComponent])
const viewerComponent = new LevelComponent('lvl-viewer', () => import('./components/atomics/viewer/Viewer.ts'), [blueprintViewerComponent])

/* overlay */
const overlayComponent = new LevelComponent('lvl-overlay', () => import('./components/overlay/Overlay.ts'))

let webComponentImports = new Array<LevelComponent>(
	fabComponent, buttonComponent, buttonGroupComponent, toggleComponent, inputComponent, textAreaComponent, richTextComponent, checkBoxComponent, checkboxGroupComponent, autocompleteComponent,
	dataListComponent, listComponent, enumerationComponent, multiDataViewComponent, sliderComponent, sideNavComponent, dropdownComponent, toasterComponent, breadcrumbComponent,
	tabBarComponent, valueFormatterComponent, formComponent, listFilterComponent, chipComponent, gridComponent, gridElementComponent, dialogComponent, pageDesignerComponent, 
	queryViewActionBarComponent, sectionComponent, overlayComponent, fryComponent, thumbnailComponent, viewerComponent, workflowItemComponent
)

//#region public functions

export function initComponentObserver() {
	// Callback function to execute when mutations are observed
	const webComponentAppendCallback = (mutationList: MutationRecord[]) => {
		for (const mutation of mutationList) {
			if (mutation.type === 'childList') {
				checkForNewWebComponents(mutation)
			}
		}
	}

	// Create an observer instance linked to the callback function
	const observer = new MutationObserver(webComponentAppendCallback)

	// Start observing the target node for configured mutations
	if (webComponentsRoot)
		observer.observe(webComponentsRoot, { childList: true, subtree: true })
	checkForNewWebComponents()
}

//#endregion

//#region private functions

// looking for all web components which are added to the dom AND not currently imported via modules
function checkForNewWebComponents(mutation?: MutationRecord) {
	const isNewWebComponentInMutation = (mutation: MutationRecord | null, webComponent: LevelComponent, importFoundWebComponents: boolean) => {
		const nodeList = mutation ? mutation?.addedNodes : webComponentsRoot?.childNodes
		if (!nodeList || nodeList.length === 0)
			return false

		// are there any new web components
		const someWebComponentsFound = Object.values(nodeList)
																				 .some(addedNode => addedNode.parentElement?.querySelector(webComponent.tag))
		if (!someWebComponentsFound)
			return false

		// import found components?
		if (importFoundWebComponents) {
			webComponent.importComponent()
		}
		return true
	}

	// exclude all imported webcomponents
	webComponentImports = webComponentImports.filter(importMapping => !isNewWebComponentInMutation(mutation ?? null, importMapping, true))
}

//#endregion
