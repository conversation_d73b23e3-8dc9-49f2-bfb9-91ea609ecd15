import { html } from 'lit'
import { spread } from '@open-wc/lit-helpers'
import { ifDefined } from 'lit-html/directives/if-defined.js'
import { v4 as uuid } from 'uuid'
import {IconStyle} from "@/enums/icon-style.ts";

export type IconOptions = Partial<{
	iconStyle: IconStyle
	cssStyle: string
	additionalClasses: Array<string>
	data: Record<string, string>
	toggleEnabled: boolean
	onClick: (event: MouseEvent) => void
	title: string
	inline: boolean
	disabled: boolean
	animate: boolean
}>

export const WEBVIEWER_LIBRARY_LINK: string = `${Object.keys(window).includes('useCypress') ? '.' : ''}/libs/webviewer`

const DEFAULT_STYLE = IconStyle.Light
export const getIconClass = (name: string, options?: IconOptions) => {
	let iconStyle: string = (options?.iconStyle ?? DEFAULT_STYLE).toString()
	if(name.includes('/')) {
		[ name, iconStyle ] = name.split('/')
	}
	const styleClassName = iconStyle.startsWith('fa-') ? iconStyle : `fa-${iconStyle}`
	const iconClassName = name.startsWith('fa-') ? name : `fa-${name}`
	const fixedWidth = options?.inline === true ? '' : 'icon--fixed-width'
	return `icon ${fixedWidth} ${styleClassName} ${iconClassName}`
}

export const renderIcon = (iconCss: string, options?: IconOptions) => {
	
	const handleClick = (event: MouseEvent) => {
		if (options?.toggleEnabled) {
			const icon = event.target as HTMLElement
			icon.classList.toggle('icon--off')
		}
		if (options?.onClick)
			options.onClick(event)
	}
	
	const dataAttributes = () => {
		const dataAttributes: Record<string, string> = {}
		if(!options?.data)
			return { }
		
		Object.keys(options.data).map(attributeKey => {
			dataAttributes[`data-${attributeKey}`] = options.data![attributeKey]
		})
		return dataAttributes
	}

	const tooltipName = `icon-tooltip-${uuid()}`
	const getTooltip = (title: string) => html`
		<lvl-tooltip name="${tooltipName}" placement="bottom-start">${title}</lvl-tooltip>
	`

	return html`
		<i data-tooltip="${ifDefined(options?.title ? tooltipName : undefined)}"
			 class="${getIconClass(iconCss, options)} ${options?.animate ? 'fa-spin' : ''} ${options?.additionalClasses?.join(' ')} ${options?.onClick ? 'clickable' : ''} ${options?.disabled ? 'disabled' : ''}"
			${spread(dataAttributes())}
			 style="${options?.cssStyle}" @click="${handleClick}">
		</i>
		${options?.title ? getTooltip(options.title) : ''}
	`
}