import { css } from 'lit'

/**
 * General styling for all components
 */
export const base = css`
	*, *::before, *::after {
		box-sizing: border-box;
	}

	:host {
		--size-text-xxs: 0.6rem;
		--size-text-xs: 0.8rem;
		--size-text-s: 1rem;
		--size-text-m: 1.2rem;
		--size-text-l: 1.6rem;
		--size-text-xl: 2.4rem;
		--size-text-xxl: 3.2rem;
		--size-text-xxxl: 4.8rem;

		--size-radius-s: 0.2rem;
		--size-radius-m: 0.4rem;
		--size-radius-l: 0.8rem;
		--size-radius-xl: 1.6rem;
		--size-radius-circle: 50%; 
		--size-radius-pill: 100rem;
		
		--size-spacing-xxs: 0.1rem;
		--size-spacing-xs: 0.2rem;
		--size-spacing-s: 0.4rem;
		--size-spacing-m: 0.8rem;
		--size-spacing-l: 1.6rem;
		--size-spacing-xl: 2.4rem;
		--size-spacing-xxl: 3.2rem;
		--size-spacing-xxxl: 4rem;

		font-size: var(--size-text-m);
		font-family: Roboto, system-ui, Helvetica, sans-serif;
		color: var(--cp-clr-text-primary-positiv);
		
		--font-family-default: Roboto, system-ui, Helvetica, sans-serif;
		--cp-header-height: 4rem;
	}

	/* style different html tags */

	:not(:defined) {
		display: none;
	}

	input {
		color-scheme: var(--color-scheme, var(--cp-color-scheme));
	}

	h1, h2, h3, h4, h5, h6 {
		font-size: 2em;
		margin: 0;
		font-weight: bold;
	}

	ul, menu, li {
		list-style-type: none;
		margin: 0;
		padding-left: 0;
	}
	
	a {
		color: unset;
		text-decoration: unset;
	}

	p {
		margin: 0;
		line-height: 150%;
		white-space: normal;
	}
	
	dialog {
		color: inherit;
	}
	
	[popover] {
		padding: 0;
		border: 0;
		margin: unset;
		color: unset;
		background-color: unset;
	}

	.clickable:not(.disabled) {
		cursor: pointer;
	}
	
	.hide, .hidden {
		display: none !important;
	}

	.disabled {
		color: var(--cp-clr-state-inactive);
		user-select: none;
		pointer-events: none;
	}

	.flex--centered {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.text--secondary {
		color: var(--cp-clr-text-secondary);
	}
	
	.text--tertiary {
		color: var(--cp-clr-text-tertiary);
	}

	[data-state=error] {
		--state-color: var(--cp-clr-signal-error)
	}

	[data-state=error-light] {
		--state-color: var(--cp-clr-signal-error-light)
	}

	[data-state=warning] {
		--state-color: var(--cp-clr-signal-warning)
	}

	[data-state=warning-light] {
		--state-color: var(--cp-clr-signal-warning-light)
	}

	[data-state=success] {
		--state-color: var(--cp-clr-signal-success)
	}

	[data-state=success-light] {
		--state-color: var(--cp-clr-signal-success-light)
	}

	[data-state=info] {
		--state-color: var(--cp-clr-signal-info)
	}

	[data-state=info-light] {
		--state-color: var(--cp-clr-signal-info-light)
	}

	[data-state=active] {
		--state-color: var(--cp-clr-state-active)
	}

	[data-state=inactive] {
		--state-color: var(--cp-clr-state-inactive)
	}

	[data-state=selected] {
		--state-color: var(--cp-clr-state-selected)
	}

`

/**
 *  Usable color palette 
 **/
export const color = css`
	
	/* ! DO NOT USE UNCATEGORIZED COLORS - SEE CATEGORIZED COLORS BELOW */
	:host, :root {
		/* brand */
		--clr-levelbuild-teal: #00BFCA;
		--clr-levelbuild-purple: #B04FE1;
		--clr-levelbuild-blue: #0097FD;
		--clr-levelbuild-charcoal: #444444;
		--clr-mainka-red: #E53012;
		--clr-bickhardtbau-yellow: #FFD600;
		--clr-jaegergruppe-blue: #2B68AE;
		--clr-spitzke-blue: #053CC4;
		--clr-demmelhuber-gold: #A18C61;

		/* black to white */
		--clr-black: black;
		--clr-black-950: #080A0C;
		--clr-black-900: #141B1F;
		--clr-black-800: #202A31;
		--clr-black-700: #354550;
		--clr-black-600: #455A68;
		--clr-black-500: #5E7A8D;
		--clr-black-400: #94A9B7;
		--clr-black-300: #AFBFCA;
		--clr-black-200: #CED8DE;
		--clr-black-100: #EDF0F3;
		--clr-black-50: #FAFAFA;
		--clr-white: white;

		/* blue */
		--clr-blue-950: #001E33;
		--clr-blue-900: #002E4D;
		--clr-blue-800: #003D66;
		--clr-blue-700: #005B99;
		--clr-blue-600: #007ACC;
		--clr-blue-500: #0097FD;
		--clr-blue-400: #33ADFF;
		--clr-blue-300: #66C1FF;
		--clr-blue-200: #99D6FF;
		--clr-blue-100: #CCEAFF;
		--clr-blue-50: #E5F5FF;

		/* orange */
		--clr-orange-950: #431407;
		--clr-orange-900: #7C2D12;
		--clr-orange-800: #9A3412;
		--clr-orange-700: #C2410C;
		--clr-orange-600: #EA580C;
		--clr-orange-500: #F97316;
		--clr-orange-400: #FB923C;
		--clr-orange-300: #FDBA74;
		--clr-orange-200: #FED7AA;
		--clr-orange-100: #FFEDD5;
		--clr-orange-50: #FFF7ED;

		/* yellow */
		--clr-yellow-950: #22202A;
		--clr-yellow-900: #713F12;
		--clr-yellow-800: #854D0E;
		--clr-yellow-700: #A16207;
		--clr-yellow-600: #CA8A04;
		--clr-yellow-500: #EAB308;
		--clr-yellow-400: #FACC15;
		--clr-yellow-300: #FDE047;
		--clr-yellow-200: #F2EBAA;
		--clr-yellow-100: #FEF9C3;
		--clr-yellow-50: #FFFAE8;

		/* green */
		--clr-green-950: #203D14;
		--clr-green-900: #305C1F;
		--clr-green-800: #407A29;
		--clr-green-700: #509933;
		--clr-green-600: #60B83D;
		--clr-green-500: #76C757;
		--clr-green-400: #8FD175;
		--clr-green-300: #A2D98C;
		--clr-green-200: #C1E6B3;
		--clr-green-100: #DAF0D1;
		--clr-green-50: #F3FAF0;
		
		/* red */
		--clr-red-950: #250E0E;
		--clr-red-900: #7F1D1D;
		--clr-red-800: #991B1B;
		--clr-red-700: #B91C1C;
		--clr-red-600: #DC2626;
		--clr-red-500: #DE5858;
		--clr-red-400: #F87171;
		--clr-red-300: #FCABCB;
		--clr-red-200: #F8CBCB;
		--clr-red-100: #FEE2E2;
		--clr-red-50: #FCE9E9;

		/* teal */
		--clr-teal-950: #030B18;
		--clr-teal-900: #022F3C;
		--clr-teal-800: #02535F;
		--clr-teal-700: #017783;
		--clr-teal-600: #01A6B1;
		--clr-teal-500: #00BFCA;
		--clr-teal-400: #2CC9D3;
		--clr-teal-300: #60D5DF;
		--clr-teal-200: #8FDFE9;
		--clr-teal-100: #BFEAF4;
		--clr-teal-50: #EEFFFF;

		/* purple */
		--clr-purple-950: #120718;
		--clr-purple-900: #321540;
		--clr-purple-800: #512468;
		--clr-purple-700: #713291;
		--clr-purple-600: #9041B9;
		--clr-purple-500: #B04FE1;
		--clr-purple-400: #BF70E7;
		--clr-purple-300: #CE99EC;
		--clr-purple-200: #DCB1F2;
		--clr-purple-100: #EBD1F7;
		--clr-purple-50: #FAF2FD;

		/* transparent */
		--clr-transparent-black-10: hsla(0, 0%, 0%, 0.1);
		--clr-transparent-black-15: hsla(0, 0%, 0%, 0.15);
		--clr-transparent-black-25: hsla(0, 0%, 0%, 0.25);
		--clr-transparent-black-35: hsla(0, 0%, 0%, 0.35);

		--clr-transparent-white-10: hsla(0, 100%, 100%, 0.1);
		--clr-transparent-white-15: hsla(0, 100%, 100%, 0.15);
		--clr-transparent-white-25: hsla(0, 100%, 100%, 0.25);
		--clr-transparent-white-35: hsla(0, 100%, 100%, 0.35);
	}

	/* Light mode */
	@media (prefers-color-scheme: light) {

		:host, :root {
			--cp-color-scheme: light;

			/* text */
			--cp-clr-text-primary-positiv: var(--clr-text-primary-positiv, var(--clr-black-800));
			--cp-clr-text-primary-negativ: var(--clr-text-primary-negativ, var(--clr-white));
			--cp-clr-text-secondary: var(--clr-text-secondary, var(--clr-black-500));
			--cp-clr-text-tertiary: var(--clr-text-tertiary, var(--clr-black-400));
			--cp-clr-text-overlay: var(--clr-text-overlay, var(--clr-white));

			/* signal */
			--cp-clr-signal-error: var(--clr-signal-error, var(--clr-red-500));
			--cp-clr-signal-error-medium: var(--clr-signal-error-medium, var(--clr-red-200));
			--cp-clr-signal-error-light: var(--clr-signal-error-light, var(--clr-red-50));
			--cp-clr-signal-error-hover: var(--clr-signal-error-hover, var(--clr-red-600));
			--cp-clr-signal-warning: var(--clr-signal-warning, var(--clr-orange-600));
			--cp-clr-signal-warning-medium: var(--clr-signal-warning-medium, var(--clr-orange-200));
			--cp-clr-signal-warning-light: var(--clr-signal-warning-light, var(--clr-orange-50));
			--cp-clr-signal-success: var(--clr-signal-success, var(--clr-green-500));
			--cp-clr-signal-success-medium: var(--clr-signal-success-medium, var(--clr-green-200));
			--cp-clr-signal-success-light: var(--clr-signal-success-light, var(--clr-green-50));
			--cp-clr-signal-success-hover: var(--clr-signal-success-hover, var(--clr-green-600));
			--cp-clr-signal-info: var(--clr-signal-info, var(--clr-blue-600));
			--cp-clr-signal-info-medium: var(--clr-signal-info-medium, var(--clr-blue-200));
			--cp-clr-signal-info-light: var(--clr-signal-info-light, var(--clr-blue-50));
			--cp-clr-signal-favorite: var(--clr-signal-favorite, var(--clr-yellow-500));
			--cp-clr-signal-favorite-hover: var(--clr-signal-favorite-hover, var(--clr-yellow-400));

			/* state */
			--cp-clr-state-active: var(--clr-state-active, var(--clr-blue-600));
			--cp-clr-state-active-hover: var(--clr-state-active-hover, var(--clr-blue-700));
			--cp-clr-state-primary: var(--clr-state-primary, var(--clr-blue-600));
			--cp-clr-state-primary-hover: var(--clr-state-primary-hover, var(--clr-blue-700));
			--cp-clr-state-hover: var(--clr-state-hover, var(--clr-transparent-black-10));
			--cp-clr-state-selected: var(--clr-state-selected, var(--clr-transparent-black-15));
			--cp-clr-state-enabled: var(--clr-state-enabled, var(--clr-green-500));
			--cp-clr-state-focus: var(--clr-state-focus, var(--clr-blue-300));
			--cp-clr-state-readonly: var(--clr-state-readonly, var(--clr-black-100));
			--cp-clr-state-inactive: var(--clr-state-inactive, var(--clr-black-200));
			--cp-clr-state-inactive-hover: var(--clr-state-inactive-hover, var(--clr-black-300));
			
			/* feed */
			--cp-clr-feed-system: var(--clr-feed-system, var(--clr-black-200));
			--cp-clr-feed-neutral: var(--clr-feed-neutral, var(--clr-blue-100));
			--cp-clr-feed-completed: var(--clr-feed-completed, var(--clr-green-100));
			--cp-clr-feed-attention: var(--clr-feed-attention, var(--clr-orange-300));
			--cp-clr-feed-warning: var(--clr-feed-warning, var(--clr-red-400));

			/* background */
			--cp-clr-background-lvl-0: var(--clr-background-lvl-0, var(--clr-white));
			--cp-clr-background-lvl-1: var(--clr-background-lvl-1, var(--clr-black-100));
			--cp-clr-background-lvl-2: var(--clr-background-lvl-2, var(--clr-black-200));
			--cp-clr-background-lvl-3: var(--clr-background-lvl-3, var(--clr-black-300));
			--cp-clr-background-lvl-4: var(--clr-background-lvl-4, var(--clr-black-400));
			--cp-clr-background-lvl-0-tooltip: var(--clr-background-lvl-0-tooltip, var(--clr-black-700));
			--cp-clr-background-viewer: var(--clr-background-viewer, #E3E6E8);
			--cp-clr-background-dialog: var(--clr-background-dialog, var(--clr-transparent-black-50));

			/* border */
			--cp-clr-border-white: var(--clr-border-white, var(--clr-white));
			--cp-clr-border-weak: var(--clr-border-weak, var(--clr-black-100));
			--cp-clr-border-medium: var(--clr-border-medium, var(--clr-black-300));
			--cp-clr-border-strong: var(--clr-border-strong, var(--clr-black-500));
			--cp-clr-input-border-active: var(--clr-input-border-active, var(--clr-blue-500));

			/* shadow */
			--cp-clr-shadow-weak: var(--clr-shadow-weak, var(--clr-transparent-black-15));
			--cp-clr-shadow-medium: var(--clr-shadow-medium, var(--clr-transparent-black-25));
			--cp-clr-shadow-strong: var(--clr-shadow-strong, var(--clr-transparent-black-35));

			/* hover */
			--cp-clr-hover-blue: var(--clr-hover-blue, hsl(204, 100%, 40%, 0.1));
			--cp-clr-hover-red: var(--clr-hover-red, hsl(0, 67%, 61%, 0.1));
			--cp-clr-hover-green: var(--clr-hover-green, hsl(142, 76%, 36%, 0.1));
			--cp-clr-hover-grey: var(--clr-hover-grey, hsl(0, 0%, 0%, 0.1));
		}
	}

	/* Dark mode */
	@media (prefers-color-scheme: dark) {

		:host, :root {
			--cp-color-scheme: dark;

			/* text */
			--cp-clr-text-primary-positiv: var(--clr-text-primary-positiv, var(--clr-black-300));
			--cp-clr-text-primary-negativ: var(--clr-text-primary-negativ, var(--clr-black-900));
			--cp-clr-text-secondary: var(--clr-text-secondary, var(--clr-black-500));
			--cp-clr-text-tertiary: var(--clr-text-tertiary, var(--clr-black-700));
			--cp-clr-text-overlay: var(--clr-text-overlay, var(--clr-white));

			/* signal */
			--cp-clr-signal-error: var(--clr-signal-error, var(--clr-red-400));
			--cp-clr-signal-error-medium: var(--clr-signal-error-medium, var(--clr-red-900));
			--cp-clr-signal-error-light: var(--clr-signal-error-light, var(--clr-red-950));
			--cp-clr-signal-error-hover: var(--clr-signal-error-hover, var(--clr-red-300));
			--cp-clr-signal-warning: var(--clr-signal-warning, var(--clr-orange-600));
			--cp-clr-signal-warning-medium: var(--clr-signal-warning-medium, var(--clr-orange-900));
			--cp-clr-signal-warning-light: var(--clr-signal-warning-light, var(--clr-orange-950));
			--cp-clr-signal-success: var(--clr-signal-success, var(--clr-green-400));
			--cp-clr-signal-success-medium: var(--clr-signal-success-medium, var(--clr-green-900));
			--cp-clr-signal-success-light: var(--clr-signal-success-light, var(--clr-green-950));
			--cp-clr-signal-success-hover: var(--clr-signal-success-hover, var(--clr-green-300));
			--cp-clr-signal-info: var(--clr-signal-info, var(--clr-blue-600));
			--cp-clr-signal-info-medium: var(--clr-signal-info-medium, var(--clr-blue-900));
			--cp-clr-signal-info-light: var(--clr-signal-info-light, var(--clr-blue-950));
			--cp-clr-signal-favorite: var(--clr-signal-favorite, var(--clr-yellow-400));
			--cp-clr-signal-favorite-hover: var(--clr-signal-favorite-hover, var(--clr-yellow-500));

			/* state */
			--cp-clr-state-active: var(--clr-state-active, var(--clr-blue-600));
			--cp-clr-state-active-hover: var(--clr-state-active-hover, var(--clr-blue-500));
			--cp-clr-state-primary: var(--clr-state-primary, var(--clr-blue-600));
			--cp-clr-state-primary-hover: var(--clr-state-primary-hover, var(--clr-blue-500));
			--cp-clr-state-hover: var(--clr-state-hover, var(--clr-transparent-white-10));
			--cp-clr-state-selected: var(--clr-state-selected, var(--clr-transparent-white-15));
			--cp-clr-state-enabled: var(--clr-state-enabled, var(--clr-green-700));
			--cp-clr-state-focus: var(--clr-state-focus, var(--clr-blue-400));
			--cp-clr-state-readonly: var(--clr-state-readonly, var(--clr-black-800));
			--cp-clr-state-inactive: var(--clr-state-inactive, var(--clr-black-700));
			--cp-clr-state-inactive-hover: var(--clr-state-inactive-hover, var(--clr-black-600));

			/* feed */
			--cp-clr-feed-system: var(--clr-feed-system, var(--clr-black-600));
			--cp-clr-feed-neutral: var(--clr-feed-neutral, var(--clr-blue-800));
			--cp-clr-feed-completed: var(--clr-feed-completed, var(--clr-green-800));
			--cp-clr-feed-attention: var(--clr-feed-attention, var(--clr-orange-700));
			--cp-clr-feed-warning: var(--clr-feed-warning, var(--clr-red-600));

			/* background */
			--cp-clr-background-lvl-0: var(--clr-background-lvl-0, var(--clr-black-900));
			--cp-clr-background-lvl-1: var(--clr-background-lvl-1, var(--clr-black-800));
			--cp-clr-background-lvl-2: var(--clr-background-lvl-2, var(--clr-black-700));
			--cp-clr-background-lvl-3: var(--clr-background-lvl-3, var(--clr-black-600));
			--cp-clr-background-lvl-4: var(--clr-background-lvl-4, var(--clr-black-500));
			--cp-clr-background-lvl-0-tooltip: var(--clr-background-lvl-0-tooltip, var(--clr-black-400));
			--cp-clr-background-viewer: var(--clr-background-viewer, #E3E6E8);
			--cp-clr-background-dialog: var(--clr-background-dialog, var(--clr-transparent-black-50));

			/* border */
			--cp-clr-border-white: var(--clr-border-white, var(--clr-white));
			--cp-clr-border-weak: var(--clr-border-weak, var(--clr-black-700));
			--cp-clr-border-medium: var(--clr-border-medium, var(--clr-black-600));
			--cp-clr-border-strong: var(--clr-border-strong, var(--clr-black-400));
			--cp-clr-input-border-active: var(--clr-input-border-active, var(--clr-blue-500));

			/* shadow */
			--cp-clr-shadow-weak: var(--clr-shadow-weak, var(--clr-transparent-white-15));
			--cp-clr-shadow-medium: var(--clr-shadow-medium, var(--clr-transparent-white-25));
			--cp-clr-shadow-strong: var(--clr-shadow-strong, var(--clr-transparent-white-35));

			/* hover */
			--cp-clr-hover-blue: var(--clr-hover-blue, hsl(204, 100%, 40%, 0.1));
			--cp-clr-hover-red: var(--clr-hover-red, hsl(0, 67%, 61%, 0.1));
			--cp-clr-hover-green: var(--clr-hover-green, hsl(142, 76%, 36%, 0.1));
			--cp-clr-hover-grey: var(--clr-hover-grey, hsl(0, 100%, 100%, 0.1));
		}
	}
`

/**
 * Styling for our custom scrollbar
 **/
export const scrollbar = css`
	:host {
		--scrollbar-width: 1rem;
		--scrollbar-border-width: 0.1rem;
	}

	::-webkit-scrollbar {
		width: var(--scrollbar-width);
		height: var(--scrollbar-width);
		cursor: pointer;
	}

	::-webkit-scrollbar-track {
		background-color: transparent;
	}

	::-webkit-scrollbar-thumb {
		background-color: var(--cp-clr-background-lvl-2);
		border: var(--scrollbar-border-width) solid transparent;
		border-radius: var(--size-spacing-m);
		background-clip: padding-box;
		opacity: .5;
	}

	::-webkit-scrollbar-thumb:hover {
		background-color: var(--cp-clr-background-lvl-3);
	}

	::-webkit-scrollbar-corner {
		background-color: transparent;
	}
`

export const vanishingScrollbar = css`
	.vanishing-scrollbar {	
		--scrollbar-container-width: 1.1rem;
		--scrollbar-color: transparent;
		
		transition: --scrollbar-color var(--animation-time-slow);
	}
	
	.vanishing-scrollbar:hover,
	.vanishing-scrollbar:focus {
		--scrollbar-color: var(--cp-clr-background-lvl-2);
	}

	.vanishing-scrollbar::-webkit-scrollbar-thumb {
		background-color: var(--scrollbar-color);
		border: var(--size-spacing-s) solid transparent;
		border-radius: var(--size-radius-pill);
		background-clip: padding-box;
	}

	.vanishing-scrollbar::-webkit-scrollbar-thumb:hover {
		--scrollbar-color: var(--cp-clr-background-lvl-3);
	}

	.vanishing-scrollbar::-webkit-scrollbar {
		width: 1.5rem;
		height: 1.5rem;
	}

	.vanishing-scrollbar::-webkit-resizer {
		background-color: inherit;
	}
	
	.static-scrollbar {
		overflow-y: scroll;
		overscroll-behavior: contain;
		scrollbar-gutter: stable;
	}
`

export const iconPicker = css`
	.icon-picker-dialog-content {
		display: flex;
		flex-direction: row;
		height: 68rem;
		overflow: hidden;
	}

	.icon-picker-sidebar {
		display: flex;
		flex-direction: column;
		padding: var(--size-spacing-l) 0 0 var(--size-spacing-m);
		background-color: var(--cp-clr-background-lvl-1);
		width: 25.5rem;
	}

	.icon-category {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: var(--size-spacing-xxl);
		padding: 0.8rem 1.6rem 0.8rem 0.6rem;

		& :nth-child(2) {
			width: 100%
		}
	}

	.icon-category-divider {
		height: 2.4rem;
		padding: 0 var(--size-spacing-m) 0 1.5rem;
		border-bottom: 1px solid var(--cp-clr-border-medium);
		margin-top: var(--size-spacing-l);

		& span {
			height: 2.2rem;
			font-size: 14px;
		}
	}

	.icon-category-divider:nth-child(1) {
		margin-top: 0;
	}

	.icon-picker-category-selection {
		display: flex;
		flex-direction: column;
		width: 25.5rem;
		overflow-x: hidden;
		overflow-y: scroll;
	}

	.icon-picker-searchbar {
		padding: 0 var(--size-spacing-l) 0.8rem var(--size-spacing-m)
	}

	.icon-picker-icon-selection {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		align-content: flex-start;
		overflow-x: hidden;
		overflow-y: scroll;
		padding: 8px;
		width: 100%;
	}

	.icon-card {
		width: 16rem;
		height: 10rem;
		background-color: var(--cp-clr-background-lvl-0);
		border-radius: 0.4rem;
		border: 1px solid var(--cp-clr-border-medium);
		display: flex;
		justify-content: flex-start;
		flex-direction: column;
		align-items: center;
		margin: 0.8rem;
		padding: 0.8rem;
		cursor: pointer;

		& .icon-display {
			margin-bottom: 0.8rem;
		}

		& .icon-label {
			text-align: center;
			margin: 0 0.2rem;
		}
	}

	.icon-card:hover {
		background-color: var(--cp-clr-background-lvl-2)
	}

	.icon-display {
		height: 4.8rem;
		font-size: var(--size-text-xxl);
		line-height: var(--size-text-xxxl);
	}
	
	.icon-picker-empty-category {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		height: var(--size-spacing-xxl);
		padding: var(--size-spacing-m) var(--size-spacing-l) var(--size-spacing-m) 0.6rem;
		color: var(--cp-clr-text-tertiary);
		font-size: var(--size-text-m);
		
		& > i {
			margin-right: var(--size-spacing-m);
			font-size: var(--size-spacing-l);
		}
	}

	.icon-picker-empty {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		align-content: center;
		width: 100%;
		
		& > div {
			display: flex;
			justify-content: center;
			color: var(--cp-clr-text-tertiary);
		}
		
		& .icon-picker-empty-icon {
			width: 3rem;
			height: 4rem;
			font-size: 40px;
			margin-bottom: 1.6rem;
		}
		
		& .icon-picker-empty-title {
			height: 2.6rem;
			width: 30rem;
			font-size: var(--size-text-l);
			text-align: center;
		}
		
		& .icon-picker-empty-subtitle {
			width: 22.3rem;
			height: 5.7rem;
			font-size: var(--size-text-m);
			text-align: center;
		}
	}
`

/**
 * Basic styling of FontAwesome icons
 **/
export const icon = css`
	.icon--fixed-width {
		position: relative;
		text-align: center;
		width: 1.75em;
		min-width: 1.75em;
		aspect-ratio: 1 / 1;
	}

	.icon--fixed-width::before {
		position: absolute;
		left: 50%;
		top: 50%;
		translate: -50% -50%;
	}

	.icon--off {
		opacity: var(--opacity, 0.25);
	}

	.icon:not(.disabled).clickable:hover {
		background-color: var(--cp-clr-background-lvl-2);
		border-radius: var(--size-radius-s);
	}

	.state-icon {
		position: relative;
		height: 1.33em;
		width: 0.5rem;
	}

	.state-icon::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		translate: -50% -50%;
		width: 0.5rem;
		aspect-ratio: 1 / 1;
		background-color: var(--state-color);
		border-radius: 50%;
	}
`

/**
 * Styling for a badge (point or pill with text) located at the top right of the bearer
 * custom styles:
 * --badge-offset -> offset from top right if only a point is displayed (default: 0.3rem)
 * --badge-color -> background color
 * attributes: 
 * data-badge-text -> content of the badge
 */
export const badge = css`
	.with-badge::after {
		content: attr(data-badge-text);
		position: absolute;
		right: var(--badge-offset-x, var(--badge-offset));
		top: var(--badge-offset-y, var(--badge-offset));

		min-height: var(--min-size);
		min-width: var(--min-size);
		border-radius: var(--size-radius-pill);
		text-align: center;

		font-size: var(--size-text-s);
		color: var(--cp-clr-text-primary-negativ);
		background-color: var(--badge-color, var(--cp-clr-state-active));
		outline: 1px solid var(--badge-outline-color, transparent);
	}

	.with-badge[data-badge-text]::after {
		--badge-offset: 0;
		line-height: 160%;
		translate: 50% -50%;
	}

	.with-badge[data-badge-text]:not([data-badge-text=''])::after {
		padding: 0 var(--size-spacing-s);
	}
`

/**
 * basic animation config
 **/
export const animation = css`
	:host, :root {
	  --animation-time-fast: var(--custom-animation-time-fast, 75ms);
	  --animation-time-medium: var(--custom-animation-time-medium, 150ms);
		--animation-time-slow: var(--custom-animation-time-slow, 300ms);
		--skeleton-animation-speed: 3s;
	}

	/* skeleton styling */
	@keyframes skeleton-gradient {
		0% {
			background-position: right;
		}

		100% {
			background-position: left;
		}
	}

	@keyframes skeleton-fade {
		0%, 100% {
			opacity: 0.25;
		}

		50% {
			opacity: 1;
		}
	}
`

export const skeleton = css`
	:host, :root {
		--clr-skeleton-block: var(--cp-clr-background-lvl-1);
		--clr-skeleton-text: var(--cp-clr-background-lvl-2);
		--clr-skeleton-text-2: var(--cp-clr-background-lvl-3);
	}
	
	.skeleton__block, .skeleton__text {
		display: block;
		position: relative;
		overflow: hidden;
		color: transparent;
		background-color: unset !important;
		resize: none !important;
		border-color: transparent !important;
	}

	.skeleton__block * {
		color: transparent !important;
		user-select: none;
	}

	.skeleton__block *::selection,
	.skeleton__block *::placeholder {
		color: transparent
	}

	.skeleton__block lvl-input-icon,
	.skeleton__block lvl-input-button {
		opacity: 0;
	}

	.skeleton__block::before {
		content: '';
		position: absolute;
		inset: var(--skeleton-inset, 0);
		background-color: var(--clr-skeleton-block);
		border-radius: var(--size-radius-m);
		animation: skeleton-fade var(--skeleton-animation-speed, 0) linear;
		animation-iteration-count: infinite;
		cursor: default;
	}
	
	.skeleton__text:before {
		content: '';
		position: absolute;
		inset: 0;
		height: 80%;
		border-radius: var(--size-radius-m);
		background-image: linear-gradient(90deg,
		var(--clr-skeleton-text) 0%,
		var(--clr-skeleton-text) 30%,
		hsl(from var(--clr-skeleton-text) h s l / 50%),
		var(--clr-skeleton-text) 70%,
		var(--clr-skeleton-text) 100%);
		background-size: 400%;
		animation: skeleton-gradient var(--skeleton-animation-speed, 0) linear;
		animation-iteration-count: infinite;
	}
`

/**
 * basic input field config
 **/
export const input = css`
	:host {
		--input-height: 32px;
		--input-padding-inline: 10px;
	}

	/* in preview mode those two types of icons should be reachable (the rest is below a invisible layer) */
	:host([preview]) lvl-input-icon[type=info],
	:host([preview]) lvl-input-icon[type=disconnected] {
		z-index: 1;
	}
`

/**
 * basic tooltip config
 **/
export const tooltip = css`
	:host {
		--clr-tooltip-background: #2A343C;
		--clr-tooltip-text: #FFFFFF;
		--spacing-horizontal: var(--tooltip-spacing-horizontal, 0px);
		--spacing-vertical: var(--tooltip-spacing-vertical, 0px);
		--offset-horizontal: var(--tooltip-offset-horizontal, 0px);
		--offset-vertical: var(--tooltip-offset-vertical, 0px);
		--marker-offset-horizontal: var(--tooltip-marker-offset-horizontal, 10px);
		--marker-offset-vertical: var(--tooltip-marker-offset-vertical, 10px);
	}
`

/**
 * default appearance of a dialog button
 */
export const dialogButton = css`
	/* Default Button          */
	.dialog-button, ::slotted(.dialog-button) {
		--text-color: var(--cp-clr-state-active);
		--background-color: unset;
		--border-color: transparent;

		border-radius: var(--size-radius-m);
		border: 1px solid var(--border-color);
		color: var(--text-color);
		background-color: var(--background-color);

		padding: 0 var(--size-spacing-m, 0.4rem);
		height: 3.2rem;
		min-width: 7.2rem;
	}

	.dialog-button[skeleton], ::slotted(.dialog-button[skeleton]) {
		background-color: transparent !important;
		border-color: transparent !important;
		--skeleton-inset: calc(var(--size-spacing-m, 0.5rem) * -1) calc(var(--size-spacing-l, 1rem) * -1);
		--skeleton-outline: 1px solid var(--clr-skeleton-block);
		--clr-skeleton-block: var(--cp-clr-background-lvl-2);
		pointer-events: none;
	}

	.dialog-button:hover, ::slotted(.dialog-button:hover) {
		--background-color: var(--cp-clr-background-lvl-3);
	}

	/* Primary Button */

	.dialog-button--primary, ::slotted(.dialog-button--primary) {
		--text-color: var(--cp-clr-text-primary-negativ);
		--background-color: var(--cp-clr-state-active);
	}

	.dialog-button--primary:hover, ::slotted(.dialog-button--primary:hover) {
		--background-color: var(--cp-clr-state-hover-strong);
	}

	/* Secondary Button */

	.dialog-button--secondary, ::slotted(.dialog-button--secondary) {
		--border-color: currentColor;
	}

	.dialog-button--secondary:hover, ::slotted(.dialog-button--secondary:hover) {

	}
`

export const richTextStyles = css`
	.html__wrapper {
		& p {
		margin: 0;
		line-height: 150%;
		white-space: normal;
		}
	
		& h1 {
		font-size: var(--size-text-xl);
		}
	
		& h2 {
		font-size: var(--size-text-l);
		}
	
		& h3 {
		font-size: var(--size-text-m);
		}
	
		& h1, h2, h3 {
		font-weight: bold;
		}
	
		& mark {
		border-radius: var(--size-radius-m);
		}
	
		& ol, ul {
		margin: 0;
		padding-left: var(--size-spacing-xl);
		}
	
		& ul > li {
		list-style-type: disc;
		}
	
		& ol > li {
		list-style-type: decimal;
		}
	
		& a {
		cursor: pointer;
		}
	
		& .is-empty:first-child::before {
		color: var(--cp-clr-text-tertiary);
		content: attr(data-placeholder);
		float: left;
		height: 0;
		pointer-events: none;
		}
	}
` 