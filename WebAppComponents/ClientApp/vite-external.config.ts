import { defineConfig } from 'vite'
import { resolve } from 'path'
import istanbul from 'vite-plugin-istanbul'

// https://vitejs.dev/config/
export default defineConfig({
	resolve: {
		alias: {
			'@': resolve(__dirname, './src'),
			'@stories': resolve(__dirname, './.storybook/stories'),
			'@story-home': resolve(__dirname, './.storybook'),
			'@test-home': resolve(__dirname, './cypress'),
			'@i18n': resolve(__dirname, './i18n'),
		},
	},
	plugins: [
		istanbul({
			requireEnv: false,
			cypress: true,
		}),
	],
	build: {
		emptyOutDir: true,
		copyPublicDir: false,
		sourcemap: false,
		rollupOptions: {
			input: resolve(__dirname, './src/components/hybrid/index.ts'),
			output: {
				entryFileNames: 'main.bundled.js',
			},
		},
	},
})