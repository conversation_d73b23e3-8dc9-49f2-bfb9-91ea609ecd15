using Levelbuild.Core.EntityInterface.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Entities.ContextFactories;

/// <summary>
/// Factory for postgres database contexts.
/// </summary>
public class PostgresCoreDatabaseContextFactory : IDbContextFactory<CoreDatabaseContext>
{
	private readonly IServiceProvider _serviceProvider;
	
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="serviceProvider"></param>
	public PostgresCoreDatabaseContextFactory(IServiceProvider serviceProvider)
	{
		_serviceProvider = serviceProvider;
	}
	
	/// <summary>
	/// Creates the context instance.
	/// </summary>
	/// <returns></returns>
	public CoreDatabaseContext CreateDbContext()
	{
		// dynamicJson is needed for attributes of type [JsonColumn]
		return ActivatorUtilities.CreateInstance<CoreDatabaseContext>(_serviceProvider, DatabaseProvider.Postgres);
	}
}