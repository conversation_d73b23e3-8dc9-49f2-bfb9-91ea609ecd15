using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Entities.Conventions;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.Auth;
using Levelbuild.Entities.Features.Customer;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.DataStoreContext;
using Levelbuild.Entities.Features.DeepZoom;
using Levelbuild.Entities.Features.Device;
using Levelbuild.Entities.Features.FileUploads;
using Levelbuild.Entities.Features.Localization;
using Levelbuild.Entities.Features.LoggerConfig;
using Levelbuild.Entities.Features.Module;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.Page.Create;
using Levelbuild.Entities.Features.Page.MultiData;
using Levelbuild.Entities.Features.Page.SingleData;
using Levelbuild.Entities.Features.PageView;
using Levelbuild.Entities.Features.PageView.GalleryView;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.PageView.ListView;
using Levelbuild.Entities.Features.StartupMigration;
using Levelbuild.Entities.Features.Thumbnail;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.UserCustomerMapping;
using Levelbuild.Entities.Features.Userlane;
using Levelbuild.Entities.Features.UserlaneStepAction;
using Levelbuild.Entities.Features.UserlaneStep;
using Levelbuild.Entities.Features.UserlaneStepTestCondition;
using Levelbuild.Entities.Features.Workflow;
using Levelbuild.Entities.Handlers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Serilog;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Entities;

/// <summary>
/// The WebApp's main DbContext
/// </summary>
public class CoreDatabaseContext : DbContext
{
	private readonly DatabaseProvider? _databaseProvider;
	
	public DbSet<UserEntity> Users { get; set; }
	
	public DbSet<CustomerEntity> Customers { get; set; }
	
	public DbSet<LoggerConfigEntity> LoggerConfigs { get; set; }
	
	public DbSet<DataStoreConfigEntity> DataStoreConfigs { get; set; }
	
	public DbSet<CultureEntity> Cultures { get; set; }
	
	public DbSet<TranslationEntity> Translations { get; set; }
	
	public DbSet<DataSourceEntity> DataSources { get; set; }
	
	public DbSet<DataFieldEntity> DataFields { get; set; }
	
	public DbSet<DataFieldFilterEntity> DataFieldFilters { get; set; }
	
	public DbSet<DataStoreContextEntity> DataStoreContexts { get; set; }
	public DbSet<UserlaneEntity> Userlanes { get; set; }
	
	public DbSet<UserlaneResultEntity> UserlaneResults { get; set; }
	
	public DbSet<UserlaneResultBatchEntity> UserlaneResultBatches { get; set; }
	
	public DbSet<UserlaneStepEntity> UserlaneSteps { get; set; }
	
	public DbSet<UserlaneStepActionEntity> UserlaneStepActions { get; set; }
	
	public DbSet<UserlaneStepTestConditionEntity> UserlaneStepTestConditions { get; set; }

	public DbSet<ModuleEntity> Modules { get; set; }
	
	public DbSet<PageEntity> Pages { get; set; }
	
	public DbSet<CreatePageEntity> CreatePages { get; set; }
	
	public DbSet<SingleDataPageEntity> SingleDataPages { get; set; }
	
	public DbSet<MultiDataPageEntity> MultiDataPages { get; set; }
	
	public DbSet<PageViewEntity> PageViews { get; set; }
	
	public DbSet<ListViewEntity> ListViews { get; set; }
	
	public DbSet<ListViewColumnEntity> ListViewColumns { get; set; }
	
	public DbSet<DataFieldSortingEntity> DataFieldSortings { get; set; }
	
	public DbSet<DataFieldColumnEntity> DataFieldColumns { get; set; }
	
	public DbSet<PageHeaderElementEntity> PageHeaderElements { get; set; }
	
	public DbSet<GalleryViewEntity> GalleryViews { get; set; }
	
	public DbSet<GridViewEntity> GridViews { get; set; }
	
	public DbSet<GridViewPageEntity> GridViewPages { get; set; }
	
	public DbSet<GridViewPageFilterEntity> GridViewPageFilters { get; set; }
	
	public DbSet<GridViewSectionEntity> GridViewSections { get; set; }
	
	public DbSet<GridViewFieldEntity> GridViewFields { get; set; }
	
	public DbSet<GridViewTextEntity> GridViewTexts { get; set; }
	
	public DbSet<DeviceEntity> Devices { get; set; }
	
	public DbSet<UserCustomerMappingEntity> UserCustomerMappings { get; set; }
	
	public DbSet<UserPreferencesEntity> UserPreferences { get; set; }
	
	public DbSet<StartupMigrationEntity> StartupMigrations { get; set; }
	
	public DbSet<MultiPageFilterFieldEntity> MultiPageFilterFields { get; set; }
	
	public DbSet<OneTimeAuthenticationCodeEntity> OneTimeAuthenticationCodes { get; set; }
	
	public DbSet<CachedThumbnailEntity> Thumbnails { get; set; }
	
	public DbSet<CachedDeepZoomEntity> DeepZoomImages { get; set; }
	
	public DbSet<FileUploadsEntity> FileUploads { get; set; }
	
	public DbSet<WorkflowEntity> Workflows { get; set; }
	
	public DbSet<WorkflowNodeEntity> WorkflowNodes { get; set; }

	[ExcludeFromCodeCoverage]
	public CoreDatabaseContext(DbContextOptions options) : base(options)
	{
		_databaseProvider ??= DatabaseProvider.Postgres;
	}
	
	[ExcludeFromCodeCoverage]
	public CoreDatabaseContext(DbContextOptions options, DatabaseProvider databaseProvider) : base(options)
	{
		_databaseProvider = databaseProvider;
	}
	
	/// <summary>
	/// Executes startup logic.
	/// </summary>
	/// <param name="logger"></param>
	/// <param name="serviceProvider"></param>
	public async Task HandleStartupAsync(ILogger logger, IServiceProvider serviceProvider)
	{
		await DatabaseStartupHandler.ExecuteAsync(this, logger, serviceProvider);
	}
	
	[ExcludeFromCodeCoverage]
	protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
	{
		if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
			optionsBuilder.EnableSensitiveDataLogging();
		
		// Needed for polymorphic entities since we don't always know if the resulting entity really has an included property
		optionsBuilder.ConfigureWarnings(warning => warning.Ignore(CoreEventId.InvalidIncludePathError));
		
		// Use this line if you want see where the query are to full and might need a 'AsSplitQuery()'
		// optionsBuilder.ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning));

		// needed as soon as a parent table contains a foreign key constraint as well as child tables
		//optionsBuilder
		//	.ConfigureWarnings(wa => wa.Ignore(RelationalEventId.ForeignKeyPropertiesMappedToUnrelatedTables));
		
		optionsBuilder.ConfigureWarnings(warning => warning.Ignore(CoreEventId.ManyServiceProvidersCreatedWarning));
		
		base.OnConfiguring(optionsBuilder);
	}
	
	[ExcludeFromCodeCoverage]
	protected override void OnModelCreating(ModelBuilder modelBuilder)
	{
		base.OnModelCreating(modelBuilder);
		
		if (_databaseProvider == null)
			return;
		
		if (_databaseProvider == DatabaseProvider.Postgres)
		{
			// Add the Postgres Extension for UUID generation
			modelBuilder.HasPostgresExtension("uuid-ossp");
			
			// configure npgsql to auto convert DateTime
			AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
		}
		
		foreach (var entityType in modelBuilder.Model.GetEntityTypes())
		{
			foreach (var property in entityType.GetProperties())
			{
				if (property.ClrType == typeof(DateTime))
				{
					modelBuilder.Entity(entityType.ClrType)
						.Property<DateTime>(property.Name)
						.HasConversion(
							v => v.ToUniversalTime(),
							v => v.ToUniversalTime()
						);
				}
				else if (property.ClrType == typeof(DateTime?))
				{
					modelBuilder.Entity(entityType.ClrType)
						.Property<DateTime?>(property.Name)
						.HasConversion(
							v => v.HasValue ? v.Value.ToUniversalTime() : v,
							v => v.HasValue ? v.Value.ToUniversalTime() : v
						);
				}
			}
		}
		
		// check annotations, execute custom Entity Framework configuration 
		modelBuilder.CheckAndConfigureAllModels(_databaseProvider);
	}
	
	protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
	{
		base.ConfigureConventions(configurationBuilder);
		
		configurationBuilder.Conventions.Add(_ => new MaxStringLengthConvention());
	}
}