using System.Text.RegularExpressions;

namespace Levelbuild.Entities.Extensions;

/// <summary>
/// Extension Class for Strings
/// </summary>
public static class StringExtensions
{
	/// <summary>
	/// Converts the given string, so that it can be used as a slug
	/// </summary>
	/// <param name="text"></param>
	/// <returns></returns>
	public static string ToSlug(this string text)
	{
		if (string.IsNullOrEmpty(text))
			return "";

		// trim and lowercase
		var slug = text.Trim().ToLower();

		// remove invalid chars
		slug = Regex.Replace(slug, @"[^a-z0-9\s-]", "");

		// transform spaces into minus
		slug = Regex.Replace(slug, @"\s+", " ");
		return Regex.Replace(slug, @"\s", "-");
	}
}