using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.DataField;

/// <summary>
/// Data Field Column
/// </summary>
public class DataFieldColumnEntity : PersistentEntity<DataFieldColumnEntity>, IAdministrationEntity<DataFieldColumnEntity, DataFieldColumnDto>
{
	/// <summary>
	/// Parent field id
	/// </summary>
	[Required]
	public Guid DataFieldId { get; init; }
	
	/// <summary>
	/// Parent field entity
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(DataFieldId))]
	public DataFieldEntity? DataField { get; init; }
	
	/// <summary>
	/// column position
	/// </summary>
	public int Position { get; set; }
	
	/// <summary>
	/// Display field id
	/// </summary>
	public Guid? DisplayFieldId { get; init; }
	
	
	/// <summary>
	/// Display field entity
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(DisplayFieldId))]
	public DataFieldEntity? DisplayField { get; init; }
	
	/// <inheritdoc />
	public DataFieldColumnEntity()
	{
	}

	private DataFieldColumnEntity(DataFieldColumnDto dto)
	{
		DataFieldId = dto.DataFieldId;
		DisplayFieldId = dto.DisplayFieldId;
		Position = dto.Position ?? 0;
	}
	
	/// <inheritdoc />
	public static DataFieldColumnEntity FromDto(DataFieldColumnDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new DataFieldColumnEntity(entityInfo);
	}
	
	/// <inheritdoc />
	public DataFieldColumnDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new DataFieldColumnDto(this, excludedProperties, handledObjects)
		{
			DisplayFieldName = DisplayField?.Name 
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(DataFieldColumnDto dto, UserEntity? modifiedBy = null)
	{
		Position = dto.Position ?? 0;
	}
}