using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.DataSource;

/// <summary>
/// configuration for a text element which is embedded into a GridViewSectionEntity
/// </summary>
public class DataSourceFilterEntity<TEntity> : PersistentEntity<TEntity>, IAdministrationEntity<DataSourceFilterEntity<TEntity>, DataSourceFilterDto> where TEntity : PersistentEntity<TEntity>
{
	/// <summary>
	/// reference to the DataFieldEntity we want to filter against
	/// </summary>
	public Guid FilterFieldId { get; set; }
	
	/// <summary>
	/// DataFieldEntity we want to filter against
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(FilterFieldId))]
	public DataFieldEntity? FilterField { get; set; }
	
	/// <summary>
	/// Compare operator
	/// </summary>
	public CompareOperator Operator { get; set; }
	
	/// <summary>
	/// compare value which may include things like placeholders
	/// </summary>
	public string? CompareValue { get; set; }
	
	/// <summary>
	/// create date
	/// </summary>
	public DateTime? Created { get; init; }
	
	/// <summary>
	/// create user
	/// </summary>
	public string? CreatedBy { get; init; }
	
	/// <summary>
	/// constructor
	/// </summary>
	public DataSourceFilterEntity()
	{
	}
	
	private DataSourceFilterEntity(DataSourceFilterDto dto, UserEntity? createdBy)
	{
		if (dto.FilterFieldId != null)
			FilterFieldId = dto.FilterFieldId.Value;
		if (dto.Operator != null)
			Operator = dto.Operator.Value;
		CompareValue = dto.CompareValue;
		
		Created = DateTime.Now.ToUniversalTime();
		CreatedBy = createdBy?.DisplayName;
	}
	
	/// <inheritdoc />
	public static DataSourceFilterEntity<TEntity> FromDto(DataSourceFilterDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new DataSourceFilterEntity<TEntity>(entityInfo, createdBy);
	}
	
	/// <inheritdoc />
	public DataSourceFilterDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new DataSourceFilterDto(this, excludedProperties, handledObjects)
		{
			FilterFieldName = FilterField?.Name
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(DataSourceFilterDto dto, UserEntity? modifiedBy = null)
	{
		if (dto.FilterFieldId != null)
			FilterFieldId = dto.FilterFieldId.Value;
		if (dto.Operator != null)
			Operator = dto.Operator.Value;
		if (dto.CompareValue != null)
			CompareValue = dto.CompareValue;
	}
}