using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.DataStoreConfig;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Entities.Attributes;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Helpers.DataStoreConfig;
using Levelbuild.Entities.Interfaces;
using Levelbuild.Entities.Providers;
using Microsoft.Extensions.Localization;

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Levelbuild.Entities.Features.DataStoreConfig;

/// <summary>
/// DataStore configuration entity.
/// </summary>
[Slug(nameof(Name))]
public sealed class DataStoreConfigEntity : PersistentEntity<DataStoreConfigEntity>, IAdministrationEntity<DataStoreConfigEntity, DataStoreConfigDto>
{
	/// <summary>
	/// The DataStore's type.
	/// </summary>
	[Required]
	public DataStoreType Type { get; set; }
	
	/// <summary>
	/// Human readable slug.
	/// </summary>
	public string Slug { get; private set; }
	
	/// <summary>
	/// The DataStore's name.
	/// </summary>
	[Required]
	public string Name
	{
		get => _name;
		set
		{
			_name = value;
			Slug = value.ToSlug();
		}
	}

	private string _name;
	
	/// <summary>
	/// Is the DataStore enabled?
	/// </summary>
	public bool Enabled { get; set; } = true;

	private Dictionary<string, object>? _options;
	
	/// <summary>
	/// The DataStore's options.
	///
	/// Persisted as JSON column.
	/// </summary>
	[JsonColumn]
	public Dictionary<string, object>? Options
	{
		// EF Core/Postgres will serve Options as Dictionary<string, JsonElement>
		// We should deliver the real types so that our DataStores won't have to handle Json data themselves
		get
		{
			var result = new Dictionary<string, object>();

			if (_options == null)
				return result;

			Dictionary<string, object>.Enumerator enumerator = _options!.GetEnumerator();
			try
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current.Value is JsonElement jsonElement && jsonElement.TryGetRealValue(out var value))
					{
						result.Add(enumerator.Current.Key, value!);
						continue;
					}

					result.Add(enumerator.Current.Key, enumerator.Current.Value);
				}
			}
			finally
			{
				enumerator.Dispose();
			}

			return result;
		}
		set => _options = value;
	}
	
	[DoNotPersist]
	internal DataStoreConnectionProvider<DataStoreConnectionHelper>? ConnectionProvider { get; set; }
	
	[DoNotPersist]
	private DataStoreConnectionHelper? _connection;
	
	[DoNotPersist]
	internal DataStoreConnectionHelper Connection
	{
		get
		{
			if (_connection != null)
				return _connection;
			
			if (ConnectionProvider == null)
				throw new NullReferenceException($"No connection provider set for data source '{Name}'!");
			
			return ConnectionProvider.GetConnection();
		}
	}

	private IStringLocalizer? _localizer;
	
	/// <inheritdoc />
	public DataStoreConfigEntity()
	{
	}
	
	/// <inheritdoc />
	private DataStoreConfigEntity(DataStoreConfigDto dto)
	{
		if (string.IsNullOrEmpty(dto.Name))
			throw new ArgumentException($"Property 'Name' in DataStoreConfig is not valid: {dto.Name}");

		if (dto.Type == null)
			throw new ArgumentException($"Property 'Type' in DataStoreConfig may not be empty");

		if (dto.Id != null)
			Id = dto.Id.Value;

		Name = dto.Name!;
		Type = dto.Type.Value;
		Enabled = dto.Enabled == true;
		Options = dto.Options;
	}
	
	/// <inheritdoc />
	public DataStoreConfigDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		_localizer ??= StringLocalizerFactory?.Create("DataStoreType", "");
		return new DataStoreConfigDto(this, excludedProperties, handledObjects)
		{
			TypeName = _localizer != null ? _localizer[Type.ToString()] : ""
		};
	}
	
	/// <inheritdoc />
	public static DataStoreConfigEntity FromDto(DataStoreConfigDto entityDto, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new DataStoreConfigEntity(entityDto);
	}
	
	/// <inheritdoc />
	public void UpdatePartial(DataStoreConfigDto dto, UserEntity? modifiedBy = null)
	{
		if (!string.IsNullOrEmpty(dto.Name))
			Name = dto.Name;
		if (dto.Enabled != null)
			Enabled = dto.Enabled.Value;
		if (dto.Type != null)
			Type = dto.Type.Value;
		if (dto.Options != null)
			Options = dto.Options;
	}

	/// <summary>
	/// Returns DataSources that are not present in the Storage
	/// </summary>
	/// <param name="databaseContext"></param>
	public List<DataSourceEntity> GetSourcesToSync(CoreDatabaseContext databaseContext)
	{
		var storageSources = GetDataSources();

		return databaseContext.DataSources.Where(source => source.DataStoreId == Id && !storageSources.Contains(source.Name)).ToList();
	}

	/// <summary>
	/// Syncs Storage data sources to the WebApp
	/// </summary>
	/// <param name="databaseContext"></param>
	public List<DataSourceEntity> SyncStorageSources(CoreDatabaseContext databaseContext)
	{
		var storageSources = GetDataSources();

		var syncedSources = new List<DataSourceEntity>();
		foreach (var storageSource in storageSources)
		{
			if (databaseContext.DataSources.FirstOrDefault(source => source.DataStoreId == Id && source.Name == storageSource) != null)
				continue;

			var dataStoreDataSource = GetDataSource(storageSource)!;
			var newDataSource = DataSourceEntity.CreateEntityFromDataStore(dataStoreDataSource, databaseContext, Id);
			databaseContext.DataSources.Add(newDataSource);
			databaseContext.SaveChanges();
			
			if(dataStoreDataSource is StorageDataSource storageDataSource)
				newDataSource.SyncStorageFields(databaseContext, storageDataSource);
			
			syncedSources.Add(newDataSource);
		}

		return syncedSources;
	}
	
	/// <summary>
	/// Syncs Storage data source to the WebApp
	/// </summary>
	/// <param name="databaseContext"></param>
	/// <param name="storageSourceName"></param>
	public DataSourceEntity AddStorageSource(CoreDatabaseContext databaseContext, string storageSourceName)
	{
		var dataStoreDataSource = GetDataSource(storageSourceName)!;
		var newDataSource = DataSourceEntity.CreateEntityFromDataStore(dataStoreDataSource, databaseContext, Id);
		databaseContext.DataSources.Add(newDataSource);
		databaseContext.SaveChanges();
		
		if(dataStoreDataSource is StorageDataSource storageDataSource)
			newDataSource.SyncStorageFields(databaseContext, storageDataSource);

		return newDataSource;
	}

	#region DataStore Connection Handling
	
	/// <summary>
	/// Set the connection provider for the data store
	/// </summary>
	/// <param name="connectionProvider"></param>
	public void SetConnectionProvider(DataStoreConnectionProvider<DataStoreConnectionHelper> connectionProvider)
	{
		ConnectionProvider = connectionProvider;
	}
	
	/// <summary>
	/// Save the connection to the data store
	/// </summary>
	/// <param name="connection"></param>
	public void SetConnection(DataStoreConnectionHelper connection)
	{
		_connection = connection;
	}
	
	/// <inheritdoc cref="DataStoreConnectionHelper.GetDataSource"/>
	public IDataStoreDataSource? GetDataSource(string name)
	{
		return Connection.GetDataSource(name);
	}

	/// <inheritdoc cref="DataStoreConnectionHelper.GetDataSources"/>
	public IList<string> GetDataSources()
	{
		return Connection.GetDataSources();
	}

	#endregion
}