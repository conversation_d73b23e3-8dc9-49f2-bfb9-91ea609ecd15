using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.DataStoreContext;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Entities.Attributes;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.Customer;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Helpers.DataStoreContext;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;

// ReSharper disable ConvertTypeCheckPatternToNullCheck
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Levelbuild.Entities.Features.DataStoreContext;

/// <summary>
/// DataStore context entity.
/// </summary>
[Slug(nameof(Name), nameof(DataStoreId))]
public sealed class DataStoreContextEntity : PersistentEntity<DataStoreContextEntity>, IAdministrationEntity<DataStoreContextEntity, DataStoreContextDto>
{
	/// <summary>
	/// Human readable slug.
	/// </summary>
	public string Slug { get; private set; }
	
	/// <summary>
	/// The context's name.
	/// </summary>
	[Required]
	public string Name
	{
		get => _name;
		set
		{
			_name = value;
			Slug = value.ToSlug();
		}
	}
	
	private string _name;
	
	/// <summary>
	/// The Id of the DataStore.
	/// </summary>
	[Required]
	public Guid? DataStoreId { get; set; }
	
	/// <summary>
	/// The DataStore.
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(DataStoreId))]
	public DataStoreConfigEntity DataStore { get; set; }
	
	/// <summary>
	/// The Id of the context's customer.
	/// </summary>
	[Required]
	public Guid? CustomerId { get; set; }
	
	/// <summary>
	/// The context's customer.
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(CustomerId))]
	public CustomerEntity Customer { get; set; }
	
	/// <summary>
	/// Is the context enabled?
	/// </summary>
	public bool Enabled { get; set; } = true;
	
	private Dictionary<string, object>? _options;
	
	/// <summary>
	/// The context's options.
	///
	/// Persisted as JSON column.
	/// </summary>
	[JsonColumn]
	public Dictionary<string, object>? Options
	{
		// EF Core/Postgres will serve Options as Dictionary<string, JsonElement>
		// We should deliver the real types so that our DataStores won't have to handle Json data themselves
		get
		{
			var result = new Dictionary<string, object>();
			
			if (_options == null)
				return result;
			
			Dictionary<string, object>.Enumerator enumerator = _options!.GetEnumerator();
			try
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current.Value is JsonElement jsonElement && jsonElement.TryGetRealValue(out var value))
					{
						result.Add(enumerator.Current.Key, value!);
						continue;
					}
					
					result.Add(enumerator.Current.Key, enumerator.Current.Value);
				}
			}
			finally
			{
				enumerator.Dispose();
			}
			
			return result;
		}
		set => _options = value;
	}
	
	[DoNotPersist]
	internal StorageDataStoreContextConnectionHelper Connection
	{
		get
		{
			// ReSharper disable once ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract
			if (DataStore != null)
				return new StorageDataStoreContextConnectionHelper(this);
			
			if (DbContext == null)
				throw new NullReferenceException($"No db context set for data source '{Name}'!");
			
			DataStore = DbContext.DataStoreConfigs.Find(DataStoreId)!;
			
			switch (DataStore.Type)
			{
				case DataStoreType.Storage:
					return new StorageDataStoreContextConnectionHelper(this);
				default:
					throw new Exception("Unsupported DataStore type!");
			}
		}
	}
	
	/// <summary>
	/// Constructor.
	/// </summary>
	public DataStoreContextEntity()
	{
	}
	
	private DataStoreContextEntity(DataStoreContextDto dto, CoreDatabaseContext? databaseContext)
	{
		if (string.IsNullOrEmpty(dto.Name))
			throw new ArgumentException($"Property 'Name' in DataStoreContext is not valid: {dto.Name}");
		
		if (dto.DataStoreId != null)
		{
			var dataStoreConfig = databaseContext?.DataStoreConfigs.Find(dto.DataStoreId);
			DataStore = dataStoreConfig ?? throw new ArgumentException($"Property 'DataStore' in DataStoreContext is not valid: {dto.DataStoreId}");
			DataStoreId = dto.DataStoreId;
		}
		
		if (dto.CustomerId != null)
		{
			var customer = databaseContext?.Customers.Find(dto.CustomerId);
			Customer = customer ?? throw new ArgumentException($"Property 'Customer' in DataStoreContext is not valid: {dto.CustomerId}");
			CustomerId = dto.CustomerId;
		}
		
		Name = dto.Name!;
		Enabled = dto.Enabled == true;
		Options = dto.Options;
	}
	
	/// <inheritdoc />
	public static DataStoreContextEntity FromDto(DataStoreContextDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new DataStoreContextEntity(entityInfo, context);
	}
	
	/// <inheritdoc />
	public DataStoreContextDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		var dto = new DataStoreContextDto(this, excludedProperties, handledObjects)
		{
			CustomerName = Customer?.DisplayName ?? Name
		};
		return dto;
	}
	
	/// <inheritdoc />
	public void UpdatePartial(DataStoreContextDto dto, UserEntity? modifiedBy = null)
	{
		// Ignore name changes, since it serves as key for the storage and can't be changed afterwards!
		
		if (dto.Enabled != null)
			Enabled = dto.Enabled.Value;
		if (dto.Options != null)
			Options = dto.Options;
	}
	
	#region DataStore Connection Handling
	
	/// <inheritdoc cref="StorageDataStoreContextConnectionHelper.GetContext"/>
	public IDataStoreContext GetContext()
	{
		if (Connection is StorageDataStoreContextConnectionHelper storageConnection)
		{
			return storageConnection.GetContext();
		}
		
		throw new DataStoreOperationException("Multi tenancy is not supported by DataStore!");
	}
	
	/// <inheritdoc cref="StorageDataStoreContextConnectionHelper.CreateContext"/>
	public StorageContext CreateContext()
	{
		if (Connection is StorageDataStoreContextConnectionHelper storageConnection)
		{
			return storageConnection.CreateContext();
		}
		
		throw new DataStoreOperationException("Creating a context is not supported by DataStore!");
	}
	
	/// <inheritdoc cref="StorageDataStoreContextConnectionHelper.UpdateContext"/>
	public StorageContext UpdateContext()
	{
		if (Connection is StorageDataStoreContextConnectionHelper storageConnection)
		{
			return storageConnection.UpdateContext();
		}
		
		throw new DataStoreOperationException("Updating a context is not supported by DataStore!");
	}
	
	/// <inheritdoc cref="StorageDataStoreContextConnectionHelper.RemoveContext"/>
	public void RemoveContext(bool forceDeleteDb = false)
	{
		if (Connection is StorageDataStoreContextConnectionHelper storageConnection)
		{
			storageConnection.RemoveContext(forceDeleteDb);
			return;
		}
		
		throw new DataStoreOperationException("Removing a context is not supported by DataStore!");
	}
	
	#endregion
}