using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.RegularExpressions;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using Levelbuild.Entities.Features.DataSource;

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Levelbuild.Entities.Features.FileUploads;

/// <summary>
/// Entity to configure a file upload as part of a data store
/// </summary>
public partial class FileUploadsEntity : RevisedPersistentEntity<FileUploadsEntity>, IAdministrationEntity<FileUploadsEntity, FileUploadsDto>
{
	/// <summary>
	/// Parent ID to reference the data store
	/// </summary>
	[Required]
	public Guid DataSourceId { get; set; }

	/// <summary>
	/// Parent data store
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(DataSourceId))]
	public DataSourceEntity DataSource { get; set; }

	/// <summary>
	/// Name of the file uploaded (has to be unique)
	/// </summary>
	[Required]
    public string FileName { get; set; }

    /// <summary>
    /// Whether the file is public
    /// </summary>
    public bool IsPublic { get; set; } = false;

    /// <summary>
    /// Whether the file is deleted
    /// </summary>
    public bool IsDeleted { get; set; } = false;

    /// <summary>
    /// Timestamp at which the file was deleted
    /// </summary>
    public DateTime? DeletedAt { get; set; }

    /// <summary>
    /// Name of the person who deleted the file
    /// </summary>
    public string? DeletedBy { get; set; } = null;


    /// <summary>
    /// Whether the file is read-only
    /// </summary>
    public bool IsReadOnly { get; set; } = false;


	/// <summary>
	/// Timestamp at which the data source was created
	/// </summary>
	public DateTime? Created { get; init; }

	/// <summary>
	/// Name of the person who created the data source
	/// </summary>
	[ShortString]
	public string? CreatedBy { get; init; }

	/// <summary>
	/// Timestamp at which the data source was updated
	/// </summary>
	public DateTime? LastModified { get; set; }

	/// <summary>
	/// Name of the person who updated the data source
	/// </summary>
	[ShortString]
	public string? LastModifiedBy { get; set; }

	/// <summary>
	/// Revision of the file uploaded
	/// </summary>
	public Guid Revision { get; set; }

	/// <summary>
	/// File lock for use in WOPI
	/// </summary>
	public string? LockId { get; set; }

	/// <summary>
	///
	/// </summary>
	public string? LockValue { get; set; }

	/// <summary>
	///
	/// </summary>
	public DateTime? LockExpires { get; set; }

	/// <summary>
	/// Auto-incrementing numeric ID for the file
	/// </summary>
	[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
	public int FileId { get; set; }

	/// <summary>
	/// User information for WOPI integration
	/// </summary>
	public string? UserInfo { get; set; }


	/// <inheritdoc />
	public FileUploadsEntity()
	{
	}

	private FileUploadsEntity(FileUploadsDto dto, UserEntity? createdBy, CoreDatabaseContext? databaseContext)
	{
		var dataSource = databaseContext?.DataSources.FirstOrDefault(source => source.Id == dto.DataSourceId);
		DataSource = dataSource ?? throw new ArgumentException($"Property 'DataSource' in FileUploads is not valid: {dto.DataSourceId}");
		DataSourceId = dto.DataSourceId;

		FileName = dto.FileName;
		IsPublic = dto.IsPublic;
		IsDeleted = dto.IsDeleted;
		DeletedAt = dto.DeletedAt;
		DeletedBy = dto.DeletedBy;
		IsReadOnly = dto.IsReadOnly;
		LockId = dto.LockId;
		UserInfo = dto.UserInfo ?? "";

		Created = DateTime.Now;
		CreatedBy = createdBy?.DisplayName;
		LastModified = DateTime.Now;
		LastModifiedBy = createdBy?.DisplayName;

		// Initialize Revision with a new GUID if it's empty
		if (Revision == Guid.Empty)
		{
			Revision = Guid.NewGuid();
			System.Diagnostics.Debug.WriteLine($"[ENTITY] FileUploadsEntity constructor initialized Revision to: {Revision}");
		}

        if (databaseContext != null)
		{
			SetContext(databaseContext);
			Touch();
		}
	}

	/// <inheritdoc />
	public static FileUploadsEntity FromDto(FileUploadsDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new FileUploadsEntity(entityInfo, createdBy, context);
	}

	/// <inheritdoc />
	public FileUploadsDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		excludedProperties ??= [];
		var excludedList = excludedProperties.ToList();
		if (!excludedProperties.Contains(nameof(DataFieldEntity.DataSource)))
		{
			excludedList.Add(nameof(DataFieldEntity.DataSource));
			excludedProperties = excludedList.ToArray();
		}

		return new FileUploadsDto(this, excludedProperties, handledObjects)
		{
			FileName = this.FileName,
			IsPublic = this.IsPublic,
			IsDeleted = this.IsDeleted,
			DeletedBy = this.DeletedBy,
			IsReadOnly = this.IsReadOnly,
			LockId = this.LockId,
			UserInfo = this.UserInfo ?? "",
		};
	}

	/// <inheritdoc />
	public void UpdatePartial(FileUploadsDto dto, UserEntity? modifiedBy = null)
	{
		// Ignore name changes, since it serves as key for the storage and can't be changed afterwards!


		IsPublic = dto.IsPublic;
		IsDeleted = dto.IsDeleted;
		DeletedAt = dto.DeletedAt;
		DeletedBy = dto.DeletedBy;
		IsReadOnly = dto.IsReadOnly;
		LastModified = DateTime.Now;
		LastModifiedBy = modifiedBy?.DisplayName;
		LockId = dto.LockId;

		// Update UserInfo if provided
		if (dto.UserInfo != null)
		{
			UserInfo = dto.UserInfo;
		}

		Touch();
	}

	/// <inheritdoc />
	public static IList<ValidationError> Validate(CoreDatabaseContext dbContext, FileUploadsDto dto, Guid? elementId = null)
	{
		var errors = new List<ValidationError>();
		var willCreate = elementId == null;


		if (!willCreate)
			return errors;

		if (string.IsNullOrEmpty(dto.FileName))
			errors.Add(new ValidationError(nameof(FileName), "File name may not be empty!"));

		var dataSourceExists = dbContext.DataSources.Any(dataSourceEntity => dataSourceEntity.Id == dto.DataSourceId);
		if (!dataSourceExists)
			errors.Add(new ValidationError(nameof(DataSourceId), "DataSource is not valid!"));

		return errors;
	}

	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<FileUploadsEntity>()
			.HasOne(file => file.DataSource);

		// Configure FileId as an identity column
		modelBuilder.Entity<FileUploadsEntity>()
			.Property(f => f.FileId)
			.ValueGeneratedOnAdd();

		base.ConfigureModel(modelBuilder, databaseProvider);
	}

    public static Regex DirectoryRegex()
    {
        // Directory name can contain a-zA-Z0-9_-
        return new Regex(@"^[a-zA-Z0-9_-]+$");
    }


}