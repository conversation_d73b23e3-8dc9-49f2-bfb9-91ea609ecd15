using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.Page.Create;
using Levelbuild.Entities.Features.Page.SingleData;
using Levelbuild.Entities.Features.PageView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Entities.Features.Page.MultiData;

/// <summary>
/// configuration entity for a page displaying multiple datasets at once
/// </summary>
public class MultiDataPageEntity : PageEntity, IAdministrationEntity<MultiDataPageEntity, MultiDataPageDto>
{
	/// <summary>
	/// if the result set only contains one dataset, should this dataset be displayed inside the multi data view or should the detail page be opened instead?
	/// </summary>
	public DataOpeningType? SingleRecordBehaviour { get; set; }
	
	/// <summary>
	/// reference to the CreatePageEntity which is used to create new datasets
	/// </summary>
	public Guid? CreatePageId { get; set; }
	
	/// <summary>
	/// CreatePageEntity which is used to create new datasets
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(CreatePageId))]
	public CreatePageEntity? CreatePage { get; set; }

	/// <summary>
	/// reference to the SingleDataPageEntity which is used to view/edit an existing dataset listed inside the multi data page
	/// </summary>
	public Guid? DetailPageId { get; set; }
	
	/// <summary>
	/// SingleDataPageEntity which is used to view/edit an existing dataset listed inside the multi data page
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(DetailPageId))]
	public SingleDataPageEntity? DetailPage { get; set; }
	
	/// <summary>
	/// filter fields for this page
	/// </summary>
	public ICollection<MultiPageFilterFieldEntity> FilterFields { get; } = new List<MultiPageFilterFieldEntity>();
	
	/// <summary>
	/// Default sorting of the list/table
	/// </summary>
	public virtual ICollection<DataFieldSortingEntity> DefaultSorting { get; } = new List<DataFieldSortingEntity>();
	
	/// <inheritdoc />
	public MultiDataPageEntity()
	{
		Type = PageType.MultiData;
	}

	private MultiDataPageEntity(MultiDataPageDto dto, UserEntity? createdBy, CoreDatabaseContext? databaseContext) : base(dto, createdBy, databaseContext)
	{
		SingleRecordBehaviour = dto.SingleRecordBehaviour;
		if (dto.CreatePageId != null)
			CreatePageId = dto.CreatePageId != "" ? Guid.Parse(dto.CreatePageId) : null;
		if (dto.DetailPageId != null)
			DetailPageId = dto.DetailPageId != "" ? Guid.Parse(dto.DetailPageId) : null;
	}
	
	/// <inheritdoc />
	public static MultiDataPageEntity FromDto(MultiDataPageDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new MultiDataPageEntity(entityInfo, createdBy, context);
	}
	
	/// <inheritdoc />
	public new MultiDataPageDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		excludedProperties ??= [];
		var baseLocalizer = StringLocalizerFactory?.Create("Pages", "", true);
		return new MultiDataPageDto(base.ToDto(excludedProperties, handledObjects))
		{
			NameTranslated = baseLocalizer != null ? baseLocalizer[Name] : string.Empty,
			SingleRecordBehaviour = SingleRecordBehaviour,
			CreatePage = !excludedProperties.Contains(nameof(CreatePage)) ? CreatePage?.ToDto() : null,
			CreatePageId = CreatePageId?.ToString(),
			DetailPage = !excludedProperties.Contains(nameof(DetailPage)) ? DetailPage?.ToDto() : null,
			DetailPageId = DetailPageId?.ToString(),
			DetailPageSlug = DetailPage?.Slug,
			DefaultSorting = DefaultSorting.Select(sorting => sorting.ToDto()).ToList(),
			SortingViewId = Views.FirstOrDefault(view => view.PageId == Id && view.Type == PageViewType.List)?.Id,
			FilterFields = FilterFields.Select(field => field.ToDto()).ToList(),
			DefaultView = !excludedProperties.Contains(nameof(DefaultView)) ? DefaultView?.ToDto([nameof(PageViewEntity.Page)]) : null,
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(MultiDataPageDto dto, UserEntity? modifiedBy = null)
	{
		base.UpdatePartial(dto, modifiedBy);
		if (dto.SingleRecordBehaviour != null)
			SingleRecordBehaviour = dto.SingleRecordBehaviour;
		if (dto.CreatePageId != null)
			CreatePageId = dto.CreatePageId != Guid.Empty.ToString() ? Guid.Parse(dto.CreatePageId) : null;
		if (dto.DetailPageId != null)
			DetailPageId = dto.DetailPageId != Guid.Empty.ToString() ? Guid.Parse(dto.DetailPageId) : null;
	}

	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<MultiDataPageEntity>().ToTable("MultiDataPage");
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
}