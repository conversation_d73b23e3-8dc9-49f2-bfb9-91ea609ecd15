using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Entities.Features.Page.SingleData;

/// <summary>
/// configuration entity for a page displaying a single dataset
/// </summary>
public class SingleDataPageEntity : PageEntity, IAdministrationEntity<SingleDataPageEntity, SingleDataPageDto>
{
	/// <summary>
	/// how should the page be labeled inside the breadcrumb navigation?
	/// </summary>
	public string? BreadcrumbLabel { get; set; }
	
	/// <summary>
	/// a list of HeaderElementEntities used to configure the global header which is intended to display basic information about the dataset 
	/// </summary>
	public ICollection<PageHeaderElementEntity> HeaderElements { get; } = new List<PageHeaderElementEntity>();
	
	/// <inheritdoc />
	public SingleDataPageEntity()
	{
		Type = PageType.SingleData;
	}

	private SingleDataPageEntity(SingleDataPageDto dto, UserEntity? createdBy, CoreDatabaseContext? databaseContext) : base(dto, createdBy, databaseContext)
	{
		BreadcrumbLabel = dto.BreadcrumbLabel;
		
		Views.Add(new GridViewEntity
		{
			Name =  "Overview",
			Icon = "panorama",
			SystemView = true,
			Readonly = true,
			Position = -2
		});
		Views.Add(new GridViewEntity
		{
			Name = "Form",
			Icon = "clipboard-list",
			SystemView = true,
			Position = -1
		});
	}
	
	/// <inheritdoc />
	public static SingleDataPageEntity FromDto(SingleDataPageDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new SingleDataPageEntity(entityInfo, createdBy, context);
	}
	
	/// <inheritdoc />
	public new SingleDataPageDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		var baseLocalizer = StringLocalizerFactory?.Create("Pages", "", true);
		Localizer ??= StringLocalizerFactory?.Create("SingleDataPage", "", true);
		var dto = new SingleDataPageDto(base.ToDto(excludedProperties, handledObjects))
		{
			NameTranslated = baseLocalizer != null ? baseLocalizer[Name] : string.Empty,
			BreadcrumbLabel = BreadcrumbLabel,
			BreadcrumbLabelTranslated = Localizer != null && !BreadcrumbLabel.IsNullOrEmpty() ? Localizer[BreadcrumbLabel!] : string.Empty,
			HeaderElements = new List<PageHeaderElementDto>()
		};

		foreach (var headerElement in HeaderElements)
		{
			dto.HeaderElements.Add(headerElement.ToDto());
		}
		
		return dto;
	}
	
	/// <inheritdoc />
	public void UpdatePartial(SingleDataPageDto dto, UserEntity? modifiedBy = null)
	{
		base.UpdatePartial(dto, modifiedBy);
		if (dto.BreadcrumbLabel != null)
			BreadcrumbLabel = dto.BreadcrumbLabel;
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		base.ConfigureModel(modelBuilder, databaseProvider);
		
		modelBuilder.Entity<SingleDataPageEntity>().ToTable("SingleDataPage");
		
		modelBuilder.Entity<SingleDataPageEntity>()
			.HasMany(page => page.HeaderElements)
			.WithOne(headerElement => headerElement.Page);
	}
}