using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;

namespace Levelbuild.Entities.Features.PageView.GridView;

/// <summary>
/// a grid view configuration is used to configure a single data view which displays the values of the dataset as elements positioned inside a grid
/// </summary>
public class GridViewEntity : PageViewEntity, IAdministrationEntity<GridViewEntity, GridViewDto>
{
	/// <summary>
	/// should the viewer be visible as the last column of the view?
	/// </summary>
	public bool ShowViewer { get; set; }
	
	/// <summary>
	/// annotation view id
	/// </summary>
	public Guid? AnnotationViewId { get; set; }
	
	/// <summary>
	/// which view should be used when maximizing the blueprint viewer and listing the contained annotations?
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(AnnotationViewId))]
	public PageViewEntity? AnnotationView { get; set; }
	
	/// <summary>
	/// is this a readonly view (which displays all values as text instead of using inputs)
	/// </summary>
	public bool Readonly { get; init; }
	
	/// <summary>
	/// how many columns should the view have?
	/// </summary>
	public int ColumnCount { get; set; } = 1;
	
	/// <summary>
	///  is there a minimum required width for the first column? (in px)
	/// </summary>
	public int ColumnOneMinWidth { get; set; }
	
	/// <summary>
	/// how wide should the first column be in comparison with the other visible columns?
	/// </summary>
	public int ColumnOneRatio { get; set; } = 1;
	
	/// <summary>
	///  is there a minimum required width for the second column? (in px)
	/// </summary>
	public int ColumnTwoMinWidth { get; set; }
	
	/// <summary>
	/// how wide should the second column be in comparison with the other visible columns?
	/// </summary>
	public int ColumnTwoRatio { get; set; } = 1;
	
	/// <summary>
	///  is there a minimum required width for the third column? (in px)
	/// </summary>
	public int ColumnThreeMinWidth { get; set; }
	
	/// <summary>
	/// how wide should the third column be in comparison with the other visible columns?
	/// </summary>
	public int ColumnThreeRatio { get; set; } = 1;
	
	/// <summary>
	/// how wide should the viewer be in comparison with the visible column?
	/// </summary>
	public int ViewerRatio { get; set; } = 1;
	
	/// <summary>
	/// references the sections contained in this grid view
	/// </summary>
	public ICollection<GridViewSectionEntity> Sections { get; } = new List<GridViewSectionEntity>();
	
	/// <summary>
	/// references the pages embedded into this grid view
	/// </summary>
	public ICollection<GridViewPageEntity> Pages { get; } = new List<GridViewPageEntity>();
	
	/// <summary>
	/// constructor
	/// </summary>
	public GridViewEntity()
	{
		Type = PageViewType.Grid;
		Created = DateTime.Now;
		CreatedBy = "System";
		Display = true;
	}
	
	private GridViewEntity(GridViewDto dto, UserEntity? createdBy, CoreDatabaseContext? context = null) : base(dto, createdBy, context)
	{
		Readonly = dto.Readonly;
	}
	
	/// <inheritdoc />
	public static GridViewEntity FromDto(GridViewDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new GridViewEntity(entityInfo, createdBy, context);
	}
	
	/// <inheritdoc />
	public new GridViewDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		Localizer ??= StringLocalizerFactory?.Create("PageViews","", true);
		TypeLocalizer ??= StringLocalizerFactory?.Create("PageViewType", "");
		return new GridViewDto(this, excludedProperties, handledObjects)
		{
			NameTranslated = Localizer != null ? Localizer[Name] : string.Empty,
			TypeName = TypeLocalizer != null ? TypeLocalizer[Type.ToString()] : "",
			Icon = Icon ?? string.Empty,
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(GridViewDto dto, UserEntity? modifiedBy = null)
	{
		base.UpdatePartial(dto, modifiedBy);
		if (dto.ColumnCount > 0)
			ColumnCount = dto.ColumnCount;
		if (dto.ShowViewer != null)
			ShowViewer = dto.ShowViewer.Value;
		
		if (dto.ColumnOneRatio is > 0 and <= 3)
			ColumnOneRatio = dto.ColumnOneRatio.Value;
		if (dto.ColumnTwoRatio is > 0 and <= 3)
			ColumnTwoRatio = dto.ColumnTwoRatio.Value;
		if (dto.ColumnThreeRatio is > 0 and <= 3)
			ColumnThreeRatio = dto.ColumnThreeRatio.Value;
		if (dto.ViewerRatio is > 0 and <= 3)
			ViewerRatio = dto.ViewerRatio.Value;
		
		if (dto.ColumnOneMinWidth >= 0)
			ColumnOneMinWidth = dto.ColumnOneMinWidth.Value;
		if (dto.ColumnTwoMinWidth >= 0)
			ColumnTwoMinWidth = dto.ColumnTwoMinWidth.Value;
		if (dto.ColumnThreeMinWidth >= 0)
			ColumnThreeMinWidth = dto.ColumnThreeMinWidth.Value;
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<GridViewEntity>().ToTable("GridViews");
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
}