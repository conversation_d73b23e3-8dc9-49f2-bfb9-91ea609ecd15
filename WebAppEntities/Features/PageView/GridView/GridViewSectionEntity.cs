using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;

namespace Levelbuild.Entities.Features.PageView.GridView;

/// <summary>
/// configuration entity of a section which is part of a GridViewEntity
/// </summary>
public class GridViewSectionEntity : RevisedPersistentEntity<GridViewSectionEntity>, ISortableEntity, IAdministrationEntity<GridViewSectionEntity, GridViewSectionDto>, IStartupMigration
{
	/// <summary>
	/// reference to the GridViewEntity the element is embedded into
	/// </summary>
	[Required]
	public Guid GridViewId { get; init; }
	
	/// <summary>
	/// GridViewEntity the section is embedded into
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(GridViewId))]
	public GridViewEntity? GridView { get; init; }
	
	/// <summary>
	/// GridViewColumn number the section is embedded into
	/// </summary>
	[Required]
	public int GridViewColumn { get; set; }
	
	/// <summary>
	/// determines the display order inside the column
	/// </summary>
	[Required]
	public int Position { get; set; }

	/// <summary>
	/// gets automatically updated whenever a Field, Text or Page changes 
	/// </summary>
	public int RowCount { get; set; } = -1; // -1 is used to detect non-migrated entities
	
	/// <summary>
	/// labeling / headline of the section
	/// </summary>
	public string? Title { get; set; }
	
	/// <summary>
	/// should the headline be hidden?
	/// </summary>
	public bool ShowTitle { get; set; } = true;
	
	/// <summary>
	/// is the user allowed to collapse the section so that just the headline stays visible
	/// </summary>
	public bool AllowMinimize { get; set; } = true;
	
	/// <summary>
	/// should the section be displayed collapsed when the user enters the page?
	/// </summary>
	public bool StartMinimized { get; set; }
	
	/// <summary>
	/// list of field elements which are contained inside the section
	/// </summary>
	public ICollection<GridViewFieldEntity> Fields { get; } = new List<GridViewFieldEntity>();
	
	/// <summary>
	/// list of text elements which are contained inside the section
	/// </summary>
	public ICollection<GridViewTextEntity> Texts { get; } = new List<GridViewTextEntity>();
	
	/// <summary>
	/// list of pages which are embedded inside the section
	/// </summary>
	public ICollection<GridViewPageEntity> Pages { get; } = new List<GridViewPageEntity>();
	
	private IStringLocalizer? _localizer;
	
	/// <summary>
	/// constructor
	/// </summary>
	public GridViewSectionEntity()
	{
	}
	
	private GridViewSectionEntity(GridViewSectionDto dto, CoreDatabaseContext? context = null)
	{
		if (dto.GridViewId.HasValue)
			GridViewId = dto.GridViewId.Value;
		if (dto.GridViewColumn.HasValue)
			GridViewColumn = dto.GridViewColumn.Value;
		if (dto.Position.HasValue)
			Position = dto.Position.Value;
		Title = !string.IsNullOrEmpty(dto.Title) ? dto.Title : "newSection";
		RowCount = 0;

		if (context != null)
		{
			SetContext(context);
			Touch();
		}
	}
	
	/// <inheritdoc />
	public static GridViewSectionEntity FromDto(GridViewSectionDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new GridViewSectionEntity(entityInfo);
	}
	
	/// <inheritdoc />
	public GridViewSectionDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		_localizer ??= StringLocalizerFactory?.Create("GridViewSection", "", true);
		
		return new GridViewSectionDto(this, excludedProperties, handledObjects)
		{
			TitleTranslated = _localizer != null && !string.IsNullOrEmpty(Title) ? _localizer[Title] : "",
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(GridViewSectionDto dto, UserEntity? modifiedBy = null)
	{
		if (dto.Position.HasValue)
			Position = dto.Position.Value;
		if (dto.GridViewColumn.HasValue)
			GridViewColumn = dto.GridViewColumn.Value;
		if (dto.Title != null)
			Title = dto.Title;
		if (dto.ShowTitle.HasValue)
			ShowTitle = dto.ShowTitle.Value;
		if (dto.AllowMinimize.HasValue)
			AllowMinimize = dto.AllowMinimize.Value;
		if (dto.StartMinimized.HasValue)
			StartMinimized = dto.StartMinimized.Value;
		Touch();
	}
	
	/// <summary>
	/// updates the number of used rows for a given section
	/// </summary>
	/// <param name="databaseContext">db context to use</param>
	/// <param name="sectionId">id of the GridViewSectionEntity to update</param>
	public static void UpdateRowCount(CoreDatabaseContext databaseContext, Guid? sectionId)
	{
		if (sectionId == null)
			return;
		
		var sectionEntity = databaseContext.GridViewSections
			.Include(section => section.GridView)
			.Include(section => section.Pages)
			.Include(section => section.Fields)
			.Include(section => section.Texts)
			.AsSplitQuery().FirstOrDefault(section => section.Id == sectionId);
		
		if (sectionEntity == null)
			return;
		
		sectionEntity.Touch();
		sectionEntity.UpdateRowCount();
		databaseContext.SaveChanges();
	}

	private void UpdateRowCount()
	{
		var maxRow = Pages.Count > 0 ? Pages.Max(page => page.RowEnd)-1 : 1;
		if (Fields.Count > 0)
			maxRow = Math.Max(maxRow, Fields.Max(field => field.RowEnd)-1);
		if (Texts.Count > 0)
			maxRow = Math.Max(maxRow, Texts.Max(text => text.RowEnd)-1);
		RowCount = maxRow;
	}
	
	/// <summary>
	/// Touch the parent view to mark it as changed
	/// </summary>
	public new void Touch()
	{
		base.Touch();
		if (GridView != null)
			GridView.Touch();
		else
			DbContext?.PageViews.Find(GridViewId)?.Touch();
	}

	#region Startup Migrations

	/// <inheritdoc />
	public static void RegisterMigrations(ref readonly Dictionary<string, Func<ILogger, CoreDatabaseContext, IServiceProvider, Task>> migrationRegistry)
	{
		migrationRegistry.Add(nameof(InitRowCounts), InitRowCounts);
	}
	
	private static async Task InitRowCounts(ILogger logger, CoreDatabaseContext databaseContext, IServiceProvider serviceProvider)
	{
		logger.Information("Adding RowCount to Sections...");

		// Device present but no slug? -> Needs to be set!
		var sectionsToHandle = await databaseContext.GridViewSections
			.Include(section => section.Pages)
			.Include(section => section.Fields)
			.Include(section => section.Texts)
			.Where(section => section.RowCount == -1)
			.AsSplitQuery()
			.ToListAsync();

		sectionsToHandle.ForEach(section => section.UpdateRowCount());
		await databaseContext.SaveChangesAsync();
		logger.Information("Section updates done. Affected entries: {Count}", sectionsToHandle.Count);
	}

	#endregion
}