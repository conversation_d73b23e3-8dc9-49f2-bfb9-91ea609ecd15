using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Entities.Features.PageView.GridView;

/// <summary>
/// configuration for a text element which is embedded into a GridViewSectionEntity
/// </summary>
public class GridViewTextEntity : GridViewSectionElementEntity, IAdministrationEntity<GridViewTextEntity, GridViewTextDto>
{
	/// <summary>
	/// label which is used for administrative purposes (reference in rules etc.)
	/// </summary>
	public string Label { get; set; } = "";
	
	/// <summary>
	/// the actual text to display
	/// </summary>
	[Text]
	public string Text { get; set; } = "";
	
	/// <summary>
	/// defines how to display the text element
	/// </summary>
	public GridViewTextType TextType { get; set; } = GridViewTextType.Headline1;
	
	/// <summary>
	/// should the text be displayed in a specific color
	/// </summary>
	[ShortString]
	public string? Color { get; set; }
	
	/// <summary>
	/// should the text be specifically aligned?
	/// </summary>
	public Alignment TextAlign { get; set; } = Alignment.Left;
	
	private IStringLocalizer? _localizer;
	private IStringLocalizer? _typeLocalizer;
	
	/// <inheritdoc />
	public GridViewTextEntity()
	{
		Type = GridViewSectionElementType.Text;
	}
	
	private GridViewTextEntity(GridViewTextDto dto, CoreDatabaseContext? databaseContext) : base(dto)
	{
		Type = GridViewSectionElementType.Text;
		TextType = dto.TextType ?? GridViewTextType.Headline1;
	}
	
	/// <inheritdoc />
	public static GridViewTextEntity FromDto(GridViewTextDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new GridViewTextEntity(entityInfo, context);
	}
	
	/// <inheritdoc />
	public new GridViewTextDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		_localizer ??= StringLocalizerFactory?.Create("GridViewText", "", true);
		_typeLocalizer ??= StringLocalizerFactory?.Create("GridViewTextType", "");
		var typeTranslated = _typeLocalizer != null ? _typeLocalizer[TextType.ToString()] : "";
		
		return new GridViewTextDto(this, excludedProperties, handledObjects)
		{
			LabelTranslated = _localizer != null ? _localizer[Label] : string.Empty,
			TextTranslated = _localizer != null && !Text.IsNullOrEmpty() ? _localizer[Text] : typeTranslated,
			TextTypeTranslated = typeTranslated,
			Flag = Label != "" ? Label : typeTranslated
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(GridViewTextDto dto, UserEntity? modifiedBy = null)
	{
		if (dto.SectionId.HasValue)
			SectionId = dto.SectionId.Value;
		if (dto.RowStart > -1)
			RowStart = dto.RowStart;
		if (dto.RowEnd > -1)
			RowEnd = dto.RowEnd;
		if (dto.ColStart > -1)
			ColStart = dto.ColStart;
		if (dto.ColEnd > -1)
			ColEnd = dto.ColEnd;
		
		if (dto.Label != null)
			Label = dto.Label;
		if (dto.Text != null)
			Text = dto.Text;
		if (dto.TextType != null)
			TextType = dto.TextType.Value;
		if (dto.TextAlign != null)
			TextAlign = dto.TextAlign.Value;
		if (dto.Color != null)
			Color = dto.Color;
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<GridViewTextEntity>().ToTable("GridViewTexts");
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
}