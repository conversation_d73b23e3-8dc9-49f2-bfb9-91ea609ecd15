using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.Extensions.Localization;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Entities.Features.PageView.ListView;

/// <summary>
/// describes a single column of the list view
/// </summary>
public class ListViewColumnEntity : PersistentEntity<ListViewColumnEntity>, IAdministrationEntity<ListViewColumnEntity, ListViewColumnDto>
{
	/// <summary>
	/// reference to the list view
	/// </summary>
	[Required]
	public Guid ListViewId { get; init; }

	/// <summary>
	/// reference to the list view
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(ListViewId))]
	public ListViewEntity? ListView { get; init; }

	/// <summary>
	/// field to display
	/// </summary>
	public Guid FieldId { get; init; }

	/// <summary>
	/// field to display
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(FieldId))]
	public DataFieldEntity? Field { get; init; }

	/// <summary>
	/// position of the column
	/// </summary>
	public int? Position { get; set; }
	
	/// <summary>
	/// label used inside the column header
	/// </summary>
	[ShortString]
	public string? Label { get; set; }
	
	/// <summary>
	/// should the column be displayed?
	/// </summary>
	public bool Display { get; set; } = true;
	
	/// <summary>
	/// is the user allowed to edit the values of this column? 
	/// </summary>
	public bool AllowEdit { get; set; }

	private IStringLocalizer? _localizer;

	/// <inheritdoc />
	public ListViewColumnEntity()
	{
	}

	private ListViewColumnEntity(ListViewColumnDto dto)
	{
		ListViewId = dto.ListViewId;
		FieldId = dto.FieldId;
		Position = dto.Position;
	}
	
	/// <inheritdoc />
	public static ListViewColumnEntity FromDto(ListViewColumnDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		var fieldEntity = context?.DataFields.Find(entityInfo.FieldId);
		return new ListViewColumnEntity(entityInfo)
		{
			Label = fieldEntity?.Name
		};
	}
	
	/// <inheritdoc />
	public ListViewColumnDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		_localizer ??= StringLocalizerFactory?.Create("ListViewColumn", "", true);
		return new ListViewColumnDto(this, excludedProperties, handledObjects)
		{
			Slug = Field?.Slug ?? string.Empty,
			FieldName = Field?.Name ?? string.Empty,
			FieldTypeName = Field?.Type.ToString() ?? string.Empty,
			LabelTranslated = _localizer != null && !Label.IsNullOrEmpty() ? _localizer[Label!] : string.Empty,
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(ListViewColumnDto dto, UserEntity? modifiedBy = null)
	{
		if (dto.Position != null)
			Position = dto.Position;
		if (dto.Label != null)
			Label = dto.Label;
		if (dto.Display != null)
			Display = dto.Display.Value;
		if (dto.AllowEdit != null)
			AllowEdit = dto.AllowEdit.Value;
	}
}