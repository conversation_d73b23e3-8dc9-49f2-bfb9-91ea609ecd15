using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Levelbuild.Entities.Features.PageView;

/// <summary>
/// a page view configuration is the base class for every view which is embedded into a page
/// </summary>
[PrimaryKey(nameof(Id))]
[Index(nameof(PageId), nameof(Name), IsUnique = true)]
public class PageViewEntity : RevisedPersistentEntity<PageViewEntity>, IAdministrationEntity<PageViewEntity, PageViewDto>, IPolymorphicParentEntity
{
	/// <summary>
	/// used inside the URL to address the item
	/// </summary>
	[ShortString]
	public string Slug { get; private set; }
	
	private string _name;
	
	/// <summary>
	/// name of the view
	/// </summary>
	[ShortString]
	public string Name
	{
		get => _name;
		set
		{
			_name = value;
			Slug = Name.ToSlug();
		}
	}
	
	/// <summary>
	/// type of the view (depending on the type of the page the view belongs to, there are different view types available)
	/// </summary>
	public PageViewType Type { get; init; }
	
	/// <summary>
	/// reference to the PAgeEntity the view is based on
	/// </summary>
	public Guid PageId { get; init; }
	
	/// <summary>
	/// PageEntity the view is based on
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(PageId))]
	public virtual PageEntity Page { get; init; }
	
	/// <summary>
	/// description for the page
	/// </summary>
	[Text]
	public string Description { get; set; } = "";
	
	/// <summary>
	/// should te view be visible to the end user?
	/// </summary>
	public bool Display { get; set; } = true;

	/// <summary>
	/// when was the view configuration created? 
	/// </summary>
	public DateTime? Created { get; init; }
	
	/// <summary>
	/// who created the view configuration?
	/// </summary>
	public string? CreatedBy { get; init; }
	
	/// <summary>
	/// when was the view configuration last modified?
	/// </summary>
	public DateTime? LastModified { get; set; }
	
	/// <summary>
	/// who modified the view configuration?
	/// </summary>
	public string? LastModifiedBy { get; set; }
	
	/// <summary>
	/// is this a view automatically created by the system which can't be deleted (for example overview and form inside single data page)
	/// </summary>
	public bool SystemView { get; init; }
	
	/// <summary>
	/// determines the display order when a page contains multiple views
	/// </summary>
	public int Position { get; set; }
	
	/// <summary>
	/// should the view be visible to users
	/// </summary>
	public bool Enabled { get; init; } = true;

	/// <summary>
	/// should this view have expert features included?
	/// </summary>
	public bool ExpertMode { get; set; }
	
	/// <summary>
	/// which icon should be used in conjunction with the (translated) view name?
	/// </summary>
	[ShortString]
	public string? Icon { get; set; }
	
	protected IStringLocalizer? Localizer;
	
	protected IStringLocalizer? TypeLocalizer;
	
	/// <inheritdoc />
	public PageViewEntity()
	{
	}
	
	/// <inheritdoc />
	protected PageViewEntity(PageViewDto dto, UserEntity? createdBy, CoreDatabaseContext? context = null)
	{
		Name = dto.Name ?? "";
		Type = dto.Type;
		PageId = dto.PageId;
		Created = DateTime.Now.ToUniversalTime();
		CreatedBy = createdBy?.DisplayName;
		Description = dto.Description ?? "";
		Display = dto.Display ?? true;
		ExpertMode = dto.ExpertMode ?? false;

		if (context != null)
		{
			SetContext(context);
			Touch();
		}
	}
	
	/// <inheritdoc />
	public static PageViewEntity FromDto(PageViewDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new PageViewEntity(entityInfo, createdBy, context);
	}
	
	/// <inheritdoc />
	public PageViewDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		Localizer ??= StringLocalizerFactory?.Create("PageViews", "", true);
		TypeLocalizer ??= StringLocalizerFactory?.Create("PageViewType", "");
		return new PageViewDto(this, excludedProperties, handledObjects)
		{
			NameTranslated = Localizer != null ? Localizer[Name] : string.Empty,
			TypeName = TypeLocalizer != null ? TypeLocalizer[Type.ToString()] : "",
			Icon = Icon ?? Type.GetIcon()
		};
	}
	
	/// <inheritdoc />
	public EntityDto ToRealDto(CoreDatabaseContext databaseContext, out Type realType, params string[] includes)
	{
		switch (Type)
		{
			case PageViewType.List:
				// Remove Columns include if present (should not happen)
				if (includes.Contains(nameof(ListViewDto.Columns)))
					includes = includes.Where(include => include != nameof(ListViewDto.Columns)).ToArray();
				
				var listViewQuery = includes.Aggregate(
					databaseContext.ListViews.AsQueryable(), 
					(current, include) => current.Include(include)
				);
				
				// Always include ListViewColumns here
				listViewQuery = listViewQuery
					.Include(entity => entity.Columns)
					.ThenInclude(entity => entity.Field)
					.AsSplitQuery();
				
				var listViewEntity = listViewQuery.First(entity => entity.Id == Id);
				var listViewDto = listViewEntity.ToDto();
				listViewDto.Columns = listViewEntity.Columns.Select(column => column.ToDto()).ToList();
				
				realType = typeof(ListViewDto);
				return listViewDto;
			case PageViewType.Gallery:
				var galleryViewQuery = includes.Aggregate(
					databaseContext.GalleryViews.AsQueryable(), 
					(current, include) => current.Include(include)
				);
				
				realType = typeof(GalleryViewDto);
				return galleryViewQuery.First(entity => entity.Id == Id).ToDto();
			case PageViewType.Grid:
				// Remove Sections includes if present (should not happen)
				if (includes.Contains(nameof(GridViewDto.Sections)))
					includes = includes.Where(include => include != nameof(GridViewDto.Sections)).ToArray();
				
				var gridViewQuery = includes.Aggregate(
					databaseContext.GridViews.AsQueryable(),
					(current, include) => current.Include(include)
				);
				
				gridViewQuery = gridViewQuery
					.Include(gridView => gridView.Sections)
						.ThenInclude(section => section.Fields)
					.Include(gridView => gridView.Sections)
						.ThenInclude(section => section.Texts)
					.Include(gridView => gridView.Sections)
						.ThenInclude(section => section.Pages)
							.ThenInclude(page => page.Filters)
					.Include(gridView => gridView.Sections)
						.ThenInclude(section => section.Pages)
							.ThenInclude(page => page.EmbeddedPage)
					.Include(gridView => gridView.Pages)
						.ThenInclude(page => page.Filters)
					.Include(gridView => gridView.Pages)
						.ThenInclude(page => page.EmbeddedPage)
					.AsSplitQuery()
					.AsNoTracking();
				
				realType = typeof(GridViewDto);
				return gridViewQuery.First(entity => entity.Id == Id).ToDto();
		}
		
		throw new NotSupportedException($"Type \"{Type.ToString()}\" is not supported!");
	}
	
	/// <inheritdoc />
	public void UpdatePartial(PageViewDto dto, UserEntity? modifiedBy = null)
	{
		if (dto.Name != null)
			Name = dto.Name;
		if (dto.Description != null)
			Description = dto.Description ?? "";
		if (dto.Display != null)
			Display = dto.Display.Value;
		if (dto.Icon != null)
			Icon = dto.Icon;
		if (dto.Position >= 0)
			Position = dto.Position.Value;
		if (dto.ExpertMode != null)
			ExpertMode = dto.ExpertMode.Value;

		LastModified = DateTime.Now.ToUniversalTime();
		LastModifiedBy = modifiedBy?.DisplayName;
		Touch();
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<PageViewEntity>().ToTable("PageViews");
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
	
	/// <summary>
	/// Update the Revision id to detect that the entity was changed
	/// </summary>
	public new void Touch()
	{
		base.Touch();
		DbContext?.Pages.Find(PageId)?.Touch();
	}
}