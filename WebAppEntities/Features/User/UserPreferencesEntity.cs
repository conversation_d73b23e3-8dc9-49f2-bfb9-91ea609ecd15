using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.User;
using Levelbuild.Entities.Features.Localization;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Entities.Features.User;

/// <summary>
/// Subset of the Users table, split for convenience.
///
/// Cannot exist on its own and shares its ID with the user.
/// </summary>
public class UserPreferencesEntity : PersistentEntity<UserPreferencesEntity>, IAdministrationEntity<UserPreferencesEntity, UserPreferencesDto>,
									 IConvertibleEntity<UserPreferencesDto, IDictionary<string, object>>
{
	#region Properties
	
	/// <summary>
	/// The selected culture option.
	///
	/// Decides whether culture is same as the used device's culture, or set to a fixed one.
	/// </summary>
	public CultureOptionType CultureOptionType { get; set; } = CultureOptionType.Device;
	
	/// <summary>
	/// The Id of the user's current culture.
	/// </summary>
	public Guid? CurrentCultureId { get; set; }
	
	/// <summary>
	/// The user's current culture.
	/// </summary>
	[ForeignKey(nameof(CurrentCultureId))]
	public CultureEntity? CurrentCulture { get; set; }
	
	#endregion
	
	#region Constructors
	
	/// <summary>
	/// Constructor for EF Core.
	/// </summary>
	public UserPreferencesEntity()
	{
		// nothing
	}
	
	/// <summary>
	/// Constructor for <see cref="FromDto"/>.
	/// </summary>
	/// <param name="dto"></param>
	private UserPreferencesEntity(UserPreferencesDto dto)
	{
		CultureOptionType = dto.CultureOptionType ?? CultureOptionType.Device;
		if(dto.CurrentCultureId != null)
			CurrentCultureId = Guid.Parse(dto.CurrentCultureId);
	}
	
	#endregion
	
	#region Methods
	
	/// <inheritdoc />
	public UserPreferencesDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new UserPreferencesDto(this, excludedProperties, handledObjects)
		{
			CurrentCultureId = CurrentCultureId?.ToString()
		};
	}
	
	/// <inheritdoc />
	public UserPreferencesDto ToDto(IDictionary<string, object> additionalData, string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new UserPreferencesDto(this, excludedProperties, handledObjects)
		{
			CurrentCultureId = CurrentCultureId?.ToString()
		};
	}
	
	/// <inheritdoc />
	public static UserPreferencesEntity FromDto(UserPreferencesDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new UserPreferencesEntity(entityInfo);
	}
	
	/// <inheritdoc />
	public void UpdatePartial(UserPreferencesDto dto, UserEntity? modifiedBy = null)
	{
		if (!string.IsNullOrEmpty(dto.CurrentCultureId))
			CurrentCultureId = Guid.Parse(dto.CurrentCultureId);
		if (dto.CultureOptionType != null)
			CultureOptionType = dto.CultureOptionType.Value;
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<UserPreferencesEntity>(preferences =>
		{
			preferences.ToTable("Users");
		});
		
		modelBuilder.Entity<UserPreferencesEntity>()
			.HasOne(preferences => preferences.CurrentCulture);
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
	
	#endregion
	
}