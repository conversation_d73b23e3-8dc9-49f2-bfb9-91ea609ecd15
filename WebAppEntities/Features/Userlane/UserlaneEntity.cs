using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities.Features.Module;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.UserlaneStep;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.Userlane;
    /// <summary>
    /// Represents a user lane entity in the system.
    /// </summary>
    public class UserlaneEntity : PersistentEntity<UserlaneEntity>, IAdministrationEntity<UserlaneEntity, UserlaneDto>
    {
        /// <summary>
        /// Gets or sets the type of the user lane.
        /// </summary>
        [Required]
		public required UserlaneType Type { get; set; }

		/// <summary>
		/// Get or sets the Name of the user lane.
		/// </summary>
		[Required]
		public string? Name  { get; set; }
		
		/// <summary>
		/// Get or sets the TesterRole of the user lane.
		/// </summary>
		public string TesterRole { get; set; }
		
		/// <summary>
		/// Get or sets the ClientContext of the user lane.
		/// </summary>
		public string? ClientContext { get; set; }
		
		/// <summary>
        /// Gets or sets the speed of the user lane.
        /// </summary>
		[Required]
        public int? Speed { get; set; }
		
		/// <summary>
		/// Gets or sets the StartPoint of the user lane.
		/// </summary>
		public required string StartPoint { get; set; }
		
		/// <summary>
		/// Gets or sets the PageId of the user lane.
		/// </summary>
		public required string PageId { get; set; }
		
		/// <summary>
		/// Gets or sets the ModuleId of the user lane.
		/// </summary>
		public Guid ModuleId { get; set; }
		
		/// <summary>
		/// Gets or sets the Module of the user lane.
		/// </summary>
		[CascadeDelete]
		[ForeignKey(nameof(ModuleId))]
		public ModuleEntity? Module { get; set; }
		
		/// <summary>
		/// 
		/// </summary>
		public ICollection<UserlaneStepEntity>? Steps { get; init; }

		/// <inheritdoc />
		public UserlaneEntity()
		{
		}

		/// <inheritdoc />
		public static UserlaneEntity FromDto(UserlaneDto entityDto, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
		{
			return new UserlaneEntity
			{
				Name = entityDto.Name,
				Type = entityDto.Type,
				Speed = entityDto.Speed == 0 ? 0 : entityDto.Speed,
				PageId = entityDto.PageId,
				StartPoint = entityDto.StartPoint,
				TesterRole = entityDto.TesterRole,
				ClientContext = entityDto.ClientContext,
				ModuleId = entityDto.ModuleId
			};
		}


		/// <summary>
		/// Converts the entity to a DTO (Data Transfer Object).
		/// </summary>
		/// <param name="excludedProperties">Properties to exclude during conversion.</param>
		/// <param name="handledObjects"></param>
		/// <returns>The corresponding DTO.</returns>
		/// 
		public UserlaneDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
		{
			return new UserlaneDto(this, excludedProperties)
			{
				Name = Name ?? string.Empty,
				PageId = PageId,
				StartPoint = StartPoint,
				Speed = Speed ?? 0,
				Type = Type,
				TesterRole = TesterRole,
				ClientContext = ClientContext,
				ModuleId = ModuleId
			};
		}


		/// <inheritdoc />
		/// <summary>
		/// Updates the current entity with values from the given DTO.
		/// </summary>
		/// <param name="dto">The DTO containing updated values.</param>
		/// <param name="modifiedBy">The user who modified the entity (optional).</param>
		public void UpdatePartial(UserlaneDto dto, UserEntity? modifiedBy = null)
		{
			// Note: ModuleId is excluded - userlanes cannot be moved between modules after creation
			Name = dto.Name;
			Type = dto.Type;
			Speed = dto.Speed == 0 ? 1000 : dto.Speed;
			StartPoint = dto.StartPoint ?? string.Empty;
			PageId = dto.PageId;
			TesterRole = dto.TesterRole;
			ClientContext = dto.ClientContext;
		}

    }