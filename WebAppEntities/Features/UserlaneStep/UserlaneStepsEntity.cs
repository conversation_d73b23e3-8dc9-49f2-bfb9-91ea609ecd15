using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.Userlane;
using Levelbuild.Entities.Features.UserlaneStepAction;
using Levelbuild.Entities.Features.UserlaneStepTestCondition;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.UserlaneStep;

/// <inheritdoc cref="UserlaneStepDto" />
public class UserlaneStepEntity : PersistentEntity<UserlaneStepEntity>, IAdministrationEntity<UserlaneStepEntity, UserlaneStepDto>
{
		
	/// <summary>
	/// 
	/// </summary>
	public required Guid UserlaneId { get; set; }
		
	/// <summary>
	/// 
	/// </summary>
	public required int Order { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	public string? TargetElement { get; set; }

	/// <summary>
	/// 
	/// </summary>
	public required string? Title { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	public required string? Description { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	public int Delay { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	public ICollection<UserlaneStepActionEntity>? Actions { get; init; }
	
	/// <summary>
	/// 
	/// </summary>
	public ICollection<UserlaneStepTestConditionEntity>? TestConditions { get; init; }
	
	/// <summary>
	/// 
	/// </summary>
	public virtual UserlaneEntity? Userlane { get; init; }

	/// <inheritdoc />
	public UserlaneStepEntity()
	{
			
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="excludedProperties"></param>
	/// <param name="handledObjects"></param>
	/// <returns></returns>
	public UserlaneStepDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new UserlaneStepDto(this, excludedProperties!)
		{
			UserlaneId = UserlaneId,
			Order = Order
		};
	}

	/// <inheritdoc />
	public static UserlaneStepEntity FromDto(UserlaneStepDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new UserlaneStepEntity
		{
			UserlaneId = entityInfo.UserlaneId,
			TargetElement = entityInfo.TargetElement,
			Title = entityInfo.Title,
			Description = entityInfo.Description,
			Delay = entityInfo.Delay,
			Order = entityInfo.Order,
		};
	}

	/// <inheritdoc />
	public void UpdatePartial(UserlaneStepDto dto, UserEntity? modifiedBy = null)
	{
		TargetElement = dto.TargetElement;
		Title = dto.Title;
		Description = dto.Description;
		Delay = dto.Delay;
		Order = dto.Order;
		UserlaneId = dto.UserlaneId;
	}
}