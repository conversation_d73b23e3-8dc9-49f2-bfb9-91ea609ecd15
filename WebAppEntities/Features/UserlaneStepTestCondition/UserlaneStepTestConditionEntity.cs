using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.UserlaneStep;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.UserlaneStepTestCondition;

/// <inheritdoc cref="UserlaneStepTestConditionDto" />
public class UserlaneStepTestConditionEntity : PersistentEntity<UserlaneStepTestConditionEntity>, IAdministrationEntity<UserlaneStepTestConditionEntity, UserlaneStepTestConditionDto>
{
		
	/// <summary>
	/// The UserlaneStep this test condition belongs to
	/// </summary>
	public required Guid UserlaneStepId { get; set; }
		
	/// <summary>
	/// Order of execution for this test condition
	/// </summary>
	public int Order { get; set; }
	
	/// <summary>
	/// Type of test condition (FieldComparison, ElementExists, etc.)
	/// </summary>
	public required UserlaneStepTestConditionType ConditionType { get; set; }
	
	/// <summary>
	/// Field to test (for field comparison conditions)
	/// </summary>
	public string? Field { get; set; }
	
	/// <summary>
	/// Comparison operator (Equals, NotEquals, etc.)
	/// </summary>
	public CompareOperator? Operator { get; set; }
	
	/// <summary>
	/// Value to compare against
	/// </summary>
	public string? Value { get; set; }
	
	/// <summary>
	/// Logical operator to combine with next condition (AND/OR)
	/// </summary>
	public string? LogicalOperator { get; set; }
		
	/// <summary>
	/// Navigation property to the parent UserlaneStep
	/// </summary>
	public virtual UserlaneStepEntity? UserlaneStep { get; init; }

	/// <inheritdoc />
	public UserlaneStepTestConditionEntity()
	{
	}

	/// <inheritdoc />
	public UserlaneStepTestConditionDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new UserlaneStepTestConditionDto(this, excludedProperties!)
		{
			UserlaneStepId = UserlaneStepId,
			ConditionType = ConditionType,
		};
	}

	/// <inheritdoc />
	public static UserlaneStepTestConditionEntity FromDto(UserlaneStepTestConditionDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new UserlaneStepTestConditionEntity
		{
			UserlaneStepId = entityInfo.UserlaneStepId,
			Order = entityInfo.Order,
			ConditionType = entityInfo.ConditionType,
			Field = entityInfo.Field,
			Operator = entityInfo.Operator,
			Value = entityInfo.Value,
			LogicalOperator = entityInfo.LogicalOperator,
		};
	}

	/// <inheritdoc />
	public void UpdatePartial(UserlaneStepTestConditionDto dto, UserEntity? modifiedBy = null)
	{
		UserlaneStepId = dto.UserlaneStepId;
		Order = dto.Order;
		ConditionType = dto.ConditionType;
		Field = dto.Field;
		Operator = dto.Operator;
		Value = dto.Value;
		LogicalOperator = dto.LogicalOperator;
	}
}