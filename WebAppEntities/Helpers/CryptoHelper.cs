using System.Security.Cryptography;
using System.Text;

namespace Levelbuild.Entities.Helpers;

/// <summary>
/// Helper class providing cryptographic methods
/// </summary>
public static class CryptoHelper
{
	/// <summary>
	/// Pseudo randomly generate a 32 byte salt
	/// </summary>
	/// <returns></returns>
	public static string GenerateSalt()
	{
		var salt = RandomNumberGenerator.GetBytes(32);
		
		return Convert.ToBase64String(salt);
	}
	
	/// <summary>
	/// RFC 2898 based symmetric encryption.
	///
	/// Source: https://www.ietf.org/rfc/rfc2898.txt
	/// </summary>
	/// <param name="clearText">The string to encrypt.</param>
	/// <param name="encryptionKey">The encryption passphrase that enables decryption afterwards.</param>
	/// <param name="salt">The randomly generated salt.</param>
	/// <returns>The encrypted base64 encoded string.</returns>
	public static string Encrypt(string clearText, string encryptionKey, string salt)
	{
		var clearBytes = Encoding.Unicode.GetBytes(clearText);
		var saltBytes = Encoding.Unicode.GetBytes(salt);
		using var aes = Aes.Create();
		var cryptoBytes = new Rfc2898DeriveBytes(encryptionKey, saltBytes, 16, HashAlgorithmName.SHA256);
		aes.Key = cryptoBytes.GetBytes(32);
		aes.IV = cryptoBytes.GetBytes(16);
		using var memoryStream = new MemoryStream();
		using var encryptor = aes.CreateEncryptor();
		using (var cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
		{
			cryptoStream.Write(clearBytes, 0, clearBytes.Length);
		}
				
		return Convert.ToBase64String(memoryStream.ToArray());
	}
	
	/// <summary>
	/// RFC 2898 based symmetric decryption.
	///
	/// Source: https://www.ietf.org/rfc/rfc2898.txt
	/// </summary>
	/// <param name="cipherText">The string to decrypt.</param>
	/// <param name="encryptionKey">The decryption passphrase used to encrypt the string previously.</param>
	/// <param name="salt">The randomly generated salt.</param>
	/// <returns>The decrypted string.</returns>
	public static string Decrypt(string cipherText, string encryptionKey, string salt)
	{
		cipherText = cipherText.Replace(" ", "+");
		var cipherBytes = Convert.FromBase64String(cipherText);
		var saltBytes = Encoding.Unicode.GetBytes(salt);
		using var aes = Aes.Create();
		var cryptoBytes = new Rfc2898DeriveBytes(encryptionKey, saltBytes, 16, HashAlgorithmName.SHA256);
		aes.Key = cryptoBytes.GetBytes(32);
		aes.IV = cryptoBytes.GetBytes(16);
		using var memoryStream = new MemoryStream();
		using var decryptor = aes.CreateDecryptor();
		using (var cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Write))
		{
			cryptoStream.Write(cipherBytes, 0, cipherBytes.Length);
		}
		return Encoding.Unicode.GetString(memoryStream.ToArray());
	}
	
	/// <summary>
	/// Computes a MD5 checksum of the given file.
	/// </summary>
	/// <param name="fileStream"></param>
	/// <returns></returns>
	public static string ComputeChecksum(FileStream fileStream)
	{
		using var md5 = MD5.Create();
		
		var hash = md5.ComputeHash(fileStream);
		return BitConverter
			.ToString(hash)
			.Replace("-", string.Empty)
			.ToLowerInvariant();
	}
	
	/// <inheritdoc cref="ComputeChecksum"/>
	public static async Task<string> ComputeChecksumAsync(FileStream fileStream)
	{
		using var md5 = MD5.Create();
		
		var hash = await md5.ComputeHashAsync(fileStream);
		return BitConverter
			.ToString(hash)
			.Replace("-", string.Empty)
			.ToLowerInvariant();
	}
}