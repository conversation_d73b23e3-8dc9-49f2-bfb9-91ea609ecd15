using Levelbuild.Core.StorageInterface;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DataStoreContext;

namespace Levelbuild.Entities.Helpers.DataSource;

/// <summary>
/// Helper class that provides methods for Storage-specific DataSource actions.
/// </summary>
public class StorageDataSourceConnectionHelper : DataSourceConnectionHelper
{
	/// <inheritdoc />
	public StorageDataSourceConnectionHelper(DataSourceEntity dataSourceEntity, DataStoreContextEntity? dataStoreContextEntity = null) : base(dataSourceEntity, dataStoreContextEntity) { }

	#region Methods

	#region DataSource Operations
	
	/// <inheritdoc cref="IStorageConnection.CreateDataSource"/>
	internal StorageDataSource CreateDataSource()
	{
		var config = GetDataSourceConfigFromEntity();
		
		return ExecuteOperation(connection => connection.CreateDataSource(config));
	}
	
	/// <inheritdoc cref="IStorageConnection.UpdateDataSource"/>
	internal StorageDataSource UpdateDataSource()
	{
		var config = GetDataSourceConfigFromEntity();
		
		return ExecuteOperation(connection => connection.UpdateDataSource(config));
	}
	
	/// <inheritdoc cref="IStorageConnection.RemoveDataSource"/>
	internal void RemoveDataSource()
	{
		ExecuteOperation(connection => connection.RemoveDataSource(DataSourceEntity.Name));
	}

	#endregion

	#region Field Operations
	
	/// <inheritdoc cref="IStorageConnection.CreateField"/>
	internal StorageField CreateField(StorageFieldConfig fieldConfig)
	{
		return ExecuteOperation(connection => connection.CreateField(DataSourceEntity.Name, fieldConfig));
	}
	
	/// <inheritdoc cref="IStorageConnection.UpdateField"/>
	internal StorageField UpdateField(StorageFieldConfig fieldConfig)
	{
		return ExecuteOperation(connection => connection.UpdateField(DataSourceEntity.Name, fieldConfig));
	}
	
	/// <inheritdoc cref="IStorageConnection.RenameField"/>
	internal StorageField RenameField(string fieldName, string newFieldName)
	{
		return ExecuteOperation(connection => connection.RenameField(DataSourceEntity.Name, fieldName, newFieldName));
	}
	
	/// <inheritdoc cref="IStorageConnection.RemoveField"/>
	internal void RemoveField(string fieldName)
	{
		ExecuteOperation(connection => connection.RemoveField(DataSourceEntity.Name, fieldName));
	}

	#endregion

	private StorageDataSourceConfig GetDataSourceConfigFromEntity()
	{
		return new StorageDataSourceConfig(DataSourceEntity.Name)
		{
			StoreRevisions = DataSourceEntity.StoreRevision,
			StoreFieldContent = DataSourceEntity.StoreFieldContent,
			StoreFileContent = DataSourceEntity.StoreFileContent,
			StoragePath = DataSourceEntity.StoragePath,
			FulltextSearch = DataSourceEntity.FulltextSearch,
		};
	}
	
	private IStorageConnection OpenConnection()
	{
		return (IStorageConnection) DataStoreConnection.OpenConnection();
	}

	private void ExecuteOperation(Action<IStorageConnection> operation)
	{
		IStorageConnection? connection = null;
		
		try
		{
			connection = OpenConnection();
			
			operation(connection);
		}
		finally
		{
			connection?.Dispose();
		}
	}
	
	private T ExecuteOperation<T>(Func<IStorageConnection, T> operation)
	{
		T result;
		IStorageConnection? connection = null;
		
		try
		{
			connection = OpenConnection();
			
			result = operation(connection);
		}
		finally
		{
			connection?.Dispose();
		}

		return result;
	}

	#endregion
	
}