using Levelbuild.Core.FrontendDtos;
using Levelbuild.Entities.Features.User;

namespace Levelbuild.Entities.Interfaces;

/// <summary>
/// Interface for all entities in the administration section like: user management, data store, data sources,....
/// </summary>
public interface IAdministrationEntity<TEntity, TEntityDto> : IConvertibleEntity<TEntityDto> where TEntity : class
{
	/// <summary>
	/// Create new entity instance based in data contained inside the corresponding DTO
	/// </summary>
	/// <param name="entityInfo">DTO containing entity property values</param>
	/// <param name="createdBy">User responsible for the create</param>
	/// <param name="context">Database context</param> // TODO: inject via Interceptor?
	/// <returns></returns>
	public static abstract TEntity FromDto(TEntityDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null);
	
	/// <summary>
	/// Update only parts of the entity properties
	/// </summary>
	/// <param name="dto">Transfer object which is coming from the frontend</param>
	/// <param name="modifiedBy">User responsible for the update</param>
	public void UpdatePartial(TEntityDto dto, UserEntity? modifiedBy = null);

	/// <summary>
	/// Executes this function before create and update of the entity 
	/// </summary>
	/// <param name="context">Database Context</param>
	/// <param name="dto">Transfer object which is coming from the frontend</param>
	/// <param name="elementId">ID of the entity which will be updated</param>
	/// <returns>List of validation errors</returns>
	public static virtual IList<ValidationError> Validate(CoreDatabaseContext context, TEntityDto dto, Guid? elementId = null)
	{
		return new List<ValidationError>();
	}
}