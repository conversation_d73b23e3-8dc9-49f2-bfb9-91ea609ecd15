using Levelbuild.Core.FrontendDtos.Shared;

namespace Levelbuild.Entities.Interfaces;

/// <summary>
/// Interface for polymorphic parent entities.
/// </summary>
public interface IPolymorphicParentEntity
{
	/// <summary>
	/// Converts the entity into a DTO based on its real polymorphic type.
	/// </summary>
	/// <param name="databaseContext">The db context.</param>
	/// <param name="realType">Provides the real type of the returned DTO. May for example be used for runtime conversion via <see cref="Convert"/> class.</param>
	/// <param name="includes">Optional property names that may be included to the DTO.</param>
	public EntityDto ToRealDto(CoreDatabaseContext databaseContext, out Type realType, params string[] includes);
}