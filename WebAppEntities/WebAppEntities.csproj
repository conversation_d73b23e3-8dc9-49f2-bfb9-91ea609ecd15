<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <RootNamespace>Levelbuild.Entities</RootNamespace>
        <Company>levelbuild AG</Company>
        <PackageId>levelbuild.Entities</PackageId>

        <GenerateDocumentationFile>true</GenerateDocumentationFile>
    </PropertyGroup>


    <!-- for development purpose -->
    <ItemGroup>
        <Reference Include="DataStoreInterface">
            <HintPath>..\..\WebAppCore\DataStoreInterface\bin\Debug\net8.0\DataStoreInterface.dll</HintPath>
        </Reference>
        <Reference Include="EntityInterface">
            <HintPath>..\..\WebAppCore\EntityInterface\bin\Debug\net8.0\EntityInterface.dll</HintPath>
        </Reference>
        <Reference Include="FrontendDtos">
            <HintPath>..\..\WebAppCore\FrontendDtos\bin\Debug\net8.0\FrontendDtos.dll</HintPath>
        </Reference>
        <Reference Include="SharedDtos">
            <HintPath>..\..\WebAppCore\SharedDtos\bin\Debug\net8.0\SharedDtos.dll</HintPath>
        </Reference>
        <Reference Include="ZitadelApiInterface">
            <HintPath>..\..\WebAppCore\ZitadelApiInterface\bin\Debug\net8.0\ZitadelApiInterface.dll</HintPath>
        </Reference>
        <Reference Include="StorageInterface">
            <HintPath>..\..\WebAppCore\StorageInterface\bin\Debug\net8.0\StorageInterface.dll</HintPath>
        </Reference>
    </ItemGroup>


    <!-- for release purpose -->
    <ItemGroup Condition="'$(Configuration)'=='Release'">
        <PackageReference Include="levelbuild.DataStoreInterface" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.EntityInterface" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.FrontendDtos" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.SharedDtos" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.ZitadelApiInterface" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.StorageInterface" Version="$(LVL_RELEASE_VERSION)"/>
    </ItemGroup>


    <!-- local solution reference -->
    <ItemGroup>
        <ProjectReference Include="..\Storage\Storage.csproj" />
    </ItemGroup>


    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.11" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
        <PackageReference Include="Serilog" Version="4.1.0" />
        <PackageReference Include="Zitadel" Version="7.0.23" />
    </ItemGroup>
</Project>
