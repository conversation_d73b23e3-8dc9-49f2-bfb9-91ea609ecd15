describe('Page Rendering ', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0
	let dataSourceId = ''
	let lookupSourceId = ''
	let lookupValue1Id = ''
	let lookupValue2Id = ''
	
	before(() => {
		guid = uuid()

		// create DataStore
		cy.createDataStore(guid, true, (dataStoreId) => {
			
			// create LookupSource
			cy.createDataSource(dataStoreId, "LookupSource_"+guid, {}, (sourceId) => {
				lookupSourceId = sourceId

				cy.createField(lookupSourceId, 'Value1', 'String', { length: 255 }, (fieldId) => {lookupValue1Id = fieldId})
				cy.createField(lookupSourceId, 'Value2','Integer', {}, (fieldId) => {
					lookupValue2Id = fieldId
					
					// fill in some dummy data
					cy.request({
						url: `/PublicApi/DataSources/${lookupSourceId}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Value1: 'Magic the Gathering',
								Value2: 42
							},
							groups: [ 'testgroup' ],
						}
					})
					cy.request({
						url: `/PublicApi/DataSources/${lookupSourceId}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Value1: 'Card Game',
								Value2: 69
							},
							groups: [ 'testgroup' ],
						}
					})
				})
			})

			// create DataSource
			cy.createDataSource(dataStoreId, "TestSource_"+guid, {}, (sourceId) => {
				dataSourceId = sourceId
				
				// add some fields
				cy.createField(dataSourceId, 'Name', 'String', { length: 255, defaultValue: '=USERNAME()' })
				cy.createField(dataSourceId, 'Firstname', 'String', { length: 255 })
				cy.createField(dataSourceId, 'Position', 'Integer', { defaultValue: '10'})
				cy.createField(dataSourceId, 'Tags', 'String', { length: 100, multi: true})
				cy.createField(dataSourceId, 'Numbers', 'Integer', { multi: true })
				cy.createField(dataSourceId, 'CreateDateTime', 'DateTime', { defaultValue: '=NOW()' })
				cy.createField(dataSourceId, 'Tomorrow', 'Date', { defaultValue: '=CALC_DATE(=TODAY(), 1)' })
				cy.createField(dataSourceId, 'CreateTime', 'Time', { defaultValue: '=NOW()' })
				cy.createField(dataSourceId, 'LookupField', 'Guid', { fieldType: 'LookupField', lookupSourceId: lookupSourceId, lookupDisplayFieldId: lookupValue1Id}, (fieldId) => {
					cy.createFieldColumn(fieldId, lookupValue2Id)
				})
				cy.createField(dataSourceId, 'MailSubject', 'String', {length: 255, defaultValue: '=MAILDATA(SUBJECT)' })
				cy.createField(dataSourceId, 'MailFrom', 'String', {length: 255, defaultValue: '=MAILDATA(FROM)' })
				cy.createField(dataSourceId, 'MailTo', 'String', {length: 255, defaultValue: '=MAILDATA(TO)', multi: true })
				
				// add Create Page
				cy.createPage(dataSourceId, "TestSource_C", "Create", {}, (createPageId) => {

					// fetch default grid view
					cy.getPageView(createPageId, "Form", (pageViewId) => {

						// add a section
						cy.addSection(pageViewId, 0, 0, "TestSection", (sectionId) => {

							// add some fields
							cy.forEachField(dataSourceId, true, (field, index) => {
								cy.addInputField(sectionId, field.id, field.type, index+1, index+2, 1, 25, null, field.name == 'Position' ? '=MAX(5,8)' : undefined)
							})
						})
					})
					
					// add edit page
					cy.createPage(dataSourceId, "TestSource_E", "SingleData", {}, (detailPageId) => {

						// fetch default grid view
						cy.getPageView(detailPageId, "Overview", (pageViewId) => {

							// add a section
							cy.addSection(pageViewId, 0, 0, "TestSection", (sectionId) => {

								// add some fields
								cy.forEachField(dataSourceId, true, (field, index) => {
									cy.addInputField(sectionId, field.id, field.type, index+1, index+2, 1, 25)
								})
							})
							
							// enable viewer
							cy.updatePageView(pageViewId, { type: 'Grid', showViewer: true })
						})

						// add MultiData Page and link create/edit pages
						cy.createPage(dataSourceId, "TestSource_Q", "MultiData", { createPageId: createPageId, detailPageId: detailPageId }, (pageId) => {

							// create list view
							cy.createPageView(pageId, "DefaultListView", { type: 'List', display: true }, (pageViewId) => {

								// add some columns
								cy.forEachField(dataSourceId, true, (field) => {
									cy.createColumn(pageViewId, field.id, field.name == 'Firstname' ? 1 : field.name == 'Name' ? 2 : field.name == 'Tags' ? 3 : field.name == 'Numbers' ? 4 : field.name == 'Position' ? 5 : 6)

									// order by position asc
									if (field.name == 'Position')
										cy.createFieldSorting(pageId, field.id)
								})
							})
						})
					})					
				})
			})
		})
	})
	
	it('create without file', () => {
		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// open create page
		cy.get('lvl-query-view-action-bar').shadow().find('lvl-button[data-action=create]').click()
		
		// check visibilities
		checkVisibilities(false)

		// check default values
		checkDefaultValues()
		
		// Check Lookup functionality
		// open dropdown and see if the multi column functionality is working properly
		cy.get('lvl-autocomplete[name=LookupField]').as('autocomplete').shadow().find('lvl-input-button').click({force: true})
		cy.get('@autocomplete').shadow().find('.dropdown-content tbody tr').as('rows').should('have.length', 2)
		cy.get('@rows').eq(0).find('td > span[name=LABEL]').should('contain.text', 'Card Game')
		cy.get('@rows').eq(0).find('td > span[name=Value2]').should('contain.text', '69')
		cy.get('@rows').eq(1).find('td > span[name=LABEL]').should('contain.text', 'Magic the Gathering')
		cy.get('@rows').eq(1).find('td > span[name=Value2]').should('contain.text', '42')
		cy.get('@autocomplete').shadow().find('lvl-input-button').click({force: true})
		
		// enter some values and create entry
		storeEntry('John', 'Doe', 1, ["cool", "dude"], [1, 100, 10000])
	})
	
	it('create with file added after open', () => {
		// set "allowFile" to true
		cy.updateDataSource(dataSourceId, {allowFile: true})

		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// open create page
		cy.get('lvl-query-view-action-bar').shadow().find('lvl-button[data-action=create]').click()

		// check visibilities
		checkVisibilities(true)
		
		// get hidden file upload input and attach file (clicking the upload button doesn't work)
		cy.get('@filePreview').find('main.dropzone-init-done > input[type=file]').as('fileInput').attachFile('levelbuild.png')
		checkFileInfo('levelbuild.png', '32 KB')
		
		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), true)
		
		// now our file icon should have changed as well
		checkFileInfo('levelbuild.png', '32 KB', 'fa-file-png')
		
		// fill with data and store
		storeEntry('Jane', 'Doe', 2, ["pretty", "intelligent"], [2, 200, 20000], true)
	})
	
	it('create with file added after open via drag&drop', () => {
		// set "allowFile" to true
		cy.updateDataSource(dataSourceId, {allowFile: true})

		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// open create page
		cy.get('lvl-query-view-action-bar').shadow().find('lvl-button[data-action=create]').click()

		// check visibilities
		checkVisibilities(true)

		// drag'n drop file
		cy.get('@filePreview').find('main.dropzone-init-done').attachFile('levelbuild.png', { subjectType: 'drag-n-drop' });
		checkFileInfo('levelbuild.png', '32 KB')

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), true)

		// now our file icon should have changed as well
		checkFileInfo('levelbuild.png', '32 KB', 'fa-file-png')
		
		// fill with data and store
		storeEntry('Kai', 'Doe', 3, ["little", "cute"], [3, 300, 30000], true)
	})

	it('create with file added before open via drag&drop', () => {
		// set "allowFile" to true
		cy.updateDataSource(dataSourceId, {allowFile: true})

		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// drag'n drop
		cy.get('body').attachFile('levelbuild.png', { subjectType: 'drag-n-drop' });
		
		// create page should open
		checkVisibilities(true, true)

		// check file info
		checkFileInfo('levelbuild.png', '32 KB')

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), true)

		// now our file icon should have changed as well
		checkFileInfo('levelbuild.png', '32 KB', 'fa-file-png')

		// remove file
		cy.get('@elementPreview').get('.file-edit-button').click()
		cy.get('@elementPreview').get('lvl-menu-item.file-remove').click({force:true})
		
		// viewer should disappear and file preview should have been reset
		cy.get('@filePreview').find('.file-viewer').should('not.be.visible')
		
		// file icon should have changed back to default
		cy.get('@elementPreview').find(`.file-icon.fa-memo-pad`).should('exist')
		
		// edit menu should be disabled
		cy.get('@elementPreview').get('.file-edit-button').should('have.attr', 'disabled')

		// drag'n drop file
		cy.get('@filePreview').find('main.dropzone-init-done').attachFile('levelbuild.png', { subjectType: 'drag-n-drop' });
		checkFileInfo('levelbuild.png', '32 KB')

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), true)

		// now our file icon should have changed again
		checkFileInfo('levelbuild.png', '32 KB', 'fa-file-png')
		
		// fill with data and store
		storeEntry('Alex', 'Doe', 4,  ["weird", "fancy"], [4, 400, 40000], true)
	})

	it('create with msg file added after open', () => {
		// set "allowFile" to true
		cy.updateDataSource(dataSourceId, {allowFile: true})

		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// open create page
		cy.get('lvl-query-view-action-bar').shadow().find('lvl-button[data-action=create]').click()

		// check visibilities
		checkVisibilities(true)

		// get hidden file upload input and attach file (clicking the upload button doesn't work)
		cy.get('@filePreview').find('main.dropzone-init-done > input[type=file]').as('fileInput').selectFile('cypress/fixtures/testEmail_msg.msg', { force: true })
		checkFileInfo('testEmail_msg.msg', '116 KB')

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), false)

		// now our file icon should have changed as well
		checkFileInfo('testEmail_msg.msg', '116 KB')

		// check if mail data is set correctly
		cy.get('lvl-input[name="MailSubject"]').should('have.attr', 'value', 'Geburtstag')
		cy.get('lvl-input[name="MailFrom"]').should('have.attr', 'value', 'Wolff, Philipp <<EMAIL>>')
		cy.get('lvl-input[name="MailTo"]').should('have.attr', 'value', '["Gaertner, Uwe <<EMAIL>>"]')
	})

	it('create with msg file added after open via drag&drop', () => {
		// set "allowFile" to true
		cy.updateDataSource(dataSourceId, {allowFile: true})

		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// open create page
		cy.get('lvl-query-view-action-bar').shadow().find('lvl-button[data-action=create]').click()

		// check visibilities
		checkVisibilities(true)

		// drag'n drop file
		cy.get('@filePreview').find('main.dropzone-init-done').selectFile('cypress/fixtures/testEmail_msg.msg', { action: 'drag-drop', force: true });
		checkFileInfo('testEmail_msg.msg', '116 KB')

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), false)

		// now our file icon should have changed as well
		checkFileInfo('testEmail_msg.msg', '116 KB')

		// check if mail data is set correctly
		cy.get('lvl-input[name="MailSubject"]').should('have.attr', 'value', 'Geburtstag')
		cy.get('lvl-input[name="MailFrom"]').should('have.attr', 'value', 'Wolff, Philipp <<EMAIL>>')
		cy.get('lvl-input[name="MailTo"]').should('have.attr', 'value', '["Gaertner, Uwe <<EMAIL>>"]')
	})

	it('create with msg file added before open via drag&drop', () => {
		// set "allowFile" to true
		cy.updateDataSource(dataSourceId, {allowFile: true})

		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// drag'n drop
		cy.get('body').selectFile('cypress/fixtures/testEmail_msg.msg', { action: 'drag-drop', force: true });

		// create page should open
		checkVisibilities(true, true)

		// check file info
		checkFileInfo('testEmail_msg.msg', '116 KB')

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), false)

		// now our file icon should have changed as well
		checkFileInfo('testEmail_msg.msg', '116 KB')

		// check if mail data is set correctly
		cy.get('lvl-input[name="MailSubject"]').should('have.attr', 'value', 'Geburtstag')
		cy.get('lvl-input[name="MailFrom"]').should('have.attr', 'value', 'Wolff, Philipp <<EMAIL>>')
		cy.get('lvl-input[name="MailTo"]').should('have.attr', 'value', '["Gaertner, Uwe <<EMAIL>>"]')
	})

	it('create with eml file added after open', () => {
		// set "allowFile" to true
		cy.updateDataSource(dataSourceId, {allowFile: true})

		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// open create page
		cy.get('lvl-query-view-action-bar').shadow().find('lvl-button[data-action=create]').click()

		// check visibilities
		checkVisibilities(true)

		// get hidden file upload input and attach file (clicking the upload button doesn't work)
		cy.get('@filePreview').find('main.dropzone-init-done > input[type=file]').as('fileInput').selectFile('cypress/fixtures/testEmail_eml.eml', { force: true });
		checkFileInfo('testEmail_eml.eml', '12 KB')

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), false)

		// now our file icon should have changed as well
		checkFileInfo('testEmail_eml.eml', '12 KB')

		// check if mail data is set correctly
		cy.get('lvl-input[name="MailSubject"]').should('have.attr', 'value', 'Test Email Viele Empfänger')
		cy.get('lvl-input[name="MailFrom"]').should('have.attr', 'value', 'Wolff, Philipp <<EMAIL>>')
		cy.get('lvl-input[name="MailTo"]').should('have.attr', 'value', '["Weihe, Tom <<EMAIL>>","Gaertner, Uwe <<EMAIL>>","<EMAIL>"]')
	})

	it('create with eml file added after open via drag&drop', () => {
		// set "allowFile" to true
		cy.updateDataSource(dataSourceId, {allowFile: true})

		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// open create page
		cy.get('lvl-query-view-action-bar').shadow().find('lvl-button[data-action=create]').click()

		// check visibilities
		checkVisibilities(true)

		// drag'n drop file
		cy.get('@filePreview').find('main.dropzone-init-done').selectFile('cypress/fixtures/testEmail_eml.eml', { action: 'drag-drop', force: true });
		checkFileInfo('testEmail_eml.eml', '12 KB')

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), false)

		// now our file icon should have changed as well
		checkFileInfo('testEmail_eml.eml', '12 KB')

		// check if mail data is set correctly
		cy.get('lvl-input[name="MailSubject"]').should('have.attr', 'value', 'Test Email Viele Empfänger')
		cy.get('lvl-input[name="MailFrom"]').should('have.attr', 'value', 'Wolff, Philipp <<EMAIL>>')
		cy.get('lvl-input[name="MailTo"]').should('have.attr', 'value', '["Weihe, Tom <<EMAIL>>","Gaertner, Uwe <<EMAIL>>","<EMAIL>"]')
	})

	it('create with eml file added before open via drag&drop', () => {
		// set "allowFile" to true
		cy.updateDataSource(dataSourceId, {allowFile: true})

		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// drag'n drop
		cy.get('body').selectFile('cypress/fixtures/testEmail_eml.eml', { action: 'drag-drop', force: true });

		// create page should open
		checkVisibilities(true, true)

		// check file info
		checkFileInfo('testEmail_eml.eml', '12 KB')

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewer(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), false)

		// now our file icon should have changed as well
		checkFileInfo('testEmail_eml.eml', '12 KB')

		// check if mail data is set correctly
		cy.get('lvl-input[name="MailSubject"]').should('have.attr', 'value', 'Test Email Viele Empfänger')
		cy.get('lvl-input[name="MailFrom"]').should('have.attr', 'value', 'Wolff, Philipp <<EMAIL>>')
		cy.get('lvl-input[name="MailTo"]').should('have.attr', 'value', '["Weihe, Tom <<EMAIL>>","Gaertner, Uwe <<EMAIL>>","<EMAIL>"]')
	})
	
	after(() => {
		cy.removeDataStore(guid);
	})
})

function checkVisibilities(allowFile: boolean, hasFile: boolean = false) {
	cy.get('#create-panel').as('createPanel').should('exist').should('be.visible')
	cy.get('@createPanel').find('.fileQueue').as('fileQueue').should('exist').should('be.visible')
	cy.get('@createPanel').find('.filePreview').as('filePreview').should('exist').should(allowFile ? 'be.visible' : 'not.be.visible')
	cy.get('@createPanel').find('.content').as('content').should('exist').should('be.visible')
	cy.get('@fileQueue').find('.element-add-button lvl-button').should('exist').should(allowFile ? 'not.be.visible' : 'be.visible').should('have.attr', 'disabled')
	cy.get('@fileQueue').find('.file-dropzone-button lvl-button').should('exist').should(allowFile ? 'be.visible' : 'not.be.visible').should('have.attr', 'disabled')
	cy.get('@filePreview').find('lvl-button.file-upload-start').as('uploadButton').should('exist').should(allowFile && !hasFile ? 'be.visible' : 'not.be.visible')
}

function checkDefaultValues() {
	cy.get('@createPanel').find('lvl-form[initDone]').as('form')
	cy.get('@form').find('lvl-input[name=Name]').invoke('val').should('equal', 'E2EAdminTestUser')
	let tomorrow = new Date()
	tomorrow.setDate(tomorrow.getDate() + 1)
	cy.get('@form').find('lvl-input[name=Tomorrow]').invoke('attr', 'value').should('equal', tomorrow.toISOString().slice(0,10))
	cy.get('@form').find('lvl-input[name=Position]').invoke('val').should('equal', 8)
}

function checkFileInfo(filename: string, filesizeHR: string, fileIcon?: string) {
	// filename
	cy.get('#create-panel').find('.element-preview').as('elementPreview').should('exist')
	cy.get('@elementPreview').find('label').should('contain.text', filename)

	// filesize
	cy.get('@elementPreview').find('legend').should('contain.text', `${filesizeHR} ·`)
	
	// extension
	const extension = filename.substring(filename.lastIndexOf('.')+1).toUpperCase()
	cy.get('@elementPreview').find('legend').should('contain.text', `· ${extension}`)
	
	// icon
	if (fileIcon)
		cy.get('@elementPreview').find(`.file-icon.${fileIcon}`).should('exist')
}

function waitForViewer(rootElement: Cypress.Chainable, waitForApryse: boolean) {
	rootElement.should('exist')
	if(waitForApryse)
		rootElement.shadow().find('[data-cy=apryse-webviewer]').should('exist')
}

function storeEntry(firstname: string, lastname: string, position: number, tags: string[], numbers: number[], checkViewer: boolean = false) {
	cy.get('@createPanel').find('lvl-form[initDone]').as('form').should('exist')
	cy.get('@form').find('lvl-input[name=Firstname]', { timeout: 10000 }).invoke('val', firstname)
	cy.get('@form').find('lvl-input[name=Name]').invoke('val', lastname)
	cy.get('@form').find('lvl-input[name=Position]').invoke('val', position)
	cy.get('@form').find('lvl-input[name=Tags]').invoke('prop', 'value', tags)
	cy.get('@form').find('lvl-input[name=Numbers]').invoke('prop', 'value', numbers)
	cy.get('@createPanel').find('lvl-button[data-action=save]').click({ force: true })
	
	// check if multi data page refreshes and new element is part of the result set
	cy.get('lvl-table').shadow().find(`.table__body lvl-table-row[data-position=${position-1}]:not([skeleton])`, {timeout: 10000}).as('row').should('exist')
	
	cy.get('@row').shadow().find('.table__cell').eq(1).should('exist').should('contain.text', firstname)
	cy.get('@row').shadow().find('.table__cell').eq(2).should('exist').should('contain.text', lastname)
	cy.get('@row').shadow().find('.table__cell').eq(3).as('tagColumn').should('exist')
	tags.forEach((tag) => {
		cy.get('@tagColumn').find(`lvl-fry[value=${tag}]`).should('exist')
	})
	cy.get('@row').shadow().find('.table__cell').eq(4).as('numberColumn').should('exist')
	numbers.forEach((number) => {
		cy.get('@numberColumn').find(`lvl-fry[value=${number}]`).should('exist')
	})
	cy.get('@row').shadow().find('.table__cell').eq(5).should('exist').should('contain.text', position)
	
	// open entry
	cy.get('@row').click()
	
	// goto overview
	cy.get('lvl-side-nav[initDone]').should('exist')
	cy.get('lvl-side-nav').find('lvl-side-nav-item[selected]').should('exist')
	cy.get('lvl-side-nav').children().eq(0).click()
	
	// wait for lvl-form to load
	cy.get('.single_page_content lvl-form').as('overviewForm')
	
	// check values
	cy.get('@overviewForm').find('lvl-value-formatter[name=Firstname]').should('exist').shadow().should('contain.text', firstname)
	cy.get('@overviewForm').find('lvl-value-formatter[name=Name]').shadow().should('contain.text', lastname)
	cy.get('@overviewForm').find('lvl-value-formatter[name=Tags]').as('tagValues')
	tags.forEach((tag) => {
		cy.get('@tagValues').shadow().find(`lvl-fry[value=${tag}]`).should('exist')
	})
	cy.get('@overviewForm').find('lvl-value-formatter[name=Numbers]').as('numberValues')
	numbers.forEach((number) => {
		cy.get('@numberValues').shadow().find(`lvl-fry[value=${number}]`).should('exist')
	})
	cy.get('@overviewForm').find('lvl-value-formatter[name=Position]').shadow().should('contain.text', position)
	
	if (checkViewer)
		waitForViewer(cy.get('@overviewForm').find('.file-viewer.enabled'), true)
	
	// go back to the list view
	cy.go('back')	
}