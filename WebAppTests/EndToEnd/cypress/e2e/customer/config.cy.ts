describe('Customer Configuration', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = uuid()
	
	 it('navigates to customer config entry point', () => {
		// go to CustomerConfig via Home
		cy.visit('/')
		cy.get('body').find('.dashboard__item[href="/Admin/Customers"]').as('navLink').should('be.visible')
		cy.get('@navLink').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})
	})
	
	it('CRUDs a customer config item', () => {
		createConfigItem()
		cy.wait(100)
		updateConfigItem()
		cy.wait(100)
		deAndReActivateConfigItem()
		cy.wait(200)
		deleteConfigItem() 
	})

	it('try to open customer config without authorization/authentication', () => {
		cy.visitWithoutUser('/Admin/Customers')
		cy.visitWithoutAdminRights('/Admin/Customers')
	})

	it('some additional navigation tests', () => {
		// open list view
		cy.visit('/Admin/Customers')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})
		cy.title().should('contain', 'Customers')

		// open create mask
		cy.get('[data-action=add]').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers/Create'))
		})
		cy.title().should('contain', 'New')

		// close create mask via abort button
		cy.get('.side-panel > [data-action=cancel]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})
		cy.title().should('contain', 'Customers')

		// open create mask again and reload page
		cy.get('[data-action=add]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers/Create'))
		})
		cy.title().should('contain', 'New')
		cy.reload()

		cy.get('.side-panel').shadow().find('#slider').should('have.attr', 'open')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers/Create'))
		})

		// close create mask again
		cy.get('.side-panel > [data-action=cancel]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})
		cy.title().should('contain', 'Customers')
		
		// create example
		createExampleViaRest('navigationCustomer')
		cy.get('@navigationCustomer').then(() => {
			cy.reload()
		})
		
		cy.get('lvl-list').shadow().find('lvl-list-line:first-child').should('not.have.attr', 'skeleton')

		// open entry
		let displayName: string
		cy.get('lvl-list').shadow().find('lvl-list-line-item[data-name=displayName]').contains(`Example${guid}`).as('row').then((cell) => {
			displayName = cell[0].innerText
			cy.get('@row').click()
			cy.url().then(url => {
				expect(url.endsWith('/Admin/Customers/' + displayName))
				cy.title().should('contain', displayName)
			})
		})

		// refresh entry
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers/' + displayName))
			cy.title().should('contain', displayName)
		})

		// back button should bring us back to the list view
		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})
		cy.title().should('contain', 'Customers')
		cy.get('lvl-list').should('exist')

		// refresh should keep us at the list view
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})
		cy.title().should('contain', 'Customers')

		// use breadcrumb navigation
		cy.visit(`/Admin/Customers/Example${guid}`)
		cy.get('lvl-breadcrumb')
			.shadow()
			.find('#breadcrumbs').as('breadcrumbs')
		cy.url().then(url => {
			expect(url.endsWith(`/Admin/Customers/example${guid}`))
		})

		cy.get('@breadcrumbs').contains('Customers').click()
		cy.url().then(url => {
			expect(url.endsWith(`/Admin/Customers/example${guid}`))
		})
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 2)

		cy.get('@breadcrumbs')
			.find('#home').click()
		cy.title().should('contain', 'Welcome')
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 1)
	})

	function createConfigItem() {
		cy.intercept('POST','Api/Customers').as('createRequest')
		
		// open list view
		cy.visit('/Admin/Customers')

		// open create panel
		cy.get('[data-action=add]').click()
		cy.get('#customer-form').as('form').should('be.visible')

		// url should have changed to /Admin/Customers/Create
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers/Create'))
		})

		// input form data
		cy.wait(100)

		// submit
		cy.get('.side-panel > [data-action=save]')
			.click()

		// warning should be visible that there are empty required fields
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		cy.get('@form')
			.find('#customer-display-name').shadow()
			.find('input').as('nameInput').focus().clear()
			.type(`Example${guid}`)

		// submit -> CREATE
		cy.get('.side-panel > [data-action=save]')
			.click()

		cy.wait('@createRequest')
		
		// dialog should be closed
		cy.get('.side-panel').shadow().find('#slider').should('not.have.attr', 'open')
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'Success')
	
		// check reset of the form
		cy.get('[data-action=add]').click({force: true})
		cy.get('#customer-display-name').shadow().find('input').should('be.empty')
	}

	function updateConfigItem() {
		cy.intercept('PATCH', 'Api/Customers/*').as('updateRequest')
		
		// list view -> click on first customer config
		cy.visit('/Admin/Customers/')

		// edit view and return with back button
		cy.get('lvl-list').shadow().find('lvl-list-line:first-child:not([hidden]):not([skeleton])').as('row')
			.find('lvl-list-line-item[data-name=displayName]').then((cell) => {
			let displayName = cell[0].innerText
			cy.get('@row').click()
			cy.url().then(url => {
				expect(url.endsWith('/Admin/Customers/' + displayName))
			})
		})
		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})

		cy.wait(50)
		cy.get('lvl-list').shadow().find('lvl-list-line:first-child:not([hidden]):not([skeleton])')
		// edit example view -> change input and save
		cy.visit(`/Admin/Customers/example${guid}`)
		cy.get('#customer-display-name').shadow()
			.find('input').focus().clear()
			.type(`Renamed Example${guid}`)
		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()
		cy.wait('@updateRequest')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})
		cy.title().should('contain', 'Customers')

		// item should be accessible
		cy.request({ url: `/Admin/Customers/renamed-example${guid}`, failOnStatusCode: false }).then((resp) => {
			expect(resp.status, 'StatusCode should be 200').to.equal(200)
		})

		// change to origin
		cy.visit(`/Admin/Customers/renamed-example${guid}`)
		cy.get('#customer-display-name').shadow()
			.find('input').focus().clear()
			.type(`Example${guid}`)
		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()
		cy.wait('@updateRequest')
	}
	
	function deAndReActivateConfigItem() {
		cy.intercept('Api/Customers/*/Activate').as('activationRequest')
		cy.intercept('Api/Customers/*/Deactivate').as('deactivationRequest')
		cy.get('lvl-list').shadow().find('lvl-list-line:first-child:not([hidden]):not([skeleton])')
		cy.visit(`/Admin/Customers/example${guid}`)
		cy.get('#content-buttons [data-action=deactivate]:not([hidden]):not([skeleton])').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})

		cy.get('lvl-list').shadow().find('lvl-list-line:not([hidden]):not([skeleton])')
		cy.get('lvl-list').shadow().find('lvl-list-line').contains(`Example${guid}`).closest('lvl-list-line').as('row_1').should('exist')
		cy.get('@row_1').should('not.have.attr', 'active')
		cy.intercept('Api/Customers?*').as('listRequest')
		cy.get('@row_1').find('lvl-list-line-item').last().click()
		cy.get('@row_1').find('lvl-dropdown > lvl-menu > lvl-menu-item[data-action="Activate"]').click({force: true})
		
		cy.wait('@activationRequest')
		cy.wait('@listRequest')
		cy.get('lvl-list').shadow().find('lvl-list-line:not([hidden]):not([skeleton])')
		cy.get('lvl-list').shadow().find('lvl-list-line').contains(`Example${guid}`).closest('lvl-list-line').as('row_2').should('exist')
		cy.get('@row_2').should('have.attr', 'active')
		cy.get('@row_2').find('lvl-list-line-item').last().click()
		cy.get('@row_2').find('lvl-dropdown > lvl-menu > lvl-menu-item[data-action="Deactivate"]').click({force: true})

		cy.get('lvl-list').shadow().find('lvl-list-line').contains(`Example${guid}`).closest('lvl-list-line').as('row_3').should('exist')
		cy.get('@row_3').should('not.have.attr', 'active')
		cy.visit(`/Admin/Customers/example${guid}`)
		cy.get('#content-buttons [data-action=reactivate]:not([hidden]):not([skeleton])').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})
	}

	function deleteConfigItem() {
		cy.intercept('DELETE', 'Api/Customers/*').as('deletionRequest')
		
		// wait till list is loaded
		cy.get('lvl-list').shadow().find('lvl-list-line:first-child:not([hidden]):not([skeleton])')
		// edit sample data and delete item
		cy.visit(`/Admin/Customers/example${guid}`)
		cy.wait(100)
		cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click({ force: true })
		cy.wait(50)
		cy.get('lvl-toaster').should('exist')

		cy.get('lvl-toaster').shadow().find('lvl-button[label=Delete]:not([hidden]):not([skeleton])').should('exist').click({ force: true });
		cy.wait('@deletionRequest')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Customers'))
		})

		// item should not be accessible
		cy.request({ url: `/Api/Customers/example${guid}`, failOnStatusCode: false }).then((resp) => {
			expect(resp.status, 'StatusCode should be 404').to.equal(404)
		})
	}

	function createExampleViaRest(alias: string){
		cy.request({ url: '/Api/Customers/', failOnStatusCode: true, method: 'POST', body: { displayName: `Example${guid}` } }).then((resp) => {
			expect(resp.status === 200, `Create temporary customer config: Example${guid}`).to.be.true
			cy.wrap(resp.body.data).as(alias)
		})
	}

	function deleteExampleViaRest() {
		cy.request({ url: '/Api/Customers/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
			const customerConfig = resp.body.data.rows.find((row: Record<string, any>) => row.displayName === `Example${guid}`)
			if (!customerConfig)
				return
			
			cy.request({ url: '/Api/Customers/' + customerConfig.id, failOnStatusCode: false, method: 'DELETE' }).then(deleteResponse => {
				expect(deleteResponse.status).not.null
			})
		})
	}
	
	after(() => {
		deleteExampleViaRest()
	})
})