describe('custom create methods', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	
	describe('create DataField', () => {
		const guid = uuid()
		
		it('check createField', () => {
			cy.createDataStore(guid, false).as('storeId')

			// Test Data Source
			cy.createDataSource('@storeId', 'TestSource').as('sourceId')

			// Test Data Field
			cy.createField('@sourceId', 'TestField', 'String', { length: 255 }).as('testFieldId')
			cy.get<string>('@testFieldId').then((id) => {
				cy.wrap(id).should('not.be.null')
			})

			// create some more to test that response alias within does not get overridden by another create-call 
			cy.createField('@sourceId', 'TestIntegerField', 'Integer', {}).as('testIntegerFieldId')
			cy.createField('@sourceId', 'TestIntegerTwoField', 'Integer', {}).as('testIntegerTwoFieldId')
			cy.createField('@sourceId', 'TestStringTwoField', 'String', { length: 255 }).as('testStringTwoFieldId')
			cy.createField('@sourceId', 'TestStringThreeField', 'String', { length: 255 }).as('testStringThreeFieldId')

			// create Page
			cy.createPage('@sourceId', 'TestPage', 'MultiData', {}).as('testPageId')

			// create Field sorting
			cy.createFieldSorting('@testPageId', '@testFieldId', 'asc').then((id) => {
				cy.wrap(id).should('not.be.null')
			})
			cy.createFieldSorting('@testPageId', '@testStringTwoFieldId', 'asc').should('not.be.null')
			cy.createFieldSorting('@testPageId', '@testStringThreeFieldId', 'asc').should('not.be.null')
			cy.createFieldSorting('@testPageId', '@testIntegerFieldId', 'asc').should('not.be.null')
			cy.createFieldSorting('@testPageId', '@testIntegerTwoFieldId', 'asc').should('not.be.null')
		})
		
		after(() => {
			cy.removeDataStore(guid)
		})
	})
})