describe('Module Configuration', { browser: 'chrome' }, () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0

	beforeEach(() => {
		guid = uuid()
		cy.createDataStore(guid, false).as('dataStore')

		if (Cypress.browser.family === 'chromium') {
			Cypress.automation('remote:debugger:protocol', {
				command: 'Network.enable',
				params: {}
			});
			Cypress.automation('remote:debugger:protocol', {
				command: 'Network.setCacheDisabled',
				params: { cacheDisabled: true }
			});
		}

		cy.intercept('**/DataStores/*/Sync').as('sync')
		cy.intercept('**/SyncWithFields').as('syncWithFields')
		cy.intercept('**/DataSources/*/Sync').as('syncSource')
	})

	afterEach(() => {
		cy.removeDataStore(guid)
	})

	it('navigates to module entry point', () => {
		// go to DataStore via Home
		cy.visit('/')
		cy.get('body').find('.dashboard__item[href="/Admin/Modules"]').as('navLink').should('be.visible')
		cy.get('@navLink').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Modules'))
		})
	})

	it('CRUDs a module item', () => {
		createConfigItem()
		updateConfigItem()
		deleteConfigItem()
	})

	it('Create and Navigate to source over module', () => {
		cy.createModule('@dataStore', 'ExampleModule_' + guid)
		
		cy.visit('/Admin/DataStores/testStore' + guid + '/Modules/examplemodule' + guid)

		// open create panel for dataSource
		cy.get('[data-action=add]').click()
		cy.get('#create-panel').as('form').should('be.visible')
		
		// module should be prefilled
		cy.get('@form')
			.find('#data-source-module')
			.should('have.attr', 'display-value', 'ExampleModule_' + guid)
			.should('have.attr', 'readonly')
		
		// refresh and check again
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Modules/examplemodule'+ guid + '/DataSources/example' + guid + '/BasicSettings/Create'))
		})
		// module should be prefilled
		cy.get('@form')
			.find('#data-source-module')
			.should('have.attr', 'display-value', 'ExampleModule_' + guid)
			.should('have.attr', 'readonly')

		// input form data
		cy.get('@form')
			.find('#data-source-name').shadow()
			.find('input').as('nameInput').focus().clear()
			.type('Example_' + guid)
		// storage path should have the data source name as placeholder now
		cy.get('@form')
			.find('#data-source-storage-path')
			.shadow().find('input').as('storagePathInput')
			.should('have.attr', 'placeholder', 'Example_' + guid)

		cy.get('@form')
			.find('#data-source-comment').shadow()
			.find('textarea').as('commentInput')
			.clear().type('Comment for example data source')

		// submit -> CREATE
		cy.get('.side-panel > [data-action=save]')
			.click()

		// dialog should be closed
		cy.get('.side-panel').shadow().find('#slider').should('not.have.attr', 'open')

		// check reset of the form
		cy.get('[data-action=add]').click()
		cy.get('@nameInput').should('be.empty')
		cy.get('@commentInput').should('be.empty')
		cy.get('@storagePathInput').should('be.empty')
		cy.get('.side-panel > [data-action=cancel]').click()
		
		// open entry
		cy.get('lvl-list').shadow().as('dataList')
		cy.get('@dataList').find('lvl-list-line:not([skeleton]):first-child').as('row').find('[data-name="name"]').then((_) => {
			cy.get('@row').click()
			cy.url().should('contain', '/Admin/DataStores/teststore' + guid + '/Modules/examplemodule'+ guid + '/DataSources/example' + guid)
			cy.title().should('contain', 'Example_' + guid)
			cy.wait('@syncSource')
		})

		// check breadcrumbs
		cy.get('lvl-breadcrumb')
			.shadow()
			.find('#breadcrumbs').as('breadcrumbs')

		cy.get('@breadcrumbs').contains('ExampleModule_' + guid).click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Modules/examplemodule' + guid))
		})
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 3)

		// open entry again and refresh
		cy.get('lvl-list').shadow().as('dataList')
		cy.get('@dataList').find('lvl-list-line:not([skeleton]):first-child').as('row').find('[data-name="name"]').then((_) => {
			cy.get('@row').click()
			cy.url().should('contain', '/Admin/DataStores/teststore' + guid + '/Modules/examplemodule'+ guid + '/DataSources/example' + guid)
			cy.title().should('contain', 'Example_' + guid)
			cy.wait('@syncSource')
		})
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Modules/examplemodule'+ guid + '/DataSources/example' + guid))
		})

		// check breadcrumbs
		cy.get('lvl-breadcrumb')
			.shadow()
			.find('#breadcrumbs').as('breadcrumbs')

		cy.get('@breadcrumbs').contains('ExampleModule_' + guid).click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Modules/examplemodule' + guid))
		})
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 3)
	})
	
	it('try to open module without authorization/authentication', () => {
		cy.visitWithoutUser('/Admin/Modules')
		cy.visitWithoutAdminRights('/Admin/Modules')
	})

	function createConfigItem() {
		// open list view
		cy.visit('/Admin/Modules')

		// change datastore to example
		cy.get('#module-menu').find('lvl-side-nav-item[label=TestStore_' + guid + ']').as('exampleMenuItem').click()

		// open create panel
		cy.get('[data-action=add]').click()
		cy.get('#module-form').as('form').should('be.visible')

		// check that store id is set
		cy.get('@exampleMenuItem').then(item => {
			cy.get('@form').find('[name=dataStoreId]').should('have.attr', 'value', item[0].getAttribute('value'))
		})

		// url should have changed
		cy.url().then(url => {
			expect(url.endsWith('/Modules/Create'))
		})

		// submit
		cy.get('.side-panel > [data-action=save]')
			.click()

		// warning should be visible that there are empty required fields
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		// input form data
		// typing in this input is broken on cypress side
		cy.get('@form')
			.find('#module-name').invoke('attr', 'value', 'Example_' + guid)

		cy.get('@form')
			.find('#module-icon').shadow()
			.find('input').as('iconInput').focus().clear()
			.type('explosion')

		cy.get('@form')
			.find('#module-responsible').shadow().as('responsible')
			.find('lvl-input-button').should('exist').click()
		cy.get('@responsible')
			.find('.content-container').find(`tbody > tr`).as('columnElement').should('exist')
		cy.get('@columnElement').first().click()
		
		cy.get('@form')
			.find('#module-responsible').should('have.attr', 'value')
		
		cy.get('@form')
			.find('#module-description').shadow()
			.find('textarea').as('descriptionInput')
			.clear().type('Comment for example data source')

		// submit -> CREATE
		cy.get('.side-panel > [data-action=save]')
			.click()

		// dialog should be closed
		cy.get('.side-panel').shadow().find('#slider').should('not.have.attr', 'open')

		// check reset of the form
		cy.get('[data-action=add]').click()
		cy.get('@form')
			.find('#module-name').shadow()
			.find('input').should('be.empty')
		cy.get('@descriptionInput').should('be.empty')
		cy.get('@iconInput').should('be.empty')
		cy.get('@responsible').should('not.have.attr', 'value')
		cy.get('.side-panel > [data-action=cancel]').click()
	}

	function updateConfigItem() {
		// list view -> click on first data store
		cy.visit('/Admin/DataStores/' + guid + '/Modules')

		// edit view and return with back button
		cy.get('lvl-list').shadow().as('dataList')
		cy.get('@dataList').find('lvl-list-line:not([skeleton]):first-child').as('row').find('[data-name="name"]').then((cell) => {
			let storeName = cell[0].innerText.toLowerCase().replace('_', '')
			cy.get('@row').click()
			cy.url().as('url')
			cy.get('@url').should('contain', '/Admin/DataStores/teststore' + guid + '/Modules/' + storeName)
		})

		// check that infos are already written as wait alternative
		cy.get('[name=info-dataStore-link-left]').should('have.prop', 'value', `TestStore_${guid}`)

		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Modules'))
		})

		// edit example view -> change input and save
		cy.visit('/Admin/DataStores/testStore' + guid + '/Modules/example' + guid)

		cy.get('#module-description').shadow()
			.find('textarea').focus().clear()
			.type('Renamed Description')
		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()

		// check for success toast
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'Successfully')

		// check that the value is still the new one after reload
		cy.visit('/Admin/DataStores/testStore' + guid + '/Modules/example' + guid)
		cy.get('#module-description').should('have.attr', 'value', 'Renamed Description')
	}

	function deleteConfigItem() {
		// edit sample data and delete item
		cy.visit('/Admin/DataStores/testStore' + guid + '/Modules/example' + guid)
		cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Modules'))
		})

		// item should not be accessible
		cy.request({ url: '/Api/Modules/example' + guid, failOnStatusCode: false }).then((resp) => {
			expect(resp.status, 'StatusCode should be 404').to.equal(404)
		})
	}
})

