import { ElementActionType } from '../../support/e2e.ts'

describe('MultiPageFilterFields', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0

	before(() => {
		guid = uuid()
		cy.createDataStore(guid, true).as('dataStore')

		// create lookup source for preview values
		cy.createDataSource('@dataStore', 'LookupSource_' + guid).as('lookupSource')
		cy.createField('@lookupSource', 'Filename', 'String', { length: 255 }).as('lookupDisplayField')

		// create records for lookup source
		cy.createSimpleRecord('@lookupSource', { Filename: 'File1' }).as('lookupRecordOne')
		cy.createSimpleRecord('@lookupSource', { Filename: 'File2' }).as('lookupRecordTwo')
		cy.createSimpleRecord('@lookupSource', { Filename: 'File3' }).as('lookupRecordThree')

		// create main source and fields
		cy.createDataSource('@dataStore', 'MainSource_' + guid, { fulltextSearch: true }).as('dataSource')
		cy.createField('@dataSource', 'Dummy', 'String', { length: 255 }).as('dummyField')
		cy.createField('@dataSource', 'Boolean', 'Boolean').as('booleanField')
		cy.createField('@dataSource', 'String', 'String', { length: 255 }).as('stringField')
		cy.createField('@dataSource', 'Integer', 'Integer').as('integerField')
		cy.createField('@dataSource', 'MultiInteger', 'Integer', { multi: true }).as('multiIntField')
		cy.createField('@dataSource', 'MultiInteger2', 'Integer', { multi: true }).as('multiInt2Field')
		cy.createField('@dataSource', 'Date', 'Date').as('dateField')
		cy.createField('@dataSource', 'File', 'Guid', {
			fieldType: 'LookupField',
			length: 0,
			decimalPlaces: 2,
			mandatory: false,
			unique: false,
			multi: false,
			systemField: false,
			lookupSourceId: '@lookupSource',
			lookupDisplayFieldId: '@lookupDisplayField',
			virtualLookupFieldId: null,
			virtualDataFieldId: null,
		}).as('fileField')

		// create table including columns
		cy.createPage('@dataSource', 'Page_' + guid, 'MultiData').as('page')
		cy.createPageView('@page', 'ListView', { type: 'List', display: true }).as('listView')
		cy.createColumn('@listView', '@booleanField')
		cy.createColumn('@listView', '@stringField')
		cy.createColumn('@listView', '@integerField')
		cy.createColumn('@listView', '@dateField')
		cy.createColumn('@listView', '@fileField')
		cy.createColumn('@listView', '@multiIntField')
		cy.createColumn('@listView', '@multiInt2Field')
		cy.createColumn('@listView', '@dummyField')
		
		// create sorting
		cy.createFieldSorting('@page', '@stringField', 'asc')

		// create filter fields
		cy.createMultiPageFilterField('@page', '@booleanField', { position: 1, displayInPanel: true })
		cy.createMultiPageFilterField('@page', '@stringField', { position: 2, displayInPanel: true, valuePreview: true })
		cy.createMultiPageFilterField('@page', '@integerField', { position: 3, displayInPanel: true })
		cy.createMultiPageFilterField('@page', '@dateField', { position: 4, displayInPanel: true })
		cy.createMultiPageFilterField('@page', '@fileField', { position: 5, displayInPanel: true, valuePreview: true })
		cy.createMultiPageFilterField('@page', '@multiIntField', { position: 6, displayInPanel: true, multiValue: true })
		cy.createMultiPageFilterField('@page', '@multiInt2Field', { position: 7, displayInPanel: true, multiValue: false })
		cy.createMultiPageFilterField('@page', '@dummyField', { position: 8, displayInPanel: false })

		// create records for data source
		cy.get<Record<string, any>>('@lookupRecordOne').then(lookupRecord => {
			cy.wrap(Array.from({length:10})).each((_, index: number) => {
				cy.createSimpleRecord('@dataSource', { File: lookupRecord.id, Boolean: true, String: `record_${index}`, Integer: index, MultiInteger: [1, index], MultiInteger2: [1, index], Date: new Date() })
			})
		})

		cy.get<Record<string, any>>('@lookupRecordTwo').then(lookupRecord => {
			cy.wrap(Array.from({length:10})).each((_, index: number) => {
				cy.createSimpleRecord('@dataSource', { File: lookupRecord.id, Boolean: false, String: `record_${index}`, Integer: index, MultiInteger: [2, index], MultiInteger2: [2, index], Date: new Date() })
			})
		})

		cy.get<Record<string, any>>('@lookupRecordThree').then(lookupRecord => {
			cy.wrap(Array.from({length:10})).each((_, index: number) => {
				cy.createSimpleRecord('@dataSource', { File: lookupRecord.id, Boolean: true, String: `recordAlt_${index}`, MultiInteger: [3, index], MultiInteger2: [3, index], Integer: index })
			})
		})

		// record list without lookup field + favorite = true
		cy.wrap(Array.from({length:10})).each((_, index: number) => {
			cy.createSimpleRecord('@dataSource', { Boolean: false, String: `recordAlt_${index}`, Integer: index }).as('currentRecord')
			cy.executeRecordAction('@dataSource', '@currentRecord', ElementActionType.Favorite, true)
		})
		
		// Add two more empty file values
		cy.createSimpleRecord('@dataSource', { Boolean: false, String: '', Integer: -1 })
		cy.createSimpleRecord('@dataSource', { Boolean: false, String: null, Integer: -1 })
		
		// Add inactive records
		cy.wrap(Array.from({length:10})).each((_, index: number) => {
			cy.createSimpleRecord('@dataSource', { Boolean: false, String: `inactive_${index}`, Integer: index }).as('currentRecord')
			cy.executeRecordAction('@dataSource', '@currentRecord', ElementActionType.Inactive, true)
			
			// every second one is favorite = true
			if (index % 2 == 1)
				cy.executeRecordAction('@dataSource', '@currentRecord', ElementActionType.Favorite, true)
		})
	})

	it('Show filter panel and check value preview counts', () => {
		cy.intercept('/Api/DataFields/*/Elements').as('groupRequest')

		cy.viewport(1920, 1300)
		cy.visit(`/Public/Pages/page${guid}`)
		cy.get('lvl-filter-panel').as('panel').should('exist')
		cy.get('@panel').shadow().find('.panel__wrapper').as('panelShadow')
		cy.get('@panel').find('lvl-filter-panel-section').eq(0).should('have.attr', 'name', 'Boolean').shadow().as('boolSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(1).should('have.attr', 'name', 'String').shadow().as('stringSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(2).should('have.attr', 'name', 'Integer').shadow().as('intSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(3).should('have.attr', 'name', 'Date').shadow().as('dateSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(4).should('have.attr', 'name', 'File').shadow().as('fileSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(5).should('have.attr', 'name', 'MultiInteger').shadow().as('multiIntSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(6).should('have.attr', 'name', 'MultiInteger2').shadow().as('multiIntSingleSection')
 
		// Check default count
		checkDefaultState()

		// filter by fields and check counts
		// boolean = true
		cy.get('@boolSection').find('lvl-button[value=true]').should('exist').click()
		cy.wait('@groupRequest')
		cy.get('@stringSection').find('lvl-select-list-item').should('exist').should('have.length', 20)
		cy.get('@stringSection').find('lvl-select-list-item').first().should('have.attr', 'count', '1')
		cy.get('@stringSection').find('lvl-select-list-item').each(item => {
			cy.wrap(item.attr('count')).should('equal', '1')
		})

		cy.get('@fileSection').find('lvl-select-list-item').should('have.length', 2)
		cy.get('@fileSection').find('lvl-select-list-item').first().should('have.attr', 'count', '10')
		cy.get('@fileSection').find('lvl-select-list-item').each(item => {
			cy.wrap(item.attr('count')).should('equal', '10')
		})

		cy.get('@tableFooter').should('have.prop', 'count', 20)

		// int = 1 & boolean = true
		cy.get('@intSection').find('lvl-input').shadow().find('input').type('1').blur()
		cy.wait('@groupRequest')
		cy.get('@stringSection').find('lvl-select-list-item').should('exist').should('have.length', 2)
		cy.get('@stringSection').find('lvl-select-list-item').first().should('have.attr', 'count', '1')
		cy.get('@stringSection').find('lvl-select-list-item').each(item => {
			cy.wrap(item.attr('count')).should('equal', '1')
		})

		cy.get('@fileSection').find('lvl-select-list-item').should('have.length', 2)
		cy.get('@fileSection').find('lvl-select-list-item').first().should('have.attr', 'count', '1')
		cy.get('@fileSection').find('lvl-select-list-item').each(item => {
			cy.wrap(item.attr('count')).should('equal', '1')
		})

		cy.get('@tableFooter').should('have.prop', 'count', 2)

		// check table entries
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(2).should('contain.text', 'record_1')
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(1).shadow().find('.cell__content').eq(2).should('contain.text', 'recordAlt_1')

		// add fulltext search
		cy.get('#global-search').shadow().find('input').type('recordAlt_1{enter}')
		cy.get('@tableFooter').should('have.prop', 'count', 1)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(2).should('contain.text', 'recordAlt_1')

		// ensure that filters still exists after reload
		cy.reload()
		cy.get('@panel').shadow().find('.panel__wrapper').as('panelShadow')
		cy.get('@panel').find('lvl-filter-panel-section').eq(0).should('have.attr', 'name', 'Boolean').shadow().as('boolSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(1).should('have.attr', 'name', 'String').shadow().as('stringSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(2).should('have.attr', 'name', 'Integer').shadow().as('intSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(3).should('have.attr', 'name', 'Date').shadow().as('dateSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(4).should('have.attr', 'name', 'File').shadow().as('fileSection')
		cy.get('@tableFooter').should('have.prop', 'count', 1)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(2).should('contain.text', 'recordAlt_1')
		
		// remove fulltext search
		cy.get('#global-search').shadow().find('[data-action="clear"]').click()

		// remove filter
		cy.get('@panelShadow').find('[data-action=clear]').click()
		checkDefaultState()

		// date = TODAY
		cy.get('@dateSection').find('lvl-select-list-item').first().should('exist').click()
		cy.wait('@groupRequest')
		cy.get('@stringSection').find('lvl-select-list-item').should('exist').should('have.length', 10)
		cy.get('@stringSection').find('lvl-select-list-item').each(item => {
			cy.wrap(item.attr('count')).should('equal', '2')
		})

		cy.get('@fileSection').find('lvl-select-list-item').should('have.length', 2)
		cy.get('@fileSection').find('lvl-select-list-item').first().should('have.attr', 'count', '10')
		cy.get('@fileSection').find('lvl-select-list-item').each(item => {
			cy.wrap(item.attr('count')).should('equal', '10')
		})

		cy.get('@tableFooter').should('have.prop', 'count', 20)

		// remove filter
		cy.get('@panelShadow').find('[data-action=clear]').click()
		checkDefaultState()

		// integer = -1 & string = ''
		cy.get('@intSection').find('lvl-input').shadow().find('input').type('-1').blur()
		cy.get('@stringSection').find('lvl-select-list-item').should('exist').should('have.length', 1)
		cy.get('@stringSection').find('lvl-select-list-item').first().should('have.attr', 'count', '2').and('have.attr', 'label', '-')
		cy.get('@stringSection').find('lvl-select-list-item').first().click()

		// check table entries
		cy.get('@tableFooter').should('have.prop', 'count', 2)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(2).should('contain.text', '-')
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(1).shadow().find('.cell__content').eq(2).should('contain.text', '-')

		// ensure that filters still exists after reload
		cy.reload()
		cy.get('@panel').shadow().find('.panel__wrapper').as('panelShadow')
		cy.get('@panel').find('lvl-filter-panel-section').eq(0).should('have.attr', 'name', 'Boolean').shadow().as('boolSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(1).should('have.attr', 'name', 'String').shadow().as('stringSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(2).should('have.attr', 'name', 'Integer').shadow().as('intSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(3).should('have.attr', 'name', 'Date').shadow().as('dateSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(4).should('have.attr', 'name', 'File').shadow().as('fileSection')
		cy.get('@tableFooter').should('have.prop', 'count', 2)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(1).should('contain.text', '-')
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(1).shadow().find('.cell__content').eq(1).should('contain.text', '-')

		// remove filter
		cy.get('@panelShadow').find('[data-action=clear]').click()
		checkDefaultState()

		// file = null
		cy.get('@fileSection').find('lvl-select-list-item').first().should('have.attr', 'count', '12').and('have.attr', 'label', '-')
		cy.get('@fileSection').find('lvl-select-list-item').first().click()

		// check table entries
		cy.get('@tableFooter').should('have.prop', 'count', 12)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(2).should('contain.text', '-')

		// remove filter
		cy.get('@panelShadow').find('[data-action=clear]').click()
		checkDefaultState()

		// single fulltext search
		cy.get('#global-search').shadow().find('input').type('recordAlt{enter}')
		cy.get('@tableFooter').should('have.prop', 'count', 20)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(2).should('contain.text', 'recordAlt_0')

		// use filter dialog
		cy.get('@panel').shadow().find('[data-action="openFilterDialog"]').as('openDialogButton').click()
		cy.get('@panel').shadow().find('lvl-filter-dialog[open]').as('filterDialog').should('exist')
		cy.get('@panel').shadow().find('lvl-filter-dialog[open]').shadow().find('.dialog__wrapper').as('dialogContent').should('exist')
		cy.get('@filterDialog').should('have.prop', 'count', 20)

		// file should be visible with values
		cy.get('@filterDialog').find('lvl-filter-dialog-section[name="File"]').should('exist').shadow().as('dialogFileSection')
		cy.get('@dialogFileSection').find('.dialog__filter-section .section__empty').should('not.exist')
		cy.get('@dialogFileSection').find('lvl-select-list-item').should('have.length', 2)

		// integer = 99 -> count = 0 && File field should be gone
		cy.get('@filterDialog').find('lvl-filter-dialog-section[name="Integer"]').should('exist').shadow().as('dialogIntSection')
		cy.get('@dialogIntSection').find('lvl-input').shadow().find('input').type('99', { force: true }).blur()
		cy.get('@filterDialog').should('have.prop', 'count', 0)
		cy.get('@dialogFileSection').find('.dialog__filter-section .section__empty').should('exist')
		cy.get('@dialogFileSection').find('lvl-select-list-item').should('have.length', 0)
		cy.get('@tableFooter').should('have.prop', 'count', 20)

		// reset integer
		cy.get('@dialogIntSection').find('lvl-input').shadow().find('input').clear({ force: true }).blur()
		cy.get('@dialogFileSection').find('.dialog__filter-section .section__empty').should('not.exist')
		cy.get('@dialogFileSection').find('lvl-select-list-item').should('have.length', 2)
		cy.get('@filterDialog').should('have.prop', 'count', 20)

		// file = file3
		cy.get('@dialogFileSection').find('lvl-select-list-item[label="File3"]').should('have.attr', 'count', '10')
		cy.get('@dialogFileSection').find('lvl-select-list-item[label="File3"]').click({ force: true })
		cy.get('@filterDialog').should('have.prop', 'count', 10)
		cy.get('@tableFooter').should('have.prop', 'count', 20)

		// apply filter from dialog -> table should reload
		cy.get('@dialogContent').find('[data-action="apply"]').click({ force: true })
		cy.get('@tableFooter').should('have.prop', 'count', 10) 
		
		cy.get('@panelShadow').find('[data-action=clear]').click()
		cy.get('@panelShadow').find('[data-action=clear-search]').click()
		
		// favorite
		cy.get('@panelShadow').find('.section__system-fields [name="favorite"]').click()
		cy.get('@tableFooter').should('have.prop', 'count', 10)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(2).should('contain.text', 'recordAlt_0')
		
		// favorite + inactive records
		cy.get('@panelShadow').find('.section__system-fields [name="inactive"] [value="true"]').click()
		cy.get('@tableFooter').should('have.prop', 'count', 15)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(2).should('contain.text', 'inactive_1')

		// ensure that filters still exists after reload
		cy.reload()
		cy.get('@panel').shadow().find('.panel__wrapper').as('panelShadow')
		cy.get('@panel').find('lvl-filter-panel-section').eq(0).should('have.attr', 'name', 'Boolean').shadow().as('boolSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(1).should('have.attr', 'name', 'String').shadow().as('stringSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(2).should('have.attr', 'name', 'Integer').shadow().as('intSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(3).should('have.attr', 'name', 'Date').shadow().as('dateSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(4).should('have.attr', 'name', 'File').shadow().as('fileSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(5).should('have.attr', 'name', 'MultiInteger').shadow().as('multiIntSection')
		cy.get('@panel').find('lvl-filter-panel-section').eq(6).should('have.attr', 'name', 'MultiInteger2').shadow().as('multiIntSingleSection')
		cy.get('@tableFooter').should('have.prop', 'count', 15)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(0).shadow().find('.cell__content').eq(2).should('contain.text', 'inactive_1')
		
		// inactive
		cy.get('@panelShadow').find('.section__system-fields [name="favorite"]').click()
		cy.get('@tableFooter').should('have.prop', 'count', 52)
		cy.get('lvl-table').shadow().find('.table__body').find('lvl-table-row').eq(2).shadow().find('.cell__content').eq(2).should('contain.text', 'inactive_0')
		
		// remove inactive
		cy.intercept('/Api/DataFields/*/Elements').as('groupRequest2')
		cy.get('@panelShadow').find('.section__system-fields [name="inactive"] [value="false"]').click()
		cy.wait('@groupRequest2')
		cy.wait('@groupRequest2')
		checkDefaultState()
		
		// filter by multi value
		cy.get('@multiIntSection').find('lvl-input').then(input => (input[0] as any).value = [0])
		cy.get('@tableFooter').should('have.prop', 'count', 3)
		cy.get('@multiIntSection').find('lvl-input').then(input => (input[0] as any).value = [0, 1])
		cy.get('@tableFooter').should('have.prop', 'count', 14)

		// remove filter
		cy.get('@panelShadow').find('[data-action=clear]').click()
		checkDefaultState()

		// filter by multi value with single value
		cy.get('@multiIntSingleSection').find('lvl-input').then(input => (input[0] as any).value = 0)
		cy.get('@tableFooter').should('have.prop', 'count', 3)
		cy.get('@multiIntSingleSection').find('lvl-input').then(input => (input[0] as any).value = 1)
		cy.get('@tableFooter').should('have.prop', 'count', 12)

		// remove filter
		cy.get('@panelShadow').find('[data-action=clear]').click()
		checkDefaultState()
	})

	it('Create filter fields', () => {
		cy.visit(`/Admin/DataStores/teststore${guid}/Pages/page${guid}`)
		cy.get('lvl-side-nav-item[value=FilterAndSorting]').click()
		cy.get('lvl-side-nav-item[value=FilterFields]').click()

		cy.get('#multi-page-filter-field-list lvl-list').shadow().as('filterFieldList')
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 8)
		
		// remove the 7 filter fields
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 7)
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 6)
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 5)
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 4)
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 3)
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 2)
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 1)
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 0)

		// add Boolean + File
		cy.get('#data-field-list lvl-list').shadow().as('fieldList')
		cy.get('@fieldList').contains('Boolean').click()

		// check in list
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 1)
		
		cy.get('@fieldList').contains('File').click()
		
		// check in list
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 2)

		// click on Boolean filter field
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item[data-name=displayInPanel] > .icon').should('have.data', 'value', false)
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).contains('Boolean').click()
		cy.get('#multi-page-filter-field-form').as('editForm')
			.should('exist')
			.should('not.have.class', 'skeleton')
			.should('have.attr', 'initdone')
		cy.get('@editForm').find('[name=position][initdone]').should('have.prop', 'value', 1)
		cy.get('@editForm').find('[name=displayInPanel][initdone]').click()
		cy.get('#edit-panel').find('[data-action=save]').click()
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item[data-name=displayInPanel] > .icon').should('have.data', 'value', true)

		// click on File filter field
		cy.get('@filterFieldList').find('lvl-list-line').eq(1).find('lvl-list-line-item[data-name=displayInPanel] > .icon').should('have.data', 'value', false)
		cy.get('@filterFieldList').find('lvl-list-line').eq(1).contains('File').click()
		cy.get('#multi-page-filter-field-form')
			.should('exist')
			.should('not.have.class', 'skeleton')
			.should('have.attr', 'initdone')
		cy.get('@editForm').find('[name=position][initdone]').should('have.prop', 'value', 2)
		cy.get('@editForm').find('[name=valuePreview][initdone]').should('have.prop', 'value', true)
		cy.get('@editForm').find('[name=multiValue][initdone]').click()
		cy.get('#edit-panel').find('[data-action=save]').click()
		cy.get('@filterFieldList').find('lvl-list-line').eq(1).find('lvl-list-line-item[data-name=displayInPanel] > .icon').should('have.data', 'value', false)
		
		// delete Boolean
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('have.length', 1)
		
		// edit File again and check position
		cy.intercept('PATCH','/Api/MultiPageFilterFields/*').as('patchFilterField')
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item[data-name=displayInPanel]').click()
		cy.wait('@patchFilterField').its('response.statusCode').should('eq', 200)
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item[data-name=displayInPanel] > .icon').should('have.data', 'value', true)
		
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).contains('File').click()
		cy.get('#multi-page-filter-field-form')
			.should('exist')
			.should('not.have.class', 'skeleton')
			.should('have.attr', 'initdone')
		cy.get('@editForm').find('[name=position][initdone]').should('have.prop', 'value', 1)
		cy.get('@editForm').find('[name=displayInPanel][initdone]').should('have.prop', 'value', true)
		
		// reload and check it again
		cy.reload()
		cy.get('#multi-page-filter-field-form')
			.should('exist')
			.should('not.have.class', 'skeleton')
			.should('have.attr', 'initdone')
		cy.get('#edit-panel').find('[data-action=cancel]').click()
		
		// delete File
		cy.get('@filterFieldList').find('lvl-list-line').eq(0).find('lvl-list-line-item').last().find('[data-action=delete]').click()
		cy.get('@filterFieldList').find('lvl-list-line').should('not.exist')
	})
	
	function checkDefaultState() {
		cy.get('@stringSection').find('lvl-select-list-item').should('exist').should('have.length', 21)
		cy.get('@stringSection').find('lvl-select-list-item').each(item => {
			cy.wrap(item.attr('count')).should('equal', '2')
		})

		cy.get('@fileSection').find('lvl-select-list-item').should('have.length', 4)
		cy.get('@fileSection').find('lvl-select-list-item').each((_, index) => {
			cy.get('@fileSection').find('lvl-select-list-item').eq(index).should('have.attr', 'count', index ? '10' : '12')
		})

		cy.get('lvl-multi-data-view').shadow().find('lvl-query-view-navigation').as('tableFooter').should('have.prop', 'count', 42)
	}

	after(() => {
		cy.removeDataStore(guid)
	})
})