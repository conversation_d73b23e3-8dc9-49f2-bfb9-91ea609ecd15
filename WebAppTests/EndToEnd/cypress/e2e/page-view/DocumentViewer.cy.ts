describe("Document Viewer", () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0
	let sourceGuid = ''
	let singlePageId = ''
	let elementPngFileId = ''
	let elementJpgFileId = ''
	let elementTxtFileId = ''
	let elementPdfFileId = ''
	let elementEmlFileId = ''
	let elementDwgFileId = ''
	let elementMsgFileId = ''
	let elementWithoutFileId = ''

	/*
	let elementDocxFileId = ''
	let elementXlsxFileId = ''
	let elementPptxFileId = ''
	 */
	
	beforeEach(() => {
		guid = uuid()
		// create dataStore
		cy.createDataStore(guid, true, (storeId) => {
			// create source
			cy.request({
				url: '/Api/DataSources/',
				failOnStatusCode: false,
				method: 'POST',
				body: { name: 'TestSource_' + guid, dataStoreId: storeId },
			}).then((sourceResp) => {
				expect(sourceResp.status).to.equal(200, 'Create temporary data source: TestSource')
				expect(sourceResp.body.data.id).not.null

				sourceGuid = sourceResp.body.data.id
				cy.wrap(sourceGuid).as('sourceGuid')
				
				// create a view fields 
				cy.createField(sourceResp.body.data.id, 'Filename', 'String', { length: 255 })

				// upload png file
				cy.readFile('testFiles/testFile.png', 'binary').then( image => {
					const blob = Cypress.Blob.binaryStringToBlob(image, 'image/png')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.png')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.png`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('pngFileId')
					})
				})
				cy.get('@pngFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'Magic the Gathering',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementPngFileId = resp.body.data.id
					})
				})

				// upload jpg file
				cy.readFile('testFiles/testFile.jpg', 'binary').then( image => {
					const blob = Cypress.Blob.binaryStringToBlob(image, 'image/jpg')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.jpg')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.jpg`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('jpgFileId')
					})
				})
				cy.get('@jpgFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'Hearthstone',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementJpgFileId = resp.body.data.id
					})
				})

				// upload txt file
				cy.readFile('testFiles/testFile.txt', 'binary').then( text => {
					const blob = Cypress.Blob.binaryStringToBlob(text, 'text/txt')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.txt')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.txt`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('txtFileId')
					})
				})
				cy.get('@txtFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'Legends of Runeterra',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementTxtFileId = resp.body.data.id
					})
				})

				// upload docx file
				cy.readFile('testFiles/testFile.docx', 'binary').then( text => {
					const blob = Cypress.Blob.binaryStringToBlob(text, 'text/docx')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.docx')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.docx`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('docxFileId')
					})
				})
				/* cy.get('@docxFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'Faeria',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementDocxFileId = resp.body.data.id
					})
				}) */

				// upload pdf file
				cy.readFile('testFiles/testFile.pdf', 'binary').then( text => {
					const blob = Cypress.Blob.binaryStringToBlob(text, 'text/pdf')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.pdf')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.pdf`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('pdfFileId')
					})
				})
				cy.get('@pdfFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'Slay the Spire',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementPdfFileId = resp.body.data.id
					})
				})

				// upload xlsx file
				cy.readFile('testFiles/testFile.xlsx', 'binary').then( text => {
					const blob = Cypress.Blob.binaryStringToBlob(text, 'xlsx')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.xlsx')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.xlsx`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('xlsxFileId')
					})
				})
				/* cy.get('@xlsxFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'Uno',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementXlsxFileId = resp.body.data.id
					})
				}) */

				// upload pptx file
				cy.readFile('testFiles/testFile.pptx', 'binary').then( text => {
					const blob = Cypress.Blob.binaryStringToBlob(text, 'pptx')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.pptx')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.pptx`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('pptxFileId')
					})
				})
				/* cy.get('@pptxFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'Skat',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementPptxFileId = resp.body.data.id
					})
				}) */

				// upload eml file
				cy.readFile('testFiles/testFile.eml', 'binary').then( text => {
					const blob = Cypress.Blob.binaryStringToBlob(text, 'eml')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.eml')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.eml`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('emlFileId')
					})
				})
				cy.get('@emlFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'Romme',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementEmlFileId = resp.body.data.id
					})
				})

				// upload dwg file
				cy.readFile('testFiles/testFile.dwg', 'binary').then( text => {
					const blob = Cypress.Blob.binaryStringToBlob(text, 'dwg')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.dwg')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.dwg`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('dwgFileId')
					})
				})
				cy.get('@dwgFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'Pokemon TCG',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementDwgFileId = resp.body.data.id
					})
				})

				// upload msg file
				cy.readFile('testFiles/testFile.msg', 'binary').then( text => {
					const blob = Cypress.Blob.binaryStringToBlob(text, 'msg')
					const formData = new FormData()
					formData.append('file', blob, 'testFile.msg')
					// upload file
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Files`,
						failOnStatusCode: false,
						method: 'POST',
						body: formData,
						headers: {
							'content-type': 'multipart/form-data'
						}
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Uploaded File: testFile.msg`)
						const bodyString = Cypress.Blob.arrayBufferToBinaryString(resp.body)
						const body = JSON.parse(bodyString)
						cy.wrap(body.data).as('msgFileId')
					})
				})
				cy.get('@msgFileId').then(fileUploadId => {
					// create element with file attached
					cy.request({
						url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
						failOnStatusCode: false,
						method: 'POST',
						body: {
							values: {
								Filename: 'So tasty',
							},
							fileUploadId: fileUploadId,
							groups: [ 'testgroup' ],
						},
					}).then((resp) => {
						expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
						elementMsgFileId = resp.body.data.id
					})
				})
				
				// create element without file
				cy.request({
					url: `/PublicApi/DataSources/${sourceGuid}/Elements`,
					failOnStatusCode: false,
					method: 'POST',
					body: {
						values: {
							Filename: 'Flesh and Blood',
						},
						groups: [ 'testgroup' ],
					},
				}).then((resp) => {
					expect(resp.status).to.equal(200, `Create record for main source: ${sourceGuid}`)
					elementWithoutFileId = resp.body.data.id
				})
			})
			
			// create pages
			cy.get('@sourceGuid').then((singleDatasourceGuild) => {
				// single page
				cy.request({
					url: '/Api/Pages/',
					failOnStatusCode: false,
					method: 'POST',
					body: { dtoType: 'SingleData', name: 'SinglePage_' + guid, dataSourceId: singleDatasourceGuild, type: 'SingleData' },
				}).then((pageResp) => {
					expect(pageResp.status).to.equal(200, 'Create temporary single page: SinglePage')
					expect(pageResp.body.data.id).not.null

					singlePageId = pageResp.body.data.id
					cy.wrap(singlePageId).as('singlePageId')
				})
				
				// multi page
				cy.request({
					url: '/Api/Pages/',
					failOnStatusCode: false,
					method: 'POST',
					body: { dtoType: 'MultiData', name: 'MultiPage_' + guid, dataSourceId: singleDatasourceGuild, type: 'MultiData' },
				}).then((pageResp) => {
					expect(pageResp.status).to.equal(200, 'Create temporary multi page: MultiPage')
					expect(pageResp.body.data.id).not.null

					// list view
					cy.request({
						url: '/Api/PageViews/',
						failOnStatusCode: false,
						method: 'POST',
						body: { dtoType: 'List', name: 'TestListView', pageId: pageResp.body.data.id, type: 'List' },
					}).then((viewResp) => {
						expect(viewResp.status).to.equal(200, 'Create temporary page view "TestListView" of type: ListView')
						expect(viewResp.body.data.id).not.null

						cy.request({
							url: `/Api/DataFields?filters=${JSON.stringify([
								{
									filterColumn: 'DataSourceId',
									operator: 'Equals',
									compareValue: sourceGuid,
								},
							])}`,
							failOnStatusCode: false,
							method: 'GET',
						}).then((fieldResp) => {
							expect(fieldResp.status).to.equal(200, 'Create temporary view list column')
							fieldResp.body.data.rows.forEach((row: Record<string, any>) => {
								if (!row.systemField)
									cy.createColumn(viewResp.body.data.id, row.id)

								if (row.name === 'Filename')
									cy.createFieldSorting(pageResp.body.data.id, row.id)
							})
						})
					})
				})
			})
		})
		
		// enable the viewer
		cy.visit('/Admin/DataStores/teststore' + guid + '/Pages/singlepage' + guid + '/Views/overview/BasicSettings')
		cy.get('#page-view-form[initdone]').find('lvl-toggle[name="showViewer"][initdone]').as('toggle')
			.should('be.visible').invoke('attr', 'value', 'true')
		cy.get('@toggle').should('have.attr', 'value', 'true')
		cy.get('[data-action="save"]').click()
		
		// connect single to multi page 
		cy.get('@singlePageId').then(pageId => {
			cy.visit('/Admin/DataStores/teststore' + guid + '/Pages/multipage' + guid + '/functions')
			cy.get('#detail-page[initdone]').shadow().as('singlePage').find('lvl-input-button').click()
			cy.get('@singlePage')
				.find('.content-container').find(`tbody > tr[value=${pageId}]`).as('columnElement').should('exist')
			cy.get('@columnElement').click()
			cy.get('[data-action="save"]').click()
		})
	})
	
	it('check file display', () => {
		// png
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementPngFileId + '/overview')
		cy.get('.file-viewer').shadow().find('iframe[data-cy=apryse-webviewer]').then(($iframe) => {
			const doc = $iframe.contents()
			cy.wrap(doc).find('body').should('not.be.undefined').find('img').should('be.visible')
		})
		cy.get('#detail-view-file-preview').find('.file-viewer').shadow().find('#noEntriesPlaceholder').should('not.exist')
		// reload and check again
		cy.reload()
		cy.get('.file-viewer').shadow().find('iframe[data-cy=apryse-webviewer]').then(($iframe) => {
			const doc = $iframe.contents()
			cy.wrap(doc).find('body').should('not.be.undefined').find('img').should('be.visible')
		})
		cy.get('#detail-view-file-preview').find('.file-viewer').shadow().find('#noEntriesPlaceholder').should('not.exist')
		// jpg
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementJpgFileId + '/overview')
		cy.get('.file-viewer').shadow().find('iframe[data-cy=apryse-webviewer]').then(($iframe) => {
			const doc = $iframe.contents()
			cy.wrap(doc).find('body').should('not.be.undefined').find('img').should('be.visible')
		})
		cy.get('#detail-view-file-preview').find('.file-viewer').shadow().find('#noEntriesPlaceholder').should('not.exist')
		// txt
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementTxtFileId + '/overview')
		cy.get('.file-viewer').shadow().find('iframe[data-cy=apryse-webviewer]').then(($iframe) => {
			const doc = $iframe.contents()
			cy.wrap(doc).find('body').should('not.be.undefined').find('[data-element="errorModal"]').should('be.visible')
		})
		cy.get('#detail-view-file-preview').find('.file-viewer').shadow().find('#noEntriesPlaceholder').should('not.exist')
		// pdf
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementPdfFileId + '/overview')
		cy.get('.file-viewer').shadow().find('iframe[data-cy=apryse-webviewer]').then(($iframe) => {
			const doc = $iframe.contents()
			cy.wrap(doc).find('body').should('not.be.undefined').find('#pageSection1').should('be.visible')
		})
		cy.get('#detail-view-file-preview').find('.file-viewer').shadow().find('#noEntriesPlaceholder').should('not.exist')
		/* TODO: add tests once office license is given 
		// docx
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementDocxFileId + '/overview')
		getWebViewer().children().first().should('be.visible')
		cy.get('#detail-view-file-preview').find('.file-viewer').find('#noEntriesPlaceholder').should('not.be.visible')
		// xlsx
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementXlsxFileId + '/overview')
		getWebViewer().children().first().should('be.visible')
		cy.get('#detail-view-file-preview').find('.file-viewer').find('#noEntriesPlaceholder').should('not.be.visible')
		// pptx
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementPptxFileId + '/overview')
		getWebViewer().children().first().should('be.visible')
		cy.get('#detail-view-file-preview').find('.file-viewer').find('#noEntriesPlaceholder').should('not.be.visible') */
		// eml
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementEmlFileId + '/overview')
		cy.get('.file-viewer').shadow().find('.mail-viewer').children().first().should('be.visible')
		cy.get('#detail-view-file-preview').find('.file-viewer').shadow().find('#noEntriesPlaceholder').should('not.exist')
		// dwg
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementDwgFileId + '/overview')
		cy.get('.file-viewer').shadow().find('iframe[data-cy=apryse-webviewer]').should('be.visible')
		cy.get('#detail-view-file-preview').find('.file-viewer').shadow().find('#noEntriesPlaceholder').should('not.exist')
		// msg
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementMsgFileId + '/overview')
		cy.get('.file-viewer').shadow().find('.mail-viewer').should('be.visible')
		cy.get('#detail-view-file-preview').find('.file-viewer').shadow().find('#noEntriesPlaceholder').should('not.exist')
		// no file
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementWithoutFileId + '/overview')
		cy.get('.file-viewer iframe[data-cy=apryse-webviewer]').should('not.exist')
		cy.get('#detail-view-file-preview').find('.file-viewer').should('not.be.visible')
		cy.get('#detail-view-file-preview').find('#detail-view-file-dropzone').should('be.visible')
		// reload and check again
		cy.reload()
		cy.get('.file-viewer iframe[data-cy=apryse-webviewer]').should('not.exist')
		cy.get('#detail-view-file-preview').find('.file-viewer').should('not.be.visible')
		cy.get('#detail-view-file-preview').find('#detail-view-file-dropzone').should('be.visible')
	})
	
	it('update pdf after manipulate document', () => {
		cy.visit('/Public/Pages/singlepage' + guid + '/' + elementPdfFileId + '/overview')
		cy.intercept(`Api/DataSources/${sourceGuid}/Elements/${elementPdfFileId}*`).as('pdfUpdateRequest')
		cy.get('.file-viewer').as('viewer')
		cy.get('@viewer').shadow().find('iframe[data-cy=apryse-webviewer]').then(($iframe) => {
			cy.wrap($iframe.contents()).as('document')
		})
		cy.get('@document').find('body').should('not.be.undefined').find('#pageSection1').should('be.visible')
		cy.get('@viewer').shadow().find('.viewer__toolbar').as('toolbar')
		cy.get('@toolbar').find('[data-action="annotate"]').click({ force: true})
		cy.get('@document').find('body').realClick()
		cy.get('@toolbar').find('[icon="save"]').click({ force: true })
		cy.wait('@pdfUpdateRequest')
	})
	
	afterEach(() => {
		// remove dataStore (this will delete the dataSource as well)
		cy.removeDataStore(guid)
	})
})