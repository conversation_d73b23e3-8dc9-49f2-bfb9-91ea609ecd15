describe('Edit Mask User View', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0
	let elementId = ''
	let referenceElementId = ''
	let anotherReferenceId = ''
	let overviewId = ''
	let formId = ''
	let pageId = ''
	let stringFieldId = ''
	let doubleFieldId = ''
	let booleanFieldId = ''
	let textFieldId = ''
	let richTextFieldId = ''
	let dateFieldId = ''
	let dateTimeFieldId = ''
	let dateTimeFixedFieldId = ''
	let timeFieldId = ''
	let timeFixedFieldId = ''
	let lookupFieldId = ''
	let virtualFieldId = ''

	before(() => {
		guid = uuid()
		// create dataStore
		cy.createDataStore(guid, true, (storeId) => {
			// create source to reference
			cy.request({
				url: '/Api/DataSources/',
				failOnStatusCode: false,
				method: 'POST',
				body: { name: 'TestSourceToReference_' + guid, dataStoreId: storeId },
			}).then((refSourceResp) => {
				expect(refSourceResp.status).to.equal(200, 'Create temporary data source: TestSourceToReference')
				expect(refSourceResp.body.data.id).not.null
				
				// create fields to reference
				cy.createField(refSourceResp.body.data.id, 'StringToReference', 'String', { length: 255 }, (stringId) => {
					cy.createField(refSourceResp.body.data.id, 'IntegerToReference', 'Integer', undefined, (integerId) => {
						// create data for reference source
						cy.request({
							url: `/PublicApi/DataSources/${refSourceResp.body.data.id}/Elements`,
							failOnStatusCode: false,
							method: 'POST',
							body: {
								values: {
									StringToReference: 'Magic the Gathering',
									IntegerToReference: '42'
								},
								groups: [ 'testgroup' ],
							},
						}).then((resp) => {
							expect(resp.status).to.equal(200, `Create record for reference source}`)
							referenceElementId = resp.body.data.id
							cy.wrap(referenceElementId).as('referenceElementId')
						})
						cy.request({
							url: `/PublicApi/DataSources/${refSourceResp.body.data.id}/Elements`,
							failOnStatusCode: false,
							method: 'POST',
							body: {
								values: {
									StringToReference: 'Card Game',
									IntegerToReference: '69'
								},
								groups: [ 'testgroup' ],
							},
						}).then((resp) => {
							expect(resp.status).to.equal(200, `Create record for reference source}`)
							anotherReferenceId = resp.body.data.id
						})
						
						// create source to test on
						cy.request({
							url: '/Api/DataSources/',
							failOnStatusCode: false,
							method: 'POST',
							body: { name: 'TestSource_' + guid, dataStoreId: storeId },
						}).then((sourceResp) => {
							expect(sourceResp.status).to.equal(200, 'Create temporary data source: TestSource')
							expect(sourceResp.body.data.id).not.null

							createWorkflows(sourceResp.body.data.id)

							// create a view fields 
							cy.createField(sourceResp.body.data.id, 'ActualName', 'String', { length: 255 }, (fieldId) => {
								stringFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'InSync', 'Boolean', undefined, (fieldId) => {
								booleanFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'Comment', 'Text', { length: 1000 }, (fieldId) => {
								textFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'RichText', 'Text', { length: 1000, richText: true }, (fieldId) => {
								richTextFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'SomeDate', 'Date', { }, (fieldId) => {
								dateFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'MagicNumber', 'Double', undefined, (fieldId) => {
								doubleFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'LunchTime', 'Time', undefined, (fieldId) => {
								timeFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'MagicMoment', 'DateTime', undefined, (fieldId) => {
								dateTimeFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'LunchTimeFixed', 'TimeFixed', undefined, (fieldId) => {
								timeFixedFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'MagicMomentFixed', 'DateTimeFixed', undefined, (fieldId) => {
								dateTimeFixedFieldId = String(fieldId)
							})
							cy.createField(sourceResp.body.data.id, 'ForeignName', 'Guid',
							{ fieldType: 'LookupField', lookupSourceId: refSourceResp.body.data.id, lookupDisplayFieldId: String(stringId) }, (lookupId) => {
								lookupFieldId = String(lookupId)
								
								// add additional display column
								cy.createFieldColumn(lookupFieldId, integerId)
									
								// add filter condition
								cy.createFieldFilter(lookupFieldId, stringId, 'NotLike', '##ActualName##')
									
								cy.createField(sourceResp.body.data.id, 'ForeignNumber', 'Guid',
								{ fieldType: 'VirtualField', virtualLookupFieldId: String(lookupId), virtualDataFieldId: String(integerId) }, (virtualId) => {
										virtualFieldId = String(virtualId)
								})
								// create single data for main
								cy.get('@referenceElementId').then(id => {
									cy.request({
										url: `/PublicApi/DataSources/${sourceResp.body.data.id}/Elements`,
										failOnStatusCode: false,
										method: 'POST',
										body: {
											values: {
												ActualName: 'Flesh and Blood',
												InSync: 'true',
												SomeDate: '2012-12-12T00:00:00Z',
												MagicNumber: '7',
												LunchTime: '12:00:00Z',
												LunchTimeFixed: '12:00:00',
												MagicMoment: '2012-12-12T12:12:00Z',
												MagicMomentFixed: '2012-12-12T12:12:00',
												ForeignName: id,
												Comment: 'Some text',
												RichText: '{"type":"doc","content":[{"type":"paragraph","attrs":{"textAlign":"left"},"content":[{"type":"text","text":"The programs that "},{"type":"text","marks":[{"type":"textStyle","attrs":{"color":"hsl(0, 100%, 50%)"}},{"type":"bold"},{"type":"underline"}],"text":"Chuck Norris"},{"type":"text","text":" writes don\'t have version numbers because he only writes them once. If a user reports a bug or has a feature request they don\'t live to see the sun set"}]}]}'
												
											},
											groups: [ 'testgroup' ],
										},
									}).then((resp) => {
										expect(resp.status).to.equal(200, `Create record for main source`)
										elementId = resp.body.data.id
										cy.wrap(elementId).as('elementId')
									})
								})
									
								//create single data page	
								cy.request({
									url: '/Api/Pages/',
									failOnStatusCode: false,
									method: 'POST',
									body: { dtoType: 'SingleData', name: 'TestPage_' + guid, dataSourceId: sourceResp.body.data.id, type: 'SingleData' },
								}).then((resp) => {
									expect(resp.status).to.equal(200, 'Create TestPage')
									expect(resp.body.data.id).not.null
									pageId = resp.body.data.id
									overviewId = resp.body.data.views[0].id
									configureMaskApi(overviewId)
									formId = resp.body.data.views[1].id
									configureMaskApi(formId)
								})
							})
						})
					})
				})
			})
		})
	})
	
	it('user view form', () => {
		cy.visit('/Public/Pages/testpage' + guid + '/' + elementId + '/form')
		cy.get('lvl-form[initdone]:not([skeleton])', {timeout: 8000}).as('form')
		// section 1 in column 2
		cy.get('@form').find('.page-column', {timeout: 8000}).eq(1, {timeout: 8000}).find('lvl-section').eq(0).as('section').should('be.visible')
		// check headline 1
		cy.get('@section').find('.Headline1').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(527, 50)

		// section 1 in column 3
		cy.get('.page-column').eq(2).find('lvl-section').eq(0).as('section').should('be.visible')
		// check headline 1
		cy.get('@section').find('.Headline1').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(368, 50)
		
		// section 1 in column 1
		cy.get('.page-column').eq(0).find('lvl-section').eq(0).as('section').shadow()
			.contains('h2', '[ExampleSectionTitle]').should('be.visible')
		// check headline 1
		cy.get('@section').find('.Headline1').parent('.grid-element-wrapper').as('element')
			.contains('label[style="color:red;"]', '[ExampleHeadline1]').should('be.visible')
		checkElementDimensions(444, 50)
		// check headline 2
		cy.get('@section').find('.Headline2').parent('.grid-element-wrapper').as('element')
			.contains('label', '[ExampleHeadline2]').should('be.visible')
		checkElementDimensions(444, 44)
		
		// section 2 in column 1
		cy.get('.page-column').eq(0).find('lvl-section').eq(1).as('section')
			.find('h2').should('not.exist')
		// section should be collapsed
		cy.get('@section').should('have.attr', 'collapsed')
		// section should open on toggle click
		cy.get('@section').shadow().find('.section__toggle').click()
		cy.get('@section').should('not.have.attr', 'collapsed')
		// check headline 3
		cy.get('@section').find('.Headline3').parent('.grid-element-wrapper').as('element')
			.contains('label', '[ExampleHeadline3]').should('be.visible')
		checkElementDimensions(444, 39)/* checkElementDimensions(226, 53) */
		// check date
		cy.get('@section').find('lvl-input[name="SomeDate"][label="[ExampleDate]"]').as('exampleDate').invoke('attr', 'value').should('contain', '2012-12-12')
		cy.get('@exampleDate').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)
		// check text
		cy.get('@section').find('.grid-element-text.Plain[style="text-align:justify;"]').parent('.grid-element-wrapper').as('element')
			.contains('Lorem Ipsum dolor').should('be.visible')
		checkElementDimensions(137, 53)
		// check double
		cy.get('@section').find('lvl-input[name="MagicNumber"][label="[ExampleDouble]"][value="7"][readonly]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(99, 53)
		// check string
		cy.get('@section').find('lvl-input[name="ActualName"][label="[ExampleString]"][value="Flesh and Blood"][required]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)
		// check separator
		cy.get('@section').find('hr')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(444, 16)
		// check dropdown
		cy.get('@section').find(`lvl-autocomplete[name="ForeignName"][label="[ExampleDropdown]"][value="${referenceElementId}"]`).as('autocomplete')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		
		// open dropdown and see if the multi column functionality is working properly
		cy.get('@autocomplete').shadow().find('lvl-input-button').click({force: true})
		cy.get('@autocomplete').shadow().find('.dropdown-content tbody tr').as('rows').should('have.length', 2)
		cy.get('@rows').eq(0).find('td > span[name=LABEL]').should('contain.text', 'Card Game')
		cy.get('@rows').eq(0).find('td > span[name=IntegerToReference]').should('contain.text', '69')
		cy.get('@rows').eq(1).find('td > span[name=LABEL]').should('contain.text', 'Magic the Gathering')
		cy.get('@rows').eq(1).find('td > span[name=IntegerToReference]').should('contain.text', '42')
		
		checkElementDimensions(137, 53)
		// check text
		cy.get('@section').find('lvl-textarea[label="[Text]"]').should('not.exist') // gridviewfield has no datafield assigned and therefore is not rendered
		// check boolean
		cy.get('@section').find('lvl-toggle[name="InSync"][label="[ExampleBoolean]"][value="true"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)
		// check date time
		cy.get('@section').find('lvl-input[name="MagicMoment"][label="[ExampleDateTime]"][value="2012-12-12T12:12Z"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(80, 53)
		// check date time fixed
		cy.get('@section').find('lvl-input[name="MagicMomentFixed"][label="[ExampleDateTimeFixed]"][value="2012-12-12T12:12"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(176, 53)
		// check text
		cy.get('@section').find('lvl-textarea[name="Comment"][label="[ExampleText]"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(80, 123)
		// check virtual double
		cy.get('@section').find('lvl-input[name="ForeignNumber"][label="[ExampleVirtualDouble]"][value="42"][readonly]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(99, 53)
		// check time
		cy.get('@section').find('lvl-input[name="LunchTime"][label="[ExampleTime]"]').as('exampleTime').invoke('attr', 'value').should('contain', '12:00Z')
		cy.get('@exampleTime').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)
		// check time fixed
		cy.get('@section').find('lvl-input[name="LunchTimeFixed"][label="[ExampleTimeFixed]"]').as('exampleTimeFixed').invoke('attr', 'value').should('contain', '12:00')
		cy.get('@exampleTime').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)
		
		// edit data
		// edit date
		cy.get('@section').find('lvl-input[name="SomeDate"]').invoke('attr', 'value', '2011-11-11')
		
		// clear string
		cy.get('@section').find('lvl-input[name="ActualName"]').shadow()
			.find('input').focus().clear({force: true})
		// try to save with empty required field
		cy.get('#content-buttons').find('[data-action="save"]').click()
		// warning should be visible that there are empty required fields
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')
		//wait for toast to close
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('not.exist')
		
		// edit string
		cy.get('@section').find('lvl-input[name="ActualName"]').shadow()
			.find('input').focus().clear({force: true}).type('Helmut')
		// edit dropdown
		cy.get('@section').find('lvl-autocomplete[name="ForeignName"]').shadow().find('lvl-input-button').click()
		cy.get('@section').find('lvl-autocomplete[name="ForeignName"]').shadow().find('.content-container')
			.find(`tbody > tr[value=${anotherReferenceId}]`).as('columnElement').should('exist')
		cy.get('@columnElement').click()
		// check if virtual field value changes accordingly
		cy.get('@section').find('lvl-input[name="ForeignNumber"][label="[ExampleVirtualDouble]"][value="69"][readonly]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')				
		// edit boolean 
		cy.get('@section').find('lvl-toggle[name="InSync"]').shadow().find('input[name="InSync"]').click()
		// edit date time
		cy.get('@section').find('lvl-input[name="MagicMoment"]').invoke('attr', 'value', '2011-11-11 11:11').invoke('attr', 'value').as('dataTimeUpdatedValue')
		// edit date time fixed
		cy.get('@section').find('lvl-input[name="MagicMomentFixed"]').invoke('attr', 'value', '2011-11-11 11:11')
		// edit time
		cy.get('@section').find('lvl-input[name="LunchTime"]').invoke('attr', 'value', '11:00').invoke('attr', 'value').as('timeUpdatedValue')
		// edit time
		cy.get('@section').find('lvl-input[name="LunchTimeFixed"]').invoke('attr', 'value', '11:00')
		
		cy.get('#content-buttons').find('[data-action="save"]').click()
		
		// overlay should show up
		cy.get('lvl-overlay[open]').should('exist')
		
		// wait for toaster to announce successful save
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('have.length', 1)
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').eq(0).shadow().find('.toast__content').should('contain.text', 'Successfully stored')
		
		// overlay should no longer be visible
		cy.get('lvl-overlay[open]').should('not.exist')
		
		// refresh and check elements again
		cy.reload()

		// section 1 in column 2
		cy.get('.page-column').eq(1).find('lvl-section').eq(0).as('section').should('be.visible')
		// check headline 1
		cy.get('@section').find('.Headline1').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(527, 50)

		// section 1 in column 3
		cy.get('.page-column').eq(2).find('lvl-section').eq(0).as('section').should('be.visible')
		// check headline 1
		cy.get('@section').find('.Headline1').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(368, 50)

		// section 1 in column 1
		cy.get('.page-column').eq(0).find('lvl-section').eq(0).as('section').shadow()
			.contains('h2', '[ExampleSectionTitle]').should('be.visible')
		// check headline 1
		cy.get('@section').find('.Headline1').parent('.grid-element-wrapper').as('element')
			.contains('label[style="color:red;"]', '[ExampleHeadline1]').should('be.visible')
		checkElementDimensions(444, 50)
		// check headline 2
		cy.get('@section').find('.Headline2').parent('.grid-element-wrapper').as('element')
			.contains('label', '[ExampleHeadline2]').should('be.visible')
		checkElementDimensions(444, 44)

		// section 2 in column 1
		cy.get('.page-column').eq(0).find('lvl-section').eq(1).as('section')
			.find('h2').should('not.exist')
		// check headline 3
		cy.get('@section').find('.Headline3').parent('.grid-element-wrapper').as('element')
			.contains('label', '[ExampleHeadline3]').should('be.visible')
		checkElementDimensions(444, 39)/* checkElementDimensions(226, 53) */
		// check date
		cy.get('@section').find('lvl-input[name="SomeDate"][label="[ExampleDate]"]').as('exampleDate').invoke('attr', 'value').should('contain', '2011-11-11')
		cy.get('@exampleDate').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)
		// check text
		cy.get('@section').find('.grid-element-text.Plain[style="text-align:justify;"]').parent('.grid-element-wrapper').as('element')
			.contains('Lorem Ipsum dolor').should('be.visible')
		checkElementDimensions(137, 53)
		// check double
		cy.get('@section').find('lvl-input[name="MagicNumber"][label="[ExampleDouble]"][value="7"][readonly]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(99, 53)
		// check string
		cy.get('@section').find('lvl-input[name="ActualName"][label="[ExampleString]"][value="Helmut"][required]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)
		// check separator
		cy.get('@section').find('hr')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(444, 16)
		// check dropdown
		cy.get('@section').find(`lvl-autocomplete[name="ForeignName"][label="[ExampleDropdown]"][value="${anotherReferenceId}"]`)
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)
		// check text
		cy.get('@section').find('lvl-textarea[label="[Text]"]').should('not.exist') // gridviewfield has no datafield assigned and therefore is not rendered
		// check boolean
		cy.get('@section').find('lvl-toggle[name="InSync"][label="[ExampleBoolean]"][value="false"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)
		// check date time
		cy.get<string>('@dataTimeUpdatedValue').then((value) => {
			cy.get('@section').find(`lvl-input[name="MagicMoment"][label="[ExampleDateTime]"][value="${value}"]`)
				.parent('.grid-element-wrapper').as('element').should('be.visible')
			checkElementDimensions(80, 53)
		})
		// check date time fixed
		cy.get('@section').find('lvl-input[name="MagicMomentFixed"][label="[ExampleDateTimeFixed]"][value="2011-11-11T11:11"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(176, 53)
		// check text
		cy.get('@section').find('lvl-textarea[name="Comment"][label="[ExampleText]"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(80, 123)
		// check virtual double
		cy.get('@section').find('lvl-input[name="ForeignNumber"][label="[ExampleVirtualDouble]"][value="69"][readonly]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(99, 53)
		// check time
		cy.get<string>('@timeUpdatedValue').then((value) => {
			cy.get('@section').find(`lvl-input[name="LunchTime"][label="[ExampleTime]"][value="${value}"]`)
				.parent('.grid-element-wrapper').as('element').should('be.visible')
			checkElementDimensions(137, 53)
		})
		// check time fixed
		cy.get('@section').find('lvl-input[name="LunchTimeFixed"][label="[ExampleTimeFixed]"][value="11:00"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(137, 53)

		// type invalid stuff in lookup field -> virtual field should clear
		cy.get('@section').find('lvl-autocomplete[name="ForeignName"]').as("lookupField").shadow().find('input').clear().type('invalidValue').blur({ force: true })
		cy.get('@section').find('lvl-input[name="ForeignNumber"]').as('virtualField').invoke('prop', 'value').should('equal', 0)
		
		// set to valid value and retest clear value afterwards
		cy.get('@section').find('lvl-autocomplete[name="ForeignName"]').shadow().find('lvl-input-button').click()
		cy.get('@lookupField').shadow().find('.content-container')
			.find(`tbody > tr[value=${referenceElementId}]`).as('columnElement').should('exist')
		cy.get('@columnElement').click()
		cy.get('@virtualField').invoke('prop', 'value').should('equal', 42)

		cy.get('@lookupField').invoke('prop', 'value', '')
		cy.get('@virtualField').invoke('prop', 'value').should('equal', 0)
		
		// change value of ActualName (which is part of a filter condition of ForeignName) and check result of ForeignName Autocomplete
		cy.get('@section').find('lvl-input[name="ActualName"][label="[ExampleString]"]').shadow().find('input').focus().clear().type('Card').blur()
		cy.get('@lookupField').shadow().find('lvl-input-button').click({force: true})		
		cy.get('@lookupField').shadow().find('.dropdown-content tbody tr').should('have.length', 1)
		cy.get('@lookupField').shadow().find('.content-container').find(`tbody > tr[value=${referenceElementId}]`).click()
		
		// save and reload form -> autocomplete should still be filtered correctly
		cy.get('#content-buttons').find('[data-action="save"]').click()

		// overlay should show up
		cy.get('lvl-overlay[open]').should('exist')

		// wait for toaster to announce successful save
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('have.length', 1)
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').eq(0).shadow().find('.toast__content').should('contain.text', 'Successfully stored')

		// overlay should no longer be visible
		cy.get('lvl-overlay[open]').should('not.exist')

		// refresh and check autocomplete result
		cy.reload()
		
		cy.get('@section').find('lvl-input[name="ActualName"][value="Card"]')
		cy.get('@autocomplete').shadow().find('lvl-input-button').click({force: true})
		cy.get('@autocomplete').shadow().find('.dropdown-content tbody tr').as('rows').should('have.length', 1)
	})
	
	it('user view overview', () => {
		cy.visit('/Public/Pages/testpage' + guid + '/' + elementId + '/overview')
		cy.get('lvl-form[initdone]:not([skeleton])', {timeout: 8000}).should('exist')
		
		// section 1 in column 2
		cy.get('.page-column', {timeout: 8000}).eq(1, {timeout: 8000}).find('lvl-section').eq(0).as('section').should('be.visible')
		// check headline 1
		cy.get('@section').find('.Headline1').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(527, 50)

		// section 1 in column 3
		cy.get('.page-column').eq(2).find('lvl-section').eq(0).as('section').should('be.visible')
		// check headline 1
		cy.get('@section').find('.Headline1').parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(368, 50)

		// section 1 in column 1
		cy.get('.page-column').eq(0).find('lvl-section').eq(0).as('section').shadow()
			.contains('h2', '[ExampleSectionTitle]').should('be.visible')
		// check headline 1
		cy.get('@section').find('.Headline1').parent('.grid-element-wrapper').as('element')
			.contains('label[style="color:red;"]', '[ExampleHeadline1]').should('be.visible')
		checkElementDimensions(444, 50)
		// check headline 2
		cy.get('@section').find('.Headline2').parent('.grid-element-wrapper').as('element')
			.contains('label', '[ExampleHeadline2]').should('be.visible')
		checkElementDimensions(444, 44)

		// section 2 in column 1
		cy.get('.page-column').eq(0).find('lvl-section').eq(1).as('section')
			.find('h2').should('not.exist')
		// section should be collapsed
		cy.get('@section').should('have.attr', 'collapsed')
		// section should open on toggle click
		cy.get('@section').shadow().find('.section__toggle').click()
		cy.get('@section').should('not.have.attr', 'collapsed')
		// check headline 3
		cy.get('@section').find('.Headline3').parent('.grid-element-wrapper').as('element')
			.contains('label', '[ExampleHeadline3]').should('be.visible')
		checkElementDimensions(444, 39)/* checkElementDimensions(226, 30) */
		// check date
		cy.get('@section').find('lvl-value-formatter[name="SomeDate"][type="date"]').shadow().should('contain.text', '11/11/2011')
		cy.get('@section').find('lvl-value-formatter[name="SomeDate"][type="date"]').parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleDate]')
		checkElementDimensions(137, 30)
		// check text
		cy.get('@section').find('.grid-element-text.Plain[style="text-align:justify;"]').parent('.grid-element-wrapper').as('element')
			.contains('Lorem Ipsum dolor').should('be.visible')
		checkElementDimensions(137, 30)
		// check double
		cy.get('@section').find('lvl-value-formatter[name="MagicNumber"][type="double"]').shadow().should('contain.text', '7.00')
		cy.get('@section').find('lvl-value-formatter[name="MagicNumber"][type="double"]').parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleDouble]')
		checkElementDimensions(99, 30)
		// check string
		cy.get('@section').find('lvl-value-formatter[name="ActualName"][type="string"]').shadow().should('contain.text', 'Card')
		cy.get('@section').find('lvl-value-formatter[name="ActualName"][type="string"]').parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleString]')
		checkElementDimensions(137, 30)
		// check separator
		cy.get('@section').find('hr')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		checkElementDimensions(444, 16)
		// check dropdown
		cy.get('@section').find('lvl-value-formatter[name="ForeignName"][type="string"]').shadow().should('contain.text', 'Magic the Gathering')
		cy.get('@section').find('lvl-value-formatter[name="ForeignName"][type="string"]').parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleDropdown]')
		checkElementDimensions(137, 30)
		// check boolean
		cy.get('@section').find('lvl-value-formatter[name="InSync"][type="boolean"]').shadow().should('contain.text', 'no')
		cy.get('@section').find('lvl-value-formatter[name="InSync"][type="boolean"]').parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleBoolean]')
		checkElementDimensions(137, 30)
		// check date time
		cy.get('@section').find('lvl-value-formatter[name="MagicMoment"][type="datetime"]').shadow().should('contain.text', '11/11/2011 11:11 AM')
		cy.get('@section').find('lvl-value-formatter[name="MagicMoment"][type="datetime"]').parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleDateTime]')
		checkElementDimensions(80, 44)
		// check date time fixed
		cy.get('@section').find('lvl-value-formatter[name="MagicMomentFixed"][type="datetime"]').shadow().should('contain.text', '11/11/2011 11:11 AM')
		cy.get('@section').find('lvl-value-formatter[name="MagicMomentFixed"][type="datetime"]').parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleDateTimeFixed]')
		checkElementDimensions(176, 30)
		// check text
		cy.get('@section').find('lvl-value-formatter[name="Comment"][type="text"]').shadow().should('contain.text','Some text')
		cy.get('@section').find('lvl-value-formatter[name="Comment"][type="text"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleText]')
		checkElementDimensions(80, 91)
		// check richtext
		cy.get('@section').find('lvl-value-formatter[name="RichText"][type="text"]').shadow().find('p > span')
			.should('contain.text', 'Chuck Norris')
			.should('have.css', 'color', 'rgb(255, 0, 0)')
		cy.get('@section').find('lvl-value-formatter[name="RichText"][type="text"]')
			.parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleRichText]')
		// check virtual double
		cy.get('@section').find('lvl-value-formatter[name="ForeignNumber"][type="integer"]').shadow().should('contain.text', '42')
		cy.get('@section').find('lvl-value-formatter[name="ForeignNumber"][type="integer"]').parents('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleVirtualDouble]')
		checkElementDimensions(99, 30)
		// check time
		cy.get('@section').find('lvl-value-formatter[name="LunchTime"][type="time"]').shadow().should('contain.text', '11:00 AM')
		cy.get('@section').find('lvl-value-formatter[name="LunchTime"][type="time"]').parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleTime]')
		checkElementDimensions(137, 44)
		// check time
		cy.get('@section').find('lvl-value-formatter[name="LunchTimeFixed"][type="time"]').shadow().should('contain.text', '11:00 AM')
		cy.get('@section').find('lvl-value-formatter[name="LunchTimeFixed"][type="time"]').parent('.grid-element-wrapper').as('element').should('be.visible')
		cy.get('@element').contains('label', '[ExampleTimeFixed]')
		checkElementDimensions(137, 30)
		
		// check direct edit
		cy.get('lvl-section[heading="[ExampleSection]"]').as('section')
		cy.get('@section').find('lvl-button[data-action="edit"]').click()
		
		// dialog form should contain correct heading and correct values
		cy.get('lvl-dialog[heading="[ExampleSection]"]').as('dialog').find('lvl-form').should('be.visible')
		cy.get('@dialog').find('lvl-form').as('directEditForm')
		cy.get('@directEditForm').find('lvl-input[name="SomeDate"][label="[ExampleDate]"]').invoke('attr', 'value').should('contain', '2011-11-11')
		cy.get('@directEditForm').find('lvl-input[name="ActualName"][label="[ExampleString]"]').invoke('attr', 'value').should('contain', 'Card')
		cy.get('@directEditForm').find('lvl-autocomplete[name="ForeignName"][label="[ExampleDropdown]"]').shadow().find('input').should('have.value', 'Magic the Gathering')
		cy.get('@directEditForm').find('lvl-toggle[name="InSync"][label="[ExampleBoolean]"]').invoke('attr', 'value').should('contain', 'false')
		cy.get('@directEditForm').find('lvl-input[name="MagicMoment"][label="[ExampleDateTime]"]').shadow().find('input').invoke('val').should('contain', '11/11/2011 11:11 AM')
		cy.get('@directEditForm').find('lvl-textarea[name="Comment"][label="[ExampleText]"]').invoke('val').should('contain', 'Some text')
		cy.get('@directEditForm').find('lvl-input[name="LunchTime"][label="[ExampleTime]"]').shadow().find('input').invoke('val').should('contain', '11:00 AM')
		cy.get('@directEditForm').find('lvl-input[name="ForeignNumber"][label="[ExampleVirtualDouble]"]').invoke('attr', 'value').should('contain', '42')
		cy.get('@directEditForm').find('lvl-input[name="MagicNumber"][label="[ExampleDouble]"]').invoke('attr', 'value').should('contain', '7')
		cy.get('@directEditForm').find('lvl-input[name="MagicMomentFixed"][label="[ExampleDateTimeFixed]"]').invoke('attr', 'value').should('contain', '2011-11-11T11:11')
		cy.get('@directEditForm').find('lvl-input[name="LunchTimeFixed"][label="[ExampleTimeFixed]"]').invoke('attr', 'value').should('contain', '11:00')
		cy.get('@directEditForm').find('lvl-rich-text[name="RichText"][label="[ExampleRichText]"]').invoke('val').should('contain', 'The programs')
		
		// changes inside the dialog should be reverted if hitting abort and reopening the dialog
		cy.get('@directEditForm').find('lvl-input[name="ActualName"][label="[ExampleString]"]').shadow().find('input').focus().clear().type('Karl Heinz')
		cy.get('@directEditForm').find('lvl-textarea[name="Comment"][label="[ExampleText]"]').shadow().find('textarea').focus().clear().type('Another Text')
		cy.get('@directEditForm').find('lvl-toggle[name="InSync"][label="[ExampleBoolean]"]').invoke('attr', 'value', 'true')
		cy.get('@directEditForm').find('lvl-input[name="LunchTime"][label="[ExampleTime]"]').invoke('attr', 'value', '14:00')
		cy.get('@directEditForm').find('lvl-input[name="SomeDate"][label="[ExampleDate]"]').invoke('attr', 'value', '')

		cy.get('@dialog').find('lvl-button[slot="button-left"]').click()
		cy.get('@section').find('lvl-button[data-action="edit"]').click()

		cy.get('@directEditForm').find('lvl-input[name="ActualName"][label="[ExampleString]"]').invoke('attr', 'value').should('contain', 'Card')
		cy.get('@directEditForm').find('lvl-textarea[name="Comment"][label="[ExampleText]"]').invoke('val').should('contain', 'Some text')
		cy.get('@directEditForm').find('lvl-toggle[name="InSync"][label="[ExampleBoolean]"]').invoke('attr', 'value').should('contain', 'false')
		cy.get('@directEditForm').find('lvl-input[name="LunchTime"][label="[ExampleTime]"]').shadow().find('input').invoke('val').should('contain', '11:00 AM')
		cy.get('@directEditForm').find('lvl-input[name="SomeDate"][label="[ExampleDate]"]').invoke('attr', 'value').should('contain', '2011-11-11')

		// if changes get stored, they need to be visible inside the main form

		cy.get('@directEditForm').find('lvl-input[name="ActualName"][label="[ExampleString]"]').shadow().find('input').focus().clear().type('Karl Heinz')
		cy.get('@directEditForm').find('lvl-textarea[name="Comment"][label="[ExampleText]"]').shadow().find('textarea').focus().clear().type('Another Text')
		cy.get('@directEditForm').find('lvl-toggle[name="InSync"][label="[ExampleBoolean]"]').invoke('attr', 'value', 'true')
		cy.get('@directEditForm').find('lvl-input[name="LunchTime"][label="[ExampleTime]"]').invoke('attr', 'value', '14:00')
		cy.get('@directEditForm').find('lvl-input[name="SomeDate"][label="[ExampleDate]"]').invoke('attr', 'value', '')
		
		cy.get('@dialog').find('lvl-button[slot="button-right"]').click()

		cy.get('#page-detail lvl-form').as('form')
		cy.get('@form').find('lvl-value-formatter[name="ActualName"]').shadow().should('contain.text', 'Karl Heinz')
		cy.get('@form').find('lvl-value-formatter[name="Comment"]').shadow().should('contain.text', 'Another Text')
		cy.get('@form').find('lvl-value-formatter[name="InSync"]').shadow().should('contain.text', 'yes')
		cy.get('@form').find('lvl-value-formatter[name="LunchTime"]').shadow().should('contain.text', '02:00 PM')
		
		// Empty fields should be hidden in the main form..
		cy.get('@form').find('lvl-value-formatter[name="SomeDate"]').closest('.grid-element-wrapper').should('not.be.visible')
		
		// .. empty fields should be visible in Dialog
		cy.get('@section').find('lvl-button[data-action="edit"]').click()
		cy.get('@directEditForm').find('lvl-input[name="SomeDate"][label="[ExampleDate]"]').should('be.visible')
		
		// refilled fields should re-appear in main form
		cy.get('@directEditForm').find('lvl-input[name="SomeDate"][label="[ExampleDate]"]').invoke('attr', 'value', '2024-12-11')
		cy.get('@dialog').find('lvl-button[slot="button-right"]').click()
		cy.get('@form').find('lvl-value-formatter[name="SomeDate"]').should('be.visible')
		cy.get('@form').find('lvl-value-formatter[name="SomeDate"]').shadow().should('contain.text', '12/11/2024')
	})

	it('user view workflows', () => {
		cy.visit('/Public/Pages/testpage' + guid + '/' + elementId + '/overview')
		cy.get('lvl-form[initdone]:not([skeleton])', {timeout: 8000}).should('exist')
		
		// check if workflows are loading correctly
		cy.get('#page-detail .page-view__header').as('pageHeader').should('exist')
		cy.get('@pageHeader').find('lvl-workflow-item').as('workflowItems').should('have.length', 3)
		
		// workflows are rendered in reverse order; slot 3 is the first item, slot 1 the last
		cy.get('@workflowItems').eq(2).as('thirdWorkflowItem').should('have.attr', 'workflow-name', '[ThirdWorkflow]')
		cy.get('@thirdWorkflowItem').should('have.attr', 'label', '[Start]')
		cy.get('@thirdWorkflowItem').should('have.attr', 'state-type', 'start')
		cy.get('@thirdWorkflowItem').should('have.attr', 'icon', 'rocket-launch')

		// check actions - third workflow's action are sorted differently to test sorting as well
		cy.get('@thirdWorkflowItem').find('lvl-menu-item').as('thirdWorkflowMenuItems').should('have.length', 3)
		cy.get('@thirdWorkflowMenuItems').eq(0).should('have.attr', 'label', '[Start]')
		cy.get('@thirdWorkflowMenuItems').eq(0).should('have.attr', 'data-state', 'Start')
		cy.get('@thirdWorkflowMenuItems').eq(0).should('have.attr', 'data-icon', 'rocket-launch')

		cy.get('@thirdWorkflowMenuItems').eq(1).should('have.attr', 'label', '[Positive]')
		cy.get('@thirdWorkflowMenuItems').eq(1).should('have.attr', 'data-state', 'Positive')
		cy.get('@thirdWorkflowMenuItems').eq(1).should('have.attr', 'data-icon', 'check')

		cy.get('@thirdWorkflowMenuItems').eq(2).should('have.attr', 'label', '[InProgress]')
		cy.get('@thirdWorkflowMenuItems').eq(2).should('have.attr', 'data-state', 'InProgress')
		cy.get('@thirdWorkflowMenuItems').eq(2).should('have.attr', 'data-icon', 'arrow-progress')
		
		cy.get('@workflowItems').eq(1).as('secondWorkflowItem').should('have.attr', 'workflow-name', '[SecondWorkflow]')
		cy.get('@secondWorkflowItem').should('have.attr', 'label', '[Start]')
		cy.get('@secondWorkflowItem').should('have.attr', 'state-type', 'start')
		cy.get('@secondWorkflowItem').should('have.attr', 'icon', 'rocket-launch')

		cy.get('@secondWorkflowItem').find('lvl-menu-item').as('secondWorkflowMenuItems').should('have.length', 3)
		cy.get('@secondWorkflowMenuItems').eq(0).should('have.attr', 'label', '[Start]')
		cy.get('@secondWorkflowMenuItems').eq(0).should('have.attr', 'data-state', 'Start')
		cy.get('@secondWorkflowMenuItems').eq(0).should('have.attr', 'data-icon', 'rocket-launch')

		cy.get('@secondWorkflowMenuItems').eq(1).should('have.attr', 'label', '[InProgress]')
		cy.get('@secondWorkflowMenuItems').eq(1).should('have.attr', 'data-state', 'InProgress')
		cy.get('@secondWorkflowMenuItems').eq(1).should('have.attr', 'data-icon', 'arrow-progress')

		cy.get('@secondWorkflowMenuItems').eq(2).should('have.attr', 'label', '[Positive]')
		cy.get('@secondWorkflowMenuItems').eq(2).should('have.attr', 'data-state', 'Positive')
		cy.get('@secondWorkflowMenuItems').eq(2).should('have.attr', 'data-icon', 'check')
		
		cy.get('@workflowItems').eq(0).as('firstWorkflowItem').should('have.attr', 'workflow-name', '[FirstWorkflow]')
		cy.get('@firstWorkflowItem').should('have.attr', 'label', '[Start]')
		cy.get('@firstWorkflowItem').should('have.attr', 'state-type', 'start')
		cy.get('@firstWorkflowItem').should('have.attr', 'icon', 'rocket-launch')

		cy.get('@firstWorkflowItem').find('lvl-menu-item').as('firstWorkflowMenuItems').should('have.length', 4)
		cy.get('@firstWorkflowMenuItems').eq(0).should('have.attr', 'label', '[Start]')
		cy.get('@firstWorkflowMenuItems').eq(0).should('have.attr', 'data-state', 'Start')
		cy.get('@firstWorkflowMenuItems').eq(0).should('have.attr', 'data-icon', 'rocket-launch')

		cy.get('@firstWorkflowMenuItems').eq(1).should('have.attr', 'label', '[InProgress]')
		cy.get('@firstWorkflowMenuItems').eq(1).should('have.attr', 'data-state', 'InProgress')
		cy.get('@firstWorkflowMenuItems').eq(1).should('have.attr', 'data-icon', 'arrow-progress')

		cy.get('@firstWorkflowMenuItems').eq(2).should('have.attr', 'label', '[Positive]')
		cy.get('@firstWorkflowMenuItems').eq(2).should('have.attr', 'data-state', 'Positive')
		cy.get('@firstWorkflowMenuItems').eq(2).should('have.attr', 'data-icon', 'check')

		cy.get('@firstWorkflowMenuItems').eq(3).should('have.attr', 'label', '[Negative]')
		cy.get('@firstWorkflowMenuItems').eq(3).should('have.attr', 'data-state', 'Negative')
		cy.get('@firstWorkflowMenuItems').eq(3).should('have.attr', 'data-icon', 'x')
		
		// test status changes
		cy.get('@thirdWorkflowMenuItems').eq(0).click({force: true})
		cy.get('@thirdWorkflowItem').should('have.attr', 'label', '[Start]')
		cy.get('@thirdWorkflowItem').should('have.attr', 'state-type', 'start')
		cy.get('@thirdWorkflowItem').should('have.attr', 'icon', 'rocket-launch')
		
		cy.get('@thirdWorkflowMenuItems').eq(1).click({force: true})
		cy.get('@thirdWorkflowItem').should('have.attr', 'label', '[Positive]')
		cy.get('@thirdWorkflowItem').should('have.attr', 'state-type', 'positive')
		cy.get('@thirdWorkflowItem').should('have.attr', 'icon', 'check')

		cy.wait(500)
		
		cy.get('@thirdWorkflowMenuItems').eq(2).click({force: true})
		cy.get('@thirdWorkflowItem').should('have.attr', 'label', '[InProgress]')
		cy.get('@thirdWorkflowItem').should('have.attr', 'state-type', 'inprogress')
		cy.get('@thirdWorkflowItem').should('have.attr', 'icon', 'arrow-progress')

		cy.wait(500)

		cy.get('@secondWorkflowMenuItems').eq(1).click({force: true})
		cy.get('@secondWorkflowItem').should('have.attr', 'label', '[InProgress]')
		cy.get('@secondWorkflowItem').should('have.attr', 'state-type', 'inprogress')
		cy.get('@secondWorkflowItem').should('have.attr', 'icon', 'arrow-progress')

		cy.wait(500)

		cy.get('@firstWorkflowMenuItems').eq(3).click({force: true})
		cy.get('@firstWorkflowItem').should('have.attr', 'label', '[Negative]')
		cy.get('@firstWorkflowItem').should('have.attr', 'state-type', 'negative')
		cy.get('@firstWorkflowItem').should('have.attr', 'icon', 'x')
		
		// check if status changes are still present after reload
		cy.reload()
		cy.wait(500)

		cy.get('@thirdWorkflowItem').should('have.attr', 'label', '[InProgress]')
		cy.get('@thirdWorkflowItem').should('have.attr', 'state-type', 'inprogress')
		cy.get('@thirdWorkflowItem').should('have.attr', 'icon', 'arrow-progress')
		
		cy.get('@secondWorkflowItem').should('have.attr', 'label', '[InProgress]')
		cy.get('@secondWorkflowItem').should('have.attr', 'state-type', 'inprogress')
		cy.get('@secondWorkflowItem').should('have.attr', 'icon', 'arrow-progress')

		cy.get('@firstWorkflowItem').should('have.attr', 'label', '[Negative]')
		cy.get('@firstWorkflowItem').should('have.attr', 'state-type', 'negative')
		cy.get('@firstWorkflowItem').should('have.attr', 'icon', 'x')
	})
	
	after(() => {
		// remove dataStore (this will delete the dataSource as well)
		cy.removeDataStore(guid)
	})
	
	function configureMaskApi(pageViewId: string) {
		// edit basic settings
		cy.request({
			url: '/Api/PageViews/' + pageViewId,
			failOnStatusCode: false,
			method: 'PATCH',
			body: {
				dtoType: "Grid",
				columnCount: 3,
				columnOneMinWidth: 500,
				columnOneRatio: 1,
				columnThreeMinWidth: 0,
				columnThreeRatio: 1,
				columnTwoMinWidth: 10,
				columnTwoRatio: 2,
				pageId: pageId,
				position: -2,
				showViewer: true,
				type: "Grid",
				viewerRatio: 1
			}
		}).then((pageViewResp) => {
			expect(pageViewResp.status).to.equal(200, 'Edited Basic Settings')
			expect(pageViewResp.body.data.id).not.null
		})
		// configure mask
		// section 1 column 1
		cy.request({
			url: '/Api/GridViewSections/',
			failOnStatusCode: false,
			method: 'POST',
			body: {
				gridViewId: pageViewId,
				position: 0,
				gridViewColumn: 0,
			}
		}).then((sectionPageResp) => {
			expect(sectionPageResp.status).to.equal(200, 'Created section')
			expect(sectionPageResp.body.data.id).not.null
			cy.request({
				url: '/Api/GridViewSections/' + sectionPageResp.body.data.id,
				failOnStatusCode: false,
				method: 'PATCH',
				body: {
					allowMinimize: false,
					showTitle: true,
					startMinimized: false,
					title: "ExampleSectionTitle"
				}
			})
			// add Headline 1
			cy.request({
				url: '/Api/GridViewTexts/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "25",
					ColStart: "1",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "2",
					RowStart: "1",
					TextType: "Headline1",
					text: "ExampleHeadline1"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Headline1')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewTexts/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						textType: "Headline1",
						color: "red",
						text: "ExampleHeadline1"
					}
				})
			})
			// add Headline 2
			cy.request({
				url: '/Api/GridViewTexts/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "25",
					ColStart: "1",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "3",
					RowStart: "2",
					TextType: "Headline2",
					text: "ExampleHeadline2"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Headline3')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewTexts/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						textType: "Headline2",
						text: "ExampleHeadline2"
					}
				})
			})
		})
		// add section 2 column 1
		cy.request({
			url: '/Api/GridViewSections/',
			failOnStatusCode: false,
			method: 'POST',
			body: {
				gridViewId: pageViewId,
				position: 1,
				gridViewColumn: 0
			}
		}).then((sectionPageResp) => {
			expect(sectionPageResp.status).to.equal(200, 'Created section')
			expect(sectionPageResp.body.data.id).not.null
			cy.request({
				url: '/Api/GridViewSections/' + sectionPageResp.body.data.id,
				failOnStatusCode: false,
				method: 'PATCH',
				body: {
					allowMinimize: true,
					showTitle: true,
					startMinimized: true,
					title: "ExampleSection"
				}
			})
			// add Headline 3
			cy.request({
				url: '/Api/GridViewTexts/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "25",
					ColStart: "1",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "2",
					RowStart: "1",
					TextType: "Headline3",
					text: "ExampleHeadline3"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Headline3')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewTexts/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						textType: "Headline3",
						text: "ExampleHeadline3"
					}
				})
			})
			// add Text
			cy.request({
				url: '/Api/GridViewTexts/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "9",
					ColStart: "1",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "3",
					RowStart: "2",
					TextType: "Plain",
					label: "ExampleText",
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Text')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewTexts/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						text: "Lorem Ipsum dolor.",
						textAlign: "Justify"
					}
				})
			})
			// add String input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "25",
					ColStart: "17",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "3",
					RowStart: "2",
					DataType: "String",
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created String input')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						required: true,
						dataFieldId: stringFieldId,
						label: "ExampleString",
					}
				})
			})
			// add Double input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "24",
					ColStart: "18",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "7",
					RowStart: "6",
					DataType: "Double",
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Double input')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: doubleFieldId,
						label: "ExampleDouble",
						readonly: true
					}
				})
			})
			// add Date input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "17",
					ColStart: "9",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "3",
					RowStart: "2",
					DataType: "Date"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Date input')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: dateFieldId,
						label: "ExampleDate",
					}
				})
			})
			// add Separator
			cy.request({
				url: '/Api/GridViewTexts/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "25",
					ColStart: "1",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "4",
					RowStart: "3",
					TextType: "Separator",
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Separator')
				expect(elementResp.body.data.id).not.null
			})
			// add Dropdown input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "9",
					ColStart: "1",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "5",
					RowStart: "4",
					DataType: "Guid"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Dropdown input')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: lookupFieldId,
						label: "ExampleDropdown",
					}
				})
			})
			// add empty Text input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "12",
					ColStart: "9",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "6",
					RowStart: "4",
					DataType: "Text"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Text')
				expect(elementResp.body.data.id).not.null
			})
			// add Boolean input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "25",
					ColStart: "17",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "5",
					RowStart: "4",
					DataType: "Boolean"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Boolean')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: booleanFieldId,
						label: "ExampleBoolean",
					}
				})
			})
			// add DateTime input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "6",
					ColStart: "1",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "6",
					RowStart: "5",
					DataType: "DateTime"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created DateTime')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: dateTimeFieldId,
						label: "ExampleDateTime",
					}
				})
			})
			// add DateTimeFixed input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColStart: "1",
					ColEnd: "11",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowStart: "9",
					RowEnd: "10",
					DataType: "DateTime"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created DateTime Fixed')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: dateTimeFixedFieldId,
						label: "ExampleDateTimeFixed",
					}
				})
			})
			// add Text input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "17",
					ColStart: "12",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "7",
					RowStart: "5",
					DataType: "Text"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Text')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: textFieldId,
						label: "ExampleText",
					}
				})
			})
			// add RichText input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "17",
					ColStart: "12",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "10",
					RowStart: "7",
					DataType: "Text"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Text')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: richTextFieldId,
						label: "ExampleRichText",
					}
				})
			})
			// add Time input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "25",
					ColStart: "17",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "6",
					RowStart: "5",
					DataType: "Time"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Time')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: timeFieldId,
						label: "ExampleTime",
					}
				})
			})
			// add Time input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColStart: "17",
					ColEnd: "25",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowStart: "9",
					RowEnd: "10",
					DataType: "Time"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Time Fixed')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: timeFixedFieldId,
						label: "ExampleTimeFixed",
					}
				})
			})
			// add Virtual Double input
			cy.request({
				url: '/Api/GridViewFields/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "2",
					ColStart: "8",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "7",
					RowStart: "6",
					DataType: "Double"
				}
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Virtual Double')
				expect(elementResp.body.data.id).not.null
				cy.request({
					url: '/Api/GridViewFields/' + elementResp.body.data.id,
					failOnStatusCode: false,
					method: 'PATCH',
					body: {
						helpText: "This is not helpful",
						placeholder: "Your advertising could be here!",
						dataFieldId: virtualFieldId,
						label: "ExampleVirtualDouble",
					}
				})
			})
		})
		// section 1 column 2
		cy.request({
			url: '/Api/GridViewSections/',
			failOnStatusCode: false,
			method: 'POST',
			body: {
				gridViewId: pageViewId,
				position: 0,
				gridViewColumn: 1,
			}
		}).then((sectionPageResp) => {
			expect(sectionPageResp.status).to.equal(200, 'Created section')
			expect(sectionPageResp.body.data.id).not.null
			cy.request({
				url: '/Api/GridViewSections/' + sectionPageResp.body.data.id,
				failOnStatusCode: false,
				method: 'PATCH',
				body: {
					allowMinimize: true,
					showTitle: false,
					startMinimized: false,
					title: "ExampleSection"
				}
			})
			// add Headline 1
			cy.request({
				url: '/Api/GridViewTexts/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "25",
					ColStart: "1",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "2",
					RowStart: "1",
					TextType: "Headline1",
					color: "red",
					text: "ExampleHeadline1"
				},
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Headline1')
				expect(elementResp.body.data.id).not.null
			})
		})
		// section 1 column 3
		cy.request({
			url: '/Api/GridViewSections/',
			failOnStatusCode: false,
			method: 'POST',
			body: {
				gridViewId: pageViewId,
				position: 0,
				gridViewColumn: 2,
			}
		}).then((sectionPageResp) => {
			expect(sectionPageResp.status).to.equal(200, 'Created section')
			expect(sectionPageResp.body.data.id).not.null
			cy.request({
				url: '/Api/GridViewSections/' + sectionPageResp.body.data.id,
				failOnStatusCode: false,
				method: 'PATCH',
				body: {
					allowMinimize: false,
					showTitle: false ,
					startMinimized: true,
					title: "ExampleSection"
				}
			})
			// add Headline 1
			cy.request({
				url: '/Api/GridViewTexts/',
				failOnStatusCode: false,
				method: 'POST',
				body: {
					ColEnd: "25",
					ColStart: "1",
					GridViewId: pageViewId,
					SectionId: sectionPageResp.body.data.id,
					RowEnd: "2",
					RowStart: "1",
					TextType: "Headline1",
					color: "red",
					text: "ExampleHeadline1"
				},
			}).then((elementResp) => {
				expect(elementResp.status).to.equal(200, 'Created Headline1')
				expect(elementResp.body.data.id).not.null
			})
		})
	}
	
	function checkElementDimensions(width: number, height: number){
		cy.get('@element').then(element => {
			// get element coordinates
			let elementWidth = element[0].getBoundingClientRect().width
			let elementHeight = element[0].getBoundingClientRect().height
			
			// compare coordinates
			expect(elementWidth <= width + 2 && elementWidth >= width - 2, `Width: ${elementWidth} ~= ${width} +-2`).to.be.true
			expect(elementHeight <= height + 2 && elementHeight >= height - 2, `Height: ${elementHeight} ~= ${height} +-2`).to.be.true
		})
	}
	
	function createWorkflows(dataSourceId: string) {
		cy.createWorkflow(dataSourceId, 'FirstWorkflow').as('firstWorkflow')
		cy.createWorkflowNode('@firstWorkflow', 'InProgress', 1, 'arrow-progress')
		cy.createWorkflowNode('@firstWorkflow', 'Positive', 2, 'check')
		cy.createWorkflowNode('@firstWorkflow', 'Negative', 3, 'x')

		cy.createWorkflow(dataSourceId, 'SecondWorkflow', 1).as('secondWorkflow')
		cy.createWorkflowNode('@secondWorkflow', 'InProgress', 1, 'arrow-progress')
		cy.createWorkflowNode('@secondWorkflow', 'Positive', 2, 'check')

		cy.createWorkflow(dataSourceId, 'ThirdWorkflow', 2).as('thirdWorkflow')
		cy.createWorkflowNode('@thirdWorkflow', 'InProgress', 1, 'arrow-progress', 2)
		cy.createWorkflowNode('@thirdWorkflow', 'Positive', 2, 'check', 1)
	}
})