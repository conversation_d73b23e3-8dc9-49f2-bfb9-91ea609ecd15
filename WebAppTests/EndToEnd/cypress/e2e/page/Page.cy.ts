describe('Page Configuration', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0
	
	beforeEach(() => {
		guid = uuid()
		
		cy.createDataStore(guid, false).as('dataStore')
		cy.createDataSource('@dataStore', 'TestSource_' + guid)
	})

	afterEach(() => {
		// remove dataStore (this will delete the dataSource as well)
		cy.request({ url: '/Api/DataStores/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
			expect(resp.status).to.equal(200, 'Get temporary data store: TestStore_' + guid)
			const dataStore = resp.body.data.rows.find((row: Record<string, any>) => row.name === 'TestStore_' + guid)
			cy.request({ url: '/Api/DataStores/' + dataStore.id, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
				expect(resp.status).to.equal(200, 'Delete temporary data store: TestStore_' + guid)
			})
		})
	})

	it('check page overview', () => {
		// go to Pages via Home
		cy.visit('/')
		cy.get('body').find('.dashboard__item[href="/Admin/Pages"]').as('navLink').should('be.visible')
		cy.get('@navLink').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Pages'))
		})

		// select Backend
		cy.get('#page-menu lvl-side-nav-item[label="TestStore_' + guid + '"]').click()

		// leave Page overview and enter again -> TestStore has to be selected
		cy.visit('/')
		cy.get('body').find('.dashboard__item[href="/Admin/Pages"]').click()
		cy.get('#page-menu lvl-side-nav-item[selected]').should('have.attr', 'label', 'TestStore_' + guid)
	})

	it('CRUD single data page', () => {
		createConfigItem('SingleData')
		updateConfigItem('SingleData')
		deleteConfigItem()
	})

	it('CRUD multi data page', () => {
		createConfigItem('MultiData')
		updateConfigItem('MultiData')
		deleteConfigItem()
	})

	it('use filter panel and global search', () => {
		const guidAlt = uuid()
		cy.createDataStore(guidAlt, false).as('dataStoreAlt')
		
		cy.createDataSource('@dataStoreAlt', 'Source_B').as('dataSourceAlt')
		cy.createPage('@dataSourceAlt', 'Page_D', 'MultiData')
		cy.createPage('@dataSourceAlt', 'Page_E', 'MultiData')
		cy.createPage('@dataSourceAlt', 'Page_F', 'MultiData')

		cy.createDataSource('@dataStore', 'Source_A').as('dataSource')
		cy.createPage('@dataSource', 'Page_A', 'MultiData')
		cy.createPage('@dataSource', 'Page_B', 'MultiData')
		cy.createPage('@dataSource', 'Page_C', 'MultiData')

		cy.visit('/Admin/DataStores/teststore' + guid + '/Pages')
		cy.get('lvl-filter-panel lvl-filter-panel-section[name="name"]').shadow()
			.find('lvl-input').shadow()
			.find('input').as('nameInput').should('exist')
		cy.get('lvl-list').shadow().as('list')
		cy.get('@list').find('lvl-list-line:not([skeleton])').should('have.length', 3)
		cy.get('@list').find('lvl-list-line:not([skeleton])').eq(0).find('lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_A')
		cy.get('@list').find('lvl-list-line:not([skeleton])').eq(1).find('lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_B')
		cy.get('@list').find('lvl-list-line:not([skeleton])').eq(2).find('lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_C')

		cy.get('@nameInput').focus().clear({ force: true }).type('_A{enter}', { force: true })
		cy.get('@list').find('lvl-list-line:not([skeleton])').should('have.length', 1)
		cy.get('@list').find('lvl-list-line:not([skeleton]) lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_A')

		cy.get('@nameInput').focus().clear({ force: true }).type('Page{enter}', { force: true })
		cy.get('@list').find('lvl-list-line:not([skeleton])').should('have.length', 3)

		cy.get('#global-search').shadow().find('input').as('globalSearchInput')
		cy.get('@globalSearchInput').focus().clear({ force: true }).type('Page_B{enter}', { force: true })
		cy.get('@list').find('lvl-list-line:not([skeleton])').should('have.length', 1)
		cy.get('@list').find('lvl-list-line:not([skeleton]) lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_B')

		// switch backend -> filter should be intact 
		cy.get(`lvl-side-nav-item[label="TestStore_${guidAlt}"]`).click()
		
	
		cy.get('@list').find('lvl-list-line').should('have.length', 0)

		cy.get('@globalSearchInput').focus().clear({ force: true }).type('_B{enter}', { force: true })
		// 3 because of filtering by datasource_B
		cy.get('@list').find('lvl-list-line').should('have.length', 3)

		cy.get('lvl-filter-panel').shadow().find('[data-action="clear"]').click()
		cy.get('@globalSearchInput').focus().clear({ force: true }).type('{enter}', { force: true })
		cy.get('@list').find('lvl-list-line:not([skeleton])').should('have.length', 3)
		cy.get('@list').find('lvl-list-line:not([skeleton])').eq(0).find('lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_D')
		cy.get('@list').find('lvl-list-line:not([skeleton])').eq(1).find('lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_E')
		cy.get('@list').find('lvl-list-line:not([skeleton])').eq(2).find('lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_F')

		cy.removeDataStore(guidAlt)
	})

	function createConfigItem(type: string) {
		// open list view
		cy.visit('/Admin/Pages')

		// change datastore to TestStore
		cy.get('#page-menu lvl-side-nav-item[label="TestStore_' + guid + '"]').as('exampleMenuItem').click()

		// open create panel
		cy.get('[data-action=add]').click()
		cy.get('#page-form').as('form').should('be.visible')

		// url should have changed to /Admin/DataStores/Create
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Pages/Create'))
		})

		// input form data
		cy.get('@form')
			.find('#page-name').shadow()
			.find('input').as('nameInput').focus().clear()
			.type('TestPage')

		cy.get('@form').find('#page-type').invoke('attr', 'value', type)
		cy.get('@form')
			.find('#page-type').shadow()
			.find('input').as('typeInput').should('have.value', type == 'SingleData' ? 'Single data set' : 'Listing')
		cy.get('@form').find('[name=breadcrumbLabel]').invoke('attr', 'value', type)

		cy.get('@form')
			.find('#page-description').shadow()
			.find('textarea').as('descriptionInput')
			.clear().type('Description for test page')

		// submit
		cy.get('.side-panel > [data-action=save]')
			.click()

		// warning should be visible that there are empty required fields
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		cy.get('@form')
			.find('#page-data-source-id').as('dataSourceField').shadow()
			.find('input').as('dataSourceInput').focus().clear().type('TestSource').blur({ force: true })

		// wait for the autocomplete to fetch its result from the server
		cy.get('@dataSourceField').invoke('attr', 'value').should('not.be.empty')

		// submit -> CREATE
		cy.get('.side-panel > [data-action=save]').click()

		// dialog should be closed
		cy.get('.side-panel').shadow().find('#slider').should('not.have.attr', 'open')

		// check reset of the form
		cy.get('[data-action=add]').click()
		cy.get('@nameInput').should('be.empty')
		cy.get('@typeInput').should('be.empty')
		cy.get('@descriptionInput').should('be.empty')
		cy.get('.side-panel > [data-action=cancel]').click()
	}

	function updateConfigItem(type: string) {
		// list pages
		cy.visit('/Admin/DataStores/teststore' + guid + '/Pages')

		// edit view and return with back button
		cy.get('lvl-list').shadow().as('pageList')
		cy.get('@pageList').find('lvl-list-line:not([skeleton]):first-child').as('row')
			.find('[data-name=name]').then((cell) => {
			let storeName = cell[0].innerText
			cy.get('@row').click()
			cy.url().then(url => {
				expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Pages/' + storeName))
			})
		})
		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Pages'))
		})

		// edit example view -> change input and save
		cy.visit('/Admin/DataStores/teststore' + guid + '/Pages/testpage')
		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Pages'))
		})

		cy.wait(500)

		// check that the value is still the new one after reload
		cy.visit('/Admin/DataStores/TestStore' + guid + '/Pages/testpage')

		// tests end here if type is SingleData (we currently don't have specific singledata pages)
		if (type == 'SingleData')
			return
		
		// switch to type-specific menu
		switch (type) {
			case 'MultiData':
				break
		}
		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()

		cy.wait(500)

		cy.visit('/Admin/DataStores/TestStore' + guid + '/Pages/testpage')
		switch (type) {
			case 'MultiData':
				break
		}
		cy.go('back')
	}

	function deleteConfigItem() {
		// edit sample data and delete item
		cy.visit('/Admin/DataStores/teststore' + guid + '/Pages/testpage')

		let pageId: string | null
		cy.get('#page-form input[name=id]').then((item) => {
			pageId = item[0].getAttribute('value')
			cy.request({ url: `/Api/Pages/${pageId}`, failOnStatusCode: false }).then((resp) => {
				expect(resp.status).to.equal(200, 'element exists (pageId is valid)')
			})
		})

		cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click({ force: true })

		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Pages'))

			cy.wait(500)

			// item should not be accessible
			cy.request({ url: `/Api/Pages/${pageId}`, failOnStatusCode: false }).then((resp) => {
				expect(resp.status).to.equal(404, 'Element was deleted. StatusCode should be 204') // 404 = page not found, 204 = element not found (which is the correct answer here)
			})
		})
	}
})