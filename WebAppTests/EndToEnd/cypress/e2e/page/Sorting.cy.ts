
describe('Sorting Configuration', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0
	
	beforeEach(() => {
		guid = uuid()

		// fetch Storage connection
		cy.fixture('../../../testsettings.json').then((jsonData) => {
			const connectionString = (jsonData['Storage']['ConnectionStrings']['PostgreSQL'] as string)

			// create dataStore
			cy.request({
					url: '/Api/DataStores/',
					failOnStatusCode: false,
					method: 'POST',
					body: { name: 'TestStore_' + guid, type: 'Storage', options: { dbType: 'postgres', connectionString: connectionString } },
				},
			).then((resp) => {
				expect(resp.status).to.equal(200, 'Create temporary data store: TestStore')
				expect(resp.body.data.id).not.null
				cy.request({
					url: '/Api/DataSources/',
					failOnStatusCode: false,
					method: 'POST',
					body: { name: 'TestSource_' + guid, dataStoreId: resp.body.data.id },
				}).then((sourceResp) => {
					expect(sourceResp.status).to.equal(200, 'Create temporary data source: TestSource')
					expect(sourceResp.body.data.id).not.null

					// create a view fields 
					createField(sourceResp.body.data.id, 'Filename', 'String', 255)
					createField(sourceResp.body.data.id, 'InSync', 'Boolean')
					createField(sourceResp.body.data.id, 'Comment', 'Text', 1000)
					createField(sourceResp.body.data.id, 'CreateDate', 'Date')

					cy.request({
						url: '/Api/Pages/',
						failOnStatusCode: false,
						method: 'POST',
						body: { dtoType: 'MultiData', name: 'TestPage', dataSourceId: sourceResp.body.data.id, type: 'MultiData' },
					}).then((resp) => {
						expect(resp.status).to.equal(200, 'Create temporary data source: TestPage')
						expect(resp.body.data.id).not.null

						cy.request({
							url: '/Api/PageViews/',
							failOnStatusCode: false,
							method: 'POST',
							body: { dtoType: 'List', name: 'TestListView', pageId: resp.body.data.id, type: 'List' },
						}).then((viewResp) => {
							expect(viewResp.status).to.equal(200, 'Create temporary data source: ListView')
							expect(viewResp.body.data.id).not.null

							cy.request({
								url: `/Api/DataFields?filters=${JSON.stringify([
									{
										filterColumn: 'DataSourceId',
										operator: 'Equals',
										compareValue: sourceResp.body.data.id.toString(),
									},
								])}`,
								failOnStatusCode: false,
								method: 'GET',
							}).then((fieldResp) => {
								expect(fieldResp.status).to.equal(200, 'Create temporary view list column')
								fieldResp.body.data.rows.forEach((row: Record<string, any>) => {
									if(!row.systemField)
										createColumn(viewResp.body.data.id, row.id)
								})
							})
						})
					})
				})
			})
		})
	})
	
	it('CRUD data field sorting', () => {
		cy.visit('/Admin/DataStores/teststore' + guid + '/Pages/TestPage')
		cy.get('lvl-side-nav-item[value="FilterAndSorting"]').click()
		cy.get('lvl-side-nav-item[value="Sorting"]').click()
		cy.get('#list-view-column-list lvl-list').shadow().find('lvl-list-line').should('have.length', 4)
		
		// add new sorting field
		cy.get('#data-field-list lvl-list').shadow().as('fieldList').contains('Filename').click()
		cy.get('#data-field-sorting-list lvl-list').shadow().as('sortingList').find('lvl-list-line').as('fileNameSorting').should('exist')
		
		// remove it via delete button
		cy.get('@fileNameSorting').find('[data-action=delete]').click()
		cy.get('@fileNameSorting').should('not.exist')
		
		// add sorting field again
		cy.get('@fieldList').contains('Filename').click()
		
		// update sort direction
		cy.get('@fileNameSorting').find('[data-action=ascending]').should('have.class', 'fa-solid')
		cy.get('@fileNameSorting').find('[data-action=descending]').should('not.have.class', 'fa-solid')
		cy.get('@fileNameSorting').find('[data-action=descending]').click()
		cy.get('@fileNameSorting').find('[data-action=descending]').should('have.class', 'fa-solid')
		cy.reload()
		cy.get('@fileNameSorting').find('[data-action=descending]').should('have.class', 'fa-solid')
	})

	afterEach(() => {
		// remove dataStore (this will delete the dataSource as well)
		cy.request({ url: '/Api/DataStores/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
			expect(resp.status).to.equal(200, 'Get temporary data store: TestStore')
			const dataStore = resp.body.data.rows.find((row: Record<string, any>) => row.name === 'TestStore_' + guid)
			cy.request({ url: '/Api/DataStores/' + dataStore.id, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
				expect(resp.status).to.equal(200, 'Delete temporary data store: TestStore')
			})
		})
	})

	function createColumn(listViewId: string, fieldId: string) {
		cy.request({
			url: '/Api/ListViewColumns/',
			failOnStatusCode: false,
			method: 'POST',
			body: { listViewId, fieldId },
		}).then((resp) => {
			expect(resp.status).to.equal(200, 'Create temporary list view column')
		})
	}

	function createField(dataSourceId: string, name: string, type: string, length: number = 0, decimalPlaces: number = 0) {
		cy.request({
			url: '/Api/DataFields/',
			failOnStatusCode: false,
			method: 'POST',
			body: { dataSourceId: dataSourceId, name: name, type: type, length: length, decimalPlaces: decimalPlaces },
		}).then((resp) => {
			expect(resp.status).to.equal(200, 'Create temporary data field')
		})
	}
})