describe('Page Rendering ', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0

	before(() => {
		guid = uuid()

		// create DataStore
		cy.createDataStore(guid, true).as('dataStore')
		// create DataSource
		cy.createDataSource('@dataStore', 'TestSource_' + guid, { allowFile: true }).as('dataSource')

		// add some fields
		cy.createField('@dataSource', 'Name', 'String', { length: 255, defaultValue: '=USERNAME()'})
		cy.createField('@dataSource', 'MailSubject', 'String', { length: 255, defaultValue: '=MAILDATA(SUBJECT)' })
		cy.createField('@dataSource', 'MailFrom', 'String', { length: 255, defaultValue: '=MAILDATA(FROM)'})
		cy.createField('@dataSource', 'MailTo', 'String', { length: 255, defaultValue: '=MAILDATA(TO)', multi: true})

		// add Create Page
		cy.createPage('@dataSource', 'TestSource_C', 'Create', {}).as('createPage')

		// fetch default grid view
		cy.getEntityIdFromChainable('@createPage').then((createPageId?: string) => {
			cy.getPageView(createPageId!, 'Form').as('pageView')
		})

		// add a section
		cy.addSection('@pageView', 0, 0, 'TestSection').as('section')

		// add some fields
		cy.forEachField('@dataSource', true, (field, index) => {
			cy.addInputField('@section', field.id, field.type, index + 1, index + 2, 1, 25, null, field.name == 'Position' ? '=MAX(5,8)' : undefined)
		})


		// add edit page
		cy.createPage('@dataSource', 'TestSource_E', 'SingleData').as('detailPage')

		// fetch default grid view
		cy.getEntityIdFromChainable('@detailPage').then((detailPageId?: string) => {
			cy.getPageView(detailPageId!, 'Overview').as('secondPageView')
		})
		

		// add a section
		cy.addSection('@secondPageView', 0, 0, 'TestSection').as('secondSection')

		// add some fields
		cy.forEachField('@dataSource', true, (field, index) => {
			cy.addInputField('@secondSection', field.id, field.type, index + 1, index + 2, 1, 25)
		})

		// enable viewer
		cy.updatePageView('@secondPageView', { type: 'Grid', showViewer: true })


		// add MultiData Page and link create/edit pages
		cy.getEntityIdFromChainable('@createPage').then((createPageId?: string) => {
			cy.getEntityIdFromChainable('@detailPage').then((detailPageId?: string) => {
				cy.createPage('@dataSource', 'TestSource_Q', 'MultiData', { createPageId: createPageId, detailPageId: detailPageId }).as('page')
			})
		})


		// create list view
		cy.createPageView('@page', 'DefaultListView', { type: 'List', display: true }).as('secondPageView')

		// add some columns
		cy.forEachField('@dataSource', true, (field) => {
			cy.createColumn('@secondPageView', field.id, field.name == 'Name' ? 1 : 2)

			// order by position asc
			if (field.name == 'Position')
				cy.createFieldSorting('@page', field.id)
		})
	})

	after(() => {
		cy.removeDataStore(guid)
	})

	it('element with file: delete and replace file', () => {
		//create element and open detail view
		storeEntryAndOpen()

		//click update
		cy.get('lvl-button[data-action="file-options"]').as('fileOptions').should('be.visible').click()
		cy.get('lvl-dropdown[name="file-options-dropdown"]').as('dropdown').find('[data-action="update-file"]').should('exist').click({ force: true })
		//abort opened dialog
		cy.get('#file-action-dialog').as('dialog').find('lvl-form').should('be.visible')
		cy.get('@dialog').find('lvl-button[data-action="cancel"]').click({ force: true })
		cy.get('@dialog').find('lvl-form').should('not.be.visible')
		//click delete
		cy.get('@fileOptions').click()
		cy.get('@dropdown').find('[data-action="cut-file"]').should('exist').click({ force: true })
		//confirm opened dialog
		cy.get('@dialog').find('lvl-form').should('be.visible')
		cy.get('@dialog').find('lvl-button[data-action="start-cut"]').click({ force: true })
		cy.get('@dialog').find('lvl-form').should('not.be.visible')
		//check empty viewer
		cy.get('#detail-view-file-preview').as('fileView').find('.file-dropzone.empty').should('be.visible')
		cy.get('@fileView').find('.file-upload-start.dz-clickable').should('be.visible')
		cy.get('@fileView').find('.file-viewer').should('not.be.visible')

		// refresh and check if file deletion was persisted
		cy.reload()
		cy.get('#detail-view-file-preview').as('fileView').find('.file-dropzone.empty').should('be.visible')
		cy.get('@fileView').find('.file-upload-start.dz-clickable').should('be.visible')
		cy.get('@fileView').find('.file-viewer').should('not.be.visible')

		//drag and drop new file
		cy.get('@fileView').should('have.class', 'dropzone-init-done').selectFile('cypress/fixtures/levelbuild.png', { action: 'drag-drop', force: true })
		waitForViewerDetail(cy.get('@fileView').find('.file-viewer'), true)
		cy.get('@fileView').find('.file-viewer').should('be.visible')

		//refresh and check if file upload was persisted
		cy.reload()
		waitForViewerDetail(cy.get('@fileView').find('.file-viewer'), true)
		cy.get('@fileView').find('.file-viewer').should('be.visible')

		//delete again
		cy.get('@fileOptions').click()
		cy.get('@dropdown').find('[data-action="cut-file"]').should('exist').click({ force: true })
		//confirm opened dialog
		cy.get('@dialog').find('lvl-form').should('be.visible')
		cy.get('@dialog').find('lvl-button[data-action="start-cut"]').click({ force: true })
		cy.get('@dialog').find('lvl-form').should('not.be.visible')

		//check if file was deleted
		cy.get('#detail-view-file-preview').as('fileView').find('.file-dropzone.empty').should('be.visible')
		cy.get('@fileView').find('.file-upload-start.dz-clickable').should('be.visible')
		cy.get('@fileView').find('.file-viewer').should('not.be.visible')

		// get hidden file upload input and attach file (clicking the upload button doesn't work)
		cy.get('@fileView').find('input[type=file]').as('fileInput').selectFile('cypress/fixtures/levelbuild.png', { force: true })
		waitForViewerDetail(cy.get('@fileView').find('.file-viewer'), true)
		cy.get('@fileView').find('.file-viewer').should('be.visible')

		//refresh and check if file upload was persisted
		cy.reload()
		waitForViewerDetail(cy.get('@fileView').find('.file-viewer'), true)
		cy.get('@fileView').find('.file-viewer').should('be.visible')

		//update file
		// get hidden file upload input and attach mail file (clicking the upload button doesn't work)
		cy.get('@fileView').find('input[type=file]').as('fileInput').selectFile('cypress/fixtures/testEmail_msg.msg', { force: true })
		cy.get('@fileView').find('.file-viewer').shadow().find('.mail-viewer').as('mailViewer').should('be.visible')

		//refresh and check if file update was persisted
		cy.reload()
		cy.get('@mailViewer').should('be.visible')

		//use mail viewer button to open update dialog
		cy.get('#detail-view-file-preview').trigger('mousemove')
		cy.get('@mailViewer').find('lvl-button[data-dropdown="three-dots-dropdown"]').should('be.visible').click()
		cy.get('@mailViewer').find('lvl-dropdown[name="three-dots-dropdown"]').find('lvl-menu').should('be.visible')
			.find('lvl-menu-item[data-action="viewer-update"]').should('be.visible').click()

		//abort opened dialog
		cy.get('#file-action-dialog').as('dialog').find('lvl-form').should('be.visible')
		cy.get('@dialog').find('lvl-button[data-action="cancel"]').click({ force: true })
		cy.get('@dialog').find('lvl-form').should('not.be.visible')

		//update file
		// get hidden file upload input and attach mail file (clicking the upload button doesn't work)
		cy.get('@fileView').find('input[type=file]').as('fileInput').selectFile('cypress/fixtures/testEmail_eml.eml', { force: true })
		cy.get('@fileView').find('.file-viewer').shadow().find('.mail-viewer').as('mailViewer').should('be.visible')

		//use mail viewer button to open delete dialog
		cy.get('@mailViewer').find('lvl-button[data-dropdown="three-dots-dropdown"]').should('be.visible').click()
		cy.get('@mailViewer').find('lvl-dropdown[name="three-dots-dropdown"]').find('lvl-menu').should('be.visible')
			.find('lvl-menu-item[data-action="viewer-delete"]').should('be.visible').click()
		//confirm opened dialog
		cy.get('@dialog').find('lvl-form').should('be.visible')
		cy.get('@dialog').find('lvl-button[data-action="start-cut"]').click({ force: true })
		cy.get('@dialog').find('lvl-form').should('not.be.visible')

		//check if file was deleted
		cy.get('#detail-view-file-preview').as('fileView').find('.file-dropzone.empty').should('be.visible')
		cy.get('@fileView').find('.file-upload-start.dz-clickable').should('be.visible')
		cy.get('@fileView').find('.file-viewer').should('not.be.visible')
		cy.get('@mailViewer').should('not.be.visible')
	})

	function storeEntryAndOpen() {
		// open multi data page
		cy.visit('/Public/Pages/TestSourceQ')

		// open create page
		cy.get('lvl-query-view-action-bar').shadow().find('lvl-button[data-action=create]').click()

		cy.get('#create-panel').as('createPanel').should('exist').should('be.visible')
		cy.get('@createPanel').find('.filePreview').as('filePreview').should('exist').should('be.visible')

		// drag'n drop file
		cy.get('@filePreview').find('main.dropzone-init-done').attachFile('levelbuild.png', { subjectType: 'drag-n-drop' })

		// wait till viewer is loaded (if we close the slideout too early the viewer will crash)
		waitForViewerDetail(cy.get('@filePreview').find('lvl-viewer.file-viewer.enabled:not([webviewerloading])'), true)

		// enter some values and create entry
		cy.get('@createPanel').find('lvl-form[initDone]').as('form').should('exist')
		cy.get('@form').find('lvl-input[name=Name]').invoke('val', 'Bla')
		cy.get('@createPanel').find('lvl-button[data-action=save]').click({ force: true })

		// open entry
		cy.get('lvl-table').shadow().find(`.table__body lvl-table-row[data-position=0]:not([skeleton])`, { timeout: 10000 }).as('row').should('exist')
		cy.get('@row').click()

		// goto overview
		cy.get('lvl-side-nav[initDone]').should('exist')
		cy.get('lvl-side-nav').find('lvl-side-nav-item[selected]').should('exist')
		cy.get('lvl-side-nav').children().eq(0).click()

		// wait for lvl-form to load
		cy.get('.single_page_content lvl-form').as('overviewForm')

		waitForViewerDetail(cy.get('@overviewForm').find('.file-viewer.enabled'), true)
	}

	function waitForViewerDetail(rootElement: Cypress.Chainable, waitForApryse: boolean) {
		rootElement.should('exist')
		if (waitForApryse)
			rootElement.shadow().find('[data-cy=apryse-webviewer]').should('exist')
	}
})