/**
 * Test suite for User Lanes functionality
 * Tests the creation and configuration of data sources, fields, and pages
 */
describe('User Lanes', () => {

    /**
     * Test case for creating a new data source
     * Creates a data source named 'projects' with basic configuration
     */
    it('should create a new data source', () => {
        // Start from home page and navigate to Data Sources
        cy.visit("/");
        cy.get('.dashboard__item[href="/Admin/DataSources"]')
            .should('be.visible')
            .click();

        // Open create form
        cy.get('lvl-fab[data-action="add"]')
            .should('be.visible')
            .click();

        // Wait for panel initialization
        cy.get('#create-panel[initdone]').should('exist');

        // Fill out the data source form
        cy.get('#data-source-form')
            .should('be.visible')
            .within(() => {
                // Configure name
                cy.get('lvl-input[name="name"]')
                    .should('be.visible').shadow()
                    .find('input').focus().clear()
                    .type('projects');

                // Add description
                cy.get('lvl-textarea[name="comment"]')
                    .should('be.visible').shadow()
                    .find('textarea').focus().clear()
                    .type('This is a project data source');

                // Set storage path
                cy.get('lvl-input[name="storagePath"]')
                    .should('be.visible').shadow()
                    .find('input').focus().clear()
                    .type('projects');
            });

        // Save the data source
        cy.get('lvl-slide-out')
            .find('[data-action="save"]')
            .should('be.visible')
            .click();

        // Verify successful creation
        cy.get('lvl-toaster').should('exist')
        cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
        cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
        cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'Successfully')
    });

    /**
     * Test case for creating data fields
     * Adds required fields for the projects data source
     */
    it('should create a new data fields for projects', () => {
        // Navigate to data source
        cy.visit("/");
        cy.get('.dashboard__item[href="/Admin/DataSources"]')
            .should('be.visible')
            .click();

        // Select the projects data source
        let storeName: string = ''
        cy.get('lvl-list').shadow().find('lvl-list-line:not([skeleton])')
            .find('[data-name="name"]:contains("projects")')
            .first()
            .parents('lvl-list-line')
            .as('row')
            .then((cell) => {
                storeName = cell[0].innerText
                cy.get('@row').first().click()
                cy.url().then(url => {
                    expect(url.endsWith('/Admin/DataStores/' + storeName))
                })
                cy.title().should('contain', storeName)
            })
        
        // Verify navigation
        cy.url().then(url => {
            expect(url.endsWith('/Admin/DataStores/' + storeName))
            cy.title().should('contain', storeName)
        })

        // Navigate to Fields section
        cy.get('lvl-side-nav-item[value="Fields"]').click()

        // Verify correct navigation
        cy.url().then(url => {
            expect(url.endsWith('/Admin/DataStores/' + storeName + '/Fields'))
        })
        cy.title().should('contain', storeName)

        /**
         * Helper function to create a new data field
         * @param fieldName - Name of the field
         * @param length - Length for string fields
         * @param fieldType - Data type of the field
         */
        const createField = (fieldName: string, length: string, fieldType: string) => {
            // Open create form
            cy.get('lvl-fab[data-action="add"]')
                .should('be.visible')
                .click()

            // Set field name
            cy.get('#data-field-name').shadow()
                .find('input')
                .type(fieldName)

            // Select field type
            cy.get('#data-type').shadow().find('lvl-input-button').click();
            cy.get('lvl-autocomplete#data-type').shadow()
                .find('span').find('lvl-popup').find('table').find('tbody').find('tr').find('td')
                .contains(fieldType)
                .click();
            
            // Set length for non-Date fields
            if (fieldType !== 'Date') {
                cy.get('#data-field-length').shadow()
                    .find('input')
                    .type(length)
            }

            // Save the field
            cy.get('lvl-slide-out')
                .find('[data-action="save"]')
                .should('be.visible')
                .first()
                .click();

            // Verify successful creation
            cy.get('lvl-toaster').should('exist')
            cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
            cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
            cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'Successfully')
        }

        // Create required fields
        createField('project_name', '100', 'String')
        createField('start_date', '', 'Date') 
        createField('end_date', '', 'Date')
        createField('partner_name', '100', 'String')
    });

    /**
     * Test case for creating page list and view
     * Sets up the UI components for displaying projects
     */
    it('should create a page list and view for projects', () => {
        // Navigate to Pages section
        cy.visit("/");
        cy.get('.dashboard__item[href="/Admin/Pages"]')
            .should('be.visible')
            .click();

        // Create project list page
        createPage("MultiData", "projects", "projectList") 

        // Select created page
        let storeName: string = ''
        cy.get('lvl-list').shadow().find('lvl-list-line:not([skeleton])')
            .find('[data-name="name"]:contains("projectList")')
            .first()
            .parents('lvl-list-line')
            .as('row')
            .then((cell) => {
                storeName = cell[0].innerText
                cy.get('@row').first().click()
                cy.url().then(url => {
                    expect(url.endsWith('/Admin/Pages/' + storeName))
                })
            })

        // Create page view
        cy.get('[data-action=add]').click()
        cy.get('#page-view-form').as('form').should('be.visible')

        // Configure view
        cy.get('@form')
            .find('lvl-section[heading="Information"]')
            .find('lvl-input[name="name"]').shadow()
            .find('input')
            .type("projectView")

        // Save view
        cy.get('.side-panel > [data-action=save]')
            .click()

        // Verify successful creation
        cy.get('lvl-toaster').should('exist')
        cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')

        // Select created view
        cy.get('lvl-enumeration#page-view-list')
            .find('lvl-list').shadow()
            .find('lvl-list-line')
            .contains('projectView')
            .click()

        // Navigate to View Designer section
        cy.get('lvl-side-nav-item[value="ViewDesigner"]').click()

        // Wait for view designer to load
        cy.wait(500)

        // Select data fields to display in the view
        selectDataField('partner_name')
        selectDataField('project_name')
        selectDataField('start_date')
        selectDataField('end_date')

        // Click preview button to see the view
        cy.get('lvl-button#content-button-preview[data-action="preview"]')
            .should('be.visible')
            .click()
    });


    it.only('should create a input form for projects', () => {
        // Navigate to Pages section
        cy.visit("/");
        cy.get('.dashboard__item[href="/Admin/Pages"]')
            .should('be.visible')
            .click();

        // Create project list page
        createPage("Create", "projects", "projectInputForm") 

        // Select created page
        let storeName: string = ''
        cy.get('lvl-list').shadow().find('lvl-list-line:not([skeleton])')
            .find('[data-name="name"]:contains("projectInputForm")')
            .first()
            .parents('lvl-list-line')
            .as('row')
            .then((cell) => {
                storeName = cell[0].innerText
                cy.get('@row').first().click()
                cy.url().then(url => {
                    expect(url.endsWith('/Admin/Pages/' + storeName))
                })
            })

        // Navigate to Form Designer section
        cy.get('lvl-side-nav-item[value="Designer"]').click() 
    });

    /**
     * Helper function to create a new page
     * @param type - Type of page (Create/MultiData)
     * @param dataSource - Data source to use
     * @param pageName - Name for the page
     */
    function createPage(type: string, dataSource: string, pageName: string) {
        // Open create form
        cy.get('[data-action=add]').click()
        cy.get('#page-form').as('form').should('be.visible')

        // Verify correct navigation
        cy.url().then(url => {
            expect(url.endsWith('/Admin/Pages/Create'))
        })

        // Configure basic information
        cy.get('@form')
            .find('#page-name').shadow()
            .find('input').as('nameInput').focus().clear()
            .type(pageName)

        // Set page type
        cy.get('@form').find('#page-type').invoke('attr', 'value', type)
        cy.get('@form')
            .find('#page-type').shadow()
            .find('input').as('typeInput').should('have.value', type == 'Create' ? 'Input form' : 'Listing')
        cy.get('@form').find('[name=breadcrumbLabel]').invoke('attr', 'value', type)

        // Add description
        cy.get('@form')
            .find('#page-description').shadow()
            .find('textarea').as('descriptionInput')
            .clear().type('Description for test page')

        // Initial save attempt (expect validation error)
        cy.get('.side-panel > [data-action=save]')
            .click()

        // Verify validation message
        cy.get('lvl-toaster').should('exist')
        cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
        cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
        cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

        // Set data source
        cy.get('@form')
            .find('#page-data-source-id').as('dataSourceField').shadow()
            .find('input').as('dataSourceInput').focus().clear().type(dataSource).blur({ force: true })

        // Wait for autocomplete
        cy.get('@dataSourceField').invoke('attr', 'value').should('not.be.empty')

        // Save page
        cy.get('.side-panel > [data-action=save]').click()

        // Verify panel closes
        cy.get('.side-panel').shadow().find('#slider').should('not.have.attr', 'open')

        // Verify form reset
        cy.get('[data-action=add]').click()
        cy.get('@nameInput').should('be.empty')
        cy.get('@typeInput').should('be.empty')
        cy.get('@descriptionInput').should('be.empty')
        cy.get('.side-panel > [data-action=cancel]').click()
    }

    /**
     * Helper function to select a data field from the list by name
     * @param fieldName Name of the field to select
     */
    function selectDataField(fieldName: string) {
        cy.get('lvl-list#data-field-list').shadow()
            .find('lvl-list-line')
            .find('lvl-list-line-item[type="primary"][data-name="name"]')
            .contains(fieldName).click()
    }
})
