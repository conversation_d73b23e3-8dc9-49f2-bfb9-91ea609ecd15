describe('User Configuration', () => {
	it('navigates to user config entry point', () => {
		// go to UserConfig via Home
		cy.visit('/')
		cy.get('body').find('.dashboard__item[href="/Admin/Users"]').first().as('navLink').should('be.visible')
		cy.get('@navLink').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users'))
		})
	})

	// Can not do CRUD tests, since Zidatel-API does not support delete, only deactivate 
	/* it('CRUDs a user config item', () => {
		// remove example if exists
		removeUserByName('Example')

		// remove renamed example if exists
		removeUserByName('Renamed Example')

		createConfigItem()
		cy.wait(100)
		updateConfigItem()
		cy.wait(200)
		deleteConfigItem()
	}) */

	it('try to open user config without authorization/authentication', () => {
		cy.visitWithoutUser('/Admin/Users')
		cy.visitWithoutAdminRights('/Admin/Users')
	})

	it('some additional navigation tests', () => {
		// open list view
		cy.visit('/Admin/Users')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users'))
		})
		cy.title().should('contain', 'Users')

		// open create mask
		cy.get('[data-action=add]').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users/<USER>'))
		})
		cy.title().should('contain', 'New')

		// close create mask via abort button
		cy.get('.side-panel > [data-action=cancel]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users'))
		})
		cy.title().should('contain', 'Users')

		// open create mask again and reload page
		cy.get('[data-action=add]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users/<USER>'))
		})
		cy.title().should('contain', 'New')
		cy.reload()

		cy.get('.side-panel').shadow().find('#slider').should('have.attr', 'open')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users/<USER>'))
		})

		// close create mask again
		cy.get('.side-panel > [data-action=cancel]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users'))
		})
		cy.title().should('contain', 'Users')

		// Can not do CRUD tests, since Zidatel-API does not support delete, only deactivate 
		/* 
		// create example
		createExampleViaRest()
		cy.reload()

		// open entry
		let displayName: string
		cy.get('lvl-list').shadow().find('lvl-list-line:first-child').as('row')
			.find('div[data-name=displayName]').then((cell) => {
			displayName = cell[0].innerText
			cy.get('@row').click()
			cy.url().then(url => {
				expect(url.endsWith('/Admin/Users/' + displayName))
			})
		})
		cy.title().should('contain', 'Example')

		// refresh entry
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users/' + displayName))
			cy.title().should('contain', 'Example')
		})

		// back button should bring us back to the list view
		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users'))
		})
		cy.title().should('contain', 'Users')
		cy.get('lvl-list').should('exist')

		// refresh should keep us at the list view
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users'))
		})
		cy.title().should('contain', 'Users')

		// use breadcrumb navigation
		cy.visit('/Admin/Users/<USER>')
		cy.get('lvl-breadcrumb')
			.shadow()
			.find('#breadcrumbs').as('breadcrumbs')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users/<USER>'))
		})

		cy.get('@breadcrumbs').contains('Users').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Users/<USER>'))
		})
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 2)

		cy.get('@breadcrumbs')
			.find('#home').click()
		cy.title().should('contain', 'Welcome')
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 1)

		// remove example
		deleteExampleViaRest() */
	})

	/* 	function createConfigItem() {
			// open list view
			cy.visit('/Admin/Users')
	
			// if example item already exists, remove it before starting the test run!
			cy.request({ url: '/Admin/Users/<USER>', failOnStatusCode: false }).then((resp) => {
				if (resp.status == 200) {
					cy.visit('/Admin/Users/<USER>')
					cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click({ force: true })
					cy.get('lvl-toaster').should('exist')
					cy.get('lvl-toaster').shadow().find('lvl-button[label=Ok]:not([hidden]):not([skeleton]').should('exist').click({ force: true });
				}
			})
	
			// open create panel
			cy.get('[data-action=add]').click()
			cy.get('#user-form').as('form').should('be.visible')
	
			// url should have changed to /Admin/Users/<USER>
			cy.url().then(url => {
				expect(url.endsWith('/Admin/Users/<USER>'))
			})
	
			// input form data
			cy.wait(100)
	
			// submit
			cy.get('.side-panel > [data-action=save]')
				.click()
	
			// warning should be visible that there are empty required fields
			cy.get('lvl-toaster').should('exist')
			cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
			cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
			cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')
	
			cy.get('@form')
				.find('#user-display-name').shadow()
				.find('input').as('nameInput').focus().clear()
				.type('Example')
	
			// submit -> CREATE
			cy.get('.side-panel > [data-action=save]')
				.click()

			// dialog should be closed
			cy.get('.side-panel').shadow().find('#slider').should('not.have.attr', 'open')
	
			// check reset of the form
			cy.get('[data-action=add]').click()
			cy.get('#user-display-name').shadow().find('input').should('be.empty')
		}
	
		function updateConfigItem() {
			// list view -> click on first user config
			cy.visit('/Admin/Users/')
	
			// edit view and return with back button
			cy.get('lvl-list').shadow().find('lvl-list-line:first-child').as('row')
				.find('div[data-name=displayName]').then((cell) => {
				let displayName = cell[0].innerText
				cy.get('@row').click()
				cy.url().then(url => {
					expect(url.endsWith('/Admin/Users/' + displayName))
				})
			})
			cy.go('back')
			cy.url().then(url => {
				expect(url.endsWith('/Admin/Users'))
			})
	
			cy.wait(50)
	
			// edit example view -> change input and save
			cy.visit('/Admin/Users/<USER>')
			cy.get('#user-display-name').shadow()
				.find('input').focus().clear()
				.type('Renamed Example')
			cy.wait(500)
			cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()
			cy.url().then(url => {
				expect(url.endsWith('/Admin/Users'))
			})
			cy.title().should('contain', 'Users')
	
			// item should be accessible
			cy.request({ url: '/Admin/Users/<USER>', failOnStatusCode: false }).then((resp) => {
				expect(resp.status, 'StatusCode should be 200').to.equal(200)
			})
	
			// change to origin
			cy.visit('/Admin/Users/<USER>')
			cy.get('#user-display-name').shadow()
				.find('input').focus().clear()
				.type('Example')
			cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()
		}
	
		function deleteConfigItem() {
			// edit sample data and delete item
			cy.visit('/Admin/Users/<USER>')
			cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click({ force: true })
			cy.get('lvl-toaster').should('exist')
			cy.get('lvl-toaster').shadow().find('lvl-button[label=Ok]:not([hidden]):not([skeleton]').should('exist').click({ force: true });
			cy.url().then(url => {
				expect(url.endsWith('/Admin/Users'))
			})
	
			// item should not be accessible
			cy.request({ url: '/Api/Users/<USER>', failOnStatusCode: false }).then((resp) => {
				expect(resp.status, 'StatusCode should be 404').to.equal(404)
			})
		}
		
		function removeUserByName(name: string) {
			cy.request({ url: '/Api/Users/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
				expect(resp.status).to.equal(200, 'Get temporary user config: Example')
				const userConfig = resp.body.data.rows.find((row: Record<string, any>) => row.name === name)
				if (userConfig != null) {
					cy.request({ url: '/Api/Users/' + userConfig.displayName, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
						expect(resp.status).to.equal(200, 'Delete temporary user config: Example')
					})
				}
			})
		}
	
		function createExampleViaRest(){
			cy.request({ url: '/Api/Users/', failOnStatusCode: false, method: 'POST', body: { displayName: 'Example' } }).then((resp) => {
				expect(resp.status === 200 || resp.status === 400, 'Create temporary user config: Example').to.be.true
			})
			cy.wait(50)
		}
	
		function deleteExampleViaRest() {
			cy.request({ url: '/Api/Users/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
				expect(resp.status).to.equal(200, 'Get temporary user config: Example')
				const userConfig = resp.body.data.rows.find((row: Record<string, any>) => row.displayName === 'Example')
				cy.request({ url: '/Api/Users/' + userConfig.id, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
					expect(resp.status).to.equal(200, 'Delete temporary user config: Example')
				})
			})
		} */
})