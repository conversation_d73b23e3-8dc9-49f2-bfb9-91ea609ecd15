describe('Userlanes Form Test', () => {
	beforeEach(() => {
		// Visit the page where the form is located
		cy.visit('/') // Replace with the actual path to your form page
		cy.get('body').find('dashboard__item[href="/Admin/Userlanes"]').first().as('navLink').should('be.visible')
		cy.get('@navLink').click()
		cy.get('body').find('lvl-fab[data-action="add"]').first().as('createPanel').should('be.visible')
		cy.get('@createPanel').click()
		cy.get('#userlanes-form').should('be.visible')
		cy.get('#create-panel').should('have.attr','open','open')
	});

	it('should fill out the Userlanes form and verify the values', () => {
		// Fill out the Name field
		cy.get('lvl-input[name="name"]').shadow().find('input').focus().clear().type('Project testing')

		// Select a Start page from the autocomplete dropdown
		cy.get('#pageId').shadow().find('lvl-input-button').click();
		cy.get('lvl-autocomplete#pageId').should('be.visible');
		cy.get('lvl-autocomplete#pageId lvl-option').should('have.length.at.least', 1);
		cy.get('lvl-autocomplete#pageId').shadow()
			.find('span').find('lvl-popup').find('table').find('tbody').find('tr').find('td')
			.contains('Projects') // Use text content to find the option
			.click();
		cy.get('lvl-input#startPoint').should('have.attr','readonly','readonly');
		cy.get('#pageId').shadow().find('lvl-input-button').click();
		cy.get('lvl-autocomplete#pageId').shadow()
			.find('span').find('lvl-popup').find('table').find('tbody').find('tr').find('td')
			.contains('Custom') // Use text content to find the option
			.click();
		cy.get('lvl-input#startPoint').should('not.have.attr','readonly','');
		cy.get('lvl-input#startPoint').should('have.attr','required','required');
		cy.get('#pageId').shadow().find('lvl-input-button').click();
		cy.get('lvl-autocomplete#pageId').shadow()
			.find('span').find('lvl-popup').find('table').find('tbody').find('tr').find('td')
			.contains('Projects') // Use text content to find the option
			.click();
		cy.get('.side-panel > [data-action=save]').click()
	});

	it('should validate required fields', () => {
		// Attempt to submit the form without filling required fields
		cy.get('.side-panel > [data-action=save]').click()
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		//Only enter the project name
		cy.get('lvl-input[name="name"]').shadow().find('input').focus().clear().type('Project test fail inputs')
		// Attempt to submit the form without filling required fields
		cy.get('.side-panel > [data-action=save]').click()
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		// clear the project name and add a start point
		cy.get('lvl-input[name="name"]').shadow().find('input').focus().clear()
		cy.get('lvl-input[name="startPoint"]').shadow().find('input').focus().clear().type('/Admin/Userlanes') 

		// check if we still have the errors being checked
		cy.get('.side-panel > [data-action=save]').click()
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')
	});

	it('should create and delete a userlane', () => {
		// Fill out the Name field
		cy.get('lvl-input[name="name"]').shadow().find('input').focus().clear().type('Userlane to be deleted')

		// Select a Start page from the autocomplete dropdown
		cy.get('#pageId').shadow().find('lvl-input-button').click();
		cy.get('lvl-autocomplete#pageId').should('be.visible');
		cy.get('lvl-autocomplete#pageId lvl-option').should('have.length.at.least', 1);
		cy.get('lvl-autocomplete#pageId').shadow()
			.find('span').find('lvl-popup').find('table').find('tbody').find('tr').find('td')
			.contains('Custom') // Use text content to find the option
			.click();
		cy.get('lvl-input[name="startPoint"]').shadow().find('input').focus().clear().type('/admin/Userlanes')
		cy.get('.side-panel > [data-action=save]').click()
	});
});

describe('Create userlane step',()=>{
	it('Should create a userlane step', () => {
		cy.visit('/Admin/Userlanes')
		cy.get('lvl-enumeration#userlanes-list').find('lvl-list').shadow().find('lvl-list-line').contains('Project testing').click()
		cy.get('lvl-side-nav-item[value="UserlaneSteps"]').click()
		cy.get('lvl-fab[data-action=add]').click()
		cy.get('#create-panel').should('be.visible')
		cy.get('#create-panel').should('have.attr','open','open')
		cy.get('#order').shadow().find('input').clear()
		cy.get('#order').shadow().find('input').type('1')
		cy.get('#delay').shadow().find('input').clear()
		cy.get('#delay').shadow().find('input').type('0')
		cy.get('#targetElement').shadow().find('lvl-input-button').click();
		cy.get('lvl-autocomplete#targetElement').should('be.visible');
		cy.get('lvl-autocomplete#targetElement lvl-option').should('have.length.at.least', 1);
		cy.get('lvl-autocomplete#targetElement').shadow()
					.find('span').find('lvl-popup').find('table').find('tbody').find('tr').find('td')
					.contains('Create') // Use text content to find the option
					.click();
		cy.get('#title').shadow().find('input').clear()
		cy.get('#title').shadow().find('input').type('open create panel')
		cy.get('#description').shadow().find('input').clear()
		cy.get('#description').shadow().find('input').type('Open the form')
		cy.get('.side-panel > [data-action=save]').click()
	})
	
	it('Should test validations of the userlane step',()=>{
		// Open the userlane
		cy.visit('/Admin/Userlanes')
		cy.get('lvl-enumeration#userlanes-list').find('lvl-list').shadow().find('lvl-list-line').contains('Project testing').click()
		cy.get('lvl-side-nav-item[value="UserlaneSteps"]').click()
		cy.get('lvl-fab[data-action=add]').click()
		cy.get('#create-panel').should('be.visible')
		cy.get('#create-panel').should('have.attr','open','open')
		
		// Click save without entering any details and check if we get validation errors
		cy.get('.side-panel > [data-action=save]').click()
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		// Add the title only and try to save, then check for validation errors
		cy.get('#title').shadow().find('input').clear()
		cy.get('#title').shadow().find('input').type('open create panel')
		cy.get('.side-panel > [data-action=save]').click()
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		// Remove title and add only description and save, then check for validation errors
		cy.get('#title').shadow().find('input').clear()
		cy.get('#description').shadow().find('input').clear()
		cy.get('#description').shadow().find('input').type('Open the form')
		cy.get('.side-panel > [data-action=save]').click()
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		// Save an empty step for deleting later on
		cy.get('#title').shadow().find('input').clear()
		cy.get('#title').shadow().find('input').type('Step to be deleted')
		cy.get('#description').shadow().find('input').clear()
		cy.get('#description').shadow().find('input').type('Open the form')
		cy.get('.side-panel > [data-action=save]').click()
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')
	})

	const testData = [
		{ order: 2, targetElement: 'projectName', title: 'Set project name', description: 'Enter the project name' },
		{ order: 3, targetElement: 'projectStartDate', title: 'Set start date', description: 'Enter the project start date' },
		{ order: 4, targetElement: 'projectEndData', title: 'Set end date', description: 'Enter the project end date' },
		{ order: 5, targetElement: 'save', title: 'Save', description: 'Save' },
		{ order: 6, targetElement: 'informationSection', title: 'Open record', description: 'Open saved record' },
		{ order: 7, targetElement: 'maskEditButton', title: 'Open edit mask', description: 'Open edit mask for the project' },
		{ order: 8, targetElement: 'businessPartner', title: 'Set business partner', description: 'Enter the project business partner' },
		{ order: 9, targetElement: 'dialogSaveButton', title: 'Save changes', description: 'Save changes' },
	];

	testData.forEach((data) => {
		it(`should add step with order ${data.order}`, () => {
			cy.visit('/Admin/Userlanes')
			cy.get('lvl-enumeration#userlanes-list').find('lvl-list').shadow().find('lvl-list-line').contains('Project testing').click()
			cy.get('lvl-side-nav-item[value="UserlaneSteps"]').click()
			cy.get('lvl-fab[data-action=add]').click();
			cy.get('#create-panel').should('be.visible');
			cy.get('#create-panel').should('have.attr', 'open', 'open');
			cy.get('#order').shadow().find('input').clear();
			cy.get('#order').shadow().find('input').type(data.order.toString());
			cy.get('#delay').shadow().find('input').clear();
			cy.get('#delay').shadow().find('input').type('0');
			cy.get('#targetElement').shadow().find('lvl-input-button').click();
			cy.get('lvl-autocomplete#targetElement').should('be.visible');
			cy.get('lvl-autocomplete#targetElement lvl-option').should('have.length.at.least', 1);
			cy.get('lvl-autocomplete#targetElement').shadow()
				.find('span').find('lvl-popup').find('table').find('tbody').find('tr').find('td')
				.contains(data.targetElement) // Use text content to find the option
				.click();
			cy.get('#title').shadow().find('input').clear();
			cy.get('#title').shadow().find('input').type(data.title);
			cy.get('#description').shadow().find('input').clear();
			cy.get('#description').shadow().find('input').type(data.description);
			cy.get('.side-panel > [data-action=save]').click();
		});
	});
	
})

describe('Create Userlane step action', ()=> {
	beforeEach(() => {
		cy.visit('/') // Replace with the actual path to your form page
		cy.get('body').find('.find(dashboard__item[href="/Admin/Userlanes"]').first().as('navLink').should('be.visible')
		cy.get('@navLink').click()
		cy.get('lvl-enumeration#userlanes-list').find('lvl-list').shadow().find('lvl-list-line').contains('Project testing').click()
		cy.get('lvl-side-nav-item[value="UserlaneSteps"]').click()
	})
})

// describe('Delete userlane test', () => {
// 	it('should delete the userlane', () => {
// 		cy.visit('/Admin/Userlanes')
// 		cy.get('lvl-enumeration#userlanes-list').find('lvl-list').shadow().find('lvl-list-line').contains('Userlane to be deleted').click()
// 		cy.get('#content-button-delete').click()
// 	})
// });

// describe('Delete userlane test step', () => {
// 	it('should delete the userlane', () => {
// 		cy.visit('/Admin/Userlanes')
// 		cy.get('lvl-enumeration#userlanes-list').find('lvl-list').shadow().find('lvl-list-line').contains('Project testing').click()
// 		cy.get('lvl-side-nav-item[value="UserlaneSteps"]').click()
// 		cy.get('lvl-enumeration#userlane-steps-list').find('lvl-list').shadow().find('lvl-list-line').contains('Step to be deleted').click()
// 		cy.get('#content-button-delete').click()
// 	})
// });
