{"name": "endtoend", "version": "1.0.0", "description": "WebApp End-2-End Tests", "devDependencies": {"cypress": "^14.3.3", "cypress-file-upload": "^5.0.8", "cypress-real-events": "^1.14.0", "typescript": "^5.8.3"}, "scripts": {"test-dev": "cypress open --e2e --browser chrome", "test:chrome": "cypress run --browser chrome --spec \"cypress/e2e/**/*\"", "test:chrome-beta": "cypress run --browser chrome:beta", "test:edge": "cypress run --browser edge", "test:firefox": "cypress run --browser firefox", "test:safari": "cypress run --browser webkit"}, "author": "<PERSON><PERSON>"}