using Levelbuild.Core.WebAppCoreTests.FileInterface;
using Levelbuild.Domain.S3BucketFile;
using Microsoft.Extensions.Configuration;

namespace Levelbuild.Domain.WebAppTests.FileSystemS3Bucket;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class FileSystemS3BucketTests : FileInterfaceTests
{
	public FileSystemS3BucketTests() : base(CreateFileInterface())
	{
	}

	private static S3BucketFileStore CreateFileInterface()
	{
		var config = new S3BucketFileStoreConfig("", "", "", true, "","");
		var c = new ConfigurationBuilder()
			.SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
			.AddJsonFile("testsettings.json")
			.Build()
			.GetSection("S3BucketFile");
		c.Bind(config);

		return new S3BucketFileStore(config);
	}
}