using Levelbuild.Core.GeoDataInterface.Dto;
using Levelbuild.Core.WebAppCoreTests.GeoDataInterface;
using Levelbuild.Domain.GoogleGeoData;

namespace Levelbuild.Domain.WebAppTests.GoogleGeoData;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class GoogleGeoDataProviderTests : GeoDataProviderTests
{
	public GoogleGeoDataProviderTests()
	{
		var config = new GeoDataConfig { ApiKey = "AIzaSyC_NqukPQudbCmeYNU3aKnMwNGKaEU_7YQ" };
		Provider = GoogleGeoDataProvider.GetInstance(config);
	}
	
	[Trait("Category", "Google Specific Tests")]
	[Fact(DisplayName = "Invalid ApiKey leads to UnauthorizedAccessException")]
	public async Task InvalidApiKey()
	{
		var invalidConfig = new GeoDataConfig { ApiKey = "I_AM_INVALID" };
		Provider = GoogleGeoDataProvider.GetInstance(invalidConfig);

		await Assert.ThrowsAsync<UnauthorizedAccessException>(() => Provider.GetLocation("Leipzig"));
	}
}