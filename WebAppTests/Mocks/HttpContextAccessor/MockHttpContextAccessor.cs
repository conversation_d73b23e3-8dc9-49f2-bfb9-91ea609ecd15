using Microsoft.AspNetCore.Http;

namespace Levelbuild.Domain.WebAppTests.Mocks.HttpContextAccessor;

/// <summary>
/// Mock for <see cref="IHttpContextAccessor"/>
/// </summary>
internal sealed class MockHttpContextAccessor : IHttpContextAccessor
{
	public HttpContext? HttpContext { get; set; }
	
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="serviceProvider"></param>
	public MockHttpContextAccessor(IServiceProvider serviceProvider)
	{
		HttpContext = new DefaultHttpContext
		{
			RequestServices = serviceProvider
		};
	}
}