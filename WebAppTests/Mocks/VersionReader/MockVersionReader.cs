using Levelbuild.Frontend.WebApp.Shared.Services;

namespace Levelbuild.Domain.WebAppTests.Mocks.VersionReader;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class MockVersionReader : IVersionReader
{
	public string Path { get; set; } = "validTestVersion";
	
	public Version GetVersion()
	{
		return new Version();
	}
	
	public string GetSemanticVersion()
	{
		return "0.0";
	}

	/// <inheritdoc />
	public long GetTimestamp()
	{
		return 1678279020;
	}
}