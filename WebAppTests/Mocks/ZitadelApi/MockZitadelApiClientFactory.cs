using Levelbuild.Core.ZitadelApiInterface;

namespace Levelbuild.Domain.WebAppTests.Mocks.ZitadelApi;

internal sealed class MockZitadelApiClientFactory : IZitadelApiClientFactory
{
	private IZitadelAuthApiClient _authClient;
	private IZitadelAdminApiClient _adminClient;
	private IZitadelManagementApiClient _managementClient;
	private IZitadelUserServiceApiClient _userService;
	
	public MockZitadelApiClientFactory(MockZitadelDatabase mockZitadelDatabase)
	{
		// Ensure that each test always uses the same client instance!
		_authClient = new MockZitadelAuthApiClient(mockZitadelDatabase);
		_adminClient = new MockZitadelAdminApiClient();
		_managementClient = new MockZitadelManagementApiClient(mockZitadelDatabase);
		_userService = new MockZitadelUserServiceApiClient();
	}
	
	public IZitadelAuthApiClient GetAuthClient(string accessToken)
	{
		return _authClient;
	}

	public IZitadelAdminApiClient GetAdminClient()
	{
		return _adminClient;
	}

	public IZitadelManagementApiClient GetManagementClient()
	{
		return _managementClient;
	}

	public IZitadelUserServiceApiClient GetUserServiceClient()
	{
		return _userService;
	}
}