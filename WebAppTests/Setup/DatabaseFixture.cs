using Levelbuild.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.Setup;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public abstract class DatabaseFixture : IDisposable
{
	protected readonly string DatabaseIdentifier;

	protected readonly IConfiguration StorageConfig;

	protected string? StorageConnectionString = null;

	protected string? StorageContextConnectionString = null;

	protected string? MongoDbContextConnectionString = null;
	
	protected bool Disposed = false;
	
	public IConfiguration Config { get; }

	public IServiceCollection Services { get; }

	public ServiceProvider ServiceProvider { get; protected set; }

	public IDbContextFactory<CoreDatabaseContext> ContextFactory => ServiceProvider.GetRequiredService<IDbContextFactory<CoreDatabaseContext>>();

	public CoreDatabaseContext Context => ContextFactory.CreateDbContext();

	public Dictionary<string, object> StorageOptions { get; protected set; }

	public Dictionary<string, object> StorageContextOptions { get; protected set; }

	#pragma warning disable CS8618, CS9264
	protected DatabaseFixture()
		#pragma warning restore CS8618, CS9264
	{
		Config = new ConfigurationBuilder()
			.SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
			.AddJsonFile("testsettings.json")
			.Build()
			.GetSection("WebApp");

		StorageConfig = Config.GetSection("Storage");
		DatabaseIdentifier = Guid.NewGuid().ToString();

		Services = new ServiceCollection();

		// ReSharper disable once VirtualMemberCallInConstructor
		AddDatabaseContext();
	}

	protected abstract void AddDatabaseContext();

	public abstract void InitStorageDataBase();

	public abstract void BuildServices();

	public abstract void CleanUp();

	protected abstract void HandleDispose();

	public void Dispose()
	{
		HandleDispose();
		GC.SuppressFinalize(this);
	}
}