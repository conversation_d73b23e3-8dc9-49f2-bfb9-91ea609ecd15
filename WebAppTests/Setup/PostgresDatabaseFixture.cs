using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.StorageInterface.Constants;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Entities;
using Levelbuild.Entities.ContextFactories;
using Levelbuild.Frontend.WebApp.Features.DataStoreConnectionHandling.Interceptors;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Levelbuild.Frontend.WebApp.Shared.Interceptors;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using static Levelbuild.Frontend.WebApp.Features.Database.Constants.ExtendedDatabaseProvider;

namespace Levelbuild.Domain.WebAppTests.Setup;

public class PostgresDatabaseFixture : DatabaseFixture
{
	protected override void AddDatabaseContext()
	{
		var connectionString = Config.GetConnectionString("PostgreSQL")!.Replace("[GUID]", DatabaseIdentifier);
		var dataSource = new NpgsqlDataSourceBuilder(connectionString)
			.EnableDynamicJson()
			.Build();
		Services.AddDbContextFactory<CoreDatabaseContext, PostgresCoreDatabaseContextFactory>(options =>
		{
			options.UseNpgsql(dataSource, x => x.MigrationsAssembly(Postgres.Assembly));
			options.ConfigureWarnings(warningsOptions => warningsOptions.Ignore(CoreEventId.ManyServiceProvidersCreatedWarning));
			options.AddInterceptors(PersistentEntityInterceptor.Instance);
			options.AddInterceptors(DataStoreConnectionHelperInterceptor.Instance);
		}, ServiceLifetime.Scoped);
	}

	public override void InitStorageDataBase()
	{
		// Just do this once per Fixture instance
		if (!string.IsNullOrEmpty(StorageConnectionString))
			return;
		
		StorageConnectionString = StorageConfig.GetConnectionString("PostgreSQL")!.Replace("[GUID]", DatabaseIdentifier);

		StorageOptions = new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.DatabaseConnectionString, StorageConfig.GetConnectionString("PostgreSQL")!.Replace("[GUID]", DatabaseIdentifier) },
			{ StorageConfigurationConstants.DatabaseType, "postgres" }
		};

		using var storageContext = Storage.Setup.PostgresDatabaseFixture.GetSinglePostgresStorageDatabaseContextInstance(StorageConnectionString);
		storageContext.Database.Migrate();

		var contextSection = StorageConfig.GetSection("CustomerContext");
		var storagePath = Path.Combine(contextSection.GetValue<string>("storagePath")!, "PostgreSQL");

		StorageContextConnectionString = contextSection.GetConnectionString("PostgreSQL")!.Replace("[GUID]", DatabaseIdentifier);
		MongoDbContextConnectionString = contextSection.GetConnectionString("MongoDbConnectionString")!.Replace("[GUID]", DatabaseIdentifier);
		StorageContextOptions = new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.Description, contextSection.GetValue<string>("description")! },
			{ StorageConfigurationConstants.DatabaseType, contextSection.GetValue<string>("dbType")! },
			{ StorageConfigurationConstants.DatabaseConnectionString, StorageContextConnectionString },
			{ StorageConfigurationConstants.StoragePath, storagePath },
			{ StorageConfigurationConstants.FilestoreContainer, contextSection.GetValue<string>("blobContainer")! },
			{ StorageConfigurationConstants.FilestoreUrl, contextSection.GetValue<string>("blobConnection")! },
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.Language, contextSection.GetValue<string>("language")! },
			{ StorageConfigurationConstants.ElasticConnectionString, contextSection.GetConnectionString("ElasticConnectionString")! },
			{ StorageConfigurationConstants.MongoDbConnectionString, MongoDbContextConnectionString }
		};
	}

	public override void BuildServices()
	{
		ServiceProvider = Services.BuildServiceProvider();

		using var context = Context;
		context.Database.Migrate();
	}

	public override void CleanUp()
	{
		// baseName and location should be irrelevant in this case
		var localizer = ServiceProvider.GetService<IExtendedStringLocalizerFactory>()?.Create("", "");
		if (localizer != null)
			((StringLocalizer)localizer).ClearCache();
		
		using var context = Context;
		var tableNames = context.Model.GetEntityTypes().Select(type => type.GetTableName()).Distinct().ToList();

		#pragma warning disable EF1002
		context.Database.BeginTransaction();
		foreach (var tableName in tableNames)
		{
			context.Database.ExecuteSqlRaw($"TRUNCATE TABLE public.\"{tableName}\" CASCADE");
		}
		context.Database.CommitTransaction();
		#pragma warning restore EF1002

		if (StorageConnectionString != null)
		{
			ClearStorageDb(StorageConnectionString);

			if (StorageContextConnectionString != null)
			{
				using var storageContext =
					Storage.Setup.PostgresDatabaseFixture.GetSinglePostgresStorageDatabaseContextInstance(StorageContextConnectionString);
				var dbName = storageContext.Database.GetDbConnection().Database;
				storageContext.Database.CloseConnection();
				context.Database.ExecuteSqlRaw($"DROP DATABASE if exists \"{ dbName }\" with (FORCE)");
			}
		}
	}

	private static void ClearStorageDb(string connectionString)
	{
		using var storageContext = Storage.Setup.PostgresDatabaseFixture.GetSinglePostgresStorageDatabaseContextInstance(connectionString);

		List<string?> tableNames = storageContext.Model.GetEntityTypes().Select(type => type.GetTableName()).Distinct().ToList();
		List<StorageIndexDefinition> defs = storageContext.StorageIndexDefinition.ToList();

		#pragma warning disable EF1002
		storageContext.Database.BeginTransaction();
		foreach (var tableDef in defs)
		{
			storageContext.Database.ExecuteSqlRaw($"DROP TABLE IF EXISTS public.\"{tableDef.RevisionTable}\" CASCADE");
			storageContext.Database.ExecuteSqlRaw($"DROP TABLE IF EXISTS public.\"{tableDef.Name}\" CASCADE");
		}

		foreach (var tableName in tableNames)
		{
			storageContext.Database.ExecuteSqlRaw($"TRUNCATE TABLE public.\"{tableName}\" CASCADE");
		}

		storageContext.Database.CommitTransaction();
		#pragma warning restore EF1002
	}

	protected override void HandleDispose()
	{
		using (var context = Context)
		{
			if (StorageConnectionString != null)
			{
				using var storageContext = Storage.Setup.PostgresDatabaseFixture.GetSinglePostgresStorageDatabaseContextInstance(StorageConnectionString);
				//storageContext.Database.EnsureDeleted();
				storageContext.Database.CloseConnection();
				#pragma warning disable EF1002
				context.Database.ExecuteSqlRaw($"DROP DATABASE IF EXISTS \"{storageContext.Database.GetDbConnection().Database}\" WITH (FORCE);");
				#pragma warning restore EF1002

				if (StorageContextConnectionString != null)
				{
					using var storageContextContext =
						Storage.Setup.PostgresDatabaseFixture.GetSinglePostgresStorageDatabaseContextInstance(StorageContextConnectionString);
					//storageContextContext.Database.EnsureDeleted();
					storageContextContext.Database.CloseConnection();
					#pragma warning disable EF1002
					context.Database.ExecuteSqlRaw($"DROP DATABASE IF EXISTS \"{storageContextContext.Database.GetDbConnection().Database}\" WITH (FORCE);");
					#pragma warning restore EF1002
				}
			}
			using var connection = new NpgsqlConnection(Config.GetConnectionString("PostgreSQLMainSchema"));
			connection.Open();
			using var deleteCmd = new NpgsqlCommand($"DROP DATABASE IF EXISTS \"{context.Database.GetDbConnection().Database}\" WITH (FORCE);", connection);
			deleteCmd.ExecuteNonQuery();
			connection.Close();
			context.Database.CloseConnection();
		}

		ServiceProvider.Dispose();
	}
}