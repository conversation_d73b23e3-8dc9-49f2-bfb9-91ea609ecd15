using System.Text.RegularExpressions;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.StorageInterface;
using Levelbuild.Core.StorageInterface.Constants;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Domain.Storage;
using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.StorageEntities;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.WebAppTests.Storage.Setup;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;

namespace Levelbuild.Domain.WebAppTests.Storage;

[ExcludeFromCodeCoverage]
[Collection("StoragePostgresDatabaseCollection")]
public class ContextTestPostgres : ContextTest, IClassFixture<PostgresDatabaseFixture>
{
	public ContextTestPostgres(PostgresDatabaseFixture fixture) : base(fixture)
	{
	}
}

/*
[ExcludeFromCodeCoverage]
[Collection("StorageSqlServerDatabaseCollection")]
public class ContextTestSqlServer : ContextTest, IClassFixture<SqlServerDatabaseFixture>
{
	public ContextTestSqlServer(SqlServerDatabaseFixture fixture) : base(fixture)
	{
	}
}
*/

public abstract class ContextTest
{
	private DatabaseFixture _fixture;
	private StorageConnection _compareConnection = null!;
	protected DirectoryInfo TestDirectory = null!;
	internal static string DbReplaceRegex = "(?<=Database=).+(?=;|$)";

	public ContextTest(DatabaseFixture fixture)
	{
		_fixture = fixture;
	}


	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Test create/drop database, create/update/delete context")]
	public void ContextTests()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);
		_compareConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);

		var storageContext = new StorageContext("TestIdentifier", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "Test/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestDescription" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString, Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex,
																					  "CoreDatabase_tests_storage_customer_test", RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});

		StorageContextOrm storageContextOrm = new StorageContextOrm(storageContext);
		connection.CreateContext(storageContextOrm.ToDto());

		var firstContextPath = connection.GetContext(storageContext.Identifier).Config[StorageConfigurationConstants.StoragePath];

		storageContext = new StorageContext("TestIdentifier", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "Test/Storage/NewPath" },
			{ StorageConfigurationConstants.Description, "TestNewDescription" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString,
				Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex, "Context_customer_test", RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});

		storageContextOrm = new StorageContextOrm(storageContext);
		connection.UpdateContext(storageContextOrm.ToDto());
		var secondContextPath = connection.GetContext(storageContext.Identifier).Config[StorageConfigurationConstants.StoragePath];


		var contextsBeforeRemove = _compareConnection.GetContexts();
		var contextBeforeRemove = contextsBeforeRemove.Where(it => it.Identifier.Equals(storageContext.Identifier)).FirstOrDefault();
		connection.RemoveContext(storageContext.Identifier);
		var contextsAfterRemove = _compareConnection.GetContexts();
		var contextAfterRemove = contextsAfterRemove.Where(it => it.Identifier.Equals(storageContext.Identifier)).FirstOrDefault();

		Assert.Throws<DataStoreOperationException>(() => connection.UpdateContext(storageContextOrm.ToDto()));
		Assert.True(firstContextPath != secondContextPath);
		Assert.NotNull(contextBeforeRemove);
		Assert.Null(contextAfterRemove);
	}

	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Migrate template database to new context test")]
	public void MigrateContextTest()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);
		_compareConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);

		var def1 = CreateDataSource(connection);
		var def2 = CreateDataSource(connection);
		var def3 = CreateDataSource(connection);

		var storageContext1 = new StorageContext("TestIdentifierInitialMigration1", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestInitial1/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestIdentifier1 for initial migration" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString,
				Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex, "Migrate_context_customer_test_initial1",
							  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		StorageContextOrm storageContextOrm1 = new StorageContextOrm(storageContext1);
		((IStorageConnection)connection).CreateContext(storageContextOrm1.ToDto());

		var storageContext2 = new StorageContext("TestIdentifierInitialMigration2", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestInitial2/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestIdentifier2 for initial migration" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString,
				Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex, "Migrate_context_customer_test_initial2",
							  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		StorageContextOrm storageContextOrm2 = new StorageContextOrm(storageContext2);
		((IStorageConnection)connection).CreateContext(storageContextOrm2.ToDto());

		// check results
		var connection1 = (StorageConnection)_fixture.StorageInstance.GetConnection(storageContext1.Identifier, null);
		var connection2 = (StorageConnection)_fixture.StorageInstance.GetConnection(storageContext2.Identifier, null);
		CheckExistanceAndFields(connection1, storageContext1, def1);
		CheckExistanceAndFields(connection1, storageContext1, def2);
		CheckExistanceAndFields(connection1, storageContext1, def3);

		CheckExistanceAndFields(connection2, storageContext2, def1);
		CheckExistanceAndFields(connection2, storageContext2, def2);
		CheckExistanceAndFields(connection2, storageContext2, def3);
	}


	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Test resync of data sources after reconnect")]
	public void ResyncContextTest()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);
		var def1 = CreateDataSource(connection);

		// working context
		var workingStorageContext = new StorageContext("TestResync1", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestResync1/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestResync1 for resync after reconnect" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString, Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex,
																					  "Migrate_context_customer_test_resync1",
																					  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});

		// first valid connection to initiate
		StorageContextOrm storageContextOrm = new StorageContextOrm(workingStorageContext);
		((IStorageConnection)connection).CreateContext(storageContextOrm.ToDto());
		var firstWorkingConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(workingStorageContext.Identifier, null);
		var initialTable = firstWorkingConnection.GetIndexDefinition(def1.Name);

		// NOT working context
		var notWorkingStorageContext = new StorageContext("TestResync1", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestResync1/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestResync1 for resync after reconnect" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{ StorageConfigurationConstants.DatabaseConnectionString, "not working" },
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		storageContextOrm = new StorageContextOrm(notWorkingStorageContext);
		connection.UpdateContext(storageContextOrm.ToDto());

		// Create data source
		var def = CreateDataSource(connection);

		// Create field
		// ------------------------------------------------------------
		StorageFieldConfig newField = new StorageFieldConfig("NewField")
		{
			Type = DataStoreFieldType.String,
			Length = 50
		};
		connection.CreateField(def.Name, newField);
		// ------------------------------------------------------------

		// Update field
		// ------------------------------------------------------------
		StorageFieldConfig newUpdateField = new StorageFieldConfig("NewField")
		{
			Type = DataStoreFieldType.String,
			Length = 255
		};
		connection.UpdateField(def.Name, newUpdateField);
		// ------------------------------------------------------------

		// Rename field
		// ------------------------------------------------------------
		var newRenamedField = connection.RenameField(def.Name, "NewField", "NewFieldRenamed");
		// ------------------------------------------------------------

		// check current error count after first changes
		var firstErrorCount = connection.WithDbContext(db => { return db.StorageSchemaChangeError.ToList().Count; });

		// second valid connection to check creation of data source and field
		storageContextOrm = new StorageContextOrm(workingStorageContext);
		connection.UpdateContext(storageContextOrm.ToDto());
		var secondWorkingConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(workingStorageContext.Identifier, null);

		var syncedTable = secondWorkingConnection.GetIndexDefinition(def.Name);
		var syncedRenamedField = syncedTable!.Fields.Where(it => it.Name == "NewFieldRenamed").FirstOrDefault();

		// check current error count with working custom connect
		var secondErrorCount = connection.WithDbContext(db => { return db.StorageSchemaChangeError.ToList().Count; });

		// NOT working context again
		storageContextOrm = new StorageContextOrm(notWorkingStorageContext);
		connection.UpdateContext(storageContextOrm.ToDto());
		// Remove field
		// ------------------------------------------------------------
		connection.RemoveField(def.Name, "NewFieldRenamed");
		// ------------------------------------------------------------

		// Remove definition
		// ------------------------------------------------------------
		connection.RemoveDataSource(def.Name);
		// ------------------------------------------------------------

		// check current error count after connection loss and next changes
		Thread.Sleep(1000); // short break to wait for async execution of database schema operations
		var thirdErrorCount = connection.WithDbContext(db => { return db.StorageSchemaChangeError.ToList().Count; });
		storageContextOrm = new StorageContextOrm(workingStorageContext);
		connection.UpdateContext(storageContextOrm.ToDto());
		_fixture.StorageInstance.GetConnection(workingStorageContext.Identifier, null);

		var deletedTable = secondWorkingConnection.GetIndexDefinition(def.Name);

		// check current error count after new working custom connect
		var forthErrorCount = connection.WithDbContext(db => { return db.StorageSchemaChangeError.ToList().Count; });

		Assert.True(firstErrorCount > thirdErrorCount);
		Assert.True(secondErrorCount == 0);
		Assert.True(forthErrorCount == 0);
		Assert.NotNull(initialTable);       // check, initial table was synched on context create
		Assert.NotNull(syncedTable);        // check, new table was synched on second working connection
		Assert.NotNull(newRenamedField);    // check, field was synched (created and renamed) on second working connection
		Assert.NotNull(syncedRenamedField); // check, renamed field was synched
		Assert.Null(deletedTable);          // check, table was deleted on third working connection
	}

	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Check for existing context connction test")]
	public void CheckContextConnectionTest()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);

		var storageContext1 = new StorageContext("TestIdentifierExisting1", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestExisting1/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestIdentifierExisting1 for initial migration" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString,
				Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex, "Migrate_context_customer_test_existing",
							  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		StorageContextOrm storageContextOrm1 = new StorageContextOrm(storageContext1);
		((IStorageConnection)connection).CreateContext(storageContextOrm1.ToDto());

		var storageContext2 = new StorageContext("TestIdentifierExisting2", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestExisting2/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestIdentifierExisting2 for initial migration" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString,
				Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex, "Migrate_context_customer_test_existing",
							  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		StorageContextOrm storageContextOrm2 = new StorageContextOrm(storageContext2);

		Assert.Throws<DataStoreConfigurationException>(() => ((IStorageConnection)connection).CreateContext(storageContextOrm2.ToDto()));
	}

	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Check exception for context against main connction test")]
	public void CheckContextAgainstMainConnectionTest()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);

		var storageContext1 = new StorageContext("TestIdentifierAgainstMain", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestIdentifierAgainstMain/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestIdentifierAgainstMain to check failure on context creation" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{ StorageConfigurationConstants.DatabaseConnectionString, _fixture.MainDb.ConnectionString },
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		StorageContextOrm storageContextOrm1 = new StorageContextOrm(storageContext1);

		Assert.Throws<DataStoreConfigurationException>(() => ((IStorageConnection)connection).CreateContext(storageContextOrm1.ToDto()));
	}

	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Check for creation of new storage instance")]
	public void CheckStorageInstanceCreationTest()
	{
		ILogger logger = new LoggerConfiguration()
			.MinimumLevel.Warning()
			.CreateLogger();

		var dbType = (_fixture is PostgresDatabaseFixture) ? "postgres" : "mssql";
		var dbStringType = (_fixture is PostgresDatabaseFixture) ? "PostgreSQL" : "MSSQL";
		string connectionString = Regex.Replace(DatabaseFixture.Config.GetConnectionString(dbStringType)!, DbReplaceRegex,
												"Migrate_context_main_test_new_storage_instance", RegexOptions.IgnoreCase);
		var localStorageInstance = Domain.Storage.Storage.GetInstance(new Dictionary<string, object>()
		{
			[StorageConfigurationConstants.DatabaseConnectionString] = connectionString,
			[StorageConfigurationConstants.DatabaseType] = dbType
		}, logger);

		var connection = (StorageConnection)localStorageInstance.GetConnection(null);

		var storageContext1 = new StorageContext("TestIdentifierNewStorageInstance", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestIdentifierNewStorageInstance/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestIdentifier for new StorageInstance" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString, Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex,
																					  "Migrate_context_customer_test_new_storage_instance",
																					  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});

		StorageContextOrm storageContextOrm = new StorageContextOrm(storageContext1);
		((IStorageConnection)connection).CreateContext(storageContextOrm.ToDto());
		var contextConnection = (StorageConnection)localStorageInstance.GetConnection(storageContextOrm.Identifier, null);

		Assert.True(connection.GetContexts().Count > 0);
		Assert.NotNull(connection.GetDataSources());
		Assert.Throws<InsufficientRightsException>(() => contextConnection.GetContexts());
		Assert.NotNull(contextConnection.GetDataSources());
	}


	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Create new Context containing lookup sources")]
	public void LookupSourcesInContextTest()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);
		var mainTable = CreateDataSource(connection);
		var nstTable1 = CreateDataSource(connection);
		var nstTable2 = CreateDataSource(connection);
		var nstTable3 = CreateDataSource(connection);

		int fkCountBeforeAddingLookup = GetFkCount(mainTable.Name);

		connection.CreateField(mainTable.Name, new StorageFieldConfig("IdFieldFromNst1")
		{
			LookupSource = nstTable1.Name
		});
		connection.CreateField(mainTable.Name, new StorageFieldConfig("IdFieldFromNst2")
		{
			LookupSource = nstTable2.Name
		});
		connection.CreateField(mainTable.Name, new StorageFieldConfig("IdFieldFromNst3")
		{
			LookupSource = nstTable3.Name
		});

		int fkCountAfterAddingLookup = GetFkCount(mainTable.Name);


		var storageContext = new StorageContext("TestLookupFieldsInContext", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestLookupFieldsInContext/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestLookupFieldsInContext to test creation of lookup fields in new cotext" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString, Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex,
																					  "Migrate_context_customer_TestLookupFieldsInContext",
																					  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		StorageContextOrm storageContextOrm = new StorageContextOrm(storageContext);
		((IStorageConnection)connection).CreateContext(storageContextOrm.ToDto());

		// Create custom field in template definition of custom context
		// ------------------------------------------------------------
		var localConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(storageContext.Identifier, null);
		var localMainTable = localConnection.GetDataSource(mainTable.Name);

		Assert.Equal(0, fkCountBeforeAddingLookup);
		Assert.Equal(3, fkCountAfterAddingLookup);
		Assert.NotNull(localMainTable!.Fields.Where(it => it.Name == "IdFieldFromNst1").FirstOrDefault());
		Assert.NotNull(localMainTable.Fields.Where(it => it.Name == "IdFieldFromNst2").FirstOrDefault());
		Assert.NotNull(localMainTable.Fields.Where(it => it.Name == "IdFieldFromNst3").FirstOrDefault());
	}


	private int GetFkCount(string mainTableName)
	{
		int fkCount;
		using (StorageDatabaseContext db = _fixture.GetDatabaseContextByType(true))
		{
			var def = db.StorageIndexDefinition.Include(it => it.Fields).Where(it => it.Name == mainTableName).FirstOrDefault();
			fkCount = def!.Fields.Where(it => it.LookupSource != null).ToList().Count;
		}

		return fkCount;
	}

	private void CheckExistanceAndFields(StorageConnection localConnection, StorageContext storageContext, StorageDataSource dataSource)
	{
		var mainSource = _compareConnection.GetDataSource(dataSource.Name);
		var localSource = localConnection.GetDataSource(dataSource.Name);

		Assert.Equal(mainSource!.Name, localSource!.Name);
		Assert.Equal(mainSource.StoreRevisions, localSource.StoreRevisions);
		Assert.Equal(mainSource.StoreFileContent, localSource.StoreFileContent);

		foreach (var mainSourceField in mainSource.Fields)
		{
			var localSourceField = localSource.Fields.Where(it =>
																((it.Name == "customer_" + storageContext.Identifier + "_" + mainSourceField.Name)
																 || it.Name == mainSourceField.Name)).First();
			Assert.Equal(mainSourceField.Readonly, localSourceField.Readonly);
			Assert.EndsWith(mainSourceField.Name, localSourceField.Name);
			Assert.Equal(mainSourceField.Length, localSourceField.Length);
			Assert.Equal(mainSourceField.MultiValue, localSourceField.MultiValue);
			Assert.Equal(mainSourceField.Nullable, localSourceField.Nullable);
			Assert.Equal(mainSourceField.Type, localSourceField.Type);
			Assert.Equal(mainSourceField.Unique, localSourceField.Unique);
			Assert.Equal(mainSourceField.DecimalPlaces, localSourceField.DecimalPlaces);
			Assert.Equal(mainSourceField.PrimaryKey, localSourceField.PrimaryKey);

			if (mainSourceField.DefaultValue != null && localSourceField.DefaultValue != null)
				Assert.Equal(mainSourceField.DefaultValue.ToString(), localSourceField.DefaultValue.ToString());
		}
	}


	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Test shorten alias for datasource names")]
	public void DataSourceShortenAliasTest()
	{
		// Test to check against old (not identical result) hash code function
		string dataSourceName1 = "This_Is_A_Really_Long_Database_Table_Name_And_it_is_really_hard_to_make_it_long_enough1";
		string dataSourceName2 = "This_Is_A_Really_Long_Database_Table_Name_And_it_is_really_hard_to_make_it_long_enough2";
		string shortenedDataSourceName1 = FilterParser.ShortenAlias(dataSourceName1.Substring(0, dataSourceName1.Length - 1));
		string shortenedDataSourceName2 = FilterParser.ShortenAlias(dataSourceName2.Substring(0, dataSourceName1.Length - 1));
		Assert.Equal(shortenedDataSourceName1, shortenedDataSourceName2);
		Assert.True(shortenedDataSourceName1.EndsWith("FFAE7BD8"));
		Assert.True(shortenedDataSourceName2.EndsWith("FFAE7BD8"));
	}

	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Test create datasource in template database and add to existing customer database")]
	public void CreateDataSourceContextTest()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);
		_compareConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);

		var storageContext = new StorageContext("TestIdentifierCreateDS", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestCreateDS/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestIdentifier to create datasource" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString, Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex,
																					  "Migrate_context_customer_TestIdentifierCreateDS",
																					  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		StorageContextOrm storageContextOrm = new StorageContextOrm(storageContext);
		((IStorageConnection)connection).CreateContext(storageContextOrm.ToDto());

		var def1 = CreateDataSource(connection);
		var def2 = CreateDataSource(connection);
		var def3 = CreateDataSource(connection);

		// check results
		var localConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(storageContext.Identifier, null);
		CheckExistanceAndFields(localConnection, storageContext, def1);
		CheckExistanceAndFields(localConnection, storageContext, def2);
		CheckExistanceAndFields(localConnection, storageContext, def3);
	}

	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Test create datasource/field in template database with custom overwrite")]
	public void CheckCustomerFieldsContextTest()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);

		var storageContext = new StorageContext("TestIdentifierCreateFields", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestIdentifierCreateFields/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestIdentifierCreateFields to create datasource" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString, Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex,
																					  "Migrate_context_customer_TestIdentifierCreateFields",
																					  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		StorageContextOrm storageContextOrm = new StorageContextOrm(storageContext);
		((IStorageConnection)connection).CreateContext(storageContextOrm.ToDto());
		var def1 = CreateDataSource(connection);


		// Create custom field in template definition of custom context
		// ------------------------------------------------------------
		var localConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(storageContext.Identifier, null);
		StorageFieldConfig customField = new StorageFieldConfig("customer#CustomField")
		{
			Type = DataStoreFieldType.String,
			Length = 50
		};
		var customFieldInTemplatedDefinition = localConnection.CreateField(def1.Name, customField);
		// ------------------------------------------------------------

		// Create template field in template definition
		// ------------------------------------------------------------
		StorageFieldConfig templateField = new StorageFieldConfig("TemplateField")
		{
			Type = DataStoreFieldType.String,
			Length = 50
		};
		var tepmlateFieldInTemplatedDefinition = connection.CreateField(def1.Name, templateField);
		// ------------------------------------------------------------


		// Update template field in template definition
		// ------------------------------------------------------------
		StorageFieldConfig templateUpdateField = new StorageFieldConfig("TemplateField")
		{
			Type = DataStoreFieldType.String,
			Length = 255
		};

		var templateUpdatedField = connection.UpdateField(def1.Name, templateUpdateField);
		// ------------------------------------------------------------


		// Update custom field in template definition of custom context
		// ------------------------------------------------------------
		StorageFieldConfig customUpdateFieldInTemplateDefinition = new StorageFieldConfig("customer#CustomField")
		{
			Type = DataStoreFieldType.String,
			Length = 100
		};
		var customUpdatedFieldInTemplateDefinition = localConnection.UpdateField(def1.Name, customUpdateFieldInTemplateDefinition);
		// ------------------------------------------------------------


		// Rename template field in template definition
		// ------------------------------------------------------------
		var templateRenamedField = connection.RenameField(def1.Name, "TemplateField", "TemplateFieldRenamed");
		// ------------------------------------------------------------


		// Rename custom field in template definition of custom context
		// ------------------------------------------------------------
		var customRenamedField = localConnection.RenameField(def1.Name, "customer#CustomField", "customer#CustomFieldRenamed");
		// ------------------------------------------------------------


		// Remove template field in template definition
		// ------------------------------------------------------------
		var templateRenamedFieldRemoved = connection.RemoveField(def1.Name, "TemplateFieldRenamed");
		// ------------------------------------------------------------


		// Remove custom field in template definition of custom context
		// ------------------------------------------------------------
		var customRenamedFieldRemoved = localConnection.RemoveField(def1.Name, "customer#CustomFieldRenamed");
		// ------------------------------------------------------------

		// check results
		Assert.Equal(customFieldInTemplatedDefinition.Name, customField.Name);
		Assert.Equal(tepmlateFieldInTemplatedDefinition.Name, templateField.Name);
		Assert.Equal(templateUpdatedField.Length, templateUpdateField.Length);
		Assert.Equal(customUpdatedFieldInTemplateDefinition.Length, customUpdateFieldInTemplateDefinition.Length);
		Assert.Equal("TemplateFieldRenamed", templateRenamedField.Name);
		Assert.Equal("customer#CustomFieldRenamed", customRenamedField.Name);
		Assert.True(templateRenamedFieldRemoved);
		Assert.True(customRenamedFieldRemoved);
	}

	[Trait("Category", "Context Tests")]
	[Fact(DisplayName = "Test create customer datasource and fields in database")]
	public void CheckCustomerDatasourceAndFieldsContextTest()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);

		var storageContext = new StorageContext("TestIdentifierCreateDS1", new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.StoragePath, "TestIdentifierCreateDataSource/Storage/Path" },
			{ StorageConfigurationConstants.Description, "TestIdentifierCreateDataSource to create datasource" },
			{ StorageConfigurationConstants.DatabaseType, "postgres" },
			{
				StorageConfigurationConstants.DatabaseConnectionString, Regex.Replace(_fixture.CustomerContext.DatabaseConnectionString, DbReplaceRegex,
																					  "Migrate_context_customer_TestIdentifierCreateDataSource",
																					  RegexOptions.IgnoreCase)
			},
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
			{ StorageConfigurationConstants.ElasticConnectionString, _fixture.CustomerContext.ElasticConnectionString },
			{ StorageConfigurationConstants.MongoDbConnectionString, _fixture.CustomerContext.MongoDbConnectionString! },
			{ StorageConfigurationConstants.Language, _fixture.CustomerContext.Language }
		});
		StorageContextOrm storageContextOrm = new StorageContextOrm(storageContext);
		((IStorageConnection)connection).CreateContext(storageContextOrm.ToDto());

		// create datasource in customer context
		var localConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(storageContext.Identifier, null);
		var customDefinition = CreateDataSource(localConnection, true);


		// Create custom field in customer definition of custom context
		// ------------------------------------------------------------
		StorageFieldConfig customField = new StorageFieldConfig("CustomField")
		{
			Type = DataStoreFieldType.String,
			Length = 50
		};
		var customFieldInCustomDefinition = localConnection.CreateField(customDefinition.Name, customField);
		// ------------------------------------------------------------


		// Update custom field in customer definition of custom context
		// ------------------------------------------------------------
		StorageFieldConfig customUpdateField = new StorageFieldConfig("CustomField")
		{
			Type = DataStoreFieldType.String,
			Length = 255
		};
		var customUpdatedField = localConnection.UpdateField(customDefinition.Name, customUpdateField);
		// ------------------------------------------------------------


		// Rename custom field in customer definition of custom context
		// ------------------------------------------------------------
		var customRenamedField = localConnection.RenameField(customDefinition.Name, "CustomField", "CustomFieldRenamed");
		// ------------------------------------------------------------


		// Remove custom field in customer definition of custom context
		// ------------------------------------------------------------
		var customRemovedField = localConnection.RemoveField(customDefinition.Name, "CustomFieldRenamed");
		// ------------------------------------------------------------

		// Remove customer definition of custom context
		// ------------------------------------------------------------
		var customRemovedDataSource = localConnection.RemoveDataSource(customDefinition.Name);
		// ------------------------------------------------------------


		// check results
		Assert.Equal(customField.Name, customFieldInCustomDefinition.Name);
		Assert.Equal(customUpdateField.Length, customUpdatedField.Length);
		Assert.Equal("CustomFieldRenamed", customRenamedField.Name);
		Assert.True(customRemovedField);
		Assert.True(customRemovedDataSource);
	}

	private StorageDataSource CreateDataSource(StorageConnection connection, bool customerSpecific = false)
	{
		var randomTableName = (customerSpecific) ? "customer#" + DatabaseFixture.RandomString(20) : DatabaseFixture.RandomString(20);

		// test create table with revision and changed field definition
		var config = new StorageDataSourceConfig(randomTableName)
		{
			StoreRevisions = true,
			CustomerSpecific = customerSpecific
		};
		StorageDataSource definition = connection.CreateDataSource(config);

		Assert.Equal(randomTableName, definition.Name);
		Assert.Equal(config.StoreRevisions, definition.StoreRevisions);

		var sfd = new StorageField("testcol_Standard")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.String,
			Unique = false,
			Length = 50,
			Encrypted = false
		};
		connection.CreateField(definition.Name, sfd);

		var sfd1 = new StorageField("testcol_String")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.String,
			Unique = false,
			Length = 50,
			Encrypted = false,
			MultiValue = true
		};
		connection.CreateField(definition.Name, sfd1);

		var sfd2 = new StorageField("testcol_Integer")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.Integer,
			Unique = false,
			Encrypted = false,
			MultiValue = true
		};
		connection.CreateField(definition.Name, sfd2);

		var sfd3 = new StorageField("testcol_Long")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.Long,
			Unique = false,
			Encrypted = false,
			MultiValue = true
		};
		connection.CreateField(definition.Name, sfd3);

		var sfd4 = new StorageField("testcol_Double")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.Double,
			Unique = false,
			Encrypted = false,
			MultiValue = true
		};
		connection.CreateField(definition.Name, sfd4);


		var sfd5 = new StorageField("testcol_Boolean")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.Boolean,
			Unique = false,
			Encrypted = false,
			MultiValue = true
		};
		connection.CreateField(definition.Name, sfd5);


		var sfd6 = new StorageField("testcol_DateTime")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.DateTime,
			Unique = false,
			Encrypted = false,
			MultiValue = true
		};
		connection.CreateField(definition.Name, sfd6);
		return definition;
	}
}