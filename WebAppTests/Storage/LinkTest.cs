using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Domain.Storage;
using Levelbuild.Domain.WebAppTests.Storage.Setup;

namespace Levelbuild.Domain.WebAppTests.Storage;

[ExcludeFromCodeCoverage]
[Collection("StoragePostgresDatabaseCollection")]
public class LinkTestPostgres : LinkTest, IClassFixture<PostgresDatabaseFixture>
{
	public LinkTestPostgres(PostgresDatabaseFixture fixture) : base(fixture)
	{
		TestDirectory = Directory.CreateDirectory(Path.Combine(fixture.TempPath, "PostgresSQL", "LinkTest_" + DatabaseFixture.Random.NextInt64()));
	}
}

/*[ExcludeFromCodeCoverage]
[Collection("StorageSqlServerDatabaseCollection")]
public class LinkTestSqlServer : LinkTest, IClassFixture<SqlServerDatabaseFixture>
{
	public LinkTestSqlServer(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		TestDirectory = System.IO.Directory.CreateDirectory(Path.Combine(fixture.TempPath, "MSSQL", "LinkTest_" + DatabaseFixture.Random.NextInt64()));
	}
}*/

public abstract class LinkTest
{
	private DatabaseFixture _fixture;
	private StorageConnection _mainConnection = null!;
	private StorageConnection _connection = null!;
	private StorageConnection _compareConnection = null!;
	private string _randomTableName;
	protected DirectoryInfo TestDirectory = null!;
	private string _testSettingsFile = @"testsettings.json";

	public LinkTest(DatabaseFixture fixture)
	{
		_fixture = fixture;
		_randomTableName = DatabaseFixture.RandomString(20);
	}

	[Trait("Category", "Link Tests")]
	[Fact(DisplayName = "Create link to new file and update file")]
	public void CreateLinkToFileTest()
	{
		DirectoryInfo localTestDirectory = Directory.CreateDirectory(Path.Combine(TestDirectory.ToString(), "CreateLinkToFileTest"));

		// create data source
		CreateDataSource(true);

		// upload test file and create document with file to link to
		var createdFileInfo = new FileInfo(_testSettingsFile);
		string tempFileId = UploadFile(_connection, createdFileInfo.FullName);
		var createInfo = CreateDataSet(tempFileId, 0);

		var createdElement = _compareConnection.GetElement(_randomTableName, createInfo.ElementId);
		DataStoreFileStream dataStoreFileStreamCreated = _compareConnection.GetFile(_randomTableName, createdElement.FileInfo!.Id);
		Stream createdElementOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), createdElement.FileInfo.Name));
		dataStoreFileStreamCreated.CopyTo(createdElementOutStream);
		var createdElementOutStreamLength = createdElementOutStream.Length;
		createdElementOutStream.Close();
		dataStoreFileStreamCreated.Close();

		// create element without document, update and set link
		var createLinkInfo = CreateDataSet(null, 1);
		var updateLinkInfo = UpdateDataSet(createLinkInfo.ElementId, null, 2);

		// should have no file
		var createdLinkElementNoFile = _compareConnection.GetElement(_randomTableName, updateLinkInfo.ElementId);

		// update link element
		DataStoreReference dataStoreFileLink = new DataStoreReference(_randomTableName, createdElement.Id);
		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link")
		{
			Comment = 3 + ": updated file with link"
		};
		_connection.UpdateFileLink(_randomTableName, createLinkInfo.ElementId, dataStoreFileLink, origin);

		// should now have a file
		var createdLinkElementWithFile = _compareConnection.GetElement(_randomTableName, updateLinkInfo.ElementId);
		DataStoreFileStream dataStoreFileStreamLink = _compareConnection.GetFile(_randomTableName, createdLinkElementWithFile.FileInfo!.Id);
		Stream linkElementOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), createdLinkElementWithFile.FileInfo.Name));
		dataStoreFileStreamLink.CopyTo(linkElementOutStream);
		var linkElementOutStreamLength = linkElementOutStream.Length;
		linkElementOutStream.Close();
		dataStoreFileStreamLink.Close();

		// change file in original data set
		var updatedFileInfo = new FileInfo(@"Storage/Test.pdf");
		tempFileId = UploadFile(_connection, updatedFileInfo.FullName);
		var updateInfo = UpdateDataSet(createdElement.Id, tempFileId, 4);
		var updatedElement = _compareConnection.GetElement(_randomTableName, updateInfo.ElementId);
		DataStoreFileStream dataStoreFileStreamUpdated = _compareConnection.GetFile(_randomTableName, updatedElement.FileInfo!.Id);
		Stream updatedElementOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), updatedElement.FileInfo.Name));
		dataStoreFileStreamUpdated.CopyTo(updatedElementOutStream);
		var updatedElementOutStreamLength = updatedElementOutStream.Length;
		updatedElementOutStream.Close();
		dataStoreFileStreamUpdated.Close();

		DataStoreFileStream dataStoreFileStreamRevisionLink = _compareConnection.GetFile(_randomTableName, createdLinkElementWithFile.FileInfo.Id);
		Stream revisionLinkElementOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), createdLinkElementWithFile.FileInfo.Name));
		dataStoreFileStreamRevisionLink.CopyTo(revisionLinkElementOutStream);
		var revisionLinkElementOutStreamLength = revisionLinkElementOutStream.Length;
		revisionLinkElementOutStream.Close();
		dataStoreFileStreamRevisionLink.Close();

		var newCreatedLinkElementWithFile = _compareConnection.GetElement(_randomTableName, updateLinkInfo.ElementId);
		DataStoreFileStream dataStoreFileStreamUpdatedLink = _compareConnection.GetFile(_randomTableName, newCreatedLinkElementWithFile.FileInfo!.Id);
		Stream updatedLinkElementOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), newCreatedLinkElementWithFile.FileInfo.Name));
		dataStoreFileStreamUpdatedLink.CopyTo(updatedLinkElementOutStream);
		var updatedLinkElementOutStreamLength = updatedLinkElementOutStream.Length;
		updatedLinkElementOutStream.Close();
		dataStoreFileStreamUpdatedLink.Close();

		localTestDirectory.Delete(true);
		Assert.False(createdLinkElementNoFile.HasFile());
		Assert.True(createdLinkElementWithFile.HasFile());
		Assert.True(createdElementOutStreamLength == linkElementOutStreamLength);         // check original file to link file
		Assert.True(createdElementOutStreamLength == revisionLinkElementOutStreamLength); // check original file to revision link file after original update
		Assert.True(updatedElementOutStreamLength == updatedLinkElementOutStreamLength);  // check updated original file to current link file
	}

	[Trait("Category", "Link Tests")]
	[Fact(DisplayName = "Remove single link to file entry")]
	public void RemoveSingleLinkTest()
	{
		// create data source
		CreateDataSource(true);

		// upload test file and create document with file to link to
		var createdFileInfo = new FileInfo(_testSettingsFile);
		string tempFileId = UploadFile(_connection, createdFileInfo.FullName);
		var createInfo = CreateDataSet(tempFileId, 0);

		var createdElement = _compareConnection.GetElement(_randomTableName, createInfo.ElementId);

		// create element without document, update and set link
		var createLinkInfo = CreateDataSet(null, 1);
		DataStoreReference dataStoreFileLink = new DataStoreReference(_randomTableName, createdElement.Id);
		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link")
		{
			Comment = 2 + ": created file with link"
		};
		_connection.UpdateFileLink(_randomTableName, createLinkInfo.ElementId, dataStoreFileLink, origin);

		var createdLinkElementWithFile = _compareConnection.GetElement(_randomTableName, createLinkInfo.ElementId);
		_connection.RemoveFileLink(_randomTableName, createLinkInfo.ElementId, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link"));
		var createdLinkElementWithOutFile = _compareConnection.GetElement(_randomTableName, createLinkInfo.ElementId);

		Assert.True(createdLinkElementWithFile.HasFile());
		Assert.False(createdLinkElementWithOutFile.HasFile());
	}


	[Trait("Category", "Link Tests")]
	[Fact(DisplayName = "Remove single link to file entry - ASYNC")]
	public async Task RemoveSingleLinkTestAsync()
	{
		// create data source
		CreateDataSource(true);

		// upload test file and create document with file to link to
		var createdFileInfo = new FileInfo(_testSettingsFile);
		string tempFileId = UploadFile(_connection, createdFileInfo.FullName);
		var createInfo = CreateDataSet(tempFileId, 0);

		var createdElement = _compareConnection.GetElement(_randomTableName, createInfo.ElementId);

		// create element without document, update and set link
		var createLinkInfo = CreateDataSet(null, 1);
		DataStoreReference dataStoreFileLink = new DataStoreReference(_randomTableName, createdElement.Id);
		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link")
		{
			Comment = 2 + ": created file with link"
		};

		var updateFileLinkResult = await _connection.UpdateFileLinkAsync(_randomTableName, createLinkInfo.ElementId, dataStoreFileLink, origin);
		var createdLinkElementWithFile = _compareConnection.GetElement(_randomTableName, createLinkInfo.ElementId);
		var removeFileLinkResult = await _connection.RemoveFileLinkAsync(_randomTableName, createLinkInfo.ElementId,
																		 new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link"));
		var createdLinkElementWithOutFile = _compareConnection.GetElement(_randomTableName, createLinkInfo.ElementId);

		Assert.NotNull(updateFileLinkResult);
		Assert.Equal(DataStoreOperationType.Update, updateFileLinkResult.OperationType);

		Assert.NotNull(removeFileLinkResult);
		Assert.Equal(DataStoreOperationType.Delete, removeFileLinkResult.OperationType);

		Assert.True(createdLinkElementWithFile.HasFile());
		Assert.False(createdLinkElementWithOutFile.HasFile());
	}

	[Trait("Category", "Link Tests")]
	[Fact(DisplayName = "Remove single link to multiple referenced file entry")]
	public void RemoveSingleLinkOfMultiEntryTest()
	{
		// create data source
		CreateDataSource(true);

		// upload test file and create document with file to link to
		var createdFileInfo = new FileInfo(_testSettingsFile);
		string tempFileId = UploadFile(_connection, createdFileInfo.FullName);
		var createInfo = CreateDataSet(tempFileId, 0);
		var createdElement = _compareConnection.GetElement(_randomTableName, createInfo.ElementId);

		// create element without document, update and set link
		var createLinkInfo = CreateDataSet(null, 1);
		DataStoreReference dataStoreFileLink = new DataStoreReference(_randomTableName, createdElement.Id);
		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link")
		{
			Comment = 2 + ": created file with link"
		};
		_connection.UpdateFileLink(_randomTableName, createLinkInfo.ElementId, dataStoreFileLink, origin);
		var createdLinkElementWithFile = _compareConnection.GetElement(_randomTableName, createLinkInfo.ElementId);

		// create element without document, update and set link
		var createLinkInfo2 = CreateDataSet(null, 1);

		// update link element
		DataStoreReference dataStoreFileLink2 = new DataStoreReference(_randomTableName, createdElement.Id);
		origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link")
		{
			Comment = 3 + ": created second file with link"
		};
		_connection.UpdateFileLink(_randomTableName, createLinkInfo2.ElementId, dataStoreFileLink2, origin);

		// remove one link
		_connection.RemoveFileLink(_randomTableName, createLinkInfo.ElementId, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link"));
		var createdLinkElementWithOutFile = _compareConnection.GetElement(_randomTableName, createLinkInfo.ElementId);

		// but keep second link
		var createdLinkElement2WithFile = _compareConnection.GetElement(_randomTableName, createLinkInfo2.ElementId);

		Assert.True(createdLinkElementWithFile.HasFile());
		Assert.False(createdLinkElementWithOutFile.HasFile());
		Assert.True(createdLinkElement2WithFile.HasFile());
	}

	[Trait("Category", "Link Tests")]
	[Fact(DisplayName = "Remove file of linked document (delete revision)")]
	public void DeleteLinkedRevisedDocumentTest()
	{
		DirectoryInfo localTestDirectory = Directory.CreateDirectory(Path.Combine(TestDirectory.ToString(), "DeleteLinkedRevisedDocumentTest"));

		// create data source
		CreateDataSource(true);

		// upload test file and create document with file to link to
		var createdFileInfo = new FileInfo(_testSettingsFile);
		string tempFileId = UploadFile(_connection, createdFileInfo.FullName);
		var createInfo = CreateDataSet(tempFileId, 0);
		Thread.Sleep(10);

		var updatedFileInfo = new FileInfo(@"Storage/Test.pdf");
		tempFileId = UploadFile(_connection, updatedFileInfo.FullName);
		UpdateDataSet(createInfo.ElementId, tempFileId, 2);
		Thread.Sleep(10);

		var createdElement = _compareConnection.GetElement(_randomTableName, createInfo.ElementId);
		DataStoreFileStream dataStoreFileStreamCreated = _compareConnection.GetFile(_randomTableName, createdElement.FileInfo!.Id);
		Stream createdElementOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), createdElement.FileInfo.Name));
		dataStoreFileStreamCreated.CopyTo(createdElementOutStream);
		var createdElementOutStreamLength = createdElementOutStream.Length;
		createdElementOutStream.Close();
		dataStoreFileStreamCreated.Close();

		// create element without document, update and set link
		var createLinkInfo = CreateDataSet(null, 1);
		Thread.Sleep(10);

		// update link element
		DataStoreReference dataStoreFileLink = new DataStoreReference(_randomTableName, createdElement.Id);
		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link")
		{
			Comment = 3 + ": updated file with link"
		};
		_connection.UpdateFileLink(_randomTableName, createLinkInfo.ElementId, dataStoreFileLink, origin);

		// delete original document
		_connection.DeleteElement(_randomTableName, createdElement.Id, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link"));

		// original document must be copied to link destination
		DataStoreFileStream linkedDataStoreFileStream = _compareConnection.GetFile(_randomTableName, createdElement.FileInfo.Id);
		Stream linkedElementOutStream = File.OpenWrite(Path.Combine(TestDirectory.ToString(), createdElement.FileInfo.Name));
		linkedDataStoreFileStream.CopyTo(linkedElementOutStream);
		var linkedElementOutStreamLength = linkedElementOutStream.Length;
		linkedElementOutStream.Close();
		linkedDataStoreFileStream.Close();

		localTestDirectory.Delete(true);
		Assert.Equal(createdElementOutStreamLength, linkedElementOutStreamLength);
	}

	[Trait("Category", "Link Tests")]
	[Fact(DisplayName = "Remove file of linked document (full delete)")]
	public void DeleteLinkedDocumentTest()
	{
		DirectoryInfo localTestDirectory = Directory.CreateDirectory(Path.Combine(TestDirectory.ToString(), "DeleteLinkedRevisedDocumentTest"));

		// create data source
		CreateDataSource(false);

		// upload test file and create document with file to link to
		var createdFileInfo = new FileInfo(_testSettingsFile);
		string tempFileId = UploadFile(_connection, createdFileInfo.FullName);
		var createInfo = CreateDataSet(tempFileId, 0);

		var createdElement = _compareConnection.GetElement(_randomTableName, createInfo.ElementId);
		DataStoreFileStream dataStoreFileStreamCreated = _compareConnection.GetFile(_randomTableName, createdElement.FileInfo!.Id);
		Stream createdElementOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), createdElement.FileInfo.Name));
		dataStoreFileStreamCreated.CopyTo(createdElementOutStream);
		var createdElementOutStreamLength = createdElementOutStream.Length;
		createdElementOutStream.Close();
		dataStoreFileStreamCreated.Close();

		// create element without document, update and set link
		var createLinkInfo = CreateDataSet(null, 1);

		// update link element
		DataStoreReference dataStoreFileLink = new DataStoreReference(_randomTableName, createdElement.Id);
		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link")
		{
			Comment = 2 + ": updated file with link"
		};
		_connection.UpdateFileLink(_randomTableName, createLinkInfo.ElementId, dataStoreFileLink, origin);

		// delete original document
		_connection.DeleteElement(_randomTableName, createdElement.Id, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari-link"));

		// check file size of new full document (ex link)
		var createdLinkElementWithFileAfterDeletion = _compareConnection.GetElement(_randomTableName, createLinkInfo.ElementId);
		DataStoreFileStream exLinkFileStream = _compareConnection.GetFile(_randomTableName, createdLinkElementWithFileAfterDeletion.FileInfo!.Id);
		Stream exLinkOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), createdLinkElementWithFileAfterDeletion.FileInfo.Name));
		exLinkFileStream.CopyTo(exLinkOutStream);
		var exLinkOutStreamLength = exLinkOutStream.Length;
		exLinkOutStream.Close();
		exLinkFileStream.Close();

		localTestDirectory.Delete(true);
		Assert.Equal(createdElementOutStreamLength, exLinkOutStreamLength);
		Assert.Null(_compareConnection.GetElement(_randomTableName, createInfo.ElementId));
	}

	private string UploadFile(StorageConnection storageConnection, string filePath)
	{
		FileInfo fi = new FileInfo(filePath);
		string fileName = fi.Name;
		Stream fileStream = File.OpenRead(fi.FullName);
		DataStoreFileStream dsFileStream = new DataStoreFileStream(fileName, DateTime.UtcNow, fileStream);
		string tempFileId = storageConnection.UploadFile(_randomTableName, dsFileStream);
		dsFileStream.Close();
		return tempFileId;
	}


	private DataStoreSuccessInfo UpdateDataSet(string elementId, string? tempFileId, int counter)
	{
		// create
		var updateDict = new Dictionary<string, object?>()
		{
			["testcol1"] = "teststring update",
			["testcol2"] = 456,
			["testcol_String"] = new[] { "Sat", "Sun" }
		};

		DataStoreElementData dataStoreElementData = (tempFileId != null)
														? new DataStoreElementData(elementId, updateDict, null, tempFileId)
														: new DataStoreElementData(elementId, updateDict);

		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari")
		{
			Comment = counter + ": updated file revision"
		};

		var updateInfo = _connection.UpdateElement(_randomTableName, dataStoreElementData, origin);
		return updateInfo;
	}

	private void CreateDataSource(bool withRevisions)
	{
		// use 2 different connections to check that changes are persisted
		_connection = (StorageConnection)_fixture.StorageInstance.GetConnection(_fixture.CustomerContext.Identifier,
																				new DataStoreAuthentication(
																					"testuser", Guid.NewGuid(), new List<string>() { "test" }));
		_mainConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);
		_compareConnection =
			(StorageConnection)_fixture.StorageInstance.GetConnection(_fixture.CustomerContext.Identifier,
																	  new DataStoreAuthentication("testuser", Guid.NewGuid(), new List<string>() { "test" }));

		// test create table with revision and changed field definition
		var config = new StorageDataSourceConfig(_randomTableName)
		{
			StoreRevisions = withRevisions,
			StoragePath = _randomTableName
		};

		var definition = _mainConnection.CreateDataSource(config);

		var sfd1 = new StorageField("testcol1")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.String,
			Unique = false,
			Length = 99,
			Encrypted = false
		};
		_mainConnection.CreateField(definition.Name, sfd1);

		var sfd2 = new StorageField("testcol2")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.Integer,
			Unique = false,
			Encrypted = false
		};
		_mainConnection.CreateField(definition.Name, sfd2);

		var sfd3 = new StorageField("testcol_String")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.String,
			Unique = false,
			Length = 50,
			Encrypted = false,
			MultiValue = true
		};
		_mainConnection.CreateField(definition.Name, sfd3);
	}

	private DataStoreSuccessInfo CreateDataSet(string? tempFileId, int counter)
	{
		// create
		var createDict = new Dictionary<string, object?>()
		{
			["testcol1"] = "teststring create",
			["testcol2"] = 123,
			["testcol_String"] = new[] { "Mon", "Tue", "Wed", "Thu", "Fri" }
		};

		DataStoreElementData dataStoreElementData = (tempFileId != null)
														? new DataStoreElementData(createDict, new List<string> { "test" }, tempFileId)
														: new DataStoreElementData(createDict, new List<string> { "test" });

		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari")
		{
			Comment = counter + ": initial file revision"
		};

		var createInfo =
			_connection.CreateElement(_randomTableName, dataStoreElementData, origin, null, null);
		return createInfo;
	}
}