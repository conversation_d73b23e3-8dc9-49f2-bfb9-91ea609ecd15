using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Domain.Storage;
using Levelbuild.Domain.WebAppTests.Storage.Setup;

namespace Levelbuild.Domain.WebAppTests.Storage;

[ExcludeFromCodeCoverage]
[Collection("StoragePostgresDatabaseCollection")]
public class PlaceholderTestPostgres : PlaceholderTest, IClassFixture<PostgresDatabaseFixture>
{
	public PlaceholderTestPostgres(PostgresDatabaseFixture fixture) : base(fixture)
	{
		TestDirectory = Directory.CreateDirectory(Path.Combine(fixture.TempPath, "PostgresSQL", "PlaceholderTest"));
	}
}

/*[ExcludeFromCodeCoverage]
[Collection("StorageSqlServerDatabaseCollection")]
public class PlaceholderTestSqlServer : PlaceholderTest, IClassFixture<SqlServerDatabaseFixture>
{
	public PlaceholderTestSqlServer(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		TestDirectory = Directory.CreateDirectory(Path.Combine(fixture.TempPath, "MSSQL", "PlaceholderTest"));
	}
}*/

public abstract class PlaceholderTest
{
	private DatabaseFixture _fixture;
	private StorageConnection _connection = null!;
	private StorageConnection _mainConnection = null!;
	private StorageConnection _compareConnection = null!;
	private string _randomTableName;
	protected DirectoryInfo TestDirectory = null!;
	private string _testSettingsFile = @"testsettings.json";
	private string _testTextFile = @"test.log";


	public PlaceholderTest(DatabaseFixture fixture)
	{
		_fixture = fixture;
		_randomTableName = DatabaseFixture.RandomString(20);
	}

	[Trait("Category", "Placeholder Tests")]
	[Fact(DisplayName = "Create and update data source with placeholders")]
	public void CreateAndUpdateDataSource()
	{
		CreateDataSource(true, "##testcol1##");
		var storageIndexDefinition1 = _connection.GetIndexDefinition(_randomTableName);

		UpdateDataSource(true, "##testcol2##");
		var storageIndexDefinition2 = _connection.GetIndexDefinition(_randomTableName);

		UpdateDataSource(true, "##testcol1##\\##testcol2##");
		var storageIndexDefinition3 = _connection.GetIndexDefinition(_randomTableName);

		Assert.Single(storageIndexDefinition1!.StoragePathDict);
		Assert.Equal(2, storageIndexDefinition2!.StoragePathDict.Count);
		Assert.Equal(3, storageIndexDefinition3!.StoragePathDict.Count);

		foreach (var storagePath in storageIndexDefinition3.StoragePathDict)
		{
			Assert.Contains("##testcol", storagePath.Value.Path);
		}
	}

	[Trait("Category", "Placeholder Tests")]
	[Fact(DisplayName = "Upload files, create and update dataset with file and revisions in data depending directories")]
	public void UploadFileCreateAndUpdate()
	{
		DirectoryInfo localTestDirectory = Directory.CreateDirectory(Path.Combine(TestDirectory.ToString(), "UploadFileCreateAndUpdate"));

		CreateDataSource(true, "##testcol1##");
		var createdFileInfo = new FileInfo(_testSettingsFile);
		string tempFileId = UploadFile(_connection, createdFileInfo.FullName);
		DataStoreFileStream dataStoreFileStreamTempCreated = _compareConnection.GetFile(_randomTableName, tempFileId);
		var dataStoreFileStreamTempCreatedLength = dataStoreFileStreamTempCreated.Length;
		dataStoreFileStreamTempCreated.Close();

		var createInfo = CreateDataSet(tempFileId, 0);
		var createdElement = _compareConnection.GetElement(_randomTableName, createInfo.ElementId);
		DataStoreFileStream dataStoreFileStreamCreated = _compareConnection.GetFile(_randomTableName, createdElement.FileInfo!.Id);
		Stream jsonOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), createdElement.FileInfo.Name));
		dataStoreFileStreamCreated.CopyTo(jsonOutStream);
		var jsonOutStreamLength = jsonOutStream.Length;
		jsonOutStream.Close();
		dataStoreFileStreamCreated.Close();

		var updatedFileInfo = new FileInfo(@"Storage/Test.pdf");
		tempFileId = UploadFile(_connection, updatedFileInfo.FullName);
		DataStoreFileStream dataStoreFileStreamTempUpdated = _compareConnection.GetFile(_randomTableName, tempFileId);
		var dataStoreFileStreamTempUpdatedLength = dataStoreFileStreamTempUpdated.Length;
		dataStoreFileStreamTempUpdated.Close();

		UpdateDataSource(true, "##testcol2##");
		var updateInfo = UpdateDataSet(createdElement.Id, null, tempFileId, 2);
		var updatedElement = _compareConnection.GetElement(_randomTableName, updateInfo.ElementId);
		DataStoreFileStream dataStoreFileStreamUpdated = _compareConnection.GetFile(_randomTableName, updatedElement.FileInfo!.Id);
		Stream pdfOutStream = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), updatedElement.FileInfo.Name));
		dataStoreFileStreamUpdated.CopyTo(pdfOutStream);

		var pdfOutStreamLength = pdfOutStream.Length;
		pdfOutStream.Close();
		dataStoreFileStreamUpdated.Close();

		DataStoreFileStream dataStoreFileStreamCreatedOldRevision = _compareConnection.GetFile(_randomTableName, createdElement.FileInfo.Id);
		Stream jsonOutStreamOldRevision = File.OpenWrite(Path.Combine(localTestDirectory.ToString(), createdElement.FileInfo.Name));
		dataStoreFileStreamCreatedOldRevision.CopyTo(jsonOutStreamOldRevision);
		var jsonOutStreamOldRevisionLength = jsonOutStreamOldRevision.Length;
		jsonOutStreamOldRevision.Close();
		dataStoreFileStreamCreatedOldRevision.Close();

		localTestDirectory.Delete(true);
		Assert.Equal(dataStoreFileStreamTempCreated.Name, dataStoreFileStreamCreated.Name);
		Assert.True(_fixture.CompareDatetime(dataStoreFileStreamTempCreated.Date, dataStoreFileStreamCreated.Date));

		Assert.Equal(dataStoreFileStreamTempUpdated.Name, dataStoreFileStreamUpdated.Name);
		Assert.True(_fixture.CompareDatetime(dataStoreFileStreamTempUpdated.Date, dataStoreFileStreamUpdated.Date));

		Assert.Equal(dataStoreFileStreamTempCreatedLength, jsonOutStreamLength);
		Assert.Equal(dataStoreFileStreamTempUpdatedLength, pdfOutStreamLength);
		Assert.Equal(dataStoreFileStreamTempCreatedLength, jsonOutStreamOldRevisionLength);
	}

	[Trait("Category", "Placeholder Tests")]
	[Fact(DisplayName = "Test updates with changing directory depending column values")]
	public void MultipleValuesTest()
	{
		CreateDataSource(true, "##testcol2##");

		var fileInfo1 = new FileInfo(_testSettingsFile);
		var fileInfo2 = new FileInfo(_testTextFile);

		var revisionCount = 0;
		for (int i = 0; i < 10; i++)
		{
			string tempFileId = UploadFile(_connection, fileInfo1.FullName);
			var createInfo = CreateDataSet(tempFileId, i);

			tempFileId = UploadFile(_connection, fileInfo2.FullName);
			var updateInfo = UpdateDataSet(createInfo.ElementId, null, tempFileId, i);
			Thread.Sleep(100);

			var updateDict = new Dictionary<string, object?>()
			{
				["testcol1"] = "test+string Üupdate@*",
				["testcol2"] = 2024,
				["testcol_String"] = new[] { "Sat", "Sun" }
			};
			tempFileId = UploadFile(_connection, fileInfo1.FullName);
			UpdateDataSet(createInfo.ElementId, updateDict, tempFileId, i);
			Thread.Sleep(100);

			DataStoreRevisionQuery dataStoreRevisionQuery = new DataStoreRevisionQuery(_randomTableName, updateInfo.ElementId);
			var revisions = _compareConnection.GetRevisions(dataStoreRevisionQuery);
			revisionCount += revisions.Count;
		}

		DataStoreQuery query = new DataStoreQuery(_randomTableName, null);
		var elements = _compareConnection.GetElements(query);

		DirectoryInfo directoryInfo = new DirectoryInfo(Path.Combine(_connection.Db.CustomerContext!.StoragePath, _randomTableName));

		Assert.Equal(10, elements.Count);
		Assert.Equal(30, revisionCount);
		Assert.Equal(3, directoryInfo.GetDirectories().Length);
	}

	private string UploadFile(StorageConnection storageConnection, string filePath)
	{
		FileInfo fi = new FileInfo(filePath);
		string fileName = fi.Name;
		Stream fileStream = File.OpenRead(fi.FullName);
		DataStoreFileStream dsFileStream = new DataStoreFileStream(fileName, DateTime.UtcNow, fileStream);
		string tempFileId = storageConnection.UploadFile(_randomTableName, dsFileStream);
		dsFileStream.Close();
		return tempFileId;
	}


	private DataStoreSuccessInfo UpdateDataSet(string elementId, Dictionary<string, object?>? updateDictionary, string? tempFileId, int counter)
	{
		// update
		var updateDict = (updateDictionary != null)
							 ? updateDictionary
							 : new Dictionary<string, object?>()
							 {
								 ["testcol1"] = "test+string Üupdate@*",
								 ["testcol2"] = 2023,
								 ["testcol_String"] = new[] { "Sat", "Sun" }
							 };

		DataStoreElementData dataStoreElementData = (tempFileId != null)
														? new DataStoreElementData(elementId, updateDict, null, tempFileId)
														: new DataStoreElementData(elementId, updateDict);

		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari")
		{
			Comment = counter + ": updated file revision"
		};

		var updateInfo = _connection.UpdateElement(_randomTableName, dataStoreElementData, origin);
		return updateInfo;
	}

	private void CreateDataSource(bool withRevisions, string placeholder)
	{
		DataStoreAuthentication auth = new DataStoreAuthentication("testuser", Guid.NewGuid(), new List<string>() { "test" });
		// use 2 different connections to check that changes are persisted 
		_connection = (StorageConnection)_fixture.StorageInstance.GetConnection(_fixture.CustomerContext.Identifier, auth);
		_mainConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);
		_compareConnection = (StorageConnection)_fixture.StorageInstance.GetConnection(_fixture.CustomerContext.Identifier, auth);

		// test create table with revision and changed field definition
		var config = new StorageDataSourceConfig(_randomTableName)
		{
			StoreRevisions = withRevisions,
			StoragePath = _randomTableName + "/" + placeholder
		};

		var definition = _mainConnection.CreateDataSource(config);

		var sfd1 = new StorageField("testcol1")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.String,
			Unique = false,
			Length = 99,
			Encrypted = false
		};
		_mainConnection.CreateField(definition.Name, sfd1);

		var sfd2 = new StorageField("testcol2")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.Integer,
			Unique = false,
			Encrypted = false
		};
		_mainConnection.CreateField(definition.Name, sfd2);

		var sfd3 = new StorageField("testcol_String")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.String,
			Unique = false,
			Length = 50,
			Encrypted = false,
			MultiValue = true
		};
		_mainConnection.CreateField(definition.Name, sfd3);
	}


	private void UpdateDataSource(bool withRevisions, string placeholder)
	{
		// test create table with revision and changed field definition
		var config = new StorageDataSourceConfig(_randomTableName)
		{
			StoreRevisions = withRevisions,
			StoragePath = _randomTableName + "/" + placeholder
		};

		_mainConnection.UpdateDataSource(config);
	}

	private DataStoreSuccessInfo CreateDataSet(string? tempFileId, int counter)
	{
		// create
		var createDict = new Dictionary<string, object?>()
		{
			["testcol1"] = "Äteststringäöü _ createé",
			["testcol2"] = 2022,
			["testcol_String"] = new[] { "Mon", "Tue", "Wed", "Thu", "Fri" }
		};

		DataStoreElementData dataStoreElementData = (tempFileId != null)
														? new DataStoreElementData(createDict, new List<string> { "test" }, tempFileId)
														: new DataStoreElementData(createDict, new List<string> { "test" });

		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ari")
		{
			Comment = counter + ": initial file revision"
		};

		var createInfo =
			_connection.CreateElement(_randomTableName, dataStoreElementData, origin, null, null);
		return createInfo;
	}
}