using Levelbuild.Core.DataStoreInterface;
using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.StorageEntities;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MongoDB.Driver;
using Serilog;

// ReSharper disable UnusedAutoPropertyAccessor.Global
#pragma warning disable CS8618, CS9264

namespace Levelbuild.Domain.WebAppTests.Storage.Setup;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public abstract class DatabaseFixture : IDisposable
{
	public static IConfiguration Config { get; set; }
	public IConfiguration TestConfig { get; set; }
	public StorageContextOrm CustomerContext { get; set; }

	public static readonly Random Random = new();

	public String TempPath = Path.Combine(Path.GetTempPath(), "StorageTests");

	public Db Db;
	public Db MainDb;
	public IMongoDatabase MongoDb;
	public IDataStore StorageInstance;

	public static ILogger Logger { get; set; } = new LoggerConfiguration()
		.MinimumLevel.Warning()
		.CreateLogger();

	public string StoragePath { get; set; }
	public string StorageTempPath { get; set; }

	public DatabaseFixture()
	{
		InitConfiguration();
		// ReSharper disable once VirtualMemberCallInConstructor
		CreateFixture();
	}

	public abstract void CreateFixture();

	public void Dispose()
	{
		DeleteTestTables(true);
		DeleteTestTables(false);
		DeleteTestDatabases();
		ClearStorageMainPath();
		ClearStorageTempPath();
	}

	private void ClearStorageMainPath()
	{
		if (Path.Exists(StoragePath))
			Directory.Delete(@Path.GetFullPath(StoragePath), true);
	}

	private void ClearStorageTempPath()
	{
		if (Path.Exists(StorageTempPath))
			Directory.Delete(@Path.GetFullPath(StorageTempPath), true);
	}

	public virtual bool CompareDatetime(DateTime first, DateTime second)
	{
		return first.ToUniversalTime() == second.ToUniversalTime();
	}

	protected abstract void DeleteTestDatabases();


	protected void DeleteTestTables(bool main)
	{
		using (StorageDatabaseContext db = GetDatabaseContextByType(main))
		{
			var currentDatabase = (main) ? MainDb : Db;
			db.StorageTempFile.ExecuteDelete();
			db.StorageLink.ExecuteDelete();
			db.StorageContext.ExecuteDelete();
			db.StorageSchemaChangeError.ExecuteDelete();
			db.StorageSchemaChange.ExecuteDelete();

			var defs = db.StorageIndexDefinition.Include(it => it.Fields);
			foreach (var def in defs)
			{
				currentDatabase.Migrator.DeleteConstraints(def, null!);

				try
				{
					// drop views in database for definition if exists
					currentDatabase.ConnectionHelper.WithQueryFactory(factory => factory.Statement($"""
																									DROP VIEW "{def.Name}_Short";
																									"""));
				}
				catch (Exception)
				{
				}
			}

			foreach (var def in defs)
			{
				if (def.StoreRevisions)
				{
					if (!string.IsNullOrWhiteSpace(CustomerContext.MongoDbConnectionString))
					{
						MongoDb.DropCollection(def.Name);
						MongoDb.DropCollection($"{def.Name}_Fields");
					}

					// if old revision tables existing
					var revDef = def.GetRevisionDefinition(true);
					if (revDef != null)
						currentDatabase.Migrator.Delete(revDef, null!);
				}

				if (def.HasMultiValueFields())
				{
					var mvfDef = def.GetMvfDefinition(true);
					if (mvfDef != null)
						currentDatabase.Migrator.Delete(mvfDef, null!);
				}

				currentDatabase.Migrator.Delete(def, null!);
				db.StorageIndexDefinition.Remove(def);
			}

			db.SaveChanges();
		}
	}

	public abstract StorageDatabaseContext GetDatabaseContextByType(bool main = false);

	public static string RandomString(int length)
	{
		const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
		return "test_" + new string(Enumerable.Repeat(chars, length)
										.Select(s => s[Random.Next(s.Length)]).ToArray());
	}

	private void InitConfiguration()
	{
		Config = new ConfigurationBuilder()
			.SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
			.AddJsonFile("testsettings.json")
			.Build()
			.GetSection("Storage");

		TestConfig = new ConfigurationBuilder()
			.SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
			.AddJsonFile("testsettings.json")
			.Build()
			.GetSection("Storage");
	}
}