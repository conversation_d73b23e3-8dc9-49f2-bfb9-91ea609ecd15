using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.FileInterface.Enum;
using Levelbuild.Core.StorageInterface;
using Levelbuild.Core.StorageInterface.Constants;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Domain.Storage;
using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.Storage.Db.Postgres;
using Levelbuild.Domain.Storage.Helper;
using Levelbuild.Domain.StorageEntities;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Serilog;

namespace Levelbuild.Domain.WebAppTests.Storage.Setup;

/// <summary>
/// Fixture for tests
/// </summary>
public class PostgresDatabaseFixture : DatabaseFixture
{
	public override void CreateFixture()
	{
		var connStr = Config.GetConnectionString("PostgreSQL")!;
		MainDb = new Db(SqlType.Postgres, new FeatureFlags(new Dictionary<string, object>()
						{
							["UseElasticsearch"] = true
						}), connStr, null, null, new PostgresConnectionHelper(connStr, Log.Logger), GetPooledDbContextFactory(connStr),
						new HashSet<string>(), Log.Logger);
		using (var db = GetSinglePostgresStorageDatabaseContextInstance(connStr))
		{
			db.Database.Migrate();
		}

		DeleteTestDatabases();

		var otherConnectionString = Config.GetSection("CustomerContext").GetConnectionString("PostgreSQL")!;

		var section = Config.GetSection("CustomerContext");
		StoragePath = Path.Combine(section.GetValue<string>("storagePath")!, "PostgreSQL");
		StorageTempPath = Path.Combine(section.GetValue<string>("tempPath")!, "PostgreSQL");

		Enum.TryParse<FileStoreType>(section.GetValue<string>("filestoreType"), out var fileStoreType);

		CustomerContext = new StorageContextOrm(new StorageContext(section.GetValue<string>("identifier")!, new Dictionary<string, object>()))
		{
			Description = section.GetValue<string>("description"),
			DatabaseType = SqlType.Postgres,
			DatabaseConnectionString = section.GetConnectionString("PostgreSQL")!,
			StoragePath = StoragePath,
			TemporaryPath = StorageTempPath,
			FilestoreContainer = section.GetValue<string>("filestoreContainer"),
			FilestoreConnectionString = section.GetValue<string>("filestoreConnection"),
			FilestoreUsername = section.GetValue<string>("filestoreUsername"),
			FilestorePassword = section.GetValue<string>("filestorePassword"),
			FilestoreType = fileStoreType,
			Language = section.GetValue<string>(StorageConfigurationConstants.Language)!,
			ElasticConnectionString = section.GetConnectionString(StorageConfigurationConstants.ElasticConnectionString)!,
			MongoDbConnectionString = section.GetConnectionString(StorageConfigurationConstants.MongoDbConnectionString)!
		};

		Db = new Db(SqlType.Postgres, new FeatureFlags(new Dictionary<string, object>()
					{
						["UseElasticsearch"] = true
					}), otherConnectionString, CustomerContext, new DataStoreAuthentication("testuser", Guid.NewGuid(), new List<string>() { "testgroup" }),
					new PostgresConnectionHelper(otherConnectionString, Log.Logger), GetPooledDbContextFactory(otherConnectionString), new HashSet<string>(),
					Log.Logger);
		using (var db = GetSinglePostgresStorageDatabaseContextInstance(otherConnectionString))
		{
			db.Database.Migrate();
		}

		if (!string.IsNullOrWhiteSpace(CustomerContext.MongoDbConnectionString))
			MongoDb = Db.MongoDbClient!.GetDatabase(
				FilterParser.ShortenAlias(Db.ConnectionHelper.GetDatabaseName(), null, MongoDbRevisionHelper.MongoDbNameMaxLength));

		DeleteTestTables(true);
		DeleteTestTables(false);

		StorageInstance = Levelbuild.Domain.Storage.Storage.GetInstance(new Dictionary<string, object>()
		{
			[StorageConfigurationConstants.DatabaseConnectionString] = Config.GetConnectionString("PostgreSQL")!,
			[StorageConfigurationConstants.DatabaseType] = "postgres",
			["UseElasticsearch"] = true
		}, Logger);

		GetSinglePostgresStorageDatabaseContextInstance(CustomerContext.DatabaseConnectionString).Database.EnsureDeleted();
		((IStorageConnection)StorageInstance.GetConnection(null)).CreateContext(CustomerContext.ToDto());
	}

	protected override void DeleteTestDatabases()
	{
		List<IDictionary<string, object>> databaseEntries = MainDb.ConnectionHelper.WithQueryFactory(
			factory => factory.Select("SELECT datname FROM pg_database")).Select(it => (IDictionary<string, object>)it).ToList();

		foreach (var databaseEntry in databaseEntries)
		{
			if (databaseEntry["datname"].ToString()!.ToLower().Contains("_customer_test")
				|| databaseEntry["datname"].ToString()!.ToLower().Contains("migrate_context_"))
			{
				string sql = "DROP DATABASE \"" + databaseEntry["datname"] + "\" WITH (FORCE)";
				MainDb.ConnectionHelper.WithQueryFactory(factory => { factory.Statement(sql, timeout: 60); });
			}
		}
	}

	public override StorageDatabaseContext GetDatabaseContextByType(bool main = false)
	{
		if (!main)
			return GetSinglePostgresStorageDatabaseContextInstance(Config.GetSection("CustomerContext").GetConnectionString("PostgreSQL")!);
		else
			return GetSinglePostgresStorageDatabaseContextInstance(Config.GetConnectionString("PostgreSQL")!);
	}

	internal static PostgresStorageDatabaseContext GetSinglePostgresStorageDatabaseContextInstance(string connectionString)
	{
		var factory = GetPooledDbContextFactory(connectionString);

		return factory.CreateDbContext();
	}

	private static PooledDbContextFactory<PostgresStorageDatabaseContext> GetPooledDbContextFactory(string connectionString)
	{
		var options = new DbContextOptionsBuilder<PostgresStorageDatabaseContext>()
			.UseNpgsql(connectionString)
			.Options;

		var factory = new PooledDbContextFactory<PostgresStorageDatabaseContext>(options);
		return factory;
	}
}