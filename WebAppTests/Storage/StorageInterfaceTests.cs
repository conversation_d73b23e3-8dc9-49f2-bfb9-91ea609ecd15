using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.StorageInterface;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Core.WebAppCoreTests.DataStoreInterface;
using Levelbuild.Core.WebAppCoreTests.StorageInterface;
using Levelbuild.Domain.StorageEntities;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Levelbuild.Domain.WebAppTests.Storage.Setup;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;
using Xunit.Abstractions;

namespace Levelbuild.Domain.WebAppTests.Storage;

[ExcludeFromCodeCoverage]
[Collection("StoragePostgresDatabaseCollection")]
public class StorageInterfaceTestsPostgres : StorageInterfaceTests, IClassFixture<PostgresDatabaseFixture>
{
	public StorageInterfaceTestsPostgres(PostgresDatabaseFixture fixture, ITestOutputHelper testOutputHelper) : base(fixture, testOutputHelper)
	{
		// nothing
	}

	public override bool FieldExistsInDb(string table, string fieldName)
	{
		return Fixture.Db.ConnectionHelper.WithQueryFactory(
				   factory => factory.Select(
					   $"SELECT column_name FROM information_schema.columns WHERE table_name='{table}' and column_name='{fieldName}';")).Count() > 0;
	}

	protected override void AssertFulltextResourcesCleared(StorageDataSource storageDataSource)
	{
		var indexes = Fixture.Db.ConnectionHelper.WithQueryFactory(factory => factory.Select($"""
																							  select
																							      t.relname as table_name,
																							      i.relname as index_name,
																							      a.attname as column_name
																							  from
																							      pg_class t,
																							      pg_class i,
																							      pg_index ix,
																							      pg_attribute a
																							  where
																							      t.oid = ix.indrelid
																							      and i.oid = ix.indexrelid
																							      and a.attrelid = t.oid
																							      and a.attnum = ANY(ix.indkey)
																							      and t.relkind = 'r'
																							     -- and t.relname like 'mytable'
																							      and i.relname = '{storageDataSource.Name}_fulltext_idx'
																							  order by
																							      t.relname,
																							      i.relname;
																							  """));
		Assert.Empty(indexes);

		var columns = Fixture.Db.ConnectionHelper.WithQueryFactory(factory => factory.Select($"""
																							  SELECT column_name
																							  FROM information_schema.columns
																							  WHERE table_name='{storageDataSource.Name}' and column_name='tsvector';
																							  """));
		Assert.Empty(columns);
	}
}

/*[ExcludeFromCodeCoverage]
[Collection("StorageSqlServerDatabaseCollection")]
public class StorageInterfaceTestsSqlServer : StorageInterfaceTests, IClassFixture<SqlServerDatabaseFixture>
{
	public StorageInterfaceTestsSqlServer(SqlServerDatabaseFixture fixture, ITestOutputHelper testOutputHelper) : base(fixture, testOutputHelper)
	{
		// nothing
	}

	public override bool FieldExistsInDb(string table, string fieldName)
	{
		return _fixture.Db.ConnectionHelper.WithQueryFactory(
				   factory => factory.Select(
					   $"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table}' AND COLUMN_NAME = '{fieldName}'")).Count() > 0;
	}

	protected override void AssertFulltextResourcesCleared(StorageDataSource storageDataSource)
	{
		var indexes = Fixture.Db.ConnectionHelper.WithQueryFactory(factory => factory.Select($"""
																							  SELECT object_id, property_list_id, stoplist_id FROM sys.fulltext_indexes
																							  where object_id = object_id('{storageDataSource.Name}');
																							  """));
		Assert.Empty(indexes);
	}
}*/

public abstract class StorageInterfaceTests : StorageInterfaceTest
{
	private protected DatabaseFixture Fixture;

	public StorageInterfaceTests(DatabaseFixture fixture, ITestOutputHelper testOutputHelper) : base(
		GetStorage(fixture), StorageSystemField.Id.ToString(),
		testOutputHelper)
	{
		Fixture = fixture;
	}

	public static Domain.Storage.Storage GetStorage(DatabaseFixture fixture)
	{
		IDictionary<string, object> dict = new Dictionary<string, object>();
		dict["UseElasticsearch"] = true;
		foreach (var configurationSection in DatabaseFixture.Config.GetChildren())
		{
			switch (configurationSection.Key)
			{
				case "storagePath":
					dict.Add("storagePath", Path.Combine(configurationSection.Value!, "PostgreSQL"));
					break;
				case "tempPath":
					dict.Add("tempPath", Path.Combine(configurationSection.Value!, "PostgreSQL"));
					break;
				case "ConnectionStrings":
					dict.Add("connectionString", DatabaseFixture.Config.GetConnectionString("PostgreSQL")!);
					dict.Add("dbType", "Postgres");
					break;
				default:
					dict.Add(configurationSection.Key, configurationSection.Value!);
					break;
			}
		}

		ILogger logger = DatabaseFixture.Logger;
		return (Domain.Storage.Storage)Domain.Storage.Storage.GetInstance(dict, logger);
	}


	[Fact(DisplayName = "Create and update linked data sources")]
	public void LinkDataSourceTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(null);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(connection, mainTableName);
		AddTestFields(connection, mainTableName);

		var nstTableName1 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(connection, nstTableName1);
		AddTestFields(connection, nstTableName1);

		var nstTableName2 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(connection, nstTableName2);
		AddTestFields(connection, nstTableName2);

		var nstTableName3 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(connection, nstTableName3);
		AddTestFields(connection, nstTableName3);

		int fkCountBeforeAddingLookup = GetFkCount(mainTableName);

		connection.CreateField(mainTableName, new StorageFieldConfig("IdFieldFromNst1")
		{
			LookupSource = nstTableName1
		});
		connection.CreateField(mainTableName, new StorageFieldConfig("IdFieldFromNst2")
		{
			LookupSource = nstTableName2
		});
		connection.CreateField(mainTableName, new StorageFieldConfig("ToDeleteFieldFromNst")
		{
			LookupSource = nstTableName3
		});

		int fkCountAfterAddingLookup = GetFkCount(mainTableName);

		connection.RenameField(mainTableName, "ToDeleteFieldFromNst", "SecondToDeleteFieldFromNst");
		connection.RemoveField(mainTableName, "SecondToDeleteFieldFromNst");
		int fkCountAfterRemoveOneLookup = GetFkCount(mainTableName);

		connection.RemoveDataSource(mainTableName);

		StorageIndexDefinition? removedDefinition;
		using (StorageDatabaseContext db = Fixture.GetDatabaseContextByType())
		{
			removedDefinition = db.StorageIndexDefinition.Include(it => it.Fields).Where(it => it.Name == mainTableName).FirstOrDefault();
		}

		Assert.Equal(0, fkCountBeforeAddingLookup);
		Assert.Equal(3, fkCountAfterAddingLookup);
		Assert.Equal(2, fkCountAfterRemoveOneLookup);
		Assert.Null(removedDefinition);
	}

	private int GetFkCount(string mainTableName)
	{
		int fkCount;
		using (StorageDatabaseContext db = Fixture.GetDatabaseContextByType(true))
		{
			var def = db.StorageIndexDefinition.Include(it => it.Fields).Where(it => it.Name == mainTableName).FirstOrDefault();
			fkCount = def!.Fields.Where(it => it.LookupSource != null).ToList().Count;
		}

		return fkCount;
	}

	[Fact(DisplayName = "Change field types test")]
	public void ChangeFieldTypeTest()
	{
		var schemaConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		// tests part 1
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateTableForFieldChange(schemaConnection, connection, mainTableName);

		DataStoreQuery query = new DataStoreQuery(mainTableName, null);
		var controlElements = compareConnection.GetElements(query);

		Assert.NotEmpty(controlElements);
		Assert.Throws<ArgumentException>(() => UpdateField(schemaConnection, mainTableName, "BooleanField", DataStoreFieldType.Integer)); // Bool -> Integer
		Assert.Throws<ArgumentException>(() => UpdateField(schemaConnection, mainTableName, "BooleanField", DataStoreFieldType.Long));    // Bool -> Long
		Assert.True(UpdateField(schemaConnection, mainTableName, "IntField", DataStoreFieldType.Double));                                 // Int -> Double

		Assert.True(UpdateField(schemaConnection, mainTableName, "LongField", DataStoreFieldType.String)); // Long -> String
		Assert.True(UpdateField(schemaConnection, mainTableName, "IntField", DataStoreFieldType.String));  // Double -> String
		Assert.True(UpdateField(schemaConnection, mainTableName, "IntField", DataStoreFieldType.Text));    // String -> Text

		if (this is StorageInterfaceTestsPostgres)
		{
			Assert.Throws<ArgumentException>(() => UpdateField(schemaConnection, mainTableName, "DateField", DataStoreFieldType.DateTime)); // Date -> DateTime
			Assert.Throws<ArgumentException>(() => UpdateField(schemaConnection, mainTableName, "TimeField", DataStoreFieldType.DateTime)); // Time -> DateTime
		}
		else
		{
			Assert.True(UpdateField(schemaConnection, mainTableName, "DateField", DataStoreFieldType.DateTime)); // Date -> DateTime
			Assert.True(UpdateField(schemaConnection, mainTableName, "TimeField", DataStoreFieldType.DateTime)); // Time -> DateTime
		}

		Assert.True(UpdateField(schemaConnection, mainTableName, "DateField", DataStoreFieldType.String)); // DateTime -> String

		// Reset table for tests part 2
		schemaConnection.RemoveDataSource(mainTableName);
		CreateTableForFieldChange(schemaConnection, connection, mainTableName);

		Assert.True(UpdateField(schemaConnection, mainTableName, "IntField", DataStoreFieldType.String));     // Int -> String
		Assert.True(UpdateField(schemaConnection, mainTableName, "LongField", DataStoreFieldType.Double));    // Long -> Double
		Assert.True(UpdateField(schemaConnection, mainTableName, "BooleanField", DataStoreFieldType.String)); // Bool -> String

		Assert.True(UpdateField(schemaConnection, mainTableName, "DateField", DataStoreFieldType.String)); // Date -> String
		Assert.True(UpdateField(schemaConnection, mainTableName, "TimeField", DataStoreFieldType.String)); // Time -> String

		// Reset table for tests part 3
		schemaConnection.RemoveDataSource(mainTableName);
		CreateTableForFieldChange(schemaConnection, connection, mainTableName);

		Assert.True(UpdateField(schemaConnection, mainTableName, "IntField", DataStoreFieldType.Long)); // Int -> Long

		// Reset table for tests part 4
		schemaConnection.RemoveDataSource(mainTableName);
		CreateTableForFieldChange(schemaConnection, connection, mainTableName);

		Assert.True(UpdateField(schemaConnection, mainTableName, "DateField", DataStoreFieldType.Text));    // Date -> Text
		Assert.True(UpdateField(schemaConnection, mainTableName, "TimeField", DataStoreFieldType.Text));    // Time -> Text
		Assert.True(UpdateField(schemaConnection, mainTableName, "LongField", DataStoreFieldType.Text));    // Long -> Text
		Assert.True(UpdateField(schemaConnection, mainTableName, "IntField", DataStoreFieldType.Text));     // Int -> Text
		Assert.True(UpdateField(schemaConnection, mainTableName, "BooleanField", DataStoreFieldType.Text)); // Bool -> Text
		Assert.True(UpdateField(schemaConnection, mainTableName, "DoubleField", DataStoreFieldType.Text));  // Double -> Text
	}

	[Fact(DisplayName = "Create element per sql statement and update per API")]
	public void CreateElementPerSqlStatementAndUpdatePerApiTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		StorageDataSourceConfig config = new StorageDataSourceConfig(mainTableName)
		{
			StoreRevisions = true,
			FulltextSearch = false
		};
		_ = connection.CreateDataSource(config);
		AddTestFields(connection, mainTableName);

		var firstNumber = 598748947;
		Fixture.Db.ConnectionHelper.WithQueryFactory(factory => factory.Statement($"""
																				   insert into "{mainTableName}" ("SysGroups", "StringField") values (ARRAY['{TestAuth.Groups!.First()}'], '{firstNumber}');
																				   """));

		var elements = connection.GetElements(
			new DataStoreQuery(mainTableName, null).WithFilter(new QueryFilterGroup(new List<QueryFilter>()
			{
				new EqualsFilter(new QueryFilterField("StringField"), firstNumber)
			})));
		Assert.Single(elements);
		var elementId = elements.First().Id;
		connection.UpdateElement(mainTableName, new DataStoreElementData(elementId, new Dictionary<string, object?>()
		{
			["StringField"] = 1
		}), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 54, "me"));

		Thread.Sleep(100);

		var revisions = connection.GetRevisions(new DataStoreRevisionQuery(mainTableName, elementId));
		Assert.Equal(2, revisions.CountTotal);

		Fixture.Db.ConnectionHelper.WithQueryFactory(factory => factory.Statement($"""
																				   update "{mainTableName}" set "StringField" = '{firstNumber}';
																				   """));

		connection.UpdateElement(mainTableName, new DataStoreElementData(elementId, new Dictionary<string, object?>()
		{
			["StringField"] = 1
		}), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 54, "me"));

		Thread.Sleep(100);

		var revisions1 = connection.GetRevisions(new DataStoreRevisionQuery(mainTableName, elementId));
		Assert.Equal(4, revisions1.CountTotal);
	}

	[Fact(DisplayName = "Test unification of null and empty values")]
	public void NullOrEmptyTest()
	{
		var schemaConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		StorageDataSourceConfig config = new StorageDataSourceConfig(mainTableName)
		{
			StoreRevisions = true,
			FulltextSearch = false
		};
		_ = schemaConnection.CreateDataSource(config);
		StorageFieldConfig stringField = new StorageFieldConfig("StringField")
		{
			Type = DataStoreFieldType.String,
			Length = 255,
			Nullable = true
		};
		connection.CreateField(mainTableName, stringField);

		StorageFieldConfig intField = new StorageFieldConfig("IntField")
		{
			Type = DataStoreFieldType.Integer,
			Nullable = true
		};
		connection.CreateField(mainTableName, intField);

		StorageFieldConfig doubleField = new StorageFieldConfig("DoubleField")
		{
			Type = DataStoreFieldType.Double,
			Nullable = true
		};
		connection.CreateField(mainTableName, doubleField);

		StorageFieldConfig boolField = new StorageFieldConfig("BooleanField")
		{
			Type = DataStoreFieldType.Boolean,
			Nullable = true
		};
		connection.CreateField(mainTableName, boolField);

		StorageFieldConfig dateTimeField = new StorageFieldConfig("DateTimeField")
		{
			Type = DataStoreFieldType.DateTime,
			Nullable = true
		};
		connection.CreateField(mainTableName, dateTimeField);

		connection.CreateElements(mainTableName, new List<DataStoreElementData>()
		{
			new DataStoreElementData(new Dictionary<string, object?>()
			{
				["StringField"] = "test",
				["IntField"] = 123,
				["DoubleField"] = 123.5,
				["BooleanField"] = true,
				["DateTimeField"] = DateTime.UtcNow,
			}, TestAuth.Groups!),
			new DataStoreElementData(new Dictionary<string, object?>()
			{
				["StringField"] = null,
				["IntField"] = null,
				["DoubleField"] = null,
				["BooleanField"] = null,
				["DateTimeField"] = null,
			}, TestAuth.Groups!),
			new DataStoreElementData(new Dictionary<string, object?>()
			{
				["StringField"] = "",
				["IntField"] = 0,
				["DoubleField"] = 0,
				["BooleanField"] = false,
				["DateTimeField"] = DateTime.UnixEpoch,
			}, TestAuth.Groups!),
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"), TestAuth.Groups);

		var stringGroupBy = GetGroupedElements(connection, mainTableName, "StringField");
		var intGroupBy = GetGroupedElements(connection, mainTableName, "IntField");
		var doubleGroupBy = GetGroupedElements(connection, mainTableName, "DoubleField");
		var boolGroupBy = GetGroupedElements(connection, mainTableName, "BooleanField");
		var dateTimeGroupBy = GetGroupedElements(connection, mainTableName, "DateTimeField");

		var stringNull = GetFilteredElements(connection, mainTableName, "StringField", QueryFilterOperator.IsNull);
		var stringNotNull = GetFilteredElements(connection, mainTableName, "StringField", QueryFilterOperator.IsNotNull);
		var stringEmpty = GetFilteredElements(connection, mainTableName, "StringField", QueryFilterOperator.Equals, "");
		var stringNotEmpty = GetFilteredElements(connection, mainTableName, "StringField", QueryFilterOperator.NotEquals, "");

		var intNull = GetFilteredElements(connection, mainTableName, "IntField", QueryFilterOperator.IsNull);
		var intNotNull = GetFilteredElements(connection, mainTableName, "IntField", QueryFilterOperator.IsNotNull);

		var doubleNull = GetFilteredElements(connection, mainTableName, "DoubleField", QueryFilterOperator.IsNull);
		var doubleNotNull = GetFilteredElements(connection, mainTableName, "DoubleField", QueryFilterOperator.IsNotNull);

		var boolNull = GetFilteredElements(connection, mainTableName, "BooleanField", QueryFilterOperator.IsNull);
		var boolNotNull = GetFilteredElements(connection, mainTableName, "BooleanField", QueryFilterOperator.IsNotNull);

		var dateTimeNull = GetFilteredElements(connection, mainTableName, "DateTimeField", QueryFilterOperator.IsNull);
		var dateTimeNotNull = GetFilteredElements(connection, mainTableName, "DateTimeField", QueryFilterOperator.IsNotNull);

		var stringNotLike = GetFilteredElements(connection, mainTableName, "StringField", QueryFilterOperator.NotLike, "blah");
		var intNotLike = GetFilteredElements(connection, mainTableName, "IntField", QueryFilterOperator.NotLike, "blah");
		var doubleNotLike = GetFilteredElements(connection, mainTableName, "DoubleField", QueryFilterOperator.NotLike, "blah");
		var boolNotLike = GetFilteredElements(connection, mainTableName, "BooleanField", QueryFilterOperator.NotLike, "blah");
		var dateTimeNotLike = GetFilteredElements(connection, mainTableName, "DateTimeField", QueryFilterOperator.NotLike, "blah");

		// integer, long, double, string & text are always not nullable.
		Assert.Equal(2, stringGroupBy.Count); // null & empty should be in same group
		Assert.Equal(2, intGroupBy.Count);
		Assert.Equal(2, doubleGroupBy.Count);
		Assert.Equal(3, boolGroupBy.Count);
		Assert.Equal(3, dateTimeGroupBy.Count);

		Assert.Equal(2, stringNull.Count);  // null & empty should be in same group
		Assert.Single(stringNotNull);       // null & empty should be in same group
		Assert.Equal(2, stringEmpty.Count); // null & empty should be in same group
		Assert.Single(stringNotEmpty);      // null & empty should be in same group
		Assert.Empty(intNull);
		Assert.Equal(3, intNotNull.Count);
		Assert.Empty(doubleNull);
		Assert.Equal(3, doubleNotNull.Count);
		Assert.Single(boolNull);
		Assert.Equal(2, boolNotNull.Count);
		Assert.Single(dateTimeNull);
		Assert.Equal(2, dateTimeNotNull.Count);

		Assert.Equal(3, stringNotLike.Count);
		Assert.Equal(3, intNotLike.Count);
		Assert.Equal(3, doubleNotLike.Count);
		Assert.Equal(3, boolNotLike.Count);
		Assert.Equal(3, dateTimeNotLike.Count);
	}

	private DataStoreResultSet<DataStoreElement> GetGroupedElements(IStorageConnection connection, string tableName, string fieldName)
	{
		var queryFields = new List<DataStoreQueryField>()
		{
			new("count(*)", "count"),
			new(fieldName)
		};

		return connection.GetElements(new DataStoreQuery(tableName, queryFields).WithGroupBy(new List<string>() { fieldName }));
	}

	private DataStoreResultSet<DataStoreElement> GetFilteredElements(IStorageConnection connection, string tableName, string fieldName,
																	 QueryFilterOperator queryFilterOperator, object? compareValue = null)
	{
		var filterField = new QueryFilterField(fieldName);
		QueryFilter filter = queryFilterOperator switch
		{
			QueryFilterOperator.IsNull => new IsNullFilter(filterField),
			QueryFilterOperator.IsNotNull => new NotNullFilter(filterField),
			QueryFilterOperator.NotLike => new NotLikeFilter(filterField, compareValue ?? throw new ArgumentNullException(nameof(compareValue))),
			QueryFilterOperator.Equals => new EqualsFilter(filterField, compareValue ?? throw new ArgumentNullException(nameof(compareValue))),
			QueryFilterOperator.NotEquals => new NotEqualsFilter(filterField, compareValue ?? throw new ArgumentNullException(nameof(compareValue))),
			_ => throw new NotImplementedException($"queryFilterOperator {queryFilterOperator} is not implemented")
		};

		return connection.GetElements(
			new DataStoreQuery(tableName, null).WithFilter(new QueryFilterGroup().AddFilter(filter)));
	}
}