using Grpc.Core;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.User;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Customer;
using Levelbuild.Frontend.WebApp.Features.Customer.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.Customer;

[ExcludeFromCodeCoverage]
public class PostgresCustomerControllerTests(PostgresDatabaseFixture fixture) : CustomerControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
public class SqlServerCustomerControllerTests : CustomerControllerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerCustomerControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class CustomerControllerTests : AdminControllerTest<CustomerController, CustomerEntity, CustomerDto>
{
	#region Test Data Properties

	protected override CustomerDto CreateDto => new() { DisplayName = "ImportantCompany" };
	
	protected override CustomerDto InvalidCreateDto => new() { DisplayName = "" };
	
	protected override CustomerDto InvalidUpdateDto => new()
	{
		Id = Guid.Empty,
		DisplayName = "ImportantCompany"
	};
	
	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "displayName",
				Direction = SortDirection.Asc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Limit = 2,
		Offset = 1,
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "displayName",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "displayName",
				Direction = SortDirection.Desc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>
		{
			new()
			{
				FilterColumn = "DisplayName",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "TestCustomer2"
			}
		}
	};

	#endregion
	
	protected CustomerControllerTests(DatabaseFixture fixture) : base(fixture)
	{
		var configStub = new Dictionary<string, string>
		{
			{ "Zitadel:ProjectId", "12345" },
			{ "Zitadel:CustomerRoles:0", "orgAdmin" },
			{ "Zitadel:CustomerRoles:1", "user" }
		};
		var configuration = new ConfigurationBuilder()
			.AddInMemoryCollection(configStub!)
			.Build();
		
		ControllerInstance = new CustomerController(LogManager, Fixture.ContextFactory, UserManager, configuration, ZitadelApiClientFactory, LocalizerFactory, VersionReader);
	}

	#region Data Preparation

	protected override CustomerEntity PrepareSingleEntity()
	{
		var zitadelOrgId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("TestCustomer", new List<string>());
		var customer = new CustomerEntity
		{
			RemoteId = zitadelOrgId,
			DisplayName = "TestCustomer"
		};
		
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();
		
		return entry.Entity;
	}
	
	protected override CustomerDto GetUpdateDto(CustomerEntity entity)
	{
		return new CustomerDto
		{
			Id = entity.Id,
			DisplayName = "ImportantCompany"
		};
	}
	
	protected override async Task PrepareQueryDataAsync()
	{
		var zitadelOrgIds = new List<string> { ZitadelApiClientFactory.GetManagementClient().AddOrganisation("TestCustomer1", new List<string>()) };
		QueryData.Add(new CustomerDto
		{
			Id = new Guid("00000000-0000-0000-0000-000000000001"),
			DisplayName = "TestCustomer1"
		});
		
		zitadelOrgIds.Add(ZitadelApiClientFactory.GetManagementClient().AddOrganisation("TestCustomer2", new List<string>()));
		QueryData.Add(new CustomerDto
		{
			Id = new Guid("00000000-0000-0000-0000-000000000002"),
			DisplayName = "TestCustomer2"
		});
		
		zitadelOrgIds.Add(ZitadelApiClientFactory.GetManagementClient().AddOrganisation("TestCustomer3", new List<string>()));
		QueryData.Add(new CustomerDto
		{
			Id = new Guid("00000000-0000-0000-0000-000000000003"),
			DisplayName = "TestCustomer3"
		});
		
		zitadelOrgIds.Add(ZitadelApiClientFactory.GetManagementClient().AddOrganisation("TestCustomer4", new List<string>()));
		QueryData.Add(new CustomerDto
		{
			Id = new Guid("00000000-0000-0000-0000-000000000004"),
			DisplayName = "TestCustomer4"
		});

		await using var databaseContext = Fixture.Context;
		
		// create some records in db to query data
		for(var i = 0; i < QueryData.Count; i++)
		{
			var dto = QueryData[i];
			var orgId = zitadelOrgIds[i];
			databaseContext.Customers.Add(new CustomerEntity(dto, orgId));
		}
		
		await databaseContext.SaveChangesAsync();
	}

	#endregion

	#region Assertions

	protected override void AssertIsAsExpected(CustomerDto expected, CustomerDto actual)
	{
		Assert.Equal(expected.DisplayName, actual.DisplayName);

		using var databaseContext = Fixture.Context;
		var instance = databaseContext.Customers.Find(actual.Id);
		Assert.NotNull(instance);
		
		Assert.Equal(expected.DisplayName, ZitadelApiClientFactory.GetManagementClient().GetOrganisation(instance.RemoteId).Name);
	}
	
	protected override void AssertExists(CustomerDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.Customers.Any(customer => customer.DisplayName == dto.DisplayName));
	}

	protected override void AssertDoesNotExist(CustomerDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.Customers.All(customer => customer.DisplayName != dto.DisplayName));
	}

	protected override bool DeleteSuccessful(CustomerEntity entity)
	{
		Assert.Throws<RpcException>(() => ZitadelApiClientFactory.GetManagementClient().GetOrganisation(entity.RemoteId));
		
		using var databaseContext = Fixture.Context;
		return !databaseContext.Customers.Any(customer => customer.Id == entity.Id);
	}

	protected override bool CheckQueryResult(ConfigQueryResultDto<CustomerDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var queryData = QueryData.ToList();
		
		using var databaseContext = Fixture.Context;
		
		// Add the MockCustomer that gets created by default.
		queryData.Add(databaseContext.Customers.First().ToDto());
		queryData = queryData.OrderBy(customer => customer.DisplayName).ToList();
		
		var count = queryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.DisplayName);
		var expectedList = new List<string>();
		for(var i = offset; i < count && (expectedList.Count < limit || limit == 0); i++)
		{
			expectedList.Add(queryData[i].DisplayName);
		}
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (limit > 0 && queryResultDto.Rows.Count > limit)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i] != mutatedNamesList[i])
				return false;
		}

		return true;
	}
	
	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<CustomerDto> queryResultDto)
	{
		var queryData = QueryData.ToList();
		
		using var databaseContext = Fixture.Context;
		
		// Add the MockCustomer that gets created by default.
		queryData.Add(databaseContext.Customers.First().ToDto());
		queryData = queryData.OrderBy(customer => customer.DisplayName).ToList();
		
		var count = queryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.DisplayName);
		var expectedList = new List<string>();
		for(var i = count - 1; i >= 0; i--)
		{
			expectedList.Add(queryData[i].DisplayName);
		}
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i] != mutatedNamesList[i])
				return false;
		}

		return true;
	}

	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<CustomerDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;

		return QueryData[1].DisplayName == queryResultDto.Rows.FirstOrDefault()?.DisplayName;
	}

	#endregion
	
	#region Tests

	#region Create

	[Fact(DisplayName = "Fail to create Customer with invalid app settings")]
	public async Task InvalidAppSettingsCreateTest()
	{
		var configuration = new ConfigurationBuilder()
			.AddInMemoryCollection(new Dictionary<string, string>()!)
			.Build();
		var customerController = new CustomerController(LogManager, Fixture.ContextFactory, UserManager, configuration, ZitadelApiClientFactory, LocalizerFactory, VersionReader);

		var result = (await customerController.Create(CreateDto)).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());

		await using var databaseContext = Fixture.Context;
		Assert.Null(databaseContext.Customers.FirstOrDefault(customer => customer.DisplayName == CreateDto.DisplayName));
		Assert.Equal(404, result.StatusCode);
	}

	#endregion

	#region Update

	[Fact(DisplayName = "Fail to update Customer that does not exist in Zitadel")]
	public async Task InvalidRemoteIdUpdateTest()
	{
		await using var databaseContext = Fixture.Context;
		
		var customer = new CustomerEntity
		{
			RemoteId = "IAmInvalid",
			DisplayName = "TestCustomer"
		};
		databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();

		var guid = databaseContext.Customers.First().Id;
		var dto = new CustomerDto
		{
			Id = guid,
			DisplayName = "ImportantCompany"
		};

		var result = (await ControllerInstance!.Update(guid, dto)).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Null(databaseContext.Customers.FirstOrDefault(customerEntity => customerEntity.DisplayName == dto.DisplayName));
		Assert.Equal(400, result.StatusCode);
	}

	#endregion

	#region Delete

	[Fact(DisplayName = "Fail to delete Customer that does not exist in Zitadel")]
	public void InvalidRemoteIdDeleteTest()
	{
		using var databaseContext = Fixture.Context;
		
		var customer = new CustomerEntity
		{
			RemoteId = "IAmInvalid",
			DisplayName = "TestCustomer"
		};
		databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();

		var guid = databaseContext.Customers.First().Id;
		var result = ControllerInstance!.Delete(guid).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
		Assert.NotNull(databaseContext.Customers.SingleOrDefault(customerEntity => customerEntity.Id == guid));
	}

	#endregion
	
	#region Others
	
	[Fact(DisplayName = "Deactivate Customer")]
	public void DeactivateCustomerTest()
	{
		var zitadelRemoteId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("IAmActive", new List<string>()); 
		var customer = new CustomerEntity
		{
			RemoteId = zitadelRemoteId,
			DisplayName = "TestCustomer"
		};
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();
		
		var guid = entry.Entity.Id;
		var result = ControllerInstance!.DeactivateCustomer(guid).Result as ObjectResult;
		
		databaseContext.Entry(entry.Entity).Reload();
		var customerResult = databaseContext.Customers.SingleOrDefault(customerEntity => customerEntity.Id == guid);

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse<CustomerDto>), result.Value!.GetType());
		Assert.Equal(200, result.StatusCode);
		Assert.NotNull(customerResult);
		Assert.False(customerResult.Enabled);
	}
	
	[Fact(DisplayName = "Activate Customer")]
	public void ActivateCustomerTest()
	{
		var zitadelRemoteId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("IAmInactive", new List<string>()); 
		var customer = new CustomerEntity
		{
			RemoteId = zitadelRemoteId,
			DisplayName = "TestCustomer",
			Enabled = false
		};
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();

		var guid = entry.Entity.Id;
		var result = ControllerInstance!.ActivateCustomer(guid).Result as ObjectResult;
		
		databaseContext.Entry(entry.Entity).Reload();
		var customerResult = databaseContext.Customers.SingleOrDefault(customerEntity => customerEntity.Id == guid);

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse<CustomerDto>), result.Value!.GetType());
		Assert.Equal(200, result.StatusCode);
		Assert.NotNull(customerResult);
		Assert.True(customerResult.Enabled);
	}
	
	[Fact(DisplayName = "Fail to deactivate inactive Customer")]
	public void FailDeactivateInactiveCustomerTest()
	{
		var zitadelRemoteId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("IAmActive", new List<string>()); 
		var customer = new CustomerEntity
		{
			RemoteId = zitadelRemoteId,
			DisplayName = "TestCustomer",
			Enabled = false
		};
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();
		
		var guid = entry.Entity.Id;
		var result = ControllerInstance!.DeactivateCustomer(guid).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
	}
	
	[Fact(DisplayName = "Fail to activate active Customer")]
	public void FailActivateActiveCustomerTest()
	{
		var zitadelRemoteId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("IAmActive", new List<string>()); 
		var customer = new CustomerEntity
		{
			RemoteId = zitadelRemoteId,
			DisplayName = "TestCustomer",
			Enabled = true
		};
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();
		
		var guid = entry.Entity.Id;
		var result = ControllerInstance!.ActivateCustomer(guid).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
	}
	
	[Fact(DisplayName = "Fail to deactivate unknown Customer")]
	public void FailDeactivateUnknownCustomerTest()
	{
		var result = ControllerInstance!.DeactivateCustomer(new Guid()).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(404, result.StatusCode);
	}
	
	[Fact(DisplayName = "Fail to activate unknown Customer")]
	public void FailActivateUnknownCustomerTest()
	{
		var result = ControllerInstance!.ActivateCustomer(new Guid()).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(404, result.StatusCode);
	}
	
	
	[Fact(DisplayName = "Fail to deactivate Customer unknown to Zitadel")]
	public void FailDeactivateCustomerTest()
	{
		var customer = new CustomerEntity
		{
			RemoteId = "IAmInvalid",
			DisplayName = "TestCustomer",
			Enabled = true
		};
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();
		
		var guid = entry.Entity.Id;
		var result = ControllerInstance!.DeactivateCustomer(guid).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
	}
	
	[Fact(DisplayName = "Fail to activate Customer unknown to Zitadel")]
	public void FailActivateCustomerTest()
	{
		var customer = new CustomerEntity
		{
			RemoteId = "IAmInvalid",
			DisplayName = "TestCustomer",
			Enabled = true
		};
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();
		
		var guid = entry.Entity.Id;
		var result = ControllerInstance!.ActivateCustomer(guid).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
	}
	
	#endregion

	#region UserCustomerMapping

	[Fact(DisplayName = "Add User to Customer")]
	public void AddUserToCustomerTest()
	{
		using var databaseContext = Fixture.Context;
		
		var zitadelOrgId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("TestCustomer", new List<string>());
		var customer = new CustomerEntity
		{
			RemoteId = zitadelOrgId,
			DisplayName = "TestCustomer"
		};
		databaseContext.Customers.Add(customer);

		var dto = new UserDto
		{
			Username = "FooBar",
			Email = "<EMAIL>",
			FirstName = "Foo",
			LastName = "Bar"
		};
		var zitadelUserId = ZitadelApiClientFactory.GetManagementClient().AddHumanUser(dto, zitadelOrgId);
		databaseContext.Users.Add(new(customer)
		{
			RemoteId = zitadelUserId,
			DisplayName = "FooBar",
			Email = "<EMAIL>",
			FirstName = "First",
			LastName = "Family",
		});

		zitadelOrgId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("ImportantCompany", new List<string>());
		databaseContext.Customers.Add(new CustomerEntity
		{
			RemoteId = zitadelOrgId,
			DisplayName = "ImportantCompany"
		});
		databaseContext.SaveChanges();

		var userInstance = databaseContext.Users.First(user => user.DisplayName == dto.Username);
		var customerInstance = databaseContext.Customers.First(customerEntity => customerEntity.RemoteId == zitadelOrgId);
		var result = ControllerInstance!.AddUserToCustomer(userInstance.Id.ToString(), customerInstance.Id.ToString()).Result as ObjectResult;

		// Reload instance
		userInstance = databaseContext.Users.Include(user => user.Customers).First(user => user.DisplayName == dto.Username);

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(200, result.StatusCode);

		Assert.NotNull(userInstance);
		Assert.Contains(userInstance.Customers, customerEntity => customerEntity.Id == customerInstance.Id);
	}

	[Fact(DisplayName = "Fail to add user to customer because of no user Id")]
	public void MissingUserIdMappingTest()
	{
		var result = ControllerInstance!.AddUserToCustomer("", Guid.NewGuid().ToString()).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
	}

	[Fact(DisplayName = "Fail to add user to customer because of no customer Id")]
	public void MissingCustomerIdMappingTest()
	{
		var result = ControllerInstance!.AddUserToCustomer(Guid.NewGuid().ToString(), "").Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
	}

	[Fact(DisplayName = "Fail to add user to customer because of no user Id and no customer Id")]
	public void MissingUserIdAndCustomerIdMappingTest()
	{
		var result = ControllerInstance!.AddUserToCustomer("", "").Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
	}

	[Fact(DisplayName = "Fail to add user to customer because of invalid user Id")]
	public void InvalidUserIdMappingTest()
	{
		var result = ControllerInstance!.AddUserToCustomer("IAmInvalid", Guid.NewGuid().ToString()).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
	}

	[Fact(DisplayName = "Fail to add user to customer because of invalid customer Id")]
	public void InvalidCustomerIdMappingTest()
	{
		var result = ControllerInstance!.AddUserToCustomer(Guid.NewGuid().ToString(), "IAmInvalid").Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);
	}

	[Fact(DisplayName = "Fail to add user to customer because of missing user")]
	public void MissingUserMappingTest()
	{
		using var databaseContext = Fixture.Context;
		
		var zitadelOrgId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("TestCustomer", new List<string>());
		var customer = new CustomerEntity
		{
			RemoteId = zitadelOrgId,
			DisplayName = "TestCustomer"
		};
		databaseContext.Customers.Add(customer);
		databaseContext.SaveChanges();

		var customerId = databaseContext.Customers.First().Id.ToString();
		var result = ControllerInstance!.AddUserToCustomer(Guid.NewGuid().ToString(), customerId).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(404, result.StatusCode);
	}

	[Fact(DisplayName = "Fail to add user to customer because of missing user")]
	public void MissingCustomerMappingTest()
	{
		using var databaseContext = Fixture.Context;
		
		var zitadelOrgId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("TestCustomer", new List<string>());
		var customer = new CustomerEntity
		{
			RemoteId = zitadelOrgId,
			DisplayName = "TestCustomer"
		};
		databaseContext.Customers.Add(customer);

		var dto = new UserDto
		{
			Username = "FooBar",
			Email = "<EMAIL>",
			FirstName = "Foo",
			LastName = "Bar"
		};
		var zitadelUserId = ZitadelApiClientFactory.GetManagementClient().AddHumanUser(dto, zitadelOrgId);
		databaseContext.Users.Add(new(customer)
		{
			RemoteId = zitadelUserId,
			DisplayName = "FooBar",
			Email = "<EMAIL>",
			FirstName = "First",
			LastName = "Family",
		});
		databaseContext.SaveChanges();

		var userId = databaseContext.Users.First().Id.ToString();
		var result = ControllerInstance!.AddUserToCustomer(userId, Guid.NewGuid().ToString()).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(404, result.StatusCode);
	}

	[Fact(DisplayName = "Fail to add user to customer because of user not present in Zitadel")]
	public void MissingZitadelUserMappingTest()
	{
		using var databaseContext = Fixture.Context;
		
		var zitadelOrgId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("TestCustomer", new List<string>());
		var customer = new CustomerEntity
		{
			RemoteId = zitadelOrgId,
			DisplayName = "TestCustomer"
		};
		databaseContext.Customers.Add(customer);

		databaseContext.Users.Add(new(customer)
		{
			RemoteId = "IAmInvalid",
			DisplayName = "FooBar",
			Email = "<EMAIL>",
			FirstName = "First",
			LastName = "Family",
		});

		zitadelOrgId = ZitadelApiClientFactory.GetManagementClient().AddOrganisation("ImportantCompany", new List<string>());
		databaseContext.Customers.Add(new CustomerEntity
		{
			RemoteId = zitadelOrgId,
			DisplayName = "ImportantCompany"
		});
		databaseContext.SaveChanges();

		var userInstance = databaseContext.Users
			.Include(userEntity => userEntity.Customers)
			.First();
		var customerInstance = databaseContext.Customers.First(customerEntity => customerEntity.RemoteId == zitadelOrgId);
		var result = ControllerInstance!.AddUserToCustomer(userInstance.Id.ToString(), customerInstance.Id.ToString()).Result as ObjectResult;

		Assert.NotNull(result);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		Assert.Equal(400, result.StatusCode);

		Assert.NotNull(userInstance);
		Assert.DoesNotContain(userInstance.Customers, customerEntity => customerEntity.Id == customerInstance.Id);
	}

	#endregion

	#endregion
}