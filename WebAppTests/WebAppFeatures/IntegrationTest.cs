using System.Diagnostics;
using System.Security.Claims;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.ZitadelApiInterface;
using Levelbuild.Domain.WebAppTests.Mocks.AuthenticationService;
using Levelbuild.Domain.WebAppTests.Mocks.HttpContextAccessor;
using Levelbuild.Domain.WebAppTests.Mocks.ZitadelApi;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Customer;
using Levelbuild.Entities.Features.User;
using Levelbuild.Frontend.WebApp.Features.Auth.Services;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Services;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.Viewer.Email.Services;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Services.Files;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Hosting.Internal;
using StackExchange.Redis;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures;

public abstract class IntegrationTest : IDisposable
{
	protected readonly DatabaseFixture Fixture;
	
	protected readonly ILogManager LogManager;
	
	protected readonly IHttpContextAccessor HttpContextAccessor;
	
	protected readonly IZitadelApiClientFactory ZitadelApiClientFactory;
	
	protected readonly UserManager UserManager;
	
	protected readonly IExtendedStringLocalizerFactory LocalizerFactory;
	
	protected readonly IVersionReader VersionReader;
	
	protected readonly IFileCachingService FileCachingService;
	
	protected readonly IDeepZoomHelperService DeepZoomHelperService;
	
	protected readonly IThumbnailHelperService ThumbnailHelperService;

	protected readonly IRedisAccessService RedisAccessService;
	
	protected readonly IEmailReader EmailReader;
	
	protected readonly IServiceScopeFactory Scopes;
	
	private bool _disposed = false;
	
	/// <summary>
	/// Constructor.
	/// 
	/// The _controllerInstance field must be assigned in the constructor of the derived test class.
	/// </summary>
	/// <param name="fixture"></param>
	/// <param name="additionalServiceInjection"></param>
	/// <param name="initStorage"></param>
	protected IntegrationTest(DatabaseFixture fixture, Action<IServiceCollection>? additionalServiceInjection = null, bool initStorage = false)
	{
		Fixture = fixture;
		
		Fixture.Services.AddSingleton<ILogManager, LogManager>();
		Fixture.Services.AddSingleton<IAuthenticationService, MockAuthenticationService>();
		Fixture.Services.AddSingleton<IHttpContextAccessor, MockHttpContextAccessor>();
		Fixture.Services.AddSingleton<MockZitadelDatabase>();
		Fixture.Services.AddSingleton<IZitadelApiClientFactory, MockZitadelApiClientFactory>();
		
		Fixture.Services.AddSingleton(Fixture.Config);
		Fixture.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisWriteConnection, ConnectionMultiplexer.Connect(Fixture.Config.GetConnectionString("Redis")!));
		Fixture.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisReadConnection, ConnectionMultiplexer.Connect(Fixture.Config.GetConnectionString("Redis")!));
		Fixture.Services.AddSingleton<IRedisAccessService, RedisAccessService>();
		Fixture.Services.AddSingleton<UserImpersonationCache>();
		Fixture.Services.AddScoped<UserManager>();
		
		Fixture.Services.AddSingleton<LogManager>();
		
		Fixture.Services.AddSingleton<IHostEnvironment>(new HostingEnvironment() { EnvironmentName = Environments.Development });
		Fixture.Services.AddSingleton<ITranslationService, TranslationService>();
		Fixture.Services.AddSingleton<StringLocalizerCache>();
		Fixture.Services.AddSingleton<IExtendedStringLocalizerFactory, StringLocalizerFactory>();
		
		Fixture.Services.AddSingleton<IVersionReader, VersionReader>();
		
		Fixture.Services.AddSingleton<IThumbnailMicroservice, ThumbnailMicroservice>();
		Fixture.Services.AddSingleton<IThumbnailHelperService, ThumbnailHelperService>();
		Fixture.Services.AddSingleton<IFileCachingService, FileCachingService>();
		Fixture.Services.AddSingleton<IDeepZoomMicroservice, DeepZoomMicroservice>();
		Fixture.Services.AddSingleton<IDeepZoomHelperService, DeepZoomHelperService>();
		
		Fixture.Services.AddSingleton<IEmailReader, EmailReader>();
		Fixture.Services.AddSingleton<ActivitySource>(new ActivitySource("Test"));
		
		additionalServiceInjection?.Invoke(Fixture.Services);
		
		if(initStorage)
			Fixture.InitStorageDataBase();
		
		Fixture.BuildServices();
		
		LogManager = Fixture.ServiceProvider.GetRequiredService<ILogManager>();
		HttpContextAccessor = (MockHttpContextAccessor) Fixture.ServiceProvider.GetRequiredService<IHttpContextAccessor>();
		ZitadelApiClientFactory = (MockZitadelApiClientFactory) Fixture.ServiceProvider.GetRequiredService<IZitadelApiClientFactory>();
		UserManager = Fixture.ServiceProvider.GetRequiredService<UserManager>();
		LocalizerFactory = Fixture.ServiceProvider.GetRequiredService<IExtendedStringLocalizerFactory>();
		VersionReader = Fixture.ServiceProvider.GetRequiredService<IVersionReader>();
		FileCachingService = Fixture.ServiceProvider.GetRequiredService<IFileCachingService>();
		DeepZoomHelperService = Fixture.ServiceProvider.GetRequiredService<IDeepZoomHelperService>();
		ThumbnailHelperService = Fixture.ServiceProvider.GetRequiredService<IThumbnailHelperService>();
		RedisAccessService = Fixture.ServiceProvider.GetRequiredService<IRedisAccessService>();
		EmailReader = Fixture.ServiceProvider.GetRequiredService<IEmailReader>();
		Scopes = Fixture.ServiceProvider.GetRequiredService<IServiceScopeFactory>();
		
		// Give the UserManager something to work with.
		PrepareUsers();
		SetAdminContext();
	}

	#region Auth Mocking

	private void PrepareUsers()
	{
		var customer = new CustomerEntity()
		{
			RemoteId = "1",
			DisplayName = "MockCustomer"
		};
		
		using var databaseContext = Fixture.Context;
		databaseContext.Customers.Add(customer);
		
		databaseContext.Users.Add(new UserEntity(customer)
		{
			RemoteId = "1",
			DisplayName = "MockUser",
			Email = "<EMAIL>",
			FirstName = "First",
			LastName = "Family",
		});

		databaseContext.SaveChanges();
	}

	// ReSharper disable once MemberCanBePrivate.Global
	public void SetAdminContext()
	{
		if (HttpContextAccessor.HttpContext == null)
			return;
		
		var claims = new List<Claim>()
		{
			new(ClaimTypes.NameIdentifier, "1"),
			new("http://schemas.microsoft.com/ws/2008/06/identity/claims/role", "admin"),
			new("urn:zitadel:iam:user:resourceowner:id", "1")
		};
		var identity = new ClaimsIdentity(claims, "MockAuthType");
		
		HttpContextAccessor.HttpContext.User = new ClaimsPrincipal(identity);
	}

	public void SetUserContext()
	{
		if (HttpContextAccessor.HttpContext == null)
			return;
		
		var claims = new List<Claim>()
		{
			new(ClaimTypes.NameIdentifier, "2"),
			new("http://schemas.microsoft.com/ws/2008/06/identity/claims/role", "user"),
			new("urn:zitadel:iam:user:resourceowner:id", "1")
		};
		var identity = new ClaimsIdentity(claims, "MockAuthType");

		HttpContextAccessor.HttpContext.User = new ClaimsPrincipal(identity);
	}
	
	//TODO: add back for unauthorized tests.
	/*public void ClearUserContext()
	{
		var claims = new List<Claim>()
		{
			new(ClaimTypes.NameIdentifier, string.Empty),
			new("http://schemas.microsoft.com/ws/2008/06/identity/claims/role", string.Empty),
			new("urn:zitadel:iam:user:resourceowner:id", string.Empty)
		};
		var identity = new ClaimsIdentity(claims, "MockAuthType");
		
		_httpContextAccessor.HttpContext.User = new ClaimsPrincipal(identity);
		_userManager.ClearCache();
	}*/

	#endregion

	public void Dispose()
	{
		if(_disposed)
			return;
		
		Fixture.CleanUp();
		GC.SuppressFinalize(this);
		
		_disposed = true;
	}
}