using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Logging;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.LoggerConfig;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Controllers;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Hosting.Internal;
using Serilog.Events;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.LoggerConfig;

[ExcludeFromCodeCoverage]
public class PostgresLoggerConfigControllerTests(PostgresDatabaseFixture fixture) : LoggerConfigControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
public class SqlServerLoggerConfigControllerTests : LoggerConfigControllerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerLoggerConfigControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class LoggerConfigControllerTests : AdminControllerTest<LoggerConfigController, LoggerConfigEntity, LoggerConfigDto>
{
	private readonly LogManager _logManager;
	
	private static readonly Action<IServiceCollection> ServiceInjection = services =>
	{
		services.AddSingleton<LogManager>();
		services.AddSingleton<IHostEnvironment>(new HostingEnvironment() { EnvironmentName = Environments.Development });
		services.AddSingleton<ITranslationService, TranslationService>();
		services.AddSingleton<IExtendedStringLocalizerFactory, StringLocalizerFactory>();
	};

	#region Test Data Properties

	protected override LoggerConfigDto CreateDto => new()
	{
		LoggerSource = "One.Great.Domain.Name",
		Level = LogEventLevel.Warning,
		LogToFile = false,
		IsActive = true
	};

	protected override LoggerConfigDto InvalidCreateDto => new()
	{
		LoggerSource = "",
	};

	protected override LoggerConfigDto InvalidUpdateDto => new()
	{
		Id = Guid.Empty,
		LoggerSource = "One.Better.Domain.Name",
		Level = LogEventLevel.Error
	};

	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "LoggerSource",
				Direction = SortDirection.Asc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "LoggerSource",
				Direction = SortDirection.Asc
			}
		},
		Limit = 2,
		Offset = 1
	};

	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "Level",
				Direction = SortDirection.Desc
			},
			new()
			{
				OrderColumn = "IsActive",
				Direction = SortDirection.Asc
			}
		}
	};
	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>()
		{
			new()
			{
				FilterColumn = "LoggerSource",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "Logger3"
			}
		}
	};
	
	#endregion

	protected LoggerConfigControllerTests(DatabaseFixture fixture) : base(fixture, ServiceInjection)
	{
		_logManager = Fixture.ServiceProvider.GetRequiredService<LogManager>();
		var localizerFactory = Fixture.ServiceProvider.GetRequiredService<IExtendedStringLocalizerFactory>();
		ControllerInstance = new LoggerConfigController(_logManager, Fixture.ContextFactory, UserManager, localizerFactory, VersionReader);
	}
	
	#region Data Preparation

	protected override LoggerConfigEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.LoggerConfigs.Add(new LoggerConfigEntity
		{
			LoggerSource = "One.Great.Domain.Name",
			SourceIsGroup = false,
			Level = LogEventLevel.Fatal,
			LogToFile = false,
			LogFilePath = null,
			IsActive = true
		});
		databaseContext.SaveChanges();

		return entry.Entity;
	}

	protected override LoggerConfigDto GetUpdateDto(LoggerConfigEntity entity)
	{
		var dto = entity.ToDto();
		dto.LoggerSource = "One.Better.Domain.Name";
		dto.Level = LogEventLevel.Error;
		dto.LogFilePath = "new/logfilepath";

		return dto;
	}

	protected override async Task PrepareQueryDataAsync()
	{
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000001"),
			Level = LogEventLevel.Error,
			LoggerSource = "Logger1",
			IsActive = true
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000002"),
			Level = LogEventLevel.Warning,
			LoggerSource = "Logger2",
			IsActive = true
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000003"),
			Level = LogEventLevel.Fatal,
			LoggerSource = "Logger3",
			IsActive = false
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000004"),
			Level = LogEventLevel.Information,
			LoggerSource = "Logger4",
			IsActive = true
		});

		await using var databaseContext = Fixture.Context;
		foreach (var dto in QueryData)
		{
			databaseContext.LoggerConfigs.Add(LoggerConfigEntity.FromDto(dto, null, databaseContext));
		}

		await databaseContext.SaveChangesAsync();
	}

	#endregion

	#region Assertions

	protected override void AssertIsAsExpected(LoggerConfigDto expected, LoggerConfigDto actual)
	{
		Assert.Equal(expected.LoggerSource, actual.LoggerSource);
		Assert.Equal(expected.Level, actual.Level);
		Assert.Equal(expected.LogToFile, actual.LogToFile);
		Assert.Equal(expected.LogFilePath, actual.LogFilePath);
		Assert.Equal(expected.IsActive, actual.IsActive);
	}

	protected override void AssertExists(LoggerConfigDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.LoggerConfigs.Any(config => config.LoggerSource == dto.LoggerSource));
	}
	
	protected override void AssertDoesNotExist(LoggerConfigDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.LoggerConfigs.All(config => config.LoggerSource != dto.LoggerSource));
	}

	protected override bool DeleteSuccessful(LoggerConfigEntity entity)
	{
		using var databaseContext = Fixture.Context;
		return databaseContext.LoggerConfigs.Find(entity.Id) == null;
	}

	protected override bool CheckQueryResult(ConfigQueryResultDto<LoggerConfigDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.LoggerSource);
		var expectedList = new List<string>();
		for(var i = offset; i < count && (expectedList.Count < limit || limit == 0); i++)
		{
			expectedList.Add(QueryData[i].LoggerSource!);
		}
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (limit > 0 && queryResultDto.Rows.Count > limit)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i] != mutatedNamesList[i])
				return false;
		}

		return true;
	}

	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<LoggerConfigDto> queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.LoggerSource);
		var expectedList = QueryData.OrderByDescending(dataSource => dataSource.Level).ThenBy(dataSource => dataSource.IsActive).ToList();
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i].LoggerSource != mutatedNamesList[i])
				return false;
		}

		return true;
	}
	
	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<LoggerConfigDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;
		
		return queryResultDto.Rows.FirstOrDefault()?.LoggerSource == QueryData[2].LoggerSource;
	}
	
	#endregion

	#region Tests

	#region Update

	[Trait("Category", "Logger Configuration Tests")]
	[Fact(DisplayName = "Check Impact of Logger Group Config Update on Loggers")]
	public async Task UpdateLoggersGroupTextImpactAsync()
	{
		await using var databaseContext = Fixture.Context;
		databaseContext.LoggerConfigs.Add(new LoggerConfigEntity
		{
			LoggerSource = "LoggerTestGroup",
			SourceIsGroup = true,
			Level = LogEventLevel.Fatal,
			LogToFile = false,
			LogFilePath = null,
			IsActive = true
		});
		await databaseContext.SaveChangesAsync();
		
		var config = databaseContext.LoggerConfigs.First();
		var updateData = config.ToDto();
		updateData.Level = LogEventLevel.Error;
		
		_logManager.GetLoggerForClass<LogManagerTests>(LogEventLevel.Debug, "LoggerTestGroup");
		_logManager.GetLoggerForClass<LoggerConfigControllerTests>(LogEventLevel.Verbose, "LoggerTestGroup");
		_logManager.GetLoggerForClass<LoggerConfigController>();

		var result = await ControllerInstance!.Update(config.Id, updateData);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		var resultConfig = (objectResult.Value as FrontendResponse<LoggerConfigDto>)?.Data;
		Assert.NotNull(resultConfig);
		AssertIsAsExpected(updateData, resultConfig);
		Assert.Equal(LogEventLevel.Error, _logManager.GetLogLevel(typeof(LogManagerTests).FullName!));
		Assert.Equal(LogEventLevel.Error, _logManager.GetLogLevel(typeof(LoggerConfigControllerTests).FullName!));
		Assert.NotEqual(LogEventLevel.Error, _logManager.GetLogLevel(typeof(LoggerConfigController).FullName!));
	}

	[Trait("Category", "Logger Configuration Tests")]
	[Fact(DisplayName = "Check Impact of Logger Group Config Update on Loggers when deactivating the config")]
	public async Task UpdateLoggersGroupDeactivateImpact()
	{
		await using var databaseContext = Fixture.Context;
		databaseContext.LoggerConfigs.Add(new LoggerConfigEntity
		{
			LoggerSource = "LoggerTestGroup",
			SourceIsGroup = true,
			Level = LogEventLevel.Fatal,
			LogToFile = false,
			LogFilePath = null,
			IsActive = true,
		});
		await databaseContext.SaveChangesAsync();
		
		var config = databaseContext.LoggerConfigs.First();
		var updateData = config.ToDto();
		updateData.Level = LogEventLevel.Error;
		updateData.IsActive = false;
		
		_logManager.GetLoggerForClass<LogManagerTests>(LogEventLevel.Debug, "LoggerTestGroup");
		_logManager.GetLoggerForClass<LoggerConfigControllerTests>(LogEventLevel.Verbose, "LoggerTestGroup");
		_logManager.GetLoggerForClass<LoggerConfigController>();

		var result = await ControllerInstance!.Update(config.Id, updateData);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		var resultConfig = (objectResult.Value as FrontendResponse<LoggerConfigDto>)?.Data;
		Assert.NotNull(resultConfig);
		AssertIsAsExpected(updateData, resultConfig);
		Assert.NotEqual(LogEventLevel.Error, _logManager.GetLogLevel(typeof(LogManagerTests).FullName!));
		Assert.NotEqual(LogEventLevel.Error, _logManager.GetLogLevel(typeof(LoggerConfigControllerTests).FullName!));
		Assert.NotEqual(LogEventLevel.Error, _logManager.GetLogLevel(typeof(LoggerConfigController).FullName!));
	}

	#endregion

	#endregion
}