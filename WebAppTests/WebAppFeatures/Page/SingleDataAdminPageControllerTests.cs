using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.Page.SingleData;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.Page;

[ExcludeFromCodeCoverage]
[Collection("PostgresDatabaseCollection")]
public class PostgresSingleDataAdminPageControllerTests(PostgresDatabaseFixture fixture)
	: SingleDataAdminPageControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
[Collection("SqlServerDatabaseCollection")]
public class SqlServerSingleDataPageControllerTests : SingleDataPageControllerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerSingleDataPageControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class SingleDataAdminPageControllerTests(DatabaseFixture fixture) : AdminPageControllerTests(fixture)
{
	#region Test Data Properties

	protected override SingleDataPageDto CreateDto
	{
		get
		{
			var pageDto = base.CreateDto;
			pageDto = new SingleDataPageDto(pageDto)
			{
				Type = PageType.SingleData
			};
			
			var localizer = ControllerInstance!.StringLocalizerFactory.Create("PageType", "");
			pageDto.TypeName = localizer[pageDto.Type.ToString()];

			return (SingleDataPageDto)pageDto;
		}
	}

	protected override SingleDataPageDto InvalidCreateDto
	{
		get
		{
			var pageDto = base.InvalidCreateDto;
			return new SingleDataPageDto(pageDto)
			{
				Type = PageType.SingleData,
			};
		}
	}

	protected override SingleDataPageDto InvalidUpdateDto
	{
		get
		{
			var pageDto = base.InvalidUpdateDto;
			return new SingleDataPageDto(pageDto)
			{
				Type = PageType.SingleData,
			};
		}
	}
	
	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "SinglePage2"
			}
		}
	};
	
	#endregion

	#region Data Preparation

	protected override SingleDataPageEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.SingleDataPages.Add(SingleDataPageEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();

		entry.Entity.SetStringLocalizerFactory(ControllerInstance!.StringLocalizerFactory);
		
		return entry.Entity;
	}
	
	protected override SingleDataPageDto GetUpdateDto(PageEntity entity)
	{
		var dto = ((SingleDataPageEntity)entity).ToDto();
		UpdateDtoAsync(dto).WaitWithUnwrappedExceptions();
		return dto;
	}

	#endregion

	#region Assertions

	protected override void AssertExists(PageDto dto)
	{
		base.AssertExists(dto);
		
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.SingleDataPages.Any(page => page.Name == dto.Name));
	}
	
	protected override void AssertDoesNotExist(PageDto dto)
	{
		base.AssertDoesNotExist(dto);
		
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.SingleDataPages.All(page => page.Name != dto.Name));
	}
	
	protected override bool DeleteSuccessful(PageEntity entity)
	{
		if (!base.DeleteSuccessful(entity))
			return false;
		
		using var databaseContext = Fixture.Context;
		return databaseContext.SingleDataPages.Find(entity.Id) == null;
	}
	
	#endregion
}