using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.PageView;
using Levelbuild.Entities.Features.PageView.ListView;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PageView;

[ExcludeFromCodeCoverage]
[Collection("PostgresDatabaseCollection")]
public class PostgresListViewControllerTests(PostgresDatabaseFixture fixture) : ListViewControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
[Collection("SqlServerDatabaseCollection")]
public class SqlServerListViewControllerTests : ListViewControllerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerListViewControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class ListViewControllerTests(DatabaseFixture fixture) : PageViewControllerTests(fixture)
{
	#region Test Data Properties

	protected override ListViewDto CreateDto
	{
		get
		{
			var listViewDto = base.CreateDto;
			return new ListViewDto(listViewDto)
			{
				Type = PageViewType.List,
				TypeName = "[List]",
				Created = DateTime.Now.ToUniversalTime(),
				CreatedBy = UserManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().DisplayName,
				Icon = "table-list"
			};
		}
	}

	protected override ListViewDto InvalidCreateDto
	{
		get
		{
			var listViewDto = base.InvalidCreateDto;
			return new ListViewDto(listViewDto)
			{
				Type = PageViewType.List,
			};
		}
	}

	protected override ListViewDto InvalidUpdateDto
	{
		get
		{
			var listViewDto = base.InvalidUpdateDto;
			return new ListViewDto(listViewDto)
			{
				Type = PageViewType.List,
			};
		}
	}
	
	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "ListView2"
			}
		}
	};
	
	#endregion

	#region Data Preparation

	protected override ListViewEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.ListViews.Add(ListViewEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();
		
		entry.Entity.SetStringLocalizerFactory(ControllerInstance!.StringLocalizerFactory);

		return entry.Entity;
	}
	
	protected override ListViewDto GetUpdateDto(PageViewEntity entity)
	{
		var dto = ((ListViewEntity)entity).ToDto();
		UpdateDto(dto);
		return dto;
	}

	#endregion

	#region Assertions

	protected override void AssertExists(PageViewDto dto)
	{
		base.AssertExists(dto);
		
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.ListViews.Any(list => list.Name == dto.Name));
	}
	
	protected override void AssertDoesNotExist(PageViewDto dto)
	{
		base.AssertDoesNotExist(dto);
		
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.ListViews.All(list => list.Name != dto.Name));
	}
	
	protected override bool DeleteSuccessful(PageViewEntity entity)
	{
		if (!base.DeleteSuccessful(entity))
			return false;
		
		using var databaseContext = Fixture.Context;
		return databaseContext.ListViews.Find(entity.Id) == null;
	}
	
	#endregion
}