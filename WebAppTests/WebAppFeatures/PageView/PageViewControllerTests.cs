using Google.Api.Gax;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.PageView;
using Levelbuild.Entities.Features.PageView.GalleryView;
using Levelbuild.Entities.Features.PageView.ListView;
using Levelbuild.Frontend.WebApp.Features.PageView.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Hosting.Internal;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PageView;

[ExcludeFromCodeCoverage]
public abstract class PageViewControllerTests : AdminControllerTest<PageViewController, PageViewEntity, PageViewDto>
{
	private static readonly Action<IServiceCollection> ServiceInjection = services =>
	{
		services.AddSingleton<IExtendedStringLocalizerFactory, StringLocalizerFactory>();
		services.AddSingleton<IHostEnvironment>(new HostingEnvironment() { EnvironmentName = Environments.Development });
		services.AddSingleton<ITranslationService, TranslationService>();
		services.AddLocalization();
		services.ConfigureOptions<CustomRequestLocalizationOptions>();
	};
	
	#region Test Data Properties
	
	protected override PageViewDto CreateDto
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
			var page = EntityCreation.CreatePage(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
			
			return new()
			{
				Name = "Baba PageView",
				PageId = page.Id,
				Description = "Sheesh Brudi gib dir mal diese cringy View"
			};
		}
	}
	
	protected override PageViewDto InvalidCreateDto => new()
	{
		Name = null,
		TypeName = "Typ Schlumpf"
	};
	
	protected override PageViewDto InvalidUpdateDto => new()
	{
		Id = Guid.Empty,
		Name = null,
		TypeName = string.Empty
	};
	
	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Limit = 3,
		Offset = 1,
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Desc
			}
		}
	};
	
	#endregion

	protected PageViewControllerTests(DatabaseFixture fixture) : base(fixture, ServiceInjection)
	{
		Fixture.InitStorageDataBase();
		
		var localizerFactory = Fixture.ServiceProvider.GetRequiredService<IExtendedStringLocalizerFactory>();
		ControllerInstance = new PageViewController(LogManager, Fixture.ContextFactory, UserManager, localizerFactory, VersionReader);
	}
	
	#region Data Preparation

	protected void UpdateDto(PageViewDto pageView)
	{
		pageView.Name = "Foo";
		pageView.LastModified = DateTime.Now.ToUniversalTime();
		pageView.LastModifiedBy = UserManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().DisplayName;
	}
	
	protected override async Task PrepareQueryDataAsync()
	{
		using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		var page = EntityCreation.CreatePage(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
		var titleField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														currentCustomer.Id, "Title");
		var subtitleField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														   currentCustomer.Id, "Subtitle");
		
		QueryData.Add(new GalleryViewDto()
		{
			Name = "GalleryView1",
			PageId = page.Id,
			Description = "Schlumpfbeeren ballern!",
			Type = PageViewType.Gallery,
			TitleFieldId = titleField.Id,
			SubtitleFieldId = subtitleField.Id
		});
		
		
		QueryData.Add(new GalleryViewDto()
		{
			Name = "GalleryView2",
			PageId = page.Id,
			Description = "test2",
			Type = PageViewType.Gallery,
			TitleFieldId = titleField.Id,
			SubtitleFieldId = subtitleField.Id
		});
		
		QueryData.Add(new GalleryViewDto()
		{
			Name = "GalleryView3",
			PageId = page.Id,
			Description = "test3",
			Type = PageViewType.Gallery,
			TitleFieldId = titleField.Id,
			SubtitleFieldId = subtitleField.Id
		});
		
		QueryData.Add(new ListViewDto()
		{
			Name = "ListView1",
			PageId = page.Id,
			Description = "Schlumpfbeeren ballern!",
			Type = PageViewType.List
		});
		
		QueryData.Add(new ListViewDto()
		{
			Name = "ListView2",
			PageId = page.Id,
			Description = "test2",
			Type = PageViewType.List
		});
		
		QueryData.Add(new ListViewDto()
		{
			Name = "ListView3",
			PageId = page.Id,
			Description = "test3",
			Type = PageViewType.List
		});
		
		foreach (PageViewDto dto in QueryData)
		{
			switch (dto.Type)
			{
				case PageViewType.List:
					databaseContext.ListViews.Add(ListViewEntity.FromDto((ListViewDto)dto, null, databaseContext));
					break;
				
				case PageViewType.Gallery:
					databaseContext.GalleryViews.Add(GalleryViewEntity.FromDto((GalleryViewDto)dto, null, databaseContext));
					break;
			}
		}
		
		await databaseContext.SaveChangesAsync();
	}
		
	#endregion
	
	#region Assertions
	
	protected override void AssertIsAsExpected(PageViewDto expected, PageViewDto actual)
	{
		Assert.Equal(expected.Name, actual.Name);
		Assert.False((actual.Created!.Value - expected.Created!.Value).TotalSeconds < -1 || actual.Created >= DateTime.Now.ToUniversalTime());
		Assert.Equal(expected.CreatedBy, actual.CreatedBy);
		Assert.Equal(expected.Description, actual.Description);
		Assert.False(expected.Created >= actual.LastModified || actual.LastModified >= DateTime.Now.ToUniversalTime());
		Assert.Equal(expected.LastModifiedBy, actual.LastModifiedBy);
		Assert.Equal(expected.Icon, actual.Icon);
		Assert.Equal(expected.TypeName, actual.TypeName);
		Assert.Equal(expected.Type, actual.Type);
		Assert.Equal(expected.GetType(), actual.GetType());
		Assert.Equal(expected.PageId, actual.PageId);
	}
	
	protected override void AssertExists(PageViewDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.PageViews.Any(pageView => pageView.Name == dto.Name));
	}
	
	protected override void AssertDoesNotExist(PageViewDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.PageViews.All(pageView => pageView.Name != dto.Name));
	}
	
	protected override bool DeleteSuccessful(PageViewEntity entity)
	{
		using var databaseContext = Fixture.Context;
		return databaseContext.Pages.Find(entity.Id) == null;
	}
	
	protected override bool CheckQueryResult(ConfigQueryResultDto<PageViewDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.Name);
		var expectedList = new List<string>();
		for(var i = offset; i < count && i <= (count - limit); i++)
		{
			expectedList.Add(QueryData[i].Name!);
		}
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != (count - limit))
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i] != mutatedNamesList[i])
				return false;
		}
		
		return true;
	}
	
	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<PageViewDto> queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.Name);
		var expectedList = QueryData.OrderByDescending(pageView => pageView.Name).ThenBy(pageView => pageView.Description).ToList();
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i].Name != mutatedNamesList[i])
				return false;
		}
		
		return true;
	}
	
	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<PageViewDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;
		
		int queryIndex = 0;
		switch (queryResultDto.Rows.FirstOrDefault()?.Type)
		{
			case PageViewType.List:
				queryIndex = 4;
				break;
			
			case PageViewType.Gallery:
				queryIndex = 1;
				break;
		}
		
		return QueryData[queryIndex].Name == queryResultDto.Rows.FirstOrDefault()?.Name;
	}
	
	#endregion
}