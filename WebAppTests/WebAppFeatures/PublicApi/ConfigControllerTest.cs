using System.Text.Json;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities;
using Levelbuild.Entities.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.PublicApi;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PublicApi;

[ExcludeFromCodeCoverage]
public abstract class ConfigControllerTest<TEntity, TDto> : PublicApiControllerTest<ConfigController>
	where TEntity : PersistentEntity<TEntity>, IConvertibleEntity<TDto>
	where TDto : EntityDto
{
	protected readonly JsonSerializerOptions JsonOptions = new()
	{
		PropertyNameCaseInsensitive = true
	};
	
	protected readonly IList<TDto> QueryData = new List<TDto>();
	
	#region Test Data Properties
	
	/// <summary>
	/// Contains the DTO used to create an entity.
	/// </summary>
	protected abstract TDto CreateDto { get; }
	
	#endregion
	
	#region Assertions
	
	/// <summary>
	/// Checks if the resulting DTO is as expected.
	/// </summary>
	/// <param name="expected"></param>
	/// <param name="actual"></param>
	/// <returns>True if everything is as expected.</returns>
	protected abstract void AssertIsAsExpected(TDto expected, TDto actual);
	
	/// <summary>
	/// Compares the result of a query request (including optional limit & offset) with the expected result of said query.
	/// </summary>
	/// <param name="queryResult"></param>
	/// <returns>True if the query's result matches the expectation.</returns>
	protected abstract void CheckQueryResult(IList<TDto> queryResult);
	
	#endregion
	
	
	/// <summary>
	/// Constructor.
	/// 
	/// The _controllerInstance field must be assigned in the constructor of the derived test class.
	/// </summary>
	/// <param name="fixture"></param>
	/// <param name="additionalServiceInjection"></param>
	/// <param name="initStorage"></param>
	protected ConfigControllerTest(DatabaseFixture fixture, Action<IServiceCollection>? additionalServiceInjection = null, bool initStorage = false) : base(
		fixture, additionalServiceInjection, initStorage)
	{
	}
	
	#region DataPreparation
	
	/// <summary>
	/// Creates & saves a single entity.
	/// </summary>
	/// <returns>The entity.</returns>
	protected abstract TEntity PrepareSingleEntity();
	
	/// <summary>
	/// Creates & saves multiple entities used for query tests.
	/// </summary>
	protected abstract void PrepareQueryData();
	
	#endregion
	
	#region Tests
	
	[Fact(DisplayName = "Fail to Get a non existing Entity")]
	public async Task GetNonExistingTest()
	{
		var result = await ControllerInstance!.Get(Guid.Empty);
		var error = ((result.Result as ObjectResult)?.Value as PublicApiResponse)?.Error;
		
		Assert.NotNull(result);
		Assert.Equal(404, error!.Value.ErrorCode);
		Assert.NotEmpty(error.Value.ErrorMessage);
	}
	
	#endregion
}