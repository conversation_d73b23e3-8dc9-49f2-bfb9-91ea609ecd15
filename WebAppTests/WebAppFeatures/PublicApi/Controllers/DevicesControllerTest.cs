using System.Text.Json;
using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Device;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Device;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PublicApi.Controllers;

[ExcludeFromCodeCoverage]
public class PostgresDevicesControllerTests(PostgresDatabaseFixture fixture) : DevicesControllerTest(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class DevicesControllerTest : MonomorphicConfigControllerTest<DeviceEntity, DeviceDto>
{
	#region Test Data Properties
	
	protected override DeviceDto CreateDto =>
		new()
		{
			DisplayName = "ExampleDevice",
			Enabled = true,
			Type = DeviceType.ApiClient,
			Format = DeviceFormat.MachineClient,
			UserId = UserManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().Id
		};

	private static DeviceDto InvalidCreateDto => new()
	{
		DisplayName = null
	};
	
	#endregion

	protected DevicesControllerTest(DatabaseFixture fixture) : base(fixture, initStorage: true)
	{
		ControllerInstance = new DevicesController(LogManager, Fixture.ContextFactory, UserManager, VersionReader);
	}
	
	#region DataPreparation
	
	protected override DeviceEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Devices.Add(DeviceEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();
		
		return entry.Entity;
	}

	private DeviceEntity PrepareSingleEntity(Guid userId)
	{
		var dtoWithOtherUser = CreateDto;
		dtoWithOtherUser.UserId = userId;
		
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Devices.Add(DeviceEntity.FromDto(dtoWithOtherUser, null, databaseContext));
		databaseContext.SaveChanges();
		
		return entry.Entity;
	}
	
	private DeviceDto GetUpdateDto()
	{
		return new DeviceDto
		{
			DisplayName = "UpdatedExampleDevice",
			Enabled = false,
			Type = DeviceType.ApiClient,
			Format = DeviceFormat.MachineClient,
			UserId = Guid.Empty
		};
	}
	
	protected override void PrepareQueryData()
	{
		var currentUser = UserManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions();
		QueryData.Add(new DeviceDto
		{
			DisplayName = "ExampleDevice1",
			Enabled = true,
			Type = DeviceType.Android,
			Format = DeviceFormat.Phone,
			UserId = currentUser.Id
		});
		
		QueryData.Add(new DeviceDto
		{
			DisplayName = "ExampleDevice2",
			Enabled = true,
			Type = DeviceType.ApiClient,
			Format = DeviceFormat.MachineClient,
			UserId = currentUser.Id
		});
		
		QueryData.Add(new DeviceDto
		{
			DisplayName = "ExampleDevice3",
			Enabled = true,
			Type = DeviceType.Ios,
			Format = DeviceFormat.Tablet,
			UserId = currentUser.Id
		});
		
		QueryData.Add(new DeviceDto
		{
			DisplayName = "ExampleDevice4",
			Enabled = true,
			Type = DeviceType.Android,
			Format = DeviceFormat.Unknown,
			UserId = currentUser.Id
		});
		
		// create some records in db to query data
		using var databaseContext = Fixture.Context;
		foreach (var dto in QueryData)
		{
			databaseContext.Devices.Add(DeviceEntity.FromDto(dto, null, databaseContext));
		}
		
		databaseContext.SaveChanges();
	}

	
	#endregion
	
	#region Assertions
	
	protected override void AssertIsAsExpected(DeviceDto expected, DeviceDto actual)
	{
		Assert.Equal(expected.DisplayName, actual.DisplayName);
		Assert.Equal(expected.Enabled, actual.Enabled);
		Assert.Equal(expected.ApiKey, actual.ApiKey);
		Assert.Equal(expected.Type, actual.Type);
		Assert.Equal(expected.Format, actual.Format);
	}
	
	private void AssertIsAsExpectedIgnoreApiKey(DeviceDto expected, DeviceDto actual)
	{
		Assert.Equal(expected.DisplayName, actual.DisplayName);
		Assert.Equal(expected.Enabled, actual.Enabled);
		Assert.Equal(expected.Type, actual.Type);
		Assert.Equal(expected.Format, actual.Format);
	}
	
	protected override void CheckQueryResult(IList<DeviceDto> queryResult)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResult.ToList().ConvertAll(config => config.DisplayName);
		mutatedNamesList.Sort();
		var expectedList = new List<string>();
		for(var i = 0; i < count; i++)
		{
			expectedList.Add(QueryData[i].DisplayName!);
		}
		
		Assert.Equal(queryResult.Count, count);
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			Assert.Equal(expectedList[i], mutatedNamesList[i]);
		}
	}
	
	#endregion
	
	#region Test
	
	#region Create
	
	[Fact(DisplayName = "Create Device from DTO")]
	public async Task CreateTest()
	{
		var requestDto = CreateDto;
		var result = await ((DevicesController)ControllerInstance!).Create(requestDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<JsonElement>)?.Data;
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		
		var json = (JsonElement)data;
		var config = json.Deserialize<DeviceDto>(JsonOptions);
		Assert.NotNull(config);
		AssertIsAsExpectedIgnoreApiKey(requestDto, config);
		Assert.Null(config.PasskeyId);
	}
	
	// TODO: Can probably be removed
	[Fact(DisplayName = "Create Device from DTO using one time auth code")]
	public async Task CreateWithAuthCodeTest()
	{
		var requestDto = CreateDto;
		SetAdminContext();
		
		var authCode = await UserManager.GetOrCreateOneTimeAuthCodeAsync();
		
		var result = await ((DevicesController)ControllerInstance!).CreateWithPasskey(requestDto, authCode);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		var data = (objectResult.Value as PublicApiResponse<JsonElement>)?.Data;
		Assert.NotNull(data);
		
		var json = (JsonElement)data;
		var config = json.Deserialize<DeviceDto>(JsonOptions);
		Assert.NotNull(config);
		AssertIsAsExpectedIgnoreApiKey(requestDto, config);
		Assert.NotNull(config.PasskeyId);
		Assert.NotNull(config.PasskeyCredentials);
		Assert.NotNull(config.PasskeyRegistrationUrl);
	}
	
	[Fact(DisplayName = "Fail to create Entity from invalid DTO")]
	public async Task InvalidCreateTest()
	{
		var requestDto = InvalidCreateDto;
		var result = await ((DevicesController)ControllerInstance!).Create(requestDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		
		Assert.NotNull(result);
		Assert.Equal(400, code);
	}
	
	//TODO: add back for unauthorized tests
	/*[Fact(DisplayName = "Fail to Create Device without User")]
	public void UnauthorizedCreateTest()
	{
		var requestDto = _createDto;
		ClearUserContext();
		
		var result = ((DevicesController)_controllerInstance!).Create(requestDto);
		var code = (result.Result as UnauthorizedResult)?.StatusCode;
		
		Assert.NotNull(result);
		Assert.Equal(401, code);
	}*/
	
	#endregion
	
	#region Update
	
	[Fact(DisplayName = "Update Device from DTO")]
	public async Task UpdateTest()
	{
		var device = PrepareSingleEntity();
		var requestDto = GetUpdateDto();
		var result = await ((DevicesController)ControllerInstance!).Update(device.Id, requestDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<JsonElement>)?.Data;
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		
		var json = (JsonElement)data;
		var config = json.Deserialize<DeviceDto>(JsonOptions);
		Assert.NotNull(config);
		AssertIsAsExpectedIgnoreApiKey(requestDto, config);
	}
	
	[Fact(DisplayName = "Fail to update a non existing Device")]
	public async Task UpdateNonExistingTest()
	{
		var requestDto = GetUpdateDto();
		var result = await ((DevicesController)ControllerInstance!).Update(Guid.Empty, requestDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		
		Assert.NotNull(result);
		Assert.Equal(404, code);
	}
	
	//TODO: add back for unauthorized tests
	/*[Fact(DisplayName = "Fail to update Device without User")]
	public void UnauthorizedUpdateTest()
	{
		var device = PrepareSingleEntity();
		var requestDto = GetUpdateDto();
		ClearUserContext();
		
		var result = ((DevicesController)_controllerInstance!).Update(device.Id, requestDto);
		var code = (result.Result as UnauthorizedResult)?.StatusCode;
		
		Assert.NotNull(result);
		Assert.Equal(401, code);
	}*/
	
	[Fact(DisplayName = "Fail to update Device with invalid User")]
	public async Task UpdateInvalidUserTest()
	{
		await using var databaseContext = Fixture.Context;
		var customer = EntityCreation.CreateCustomer(databaseContext, ZitadelApiClientFactory);
		var user = EntityCreation.CreateUser(databaseContext, ZitadelApiClientFactory, customer.DisplayName);
		
		var device = PrepareSingleEntity(user.Id);
		var requestDto = GetUpdateDto();
		
		var result = await ((DevicesController)ControllerInstance!).Update(device.Id, requestDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		
		Assert.NotNull(result);
		Assert.Equal(401, code);
	}
	
	#endregion

	#endregion
}