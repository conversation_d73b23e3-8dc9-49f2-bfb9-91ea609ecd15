using System.Globalization;
using System.Security.Claims;
using System.Text.Json;
using Google.Api.Gax;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.DataStoreContext;
using Levelbuild.Entities.Features.Workflow;
using Levelbuild.Entities.Helpers.DataSource;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PublicApi.Controllers;

[ExcludeFromCodeCoverage]
public class PostgresElementsControllerTests(PostgresDatabaseFixture fixture) : ElementsControllerTest(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class ElementsControllerTest : PublicApiControllerTest<ElementsController>
{
	private readonly JsonSerializerOptions _jsonOptions = new()
	{
		PropertyNameCaseInsensitive = true
	};
	
	#region Test Data Properties

	private static DateTime _utcNow = DateTime.UtcNow;
	private static DateTime _localNow = DateTime.Now;
	private static string _dateTime = _utcNow.ToString(DateTimeFormat.DateTime, CultureInfo.InvariantCulture);
	private static string _dateTimeFixed = _localNow.ToString(DateTimeFormat.DateTimeFixed, CultureInfo.InvariantCulture);
	private static string _time = _utcNow.ToString(DateTimeFormat.Time, CultureInfo.InvariantCulture);
	private static string _timeFixed = _localNow.ToString(DateTimeFormat.TimeFixed, CultureInfo.InvariantCulture);

	private static string _dateFormatTestValue = new DateTime(
		_utcNow.Year,
		_utcNow.Month,
		_utcNow.Day,
		12,
		59,
		59,
		DateTimeKind.Utc
	).ToString(DateTimeFormat.DateTime, CultureInfo.InvariantCulture);

	private IDictionary<string, object> _createValues = new Dictionary<string, object>
	{
		{ "String", "A short test string." },
		{ "Integer", 42 },
		{ "Long", 69L },
		{ "Double", 42.69D },
		{ "Date", _dateTime },
		{ "DateTime", _dateTime },
		{ "DateTimeFixed", _dateTimeFixed },
		{ "Time", _time },
		{ "TimeFixed", _timeFixed },
		{ "DateFormatTestField", _dateFormatTestValue },
		{
			"Text",
			"Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.   \n\nDuis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.   \n\nUt wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi.   \n\nNam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat.   \n\nDuis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis.   \n\nAt vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat.   \n\nConsetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus.   \n\nLorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.   \n\nDuis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.   \n\nUt wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi.   \n\nNam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo"
		},
		{ "Boolean", true },
	};

	private IDictionary<string, object> _updateValues = new Dictionary<string, object>
	{
		{ "String", "A short updated test string." },
		{ "Integer", 43 },
		{ "Long", 70L },
		{ "Double", 43.70D },
		{ "Date", _dateTime },
		{ "DateTime", _dateTime },
		{ "Time", _dateTime },
		{ "DateFormatTestField", _dateFormatTestValue },
		{
			"Text",
			"Lirem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.   \n\nDuis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.   \n\nUt wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi.   \n\nNam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat.   \n\nDuis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis.   \n\nAt vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat.   \n\nConsetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus.   \n\nLorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.   \n\nDuis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.   \n\nUt wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi.   \n\nNam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo"
		},
		{ "Boolean", false },
	};

	private DataStoreOperationOrigin _origin = new(DataStoreOperationOriginType.System, 0, "Test");
	
	private WorkflowNodeEntity _activeWorkflowNode;

	#endregion

	protected ElementsControllerTest(DatabaseFixture fixture) : base(fixture, initStorage: true)
	{
		ControllerInstance = new ElementsController(LogManager, Fixture.ContextFactory, VersionReader, LocalizerFactory, DeepZoomHelperService, ThumbnailHelperService);

		SetAdminContext();
	}

	#region Data Preparation

	private async Task<DataSourceEntity> CreateDataSourceAsync(string name = "ExampleSource", DataSourceEntity? lookupSource = null)
	{
		await using var databaseContext = Fixture.Context;
		if (databaseContext.DataSources.Any(dataSource => dataSource.Name == name))
			return databaseContext.DataSources.First(dataSource => dataSource.Name == name);

		if (!databaseContext.DataStoreConfigs.Any())
		{
			databaseContext.DataStoreConfigs.Add(new DataStoreConfigEntity()
			{
				Name = "ExampleStore",
				Enabled = true,
				Type = DataStoreType.Storage,
				Options = Fixture.StorageOptions
			});
			await databaseContext.SaveChangesAsync();
			databaseContext.ChangeTracker.Clear();

			databaseContext.DataStoreContexts.Add(new DataStoreContextEntity()
			{
				Name = "ExampleContext",
				Enabled = true,
				DataStoreId = databaseContext.DataStoreConfigs.First().Id,
				CustomerId = (await UserManager.GetCurrentCustomerAsync()).Id,
				Options = Fixture.StorageContextOptions
			});
			await databaseContext.SaveChangesAsync();
			databaseContext.ChangeTracker.Clear();
			databaseContext.DataStoreContexts.First().CreateContext();
		}

		var dataStore = databaseContext.DataStoreConfigs.First();
		var dataSource = new DataSourceEntity()
		{
			DataStoreId = dataStore.Id,
			Name = name,
			Responsible = "UGA",
			Comment = "Example data source",
			StoragePath = "",
			StoreRevision = true,
		};
		databaseContext.DataSources.Add(dataSource);

		dataSource.SetConnection(new StorageDataSourceConnectionHelper(dataSource));

		var storageDataSource = dataSource.CreateDataSource();
		dataSource.SyncStorageFields(databaseContext, storageDataSource);
		await databaseContext.SaveChangesAsync();

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "String",
			FieldType = DataFieldType.DataField,
			Type = DataType.String,
			Length = 200,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "Integer",
			FieldType = DataFieldType.DataField,
			Type = DataType.Integer,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "Long",
			FieldType = DataFieldType.DataField,
			Type = DataType.Long,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "Double",
			FieldType = DataFieldType.DataField,
			Type = DataType.Double,
			DecimalPlaces = 2,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "Date",
			FieldType = DataFieldType.DataField,
			Type = DataType.Date,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "DateTime",
			FieldType = DataFieldType.DataField,
			Type = DataType.DateTime,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "DateTimeFixed",
			FieldType = DataFieldType.DataField,
			Type = DataType.DateTimeFixed,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "Time",
			FieldType = DataFieldType.DataField,
			Type = DataType.Time,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "TimeFixed",
			FieldType = DataFieldType.DataField,
			Type = DataType.TimeFixed,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "DateFormatTestField",
			FieldType = DataFieldType.DataField,
			Type = DataType.DateTime,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "Text",
			Type = DataType.Text,
		});

		dataSource.Fields.Add(new DataFieldEntity()
		{
			DataSourceId = dataSource.Id,
			Name = "Boolean",
			FieldType = DataFieldType.DataField,
			Type = DataType.Boolean,
		});

		if (lookupSource != null)
		{
			dataSource.Fields.Add(new DataFieldEntity()
			{
				DataSourceId = dataSource.Id,
				Name = "Lookup",
				Type = DataType.Guid,
				FieldType = DataFieldType.LookupField,
				LookupSourceId = lookupSource.Id,
				LookupDisplayFieldId = lookupSource.Fields.First(field => field.Name == "String").Id,
			});
		}

		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		dataSource = databaseContext.DataSources
			.Include(source => source.Fields).ThenInclude(field => field.LookupSource)
			.Include(source => source.Fields).ThenInclude(field => field.LookupDisplayField)
			.First(source => source.Name == dataSource.Name);
		var fields = dataSource.Fields.Where(field => !field.SystemField);
		foreach (var field in fields)
		{
			field.CreateField();
		}

		if (lookupSource != null)
		{
			dataSource.Fields.Add(new DataFieldEntity()
			{
				DataSourceId = dataSource.Id,
				Name = "Virtual",
				FieldType = DataFieldType.VirtualField,
				VirtualLookupFieldId = dataSource.Fields.First(field => field.FieldType == DataFieldType.LookupField).Id,
				VirtualDataFieldId = lookupSource.Fields.First(field => field.Name == "Integer").Id,
				VirtualDataStoreQueryName = "Lookup.Integer",
				HasVirtualData = true
			});
			await databaseContext.SaveChangesAsync();
		}

		return dataSource;
	}
	
	private WorkflowEntity PrepareWorkflow()
	{
		var databaseContext = Fixture.Context;
		var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
		var dataSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														 currentCustomer!.Id, syncSysFields: false);
		var workflow = EntityCreation.CreateWorkflow(databaseContext, dataSource);
		EntityCreation.CreateWorkflowNode(databaseContext, workflow, "Start");
		_activeWorkflowNode = EntityCreation.CreateWorkflowNode(databaseContext, workflow, "InProgress", WorkflowNodeState.InProgress);
		EntityCreation.CreateWorkflowNode(databaseContext, workflow, "Positive", WorkflowNodeState.Positive);
		EntityCreation.CreateWorkflowNode(databaseContext, workflow, "Negative", WorkflowNodeState.Negative);
		
		return databaseContext.Workflows
			.Include(wf => wf.Nodes)
			.Single(wf => wf.Id == workflow.Id);
	}

	private string PrepareElementForWorkflow()
	{
		var databaseContext = Fixture.Context;
		var dataSource = databaseContext.DataSources.First();

		var successInfo = dataSource.CreateElement(new DataStoreElementData
		{
			Values = new Dictionary<string, object?>()
			{
				{ "ExampleWorkflow_WorkflowStatus", _activeWorkflowNode.Id }	
			},
			Groups = new List<string> { "testgroup" }
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.System, -1, ""));
		return successInfo.ElementId;
	}

	#endregion

	#region Assertions

	private bool Exists(DataSourceEntity dataSource, string elementId)
	{
		// ReSharper disable once ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract
		return dataSource.GetElement(elementId) != null;
	}

	private bool IsPersistedCorrectly(DataSourceEntity dataSource, string elementId, DataStoreElementData requestElement)
	{
		var element = dataSource.GetElement(elementId);

		// ReSharper disable once ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract
		if (element == null)
			return false;

		foreach (var pair in requestElement.Values)
		{
			bool valid = element.Values.ContainsKey(pair.Key);
			switch (pair.Key)
			{
				case "Date":
					valid = valid && CompareDate(((DateTime)element.Values[pair.Key]!).ToString(DateTimeFormat.Date), (DateTime)pair.Value!);
					break;
				case "DateTime":
				case "DateFormatTestField":
					valid = valid && CompareDateTime((DateTime)element.Values[pair.Key]!, (DateTime)pair.Value!);
					break;
				case "DateTimeFixed":
					valid = valid && CompareDateTime((DateTime)element.Values[pair.Key]!, ((DateTime)pair.Value!).ToUniversalTime());
					break;
				case "Time":
					valid = valid && CompareTime(((DateTime)element.Values[pair.Key]!).ToString(DateTimeFormat.Time), ((DateTime)pair.Value!).TimeOfDay);
					break;
				case "TimeFixed":
					valid = valid && CompareTime(((DateTime)element.Values[pair.Key]!).ToString(DateTimeFormat.TimeFixed),
												 ((DateTime)pair.Value!).ToUniversalTime().TimeOfDay);
					break;
				case "Lookup":
					valid = valid && element.Values[pair.Key]!.Equals(pair.Value!.ToString());
					break;
				default:
					valid = valid && element.Values[pair.Key]!.Equals(pair.Value);
					break;
			}

			if (!valid)
				return false;
		}

		return true;
	}

	private bool IsPersistedCorrectly(DataSourceEntity dataSource, string[] elementIds, List<DataStoreElementData> requestElements)
	{
		for (var i = 0; i < elementIds.Length; i++)
		{
			if (!IsPersistedCorrectly(dataSource, elementIds[i], requestElements[i]))
				return false;
		}

		return true;
	}

	#endregion

	#region Tests

	#region Get

	[Fact(DisplayName = "Get Element by Id")]
	public async Task GetTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var result = ControllerInstance!.Get(mainDataSource.Id, elementInfo.ElementId);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<DataStoreElement>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		Assert.False(data.Id.IsNullOrEmpty());
		Assert.Equal(_createValues["String"], data.Values["String"]);
		Assert.Equal(_createValues["Integer"], data.Values["Integer"]);
		Assert.Equal(_createValues["Long"], data.Values["Long"]);
		Assert.Equal(_createValues["Double"], data.Values["Double"]);
		Assert.True(CompareDate((string)data.Values["Date"]!, (DateTime)_createValues["Date"]));
		Assert.True(CompareDateTime((DateTime)data.Values["DateTime"]!, (DateTime)_createValues["DateTime"]));
		Assert.True(CompareTime((string)data.Values["Time"]!, ((DateTime)_createValues["Time"]).TimeOfDay));
		Assert.True(CompareDateTime((DateTime)data.Values["DateFormatTestField"]!, (DateTime)_createValues["DateFormatTestField"]));
		Assert.Equal(_createValues["Text"], data.Values["Text"]);
		Assert.Equal(_createValues["Boolean"], data.Values["Boolean"]);

		var lookup = (Dictionary<string, object>)data.Values["Lookup"]!;
		Assert.True(lookup.ContainsKey("Id"));
		Assert.Equal(_createValues["Lookup"].ToString(), lookup["Id"]);
		Assert.True(lookup.ContainsKey("DisplayValue"));
		Assert.Equal(_createValues["String"].ToString(), lookup["DisplayValue"]);
		Assert.Equal(_createValues["Integer"], data.Values["Virtual"]);
	}
	
	[Fact(DisplayName = "Get Element by Id with Lookup Field missing its Displayfield")]
	public async Task GetBrokenLookupTest()
	{
		await using var databaseContext = Fixture.Context;
		
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var brokenLookupField =
			databaseContext.DataFields.FirstOrDefault(field => field.DataSourceId == mainDataSource.Id && field.FieldType == DataFieldType.LookupField)!;
		brokenLookupField.LookupDisplayField = null;
		brokenLookupField.LookupDisplayFieldId = null;
		await databaseContext.SaveChangesAsync();
		
		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var result = ControllerInstance!.Get(mainDataSource.Id, elementInfo.ElementId);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<DataStoreElement>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		Assert.False(data.Id.IsNullOrEmpty());
		Assert.Equal(_createValues["String"], data.Values["String"]);
		Assert.Equal(_createValues["Integer"], data.Values["Integer"]);
		Assert.Equal(_createValues["Long"], data.Values["Long"]);
		Assert.Equal(_createValues["Double"], data.Values["Double"]);
		Assert.True(CompareDate((string)data.Values["Date"]!, (DateTime)_createValues["Date"]));
		Assert.True(CompareDateTime((DateTime)data.Values["DateTime"]!, (DateTime)_createValues["DateTime"]));
		Assert.True(CompareTime((string)data.Values["Time"]!, ((DateTime)_createValues["Time"]).TimeOfDay));
		Assert.True(CompareDateTime((DateTime)data.Values["DateFormatTestField"]!, (DateTime)_createValues["DateFormatTestField"]));
		Assert.Equal(_createValues["Text"], data.Values["Text"]);
		Assert.Equal(_createValues["Boolean"], data.Values["Boolean"]);
		Assert.Equal(_createValues["Lookup"].ToString(), data.Values["Lookup"]);
		Assert.Equal(_createValues["Integer"], data.Values["Virtual"]);
	}

	[Fact(DisplayName = "Get Element by Id that contains a stacking virtual field")]
	public async Task GetStackingVirtualTest()
	{
		var databaseContext = Fixture.Context;
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var selfRefLookup = databaseContext.DataFields.Add(new DataFieldEntity
		{
			Name = "SelfRefLookup",
			DataSourceId = lookupDataSource.Id,
			FieldType = DataFieldType.LookupField,
			LookupSourceId = lookupDataSource.Id,
			LookupSource = databaseContext.DataSources.Find(lookupDataSource.Id)!,
			LookupDisplayFieldId = lookupDataSource.Fields.FirstOrDefault(field => field.Name == "String")?.Id ??
								   databaseContext.DataFields.FirstOrDefault(field => field.Name == "String")!.Id,
			Length = 255
		});
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		selfRefLookup.Entity.SetContext(Fixture.Context);
		selfRefLookup.Entity.CreateField();
		var selfRefVirtual = databaseContext.DataFields.Add(new DataFieldEntity
		{
			Name = "SelfRefVirtual",
			DataSourceId = lookupDataSource.Id,
			FieldType = DataFieldType.VirtualField,
			VirtualLookupFieldId = selfRefLookup.Entity.Id,
			VirtualDataFieldId = databaseContext.DataFields.FirstOrDefault(field => field.Name == "String")!.Id,
			VirtualDataStoreQueryName = "SelfRefLookup.String",
			HasVirtualData = true
		});
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);
		databaseContext.DataFields.Add(new DataFieldEntity
		{
			Name = "StackingVirtual",
			DataSourceId = mainDataSource.Id,
			FieldType = DataFieldType.VirtualField,
			VirtualLookupFieldId = databaseContext.DataFields.FirstOrDefault(field => field.Name == "Lookup")!.Id,
			VirtualDataFieldId = selfRefVirtual.Entity.Id,
			VirtualDataStoreQueryName = "Lookup.SelfRefLookup.String",
			HasVirtualData = true
		});
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfoSelfRef = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("SelfRefLookup", lookupInfoSelfRef.ElementId);
		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Remove("SelfRefLookup");

		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var result = ControllerInstance!.Get(mainDataSource.Id, elementInfo.ElementId);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(result);
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var data = (objectResult.Value as PublicApiResponse<DataStoreElement>)?.Data;
		Assert.NotNull(data);
		Assert.False(data.Id.IsNullOrEmpty());
		Assert.Equal(_createValues["String"], data.Values["String"]);
		Assert.Equal(_createValues["Integer"], data.Values["Integer"]);
		Assert.Equal(_createValues["Long"], data.Values["Long"]);
		Assert.Equal(_createValues["Double"], data.Values["Double"]);
		Assert.True(CompareDate((string)data.Values["Date"]!, (DateTime)_createValues["Date"]));
		Assert.True(CompareDateTime((DateTime)data.Values["DateTime"]!, (DateTime)_createValues["DateTime"]));
		Assert.True(CompareTime((string)data.Values["Time"]!, ((DateTime)_createValues["Time"]).TimeOfDay));
		Assert.True(CompareDateTime((DateTime)data.Values["DateFormatTestField"]!, (DateTime)_createValues["DateFormatTestField"]));
		Assert.Equal(_createValues["Text"], data.Values["Text"]);
		Assert.Equal(_createValues["Boolean"], data.Values["Boolean"]);
		Assert.Equal(_createValues["String"], data.Values["StackingVirtual"]);

		var lookup = (Dictionary<string, object>)data.Values["Lookup"]!;
		Assert.True(lookup.ContainsKey("Id"));
		Assert.Equal(_createValues["Lookup"].ToString(), lookup["Id"]);
		Assert.True(lookup.ContainsKey("DisplayValue"));
		Assert.Equal(_createValues["String"].ToString(), lookup["DisplayValue"]);
	}
	
	[Fact(DisplayName = "Get Element by Id that contains a stacking virtual field that references a lookup field")]
	public async Task GetStackingVirtualOnLookupTest()
	{
		var databaseContext = Fixture.Context;
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var selfRefLookup = databaseContext.DataFields.Add(new DataFieldEntity
		{
			Name = "SelfRefLookup",
			DataSourceId = lookupDataSource.Id,
			FieldType = DataFieldType.LookupField,
			LookupSourceId = lookupDataSource.Id,
			LookupSource = databaseContext.DataSources.Find(lookupDataSource.Id)!,
			LookupDisplayFieldId = lookupDataSource.Fields.FirstOrDefault(field => field.Name == "String")?.Id ??
								   databaseContext.DataFields.FirstOrDefault(field => field.Name == "String")!.Id,
			Length = 255
		});
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		selfRefLookup.Entity.SetContext(Fixture.Context);
		selfRefLookup.Entity.CreateField();

		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);
		databaseContext.DataFields.Add(new DataFieldEntity
		{
			Name = "VirtualOnLookup",
			DataSourceId = mainDataSource.Id,
			FieldType = DataFieldType.VirtualField,
			Type = DataType.Guid,
			VirtualLookupFieldId = databaseContext.DataFields.FirstOrDefault(field => field.Name == "Lookup")!.Id,
			VirtualDataFieldId = selfRefLookup.Entity.Id,
			VirtualDataStoreQueryName = "Lookup.SelfRefLookup.String",
			HasVirtualData = true
		});
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfoSelfRef = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("SelfRefLookup", lookupInfoSelfRef.ElementId);
		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Remove("SelfRefLookup");

		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var result = ControllerInstance!.Get(mainDataSource.Id, elementInfo.ElementId);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(result);
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var data = (objectResult.Value as PublicApiResponse<DataStoreElement>)?.Data;
		Assert.NotNull(data);
		Assert.False(data.Id.IsNullOrEmpty());
		Assert.Equal(_createValues["String"], data.Values["String"]);
		Assert.Equal(_createValues["Integer"], data.Values["Integer"]);
		Assert.Equal(_createValues["Long"], data.Values["Long"]);
		Assert.Equal(_createValues["Double"], data.Values["Double"]);
		Assert.True(CompareDate((string)data.Values["Date"]!, (DateTime)_createValues["Date"]));
		Assert.True(CompareDateTime((DateTime)data.Values["DateTime"]!, (DateTime)_createValues["DateTime"]));
		Assert.True(CompareTime((string)data.Values["Time"]!, ((DateTime)_createValues["Time"]).TimeOfDay));
		Assert.True(CompareDateTime((DateTime)data.Values["DateFormatTestField"]!, (DateTime)_createValues["DateFormatTestField"]));
		Assert.Equal(_createValues["Text"], data.Values["Text"]);
		Assert.Equal(_createValues["Boolean"], data.Values["Boolean"]);
		
		var virtualOnLookup = (Dictionary<string, object>)data.Values["VirtualOnLookup"]!;
		Assert.True(virtualOnLookup.ContainsKey("Id"));
		Assert.Equal(lookupInfoSelfRef.ElementId, virtualOnLookup["Id"]);
		Assert.True(virtualOnLookup.ContainsKey("DisplayValue"));
		Assert.Equal(_createValues["String"].ToString(), virtualOnLookup["DisplayValue"]);

		var lookup = (Dictionary<string, object>)data.Values["Lookup"]!;
		Assert.True(lookup.ContainsKey("Id"));
		Assert.Equal(_createValues["Lookup"].ToString(), lookup["Id"]);
		Assert.True(lookup.ContainsKey("DisplayValue"));
		Assert.Equal(_createValues["String"].ToString(), lookup["DisplayValue"]);
	}

	[Fact(DisplayName = "Get Element by Id that contains empty lookup field")]
	public async Task GetWithEmptyLookupTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var result = ControllerInstance!.Get(mainDataSource.Id, elementInfo.ElementId);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<DataStoreElement>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		Assert.False(data.Id.IsNullOrEmpty());
		Assert.Equal(_createValues["String"], data.Values["String"]);
		Assert.Equal(_createValues["Integer"], data.Values["Integer"]);
		Assert.Equal(_createValues["Long"], data.Values["Long"]);
		Assert.Equal(_createValues["Double"], data.Values["Double"]);
		Assert.True(CompareDate((string)data.Values["Date"]!, (DateTime)_createValues["Date"]));
		Assert.True(CompareDateTime((DateTime)data.Values["DateTime"]!, (DateTime)_createValues["DateTime"]));
		Assert.True(CompareTime((string)data.Values["Time"]!, ((DateTime)_createValues["Time"]).TimeOfDay));
		Assert.True(CompareDateTime((DateTime)data.Values["DateFormatTestField"]!, (DateTime)_createValues["DateFormatTestField"]));
		Assert.Equal(_createValues["Text"], data.Values["Text"]);
		Assert.Equal(_createValues["Boolean"], data.Values["Boolean"]);
		Assert.Null(data.Values["Lookup"]);
	}

	[Fact(DisplayName = "Get Element by Id with invalid DataSourceId")]
	public async Task InvalidGetTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var lookupInfo = lookupDataSource.CreateElement(new(_createValues!, new List<string> { "testgroup" }), _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(new(_createValues!, new List<string> { "testgroup" }), _origin);

		var result = ControllerInstance!.Get(lookupDataSource.Id, elementInfo.ElementId);
		var code = (result.Result as ObjectResult)?.StatusCode;

		Assert.NotNull(result);
		Assert.Equal(404, code);
	}

	#endregion

	#region Create

	[Fact(DisplayName = "Create Element")]
	public async Task CreateTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var lookupInfo = lookupDataSource.CreateElement(new(_createValues!, new List<string> { "testgroup" }), _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var requestData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });

		var result = ControllerInstance!.Create(mainDataSource.Id, requestData);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<DataStoreElement>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		Assert.False(data.Id.IsNullOrEmpty());
		Assert.Equal(_createValues["String"], data.Values["String"]);
		Assert.Equal(_createValues["Integer"], data.Values["Integer"]);
		Assert.Equal(_createValues["Long"], data.Values["Long"]);
		Assert.Equal(_createValues["Double"], data.Values["Double"]);
		Assert.True(CompareDate((string)data.Values["Date"]!, (DateTime)_createValues["Date"]));
		Assert.True(CompareDateTime((DateTime)data.Values["DateTime"]!, (DateTime)_createValues["DateTime"]));
		Assert.Equal((string)data.Values["DateTimeFixed"]!,
					 ((DateTime)_createValues["DateTimeFixed"]).ToUniversalTime().ToString(DateTimeFormat.DateTimeFixed, CultureInfo.InvariantCulture));
		Assert.True(CompareTime((string)data.Values["Time"]!, ((DateTime)_createValues["Time"]).TimeOfDay));
		Assert.True(CompareTime((string)data.Values["TimeFixed"]!, ((DateTime)_createValues["TimeFixed"]).ToUniversalTime().TimeOfDay));
		Assert.True(CompareDateTime((DateTime)data.Values["DateFormatTestField"]!, (DateTime)_createValues["DateFormatTestField"]));
		Assert.Equal(_createValues["Text"], data.Values["Text"]);
		Assert.Equal(_createValues["Boolean"], data.Values["Boolean"]);
		Assert.Equal(_createValues["Lookup"].ToString(), ((Dictionary<string, object>)data.Values["Lookup"]!)["Id"]);
		Assert.Equal(_createValues["String"].ToString(), ((Dictionary<string, object>)data.Values["Lookup"]!)["DisplayValue"]);

		Assert.True(IsPersistedCorrectly(mainDataSource, data.Id, requestData));
	}

	[Fact(DisplayName = "Create Element with Lookup Field missing its Displayfield")]
	public async Task CreateBrokenLookupTest()
	{
		await using var databaseContext = Fixture.Context;

		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var brokenLookupField =
			databaseContext.DataFields.FirstOrDefault(field => field.DataSourceId == mainDataSource.Id && field.FieldType == DataFieldType.LookupField)!;
		brokenLookupField.LookupDisplayField = null;
		brokenLookupField.LookupDisplayFieldId = null;
		await databaseContext.SaveChangesAsync();

		var lookupInfo = lookupDataSource.CreateElement(new(_createValues!, new List<string> { "testgroup" }), _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var requestData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });

		var result = ControllerInstance!.Create(mainDataSource.Id, requestData);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<DataStoreElement>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		Assert.False(data.Id.IsNullOrEmpty());
		Assert.Equal(_createValues["String"], data.Values["String"]);
		Assert.Equal(_createValues["Integer"], data.Values["Integer"]);
		Assert.Equal(_createValues["Long"], data.Values["Long"]);
		Assert.Equal(_createValues["Double"], data.Values["Double"]);
		Assert.True(CompareDate((string)data.Values["Date"]!, (DateTime)_createValues["Date"]));
		Assert.True(CompareDateTime((DateTime)data.Values["DateTime"]!, (DateTime)_createValues["DateTime"]));
		Assert.Equal((string)data.Values["DateTimeFixed"]!,
					 ((DateTime)_createValues["DateTimeFixed"]).ToUniversalTime().ToString(DateTimeFormat.DateTimeFixed, CultureInfo.InvariantCulture));
		Assert.True(CompareTime((string)data.Values["Time"]!, ((DateTime)_createValues["Time"]).TimeOfDay));
		Assert.True(CompareTime((string)data.Values["TimeFixed"]!, ((DateTime)_createValues["TimeFixed"]).ToUniversalTime().TimeOfDay));
		Assert.True(CompareDateTime((DateTime)data.Values["DateFormatTestField"]!, (DateTime)_createValues["DateFormatTestField"]));
		Assert.Equal(_createValues["Text"], data.Values["Text"]);
		Assert.Equal(_createValues["Boolean"], data.Values["Boolean"]);
		Assert.Equal(_createValues["Lookup"].ToString(), data.Values["Lookup"]);

		Assert.True(IsPersistedCorrectly(mainDataSource, data.Id, requestData));
	}

	[Fact(DisplayName = "Batch Create Elements")]
	public async Task CreateBatchTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var lookupInfo = lookupDataSource.CreateElement(new(_createValues!, new List<string> { "testgroup" }), _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);

		var count = 4;
		var createList = new List<DataStoreElementData>();
		for (var i = 0; i < count; i++)
		{
			createList.Add(new(_createValues!, new List<string> { "testgroup" }));
		}

		var result = ControllerInstance!.CreateBatch(mainDataSource.Id, createList);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<string[]>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		Assert.True(data.Length == count);

		Assert.True(IsPersistedCorrectly(mainDataSource, data, createList));
	}

	[Fact(DisplayName = "Create Element with invalid value")]
	public async Task InvalidCreateTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var lookupInfo = lookupDataSource.CreateElement(new(_createValues!, new List<string> { "testgroup" }), _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		_createValues.Add("InvalidField", "nonsense");
		var requestData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });

		var result = ControllerInstance!.Create(mainDataSource.Id, requestData);
		var code = (result.Result as ObjectResult)?.StatusCode;

		Assert.NotNull(result);
		Assert.Equal(400, code);
	}

	[Fact(DisplayName = "Batch Create Elements with one invalid Element")]
	public async Task InvalidCreateBatchTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var lookupInfo = lookupDataSource.CreateElement(new(_createValues!, new List<string> { "testgroup" }), _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);

		var count = 4;
		var createList = new List<DataStoreElementData>();
		for (var i = 0; i < count; i++)
		{
			createList.Add(new(_createValues!, new List<string> { "testgroup" }));
		}

		createList[^1].Values.Add("InvalidField", "nonsense");

		var result = ControllerInstance!.CreateBatch(mainDataSource.Id, createList);
		var code = (result.Result as ObjectResult)?.StatusCode;

		Assert.NotNull(result);
		Assert.Equal(400, code);
	}

	#endregion

	#region Update

	[Fact(DisplayName = "Update Element by Id")]
	public async Task UpdateTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var firstLookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		var secondLookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", firstLookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		_updateValues.Add("Lookup", secondLookupInfo.ElementId);
		var updateData = new DataStoreElementData(elementInfo.ElementId, _updateValues!, new List<string> { "testgroup" });

		var result = await ControllerInstance!.Update(mainDataSource.Id, elementInfo.ElementId, updateData);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<DataStoreElement>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		Assert.False(data.Id.IsNullOrEmpty());
		Assert.Equal(_updateValues["String"], data.Values["String"]);
		Assert.Equal(_updateValues["Integer"], data.Values["Integer"]);
		Assert.Equal(_updateValues["Long"], data.Values["Long"]);
		Assert.Equal(_updateValues["Double"], data.Values["Double"]);
		Assert.True(CompareDate((string)data.Values["Date"]!, (DateTime)_updateValues["Date"]));
		Assert.True(CompareDateTime((DateTime)data.Values["DateTime"]!, (DateTime)_updateValues["DateTime"]));
		Assert.True(CompareTime((string)data.Values["Time"]!, ((DateTime)_updateValues["Time"]).TimeOfDay));
		Assert.True(CompareDateTime((DateTime)data.Values["DateFormatTestField"]!, (DateTime)_createValues["DateFormatTestField"]));
		Assert.Equal(_updateValues["Text"], data.Values["Text"]);
		Assert.Equal(_updateValues["Boolean"], data.Values["Boolean"]);
		Assert.Equal(_updateValues["Lookup"].ToString(), ((Dictionary<string, object>)data.Values["Lookup"]!)["Id"]);
		Assert.Equal(_createValues["String"].ToString(), ((Dictionary<string, object>)data.Values["Lookup"]!)["DisplayValue"]);

		Assert.True(IsPersistedCorrectly(mainDataSource, data.Id, updateData));
	}

	[Fact(DisplayName = "Update Element by Id with Lookup Field missing its Displayfield")]
	public async Task UpdateBrokenLookupTest()
	{
		await using var databaseContext = Fixture.Context;
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var brokenLookupField =
			databaseContext.DataFields.FirstOrDefault(field => field.DataSourceId == mainDataSource.Id && field.FieldType == DataFieldType.LookupField)!;
		brokenLookupField.LookupDisplayField = null;
		brokenLookupField.LookupDisplayFieldId = null;
		await databaseContext.SaveChangesAsync();

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var firstLookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		var secondLookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", firstLookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		_updateValues.Add("Lookup", secondLookupInfo.ElementId);
		var updateData = new DataStoreElementData(elementInfo.ElementId, _updateValues!, new List<string> { "testgroup" });

		var result = await ControllerInstance!.Update(mainDataSource.Id, elementInfo.ElementId, updateData);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<DataStoreElement>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		Assert.False(data.Id.IsNullOrEmpty());
		Assert.Equal(_updateValues["String"], data.Values["String"]);
		Assert.Equal(_updateValues["Integer"], data.Values["Integer"]);
		Assert.Equal(_updateValues["Long"], data.Values["Long"]);
		Assert.Equal(_updateValues["Double"], data.Values["Double"]);
		Assert.True(CompareDate((string)data.Values["Date"]!, (DateTime)_updateValues["Date"]));
		Assert.True(CompareDateTime((DateTime)data.Values["DateTime"]!, (DateTime)_updateValues["DateTime"]));
		Assert.True(CompareTime((string)data.Values["Time"]!, ((DateTime)_updateValues["Time"]).TimeOfDay));
		Assert.True(CompareDateTime((DateTime)data.Values["DateFormatTestField"]!, (DateTime)_createValues["DateFormatTestField"]));
		Assert.Equal(_updateValues["Text"], data.Values["Text"]);
		Assert.Equal(_updateValues["Boolean"], data.Values["Boolean"]);
		Assert.Equal(_updateValues["Lookup"].ToString(), data.Values["Lookup"]!);

		Assert.True(IsPersistedCorrectly(mainDataSource, data.Id, updateData));
	}

	[Fact(DisplayName = "Update Element with invalid Values")]
	public async Task InvalidValuesUpdateTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);
		var updatedElementData = new DataStoreElementData
		{
			ElementId = elementInfo.ElementId,
			Values = new Dictionary<string, object?>
			{
				{ "String", "A short updated test string." },
				{ "nonExsitingField", "nonsense" }
			},
			Groups = new List<string> { "testgroup" }
		};

		var result = await ControllerInstance!.Update(mainDataSource.Id, elementInfo.ElementId, updatedElementData);
		var code = (result.Result as ObjectResult)?.StatusCode;

		Assert.NotNull(result);
		Assert.Equal(400, code);

		Assert.True(IsPersistedCorrectly(mainDataSource, elementInfo.ElementId, elementData));
	}

	[Fact(DisplayName = "Update Element with invalid ElementId in request")]
	public async Task InvalidIdUpdateTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);
		var updateData = new DataStoreElementData(lookupInfo.ElementId, _updateValues!, new List<string> { "testgroup" });

		var result = await ControllerInstance!.Update(mainDataSource.Id, elementInfo.ElementId, updateData);
		var code = (result.Result as ObjectResult)?.StatusCode;

		Assert.NotNull(result);
		Assert.Equal(400, code);

		Assert.True(IsPersistedCorrectly(mainDataSource, elementInfo.ElementId, elementData));
	}

	[Fact(DisplayName = "Update Element with invalid DataSourceId")]
	public async Task InvalidSourceUpdateTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);
		var updateData = new DataStoreElementData(elementInfo.ElementId, _updateValues!, new List<string> { "testgroup" });

		var result = await ControllerInstance!.Update(lookupDataSource.Id, elementInfo.ElementId, updateData);
		var code = (result.Result as ObjectResult)?.StatusCode;

		Assert.NotNull(result);
		Assert.Equal(404, code);

		Assert.True(IsPersistedCorrectly(mainDataSource, elementInfo.ElementId, elementData));
	}

	#endregion

	#region Delete

	[Fact(DisplayName = "Delete Element")]
	public async Task DeleteTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var result = await ControllerInstance!.Delete(mainDataSource.Id, elementInfo.ElementId);
		var code = (result.Result as ObjectResult)?.StatusCode;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.Null(await mainDataSource.GetElementAsync(elementInfo.ElementId));
	}

	[Fact(DisplayName = "Delete Element with invalid DataSourceId")]
	public async Task InvalidDeleteTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var result = await ControllerInstance!.Delete(lookupDataSource.Id, elementInfo.ElementId);
		var code = (result.Result as ObjectResult)?.StatusCode;

		Assert.NotNull(result);
		Assert.Equal(404, code);
		Assert.True(Exists(mainDataSource, elementInfo.ElementId));
	}

	#endregion

	#region Action

	[Fact(DisplayName = "Update Element Favourite")]
	public async Task FavouriteTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var actionDto = new ElementActionDto()
		{
			Type = ElementActionType.Favorite,
			Value = true
		};
		
		var result = await ControllerInstance!.ExecuteElementAction(mainDataSource.Id, elementInfo.ElementId, actionDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var favourite = (await mainDataSource.GetElementAsync(elementInfo.ElementId))!.IsFavourite;
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.True(favourite);
	}
	
	[Fact(DisplayName = "Update Element Inactive")]
	public async Task InactiveTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var actionDto = new ElementActionDto()
		{
			Type = ElementActionType.Inactive,
			Value = true
		};
		
		var result = await ControllerInstance!.ExecuteElementAction(mainDataSource.Id, elementInfo.ElementId, actionDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var inactive = (await mainDataSource.GetElementAsync(elementInfo.ElementId))!.IsInactive;
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.True(inactive);
	}
	
	[Fact(DisplayName = "Update Elements Inactive")]
	public async Task InactiveMultipleTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);
		var secondElementInfo = mainDataSource.CreateElement(elementData, _origin);

		var actionDto = new ElementActionDto()
		{
			Type = ElementActionType.Inactive,
			Value = true,
			Elements = new List<string>(){elementInfo.ElementId, secondElementInfo.ElementId}
		};
		
		var result = await ControllerInstance!.ExecuteElementsAction(mainDataSource.Id, actionDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var inactive = (await mainDataSource.GetElementAsync(elementInfo.ElementId))!.IsInactive;
		var secondInactive = (await mainDataSource.GetElementAsync(secondElementInfo.ElementId))!.IsInactive;
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.True(inactive);
		Assert.True(secondInactive);
	}
	
	[Fact(DisplayName = "Update Elements Favourite")]
	public async Task FavouriteMultipleTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);
		var secondElementInfo = mainDataSource.CreateElement(elementData, _origin);

		var actionDto = new ElementActionDto()
		{
			Type = ElementActionType.Favorite,
			Value = true,
			Elements = new List<string>(){elementInfo.ElementId, secondElementInfo.ElementId}
		};
		
		var result = await ControllerInstance!.ExecuteElementsAction(mainDataSource.Id, actionDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var favourite = (await mainDataSource.GetElementAsync(elementInfo.ElementId))!.IsFavourite;
		var secondFavourite = (await mainDataSource.GetElementAsync(secondElementInfo.ElementId))!.IsFavourite;
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.True(favourite);
		Assert.True(secondFavourite);
	}
	
	[Fact(DisplayName = "Update Element Favourite twice")]
	public async Task FavouriteAlreadyFavouriteElementIdTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);
		
		var actionDto = new ElementActionDto()
		{
			Type = ElementActionType.Favorite,
			Value = true
		};
		
		await ControllerInstance!.ExecuteElementAction(mainDataSource.Id, elementInfo.ElementId!, actionDto);
		var result = await ControllerInstance!.ExecuteElementAction(mainDataSource.Id, elementInfo.ElementId!, actionDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var favourite = (await mainDataSource.GetElementAsync(elementInfo.ElementId))!.IsFavourite;
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.True(favourite);
	}
	
	[Fact(DisplayName = "Update non existent Element Favourite")]
	public async Task FavouriteInvalidElementIdTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var actionDto = new ElementActionDto()
		{
			Type = ElementActionType.Favorite,
			Value = true
		};
		
		var result = await ControllerInstance!.ExecuteElementAction(mainDataSource.Id, new Guid().ToString(), actionDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		
		Assert.NotNull(result);
		Assert.Equal(404, code);
	}
	
	[Fact(DisplayName = "Update non existent Elements Favourite")]
	public async Task FavouriteInvalidElementsIdTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var actionDto = new ElementActionDto()
		{
			Type = ElementActionType.Favorite,
			Value = true,
			Elements = new List<string>() {new Guid().ToString(), new Guid().ToString()}
		};
		
		var result = await ControllerInstance!.ExecuteElementsAction(mainDataSource.Id, actionDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		
		Assert.NotNull(result);
		Assert.Equal(404, code);
	}
	
	[Fact(DisplayName = "Update Element invalid Inactive Action")]
	public async Task InactiveInvalidActionTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);

		var actionDto = new ElementActionDto()
		{
			Type = ElementActionType.Inactive,
			Value = "bla"
		};
		
		var result = await ControllerInstance!.ExecuteElementAction(mainDataSource.Id, elementInfo.ElementId, actionDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var inactive = (await mainDataSource.GetElementAsync(elementInfo.ElementId))!.IsInactive;
		
		Assert.NotNull(result);
		Assert.Equal(400, code);
		Assert.False(inactive);
	}
	
	[Fact(DisplayName = "Update Elements invalid Inactive Action")]
	public async Task InactiveInvalidActionsTest()
	{
		var lookupDataSource = await CreateDataSourceAsync("lookupSource");
		var mainDataSource = await CreateDataSourceAsync("mainSource", lookupDataSource);

		var elementData = new DataStoreElementData(_createValues!, new List<string> { "testgroup" });
		elementData.PrepareValuesForDataStore(mainDataSource.Fields);

		var lookupInfo = lookupDataSource.CreateElement(elementData, _origin);
		_createValues.Add("Lookup", lookupInfo.ElementId);
		var elementInfo = mainDataSource.CreateElement(elementData, _origin);
		var secondElementInfo = mainDataSource.CreateElement(elementData, _origin);

		var actionDto = new ElementActionDto()
		{
			Type = ElementActionType.Inactive,
			Value = "bla",
			Elements = new List<string>{elementInfo.ElementId, secondElementInfo.ElementId}
		};
		
		var result = await ControllerInstance!.ExecuteElementsAction(mainDataSource.Id, actionDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var inactive = (await mainDataSource.GetElementAsync(elementInfo.ElementId))!.IsInactive;
		var secondInactive = (await mainDataSource.GetElementAsync(secondElementInfo.ElementId))!.IsInactive;
		
		Assert.NotNull(result);
		Assert.Equal(400, code);
		Assert.False(inactive);
		Assert.False(secondInactive);
	}
	
	#endregion

	#region Workflow

	[Fact(DisplayName = "Test get current workflow state")]
	public async Task TestGetCurrentWorkflowState()
	{
		var databaseContext = Fixture.Context;
		var workflow = PrepareWorkflow();
		var elementId = PrepareElementForWorkflow();
		
		var result = await ControllerInstance!.GetCurrentWorkflowState(workflow.DataSourceId, elementId, workflow.Id);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(objectResult.StatusCode, 200);

		var json = (objectResult.Value as PublicApiResponse<JsonElement>)!.Data;
		var actual = json.Deserialize<WorkflowNodeDto>(_jsonOptions);
		Assert.NotNull(actual);
		
		var expected = databaseContext.WorkflowNodes
			.Single(node => node.WorkflowId == workflow.Id && node.State == WorkflowNodeState.InProgress)
			.ToDto();
		Assert.NotNull(expected);
		Assert.Equal(expected.Id, actual.Id);
		Assert.Equal(expected.WorkflowId, actual.WorkflowId);
		Assert.Equal(expected.Name, actual.Name);
		Assert.Equal(expected.State, actual.State);
		Assert.Equal(expected.Icon, actual.Icon);
	}
	
	[Fact(DisplayName = "Test change workflow state")]
	public async Task TestChangeWorkflowStatus()
	{
		var databaseContext = Fixture.Context;
		var workflow = PrepareWorkflow();
		var elementId = PrepareElementForWorkflow();
		var expectedNode = databaseContext.WorkflowNodes
			.Single(node => node.WorkflowId == workflow.Id && node.State == WorkflowNodeState.Positive);
		
		var result = await ControllerInstance!.ChangeWorkflowStatus(workflow.DataSourceId, elementId, workflow.Id, expectedNode.Id);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(objectResult.StatusCode, 200);
		
		// Check if change really happened
		result = await ControllerInstance.GetCurrentWorkflowState(workflow.DataSourceId, elementId, workflow.Id);
		Assert.NotNull(result);
		
		objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(objectResult.StatusCode, 200);

		var json = (objectResult.Value as PublicApiResponse<JsonElement>)!.Data;
		var actual = json.Deserialize<WorkflowNodeDto>(_jsonOptions);
		Assert.NotNull(actual);
		
		var expected = expectedNode.ToDto();
		Assert.NotNull(expected);
		Assert.Equal(expected.Id, actual.Id);
		Assert.Equal(expected.WorkflowId, actual.WorkflowId);
		Assert.Equal(expected.Name, actual.Name);
		Assert.Equal(expected.State, actual.State);
		Assert.Equal(expected.Icon, actual.Icon);
	}

	#endregion
	
	#endregion

	#region HelperFunctions

	private bool CompareDate(string actualString, DateTime expected)
	{
		var actual = DateTimeOffset.Parse(actualString, CultureInfo.CurrentCulture, DateTimeStyles.AssumeUniversal).Date;

		if (actual.Day != expected.Day)
			return false;

		if (actual.Date != expected.Date)
			return false;

		return actual.Year == expected.Year;
	}

	private bool CompareDateTime(DateTime actual, DateTime expected)
	{
		if (actual.Day != expected.Day)
			return false;

		if (actual.Date != expected.Date)
			return false;

		if (actual.Year != expected.Year)
			return false;

		if (actual.Hour != expected.Hour)
			return false;

		if (actual.Minute != expected.Minute)
			return false;

		return actual.Second == expected.Second;
	}

	private bool CompareTime(string actualString, TimeSpan expected)
	{
		var actual = DateTimeOffset.Parse(actualString, CultureInfo.CurrentCulture, DateTimeStyles.AssumeUniversal).TimeOfDay;

		if (actual.Hours != expected.Hours)
			return false;

		if (actual.Minutes != expected.Minutes)
			return false;

		return actual.Seconds == expected.Seconds;
	}

	private new void SetAdminContext()
	{
		var claims = new List<Claim>()
		{
			new(ClaimTypes.NameIdentifier, "1"),
			new("http://schemas.microsoft.com/ws/2008/06/identity/claims/role", "admin"),
			new("urn:zitadel:iam:user:resourceowner:id", "1")
		};
		var identity = new ClaimsIdentity(claims, "MockAuthType");

		HttpContextAccessor.HttpContext!.User = new ClaimsPrincipal(identity);
	}

	#endregion
}