using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Page.MultiData;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PublicApi.Controllers;

[ExcludeFromCodeCoverage]
public class PostgresMultiDataPagesControllerTest(PostgresDatabaseFixture fixture)
	: MultiDataPagesControllerTest(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class MultiDataPagesControllerTest(DatabaseFixture fixture) : PagesControllerTest<MultiDataPageEntity, MultiDataPageDto>(fixture)
{
	#region Test Data Properties
	
	protected override MultiDataPageDto CreateDto
	{
		get
		{
			var pageDto = base.CreateDto;
			pageDto = new MultiDataPageDto(pageDto)
			{
				Type = PageType.MultiData,
				SingleRecordBehaviour = DataOpeningType.InTable
			};
			
			var localizer = LocalizerFactory.Create("PageType", "");
			pageDto.TypeName = localizer[pageDto.Type.ToString()];
			
			return (MultiDataPageDto)pageDto;
		}
	}
	
	#endregion

	#region Data Preparation
	
	protected override MultiDataPageEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.MultiDataPages.Add(MultiDataPageEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();
		
		entry.Entity.SetStringLocalizerFactory(LocalizerFactory);
		
		return entry.Entity;
	}
	
	#endregion
	
	#region Assertions
	
	protected override void AssertIsAsExpected(PageDto expected, PageDto actual)
	{
		var castActual = (MultiDataPageDto)actual;
		var castExpected = (MultiDataPageDto)expected;
		Assert.Equal(castActual.SingleRecordBehaviour, castExpected.SingleRecordBehaviour);
		base.AssertIsAsExpected(expected, actual);
	}
	
	#endregion
}