using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Frontend.WebApp.Features.Mvc.PublicApi;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PublicApi;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

[ExcludeFromCodeCoverage]
public abstract class PublicApiControllerTest<TController> : IntegrationTest
	where TController : PublicApiController
{
	protected TController? ControllerInstance;
	
	/// <summary>
	/// Constructor.
	/// 
	/// The _controllerInstance field must be assigned in the constructor of the derived test class.
	/// </summary>
	/// <param name="fixture"></param>
	/// <param name="additionalServiceInjection"></param>
	/// <param name="initStorage"></param>
	protected PublicApiControllerTest(DatabaseFixture fixture, Action<IServiceCollection>? additionalServiceInjection = null, bool initStorage = false) : base(fixture, additionalServiceInjection, initStorage)
	{
		
	}
}