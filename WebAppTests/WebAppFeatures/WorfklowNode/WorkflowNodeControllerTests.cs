using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Workflow;
using Levelbuild.Frontend.WebApp.Features.WorkflowNode.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.WorfklowNode;

[ExcludeFromCodeCoverage]
public class PostgresWorkflowNodeControllerTests(PostgresDatabaseFixture fixture) : WorkflowNodeControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

public abstract class WorkflowNodeControllerTests : AdminControllerTest<WorkflowNodeController, WorkflowNodeEntity, WorkflowNodeDto>
{
	#region Test Data Properties

	protected override WorkflowNodeDto CreateDto
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
			var dataSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														 currentCustomer!.Id, syncSysFields: false);
			var workflow = EntityCreation.CreateWorkflow(databaseContext, dataSource);

			return new WorkflowNodeDto()
			{
				WorkflowId = workflow.Id,
				Name = "TestNode",
				Icon = "bug",
				State = WorkflowNodeState.InProgress,
				Sorting = 1
			};
		}
	}

	protected override WorkflowNodeDto InvalidCreateDto => new()
	{
		WorkflowId = Guid.Empty,
		Name = string.Empty,
		Icon = "bug",
		State = WorkflowNodeState.InProgress,
		Sorting = 1
	};

	protected override WorkflowNodeDto InvalidUpdateDto => new()
	{
		WorkflowId = Guid.Empty,
		Name = "TestNode",
		Icon = "start",
		State = WorkflowNodeState.InProgress,
		Sorting = 0
	};

	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		},
		Limit = 2,
		Offset = 1
	};

	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "sorting",
				Direction = SortDirection.Desc
			},
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "TestNode2"
			}
		}
	};

	#endregion

	protected WorkflowNodeControllerTests(DatabaseFixture fixture) : base(fixture, initStorage: true)
	{
		ControllerInstance = new WorkflowNodeController(LogManager, Fixture.ContextFactory, UserManager, LocalizerFactory, VersionReader);
	}

	#region Data Preparation	
	
	protected override WorkflowNodeEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entity = databaseContext.WorkflowNodes.Add(WorkflowNodeEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();

		return entity.Entity;
	}
	
	private WorkflowNodeEntity PrepareStartNode()
	{
		using var databaseContext = Fixture.Context;
		var createDto = CreateDto;
		createDto.State = WorkflowNodeState.Start;
		var entity = databaseContext.WorkflowNodes.Add(WorkflowNodeEntity.FromDto(createDto, null, databaseContext));
		databaseContext.SaveChanges();

		return entity.Entity;
	}

	protected override WorkflowNodeDto GetUpdateDto(WorkflowNodeEntity entity)
	{
		var dto = entity.ToDto();
		dto.Name = "UpdatedName";
		dto.Icon = "check";
		dto.State = WorkflowNodeState.Positive;
		dto.Sorting = 2;
		return dto;
	}

	protected override async Task PrepareQueryDataAsync()
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		var dataSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id, syncSysFields: false);
		var workflow = EntityCreation.CreateWorkflow(databaseContext, dataSource);
		
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000001"),
			WorkflowId = workflow.Id,
			Name = "TestNode1",
			State = WorkflowNodeState.Start,
			Sorting = 0
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000002"),
			WorkflowId = workflow.Id,
			Name = "TestNode2",
			State = WorkflowNodeState.InProgress,
			Sorting = 1
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000003"),
			WorkflowId = workflow.Id,
			Name = "TestNode3",
			State = WorkflowNodeState.Positive,
			Sorting = 2
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000003"),
			WorkflowId = workflow.Id,
			Name = "TestNode4",
			State = WorkflowNodeState.Negative,
			Sorting = 3
		});

		foreach (var dto in QueryData)
		{
			databaseContext.WorkflowNodes.Add(WorkflowNodeEntity.FromDto(dto, null, databaseContext));
		}
		
		await databaseContext.SaveChangesAsync();
	}

	#endregion
	
	#region Assertions
	
	protected override void AssertIsAsExpected(WorkflowNodeDto expected, WorkflowNodeDto actual)
	{
		Assert.Equal(expected.WorkflowId, actual.WorkflowId);
		Assert.Equal(expected.Name, actual.Name);
		Assert.Equal(expected.Icon, actual.Icon);
		Assert.Equal(expected.State, actual.State);
		Assert.Equal(expected.Sorting, actual.Sorting);
		Assert.Equal(expected.CustomerId, actual.CustomerId);
	}

	protected override void AssertExists(WorkflowNodeDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.WorkflowNodes.Any(node => node.Name == dto.Name && node.WorkflowId == dto.WorkflowId));
	}

	protected override void AssertDoesNotExist(WorkflowNodeDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.WorkflowNodes.All(node => node.Name != dto.Name ||
															  (node.Name == dto.Name && node.WorkflowId != dto.WorkflowId)));
	}

	protected override bool DeleteSuccessful(WorkflowNodeEntity entity)
	{
		using var databaseContext = Fixture.Context;
		return databaseContext.WorkflowNodes.Find(entity.Id) == null;
	}

	protected override bool CheckQueryResult(ConfigQueryResultDto<WorkflowNodeDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows
			.ToList()
			.ConvertAll(node => node.Name);
		var expectedList = new List<string>();
		for (var i = offset; i < count && (expectedList.Count < limit || limit == 0); i++)
		{
			expectedList.Add(QueryData[i].Name!);
		}

		if (queryResultDto.CountTotal != count)
			return false;
		if (limit > 0 && queryResultDto.Rows.Count > limit)
			return false;

		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i] != mutatedNamesList[i])
				return false;
		}

		return true;
	}

	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<WorkflowNodeDto> queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows
			.ToList()
			.ConvertAll(node => node.Name);
		var expectedList = QueryData
			.OrderByDescending(node => node.Sorting)
			.ThenBy(node => node.Name)
			.ToList();

		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;

		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i].Name != mutatedNamesList[i])
				return false;
		}

		return true;
	}

	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<WorkflowNodeDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;

		return QueryData[1].Name == queryResultDto.Rows.FirstOrDefault()?.Name;
	}
	
	#endregion

	#region Tests

	[Trait("Category", "WorkflowNodeController Tests")]
	[Fact(DisplayName = "Update state of StartNode (must fail!)")]
	public async Task UpdateStartNodeStateTest()
	{
		var entity = PrepareStartNode();
		var updateDto = GetUpdateDto(entity);
		
		var result = await ControllerInstance!.Update(entity.Id, updateDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		
		Assert.NotNull(result);
		Assert.Equal(400, code);
	}

	#endregion
}