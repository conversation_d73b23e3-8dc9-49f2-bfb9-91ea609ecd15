using Google.Api.Gax;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Workflow;
using Levelbuild.Frontend.WebApp.Features.Workflow.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.Workflow;

[ExcludeFromCodeCoverage]
public class PostgresPublicWorkflowControllerTests(PostgresDatabaseFixture fixture) : PublicWorkflowControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

public abstract class PublicWorkflowControllerTests : IntegrationTest
{
	private PublicWorkflowController _controllerInstance;

	private WorkflowNodeEntity _activeWorkflowNode;
	
	public PublicWorkflowControllerTests(DatabaseFixture fixture) :
		base(fixture, initStorage: true)
	{
		_controllerInstance = new PublicWorkflowController(LogManager, VersionReader, DeepZoomHelperService, Fixture.ContextFactory, LocalizerFactory);
	}

	#region DataPreparation

	private WorkflowEntity PrepareWorkflow()
	{
		var databaseContext = Fixture.Context;
		var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
		var dataSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														 currentCustomer!.Id, syncSysFields: false);
		var workflow = EntityCreation.CreateWorkflow(databaseContext, dataSource);
		EntityCreation.CreateWorkflowNode(databaseContext, workflow, "Start");
		_activeWorkflowNode = EntityCreation.CreateWorkflowNode(databaseContext, workflow, "InProgress", WorkflowNodeState.InProgress);
		EntityCreation.CreateWorkflowNode(databaseContext, workflow, "Positive", WorkflowNodeState.Positive);
		EntityCreation.CreateWorkflowNode(databaseContext, workflow, "Negative", WorkflowNodeState.Negative);
		
		return databaseContext.Workflows
			.Include(wf => wf.Nodes)
			.Single(wf => wf.Id == workflow.Id);
	}

	private string PrepareElement()
	{
		var databaseContext = Fixture.Context;
		var dataSource = databaseContext.DataSources.First();

		var successInfo = dataSource.CreateElement(new DataStoreElementData
		{
			Values = new Dictionary<string, object?>()
			{
				{ "ExampleWorkflow_WorkflowStatus", _activeWorkflowNode.Id }	
			},
			Groups = new List<string> { "testgroup" }
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.System, -1, ""));
		return successInfo.ElementId;
	}

	#endregion
	
	#region Tests

	[Fact(DisplayName = "Test get current workflow state")]
	public void TestGetCurrentWorkflowState()
	{
		var databaseContext = Fixture.Context;
		var workflow = PrepareWorkflow();
		var elementId = PrepareElement();
		
		var result = _controllerInstance.GetCurrentWorkflowState(workflow.Id, elementId);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(objectResult.StatusCode, 200);

		var actual = (objectResult.Value as FrontendResponse<WorkflowNodeDto>)!.Data;
		Assert.NotNull(actual);
		
		var expected = databaseContext.WorkflowNodes
			.Single(node => node.WorkflowId == workflow.Id && node.State == WorkflowNodeState.InProgress)
			.ToDto();
		Assert.NotNull(expected);
		Assert.Equal(expected.Id, actual.Id);
		Assert.Equal(expected.WorkflowId, actual.WorkflowId);
		Assert.Equal(expected.Name, actual.Name);
		Assert.Equal(expected.State, actual.State);
		Assert.Equal(expected.Icon, actual.Icon);
	}

	[Fact(DisplayName = "Test change workflow state")]
	public void TestChangeWorkflowStatus()
	{
		var databaseContext = Fixture.Context;
		var workflow = PrepareWorkflow();
		var elementId = PrepareElement();
		var expectedNode = databaseContext.WorkflowNodes
			.Single(node => node.WorkflowId == workflow.Id && node.State == WorkflowNodeState.Positive);
		
		var result = _controllerInstance.ChangeWorkflowStatus(workflow.Id, elementId, expectedNode.Id);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(objectResult.StatusCode, 200);
		
		// Check if change really happened
		result = _controllerInstance.GetCurrentWorkflowState(workflow.Id, elementId);
		Assert.NotNull(result);
		
		objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(objectResult.StatusCode, 200);

		var actual = (objectResult.Value as FrontendResponse<WorkflowNodeDto>)!.Data;
		Assert.NotNull(actual);
		
		var expected = expectedNode.ToDto();
		Assert.NotNull(expected);
		Assert.Equal(expected.Id, actual.Id);
		Assert.Equal(expected.WorkflowId, actual.WorkflowId);
		Assert.Equal(expected.Name, actual.Name);
		Assert.Equal(expected.State, actual.State);
		Assert.Equal(expected.Icon, actual.Icon);
	}

	#endregion
}