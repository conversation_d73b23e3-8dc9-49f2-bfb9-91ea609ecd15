using JetBrains.Annotations;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.Extensions;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.Extensions;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class QueryableExtensionsTests
{
	private class Tag
	{
		public int Position { get; set; }
		public int Id { get; set; }
		public string Name { get; set; }
	};

	private class SimpleTag
	{
		public string Name { get; set; }
	}


	private readonly List<Tag> _testCollection = new()
	{
		new Tag { Position = 0, Id = 4, Name = "foo" },
		new Tag { Position = 1, Id = 3, Name = "bar" },
		new Tag { Position = 2, Id = 2, Name = "foo" },
		new Tag { Position = 3, Id = 1, Name = "blub" },
		new Tag { Position = 0, Id = 0, Name = "bla" },
	};

	private readonly List<SimpleTag> _testCollectionSimple = new()
	{
		new SimpleTag { Name = "foo" },
		new SimpleTag { Name = "bar" },
		new SimpleTag { Name = "foo" },
		new SimpleTag { Name = "blub" },
		new SimpleTag { Name = "bla" },
	};

	private readonly IList<IDictionary<string, string>> _testCollectionCrude = new List<IDictionary<string, string>>()
	{
		new Dictionary<string, string>() { { "a", "1" } },
		new Dictionary<string, string>() { { "a", "2" } },
		new Dictionary<string, string>() { { "a", "3" } },
	};

	#region Sorting

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Sorts a list by Id")]
	public void WithSortingByDefaultTest()
	{
		var orderedList = _testCollection.AsQueryable().WithSorting().ToList();

		Assert.Equal(orderedList[0], _testCollection[4]);
		Assert.Equal(orderedList[1], _testCollection[3]);
		Assert.Equal(orderedList[2], _testCollection[2]);
		Assert.Equal(orderedList[3], _testCollection[1]);
		Assert.Equal(orderedList[4], _testCollection[0]);
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Sorts a list by its name desc")]
	public void WithSortingByNameTest()
	{
		var orderedList = _testCollection.AsQueryable().WithSorting(new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Desc
			}
		}).ToList();

		Assert.Equal(orderedList[0], _testCollection[2]);
		Assert.Equal(orderedList[1], _testCollection[0]);
		Assert.Equal(orderedList[2], _testCollection[3]);
		Assert.Equal(orderedList[3], _testCollection[4]);
		Assert.Equal(orderedList[4], _testCollection[1]);
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Sorts a list by its position desc and name asc")]
	public void WithSortingByMultipleFieldsTest()
	{
		var orderedList = _testCollection.AsQueryable().WithSorting(new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "position",
				Direction = SortDirection.Desc
			},
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}).ToList();

		Assert.Equal(orderedList[0], _testCollection[3]);
		Assert.Equal(orderedList[1], _testCollection[2]);
		Assert.Equal(orderedList[2], _testCollection[1]);
		Assert.Equal(orderedList[3], _testCollection[4]);
		Assert.Equal(orderedList[4], _testCollection[0]);
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Don't sort a simple list")]
	public void WithSortingByDefaultSimpleTest()
	{
		var orderedList = _testCollectionSimple.AsQueryable().WithSorting().ToList();

		Assert.Equal(orderedList[0], _testCollectionSimple[0]);
		Assert.Equal(orderedList[1], _testCollectionSimple[1]);
		Assert.Equal(orderedList[2], _testCollectionSimple[2]);
		Assert.Equal(orderedList[3], _testCollectionSimple[3]);
		Assert.Equal(orderedList[4], _testCollectionSimple[4]);
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Sorts a simple list by its name desc")]
	public void WithSortingByNameSimpleTest()
	{
		var orderedList = _testCollectionSimple.AsQueryable().WithSorting(new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Desc
			}
		}).ToList();

		Assert.Equal(orderedList[0].Name, _testCollectionSimple[2].Name);
		Assert.Equal(orderedList[1].Name, _testCollectionSimple[0].Name);
		Assert.Equal(orderedList[2].Name, _testCollectionSimple[3].Name);
		Assert.Equal(orderedList[3].Name, _testCollectionSimple[4].Name);
		Assert.Equal(orderedList[4].Name, _testCollectionSimple[1].Name);
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Sorts a simple list by its invalid position desc and name asc")]
	public void WithSortingByMultipleFieldsSimpleTest()
	{
		try
		{
			_testCollectionSimple.AsQueryable().WithSorting(new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "position",
					Direction = SortDirection.Desc
				},
				new()
				{
					OrderColumn = "name",
					Direction = SortDirection.Asc
				}
			}).ToList();
		}
		catch (Exception e)
		{
			Assert.IsType<ArgumentException>(e);
		}
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Skips two dictionary list items")]
	public void WithSkipByACrudeTest()
	{
		var orderedList = _testCollectionCrude.AsQueryable().WithQueryParams(new QueryParamsDto()
		{
			Limit = 1,
			Offset = 2
		}).ToList();

		Assert.Equal(orderedList[0], _testCollectionCrude[2]);
	}

	#endregion

	#region Filtering

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Filters a list by int")]
	public void WithFilterByIntTest()
	{
		var filteredList = _testCollection.AsQueryable().WithFiltering(new List<QueryParamFilterDto>()
		{
			new()
			{
				FilterColumn = "id",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = 0
			}
		}).ToList();

		Assert.Single(filteredList);
		Assert.Equal(_testCollection[4], filteredList[0]);
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Filters a list by string")]
	public void WithFilterByStringTest()
	{
		var filteredList = _testCollection.AsQueryable().WithFiltering(new List<QueryParamFilterDto>()
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "foo"
			}
		}).ToList();

		Assert.Equal(2, filteredList.Count);
		Assert.Equal(_testCollection[0], filteredList[0]);
		Assert.Equal(_testCollection[2], filteredList[1]);
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Filters without a filter")]
	public void WithoutFilterTest()
	{
		var filteredList = _testCollection.AsQueryable().WithFiltering(new List<QueryParamFilterDto>()).ToList();
		Assert.Equal(5, filteredList.Count);
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Filters without a valid filter")]
	public void WithFilterWhichAreInvalidTest()
	{
		try
		{
			_testCollection.AsQueryable().WithFiltering(new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "fakeName",
					Operator = QueryParamFilterOperator.IsNull
				}
			});
			Assert.Fail("Argument exception was NOT thrown thrown");
		}
		catch (Exception e)
		{
			Assert.IsType<ArgumentException>(e);
		}
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Filters a list by multiple filters")]
	public void WithFilterMultipleTest()
	{
		var filteredList = _testCollection.AsQueryable().WithFiltering(new List<QueryParamFilterDto>()
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "foo"
			},
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "bar"
			},
			new()
			{
				FilterColumn = "position",
				Operator = QueryParamFilterOperator.GreaterThan,
				CompareValue = 0
			},
			new()
			{
				FilterColumn = "position",
				Operator = QueryParamFilterOperator.LessThan,
				CompareValue = 3
			},
			new()
			{
				FilterColumn = "position",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = 3
			}
		}).ToList();

		Assert.Equal(2, filteredList.Count);
		Assert.Equal(_testCollection[1], filteredList[0]);
		Assert.Equal(_testCollection[2], filteredList[1]);
	}

	#endregion

	#region QueryParams

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Query a list with nullable params")]
	public void WithNullableQueryParamsTest()
	{
		#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
		Assert.Throws<ArgumentNullException>(() => _testCollection.AsQueryable().WithQueryParams(null).ToList());
		#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
	}

	[Trait("Category", "QueryableExtensions Tests")]
	[Fact(DisplayName = "Query a list with all params")]
	public void WithAllQueryParamsTest()
	{
		var filtering = new List<QueryParamFilterDto>
		{
			new()
			{
				FilterColumn = "position",
				Operator = QueryParamFilterOperator.GreaterThanEquals,
				CompareValue = 1
			}
		};

		var sorting = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		};

		var orderedList = _testCollection.AsQueryable().WithQueryParams(new QueryParamsDto()
		{
			Sortings = sorting,
			Filters = filtering,
			Limit = 1,
			Offset = 1
		}).ToList();

		Assert.Single(orderedList);
		Assert.Equal(_testCollection[3], orderedList[0]);
	}

	#endregion
}