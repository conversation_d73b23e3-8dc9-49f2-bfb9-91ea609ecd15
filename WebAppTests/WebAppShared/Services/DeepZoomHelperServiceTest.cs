using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Domain.WebAppTests.WebAppFeatures;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DeepZoom;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.Services;

[ExcludeFromCodeCoverage]
public class PostgresDeepZoomHelperServiceTest(PostgresDatabaseFixture fixture) : DeepZoomHelperServiceTest(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class DeepZoomHelperServiceTest : IntegrationTest
{
	private readonly CoreDatabaseContext _deepZoomHelperServiceContext;
	
	protected DeepZoomHelperServiceTest(DatabaseFixture fixture, Action<IServiceCollection>? additionalServiceInjection = null, bool initStorage = true) : base(fixture, additionalServiceInjection, initStorage)
	{
		_deepZoomHelperServiceContext = Fixture.Context;
	}

	#region DataPreparation

	private async Task<DataSourceEntity> PrepareDataSourceAsync()
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 (await UserManager.GetCurrentCustomerAsync()).Id, syncSysFields: true);
		EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id,
									   "TestField", true, source.Name);
		return source;
	}
	
	private string AddFileToDataSource(DataSourceEntity dataSource)
	{
		var pdfFile = File.OpenRead("../../../test.pdf");
		var file = new FormFile(pdfFile, 0, pdfFile.Length, pdfFile.Name, pdfFile.Name);
		
		var storeFileStream = new DataStoreFileStream(file.FileName, DateTime.Now.ToUniversalTime(), file.OpenReadStream(), file.Length);
		return dataSource.UploadFile(storeFileStream);
	}

	#endregion

	#region Tests
	
	[Fact(DisplayName = "Cache DeepZoomHelperService")]
	public async Task TestCacheDeepZoomHelper()
	{
		var source = await PrepareDataSourceAsync();
		var fileId = AddFileToDataSource(source);
		var dpi = 200;
		var result = await DeepZoomHelperService.GetAndCacheDeepZoomImageAsync(_deepZoomHelperServiceContext, fileId, source, dpi);
		
		Assert.NotNull(result);
		Assert.Equal($"{fileId}_{dpi}", result.CachedFileId);
		Assert.Equal(fileId, result.FileId);
		Assert.Equal(dpi, result.Dpi);
		Assert.True((DateTime.Now - result.LastTouched).TotalMinutes < 2);
	}
	
	[Fact(DisplayName = "Get DeepZoomHelperService")]
	public async Task TestGetDeepZoomHelper()
	{
		var source = await PrepareDataSourceAsync();
		var fileId = AddFileToDataSource(source);
		var dpi = 200;
		await DeepZoomHelperService.GetAndCacheDeepZoomImageAsync(_deepZoomHelperServiceContext, fileId, source, dpi);
		var subFile = await DeepZoomHelperService.GetDeepZoomImageFileAsync(_deepZoomHelperServiceContext, fileId, "0", "0_0.webp", dpi);
		
		Assert.NotNull(subFile);
	}
	
	[Fact(DisplayName = "Delete DeepZoomHelperService")]
	public async Task TestDeleteDeepZoomHelper()
	{
		var source = await PrepareDataSourceAsync();
		var fileId = AddFileToDataSource(source);
		var dpi = 200;
		var result = await DeepZoomHelperService.GetAndCacheDeepZoomImageAsync(_deepZoomHelperServiceContext, fileId, source, dpi);
		// add entity without any files
		_deepZoomHelperServiceContext.DeepZoomImages.Add(new CachedDeepZoomEntity()
		{
			FileId = fileId,
			CachedFileId = "bla",
		});
		await _deepZoomHelperServiceContext.SaveChangesAsync();
		
		await DeepZoomHelperService.DeleteDeepZoomImagesAsync(_deepZoomHelperServiceContext, fileId);
		var subFile = await DeepZoomHelperService.GetDeepZoomImageFileAsync(_deepZoomHelperServiceContext, fileId, "0", "0_0.webp", dpi);
		
		Assert.Null(subFile);
		Assert.Equal(0, _deepZoomHelperServiceContext.DeepZoomImages.Count());
	}
	
	[Fact(DisplayName = "Cancel Delete DeepZoomHelperService")]
	public async Task TestCancelDeleteDeepZoomHelper()
	{
		var source = await PrepareDataSourceAsync();
		var fileId = AddFileToDataSource(source);
		var dpi = 200;
		await DeepZoomHelperService.GetAndCacheDeepZoomImageAsync(_deepZoomHelperServiceContext, fileId, source, dpi);
		var cancellationTokenSource = new CancellationTokenSource();
		var cancellationToken = cancellationTokenSource.Token;
		var task = DeepZoomHelperService.DeleteDeepZoomImagesAsync(_deepZoomHelperServiceContext, fileId, cancellationToken);
		await cancellationTokenSource.CancelAsync();
		await task;
	}
	
	#endregion
	
	public new void Dispose()
	{
		_deepZoomHelperServiceContext.Dispose();
		base.Dispose();
	}
}