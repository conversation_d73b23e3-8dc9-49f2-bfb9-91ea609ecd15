using System.Net;
using Levelbuild.Frontend.WebApp.Shared.Services.Files;
using Microsoft.Extensions.Configuration;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.Services;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class ThumbnailMicroserviceTest
{
	private ThumbnailMicroservice _thumbnailMicroservice;
	
	public ThumbnailMicroserviceTest()
	{
		var config = new ConfigurationBuilder()
			.SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
			.AddJsonFile("testsettings.json")
			.Build()
			.GetSection("WebApp")
			.GetSection("ThumbnailMicroservice");
		
		var inMemorySettings = new Dictionary<string, string> {
			{"ThumbnailMicroService:Url", config.GetSection("Url").Value!},
		};
		
		IConfiguration configuration = new ConfigurationBuilder()
			.AddInMemoryCollection(inMemorySettings!)
			.Build();
		
		_thumbnailMicroservice = new ThumbnailMicroservice(configuration);
	}
	
	[Fact(DisplayName = "Get Thumbnail")]
	public async Task TestGetThumbnail()
	{
		var pngFile = File.OpenRead("../../../testImage.png");
		var thumbnailResponse = await _thumbnailMicroservice.GetThumbnail(pngFile, pngFile.Name, 100, 100);
		
		Assert.NotNull(thumbnailResponse);
		Assert.Equal(HttpStatusCode.OK, thumbnailResponse.StatusCode);
		Assert.NotNull(thumbnailResponse.Content);
	}
	
	[Fact(DisplayName = "Get Thumbnail invalid file type")]
	public async Task TestGetThumbnailInvalidFileType()
	{
		var txtFile = File.OpenRead("../../../testText.txt");
		var thumbnailResponse = await _thumbnailMicroservice.GetThumbnail(txtFile, txtFile.Name, 100, 100);
		
		Assert.NotNull(thumbnailResponse);
		Assert.Equal(HttpStatusCode.UnsupportedMediaType, thumbnailResponse.StatusCode);
	}
}