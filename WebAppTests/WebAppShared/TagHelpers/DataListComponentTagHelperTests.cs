using System.Text.Json;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;
#pragma warning disable CS0618 // Type or member is obsolete

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class DataListComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public DataListComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new DataListComponentTagHelper();
	}

	[Trait("Category", "DataListComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToDataList()
	{
		var filters = new List<QueryParamFilterDto>
		{
			new() { FilterColumn = "Column1", Operator = QueryParamFilterOperator.Equals, CompareValue = "Bazinga" },
			new() { FilterColumn = "Column2", Operator = QueryParamFilterOperator.In, CompareValue = "Joke" }
		};
		var sortings = new List<QueryParamSortingDto>
		{
			new() { Direction = SortDirection.Asc, OrderColumn = "Column1" }
		};
		var rows = new List<Dictionary<string, object>>
		{
			new()
			{
				{ "ID", "abcd-1234" },
				{ "Number", 1234 },
				{ "Name", "Otto" },
			},
			new()
			{
				{ "ID", "vwxz-7890" },
				{ "Number", 1597 },
				{ "Name", "Boris" },
			},
			new()
			{
				{ "ID", "azyb-1057" },
				{ "Number", 7842 },
				{ "Name", "Linda" },
			}
		};

		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("IdentityColumn", "ThisColumn"),
			TagHelperTestParameter.GetInstance("Offset", 15, 15),
			TagHelperTestParameter.GetInstance("Url", "localhost:1234"),
			TagHelperTestParameter.GetInstance("Limit", 25),
			TagHelperTestParameter.GetInstance("Paging", true),
			TagHelperTestParameter.GetInstance("Selectable", true),
			TagHelperTestParameter.GetInstance("Responsive", true),
			TagHelperTestParameter.GetInstance("Searchbar", true),
			TagHelperTestParameter.GetInstance("GroupBy", "name"),
			TagHelperTestParameter.GetInstance("LabelColumn", "LabelColumn"),
			TagHelperTestParameter.GetTestSeparatelyInstance("Width", "50%"),
			TagHelperTestParameter.GetTestSeparatelyInstance("Height", "75%"),
			TagHelperTestParameter.GetInstance("Sortings", sortings, JsonSerializer.Serialize(sortings, ConfigHelper.JsonOptionsCamel)),
			TagHelperTestParameter.GetInstance("Filters", filters, JsonSerializer.Serialize(filters, ConfigHelper.JsonOptionsCamel)),
			TagHelperTestParameter.GetInstance("Rows", rows, JsonSerializer.Serialize(rows, ConfigHelper.JsonOptionsCamel)),
		};

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-data-list", _output.TagName);
		Assert.Equal("width: 50%; height: 75%;", _output.Attributes["style"].Value);
	}

	[Trait("Category", "DataListComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}