using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class DropdownComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public DropdownComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new DropdownComponentTagHelper();
	}

	[Trait("Category", "DropdownComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToInput()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetTestSeparatelyInstance("PopupPlacement", PopupPlacement.LeftStart),
			TagHelperTestParameter.GetInstance("Name", "ThisDropdownName"),
			TagHelperTestParameter.GetInstance("Flip", true),
			TagHelperTestParameter.GetInstance("Shift", true),
			TagHelperTestParameter.GetInstance("Orbit", true),
			TagHelperTestParameter.GetInstance("KeepOpen", true),
			TagHelperTestParameter.GetInstance("Border", "1px solid white"),
			TagHelperTestParameter.GetInstance("BorderRadius", "1px 2px"),
			TagHelperTestParameter.GetAlternativeInstance("BlockOffsetX", "offset-block-x", 4),
			TagHelperTestParameter.GetAlternativeInstance("BlockOffsetY", "offset-block-y", 4),
			TagHelperTestParameter.GetTestSeparatelyInstance("BoxShadow", Intensity.Medium),
		};

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-dropdown", _output.TagName);
		Assert.Equal("left-start", _output.Attributes["placement"]?.Value);
	}

	[Trait("Category", "InputComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}