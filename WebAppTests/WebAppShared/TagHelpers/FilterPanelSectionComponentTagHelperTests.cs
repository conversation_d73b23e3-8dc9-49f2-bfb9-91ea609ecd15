using System.Text.Json;
using Humanizer;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class FilterPanelSectionComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public FilterPanelSectionComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new FilterPanelSectionComponentTagHelper();
	}

	[Trait("Category", "FilterPanelSectionComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToFilterPanelSection()
	{
		var possibleValues = new List<FilterFieldQueryItemResultDto>
		{
			new() { Value = "Bla", Label = "Blub", Count = 1},
			new() { Value = "Schlumpf", Label = "Schlumpfbeere", Count = 1},
		};
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Name", "section"),
			TagHelperTestParameter.GetInstance("Label", "Section"),
			TagHelperTestParameter.GetInstance("Type", InputDataType.String, InputDataType.String.GetTypeAsString()),
			TagHelperTestParameter.GetAlternativeInstance("MultiValue", "multi-value", true),
			TagHelperTestParameter.GetInstance("Placeholder", "Is a section"),
			TagHelperTestParameter.GetInstance("DecimalPlaces", 2),
			TagHelperTestParameter.GetInstance("FormatType", DataFieldFormatType.CustomText, DataFieldFormatType.CustomText.GetDisplayName().Camelize()),
			TagHelperTestParameter.GetInstance("LabelTrue", "true"),
			TagHelperTestParameter.GetInstance("LabelFalse", "false"),
			TagHelperTestParameter.GetAlternativeInstance("ValuePreview", "value-preview", true),
			TagHelperTestParameter.GetAlternativeInstance("IsMultiField", "is-multi-field", true),
			TagHelperTestParameter.GetInstance("Url", "url"),
			TagHelperTestParameter.GetInstance("Hidden", true),
			TagHelperTestParameter.GetInstance("PossibleValues", possibleValues, JsonSerializer.Serialize(possibleValues, ConfigHelper.JsonOptionsCamel))
		};

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-filter-panel-section", _output.TagName);
	}

	[Trait("Category", "FilterPanelSectionComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}