using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class InputComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;
	
	private TagHelperOutput _output;
	
	private readonly TagHelper _tagHelper;
	
	public InputComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new InputComponentTagHelper();
	}
	
	[Trait("Category", "InputComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToInput()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Name", "GoodName"),
			TagHelperTestParameter.GetInstance("Label", "The best Label"),
			TagHelperTestParameter.GetInstance("Value", "Greatest Value"),
			TagHelperTestParameter.GetInstance("Sign", "-"),
			TagHelperTestParameter.GetInstance("TextAlign", Alignment.Center, Alignment.Center.GetAlignmentAsString()),
			TagHelperTestParameter.GetInstance("Type", InputDataType.Integer, InputDataType.Integer.GetTypeAsString()),
			TagHelperTestParameter.GetInstance("Required", true),
			TagHelperTestParameter.GetInstance("Readonly", true),
			TagHelperTestParameter.GetInstance("Placeholder", "Holding the Place"),
			TagHelperTestParameter.GetInstance("Tooltip", "Hidden Tooltip"),
			TagHelperTestParameter.GetInstance("Error", "Wrong Error"),
			TagHelperTestParameter.GetInstance("NegativeOperators", true),
			TagHelperTestParameter.GetInstance("IgnoreTimezone", true),
			TagHelperTestParameter.GetInstance("CompareOperator", CompareOperator.GreaterThanEquals, CompareOperator.GreaterThanEquals.GetDisplayName().ToLower())
		};
		
		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);
		
		Assert.Equal("lvl-input", _output.TagName);
	}
	
	[Trait("Category", "InputComponentTagHelperTests")]
	[Fact(DisplayName = "Mismatching Compare Operator should default to Equals")]
	public void WriteMismatchingCompareOperator()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Type", InputDataType.Integer, InputDataType.Integer.GetTypeAsString()),
			TagHelperTestParameter.GetInstance("CompareOperator", CompareOperator.Like, CompareOperator.Equals.GetDisplayName().ToLower())
		};
		
		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);
	}
	
	[Trait("Category", "InputComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}