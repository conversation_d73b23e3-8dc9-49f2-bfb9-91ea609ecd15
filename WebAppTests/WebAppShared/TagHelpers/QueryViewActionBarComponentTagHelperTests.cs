using System.Text.Json;
using Levelbuild.Frontend.WebApp.Shared;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class QueryViewActionBarComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;
	
	private TagHelperOutput _output;
	
	private readonly TagHelper _tagHelper;
	
	public QueryViewActionBarComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new QueryViewActionBarComponentTagHelper();
	}
	
	[Trait("Category", "QueryViewActionBarComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToInput()
	{
		IList<SelectListItemDefinition> views = [
			new SelectListItemDefinition("table", "tableKey", "tableIcon"), 
			new SelectListItemDefinition("grid", "gridKey", "gridIcon")
		];
		
		List<TagHelperTestParameter> arguments = [
			TagHelperTestParameter.GetInstance("Disabled", true),
			TagHelperTestParameter.GetInstance("WithSelectAll", true),
			TagHelperTestParameter.GetInstance("WithFavoriteAction", true),
			TagHelperTestParameter.GetInstance("WithInactiveAction", true),
			TagHelperTestParameter.GetInstance("WithSearch", true),
			TagHelperTestParameter.GetInstance("WithColumns", true),
			TagHelperTestParameter.GetInstance("WithDisplayType", true),
			TagHelperTestParameter.GetInstance("WithCreate", true),
			TagHelperTestParameter.GetInstance("Embedded", true),
			TagHelperTestParameter.GetInstance("DefaultView", "table"),
			TagHelperTestParameter.GetInstance("Views", views, JsonSerializer.Serialize(views, ConfigHelper.JsonOptionsCamel))
		];
		
		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);
		
		Assert.Equal("lvl-query-view-action-bar", _output.TagName);
	}
	
	[Trait("Category", "QueryViewActionBarComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}