using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class RichTextComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;
	
	private TagHelperOutput _output;
	
	private readonly TagHelper _tagHelper;
	
	public RichTextComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new RichTextComponentTagHelper();
	}
	
	[Trait("Category", "RichTextComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributes()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Name", "GoodName"),
			TagHelperTestParameter.GetInstance("Label", "The best Label"),
			TagHelperTestParameter.GetInstance("Value", "maxValue"),
			TagHelperTestParameter.GetAlternativeInstance("MinimumSize", "size", Size.Medium, Size.Medium.GetSizeAsString()),
			TagHelperTestParameter.GetInstance("Required", true),
			TagHelperTestParameter.GetInstance("Readonly", true),
			TagHelperTestParameter.GetInstance("Placeholder", "Holding the Place"),
		};
		
		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);
		
		Assert.Equal("lvl-rich-text", _output.TagName);
	}
	
	[Trait("Category", "RichTextComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void WriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}