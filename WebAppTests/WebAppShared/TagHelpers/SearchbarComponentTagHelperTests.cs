using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class SearchbarComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;
	
	private TagHelperOutput _output;
	
	private readonly TagHelper _tagHelper;
	
	public SearchbarComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new SearchbarComponentTagHelper();
	}
	
	[Trait("Category", "SearchbarComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributes()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetAlternativeInstance("Size", "size", FontSize.Medium, FontSize.Medium.GetFontSizeAsString()),
			TagHelperTestParameter.GetInstance("Disabled", true),
			TagHelperTestParameter.GetInstance("Rounded", true),
			TagHelperTestParameter.GetInstance("WithSorting",  true),
			TagHelperTestParameter.GetInstance("MainBar",  true),
		};
		
		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);
		
		Assert.Equal("lvl-search", _output.TagName);
	}
	
	[Trait("Category", "SearchbarComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void WriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}