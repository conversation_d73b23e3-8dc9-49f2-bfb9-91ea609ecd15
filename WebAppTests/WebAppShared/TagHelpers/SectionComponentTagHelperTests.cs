using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class SectionComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;
	
	private TagHelperOutput _output;
	
	private readonly TagHelper _tagHelper;
	
	public SectionComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new SectionComponentTagHelper()
		{
			Heading = "Test"
		};
	}
	
	[Trait("Category", "SectionComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToInput()
	{
		List<TagHelperTestParameter> arguments = [
			TagHelperTestParameter.GetInstance("Heading", "Test"),
			TagHelperTestParameter.GetInstance("Subtitle", "Subtext"),
			TagHelperTestParameter.GetInstance("MaxHeight", 200),
			TagHelperTestParameter.GetInstance("AllowCollapse", true),
			TagHelperTestParameter.GetInstance("Collapsed", true),
			TagHelperTestParameter.GetInstance("Hidden", true),
			TagHelperTestParameter.GetInstance("Skeleton", true)
		];
		
		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);
		
		Assert.Equal("lvl-section", _output.TagName);
	}
	
	[Trait("Category", "SectionComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}