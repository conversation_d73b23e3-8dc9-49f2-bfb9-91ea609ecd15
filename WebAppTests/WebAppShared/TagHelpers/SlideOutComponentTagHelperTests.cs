using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class SlideOutComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;
	
	private TagHelperOutput _output;
	
	private readonly TagHelper _tagHelper;
	
	public SlideOutComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new SlideOutComponentTagHelper();
	}
	
	[Trait("Category", "InputComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToInput()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Heading", "A Great Title"),
			TagHelperTestParameter.GetInstance("Icon", "fa-awesome-icon"),
			TagHelperTestParameter.GetInstance("Position", Alignment.Right, Alignment.Right.GetAlignmentAsString()),
			TagHelperTestParameter.GetInstance("Anchor", true),
			TagHelperTestParameter.GetInstance("Modal", true),
			TagHelperTestParameter.GetInstance("Open", true),
			TagHelperTestParameter.GetInstance("Width", 13.14),
			TagHelperTestParameter.GetInstance("Navigation", true),
			TagHelperTestParameter.GetInstance("PageCount", 5),
			TagHelperTestParameter.GetInstance("PageNumber", 2)
		};
		
		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);
		
		Assert.Equal("lvl-slide-out", _output.TagName);
	}
	
	[Trait("Category", "InputComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}