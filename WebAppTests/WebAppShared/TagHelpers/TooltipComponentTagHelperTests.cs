using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class TooltipComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public TooltipComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new TooltipComponentTagHelper();
	}

	[Trait("Category", "TooltipComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToToggle()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Name", "ToolTipName"),
			TagHelperTestParameter.GetTestSeparatelyInstance("Placement", PopupPlacement.Right),
			TagHelperTestParameter.GetTestSeparatelyInstance("BlockOffset", 10),
			TagHelperTestParameter.GetTestSeparatelyInstance("BlockOffsetX", 15),
			TagHelperTestParameter.GetTestSeparatelyInstance("BlockOffsetY", 20),
			TagHelperTestParameter.GetTestSeparatelyInstance("InlineOffset", 25),
			TagHelperTestParameter.GetTestSeparatelyInstance("InlineOffsetX", 30),
			TagHelperTestParameter.GetTestSeparatelyInstance("InlineOffsetY", 35)
		};

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-tooltip", _output.TagName);
		Assert.Equal("right", _output.Attributes["placement"].Value);
		Assert.Equal(10, _output.Attributes["offset-block"].Value);
		Assert.Equal(15, _output.Attributes["offset-block-x"].Value);
		Assert.Equal(20, _output.Attributes["offset-block-y"].Value);
		Assert.Equal(25, _output.Attributes["offset-inline"].Value);
		Assert.Equal(30, _output.Attributes["offset-inline-x"].Value);
		Assert.Equal(35, _output.Attributes["offset-inline-y"].Value);
	}

	[Trait("Category", "TooltipComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}