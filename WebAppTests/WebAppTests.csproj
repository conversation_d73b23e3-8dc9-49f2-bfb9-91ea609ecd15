<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>true</IsPackable>
        <IsTestProject>true</IsTestProject>
        <RootNamespace>Levelbuild.Domain.WebAppTests</RootNamespace>
        <Company>levelbuild AG</Company>
        <PackageId>levelbuild.WebAppTests</PackageId>

        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <PreserveCompilationContext>true</PreserveCompilationContext>
    </PropertyGroup>


    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    <!--
    The following part will prevent warnings to be displayed.
    MSB3277: Found conflicts between different versions of dependencies
    -->
    <PropertyGroup Condition="'$(GITLAB_CI)' == 'true'">
        <NoWarn>MSB3277</NoWarn>
    </PropertyGroup>


    <!-- for development purpose -->
    <ItemGroup>
        <Reference Include="EntityInterface">
            <HintPath>..\..\WebAppCore\EntityInterface\bin\Debug\net8.0\EntityInterface.dll</HintPath>
        </Reference>
        <Reference Include="FrontendDtos">
            <HintPath>..\..\WebAppCore\FrontendDtos\bin\Debug\net8.0\FrontendDtos.dll</HintPath>
        </Reference>
        <Reference Include="GeoDataInterface">
            <HintPath>..\..\WebAppCore\GeoDataInterface\bin\Debug\net8.0\GeoDataInterface.dll</HintPath>
        </Reference>
        <Reference Include="SharedDtos">
            <HintPath>..\..\WebAppCore\SharedDtos\bin\Debug\net8.0\SharedDtos.dll</HintPath>
        </Reference>
        <Reference Include="ZitadelApiInterface">
            <HintPath>..\..\WebAppCore\ZitadelApiInterface\bin\Debug\net8.0\ZitadelApiInterface.dll</HintPath>
        </Reference>
        <Reference Include="FileInterface">
            <HintPath>..\..\WebAppCore\FileInterface\bin\Debug\net8.0\FileInterface.dll</HintPath>
        </Reference>
        <Reference Include="StorageInterface">
            <HintPath>..\..\WebAppCore\StorageInterface\bin\Debug\net8.0\StorageInterface.dll</HintPath>
        </Reference>
        <Reference Include="DataStoreInterface">
            <HintPath>..\..\WebAppCore\DataStoreInterface\bin\Debug\net8.0\DataStoreInterface.dll</HintPath>
        </Reference>
        <Reference Include="WebAppCoreTests">
            <HintPath>..\..\WebAppCore\WebAppCoreTests\bin\Debug\net8.0\WebAppCoreTests.dll</HintPath>
        </Reference>
    </ItemGroup>


    <!-- for release purpose -->
    <ItemGroup Condition="'$(Configuration)'=='Release'">
        <PackageReference Include="levelbuild.EntityInterface" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.FrontendDtos" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.GeoDataInterface" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.SharedDtos" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.ZitadelApiInterface" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.FileInterface" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.StorageInterface" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.DataStoreInterface" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.WebAppCoreTests" Version="$(LVL_RELEASE_VERSION)"/>
    </ItemGroup>


    <!-- local solution reference -->
    <ItemGroup>
        <ProjectReference Include="..\GoogleStorageFile\GoogleStorageFile.csproj" />
        <ProjectReference Include="..\S3BucketFile\S3BucketFile.csproj"/>
        <ProjectReference Include="..\GoogleGeoData\GoogleGeoData.csproj"/>
        <ProjectReference Include="..\OsmGeoData\OsmGeoData.csproj"/>
        <ProjectReference Include="..\StorageEntities\StorageEntities.csproj"/>
        <ProjectReference Include="..\WebAppEntities\WebAppEntities.csproj" />
        <ProjectReference Include="..\WebApp\WebApp.csproj"/>
        <ProjectReference Include="..\Storage\Storage.csproj"/>
    </ItemGroup>


    <!-- TODO: What happens here? -->
    <ItemGroup>
        <None Update="testsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Storage\Test.pdf">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="WebAppFeatures\DataStoreConnectionHandling\Test.pdf">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="validTestVersion.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="invalidTestVersion.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="test.log">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>


    <ItemGroup>
        <PackageReference Include="ICG.AspNetCore.Utilities.UnitTesting.TagHelpers" Version="1.0.1"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
        <PackageReference Include="xunit" Version="2.9.2" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.4">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.msbuild" Version="6.0.4">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="JunitXml.TestLogger" Version="6.1.0"/>

        <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
        <PackageReference Include="FluentMigrator" Version="6.2.0" />
        <PackageReference Include="FluentMigrator.Runner" Version="6.2.0" />
        <PackageReference Include="FluentMigrator.Runner.SqlServer" Version="6.2.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.11" />
        <PackageReference Include="SharpCompress" Version="0.38.0" />
        <PackageReference Include="SqlKata.Execution" Version="3.0.0-beta"/>
    </ItemGroup>
</Project>
