<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <RootNamespace>Levelbuild.Domain.ZitadelApi</RootNamespace>
        <Company>levelbuild AG</Company>
        <PackageId>levelbuild.ZitadelApi</PackageId>
        
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
    </PropertyGroup>


    <!-- for development purpose -->
    <ItemGroup>
        <Reference Include="FrontendDtos">
            <HintPath>..\..\WebAppCore\FrontendDtos\bin\Debug\net8.0\FrontendDtos.dll</HintPath>
        </Reference>
        <Reference Include="SharedDtos">
            <HintPath>..\..\WebAppCore\SharedDtos\bin\Debug\net8.0\SharedDtos.dll</HintPath>
        </Reference>
        <Reference Include="ZitadelApiInterface">
            <HintPath>..\..\WebAppCore\ZitadelApiInterface\bin\Debug\net8.0\ZitadelApiInterface.dll</HintPath>
        </Reference>
    </ItemGroup>


    <!-- for release purpose -->
    <ItemGroup Condition="'$(Configuration)'=='Release'">
        <PackageReference Include="levelbuild.FrontendDtos" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.SharedDtos" Version="$(LVL_RELEASE_VERSION)"/>
        <PackageReference Include="levelbuild.ZitadelApiInterface" Version="$(LVL_RELEASE_VERSION)"/>
    </ItemGroup>


    <ItemGroup>
      <PackageReference Include="Zitadel" Version="7.0.23" />
    </ItemGroup>

</Project>
