using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using Levelbuild.Core.ZitadelApiInterface;
using Zitadel.Api;
using Zitadel.Credentials;
using Zitadel.User.V2;

namespace Levelbuild.Domain.ZitadelApi;

/// <inheritdoc cref="IZitadelUserServiceApiClient"/>
public class ZitadelUserServiceApiClient : ZitadelApiClient<UserService.UserServiceClient>, IZitadelUserServiceApiClient
{
	#region Init

	private ZitadelUserServiceApiClient(UserService.UserServiceClient client) : base(client)
	{
		// nothing...
	}

	/// <summary>
	/// Creates and returns a new instance of a Zitadel User Service API client.
	/// </summary>
	/// <param name="apiUrl">URL of the target Zitadel instance.</param>
	/// <param name="serviceAccount">The ServiceAccount to use to authenticate API calls.</param>
	public static ZitadelUserServiceApiClient GetInstance(string apiUrl, ServiceAccount serviceAccount)
	{
		var client = Clients.UserService(
			new(
				apiUrl,
				ITokenProvider.ServiceAccount(
					apiUrl,
					serviceAccount,
					new() { ApiAccess = true }
				)
			)
		);

		return new ZitadelUserServiceApiClient(client);
	}

	#endregion

	#region Methods

	#region Passkeys

	/// <inheritdoc />
	public PasskeyRegistrationCode CreatePasskeyRegistrationCode(string userId)
	{
		var response = Client.CreatePasskeyRegistrationLink(new()
		{
			UserId = userId,
			ReturnCode = new ReturnPasskeyRegistrationCode()
		});
		return response.Code;
	}
	
	/// <inheritdoc />
	public (string PasskeyId, Struct Credentials) GetPasskeyRegistrationCredentials(string userId, PasskeyRegistrationCode code, string domain)
	{
		var response = Client.RegisterPasskey(new()
		{
			UserId = userId,
			Code = code,
			Domain = domain
		});
		
		return (response.PasskeyId, response.PublicKeyCredentialCreationOptions);
	}

	/// <inheritdoc />
	public (bool Success, string? Message) VerifyPasskeyRegistration(string userId, string passkeyId, string passkeyName, Struct publicKeyCredentials)
	{
		try
		{
			Client.VerifyPasskeyRegistration(new()
			{
				UserId = userId,
				PasskeyId = passkeyId,
				PasskeyName = passkeyName,
				PublicKeyCredential = publicKeyCredentials
			});
			
			return (true, null);
		}
		catch (RpcException e)
		{
			return (false, e.Message);
		}
	}

	/// <inheritdoc />
	public IList<Passkey> ListPasskeys(string userId)
	{
		try
		{
			var response = Client.ListPasskeys(new()
			{
				UserId = userId
			});

			return response.Result.ToList();
		}
		catch (RpcException)
		{
			return new List<Passkey>();
		}
	}

	/// <inheritdoc />
	public void RemovePasskey(string userId, string passkeyId)
	{
		Client.RemovePasskey(new()
		{
			UserId = userId,
			PasskeyId = passkeyId
		});
	}

	#endregion

	#endregion
}